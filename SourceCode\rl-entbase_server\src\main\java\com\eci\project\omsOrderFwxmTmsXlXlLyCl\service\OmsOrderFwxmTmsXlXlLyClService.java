package com.eci.project.omsOrderFwxmTmsXlXlLyCl.service;

import com.eci.common.util.StringUtils;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.omsOrderFwxmTmsXlXlLyCl.dao.OmsOrderFwxmTmsXlXlLyClDao;
import com.eci.project.omsOrderFwxmTmsXlXlLyCl.entity.OmsOrderFwxmTmsXlXlLyClEntity;
import com.eci.project.omsOrderFwxmTmsXlXlLyCl.validate.OmsOrderFwxmTmsXlXlLyClVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
* 委托内容-程运序列-陆运-车辆信息Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-06-04
*/
@Service
@Slf4j
public class OmsOrderFwxmTmsXlXlLyClService implements EciBaseService<OmsOrderFwxmTmsXlXlLyClEntity> {

    @Autowired
    private OmsOrderFwxmTmsXlXlLyClDao omsOrderFwxmTmsXlXlLyClDao;

    @Autowired
    private OmsOrderFwxmTmsXlXlLyClVal omsOrderFwxmTmsXlXlLyClVal;


    @Override
    public TgPageInfo queryPageList(OmsOrderFwxmTmsXlXlLyClEntity entity) {
        EciQuery<OmsOrderFwxmTmsXlXlLyClEntity> eciQuery = EciQuery.buildQuery(entity);
        List<OmsOrderFwxmTmsXlXlLyClEntity> entities = omsOrderFwxmTmsXlXlLyClDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderFwxmTmsXlXlLyClEntity save(OmsOrderFwxmTmsXlXlLyClEntity entity) {
        // 返回实体对象
        OmsOrderFwxmTmsXlXlLyClEntity omsOrderFwxmTmsXlXlLyClEntity = null;
        omsOrderFwxmTmsXlXlLyClVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            omsOrderFwxmTmsXlXlLyClEntity = omsOrderFwxmTmsXlXlLyClDao.insertOne(entity);

        }else{

            omsOrderFwxmTmsXlXlLyClEntity = omsOrderFwxmTmsXlXlLyClDao.updateByEntityId(entity);

        }
        return omsOrderFwxmTmsXlXlLyClEntity;
    }

    @Override
    public List<OmsOrderFwxmTmsXlXlLyClEntity> selectList(OmsOrderFwxmTmsXlXlLyClEntity entity) {
        if(StringUtils.hasValue(entity.getOrderNo()) || StringUtils.hasValue(entity.getPreNo())){
            return omsOrderFwxmTmsXlXlLyClDao.selectList(entity);
        }else{
            return new ArrayList<>();
        }
    }

    @Override
    public OmsOrderFwxmTmsXlXlLyClEntity selectOneById(Serializable id) {
        return omsOrderFwxmTmsXlXlLyClDao.selectById(id);
    }


    @Override
    public void insertBatch(List<OmsOrderFwxmTmsXlXlLyClEntity> list) {
        omsOrderFwxmTmsXlXlLyClDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return omsOrderFwxmTmsXlXlLyClDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return omsOrderFwxmTmsXlXlLyClDao.deleteById(id);
    }

}