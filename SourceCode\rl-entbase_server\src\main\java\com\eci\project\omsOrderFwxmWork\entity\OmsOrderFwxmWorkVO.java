package com.eci.project.omsOrderFwxmWork.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.eci.common.DictField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

/**
 * @description 订单服务项目工作单据对象
 * <AUTHOR>
 */
@Data
public class OmsOrderFwxmWorkVO {

    /**
     * 工作号 (WORK_NO)
     */
    @ApiModelProperty("工作号")
    @TableField("WORK_NO")
    private String workNo;

    /**
     * 订单号 (ORDER_NO)
     */
    @ApiModelProperty("订单号")
    @TableField("ORDER_NO")
    private String orderNo;

    /**
     * PRE_NO
     */
    @ApiModelProperty("PRE_NO")
    @TableField("PRE_NO")
    private String preNo;

    /**
     * 服务项目代码 (FWXM_CODE)
     */
    @ApiModelProperty("服务项目代码")
    @TableField("FWXM_CODE")
    @DictField(sql = "SELECT A.CODE,A.NAME  FROM FZGJ_BD_SERVICE_ITEM_PT A  WHERE A.STATUS = 'Y' ")
    private String fwxmCode;

    /**
     * 任务序号 (TASK_SEQ)
     */
    @ApiModelProperty("任务序号")
    @TableField("TASK_SEQ")
    private Integer taskSeq;

    /**
     * 任务前置 (TASK_PRE)
     */
    @ApiModelProperty("任务前置")
    @TableField("TASK_PRE")
    private String taskPre;

    /**
     * 是否委办 (IS_WB)
     */
    @ApiModelProperty("是否委办")
    @TableField("IS_WB")
    private String isWb;

    /**
     * 供应商代码 (GYS_CODE)
     */
    @ApiModelProperty("供应商代码")
    @TableField("GYS_CODE")
    private String gysCode;

    /**
     * 内部节点代码 (NODE_CODE_NB)
     */
    @ApiModelProperty("内部节点代码")
    @TableField("NODE_CODE_NB")
    private String nodeCodeNb;

    /**
     * 报价代码 (QUOTE_CODE)
     */
    @ApiModelProperty("报价代码")
    @TableField("QUOTE_CODE")
    private String quoteCode;

    /**
     * 状态 (STATUS)
     */
    @ApiModelProperty("状态")
    @TableField("STATUS")
    private String status;

    /**
     * 阶段 (STAGE)
     */
    @ApiModelProperty("阶段")
    @TableField("STAGE")
    private String stage;

    /**
     * 系统代码 (SYS_CODE)
     */
    @ApiModelProperty("系统代码")
    @TableField("SYS_CODE")
    private String sysCode;

    /**
     * 账单代码 (BILL_CODE)
     */
    @ApiModelProperty("账单代码")
    @TableField("BILL_CODE")
    private String billCode;

    /**
     * DOC_NO
     */
    @ApiModelProperty("DOC_NO")
    @TableField("DOC_NO")
    private String docNo;

    /**
     * 响应代码 (RESPONSE_CODE)
     */
    @ApiModelProperty("响应代码")
    @TableField("RESPONSE_CODE")
    private String responseCode;

    /**
     * 发送日期 (SEND_DATE)
     */
    @ApiModelProperty("发送日期")
    @TableField("SEND_DATE")
    private Date sendDate;

    /**
     * 创建用户 (CREATE_USER)
     */
    @ApiModelProperty("创建用户")
    @TableField("CREATE_USER")
    private String createUser;

    /**
     * 创建用户名称 (CREATE_USER_NAME)
     */
    @ApiModelProperty("创建用户名称")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
     * 创建日期 (CREATE_DATE)
     */
    @ApiModelProperty("创建日期")
    @TableField("CREATE_DATE")
    private Date createDate;

    /**
     * 更新用户 (UPDATE_USER)
     */
    @ApiModelProperty("更新用户")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
     * 更新用户名称 (UPDATE_USER_NAME)
     */
    @ApiModelProperty("更新用户名称")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
     * 更新日期 (UPDATE_DATE)
     */
    @ApiModelProperty("更新日期")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    /**
     * 公司代码 (COMPANY_CODE)
     */
    @ApiModelProperty("公司代码")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
     * 公司名称 (COMPANY_NAME)
     */
    @ApiModelProperty("公司名称")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
     * 节点代码 (NODE_CODE)
     */
    @ApiModelProperty("节点代码")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
     * 节点名称 (NODE_NAME)
     */
    @ApiModelProperty("节点名称")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
     * 集团代码 (GROUP_CODE)
     */
    @ApiModelProperty("集团代码")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
     * 集团名称 (GROUP_NAME)
     */
    @ApiModelProperty("集团名称")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
     * GUID
     */
    @ApiModelProperty("GUID")
    @TableField("GUID")
    private String guid;

    /**
     * 作业完成 (OP_COMPLETE_OK)
     * 来源: CASE WHEN A.OP_COMPLETE_OK = 'Y' THEN '√' ELSE NULL END
     */
    @ApiModelProperty("作业完成: '√'表示是")
    @TableField("OP_COMPLETE_OK")
    private String opCompleteOk;

    /**
     * 作业数据齐全 (DATA_OK)
     * 来源: CASE WHEN A.DATA_OK = 'Y' THEN '√' ELSE NULL END
     */
    @ApiModelProperty("作业数据齐全: '√'表示是")
    @TableField("DATA_OK")
    private String dataOk;

    /**
     * 结算完成 (ARAP_OK)
     * 来源: CASE WHEN A.ARAP_OK = 'Y' THEN '√' ELSE NULL END
     */
    @ApiModelProperty("结算完成: '√'表示是")
    @TableField("ARAP_OK")
    private String arapOk;

    /**
     * 显示状态 (SHOWSTATUS)
     * 来源: CASE WHEN A.STATUS = 'TH' THEN 'Y' ELSE 'N' END
     */
    @ApiModelProperty("显示状态: 'Y' 或 'N'")
    @TableField("SHOWSTATUS")
    private String showStatus;

    /**
     * PRE_WORK_NO
     */
    @ApiModelProperty("PRE_WORK_NO")
    @TableField("PRE_WORK_NO")
    private String preWorkNo;

    /**
     * UDF2
     */
    @ApiModelProperty("UDF2")
    @TableField("UDF2")
    private String udf2;

    /**
     * 是否AP (IS_AP)
     * 来源: CASE A.IS_AP WHEN 'Y' THEN '是' WHEN 'N' THEN '否' ELSE NULL END
     */
    @ApiModelProperty("是否AP: '是' 或 '否'")
    @TableField("IS_AP")
    private String isAp;

    /**
     * FBPF
     * 来源: CASE (SELECT 1 FROM FZGJ_BD_PRODUCT_FWXM_FF F WHERE F.FWXM_CODE = A.FWXM_CODE AND F.GROUP_CODE = A.GROUP_CODE AND F.PRODUCT_CODE = O.PRODUCT_CODE) WHEN 1 THEN 'N' ELSE 'Y' END
     */
    @ApiModelProperty("FBPF")
    @TableField("FBPF")
    private String fbpf;

    /**
     * 审核日期 (AUDIT_DATE)
     * 来源: (SELECT MAX(P.AUDIT_DATE) FROM OMS_ORDER_PRE P, OMS_ORDER OS WHERE OS.PRE_ORDER_NO = O.ORDER_NO AND P.PRE_NO = OS.PRE_NO)
     */
    @ApiModelProperty("审核日期")
    @TableField("AUDIT_DATE")
    private Date auditDate;

}