package com.eci.project.fzgjBdProvince.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.common.DictField;
import com.eci.common.ZsrBaseEntity;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;

import java.util.Date;


/**
* 省对象 FZGJ_BD_PROVINCE
*
* @<NAME_EMAIL>
* @date 2025-03-17
*/
@FieldNameConstants
public class FzgjBdProvinceBaseEntity extends ZsrBaseEntity
{
	/**
	* GUID
	*/
	@ApiModelProperty("GUID(50)")
	@TableId("GUID")
	private String guid;

	/**
	* 省名称
	*/
	@ApiModelProperty("省名称(50)")
	@TableField("NAME")
	private String name;

	/**
	* 国家代码
	*/
	@ApiModelProperty("国家代码(50)")
	@TableField("COUNTRY_ID")
	@DictField(queryKey = "BASE_COUNTRY")
	private String countryId;

	/**
	* 备注
	*/
	@ApiModelProperty("备注(50)")
	@TableField("MEMO")
	private String memo;

	/**
	* 状态
	*/
	@ApiModelProperty("状态(1)")
	@TableField("STATUS")
	private String status;

	/**
	* 创建人
	*/
	@ApiModelProperty("创建人(50)")
	@TableField("CREATE_USER")
	private String createUser;

	/**
	* 创建时间
	*/
	@ApiModelProperty("创建时间(7)")
	@TableField("CREATE_DATE")
	private Date createDate;

	@ApiModelProperty("创建时间开始")
	@TableField(exist=false)
	private Date createDateStart;

	@ApiModelProperty("创建时间结束")
	@TableField(exist=false)
	private Date createDateEnd;

	/**
	* 修改人
	*/
	@ApiModelProperty("修改人(50)")
	@TableField("UPDATE_USER")
	private String updateUser;

	/**
	* 修改时间
	*/
	@ApiModelProperty("修改时间(7)")
	@TableField("UPDATE_DATE")
	private Date updateDate;

	@ApiModelProperty("修改时间开始")
	@TableField(exist=false)
	private Date updateDateStart;

	@ApiModelProperty("修改时间结束")
	@TableField(exist=false)
	private Date updateDateEnd;

	/**
	* 顺序
	*/
	@ApiModelProperty("顺序(22)")
	@TableField("SEQ")
	private Integer seq;

	/**
	* CREATE_USER
	*/
	@ApiModelProperty("CREATE_USER(200)")
	@TableField("CREATE_USER_NAME")
	private String createUserName;

	/**
	* UPDATE_USER
	*/
	@ApiModelProperty("UPDATE_USER(200)")
	@TableField("UPDATE_USER_NAME")
	private String updateUserName;

	/**
	* 英文名称，仅做存储
	*/
	@ApiModelProperty("英文名称，仅做存储(200)")
	@TableField("EN_NAME")
	private String enName;

	@JsonIgnore
	@TableField(exist = false)
	private Class<?> clazz = this.getClass();

	public Class<?> getClazz() {
		return clazz;
	}

	public void setClazz(Class<?> clazz) {
		this.clazz = clazz;
	}

	public FzgjBdProvinceBaseEntity setGuid(String guid) {
		this.guid = guid;
		return this;
	}

	public String getGuid() {
		return guid;
	}

	public FzgjBdProvinceBaseEntity setName(String name) {
		this.name = name;
		return this;
	}

	public String getName() {
		return name;
	}

	public FzgjBdProvinceBaseEntity setCountryId(String countryId) {
		this.countryId = countryId;
		return this;
	}

	public String getCountryId() {
		return countryId;
	}

	public FzgjBdProvinceBaseEntity setMemo(String memo) {
		this.memo = memo;
		return this;
	}

	public String getMemo() {
		return memo;
	}

	public FzgjBdProvinceBaseEntity setStatus(String status) {
		this.status = status;
		return this;
	}

	public String getStatus() {
		return status;
	}

	public FzgjBdProvinceBaseEntity setCreateUser(String createUser) {
		this.createUser = createUser;
		return this;
	}

	public String getCreateUser() {
		return createUser;
	}

	public FzgjBdProvinceBaseEntity setCreateDate(Date createDate) {
		this.createDate = createDate;
		return this;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public FzgjBdProvinceBaseEntity setCreateDateStart(Date createDateStart) {
		this.createDateStart = createDateStart;
		return this;
	}

	public Date getCreateDateStart() {
		return createDateStart;
	}

	public FzgjBdProvinceBaseEntity setCreateDateEnd(Date createDateEnd) {
		this.createDateEnd = createDateEnd;
		return this;
	}

	public Date getCreateDateEnd() {
		return createDateEnd;
	}
	public FzgjBdProvinceBaseEntity setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
		return this;
	}

	public String getUpdateUser() {
		return updateUser;
	}

	public FzgjBdProvinceBaseEntity setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
		return this;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public FzgjBdProvinceBaseEntity setUpdateDateStart(Date updateDateStart) {
		this.updateDateStart = updateDateStart;
		return this;
	}

	public Date getUpdateDateStart() {
		return updateDateStart;
	}

	public FzgjBdProvinceBaseEntity setUpdateDateEnd(Date updateDateEnd) {
		this.updateDateEnd = updateDateEnd;
		return this;
	}

	public Date getUpdateDateEnd() {
		return updateDateEnd;
	}
	public FzgjBdProvinceBaseEntity setSeq(Integer seq) {
		this.seq = seq;
		return this;
	}

	public Integer getSeq() {
		return seq;
	}

	public FzgjBdProvinceBaseEntity setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
		return this;
	}

	public String getCreateUserName() {
		return createUserName;
	}

	public FzgjBdProvinceBaseEntity setUpdateUserName(String updateUserName) {
		this.updateUserName = updateUserName;
		return this;
	}

	public String getUpdateUserName() {
		return updateUserName;
	}

	public FzgjBdProvinceBaseEntity setEnName(String enName) {
		this.enName = enName;
		return this;
	}

	public String getEnName() {
		return enName;
	}

}
