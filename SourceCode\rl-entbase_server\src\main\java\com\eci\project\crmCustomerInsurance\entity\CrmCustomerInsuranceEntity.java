package com.eci.project.crmCustomerInsurance.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.math.BigDecimal;
import java.util.Date;


/**
* 司机保险管理对象 CRM_CUSTOMER_INSURANCE
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-05-26
*/
@ApiModel("司机保险管理")
@TableName("CRM_CUSTOMER_INSURANCE")
@FieldNameConstants
public class CrmCustomerInsuranceEntity extends EciBaseEntity{
    /**
    * GUID
    */
    @ApiModelProperty("GUID(50)")
    @TableId("GUID")
    private String guid;

    /**
    * GUID
    */
    @ApiModelProperty("GUID(50)")
    @TableField("CUSTOMER_GUID")
    private String customerGuid;

    /**
    * 保险单号
    */
    @ApiModelProperty("保险单号(30)")
    @TableField("POLICY_NO")
    private String policyNo;

    /**
    * 保险公司
    */
    @ApiModelProperty("保险公司(200)")
    @TableField("INSURER")
    private String insurer;
    @TableField(exist = false)
    private String statusName;

    /**
    * 险种
    */
    @ApiModelProperty("险种(40)")
    @TableField("INSURANCE_TYPE")
    private String insuranceType;

    /**
    * 保险开始日期
    */
    @ApiModelProperty("保险开始日期(7)")
    @TableField("START_DATE")
    private Date startDate;

    @ApiModelProperty("保险开始日期开始")
    @TableField(exist=false)
    private Date startDateStart;

    @ApiModelProperty("保险开始日期结束")
    @TableField(exist=false)
    private Date startDateEnd;

    /**
    * 保险结束日期
    */
    @ApiModelProperty("保险结束日期(7)")
    @TableField("END_DATE")
    private Date endDate;

    @ApiModelProperty("保险结束日期开始")
    @TableField(exist=false)
    private Date endDateStart;

    @ApiModelProperty("保险结束日期结束")
    @TableField(exist=false)
    private Date endDateEnd;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 保险状态
    */
    @ApiModelProperty("保险状态(20)")
    @TableField("STATUS")
    private String status;

    /**
    * 保险金额
    */
    @ApiModelProperty("保险金额(22)")
    @TableField("AMOUNT")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal amount;

    /**
    * 保险期限
    */
    @ApiModelProperty("保险期限(20)")
    @TableField("INSURANCE_TERM")
    private String insuranceTerm;

    /**
    * 备注
    */
    @ApiModelProperty("备注(20)")
    @TableField("MEMO")
    private String memo;

    /**
    * 投保人\企业名称
    */
    @ApiModelProperty("投保人企业名称(50)")
    @TableField("POLICYHOLDER")
    private String policyholder;

    @ApiModelProperty("(20)")
    @TableField("CREATE_USER")
    private String createUser;

    @ApiModelProperty("(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    @ApiModelProperty("(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("结束")
    @TableField(exist=false)
    private Date createDateEnd;

    @ApiModelProperty("(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public CrmCustomerInsuranceEntity() {
        this.setSubClazz(CrmCustomerInsuranceEntity.class);
    }

    public CrmCustomerInsuranceEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public String getStatusName() {
        this.nodifyGetFiled("status");
        if(new Date().after(this.endDate)){
            return "无效";
        }else{
            return "有效";
        }

    }
    public CrmCustomerInsuranceEntity setCustomerGuid(String customerGuid) {
        this.customerGuid = customerGuid;
        this.nodifySetFiled("customerGuid", customerGuid);
        return this;
    }

    public String getCustomerGuid() {
        this.nodifyGetFiled("customerGuid");
        return customerGuid;
    }

    public CrmCustomerInsuranceEntity setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
        this.nodifySetFiled("policyNo", policyNo);
        return this;
    }

    public String getPolicyNo() {
        this.nodifyGetFiled("policyNo");
        return policyNo;
    }

    public CrmCustomerInsuranceEntity setInsurer(String insurer) {
        this.insurer = insurer;
        this.nodifySetFiled("insurer", insurer);
        return this;
    }

    public String getInsurer() {
        this.nodifyGetFiled("insurer");
        return insurer;
    }

    public CrmCustomerInsuranceEntity setInsuranceType(String insuranceType) {
        this.insuranceType = insuranceType;
        this.nodifySetFiled("insuranceType", insuranceType);
        return this;
    }

    public String getInsuranceType() {
        this.nodifyGetFiled("insuranceType");
        return insuranceType;
    }

    public CrmCustomerInsuranceEntity setStartDate(Date startDate) {
        this.startDate = startDate;
        this.nodifySetFiled("startDate", startDate);
        return this;
    }

    public Date getStartDate() {
        this.nodifyGetFiled("startDate");
        return startDate;
    }

    public CrmCustomerInsuranceEntity setStartDateStart(Date startDateStart) {
        this.startDateStart = startDateStart;
        this.nodifySetFiled("startDateStart", startDateStart);
        return this;
    }

    public Date getStartDateStart() {
        this.nodifyGetFiled("startDateStart");
        return startDateStart;
    }

    public CrmCustomerInsuranceEntity setStartDateEnd(Date startDateEnd) {
        this.startDateEnd = startDateEnd;
        this.nodifySetFiled("startDateEnd", startDateEnd);
        return this;
    }

    public Date getStartDateEnd() {
        this.nodifyGetFiled("startDateEnd");
        return startDateEnd;
    }
    public CrmCustomerInsuranceEntity setEndDate(Date endDate) {
        this.endDate = endDate;
        this.nodifySetFiled("endDate", endDate);
        return this;
    }

    public Date getEndDate() {
        this.nodifyGetFiled("endDate");
        return endDate;
    }

    public CrmCustomerInsuranceEntity setEndDateStart(Date endDateStart) {
        this.endDateStart = endDateStart;
        this.nodifySetFiled("endDateStart", endDateStart);
        return this;
    }

    public Date getEndDateStart() {
        this.nodifyGetFiled("endDateStart");
        return endDateStart;
    }

    public CrmCustomerInsuranceEntity setEndDateEnd(Date endDateEnd) {
        this.endDateEnd = endDateEnd;
        this.nodifySetFiled("endDateEnd", endDateEnd);
        return this;
    }

    public Date getEndDateEnd() {
        this.nodifyGetFiled("endDateEnd");
        return endDateEnd;
    }
    public CrmCustomerInsuranceEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public CrmCustomerInsuranceEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public CrmCustomerInsuranceEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public CrmCustomerInsuranceEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public CrmCustomerInsuranceEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public CrmCustomerInsuranceEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public CrmCustomerInsuranceEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public CrmCustomerInsuranceEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public CrmCustomerInsuranceEntity setStatus(String status) {
        this.status = status;
        this.nodifySetFiled("status", status);
        return this;
    }

    public String getStatus() {
        this.nodifyGetFiled("status");
        return status;
    }

    public CrmCustomerInsuranceEntity setAmount(BigDecimal amount) {
        this.amount = amount;
        this.nodifySetFiled("amount", amount);
        return this;
    }

    public BigDecimal getAmount() {
        this.nodifyGetFiled("amount");
        return amount;
    }

    public CrmCustomerInsuranceEntity setInsuranceTerm(String insuranceTerm) {
        this.insuranceTerm = insuranceTerm;
        this.nodifySetFiled("insuranceTerm", insuranceTerm);
        return this;
    }

    public String getInsuranceTerm() {
        this.nodifyGetFiled("insuranceTerm");
        return insuranceTerm;
    }

    public CrmCustomerInsuranceEntity setMemo(String memo) {
        this.memo = memo;
        this.nodifySetFiled("memo", memo);
        return this;
    }

    public String getMemo() {
        this.nodifyGetFiled("memo");
        return memo;
    }

    public CrmCustomerInsuranceEntity setPolicyholder(String policyholder) {
        this.policyholder = policyholder;
        this.nodifySetFiled("policyholder", policyholder);
        return this;
    }

    public String getPolicyholder() {
        this.nodifyGetFiled("policyholder");
        return policyholder;
    }

    public CrmCustomerInsuranceEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public CrmCustomerInsuranceEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public CrmCustomerInsuranceEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public CrmCustomerInsuranceEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public CrmCustomerInsuranceEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public CrmCustomerInsuranceEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public CrmCustomerInsuranceEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public CrmCustomerInsuranceEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
}
