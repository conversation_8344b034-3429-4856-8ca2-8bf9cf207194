<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.etmsBdTruckInsuranceQz.dao.EtmsBdTruckInsuranceQzDao">
    <resultMap type="EtmsBdTruckInsuranceQzEntity" id="EtmsBdTruckInsuranceQzResult">
        <result property="guid" column="GUID"/>
        <result property="truckGuid" column="TRUCK_GUID"/>
        <result property="policyNo" column="POLICY_NO"/>
        <result property="insurer" column="INSURER"/>
        <result property="insuranceType" column="INSURANCE_TYPE"/>
        <result property="startDate" column="START_DATE"/>
        <result property="endDate" column="END_DATE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="modMark" column="MOD_MARK"/>
        <result property="insuredName" column="INSURED_NAME"/>
        <result property="insuranceStatus" column="INSURANCE_STATUS"/>
        <result property="insuranceMoney" column="INSURANCE_MONEY"/>
        <result property="insurancePeriod" column="INSURANCE_PERIOD"/>
        <result property="remark" column="REMARK"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>

    <sql id="selectEtmsBdTruckInsuranceQzEntityVo">
        select
            GUID,
            TRUCK_GUID,
            POLICY_NO,
            INSURER,
            INSURANCE_TYPE,
            START_DATE,
            END_DATE,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            CREATE_USER_NAME,
            UPDATE_USER_NAME,
            MOD_MARK,
            INSURED_NAME,
            INSURANCE_STATUS,
            INSURANCE_MONEY,
            INSURANCE_PERIOD,
            REMARK,
            CREATE_DATE,
            UPDATE_DATE
        from ETMS_BD_TRUCK_INSURANCE_QZ
    </sql>
</mapper>