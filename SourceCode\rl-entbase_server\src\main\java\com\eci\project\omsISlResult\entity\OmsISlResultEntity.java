package com.eci.project.omsISlResult.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;

import java.util.Date;


/**
* 受理结果及单据编号对象 OMS_I_SL_RESULT
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-04-21
*/
@ApiModel("受理结果及单据编号")
@TableName("OMS_I_SL_RESULT")
@FieldNameConstants
public class OmsISlResultEntity extends EciBaseEntity{
    /**
    * GUID
    */
    @ApiModelProperty("GUID(36)")
    @TableId("GUID")
    private String guid;

    /**
    * OMS协作任务编号
    */
    @ApiModelProperty("OMS协作任务编号(50)")
    @TableField("WORK_NO")
    private String workNo;

    /**
    * OMS订单号
    */
    @ApiModelProperty("OMS订单号(36)")
    @TableField("ORDER_NO")
    private String orderNo;

    /**
    * 受理结果，确认/拒绝接单
    */
    @ApiModelProperty("受理结果，确认/拒绝接单(10)")
    @TableField("RESULT")
    private String result;

    /**
    * 作业系统代码
    */
    @ApiModelProperty("作业系统代码(10)")
    @TableField("SYS_CODE")
    private String sysCode;

    /**
    * 作业系统单据类型
    */
    @ApiModelProperty("作业系统单据类型(20)")
    @TableField("DOC_TYPE")
    private String docType;

    /**
    * 作业系统单据编号，拒绝时会发空
    */
    @ApiModelProperty("作业系统单据编号，拒绝时会发空(36)")
    @TableField("DOC_NO")
    private String docNo;

    /**
    * 传输时间
    */
    @ApiModelProperty("传输时间(7)")
    @TableField("TRN_DATE")
    private Date trnDate;

    @ApiModelProperty("传输时间开始")
    @TableField(exist=false)
    private Date trnDateStart;

    @ApiModelProperty("传输时间结束")
    @TableField(exist=false)
    private Date trnDateEnd;

    /**
    * 处理标记,0未处理，1已处理，2处理失败
    */
    @ApiModelProperty("处理标记,0未处理，1已处理，2处理失败(1)")
    @TableField("OP_FLAG")
    private String opFlag;

    /**
    * 操作时间
    */
    @ApiModelProperty("操作时间(7)")
    @TableField("OP_DATE")
    private Date opDate;

    @ApiModelProperty("操作时间开始")
    @TableField(exist=false)
    private Date opDateStart;

    @ApiModelProperty("操作时间结束")
    @TableField(exist=false)
    private Date opDateEnd;

    /**
    * 处理结果
    */
    @ApiModelProperty("处理结果(4,000)")
    @TableField("OP_RESULT")
    private String opResult;

    /**
    * 协作委托单编号
    */
    @ApiModelProperty("协作委托单编号(36)")
    @TableField("XZWT_NO")
    private String xzwtNo;

    /**
    * 业务数据唯一注册编号
    */
    @ApiModelProperty("业务数据唯一注册编号(50)")
    @TableField("BIZ_REG_ID")
    private String bizRegId;

    /**
    * 发送企业代码
    */
    @ApiModelProperty("发送企业代码(20)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 发送企业名称
    */
    @ApiModelProperty("发送企业名称(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public OmsISlResultEntity setGuid(String guid) {
        this.guid = guid;
        return this;
    }

    public String getGuid() {
        return guid;
    }

    public OmsISlResultEntity setWorkNo(String workNo) {
        this.workNo = workNo;
        return this;
    }

    public String getWorkNo() {
        return workNo;
    }

    public OmsISlResultEntity setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        return this;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public OmsISlResultEntity setResult(String result) {
        this.result = result;
        return this;
    }

    public String getResult() {
        return result;
    }

    public OmsISlResultEntity setSysCode(String sysCode) {
        this.sysCode = sysCode;
        return this;
    }

    public String getSysCode() {
        return sysCode;
    }

    public OmsISlResultEntity setDocType(String docType) {
        this.docType = docType;
        return this;
    }

    public String getDocType() {
        return docType;
    }

    public OmsISlResultEntity setDocNo(String docNo) {
        this.docNo = docNo;
        return this;
    }

    public String getDocNo() {
        return docNo;
    }

    public OmsISlResultEntity setTrnDate(Date trnDate) {
        this.trnDate = trnDate;
        return this;
    }

    public Date getTrnDate() {
        return trnDate;
    }

    public OmsISlResultEntity setTrnDateStart(Date trnDateStart) {
        this.trnDateStart = trnDateStart;
        return this;
    }

    public Date getTrnDateStart() {
        return trnDateStart;
    }

    public OmsISlResultEntity setTrnDateEnd(Date trnDateEnd) {
        this.trnDateEnd = trnDateEnd;
        return this;
    }

    public Date getTrnDateEnd() {
        return trnDateEnd;
    }
    public OmsISlResultEntity setOpFlag(String opFlag) {
        this.opFlag = opFlag;
        return this;
    }

    public String getOpFlag() {
        return opFlag;
    }

    public OmsISlResultEntity setOpDate(Date opDate) {
        this.opDate = opDate;
        return this;
    }

    public Date getOpDate() {
        return opDate;
    }

    public OmsISlResultEntity setOpDateStart(Date opDateStart) {
        this.opDateStart = opDateStart;
        return this;
    }

    public Date getOpDateStart() {
        return opDateStart;
    }

    public OmsISlResultEntity setOpDateEnd(Date opDateEnd) {
        this.opDateEnd = opDateEnd;
        return this;
    }

    public Date getOpDateEnd() {
        return opDateEnd;
    }
    public OmsISlResultEntity setOpResult(String opResult) {
        this.opResult = opResult;
        return this;
    }

    public String getOpResult() {
        return opResult;
    }

    public OmsISlResultEntity setXzwtNo(String xzwtNo) {
        this.xzwtNo = xzwtNo;
        return this;
    }

    public String getXzwtNo() {
        return xzwtNo;
    }

    public OmsISlResultEntity setBizRegId(String bizRegId) {
        this.bizRegId = bizRegId;
        return this;
    }

    public String getBizRegId() {
        return bizRegId;
    }

    public OmsISlResultEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        return this;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public OmsISlResultEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        return this;
    }

    public String getCompanyName() {
        return companyName;
    }

}
