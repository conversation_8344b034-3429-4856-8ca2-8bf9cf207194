package com.eci.project.fzgjException.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjException.entity.FzgjExceptionEntity;

import org.springframework.stereotype.Service;


/**
* 订单作业异常Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-25
*/
@Service
public class FzgjExceptionVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjExceptionEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjExceptionEntity entity, BusinessType businessType) {

    }

}
