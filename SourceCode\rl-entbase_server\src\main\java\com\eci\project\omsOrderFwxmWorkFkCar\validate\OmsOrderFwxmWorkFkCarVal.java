package com.eci.project.omsOrderFwxmWorkFkCar.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.omsOrderFwxmWorkFkCar.entity.OmsOrderFwxmWorkFkCarEntity;

import org.springframework.stereotype.Service;


/**
* 反馈内容-车辆Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-05-21
*/
@Service
public class OmsOrderFwxmWorkFkCarVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(OmsOrderFwxmWorkFkCarEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(OmsOrderFwxmWorkFkCarEntity entity, BusinessType businessType) {

    }

}
