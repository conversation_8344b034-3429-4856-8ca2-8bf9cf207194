package com.eci.project.fzgjTaskLimitationPt.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;


/**
* 平台级作业环节及参考时效对象 FZGJ_TASK_LIMITATION_PT
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-03-28
*/
@ApiModel("平台级作业环节及参考时效")
@TableName("FZGJ_TASK_LIMITATION_PT")
public class FzgjTaskLimitationPtEntity extends FzgjTaskLimitationPtBaseEntity{

    @ApiModelProperty("作业环节状态")
    @TableField(exist=false)
    private boolean nodeStatus;

    public FzgjTaskLimitationPtEntity setNodeStatus(boolean createDateStart) {
        this.nodeStatus = createDateStart;
        return this;
    }

    public boolean getNodeStatus() {
        return nodeStatus;
    }

}
