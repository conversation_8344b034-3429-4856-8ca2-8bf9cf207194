package com.eci.project.fzgjBaseInformation.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjBaseInformation.entity.FzgjBaseInformationEntity;

import org.springframework.stereotype.Service;


/**
* 新闻通知公告信息表Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-06-18
*/
@Service
public class FzgjBaseInformationVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjBaseInformationEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjBaseInformationEntity entity, BusinessType businessType) {

    }

}
