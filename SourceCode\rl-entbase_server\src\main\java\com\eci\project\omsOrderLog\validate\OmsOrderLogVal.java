package com.eci.project.omsOrderLog.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.omsOrderLog.entity.OmsOrderLogEntity;

import org.springframework.stereotype.Service;


/**
* 订单操作日志信息Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-06-19
*/
@Service
public class OmsOrderLogVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(OmsOrderLogEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(OmsOrderLogEntity entity, BusinessType businessType) {

    }

}
