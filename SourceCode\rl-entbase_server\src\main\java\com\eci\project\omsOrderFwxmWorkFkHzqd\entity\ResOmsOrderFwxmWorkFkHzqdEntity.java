package com.eci.project.omsOrderFwxmWorkFkHzqd.entity;

import com.eci.project.omsOrderFwxmWorkFkHfd.entity.OmsOrderFwxmWorkFkHfdEntity;
import com.eci.wu.core.EntityBase;

import java.util.List;

/**
 * @ClassName: ResOmsOrderFwxmWorkFkHzqdEntity
 * @Author: guangyan.mei
 * @Date: 2025/6/3 10:12
 * @Description: TODO
 */
public class ResOmsOrderFwxmWorkFkHzqdEntity {

    // 核注清单作业
    public List<OmsOrderFwxmWorkFkHzqdEntity> dtDateHZQD;

    public List<OmsOrderFwxmWorkFkHzqdEntity> getDtDateHZQD() {
        return dtDateHZQD;
    }

    public void setDtDateHZQD(List<OmsOrderFwxmWorkFkHzqdEntity> dtDateHZQD) {
        this.dtDateHZQD = dtDateHZQD;
    }

}
