package com.eci.project.omsOrderFwxmWorkFkHzqd.entity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * 反馈作业完成的请求对象。
 * 用于通知OMS系统某个作业或单据的状态已完成或数据已备齐。
 */
@Data
public class FeedbackOperationCompletion {

    /**
     * OMS协作任务编号。
     * 必填。
     */
    private String WORK_NO;

    /**
     * OMS订单号。
     * 必填。
     */
    private String ORDER_NO;

    /**
     * 单据状态。
     * **必填**。通常为 "作业完成" 或 "作业数据齐全"。
     */
    private String DOC_STATUS;

    /**
     * 作业完成时间。
     * 可选。格式通常为 "yyyy-MM-dd HH:mm:ss"。
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date OK_DATE;


    /**
     * 作业系统代码。
     * 必填。
     */
    private String SYS_CODE;

    /**
     * 传输时间。
     * 可选。格式通常为 "yyyy-MM-dd HH:mm:ss"。
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date TRN_DATE;

}