package com.eci.project.omsOrderFwxmTmsXlXlLy.service;

import com.aspose.cad.internal.A.Q;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.Zsr;
import com.eci.common.db.DBHelper;
import com.eci.common.util.DateUtils;
import com.eci.common.util.EciSqlUtl;
import com.eci.common.util.StringUtils;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.exception.BaseException;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.omsOrderFwxmBgbj.service.OmsOrderFwxmBgbjService;
import com.eci.project.omsOrderFwxmTmsXlXl.dao.OmsOrderFwxmTmsXlXlDao;
import com.eci.project.omsOrderFwxmTmsXlXl.entity.OmsOrderFwxmTmsXlXlEntity;
import com.eci.project.omsOrderFwxmTmsXlXlLy.dao.OmsOrderFwxmTmsXlXlLyDao;
import com.eci.project.omsOrderFwxmTmsXlXlLy.entity.OmsOrderFwxmTmsXlXlLyDTOEntity;
import com.eci.project.omsOrderFwxmTmsXlXlLy.entity.OmsOrderFwxmTmsXlXlLyEntity;
import com.eci.project.omsOrderFwxmTmsXlXlLy.validate.OmsOrderFwxmTmsXlXlLyVal;

import com.eci.project.omsOrderFwxmTmsXlXlLyCl.dao.OmsOrderFwxmTmsXlXlLyClDao;
import com.eci.project.omsOrderFwxmTmsXlXlLyCl.entity.OmsOrderFwxmTmsXlXlLyClEntity;
import com.eci.project.omsOrderFwxmTmsXlXlLyPc.dao.OmsOrderFwxmTmsXlXlLyPcDao;
import com.eci.project.omsOrderFwxmTmsXlXlLyPc.entity.OmsOrderFwxmTmsXlXlLyPcEntity;
import com.eci.project.omsOrderFwxmTmsXlXlLyXl.dao.OmsOrderFwxmTmsXlXlLyXlDao;
import com.eci.project.omsOrderFwxmTmsXlXlLyXl.entity.OmsOrderFwxmTmsXlXlLyXlEntity;
import com.eci.project.omsOrderFwxmWork.dao.OmsOrderFwxmWorkDao;
import com.eci.project.omsOrderFwxmWork.entity.OmsOrderFwxmWorkEntity;
import com.eci.project.omsOrderFwxmZhysXl.dao.OmsOrderFwxmZhysXlDao;
import com.eci.project.omsOrderFwxmZhysXl.entity.OmsOrderFwxmZhysXlEntity;
import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.List;
import java.util.Random;
import java.util.UUID;


/**
 * 委托内容-程运序列-陆运Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-06-04
 */
@Service
@Slf4j
public class OmsOrderFwxmTmsXlXlLyService implements EciBaseService<OmsOrderFwxmTmsXlXlLyEntity> {

    @Autowired
    private OmsOrderFwxmTmsXlXlLyDao omsOrderFwxmTmsXlXlLyDao;
    @Autowired
    private OmsOrderFwxmTmsXlXlLyXlDao omsOrderFwxmTmsXlXlLyXlDao;
    @Autowired
    private OmsOrderFwxmTmsXlXlLyClDao omsOrderFwxmTmsXlXlLyClDao;
    @Autowired
    private OmsOrderFwxmZhysXlDao omsOrderFwxmZhysXlDao;
    @Autowired
    private OmsOrderFwxmTmsXlXlLyPcDao omsOrderFwxmTmsXlXlLyPcDao;
    @Autowired
    private OmsOrderFwxmBgbjService omsOrderFwxmBgbjService;
    @Autowired
    private OmsOrderFwxmWorkDao omsOrderFwxmWorkDao;

    @Autowired
    private OmsOrderFwxmTmsXlXlLyVal omsOrderFwxmTmsXlXlLyVal;

    public static String getUniqueCode(int length) {
        UUID uuid = UUID.randomUUID();
        String combined = uuid.toString().replaceAll("-", "").toUpperCase(); // 直接使用UUID的字符串形式
        return combined.length() >= length
                ? combined.substring(0, length)
                : combined + getUniqueCode(length - combined.length()); // 递归补足长度
    }

    @Override
    public TgPageInfo queryPageList(OmsOrderFwxmTmsXlXlLyEntity entity) {
        EciQuery<OmsOrderFwxmTmsXlXlLyEntity> eciQuery = EciQuery.buildQuery(entity);
        List<OmsOrderFwxmTmsXlXlLyEntity> entities = omsOrderFwxmTmsXlXlLyDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderFwxmTmsXlXlLyEntity save(OmsOrderFwxmTmsXlXlLyEntity entity) {
        // 返回实体对象
        OmsOrderFwxmTmsXlXlLyEntity omsOrderFwxmTmsXlXlLyEntity = null;
        omsOrderFwxmTmsXlXlLyVal.saveValidate(entity, BllContext.getBusinessType());
        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            omsOrderFwxmTmsXlXlLyEntity = omsOrderFwxmTmsXlXlLyDao.insertOne(entity);
        } else {
            omsOrderFwxmTmsXlXlLyEntity = omsOrderFwxmTmsXlXlLyDao.updateByEntityId(entity);
        }
        return omsOrderFwxmTmsXlXlLyEntity;
    }

    @Transactional(rollbackFor = Exception.class)
    public OmsOrderFwxmTmsXlXlLyDTOEntity saveInfo(OmsOrderFwxmTmsXlXlLyDTOEntity entity, List<OmsOrderFwxmTmsXlXlLyXlEntity> xlList, List<OmsOrderFwxmTmsXlXlLyClEntity> clList) {
        //查询综合运输路线表
        QueryWrapper<OmsOrderFwxmZhysXlEntity> zhysXlQueryWrapper = new QueryWrapper<>();
        if (StringUtils.hasValue(entity.getOrderNo())) {
            zhysXlQueryWrapper.eq("ORDER_NO", entity.getOrderNo());
        }
        zhysXlQueryWrapper.eq("LINE_NO", entity.getLineNo());
        if (StringUtils.hasValue(entity.getPreNo())) {
            zhysXlQueryWrapper.apply("ZHYS_GUID = (SELECT ZHYS_NO FROM OMS_ORDER_FWXM_ZHYS WHERE PRE_NO = {0})", entity.getPreNo());
        }

        OmsOrderFwxmZhysXlEntity zhysXlEntity = omsOrderFwxmZhysXlDao.selectOne(zhysXlQueryWrapper);
        // 返回实体对象
        OmsOrderFwxmTmsXlXlLyEntity saveEntity = null;
        OmsOrderFwxmTmsXlXlLyEntity omsOrderFwxmTmsXlXlLyEntity = new OmsOrderFwxmTmsXlXlLyEntity();
        omsOrderFwxmTmsXlXlLyVal.saveValidate(entity, BllContext.getBusinessType());
        QueryWrapper<OmsOrderFwxmTmsXlXlLyEntity> queryWrapper = new QueryWrapper<>();
        if (StringUtils.hasValue(entity.getOrderNo())) {
            queryWrapper.eq("ORDER_NO", entity.getOrderNo());
        }
        if (StringUtils.hasValue(entity.getPreNo())) {
            queryWrapper.eq("PRE_NO", entity.getPreNo());
        }
        queryWrapper.eq("FWXM_CODE", entity.getFwxmCode());
        if (StringUtils.hasValue(entity.getLineNo())) {
            queryWrapper.eq("LINE_NO", entity.getLineNo());
        }
        List<OmsOrderFwxmTmsXlXlLyEntity> listHead = omsOrderFwxmTmsXlXlLyDao.selectList(queryWrapper);
        boolean addZhysXl = zhysXlEntity == null;
        String workNo = (StringUtils.hasValue(entity.getOrderNo()) ? entity.getOrderNo() : entity.getPreNo()) + "-" + entity.getFwxmCode() + "-" + getUniqueCode(4);
        List<OmsOrderFwxmWorkEntity> omsOrderFwxmWorkEntities = omsOrderFwxmBgbjService.LoadByNo(StringUtils.hasValue(entity.getOrderNo()) ? entity.getOrderNo() : entity.getPreNo(), entity.getFwxmCode());
        OmsOrderFwxmWorkEntity orderFwxmWorkEntityNew = null;
        if (omsOrderFwxmWorkEntities.size() == 0) {
            //更新订单服务项目委托
            orderFwxmWorkEntityNew = new OmsOrderFwxmWorkEntity();
            orderFwxmWorkEntityNew.setGuid(IdWorker.get32UUID());
            orderFwxmWorkEntityNew.setNodeCodeNb(entity.getNodeCodeNb());
            orderFwxmWorkEntityNew.setGysCode(entity.getGysCode());
            orderFwxmWorkEntityNew.setFwxmCode(entity.getFwxmCode());
            orderFwxmWorkEntityNew.setOrderNo(entity.getOrderNo());
            orderFwxmWorkEntityNew.setPreNo(entity.getPreNo());
            orderFwxmWorkEntityNew.setIsWb(entity.getIsWb());
            if (orderFwxmWorkEntityNew.getIsWb().equals("Y")) {
                orderFwxmWorkEntityNew.setNodeCodeNb("");
            } else {
                orderFwxmWorkEntityNew.setGysCode("");
            }
            orderFwxmWorkEntityNew.setWorkNo(workNo);
            orderFwxmWorkEntityNew.setFwlxCode(entity.getFwxmCode().substring(0, 3));
            orderFwxmWorkEntityNew.setCreateDate(DateUtils.getNowDate());
            orderFwxmWorkEntityNew.setCreateUser(UserContext.getUserInfo().getUserId());
            orderFwxmWorkEntityNew.setCreateUserName(UserContext.getUserInfo().getTrueName());
            orderFwxmWorkEntityNew.setUpdateDate(DateUtils.getNowDate());
            orderFwxmWorkEntityNew.setUpdateUser(UserContext.getUserInfo().getUserId());
            orderFwxmWorkEntityNew.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            orderFwxmWorkEntityNew.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            orderFwxmWorkEntityNew.setCompanyName(UserContext.getUserInfo().getCompanyName());
            orderFwxmWorkEntityNew.setNodeCode(UserContext.getUserInfo().getDeptCode());
            orderFwxmWorkEntityNew.setNodeName(UserContext.getUserInfo().getDeptName());
            orderFwxmWorkEntityNew.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            orderFwxmWorkEntityNew.setGroupName(UserContext.getUserInfo().getCompanyName());
            orderFwxmWorkEntityNew.setUdf7(entity.getFwxmCode());
            omsOrderFwxmWorkDao.insertOne(orderFwxmWorkEntityNew);

        } else {
            orderFwxmWorkEntityNew = omsOrderFwxmWorkEntities.get(0);
            //电子口岸入网申请不修改ORDER_FWXM_WORK
            if (!entity.getFwxmCode().equals("*********")) {
                if (orderFwxmWorkEntityNew.getIsWb().equals("Y")) {
                    orderFwxmWorkEntityNew.setNodeCodeNb("");
                } else {
                    orderFwxmWorkEntityNew.setGysCode("");
                }
                orderFwxmWorkEntityNew.setFwxmCode(entity.getFwxmCode());
                orderFwxmWorkEntityNew.setOrderNo(entity.getOrderNo());
                orderFwxmWorkEntityNew.setPreNo(entity.getPreNo());
                orderFwxmWorkEntityNew.setUpdateDate(DateUtils.getNowDate());
                orderFwxmWorkEntityNew.setUpdateUser(UserContext.getUserInfo().getUserId());
                orderFwxmWorkEntityNew.setUpdateUserName(UserContext.getUserInfo().getTrueName());
                omsOrderFwxmWorkDao.updateByEntityId(orderFwxmWorkEntityNew);
            }
        }
        if (addZhysXl) {
            zhysXlEntity = new OmsOrderFwxmZhysXlEntity();
            zhysXlEntity.setSeqNo(IdWorker.get32UUID());
            zhysXlEntity.setLineNo(IdWorker.get32UUID());
            zhysXlEntity.setOrderNo(entity.getOrderNo());
            zhysXlEntity.setIsWb(entity.getIsWb());
            if (entity.getIsWb().equals("Y")) {
                zhysXlEntity.setNbzyzz("");
                zhysXlEntity.setGys(entity.getGysCode());
            } else {
                zhysXlEntity.setGys("");
                zhysXlEntity.setNbzyzz(entity.getNodeCodeNb());
            }
            zhysXlEntity.setXzrwbh(workNo);
            zhysXlEntity.setCreateDate(DateUtils.getNowDate());
            zhysXlEntity.setCreateUser(UserContext.getUserInfo().getUserId());
            zhysXlEntity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            zhysXlEntity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            zhysXlEntity.setCompanyName(UserContext.getUserInfo().getCompanyName());
            zhysXlEntity.setNodeCode(UserContext.getUserInfo().getDeptCode());
            zhysXlEntity.setNodeName(UserContext.getUserInfo().getDeptName());
            zhysXlEntity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            zhysXlEntity.setGroupName(UserContext.getUserInfo().getCompanyName());
        } else {
            if (!StringUtils.hasValue(zhysXlEntity.getXzrwbh())) {
                zhysXlEntity.setXzrwbh(workNo);
            }
            zhysXlEntity.setIsWb(entity.getIsWb());
            zhysXlEntity.setGys(entity.getGysCode());
            zhysXlEntity.setNbzyzz(entity.getNodeCodeNb());
            zhysXlEntity.setUpdateDate(DateUtils.getNowDate());
            zhysXlEntity.setUpdateUser(UserContext.getUserInfo().getUserId());
            zhysXlEntity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        }
        if (listHead.size() == 0) {
            BeanUtils.copyProperties(entity, omsOrderFwxmTmsXlXlLyEntity);
            omsOrderFwxmTmsXlXlLyEntity.setWorkNo(workNo);
            omsOrderFwxmTmsXlXlLyEntity.setLineNo(zhysXlEntity.getLineNo());
            omsOrderFwxmTmsXlXlLyEntity.setLyNo((StringUtils.hasValue(entity.getOrderNo()) ? entity.getOrderNo() : entity.getPreNo()) + "-" + entity.getFwxmCode() + "-" + getUniqueCode(6));
            omsOrderFwxmTmsXlXlLyEntity.setCreateDate(DateUtils.getNowDate());
            omsOrderFwxmTmsXlXlLyEntity.setCreateUser(UserContext.getUserInfo().getUserId());
            omsOrderFwxmTmsXlXlLyEntity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            omsOrderFwxmTmsXlXlLyEntity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            omsOrderFwxmTmsXlXlLyEntity.setCompanyName(UserContext.getUserInfo().getCompanyName());
            omsOrderFwxmTmsXlXlLyEntity.setNodeCode(UserContext.getUserInfo().getDeptCode());
            omsOrderFwxmTmsXlXlLyEntity.setNodeName(UserContext.getUserInfo().getDeptName());
            omsOrderFwxmTmsXlXlLyEntity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            omsOrderFwxmTmsXlXlLyEntity.setGroupName(UserContext.getUserInfo().getCompanyName());
            saveEntity = omsOrderFwxmTmsXlXlLyDao.insertOne(omsOrderFwxmTmsXlXlLyEntity);
        } else {
            omsOrderFwxmTmsXlXlLyEntity = listHead.get(0);
            omsOrderFwxmTmsXlXlLyEntity.setCyfs(entity.getCyfs());
            omsOrderFwxmTmsXlXlLyEntity.setIsZp(entity.getIsZp());
            omsOrderFwxmTmsXlXlLyEntity.setWbysjds(entity.getWbysjds());
            omsOrderFwxmTmsXlXlLyEntity.setIsLhwt(entity.getIsLhwt());
            omsOrderFwxmTmsXlXlLyEntity.setIsFzzh(entity.getIsFzzh());
            omsOrderFwxmTmsXlXlLyEntity.setIsFzxh(entity.getIsFzxh());
            omsOrderFwxmTmsXlXlLyEntity.setIsYcy(entity.getIsYcy());
            omsOrderFwxmTmsXlXlLyEntity.setIsDyyc(entity.getIsDyyc());
            omsOrderFwxmTmsXlXlLyEntity.setIsCtfl(entity.getIsCtfl());
            omsOrderFwxmTmsXlXlLyEntity.setIsGps(entity.getIsGps());
            omsOrderFwxmTmsXlXlLyEntity.setIsExpressway(entity.getIsExpressway());
            omsOrderFwxmTmsXlXlLyEntity.setLdjfyj(entity.getLdjfyj());
            omsOrderFwxmTmsXlXlLyEntity.setOtherMemo(entity.getOtherMemo());
            omsOrderFwxmTmsXlXlLyEntity.setUpdateDate(DateUtils.getNowDate());
            omsOrderFwxmTmsXlXlLyEntity.setUpdateUser(UserContext.getUserInfo().getUserId());
            omsOrderFwxmTmsXlXlLyEntity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            saveEntity = omsOrderFwxmTmsXlXlLyDao.updateByEntityId(omsOrderFwxmTmsXlXlLyEntity);
        }
        if (saveEntity != null) {
            String lyNo = saveEntity.getLyNo();
            QueryWrapper<OmsOrderFwxmTmsXlXlLyPcEntity> pcEntityQueryWrapper = new QueryWrapper<>();
            if (StringUtils.hasValue(entity.getOrderNo())) {
                pcEntityQueryWrapper.eq("ORDER_NO", entity.getOrderNo());
            }
            if (StringUtils.hasValue(entity.getPreNo())) {
                pcEntityQueryWrapper.eq("PRE_NO", entity.getPreNo());
            }
            if (StringUtils.hasValue(entity.getSeqNo())) {
                pcEntityQueryWrapper.eq("SEQ_NO", entity.getSeqNo());
            }
            pcEntityQueryWrapper.eq("GROUP_CODE", UserContext.getUserInfo().getCompanyCode());
            omsOrderFwxmTmsXlXlLyPcDao.delete(pcEntityQueryWrapper);
            pcEntityQueryWrapper.clear();
            if (StringUtils.hasValue(entity.getOrderNo())) {
                pcEntityQueryWrapper.eq("ORDER_NO", entity.getOrderNo());
            }
            if (StringUtils.hasValue(entity.getPreNo())) {
                pcEntityQueryWrapper.eq("PRE_NO", entity.getPreNo());
            }
            if (StringUtils.hasValue(entity.getSeqNo())) {
                pcEntityQueryWrapper.eq("SEQ_NO", entity.getSeqNo());
            }
            OmsOrderFwxmTmsXlXlLyPcEntity pcList = omsOrderFwxmTmsXlXlLyPcDao.selectOne(pcEntityQueryWrapper);

            if (entity.getCyfs().equals("ZCZP")) {
                OmsOrderFwxmTmsXlXlLyPcEntity pcEntity = new OmsOrderFwxmTmsXlXlLyPcEntity();
                pcEntity.setGuid(IdWorker.get32UUID());
                pcEntity.setLyNo(lyNo);
                pcEntity.setSeqNo(zhysXlEntity.getSeqNo());
                pcEntity.setOrderNo(entity.getOrderNo());
                pcEntity.setPreNo(entity.getPreNo());
                pcEntity.setLineNo(zhysXlEntity.getLineNo());
                if (pcList == null) {
                    pcEntity.setBpSeqNo(zhysXlEntity.getXzrwbh());
                    if (entity.getIsZp().equals("Y")) {
                        pcEntity.setZpSeqNo(zhysXlEntity.getXzrwbh());
                    }
                    pcEntityQueryWrapper.clear();
                    pcEntityQueryWrapper.eq("ZP_SEQ_NO", pcEntity.getBpSeqNo());
                    if (omsOrderFwxmTmsXlXlLyPcDao.selectCount(pcEntityQueryWrapper) >= 2) {
                        throw new BaseException("该条为主拼单数据，存在被拼单数据，不能修改！");
                    }
                } else {
                    pcEntity.setBpSeqNo(pcList.getBpSeqNo());
                    pcEntity.setZpSeqNo(pcList.getZpSeqNo());

                }
                pcEntity.setCreateDate(DateUtils.getNowDate());
                pcEntity.setCreateUser(UserContext.getUserInfo().getUserId());
                pcEntity.setCreateUserName(UserContext.getUserInfo().getTrueName());
                pcEntity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
                pcEntity.setCompanyName(UserContext.getUserInfo().getCompanyName());
                pcEntity.setNodeCode(UserContext.getUserInfo().getDeptCode());
                pcEntity.setNodeName(UserContext.getUserInfo().getDeptName());
                pcEntity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
                pcEntity.setGroupName(UserContext.getUserInfo().getCompanyName());
                omsOrderFwxmTmsXlXlLyPcDao.insertOne(pcEntity);
            }


            QueryWrapper<OmsOrderFwxmTmsXlXlLyXlEntity> queryWrapper_xl = new QueryWrapper<>();
            if (StringUtils.hasValue(entity.getOrderNo())) {
                queryWrapper_xl.eq("ORDER_NO", entity.getOrderNo());
            }
            if (StringUtils.hasValue(entity.getPreNo())) {
                queryWrapper_xl.eq("PRE_NO", entity.getPreNo());
            }
            queryWrapper_xl.eq("LY_NO", lyNo);
            omsOrderFwxmTmsXlXlLyXlDao.delete(queryWrapper_xl);
            for (OmsOrderFwxmTmsXlXlLyXlEntity xlEntity : xlList) {
                xlEntity.setGuid(IdWorker.get32UUID());
                xlEntity.setLyNo(lyNo);
                xlEntity.setOrderNo(entity.getOrderNo());
                xlEntity.setPreNo(entity.getPreNo());
                xlEntity.setCreateDate(DateUtils.getNowDate());
                xlEntity.setCreateUser(UserContext.getUserInfo().getUserId());
                xlEntity.setCreateUserName(UserContext.getUserInfo().getTrueName());
                xlEntity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
                xlEntity.setCompanyName(UserContext.getUserInfo().getCompanyName());
                xlEntity.setNodeCode(UserContext.getUserInfo().getDeptCode());
                xlEntity.setNodeName(UserContext.getUserInfo().getDeptName());
                xlEntity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
                xlEntity.setGroupName(UserContext.getUserInfo().getCompanyName());
                if (xlEntity.getXlType().equals("QS") && zhysXlEntity != null) {
                    zhysXlEntity.setNationalidCounty(xlEntity.getCounty());
                    zhysXlEntity.setNationalidProvince(xlEntity.getProvince());
                    zhysXlEntity.setNationalidCity(xlEntity.getCity());
                    zhysXlEntity.setNationalidRegion(xlEntity.getRegion());
                    zhysXlEntity.setNationalidTown(xlEntity.getTown());
                }
                if (xlEntity.getXlType().equals("ZD") && zhysXlEntity != null) {
                    zhysXlEntity.setTerminusidCountry(xlEntity.getCounty());
                    zhysXlEntity.setTerminusidProvince(xlEntity.getProvince());
                    zhysXlEntity.setTerminusidCity(xlEntity.getCity());
                    zhysXlEntity.setTerminusidRegion(xlEntity.getRegion());
                    zhysXlEntity.setTerminusidTown(xlEntity.getTown());
                }
            }
            if (addZhysXl) {
                omsOrderFwxmZhysXlDao.insertOne(zhysXlEntity);
            } else {
                omsOrderFwxmZhysXlDao.updateByEntityId(zhysXlEntity);
            }
            omsOrderFwxmTmsXlXlLyXlDao.insertList(xlList);

            QueryWrapper<OmsOrderFwxmTmsXlXlLyClEntity> queryWrapper_cl = new QueryWrapper<>();
            if (StringUtils.hasValue(entity.getOrderNo())) {
                queryWrapper_cl.eq("ORDER_NO", entity.getOrderNo());
            }
            if (StringUtils.hasValue(entity.getPreNo())) {
                queryWrapper_cl.eq("PRE_NO", entity.getPreNo());
            }
            queryWrapper_cl.eq("LY_NO", lyNo);
            omsOrderFwxmTmsXlXlLyClDao.delete(queryWrapper_cl);
            for (OmsOrderFwxmTmsXlXlLyClEntity clEntity : clList){
                clEntity.setGuid(IdWorker.get32UUID());
                clEntity.setLyNo(lyNo);
                clEntity.setOrderNo(entity.getOrderNo());
                clEntity.setPreNo(entity.getPreNo());
                clEntity.setCreateDate(DateUtils.getNowDate());
                clEntity.setCreateUser(UserContext.getUserInfo().getUserId());
                clEntity.setCreateUserName(UserContext.getUserInfo().getTrueName());
                clEntity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
                clEntity.setCompanyName(UserContext.getUserInfo().getCompanyName());
                clEntity.setNodeCode(UserContext.getUserInfo().getDeptCode());
                clEntity.setNodeName(UserContext.getUserInfo().getDeptName());
                clEntity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
                clEntity.setGroupName(UserContext.getUserInfo().getCompanyName());
            }
            omsOrderFwxmTmsXlXlLyClDao.insertList(clList);

        }
        return entity;
    }

    @Override
    public List<OmsOrderFwxmTmsXlXlLyEntity> selectList(OmsOrderFwxmTmsXlXlLyEntity entity) {
        return omsOrderFwxmTmsXlXlLyDao.selectList(entity);
    }

    @Override
    public OmsOrderFwxmTmsXlXlLyEntity selectOneById(Serializable id) {
        return omsOrderFwxmTmsXlXlLyDao.selectById(id);
    }

    public OmsOrderFwxmTmsXlXlLyDTOEntity selectOneByEntity(OmsOrderFwxmTmsXlXlLyEntity entity) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT A.*,B.LINE_NO as ZHYSXL_NO,B.NATIONALID_COUNTY,B.NATIONALID_TOWN,B.TERMINUSID_COUNTRY,B.TERMINUSID_TOWN,B.IS_WB,B.GYS as GYSCODE, B.NBZYZZ AS NODE_CODE_NB,B.XZRWBH, ");
        sql.append("  PC.ZP_SEQ_NO,(SELECT COUNT(1) FROM OMS_ORDER_FWXM_TMS_XL_XL_LY_PC P WHERE P.ZP_SEQ_NO = PC.ZP_SEQ_NO AND P.GROUP_CODE = PC.GROUP_CODE ) ZP_NUM  ");
        sql.append(" FROM OMS_ORDER_FWXM_ZHYS_XL B");
        sql.append(" LEFT JOIN OMS_ORDER_FWXM_TMS_XL_XL_LY A ON B.LINE_NO = A.LINE_NO AND B.GROUP_CODE = A.GROUP_CODE");
        sql.append(" LEFT JOIN OMS_ORDER_FWXM_TMS_XL_XL_LY_PC PC ON PC.LY_NO = A.LY_NO AND PC.GROUP_CODE = A.GROUP_CODE");
        sql.append(" where B.GROUP_CODE ='" + UserContext.getUserInfo().getCompanyCode() + "' ");
        sql.append(" and (A.PRE_NO ='" + entity.getPreNo()+"' OR A.ORDER_NO ='"+entity.getOrderNo()+"')");
        sql.append(" and A.FWXM_CODE ='" + entity.getFwxmCode()+"'");
        if (StringUtils.hasValue(entity.getLineNo())) {
            sql.append(" and A.LINE_NO =" + EciSqlUtl.SQLQ(entity.getLineNo()));
        }
        List<OmsOrderFwxmTmsXlXlLyDTOEntity> list = DBHelper.selectList(sql.toString(), OmsOrderFwxmTmsXlXlLyDTOEntity.class);
        return list.size() > 0 ? list.get(0) : new OmsOrderFwxmTmsXlXlLyDTOEntity();
    }

    @Override
    public void insertBatch(List<OmsOrderFwxmTmsXlXlLyEntity> list) {
        omsOrderFwxmTmsXlXlLyDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return omsOrderFwxmTmsXlXlLyDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return omsOrderFwxmTmsXlXlLyDao.deleteById(id);
    }
}