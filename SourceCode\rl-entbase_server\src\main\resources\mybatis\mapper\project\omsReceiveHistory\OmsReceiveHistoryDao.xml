<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.omsReceiveHistory.dao.OmsReceiveHistoryDao">
    <resultMap type="OmsReceiveHistoryEntity" id="OmsReceiveHistoryResult">
        <result property="guid" column="GUID"/>
        <result property="jsonbody" column="JSONBODY"/>
        <result property="orderno" column="ORDERNO"/>
        <result property="workno" column="WORKNO"/>
        <result property="createdate" column="CREATEDATE"/>
    </resultMap>

    <sql id="selectOmsReceiveHistoryEntityVo">
        select
            GUID,
            JSONBODY,
            ORDERNO,
            WORKNO,
            CREATEDATE
        from OMS_RECEIVE_HISTORY
    </sql>
</mapper>