package com.eci.project.etmsBdDriverCertificate.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.etmsBdDriverCertificate.entity.EtmsBdDriverCertificateSearchEntity;
import com.eci.project.etmsBdDriverCertificate.service.EtmsBdDriverCertificateService;
import com.eci.project.etmsBdDriverCertificate.entity.EtmsBdDriverCertificateEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 司机证件管理Controller
*
* @<NAME_EMAIL>
* @date 2025-04-28
*/
@Api(tags = "司机证件管理")
@RestController
@RequestMapping("/etmsBdDriverCertificate")
public class EtmsBdDriverCertificateController extends EciBaseController {

    @Autowired
    private EtmsBdDriverCertificateService etmsBdDriverCertificateService;


    @ApiOperation("司机证件管理:保存")
    @EciLog(title = "司机证件管理:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody EtmsBdDriverCertificateEntity entity){
        EtmsBdDriverCertificateEntity etmsBdDriverCertificateEntity =etmsBdDriverCertificateService.save(entity);
        return ResponseMsgUtil.success(10001,etmsBdDriverCertificateEntity);
    }


    @ApiOperation("司机证件管理:查询列表")
    @EciLog(title = "司机证件管理:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody EtmsBdDriverCertificateEntity entity){
        List<EtmsBdDriverCertificateEntity> etmsBdDriverCertificateEntities = etmsBdDriverCertificateService.selectList(entity);
        return ResponseMsgUtil.success(10001,etmsBdDriverCertificateEntities);
    }


    @ApiOperation("司机证件管理:分页查询列表")
    @EciLog(title = "司机证件管理:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody EtmsBdDriverCertificateEntity entity){
        TgPageInfo tgPageInfo = etmsBdDriverCertificateService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("司机证件管理:根据ID查一条")
    @EciLog(title = "司机证件管理:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody EtmsBdDriverCertificateEntity entity){
        EtmsBdDriverCertificateEntity  etmsBdDriverCertificateEntity = etmsBdDriverCertificateService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,etmsBdDriverCertificateEntity);
    }


    @ApiOperation("司机证件管理:根据ID删除一条")
    @EciLog(title = "司机证件管理:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody EtmsBdDriverCertificateEntity entity){
        int count = etmsBdDriverCertificateService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("司机证件管理:根据ID字符串删除多条")
    @EciLog(title = "司机证件管理:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody EtmsBdDriverCertificateEntity entity) {
        int count = etmsBdDriverCertificateService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }
    @ApiOperation("司机资质审核:司机证件管理分页查询列表")
    @EciLog(title = "司机资质审核:司机证件管理分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectCertificatePageList")
    @EciAction()
    public ResponseMsg selectCertificatePageList(@RequestBody EtmsBdDriverCertificateSearchEntity entity){
        TgPageInfo tgPageInfo = etmsBdDriverCertificateService.queryCertificatePageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }

}