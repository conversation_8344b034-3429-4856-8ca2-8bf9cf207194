package com.eci.project.etmsBdTruck.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;

import java.math.BigDecimal;
import java.util.Date;


/**
* 车辆信息对象 ETMS_BD_TRUCK
*
* @<NAME_EMAIL>
* @date 2025-04-11
*/
@FieldNameConstants
public class EtmsBdTruckBaseEntity extends EciBaseEntity
{
	/**
	* GUID
	*/
	@ApiModelProperty("GUID(50)")
	@TableField("GUID")
	private String guid;

	/**
	* 车辆规格GUID
	*/
	@ApiModelProperty("车辆规格GUID(50)")
	@TableField("TRUCK_SPCE_GUID")
	private String truckSpceGuid;

	/**
	* 车牌号
	*/
	@ApiModelProperty("车牌号(20)")
	@TableField("TRUCK_NO")
	private String truckNo;

	/**
	* 是否挂靠
	*/
	@ApiModelProperty("是否挂靠(1)")
	@TableField("IS_GK")
	private String isGk;

	/**
	* 车主业务伙伴GUID
	*/
	@ApiModelProperty("车主业务伙伴GUID(50)")
	@TableField("PARTNER_GUID")
	private String partnerGuid;

	/**
	* 定位方式
	*/
	@ApiModelProperty("定位方式(20)")
	@TableField("GPS_MODE")
	private String gpsMode;

	/**
	* 定位设备编号
	*/
	@ApiModelProperty("定位设备编号(40)")
	@TableField("GPS_NO")
	private String gpsNo;

	/**
	* 默认驾驶人GUID
	*/
	@ApiModelProperty("默认驾驶人GUID(50)")
	@TableField("DRIVER_GUID")
	private String driverGuid;

	/**
	* 状态  默认空闲
	*/
	@ApiModelProperty("状态  默认空闲(1)")
	@TableField("STATUS")
	private String status;

	/**
	* 创建时间
	*/
	@ApiModelProperty("创建时间(7)")
	@TableField("CREATE_DATE")
	private Date createDate;

	@ApiModelProperty("创建时间开始")
	@TableField(exist=false)
	private Date createDateStart;

	@ApiModelProperty("创建时间结束")
	@TableField(exist=false)
	private Date createDateEnd;

	/**
	* 创建人
	*/
	@ApiModelProperty("创建人(50)")
	@TableField("CREATE_USER")
	private String createUser;

	/**
	* 创建人企业
	*/
	@ApiModelProperty("创建人企业(50)")
	@TableField("CREATE_COMPANY")
	private String createCompany;

	/**
	* 修改时间
	*/
	@ApiModelProperty("修改时间(7)")
	@TableField("UPDATE_DATE")
	private Date updateDate;

	@ApiModelProperty("修改时间开始")
	@TableField(exist=false)
	private Date updateDateStart;

	@ApiModelProperty("修改时间结束")
	@TableField(exist=false)
	private Date updateDateEnd;

	/**
	* 修改人
	*/
	@ApiModelProperty("修改人(50)")
	@TableField("UPDATE_USER")
	private String updateUser;

	/**
	* 备注
	*/
	@ApiModelProperty("备注(200)")
	@TableField("MEMO")
	private String memo;

	/**
	* 状态启用
	*/
	@ApiModelProperty("状态启用(1)")
	@TableField("IS_USER")
	private String isUser;

	/**
	* 理论油耗
	*/
	@ApiModelProperty("理论油耗(22)")
	@TableField("LL_OIL")
	private BigDecimal llOil;

	/**
	* 行驶证日期
	*/
	@ApiModelProperty("行驶证日期(7)")
	@TableField("LICENSE_DATE")
	private Date licenseDate;

	@ApiModelProperty("行驶证日期开始")
	@TableField(exist=false)
	private Date licenseDateStart;

	@ApiModelProperty("行驶证日期结束")
	@TableField(exist=false)
	private Date licenseDateEnd;

	/**
	* 车辆定级日期
	*/
	@ApiModelProperty("车辆定级日期(7)")
	@TableField("RATING_DATE")
	private Date ratingDate;

	@ApiModelProperty("车辆定级日期开始")
	@TableField(exist=false)
	private Date ratingDateStart;

	@ApiModelProperty("车辆定级日期结束")
	@TableField(exist=false)
	private Date ratingDateEnd;

	/**
	* 营运证日期
	*/
	@ApiModelProperty("营运证日期(7)")
	@TableField("OPERATION_DATE")
	private Date operationDate;

	@ApiModelProperty("营运证日期开始")
	@TableField(exist=false)
	private Date operationDateStart;

	@ApiModelProperty("营运证日期结束")
	@TableField(exist=false)
	private Date operationDateEnd;

	/**
	* 货车类型
	*/
	@ApiModelProperty("货车类型(20)")
	@TableField("CAR_TYPE")
	private String carType;

	/**
	* 常住地
	*/
	@ApiModelProperty("常住地(50)")
	@TableField("RESIDE")
	private String reside;

	/**
	* 车源联系人
	*/
	@ApiModelProperty("车源联系人(30)")
	@TableField("LINK")
	private String link;

	/**
	* 车源联系电话
	*/
	@ApiModelProperty("车源联系电话(30)")
	@TableField("TEL")
	private String tel;

	/**
	* 车辆识别代号
	*/
	@ApiModelProperty("车辆识别代号(30)")
	@TableField("TRUCK_VIN")
	private String truckVin;

	/**
	* 车长(米)
	*/
	@ApiModelProperty("车长(米)(22)")
	@TableField("TLENGTH")
	private BigDecimal tlength;

	/**
	* 车重（吨）
	*/
	@ApiModelProperty("车重（吨）(22)")
	@TableField("TWEIGHT")
	private BigDecimal tweight;

	@ApiModelProperty("(50)")
	@TableField("TRAILER_NO")
	private String trailerNo;

	/**
	* 车辆性质
	*/
	@ApiModelProperty("车辆性质(20)")
	@TableField("ATTRIBUTE_CODE")
	private String attributeCode;

	/**
	* 修改人所属部门ID
	*/
	@ApiModelProperty("修改人所属部门ID(50)")
	@TableField("ORG_DEP_ID")
	private String orgDepId;

	/**
	* 修改人所属部门CODE
	*/
	@ApiModelProperty("修改人所属部门CODE(50)")
	@TableField("ORG_DEP_CODE")
	private String orgDepCode;

	/**
	* 修改人所属部门名称
	*/
	@ApiModelProperty("修改人所属部门名称(50)")
	@TableField("ORG_DEP_NAME")
	private String orgDepName;

	@ApiModelProperty("(50)")
	@TableField("SSO_COMPANY_GUID")
	private String ssoCompanyGuid;

	/**
	* 所属企业代码
	*/
	@ApiModelProperty("所属企业代码(20)")
	@TableField("ORG_CODE")
	private String orgCode;

	/**
	* 所属企业名称
	*/
	@ApiModelProperty("所属企业名称(200)")
	@TableField("ORG_NAME")
	private String orgName;

	/**
	* 归属公司
	*/
	@ApiModelProperty("归属公司(50)")
	@TableField("COMPANY_CODE")
	private String companyCode;

	/**
	* 归属公司
	*/
	@ApiModelProperty("归属公司(50)")
	@TableField("COMPANY_NAME")
	private String companyName;

	/**
	* 归属组织
	*/
	@ApiModelProperty("归属组织(50)")
	@TableField("NODE_CODE")
	private String nodeCode;

	/**
	* 归属组织
	*/
	@ApiModelProperty("归属组织(50)")
	@TableField("NODE_NAME")
	private String nodeName;

	/**
	* 归属集团
	*/
	@ApiModelProperty("归属集团(50)")
	@TableField("GROUP_CODE")
	private String groupCode;

	/**
	* 归属集团
	*/
	@ApiModelProperty("归属集团(50)")
	@TableField("GROUP_NAME")
	private String groupName;

	/**
	* 创建人
	*/
	@ApiModelProperty("创建人(50)")
	@TableField("CREATE_USER_NAME")
	private String createUserName;

	/**
	* 最后修改人
	*/
	@ApiModelProperty("最后修改人(50)")
	@TableField("UPDATE_USER_NAME")
	private String updateUserName;

	/**
	* 整备质量(KG)
	*/
	@ApiModelProperty("整备质量(KG)(22)")
	@TableField("ZBZL_KG")
	private BigDecimal zbzlKg;

	/**
	* 计费车辆类型
	*/
	@ApiModelProperty("计费车辆类型(50)")
	@TableField("CLLX")
	private String cllx;

	/**
	* 计费车辆规格
	*/
	@ApiModelProperty("计费车辆规格(50)")
	@TableField("CLCC")
	private String clcc;

	@ApiModelProperty("(10)")
	@TableField("CAR_COLOR")
	private String carColor;

	/**
	* 审批状态（ZC：暂存，SS：送审，TH：退回，SX：已生效）
	*/
	@ApiModelProperty("审批状态（ZC：暂存，SS：送审，TH：退回，SX：已生效）(10)")
	@TableField("CHECK_STATUS")
	private String checkStatus;

	/**
	* 审批人
	*/
	@ApiModelProperty("审批人(50)")
	@TableField("CHECK_USER")
	private String checkUser;

	/**
	* 审批意见
	*/
	@ApiModelProperty("审批意见(4,000)")
	@TableField("CHECK_RMK")
	private String checkRmk;

	/**
	* 行驶证车辆类型
	*/
	@ApiModelProperty("行驶证车辆类型(50)")
	@TableField("TRUCK_TYPE")
	private String truckType;

	/**
	* 车辆尺寸
	*/
	@ApiModelProperty("车辆尺寸(22)")
	@TableField("CAR_LONG")
	private BigDecimal carLong;

	/**
	* 车辆尺寸类型
	*/
	@ApiModelProperty("车辆尺寸类型(50)")
	@TableField("CAR_LONG_TYPE")
	private String carLongType;

	/**
	* 是否满足补贴
	*/
	@ApiModelProperty("是否满足补贴(50)")
	@TableField("IS_BT")
	private String isBt;

	@JsonIgnore
	@TableField(exist = false)
	private Class<?> clazz = this.getClass();

	public Class<?> getClazz() {
		return clazz;
	}

	public void setClazz(Class<?> clazz) {
		this.clazz = clazz;
	}

	public EtmsBdTruckBaseEntity setGuid(String guid) {
		this.guid = guid;
		return this;
	}

	public String getGuid() {
		return guid;
	}

	public EtmsBdTruckBaseEntity setTruckSpceGuid(String truckSpceGuid) {
		this.truckSpceGuid = truckSpceGuid;
		return this;
	}

	public String getTruckSpceGuid() {
		return truckSpceGuid;
	}

	public EtmsBdTruckBaseEntity setTruckNo(String truckNo) {
		this.truckNo = truckNo;
		return this;
	}

	public String getTruckNo() {
		return truckNo;
	}

	public EtmsBdTruckBaseEntity setIsGk(String isGk) {
		this.isGk = isGk;
		return this;
	}

	public String getIsGk() {
		return isGk;
	}

	public EtmsBdTruckBaseEntity setPartnerGuid(String partnerGuid) {
		this.partnerGuid = partnerGuid;
		return this;
	}

	public String getPartnerGuid() {
		return partnerGuid;
	}

	public EtmsBdTruckBaseEntity setGpsMode(String gpsMode) {
		this.gpsMode = gpsMode;
		return this;
	}

	public String getGpsMode() {
		return gpsMode;
	}

	public EtmsBdTruckBaseEntity setGpsNo(String gpsNo) {
		this.gpsNo = gpsNo;
		return this;
	}

	public String getGpsNo() {
		return gpsNo;
	}

	public EtmsBdTruckBaseEntity setDriverGuid(String driverGuid) {
		this.driverGuid = driverGuid;
		return this;
	}

	public String getDriverGuid() {
		return driverGuid;
	}

	public EtmsBdTruckBaseEntity setStatus(String status) {
		this.status = status;
		return this;
	}

	public String getStatus() {
		return status;
	}

	public EtmsBdTruckBaseEntity setCreateDate(Date createDate) {
		this.createDate = createDate;
		return this;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public EtmsBdTruckBaseEntity setCreateDateStart(Date createDateStart) {
		this.createDateStart = createDateStart;
		return this;
	}

	public Date getCreateDateStart() {
		return createDateStart;
	}

	public EtmsBdTruckBaseEntity setCreateDateEnd(Date createDateEnd) {
		this.createDateEnd = createDateEnd;
		return this;
	}

	public Date getCreateDateEnd() {
		return createDateEnd;
	}
	public EtmsBdTruckBaseEntity setCreateUser(String createUser) {
		this.createUser = createUser;
		return this;
	}

	public String getCreateUser() {
		return createUser;
	}

	public EtmsBdTruckBaseEntity setCreateCompany(String createCompany) {
		this.createCompany = createCompany;
		return this;
	}

	public String getCreateCompany() {
		return createCompany;
	}

	public EtmsBdTruckBaseEntity setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
		return this;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public EtmsBdTruckBaseEntity setUpdateDateStart(Date updateDateStart) {
		this.updateDateStart = updateDateStart;
		return this;
	}

	public Date getUpdateDateStart() {
		return updateDateStart;
	}

	public EtmsBdTruckBaseEntity setUpdateDateEnd(Date updateDateEnd) {
		this.updateDateEnd = updateDateEnd;
		return this;
	}

	public Date getUpdateDateEnd() {
		return updateDateEnd;
	}
	public EtmsBdTruckBaseEntity setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
		return this;
	}

	public String getUpdateUser() {
		return updateUser;
	}

	public EtmsBdTruckBaseEntity setMemo(String memo) {
		this.memo = memo;
		return this;
	}

	public String getMemo() {
		return memo;
	}

	public EtmsBdTruckBaseEntity setIsUser(String isUser) {
		this.isUser = isUser;
		return this;
	}

	public String getIsUser() {
		return isUser;
	}

	public EtmsBdTruckBaseEntity setLlOil(BigDecimal llOil) {
		this.llOil = llOil;
		return this;
	}

	public BigDecimal getLlOil() {
		return llOil;
	}

	public EtmsBdTruckBaseEntity setLicenseDate(Date licenseDate) {
		this.licenseDate = licenseDate;
		return this;
	}

	public Date getLicenseDate() {
		return licenseDate;
	}

	public EtmsBdTruckBaseEntity setLicenseDateStart(Date licenseDateStart) {
		this.licenseDateStart = licenseDateStart;
		return this;
	}

	public Date getLicenseDateStart() {
		return licenseDateStart;
	}

	public EtmsBdTruckBaseEntity setLicenseDateEnd(Date licenseDateEnd) {
		this.licenseDateEnd = licenseDateEnd;
		return this;
	}

	public Date getLicenseDateEnd() {
		return licenseDateEnd;
	}
	public EtmsBdTruckBaseEntity setRatingDate(Date ratingDate) {
		this.ratingDate = ratingDate;
		return this;
	}

	public Date getRatingDate() {
		return ratingDate;
	}

	public EtmsBdTruckBaseEntity setRatingDateStart(Date ratingDateStart) {
		this.ratingDateStart = ratingDateStart;
		return this;
	}

	public Date getRatingDateStart() {
		return ratingDateStart;
	}

	public EtmsBdTruckBaseEntity setRatingDateEnd(Date ratingDateEnd) {
		this.ratingDateEnd = ratingDateEnd;
		return this;
	}

	public Date getRatingDateEnd() {
		return ratingDateEnd;
	}
	public EtmsBdTruckBaseEntity setOperationDate(Date operationDate) {
		this.operationDate = operationDate;
		return this;
	}

	public Date getOperationDate() {
		return operationDate;
	}

	public EtmsBdTruckBaseEntity setOperationDateStart(Date operationDateStart) {
		this.operationDateStart = operationDateStart;
		return this;
	}

	public Date getOperationDateStart() {
		return operationDateStart;
	}

	public EtmsBdTruckBaseEntity setOperationDateEnd(Date operationDateEnd) {
		this.operationDateEnd = operationDateEnd;
		return this;
	}

	public Date getOperationDateEnd() {
		return operationDateEnd;
	}
	public EtmsBdTruckBaseEntity setCarType(String carType) {
		this.carType = carType;
		return this;
	}

	public String getCarType() {
		return carType;
	}

	public EtmsBdTruckBaseEntity setReside(String reside) {
		this.reside = reside;
		return this;
	}

	public String getReside() {
		return reside;
	}

	public EtmsBdTruckBaseEntity setLink(String link) {
		this.link = link;
		return this;
	}

	public String getLink() {
		return link;
	}

	public EtmsBdTruckBaseEntity setTel(String tel) {
		this.tel = tel;
		return this;
	}

	public String getTel() {
		return tel;
	}

	public EtmsBdTruckBaseEntity setTruckVin(String truckVin) {
		this.truckVin = truckVin;
		return this;
	}

	public String getTruckVin() {
		return truckVin;
	}

	public EtmsBdTruckBaseEntity setTlength(BigDecimal tlength) {
		this.tlength = tlength;
		return this;
	}

	public BigDecimal getTlength() {
		return tlength;
	}

	public EtmsBdTruckBaseEntity setTweight(BigDecimal tweight) {
		this.tweight = tweight;
		return this;
	}

	public BigDecimal getTweight() {
		return tweight;
	}

	public EtmsBdTruckBaseEntity setTrailerNo(String trailerNo) {
		this.trailerNo = trailerNo;
		return this;
	}

	public String getTrailerNo() {
		return trailerNo;
	}

	public EtmsBdTruckBaseEntity setAttributeCode(String attributeCode) {
		this.attributeCode = attributeCode;
		return this;
	}

	public String getAttributeCode() {
		return attributeCode;
	}

	public EtmsBdTruckBaseEntity setOrgDepId(String orgDepId) {
		this.orgDepId = orgDepId;
		return this;
	}

	public String getOrgDepId() {
		return orgDepId;
	}

	public EtmsBdTruckBaseEntity setOrgDepCode(String orgDepCode) {
		this.orgDepCode = orgDepCode;
		return this;
	}

	public String getOrgDepCode() {
		return orgDepCode;
	}

	public EtmsBdTruckBaseEntity setOrgDepName(String orgDepName) {
		this.orgDepName = orgDepName;
		return this;
	}

	public String getOrgDepName() {
		return orgDepName;
	}

	public EtmsBdTruckBaseEntity setSsoCompanyGuid(String ssoCompanyGuid) {
		this.ssoCompanyGuid = ssoCompanyGuid;
		return this;
	}

	public String getSsoCompanyGuid() {
		return ssoCompanyGuid;
	}

	public EtmsBdTruckBaseEntity setOrgCode(String orgCode) {
		this.orgCode = orgCode;
		return this;
	}

	public String getOrgCode() {
		return orgCode;
	}

	public EtmsBdTruckBaseEntity setOrgName(String orgName) {
		this.orgName = orgName;
		return this;
	}

	public String getOrgName() {
		return orgName;
	}

	public EtmsBdTruckBaseEntity setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
		return this;
	}

	public String getCompanyCode() {
		return companyCode;
	}

	public EtmsBdTruckBaseEntity setCompanyName(String companyName) {
		this.companyName = companyName;
		return this;
	}

	public String getCompanyName() {
		return companyName;
	}

	public EtmsBdTruckBaseEntity setNodeCode(String nodeCode) {
		this.nodeCode = nodeCode;
		return this;
	}

	public String getNodeCode() {
		return nodeCode;
	}

	public EtmsBdTruckBaseEntity setNodeName(String nodeName) {
		this.nodeName = nodeName;
		return this;
	}

	public String getNodeName() {
		return nodeName;
	}

	public EtmsBdTruckBaseEntity setGroupCode(String groupCode) {
		this.groupCode = groupCode;
		return this;
	}

	public String getGroupCode() {
		return groupCode;
	}

	public EtmsBdTruckBaseEntity setGroupName(String groupName) {
		this.groupName = groupName;
		return this;
	}

	public String getGroupName() {
		return groupName;
	}

	public EtmsBdTruckBaseEntity setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
		return this;
	}

	public String getCreateUserName() {
		return createUserName;
	}

	public EtmsBdTruckBaseEntity setUpdateUserName(String updateUserName) {
		this.updateUserName = updateUserName;
		return this;
	}

	public String getUpdateUserName() {
		return updateUserName;
	}

	public EtmsBdTruckBaseEntity setZbzlKg(BigDecimal zbzlKg) {
		this.zbzlKg = zbzlKg;
		return this;
	}

	public BigDecimal getZbzlKg() {
		return zbzlKg;
	}

	public EtmsBdTruckBaseEntity setCllx(String cllx) {
		this.cllx = cllx;
		return this;
	}

	public String getCllx() {
		return cllx;
	}

	public EtmsBdTruckBaseEntity setClcc(String clcc) {
		this.clcc = clcc;
		return this;
	}

	public String getClcc() {
		return clcc;
	}

	public EtmsBdTruckBaseEntity setCarColor(String carColor) {
		this.carColor = carColor;
		return this;
	}

	public String getCarColor() {
		return carColor;
	}

	public EtmsBdTruckBaseEntity setCheckStatus(String checkStatus) {
		this.checkStatus = checkStatus;
		return this;
	}

	public String getCheckStatus() {
		return checkStatus;
	}

	public EtmsBdTruckBaseEntity setCheckUser(String checkUser) {
		this.checkUser = checkUser;
		return this;
	}

	public String getCheckUser() {
		return checkUser;
	}

	public EtmsBdTruckBaseEntity setCheckRmk(String checkRmk) {
		this.checkRmk = checkRmk;
		return this;
	}

	public String getCheckRmk() {
		return checkRmk;
	}

	public EtmsBdTruckBaseEntity setTruckType(String truckType) {
		this.truckType = truckType;
		return this;
	}

	public String getTruckType() {
		return truckType;
	}

	public EtmsBdTruckBaseEntity setCarLong(BigDecimal carLong) {
		this.carLong = carLong;
		return this;
	}

	public BigDecimal getCarLong() {
		return carLong;
	}

	public EtmsBdTruckBaseEntity setCarLongType(String carLongType) {
		this.carLongType = carLongType;
		return this;
	}

	public String getCarLongType() {
		return carLongType;
	}

	public EtmsBdTruckBaseEntity setIsBt(String isBt) {
		this.isBt = isBt;
		return this;
	}

	public String getIsBt() {
		return isBt;
	}

}
