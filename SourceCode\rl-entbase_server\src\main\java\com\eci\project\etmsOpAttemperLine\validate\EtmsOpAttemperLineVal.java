package com.eci.project.etmsOpAttemperLine.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.etmsOpAttemperLine.entity.EtmsOpAttemperLineEntity;

import org.springframework.stereotype.Service;


/**
* 托运线路站点信息Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-06-13
*/
@Service
public class EtmsOpAttemperLineVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(EtmsOpAttemperLineEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(EtmsOpAttemperLineEntity entity, BusinessType businessType) {

    }

}
