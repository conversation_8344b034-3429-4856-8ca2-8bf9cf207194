package com.eci.project.etmsTruckOrderPj.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eci.common.util.Validate;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.etmsTruckOrderPj.dao.EtmsTruckOrderPjDao;
import com.eci.project.etmsTruckOrderPj.entity.EtmsTruckOrderPjEntity;
import com.eci.project.etmsTruckOrderPj.entity.orderPj;
import com.eci.project.etmsTruckOrderPj.validate.EtmsTruckOrderPjVal;

import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.management.Query;
import java.io.Serializable;
import java.util.List;


/**
* Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-22
*/
@Service
@Slf4j
public class EtmsTruckOrderPjService implements EciBaseService<EtmsTruckOrderPjEntity> {

    @Autowired
    private EtmsTruckOrderPjDao etmsTruckOrderPjDao;

    @Autowired
    private EtmsTruckOrderPjVal etmsTruckOrderPjVal;


    @Override
    public TgPageInfo queryPageList(EtmsTruckOrderPjEntity entity) {
        EciQuery<EtmsTruckOrderPjEntity> eciQuery = EciQuery.buildQuery(entity);
        List<EtmsTruckOrderPjEntity> entities = etmsTruckOrderPjDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }

    public TgPageInfo queryPages(String driverName) {
        try{
            Validate.isTrue(BllContext.getPaging() == null, "分页查询,paging不能为空");
            Validate.isTrue(BllContext.getPaging().getPageNum() == 0, "分页查询,paging.pageNum不能为0");
            Integer pageSize = BllContext.getPaging().getPageSize();
            Integer pageNum = BllContext.getPaging().getPageNum();
            QueryWrapper query=new QueryWrapper();
            query.apply("A.DRIVER_NAME={0}",driverName);
            PageHelper.startPage(pageNum, pageSize == -1 ? 0 : pageSize);
            List<orderPj> entities = etmsTruckOrderPjDao.queryPages(query);
            return EciQuery.getPageInfo(entities);
        }catch (Throwable var5){
            throw var5;
        }
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public EtmsTruckOrderPjEntity save(EtmsTruckOrderPjEntity entity) {
        // 返回实体对象
        EtmsTruckOrderPjEntity etmsTruckOrderPjEntity = null;
        etmsTruckOrderPjVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            etmsTruckOrderPjEntity = etmsTruckOrderPjDao.insertOne(entity);

        }else{

            etmsTruckOrderPjEntity = etmsTruckOrderPjDao.updateByEntityId(entity);

        }
        return etmsTruckOrderPjEntity;
    }

    @Override
    public List<EtmsTruckOrderPjEntity> selectList(EtmsTruckOrderPjEntity entity) {
        return etmsTruckOrderPjDao.selectList(entity);
    }

    @Override
    public EtmsTruckOrderPjEntity selectOneById(Serializable id) {
        return etmsTruckOrderPjDao.selectById(id);
    }


    @Override
    public void insertBatch(List<EtmsTruckOrderPjEntity> list) {
        etmsTruckOrderPjDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return etmsTruckOrderPjDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return etmsTruckOrderPjDao.deleteById(id);
    }

}