package com.eci.common;

import com.eci.common.db.DBHelper;
import com.eci.common.util.StringUtils;
import com.eci.common.utils.CommonLib;
import com.eci.sso.role.entity.UserContext;
import com.eci.wu.core.DataTable;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.CacheStats;
import com.google.common.cache.LoadingCache;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * 数据字典查询工具类
 *
 * <AUTHOR>
 * @version 1.0
 * <remark>code转name工具类，自动查询 SYS_DATA_HELP 表数据，根据 query_key ，得到Code和Name的配置，然后自行缓存，而不依赖框架现有的缓存</remark>
 * @date 2025-1-13 11:25:47
 */
public class DataDictUtils {

    /**
     * 数据库的code字段
     */
    private final static String Code = "CODE";
    /**
     * 数据库的name字段
     */
    private final static String Name = "NAME";

    /**
     * 缓存时长，单位秒
     */
    public final static long cacheDuration = 15;

    /**
     * SQL模板
     */
    private static final String DATA_DICT_SQL_TEMPLATE = new StringBuilder(200)
            .append("DECLARE ")
            .append("  v_sql CLOB; ")
            .append("BEGIN ")
            .append("  SELECT SQL_COMMAND INTO v_sql ")
            .append("  FROM SYS_DATA_HELP ")
            .append("  WHERE query_key = ?  ; ")
            .append("   ")
            .append("  EXECUTE IMMEDIATE v_sql; ")
            .append("END")
            .toString();

    /**
     * 缓存配置
     */
    private static final LoadingCache<String, Map<String, CodeNameCommon>> CODE_CACHE = CacheBuilder.newBuilder()
            .maximumSize(1000)  // 最大缓存条目数
            .expireAfterWrite(cacheDuration, TimeUnit.SECONDS)  // 写入后xxx秒后过期
            .recordStats()  // 记录缓存统计信息
            .removalListener(notification -> {
            })
            .build(new CacheLoader<String, Map<String, CodeNameCommon>>() {
                @Override
                public Map<String, CodeNameCommon> load(String queryKey) {

                    return queryCodeNameMapFromDB(queryKey);
                }
            });

    /**
     * 缓存配置
     */
    private static final LoadingCache<String, HashMap<String, DataTable>> CODE_CACHE_DataTable = CacheBuilder.newBuilder()
            .maximumSize(1000)  // 最大缓存条目数
            .expireAfterWrite(cacheDuration, TimeUnit.SECONDS)  // 写入后xxx秒后过期
            .recordStats()  // 记录缓存统计信息
            .removalListener(notification -> {
            })
            .build(new CacheLoader<String, HashMap<String, DataTable>>() {
                @Override
                public HashMap<String, DataTable> load(String queryKey) {
                    return queryKeyFromDB(queryKey);
                }
            });


    /**
     * 从数据库查询代码映射
     *
     * @param queryKey 查询键
     * @return 代码-名称映射
     */
    private static Map<String, CodeNameCommon> queryCodeNameMapFromDB(String queryKey) {
        if (StringUtils.isBlank(queryKey)) {
            throw new IllegalArgumentException("queryKey cannot be null or empty");
        }

        try {
            // 为了兼容多个数据库，需要查询两次，第一次查询，获得sql语句 第二次查询，得到sql的结果
            String firstSql = "SELECT SQL_COMMAND FROM SYS_DATA_HELP WHERE query_key = ?";
            DataTable dataTable = DBHelper.getDataTable(firstSql, queryKey);
            String secondSql = new CaseInsensitiveRowWrapper(dataTable.rows.get(0)).get("SQL_COMMAND").toString();
            PrintUtil.customPrint("查询数据字典2 键 {} ", queryKey);
            // 第二次查询
            secondSql = secondSql.replace("#{companyCode}", cmn.SQLQ(UserContext.getUserInfo().getCompanyCode()));
            DataTable table = DBHelper.getDataTable(secondSql);
            // 主方法中使用
            HashMap<String, CodeNameCommon> codeNameCommonHashMap = table.rows.stream()
                    .parallel()   // 添加并行处理
                    .map(CaseInsensitiveRowWrapper::new)
                    .filter(row ->
                            row.get(Code) != null &&
                                    row.get(Name) != null
                    )
                    .collect(Collectors.toMap(
                            row -> row.get(Code).toString(),
                            row -> new CodeNameCommon(
                                    row.get(Code).toString(),
                                    row.get(Name).toString()
                            ),
                            (existing, replacement) -> existing,
                            HashMap::new
                    ));
            return codeNameCommonHashMap;

        } catch (Exception e) {
            PrintUtil.customErrorPrint("查询数据字典失败 键 {} 失败原因 {} ", queryKey, e);
            return new HashMap<>();
        }
    }

    /**
     * 查询代码映射（带缓存）
     *
     * @param queryKey 查询键
     * @return 代码-名称映射
     */
    public static Map<String, CodeNameCommon> queryCodeNameMap(String queryKey) {
        try {
            return CODE_CACHE.get(queryKey);
        } catch (ExecutionException e) {
            return new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
        }
    }

    /**
     * 查询代码映射（带缓存）
     *
     * @param queryKey 查询键
     * @return 代码-名称映射
     */
    public static Map<String, DataTable> queryKeyForDataTable(String queryKey) {
        try {
            HashMap<String, DataTable> stringDataTableHashMap = CODE_CACHE_DataTable.get(queryKey);
            return stringDataTableHashMap;
        } catch (ExecutionException e) {
            return new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
        }
    }

    static CommonLib cmn = CommonLib.getInstance();

    /**
     * 从数据库查询代码映射
     *
     * @param queryKey 查询键
     * @return 代码-名称映射
     */
    private static HashMap<String, DataTable> queryKeyFromDB(String queryKey) {
        if (StringUtils.isBlank(queryKey)) {
            throw new IllegalArgumentException("queryKey cannot be null or empty");
        }

        try {
            // 为了兼容多个数据库，需要查询两次，第一次查询，获得sql语句 第二次查询，得到sql的结果
            String firstSql = "SELECT SQL_COMMAND FROM SYS_DATA_HELP WHERE query_key = ?";
            DataTable dataTable = DBHelper.getDataTable(firstSql, queryKey);
            String secondSql = new CaseInsensitiveRowWrapper(dataTable.rows.get(0)).get("SQL_COMMAND").toString();
            secondSql = secondSql.replace("#{companyCode}", cmn.SQLQ(UserContext.getUserInfo().getCompanyCode()));
            PrintUtil.customPrint("查询数据字典2 键 {} ", queryKey);
            // 第二次查询
            DataTable table = DBHelper.getDataTable(secondSql);
            HashMap<String, DataTable> dataTableHashMap = new HashMap<>();
            dataTableHashMap.put(queryKey, table);
            return dataTableHashMap;

        } catch (Exception e) {
            PrintUtil.customErrorPrint("查询数据字典失败 键 {} 失败原因 {} ", queryKey, e);
            return new HashMap<>();
        }
    }


    /**
     * 查询代码映射（带缓存）
     *
     * @param queryKey 查询键
     * @return 代码-名称映射

    public static Map<String, String> queryCodeNameMap(String queryKey) {
    try {
    return CODE_CACHE.get(queryKey);
    } catch (ExecutionException e) {
    return queryCodeNameMapFromDB(queryKey);
    }
    }
     */


//    /**
//     * 根据代码获取名称（带缓存）
//     *
//     * @param queryKey 查询键
//     * @param code     代码值
//     * @return 对应的名称
//     */
//    public static String getNameByCode(String queryKey, String code) {
//        if (StringUtils.isBlank(code)) {
//            return "";
//        }
//        return queryCodeNameMap(queryKey).getOrDefault(code, new CodeNameCommon(code, "" ));
//    }

    /**
     * 根据代码获取名称（带缓存）
     *
     * @param queryKey 查询键
     * @param name     代码值
     * @return 对应的名称
     */
    public static String getNameByName(String queryKey, String name) {
        // 检查 name 是否为空，提前返回
        if (StringUtils.isBlank(name)) {
            return "";
        }

        // 获取查询结果并进行非空检查
        Map<String, CodeNameCommon> codeNameMap = queryCodeNameMap(queryKey);
        if (codeNameMap == null || codeNameMap.isEmpty()) {
            return "";
        }

        // 流式操作查找匹配的名称并返回代码
        return codeNameMap.values().stream()
                .filter(c -> name.equals(c.getName()))  // 避免空指针，通过 name.equals()
                .findFirst()
                .map(CodeNameCommon::getCode)
                .orElse(null);
    }

    /**
     * 批量查询代码映射
     *
     * @param queryKeys 查询键集合
     * @return 查询键-映射关系的Map
     */
    public static Map<String, Map<String, CodeNameCommon>> queryMultiCodeNameMap(Collection<String> queryKeys) {
        if (CollectionUtils.isEmpty(queryKeys)) {
            return new HashMap<>();
        }

        return queryKeys.stream()
                .distinct()
                .collect(Collectors.toMap(
                        key -> key,
                        DataDictUtils::queryCodeNameMap,
                        (existing, replacement) -> existing,
                        HashMap::new
                ));
    }

    /**
     * 异步查询代码映射
     *
     * @param queryKey 查询键
     * @return CompletableFuture包装的查询结果
     */
    public static CompletableFuture<Map<String, CodeNameCommon>> queryCodeNameMapAsync(String queryKey) {
        return CompletableFuture.supplyAsync(() -> queryCodeNameMap(queryKey));
    }

    /**
     * 清除指定键的缓存
     *
     * @param queryKey 查询键
     */
    public static void invalidateCache(String queryKey) {
        CODE_CACHE.invalidate(queryKey);
    }

    /**
     * 清除所有缓存
     */
    public static void invalidateAllCache() {
        CODE_CACHE.invalidateAll();
    }

    /**
     * 获取缓存统计信息
     *
     * @return 缓存统计信息
     */
    public static CacheStats getCacheStats() {
        return CODE_CACHE.stats();
    }
}
