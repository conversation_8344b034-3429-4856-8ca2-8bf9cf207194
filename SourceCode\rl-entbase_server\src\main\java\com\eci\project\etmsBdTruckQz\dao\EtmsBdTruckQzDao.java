package com.eci.project.etmsBdTruckQz.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.eci.project.fzgjBdArea.entity.FzgjBdAreaEntity;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.etmsBdTruckQz.entity.EtmsBdTruckQzEntity;

import java.util.List;


/**
* 车辆信息Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-04-09
*/
public interface EtmsBdTruckQzDao extends EciBaseDao<EtmsBdTruckQzEntity> {
}