<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.omsOrderGoodsPack.dao.OmsOrderGoodsPackDao">
    <resultMap type="OmsOrderGoodsPackEntity" id="OmsOrderGoodsPackResult">
        <result property="guid" column="GUID"/>
        <result property="goodsGuid" column="GOODS_GUID"/>
        <result property="orderNo" column="ORDER_NO"/>
        <result property="preNo" column="PRE_NO"/>
        <result property="packType" column="PACK_TYPE"/>
        <result property="isMzbz" column="IS_MZBZ"/>
        <result property="qtyPack" column="QTY_PACK"/>
        <result property="longs" column="LONGS"/>
        <result property="widths" column="WIDTHS"/>
        <result property="heights" column="HEIGHTS"/>
        <result property="weightPiece" column="WEIGHT_PIECE"/>
        <result property="kdfcs" column="KDFCS"/>
        <result property="packMemo" column="PACK_MEMO"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="qtyGoods" column="QTY_GOODS"/>
        <result property="unit" column="UNIT"/>
    </resultMap>

    <sql id="selectOmsOrderGoodsPackEntityVo">
        select
            GUID,
            GOODS_GUID,
            ORDER_NO,
            PRE_NO,
            PACK_TYPE,
            IS_MZBZ,
            QTY_PACK,
            LONGS,
            WIDTHS,
            HEIGHTS,
            WEIGHT_PIECE,
            KDFCS,
            PACK_MEMO,
            CREATE_USER,
            CREATE_USER_NAME,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_USER_NAME,
            UPDATE_DATE,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            QTY_GOODS,
            UNIT
        from OMS_ORDER_GOODS_PACK
    </sql>
</mapper>