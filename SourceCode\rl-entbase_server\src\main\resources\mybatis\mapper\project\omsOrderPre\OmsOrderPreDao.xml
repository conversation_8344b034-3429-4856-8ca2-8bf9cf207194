<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.omsOrderPre.dao.OmsOrderPreDao">
    <resultMap type="OmsOrderPreEntity" id="OmsOrderPreResult">
        <result property="preNo" column="PRE_NO"/>
        <result property="sysCode" column="SYS_CODE"/>
        <result property="sysDocNo" column="SYS_DOC_NO"/>
        <result property="xdUser" column="XD_USER"/>
        <result property="xdDate" column="XD_DATE"/>
        <result property="consigneeCode" column="CONSIGNEE_CODE"/>
        <result property="shipper" column="SHIPPER"/>
        <result property="receiver" column="RECEIVER"/>
        <result property="customerBu" column="CUSTOMER_BU"/>
        <result property="customerOrderNo" column="CUSTOMER_ORDER_NO"/>
        <result property="opType" column="OP_TYPE"/>
        <result property="productCode" column="PRODUCT_CODE"/>
        <result property="isJjh" column="IS_JJH"/>
        <result property="opDate" column="OP_DATE"/>
        <result property="fkfaCode" column="FKFA_CODE"/>
        <result property="accountCode" column="ACCOUNT_CODE"/>
        <result property="accountMode" column="ACCOUNT_MODE"/>
        <result property="payMode" column="PAY_MODE"/>
        <result property="jdUser" column="JD_USER"/>
        <result property="jdNode" column="JD_NODE"/>
        <result property="jdCompany" column="JD_COMPANY"/>
        <result property="xsUser" column="XS_USER"/>
        <result property="xsNode" column="XS_NODE"/>
        <result property="jsUser" column="JS_USER"/>
        <result property="jsNode" column="JS_NODE"/>
        <result property="xzfaNo" column="XZFA_NO"/>
        <result property="status" column="STATUS"/>
        <result property="stage" column="STAGE"/>
        <result property="dataOk" column="DATA_OK"/>
        <result property="arapOk" column="ARAP_OK"/>
        <result property="cancelFlag" column="CANCEL_FLAG"/>
        <result property="cancelReason" column="CANCEL_REASON"/>
        <result property="bizMemo" column="BIZ_MEMO"/>
        <result property="accountMemo" column="ACCOUNT_MEMO"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="tel" column="TEL"/>
        <result property="eMail" column="E_MAIL"/>
        <result property="requestOkDate" column="REQUEST_OK_DATE"/>
        <result property="fwlxCode" column="FWLX_CODE"/>
        <result property="sendUser" column="SEND_USER"/>
        <result property="sendDate" column="SEND_DATE"/>
        <result property="sendNode" column="SEND_NODE"/>
        <result property="sendNodeName" column="SEND_NODE_NAME"/>
        <result property="auditStatus" column="AUDIT_STATUS"/>
        <result property="auditUser" column="AUDIT_USER"/>
        <result property="auditDate" column="AUDIT_DATE"/>
        <result property="auditNode" column="AUDIT_NODE"/>
        <result property="auditNodeName" column="AUDIT_NODE_NAME"/>
        <result property="isCancel" column="IS_CANCEL"/>
        <result property="cancelDate" column="CANCEL_DATE"/>
        <result property="cancelUser" column="CANCEL_USER"/>
        <result property="cancelUserName" column="CANCEL_USER_NAME"/>
        <result property="cancelNode" column="CANCEL_NODE"/>
        <result property="cancelNodeName" column="CANCEL_NODE_NAME"/>
        <result property="cancelRemark" column="CANCEL_REMARK"/>
        <result property="auditMemo" column="AUDIT_MEMO"/>
        <result property="sendUserName" column="SEND_USER_NAME"/>
        <result property="auditUserName" column="AUDIT_USER_NAME"/>
        <result property="fwlxName" column="FWLX_NAME"/>
        <result property="appRead" column="APP_READ"/>
        <result property="jdGroupCode" column="JD_GROUP_CODE"/>
        <result property="opCompleteOkDate" column="OP_COMPLETE_OK_DATE"/>
        <result property="dataOkDate" column="DATA_OK_DATE"/>
        <result property="arapOkDate" column="ARAP_OK_DATE"/>
        <result property="opCompleteOk" column="OP_COMPLETE_OK"/>
        <result property="language" column="LANGUAGE"/>
        <result property="xdUserName" column="XD_USER_NAME"/>
        <result property="xdNodeCode" column="XD_NODE_CODE"/>
        <result property="xdNodeName" column="XD_NODE_NAME"/>
        <result property="xdCompanyCode" column="XD_COMPANY_CODE"/>
        <result property="xdCompanyName" column="XD_COMPANY_NAME"/>
        <result property="xdGroupCode" column="XD_GROUP_CODE"/>
        <result property="xdGroupName" column="XD_GROUP_NAME"/>
        <result property="jdUserName" column="JD_USER_NAME"/>
        <result property="bizRegId" column="BIZ_REG_ID"/>
        <result property="isQrjd" column="IS_QRJD"/>
        <result property="ie" column="IE"/>
        <result property="ygMileage" column="YG_MILEAGE"/>
        <result property="isBg" column="IS_BG"/>
        <result property="emsNo" column="EMS_NO"/>
        <result property="ygMileageT" column="YG_MILEAGE_T"/>
        <result property="ygMileageS" column="YG_MILEAGE_S"/>
        <result property="expressNo" column="EXPRESS_NO"/>
        <result property="carNo" column="CAR_NO"/>
        <result property="driverPhone" column="DRIVER_PHONE"/>
        <result property="totalPieces" column="TOTAL_PIECES"/>
        <result property="pieceUnit" column="PIECE_UNIT"/>
        <result property="zsDate" column="ZS_DATE"/>
        <result property="isDetail" column="IS_DETAIL"/>
    </resultMap>

    <sql id="selectOmsOrderPreEntityVo">
        select
            PRE_NO,
            SYS_CODE,
            SYS_DOC_NO,
            XD_USER,
            XD_DATE,
            CONSIGNEE_CODE,
            SHIPPER,
            RECEIVER,
            CUSTOMER_BU,
            CUSTOMER_ORDER_NO,
            OP_TYPE,
            PRODUCT_CODE,
            IS_JJH,
            OP_DATE,
            FKFA_CODE,
            ACCOUNT_CODE,
            ACCOUNT_MODE,
            PAY_MODE,
            JD_USER,
            JD_NODE,
            JD_COMPANY,
            XS_USER,
            XS_NODE,
            JS_USER,
            JS_NODE,
            XZFA_NO,
            STATUS,
            STAGE,
            DATA_OK,
            ARAP_OK,
            CANCEL_FLAG,
            CANCEL_REASON,
            BIZ_MEMO,
            ACCOUNT_MEMO,
            CREATE_USER,
            CREATE_USER_NAME,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_USER_NAME,
            UPDATE_DATE,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            TEL,
            E_MAIL,
            REQUEST_OK_DATE,
            FWLX_CODE,
            SEND_USER,
            SEND_DATE,
            SEND_NODE,
            SEND_NODE_NAME,
            AUDIT_STATUS,
            AUDIT_USER,
            AUDIT_DATE,
            AUDIT_NODE,
            AUDIT_NODE_NAME,
            IS_CANCEL,
            CANCEL_DATE,
            CANCEL_USER,
            CANCEL_USER_NAME,
            CANCEL_NODE,
            CANCEL_NODE_NAME,
            CANCEL_REMARK,
            AUDIT_MEMO,
            SEND_USER_NAME,
            AUDIT_USER_NAME,
            FWLX_NAME,
            APP_READ,
            JD_GROUP_CODE,
            OP_COMPLETE_OK_DATE,
            DATA_OK_DATE,
            ARAP_OK_DATE,
            OP_COMPLETE_OK,
            LANGUAGE,
            XD_USER_NAME,
            XD_NODE_CODE,
            XD_NODE_NAME,
            XD_COMPANY_CODE,
            XD_COMPANY_NAME,
            XD_GROUP_CODE,
            XD_GROUP_NAME,
            JD_USER_NAME,
            BIZ_REG_ID,
            IS_QRJD,
            IE,
            YG_MILEAGE,
            IS_BG,
            EMS_NO,
            YG_MILEAGE_T,
            YG_MILEAGE_S,
            EXPRESS_NO,
            CAR_NO,
            DRIVER_PHONE,
            TOTAL_PIECES,
            PIECE_UNIT,
            ZS_DATE,
            IS_DETAIL
        from OMS_ORDER_PRE
    </sql>
</mapper>