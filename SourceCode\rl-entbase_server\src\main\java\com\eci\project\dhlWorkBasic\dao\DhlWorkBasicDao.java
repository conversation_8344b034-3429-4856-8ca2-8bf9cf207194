package com.eci.project.dhlWorkBasic.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.dhlWorkBasic.entity.DhlWorkBasicEntity;


/**
* 运单信息Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-04-22
*/
public interface DhlWorkBasicDao extends EciBaseDao<DhlWorkBasicEntity> {

}