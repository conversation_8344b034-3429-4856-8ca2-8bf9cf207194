package com.eci.project.fzgjBdCity.service.impl;

import com.eci.common.web.BllContext;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjBdCity.dao.FzgjBdCityDao;
import com.eci.project.fzgjBdCity.entity.FzgjBdCityEntity;
import com.eci.project.fzgjBdCity.service.IFzgjBdCityService;
import com.eci.project.fzgjBdCity.validate.FzgjBdCityVal;

import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 市Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-03-17
*/
@Service
@Slf4j
public class FzgjBdCityServiceImpl implements IFzgjBdCityService
{
    @Autowired
    private FzgjBdCityDao fzgjBdCityDao;

    @Autowired
    private FzgjBdCityVal fzgjBdCityVal;


    @Override
    public TgPageInfo queryPageList(FzgjBdCityEntity entity) {
        EciQuery<FzgjBdCityEntity> eciQuery = EciQuery.doQuery(entity);
        List<FzgjBdCityEntity> entities = fzgjBdCityDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjBdCityEntity save(FzgjBdCityEntity entity) {

        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setCreateDate(new java.util.Date());
            entity.setUpdateDate(new java.util.Date());
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        } else {
            entity.setUpdateDate(new java.util.Date());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        }
        // 返回实体对象
        FzgjBdCityEntity fzgjBdCityEntity = null;
        fzgjBdCityVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjBdCityEntity = fzgjBdCityDao.insertOne(entity);

        }else{

            fzgjBdCityEntity = fzgjBdCityDao.updateByEntityId(entity);

        }
        return fzgjBdCityEntity;
    }

    @Override
    public List<FzgjBdCityEntity> selectList(FzgjBdCityEntity entity) {
        return fzgjBdCityDao.selectList(entity);
    }

    @Override
    public FzgjBdCityEntity selectOneById(Serializable id) {
        return fzgjBdCityDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjBdCityEntity> list) {
        fzgjBdCityDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjBdCityDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjBdCityDao.deleteById(id);
    }

}