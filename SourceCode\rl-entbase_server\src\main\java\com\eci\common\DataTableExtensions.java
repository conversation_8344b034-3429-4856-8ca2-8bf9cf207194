package com.eci.common;

import com.eci.wu.core.DataTable;
import lombok.var;

import java.lang.reflect.Constructor;
import java.util.List;
import java.util.stream.Collectors;

public class DataTableExtensions {

    /**
     * 将DataTable的每一行映射为指定类型的对象，并返回List<T>。
     *
     * @param <T>       返回的对象类型
     * @param dataTable 数据表（DataTable）的数据
     * @param clazz     目标对象的Class类型
     * @return 返回转换后的List<T>对象
     * @throws NoSuchMethodException  反射时可能抛出的异常
     * @throws IllegalAccessException 反射时可能抛出的异常
     * @throws InstantiationException 反射时可能抛出的异常
     */
    public static <T> List<T> upgrade(DataTable dataTable, Class<T> clazz) throws Exception {
        return dataTable.rows.stream()
                .filter(row -> row.get("Code") != null && row.get("Name") != null)  // 根据需要过滤掉null的行
                .map(row -> {
                    try {
                        // 使用反射构造目标对象
                        Constructor<T> constructor = clazz.getConstructor(String.class, String.class);
                        return constructor.newInstance(row.get("Code").toString(), row.get("Name").toString());
                    } catch (Exception e) {
                        e.printStackTrace();
                        return null;
                    }
                })
                .collect(Collectors.toList());
    }

    /**
     * 将DataTable的第一行数据转换为指定类型的对象，并返回T。
     *
     * @param <T>       返回的对象类型
     * @param dataTable 数据表（DataTable）的数据
     * @param clazz     目标对象的Class类型
     * @return 返回转换后的单个T对象
     * @throws NoSuchMethodException  反射时可能抛出的异常
     * @throws IllegalAccessException 反射时可能抛出的异常
     * @throws InstantiationException 反射时可能抛出的异常
     */
    public static <T> T upgradeSingle(DataTable dataTable, Class<T> clazz) throws Exception {
        if (dataTable.rows.isEmpty()) {
            return null; // 如果没有数据，返回null
        }

        // 获取第一行数据，转换成单个对象
        var row = dataTable.rows.get(0);
        Constructor<T> constructor = clazz.getConstructor(String.class, String.class);
        return constructor.newInstance(row.get("Code").toString(), row.get("Name").toString());
    }
}