package com.eci.project.omsOrderFwxmWorkTrace.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.Extensions;
import com.eci.common.db.DBHelper;
import com.eci.common.enums.Enums;
import com.eci.common.utils.CommonLib;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.exception.BaseException;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjTaskLimitation.dao.FzgjTaskLimitationDao;
import com.eci.project.fzgjTaskLimitation.entity.FzgjTaskLimitationEntity;
import com.eci.project.fzgjTaskLimitationPt.dao.FzgjTaskLimitationPtDao;
import com.eci.project.fzgjTaskLimitationPt.entity.FzgjTaskLimitationPtEntity;
import com.eci.project.omsOrderFwxmWork.dao.OmsOrderFwxmWorkDao;
import com.eci.project.omsOrderFwxmWork.entity.OmsOrderFwxmWorkEntity;
import com.eci.project.omsOrderFwxmWorkTrace.dao.OmsOrderFwxmWorkTraceDao;
import com.eci.project.omsOrderFwxmWorkTrace.entity.LinkColorEntity;
import com.eci.project.omsOrderFwxmWorkTrace.entity.OmsOrderFwxmWorkTraceEntity;
import com.eci.project.omsOrderFwxmWorkTrace.entity.OrderTraceLinkEntity;
import com.eci.project.omsOrderFwxmWorkTrace.validate.OmsOrderFwxmWorkTraceVal;
import com.eci.sso.role.entity.UserContext;
import com.eci.wu.core.EntityBase;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;


/**
 * 作业跟踪Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-04-22
 */
@Service
@Slf4j
public class OmsOrderFwxmWorkTraceZsrService implements EciBaseService<OmsOrderFwxmWorkTraceEntity> {

    /**
     * 协作任务
     */
    @Autowired
    private OmsOrderFwxmWorkDao omsOrderFwxmWorkDao;
    /**
     * 协作任务追踪
     */
    @Autowired
    private OmsOrderFwxmWorkTraceDao omsOrderFwxmWorkTraceDao;
    @Autowired
    private FzgjTaskLimitationPtDao fzgjTaskLimitationDao;

    private void deleteWorkTraceList(String orderNo) {
        omsOrderFwxmWorkTraceDao.delete()
                .eq(OmsOrderFwxmWorkTraceEntity::getOrderNo, orderNo)
                .eq(OmsOrderFwxmWorkTraceEntity::getGroupCode, UserContext.getUserInfo().getCompanyCode())
                .execute();
    }

    private void deleteWorkTraceListZiZhu(String preNo) {
        omsOrderFwxmWorkTraceDao.delete()
                .eq(OmsOrderFwxmWorkTraceEntity::getPreNo, preNo)
                .eq(OmsOrderFwxmWorkTraceEntity::getGroupCode, UserContext.getUserInfo().getCompanyCode())
                .execute();
    }

    private List<OmsOrderFwxmWorkTraceEntity> getWorkTraceList(String orderNo) {
        return omsOrderFwxmWorkTraceDao.select()
                .eq(OmsOrderFwxmWorkTraceEntity::getOrderNo, orderNo)
                .eq(OmsOrderFwxmWorkTraceEntity::getGroupCode, UserContext.getUserInfo().getCompanyCode())
                .list();
    }

    private List<OmsOrderFwxmWorkTraceEntity> getWorkTraceListZiZhu(String preNo) {
        return omsOrderFwxmWorkTraceDao.select()
                .eq(OmsOrderFwxmWorkTraceEntity::getPreNo, preNo)
                .eq(OmsOrderFwxmWorkTraceEntity::getGroupCode, UserContext.getUserInfo().getCompanyCode())
                .list();
    }

    private List<OmsOrderFwxmWorkEntity> getWorkList(String orderNo) {
        return omsOrderFwxmWorkDao.select()
                .eq(OmsOrderFwxmWorkEntity::getOrderNo, orderNo)
                .eq(OmsOrderFwxmWorkEntity::getGroupCode, UserContext.getUserInfo().getCompanyCode())
                .list();
    }

    private List<OmsOrderFwxmWorkEntity> getWorkListZiZhu(String preNo) {
        return omsOrderFwxmWorkDao.select()
                .eq(OmsOrderFwxmWorkEntity::getPreNo, preNo)
                .eq(OmsOrderFwxmWorkEntity::getGroupCode, UserContext.getUserInfo().getCompanyCode())
                .list();
    }

    private List<FzgjTaskLimitationPtEntity> getFzgjTaskLimitationList(String fwxmCode) {
        return fzgjTaskLimitationDao.select()
                .eq(FzgjTaskLimitationPtEntity::getTargetCode, fwxmCode)
                .list();
    }


    /**
     * 订单下所有协作任务跟踪新增
     *
     * @param orderNo
     */
    public void WorkTraceAllSave(String orderNo) {

        List<OmsOrderFwxmWorkEntity> workList = getWorkList(orderNo);

        List<OmsOrderFwxmWorkTraceEntity> listTraceOld = getWorkTraceList(orderNo);
        if (workList == null || workList.size() < 1) {
            return;
        }

        // 删除旧数据
        deleteWorkTraceList(orderNo);

        List<String> listSaveSql = new ArrayList<>();

        workList.forEach(work -> {
            List<FzgjTaskLimitationPtEntity> tacxLimitation = getFzgjTaskLimitationList(work.getFwxmCode());

            if (tacxLimitation.size() == 0 && !work.getFwxmCode().equals("200100") && !work.getFwxmCode().equals("100200100")) {
                throw new BaseException("服务项目作业环节未维护");
            }
            tacxLimitation.forEach(tacx -> {
                OmsOrderFwxmWorkTraceEntity trace = new OmsOrderFwxmWorkTraceEntity();
                Optional<OmsOrderFwxmWorkTraceEntity> traceOld = listTraceOld.stream()
                        .filter(s -> s.getWorkNo().equals(work.getWorkNo()) && s.getLinkCode().equals(tacx.getCode()))
                        .findFirst();
                //  .FirstOrDefault(s = > s.WORK_NO == work.WORK_NO && s.LINK_CODE == tacx.CODE)
                if (traceOld.isPresent()) {
                    trace.setPlanOkDate(traceOld.get().getPlanOkDate());// PLAN_OK_DATE = traceOld.PLAN_OK_DATE;
                    trace.setActualOkDate(traceOld.get().getActualOkDate());//  ACTUAL_OK_DATE = traceOld.ACTUAL_OK_DATE;
                }
                trace.setGuid(IdWorker.getIdStr());// = Guid.NewGuid().ToString("N");
                trace.setWorkNo(work.getWorkNo());//  WORK_NO = work.WORK_NO;
                trace.setOrderNo(work.getOrderNo());// ORDER_NO = work.ORDER_NO;
                trace.setPreNo(work.getPreNo());// PRE_NO = work.PRE_NO;
                trace.setLinkSeq(tacx.getSeq());// LINK_SEQ = tacx.SEQ;
                trace.setLinkCode(tacx.getCode());// LINK_CODE = tacx.CODE;
                trace.setJobd(tacx.getMemo());// JOBD = tacx.MEMO;
                trace.setWorkGuid(work.getGuid());// WORK_GUID = work.GUID;
                trace.setBizRegId(work.getBizRegId());// BIZ_REG_ID = work.BIZ_REG_ID;

                // 增加用户信息
                trace.setUpdateDate(new java.util.Date());
                trace.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
                trace.setUpdateUserName(UserContext.getUserInfo().getTrueName());
                trace.setCreateDate(new java.util.Date());
                trace.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
                trace.setCreateUserName(UserContext.getUserInfo().getTrueName());
                trace.setGroupCode(UserContext.getUserInfo().getCompanyCode());
                trace.setGroupName(UserContext.getUserInfo().getCompanyName());
                trace.setNodeCode(UserContext.getUserInfo().getCompanyCode());
                trace.setNodeName(UserContext.getUserInfo().getCompanyName());

                omsOrderFwxmWorkTraceDao.insert(trace);

            });

        });

    }


    /**
     * 订单下所有协作任务跟踪新增-自助下单
     *
     * @param preNo
     */
    public void WorkTraceAllSaveZiZhuXiaDan(String preNo) {

        List<OmsOrderFwxmWorkEntity> workList = getWorkListZiZhu(preNo);

        List<OmsOrderFwxmWorkTraceEntity> listTraceOld = getWorkTraceListZiZhu(preNo);
        if (workList == null || workList.size() < 1) {
            return;
        }

        // 删除旧数据
        deleteWorkTraceListZiZhu(preNo);

        List<String> listSaveSql = new ArrayList<>();

        workList.forEach(work -> {

            List<FzgjTaskLimitationPtEntity> tacxLimitation = getFzgjTaskLimitationList(work.getFwxmCode());

            if (tacxLimitation.size() == 0) {
                throw new BaseException("服务项目作业环节未维护");
            }
            tacxLimitation.forEach(tacx -> {
                OmsOrderFwxmWorkTraceEntity trace = new OmsOrderFwxmWorkTraceEntity();
                Optional<OmsOrderFwxmWorkTraceEntity> traceOld = listTraceOld.stream()
                        .filter(s -> s.getWorkNo().equals(work.getWorkNo()) && s.getLinkCode().equals(tacx.getCode()))
                        .findFirst();
                //  .FirstOrDefault(s = > s.WORK_NO == work.WORK_NO && s.LINK_CODE == tacx.CODE)
                if (traceOld.isPresent()) {
                    trace.setPlanOkDate(traceOld.get().getPlanOkDate());// PLAN_OK_DATE = traceOld.PLAN_OK_DATE;
                    trace.setActualOkDate(traceOld.get().getActualOkDate());//  ACTUAL_OK_DATE = traceOld.ACTUAL_OK_DATE;
                }
                trace.setGuid(IdWorker.getIdStr());// = Guid.NewGuid().ToString("N");
                trace.setWorkNo(work.getWorkNo());//  WORK_NO = work.WORK_NO;
                trace.setOrderNo(work.getOrderNo());// ORDER_NO = work.ORDER_NO;
                trace.setPreNo(work.getPreNo());// PRE_NO = work.PRE_NO;
                trace.setLinkSeq(tacx.getSeq());// LINK_SEQ = tacx.SEQ;
                trace.setLinkCode(tacx.getCode());// LINK_CODE = tacx.CODE;
                trace.setJobd(tacx.getMemo());// JOBD = tacx.MEMO;
                trace.setWorkGuid(work.getGuid());// WORK_GUID = work.GUID;
                trace.setBizRegId(work.getBizRegId());// BIZ_REG_ID = work.BIZ_REG_ID;

                // 增加用户信息
                trace.setUpdateDate(new java.util.Date());
                trace.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
                trace.setUpdateUserName(UserContext.getUserInfo().getTrueName());
                trace.setCreateDate(new java.util.Date());
                trace.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
                trace.setCreateUserName(UserContext.getUserInfo().getTrueName());
                trace.setGroupCode(UserContext.getUserInfo().getCompanyCode());
                trace.setGroupName(UserContext.getUserInfo().getCompanyName());
                trace.setNodeCode(UserContext.getUserInfo().getCompanyCode());
                trace.setNodeName(UserContext.getUserInfo().getCompanyName());

                omsOrderFwxmWorkTraceDao.insert(trace);

            });

        });

    }

}