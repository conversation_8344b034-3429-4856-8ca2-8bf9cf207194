package com.eci.project.vFzgjBdArea.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.vFzgjBdArea.entity.VFzgjBdAreaEntity;

import org.springframework.stereotype.Service;


/**
* Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-05-20
*/
@Service
public class VFzgjBdAreaVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(VFzgjBdAreaEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(VFzgjBdAreaEntity entity, BusinessType businessType) {

    }

}
