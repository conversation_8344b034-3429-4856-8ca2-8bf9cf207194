package com.eci.project.fzgjBaseDataDetail.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.common.ZsrBaseEntity;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;



/**
* 扩展基础资料明细对象 FZGJ_BASE_DATA_DETAIL
*
* @<NAME_EMAIL>
* @date 2025-03-18
*/
@FieldNameConstants
public class FzgjBaseDataDetailBaseEntity extends ZsrBaseEntity
{
	/**
	* GUID
	*/
	@ApiModelProperty("GUID(50)")
	@TableId("GUID")
	private String guid;

	/**
	* 集团代码
	*/
	@ApiModelProperty("集团代码(50)")
	@TableField("GROUP_CODE")
	private String groupCode;

	/**
	* 代码
	*/
	@ApiModelProperty("代码(20)")
	@TableField("CODE")
	private String code;

	/**
	* 名称
	*/
	@ApiModelProperty("名称(200)")
	@TableField("NAME")
	private String name;

	/**
	* 状态
	*/
	@ApiModelProperty("状态(1)")
	@TableField("STATUS")
	private String status;

	/**
	* 备注
	*/
	@ApiModelProperty("备注(200)")
	@TableField("MEMO")
	private String memo;

	/**
	* 顺序
	*/
	@ApiModelProperty("顺序(22)")
	@TableField("SEQ")
	private Integer seq;

	/**
	* 所属集团代码
	*/
	@ApiModelProperty("所属集团代码(50)")
	@TableField("BELONG_GROUP")
	private String belongGroup;

	/**
	* 参数代码，多个以英文逗号分割
	*/
	@ApiModelProperty("参数代码，多个以英文逗号分割(200)")
	@TableField("PARAM_CODE")
	private String paramCode;

	@JsonIgnore
	@TableField(exist = false)
	private Class<?> clazz = this.getClass();

	public Class<?> getClazz() {
		return clazz;
	}

	public void setClazz(Class<?> clazz) {
		this.clazz = clazz;
	}

	public FzgjBaseDataDetailBaseEntity setGuid(String guid) {
		this.guid = guid;
		return this;
	}

	public String getGuid() {
		return guid;
	}

	public FzgjBaseDataDetailBaseEntity setGroupCode(String groupCode) {
		this.groupCode = groupCode;
		return this;
	}

	public String getGroupCode() {
		return groupCode;
	}

	public FzgjBaseDataDetailBaseEntity setCode(String code) {
		this.code = code;
		return this;
	}

	public String getCode() {
		return code;
	}

	public FzgjBaseDataDetailBaseEntity setName(String name) {
		this.name = name;
		return this;
	}

	public String getName() {
		return name;
	}

	public FzgjBaseDataDetailBaseEntity setStatus(String status) {
		this.status = status;
		return this;
	}

	public String getStatus() {
		return status;
	}

	public FzgjBaseDataDetailBaseEntity setMemo(String memo) {
		this.memo = memo;
		return this;
	}

	public String getMemo() {
		return memo;
	}

	public FzgjBaseDataDetailBaseEntity setSeq(Integer seq) {
		this.seq = seq;
		return this;
	}

	public Integer getSeq() {
		return seq;
	}

	public FzgjBaseDataDetailBaseEntity setBelongGroup(String belongGroup) {
		this.belongGroup = belongGroup;
		return this;
	}

	public String getBelongGroup() {
		return belongGroup;
	}

	public FzgjBaseDataDetailBaseEntity setParamCode(String paramCode) {
		this.paramCode = paramCode;
		return this;
	}

	public String getParamCode() {
		return paramCode;
	}

}
