package com.eci.project.etmsOpAttemperCargo.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.etmsOpAttemperCargo.dao.EtmsOpAttemperCargoDao;
import com.eci.project.etmsOpAttemperCargo.entity.EtmsOpAttemperCargoEntity;
import com.eci.project.etmsOpAttemperCargo.validate.EtmsOpAttemperCargoVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 托运货物信息Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-06-13
*/
@Service
@Slf4j
public class EtmsOpAttemperCargoService implements EciBaseService<EtmsOpAttemperCargoEntity> {

    @Autowired
    private EtmsOpAttemperCargoDao etmsOpAttemperCargoDao;

    @Autowired
    private EtmsOpAttemperCargoVal etmsOpAttemperCargoVal;


    @Override
    public TgPageInfo queryPageList(EtmsOpAttemperCargoEntity entity) {
        EciQuery<EtmsOpAttemperCargoEntity> eciQuery = EciQuery.buildQuery(entity);
        List<EtmsOpAttemperCargoEntity> entities = etmsOpAttemperCargoDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public EtmsOpAttemperCargoEntity save(EtmsOpAttemperCargoEntity entity) {
        // 返回实体对象
        EtmsOpAttemperCargoEntity etmsOpAttemperCargoEntity = null;
        etmsOpAttemperCargoVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            etmsOpAttemperCargoEntity = etmsOpAttemperCargoDao.insertOne(entity);

        }else{

            etmsOpAttemperCargoEntity = etmsOpAttemperCargoDao.updateByEntityId(entity);

        }
        return etmsOpAttemperCargoEntity;
    }

    @Override
    public List<EtmsOpAttemperCargoEntity> selectList(EtmsOpAttemperCargoEntity entity) {
        return etmsOpAttemperCargoDao.selectList(entity);
    }

    @Override
    public EtmsOpAttemperCargoEntity selectOneById(Serializable id) {
        return etmsOpAttemperCargoDao.selectById(id);
    }


    @Override
    public void insertBatch(List<EtmsOpAttemperCargoEntity> list) {
        etmsOpAttemperCargoDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return etmsOpAttemperCargoDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return etmsOpAttemperCargoDao.deleteById(id);
    }

}