package com.eci.project.omsOrderFwxmWorkFk.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 反馈内容表头对象 OMS_ORDER_FWXM_WORK_FK
 * 可以自己扩展字段
 * @<NAME_EMAIL>
 * @date 2025-06-10
 */
@ApiModel("反馈内容表头")
@TableName("OMS_ORDER_FWXM_WORK_FK")
@FieldNameConstants
public class OmsOrderFwxmWorkFkEntity extends EciBaseEntity{
    /**
     * GUID
     */
    @ApiModelProperty("GUID(36)")
    @TableId("GUID")
    private String guid;

    /**
     * 协作任务编号
     */
    @ApiModelProperty("协作任务编号(50)")
    @TableField("WORK_NO")
    private String workNo;

    /**
     * 订单号
     */
    @ApiModelProperty("订单号(36)")
    @TableField("ORDER_NO")
    private String orderNo;

    /**
     * 服务类型代码
     */
    @ApiModelProperty("服务类型代码(20)")
    @TableField("FWLX_CODE")
    private String fwlxCode;

    /**
     * 协作服务项目
     */
    @ApiModelProperty("协作服务项目(20)")
    @TableField("FWXM_CODE")
    private String fwxmCode;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
     * 创建日期
     */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
     * 最后修改人
     */
    @ApiModelProperty("最后修改人(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
     * 最后修改人
     */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
     * 最后修改日期
     */
    @ApiModelProperty("最后修改日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("最后修改日期开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("最后修改日期结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
     * 归属公司
     */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
     * 归属公司
     */
    @ApiModelProperty("归属公司(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
     * 归属组织
     */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
     * 归属组织
     */
    @ApiModelProperty("归属组织(200)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
     * 归属集团
     */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
     * 归属集团
     */
    @ApiModelProperty("归属集团(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
     * 作业说明
     */
    @ApiModelProperty("作业说明(500)")
    @TableField("OP_MEMO")
    private String opMemo;

    /**
     * 海关查验次数、拼票数
     */
    @ApiModelProperty("海关查验次数、拼票数(22)")
    @TableField("HGCY_NUM")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal hgcyNum;

    /**
     * 海关查验备注
     */
    @ApiModelProperty("海关查验备注(200)")
    @TableField("HGCY_MEMO")
    private String hgcyMemo;

    /**
     * 商检查验次数
     */
    @ApiModelProperty("商检查验次数(22)")
    @TableField("SJCY_NUM")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal sjcyNum;

    /**
     * 商检查验备注、供船申报单号
     */
    @ApiModelProperty("商检查验备注、供船申报单号(500)")
    @TableField("SJCY_MEMO")
    private String sjcyMemo;

    /**
     * 入库交接单号/结算单号/收出货单号、参考号
     */
    @ApiModelProperty("入库交接单号/结算单号/收出货单号、参考号(500)")
    @TableField("JJ_NO_RK")
    private String jjNoRk;

    /**
     * 出库交接单号、换单供应商
     */
    @ApiModelProperty("出库交接单号、换单供应商(50)")
    @TableField("JJ_NO_CK")
    private String jjNoCk;

    /**
     * 铁路班次号、供应商编号
     */
    @ApiModelProperty("铁路班次号、供应商编号(500)")
    @TableField("TL_BC_NO")
    private String tlBcNo;

    /**
     * 铁路班列日期、截港日期、发车日期
     */
    @ApiModelProperty("铁路班列日期、截港日期、发车日期(7)")
    @TableField("TL_DATE")
    private Date tlDate;

    @ApiModelProperty("铁路班列日期、截港日期、发车日期开始")
    @TableField(exist=false)
    private Date tlDateStart;

    @ApiModelProperty("铁路班列日期、截港日期、发车日期结束")
    @TableField(exist=false)
    private Date tlDateEnd;

    /**
     * 铁路铅封号、最终目的地
     */
    @ApiModelProperty("铁路铅封号、最终目的地(500)")
    @TableField("TL_QF_NO")
    private String tlQfNo;

    /**
     * 铁路关封号
     */
    @ApiModelProperty("铁路关封号(20)")
    @TableField("TL_GF_NO")
    private String tlGfNo;

    /**
     * 铁路订舱号
     */
    @ApiModelProperty("铁路订舱号(20)")
    @TableField("TL_DC_NO")
    private String tlDcNo;

    /**
     * 铁路提运运单号
     */
    @ApiModelProperty("铁路提运运单号(500)")
    @TableField("TL_YD_NO")
    private String tlYdNo;

    /**
     * 铁路预计到站日期、截关时间、到达日期
     */
    @ApiModelProperty("铁路预计到站日期、截关时间、到达日期(7)")
    @TableField("TL_YJ_DATE")
    private Date tlYjDate;

    @ApiModelProperty("铁路预计到站日期、截关时间、到达日期开始")
    @TableField(exist=false)
    private Date tlYjDateStart;

    @ApiModelProperty("铁路预计到站日期、截关时间、到达日期结束")
    @TableField(exist=false)
    private Date tlYjDateEnd;

    /**
     * 海运船名航次
     */
    @ApiModelProperty("海运船名航次(500)")
    @TableField("HY_CM")
    private String hyCm;

    /**
     * 海运航次、提单品名
     */
    @ApiModelProperty("海运航次、提单品名(500)")
    @TableField("HY_HC")
    private String hyHc;

    /**
     * 海运铅封号
     */
    @ApiModelProperty("海运铅封号(20)")
    @TableField("HY_QF_NO")
    private String hyQfNo;

    /**
     * 海运订舱号
     */
    @ApiModelProperty("海运订舱号(20)")
    @TableField("HY_DC_NO")
    private String hyDcNo;

    /**
     * 海运提单号
     */
    @ApiModelProperty("海运提单号(20)")
    @TableField("HY_TD_NO")
    private String hyTdNo;

    /**
     * 海运开航日期、离港日期、
     */
    @ApiModelProperty("海运开航日期、离港日期、(7)")
    @TableField("HY_DATE")
    private Date hyDate;

    @ApiModelProperty("海运开航日期、离港日期、开始")
    @TableField(exist=false)
    private Date hyDateStart;

    @ApiModelProperty("海运开航日期、离港日期、结束")
    @TableField(exist=false)
    private Date hyDateEnd;

    /**
     * 海运预计到港日期、到港日期
     */
    @ApiModelProperty("海运预计到港日期、到港日期(7)")
    @TableField("HY_YJ_DATE")
    private Date hyYjDate;

    @ApiModelProperty("海运预计到港日期、到港日期开始")
    @TableField(exist=false)
    private Date hyYjDateStart;

    @ApiModelProperty("海运预计到港日期、到港日期结束")
    @TableField(exist=false)
    private Date hyYjDateEnd;

    /**
     * 海、空运、铁路主单号
     */
    @ApiModelProperty("海、空运、铁路主单号(500)")
    @TableField("KY_MB_NO")
    private String kyMbNo;

    /**
     * 海、空运、铁路分单号
     */
    @ApiModelProperty("海、空运、铁路分单号(500)")
    @TableField("KY_HB_NO")
    private String kyHbNo;

    /**
     * 空运航空公司、船公司
     */
    @ApiModelProperty("空运航空公司、船公司(80)")
    @TableField("KY_HKGS")
    private String kyHkgs;

    /**
     * 空运航班号
     */
    @ApiModelProperty("空运航班号(40)")
    @TableField("KY_HBH")
    private String kyHbh;

    /**
     * 空运进仓编号
     */
    @ApiModelProperty("空运进仓编号(500)")
    @TableField("KY_JCBH")
    private String kyJcbh;

    /**
     * 空运航班日期、截SI时间、开航日期
     */
    @ApiModelProperty("空运航班日期、截SI时间、开航日期(7)")
    @TableField("KY_DATE")
    private Date kyDate;

    @ApiModelProperty("空运航班日期、截SI时间、开航日期开始")
    @TableField(exist=false)
    private Date kyDateStart;

    @ApiModelProperty("空运航班日期、截SI时间、开航日期结束")
    @TableField(exist=false)
    private Date kyDateEnd;

    /**
     * 空运预计到港日期、签单时间、到港日期
     */
    @ApiModelProperty("空运预计到港日期、签单时间、到港日期(7)")
    @TableField("KY_YJ_DATE")
    private Date kyYjDate;

    @ApiModelProperty("空运预计到港日期、签单时间、到港日期开始")
    @TableField(exist=false)
    private Date kyYjDateStart;

    @ApiModelProperty("空运预计到港日期、签单时间、到港日期结束")
    @TableField(exist=false)
    private Date kyYjDateEnd;

    /**
     * 协作委托单编号
     */
    @ApiModelProperty("协作委托单编号(36)")
    @TableField("XZWT_NO")
    private String xzwtNo;

    /**
     * 作业系统代码
     */
    @ApiModelProperty("作业系统代码(10)")
    @TableField("SYS_CODE")
    private String sysCode;

    /**
     * 业务数据唯一注册编号
     */
    @ApiModelProperty("业务数据唯一注册编号(50)")
    @TableField("BIZ_REG_ID")
    private String bizRegId;

    /**
     * 作业系统单据类型
     */
    @ApiModelProperty("作业系统单据类型(20)")
    @TableField("DOC_TYPE")
    private String docType;

    /**
     * 提单计费重量/装火车过磅数量（吨）、预估税金
     */
    @ApiModelProperty("提单计费重量/装火车过磅数量（吨）、预估税金(22)")
    @TableField("TDJFZL")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal tdjfzl;

    /**
     * 船公司
     */
    @ApiModelProperty("船公司(500)")
    @TableField("CGS")
    private String cgs;

    /**
     * 不良品/坏污损说明
     */
    @ApiModelProperty("不良品/坏污损说明(200)")
    @TableField("POOR_QUALITY_DESC")
    private String poorQualityDesc;

    /**
     * 货物属性
     */
    @ApiModelProperty("货物属性(50)")
    @TableField("GOODS_PROTETY")
    private String goodsProtety;

    /**
     * 货物品名
     */
    @ApiModelProperty("货物品名(500)")
    @TableField("GOODS_NAME")
    private String goodsName;

    /**
     * 总毛重(kg)
     */
    @ApiModelProperty("总毛重(kg)(22)")
    @TableField("WEIGHT_TOTAL")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal weightTotal;

    /**
     * 总件数
     */
    @ApiModelProperty("总件数(22)")
    @TableField("PIECE_TOTAL")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal pieceTotal;

    /**
     * 计费重量(kg)
     */
    @ApiModelProperty("计费重量(kg)(22)")
    @TableField("WEIGHT_CALC")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal weightCalc;

    /**
     * 总体积(m3)
     */
    @ApiModelProperty("总体积(m3)(22)")
    @TableField("VOLUME_TOTAL")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal volumeTotal;

    /**
     * 客户订单号
     */
    @ApiModelProperty("客户订单号(500)")
    @TableField("KH_ORDER_NO")
    private String khOrderNo;

    /**
     * 包装类型
     */
    @ApiModelProperty("包装类型(500)")
    @TableField("PKY_TYPE")
    private String pkyType;

    /**
     * 成交方式
     */
    @ApiModelProperty("成交方式(10)")
    @TableField("CJFS")
    private String cjfs;

    /**
     * 起运港、起运站
     */
    @ApiModelProperty("起运港、起运站(50)")
    @TableField("QYG")
    private String qyg;

    /**
     * 目的港、目的站
     */
    @ApiModelProperty("目的港、目的站(50)")
    @TableField("MDG")
    private String mdg;

    /**
     * 提单要求
     */
    @ApiModelProperty("提单要求(500)")
    @TableField("TDYQ")
    private String tdyq;

    /**
     * 港区
     */
    @ApiModelProperty("港区(50)")
    @TableField("GQ")
    private String gq;

    /**
     * 航线
     */
    @ApiModelProperty("航线(500)")
    @TableField("HX")
    private String hx;

    /**
     * 整箱、舱单申报完成、海外代理货
     */
    @ApiModelProperty("整箱、舱单申报完成、海外代理货(1)")
    @TableField("ZX")
    private String zx;

    /**
     * 拼箱、直拼整柜
     */
    @ApiModelProperty("拼箱、直拼整柜(1)")
    @TableField("PX")
    private String px;

    /**
     * 主单发货人
     */
    @ApiModelProperty("主单发货人(500)")
    @TableField("ZDFHR")
    private String zdfhr;

    /**
     * 主单收货人
     */
    @ApiModelProperty("主单收货人(500)")
    @TableField("ZDSHR")
    private String zdshr;

    /**
     * 主单通知人
     */
    @ApiModelProperty("主单通知人(500)")
    @TableField("ZDTZR")
    private String zdtzr;

    /**
     * 发货人
     */
    @ApiModelProperty("发货人(50)")
    @TableField("FHR")
    private String fhr;

    /**
     * 收货人
     */
    @ApiModelProperty("收货人(50)")
    @TableField("SHR_CONSIGNEE")
    private String shrConsignee;

    /**
     * 通知人
     */
    @ApiModelProperty("通知人(50)")
    @TableField("TZR")
    private String tzr;

    /**
     * 唛头
     */
    @ApiModelProperty("唛头(500)")
    @TableField("MT")
    private String mt;

    /**
     * 交货地、最终目的地
     */
    @ApiModelProperty("交货地、最终目的地(500)")
    @TableField("JHD")
    private String jhd;

    /**
     * 运费条款(主单)
     */
    @ApiModelProperty("运费条款(主单)(50)")
    @TableField("YFTK_ZD")
    private String yftkZd;

    /**
     * 杂费条款(主单)
     */
    @ApiModelProperty("杂费条款(主单)(50)")
    @TableField("ZFTK_ZD")
    private String zftkZd;

    /**
     * 运费条款(分单)
     */
    @ApiModelProperty("运费条款(分单)(50)")
    @TableField("YFTK_FD")
    private String yftkFd;

    /**
     * 杂费条款(分单)
     */
    @ApiModelProperty("杂费条款(分单)(50)")
    @TableField("ZFTK_FD")
    private String zftkFd;

    /**
     * 提单制作人
     */
    @ApiModelProperty("提单制作人(500)")
    @TableField("TDZZR")
    private String tdzzr;

    /**
     * 仓库、港区仓库
     */
    @ApiModelProperty("仓库、港区仓库(500)")
    @TableField("WH")
    private String wh;

    /**
     * 订舱类型
     */
    @ApiModelProperty("订舱类型(500)")
    @TableField("DCLX")
    private String dclx;

    /**
     * 订舱代理
     */
    @ApiModelProperty("订舱代理(500)")
    @TableField("DCDL")
    private String dcdl;

    /**
     * 舱单申报
     */
    @ApiModelProperty("舱单申报(500)")
    @TableField("CDSB")
    private String cdsb;

    /**
     * 中转港代码
     */
    @ApiModelProperty("中转港代码(50)")
    @TableField("ZZG_CODE")
    private String zzgCode;

    /**
     * 实际总毛重(kg)
     */
    @ApiModelProperty("实际总毛重(kg)(22)")
    @TableField("WEIGHT_TOTAL_SJ")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal weightTotalSj;

    /**
     * 实际总件数
     */
    @ApiModelProperty("实际总件数(22)")
    @TableField("PIECE_TOTAL_SJ")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal pieceTotalSj;

    /**
     * 实际计费重量(kg)
     */
    @ApiModelProperty("实际计费重量(kg)(22)")
    @TableField("WEIGHT_CALC_SJ")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal weightCalcSj;

    /**
     * 实际总体积(m3)
     */
    @ApiModelProperty("实际总体积(m3)(22)")
    @TableField("VOLUME_TOTAL_SJ")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal volumeTotalSj;

    /**
     * 制单总毛重(kg)
     */
    @ApiModelProperty("制单总毛重(kg)(22)")
    @TableField("WEIGHT_TOTAL_ZD")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal weightTotalZd;

    /**
     * 制单总件数
     */
    @ApiModelProperty("制单总件数(22)")
    @TableField("PIECE_TOTAL_ZD")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal pieceTotalZd;

    /**
     * 制单计费重量(kg)
     */
    @ApiModelProperty("制单计费重量(kg)(22)")
    @TableField("WEIGHT_CALC_ZD")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal weightCalcZd;

    /**
     * 制单总体积(m3)
     */
    @ApiModelProperty("制单总体积(m3)(22)")
    @TableField("VOLUME_TOTAL_ZD")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal volumeTotalZd;

    /**
     * 国外代理
     */
    @ApiModelProperty("国外代理(50)")
    @TableField("GWDL")
    private String gwdl;

    /**
     * 最终目的地
     */
    @ApiModelProperty("最终目的地(500)")
    @TableField("ZDMDD")
    private String zdmdd;

    /**
     * 实际航班
     */
    @ApiModelProperty("实际航班(40)")
    @TableField("SJHB")
    private String sjhb;

    /**
     * 仓库地址
     */
    @ApiModelProperty("仓库地址(500)")
    @TableField("WH_ADD")
    private String whAdd;

    /**
     * 关单数量
     */
    @ApiModelProperty("关单数量(22)")
    @TableField("GD_QTY")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal gdQty;

    /**
     * 运输工具，代理地址
     */
    @ApiModelProperty("运输工具，代理地址(500)")
    @TableField("YSGJ")
    private String ysgj;

    /**
     * 出入库
     */
    @ApiModelProperty("出入库(500)")
    @TableField("CRK")
    private String crk;

    /**
     * 入库日期，实际送达日期
     */
    @ApiModelProperty("入库日期，实际送达日期(7)")
    @TableField("RK_DATE")
    private Date rkDate;

    @ApiModelProperty("入库日期，实际送达日期开始")
    @TableField(exist=false)
    private Date rkDateStart;

    @ApiModelProperty("入库日期，实际送达日期结束")
    @TableField(exist=false)
    private Date rkDateEnd;

    /**
     * 出库日期
     */
    @ApiModelProperty("出库日期(7)")
    @TableField("CK_DATE")
    private Date ckDate;

    @ApiModelProperty("出库日期开始")
    @TableField(exist=false)
    private Date ckDateStart;

    @ApiModelProperty("出库日期结束")
    @TableField(exist=false)
    private Date ckDateEnd;

    /**
     * 进出口
     */
    @ApiModelProperty("进出口(20)")
    @TableField("IE")
    private String ie;

    /**
     * 货物尺寸
     */
    @ApiModelProperty("货物尺寸(200)")
    @TableField("GOODS_SIZE")
    private String goodsSize;

    /**
     * 陆运订舱号
     */
    @ApiModelProperty("陆运订舱号(20)")
    @TableField("LY_DC_NO")
    private String lyDcNo;

    /**
     * CCRN
     */
    @ApiModelProperty("CCRN(20)")
    @TableField("CCRN")
    private String ccrn;

    /**
     * 六联单号
     */
    @ApiModelProperty("六联单号(20)")
    @TableField("LY_LLDH")
    private String lyLldh;

    /**
     * 集装箱1-箱号
     */
    @ApiModelProperty("集装箱1-箱号(20)")
    @TableField("BOX_NO1")
    private String boxNo1;

    /**
     * 集装箱1-封号
     */
    @ApiModelProperty("集装箱1-封号(20)")
    @TableField("BOX_FH1")
    private String boxFh1;

    /**
     * 集装箱1-皮重(KG)
     */
    @ApiModelProperty("集装箱1-皮重(KG)(20)")
    @TableField("BOX_PZ1")
    private String boxPz1;

    /**
     * 集装箱1-类型
     */
    @ApiModelProperty("集装箱1-类型(20)")
    @TableField("BOX_TYPE1")
    private String boxType1;

    /**
     * 集装箱1-尺寸
     */
    @ApiModelProperty("集装箱1-尺寸(20)")
    @TableField("BOX_SIZE1")
    private String boxSize1;

    /**
     * 集装箱2-箱号
     */
    @ApiModelProperty("集装箱2-箱号(20)")
    @TableField("BOX_NO2")
    private String boxNo2;

    /**
     * 集装箱2-封号
     */
    @ApiModelProperty("集装箱2-封号(20)")
    @TableField("BOX_FH2")
    private String boxFh2;

    /**
     * 集装箱2-皮重(KG)
     */
    @ApiModelProperty("集装箱2-皮重(KG)(20)")
    @TableField("BOX_PZ2")
    private String boxPz2;

    /**
     * 集装箱2-类型
     */
    @ApiModelProperty("集装箱2-类型(20)")
    @TableField("BOX_TYPE2")
    private String boxType2;

    /**
     * 集装箱2-尺寸
     */
    @ApiModelProperty("集装箱2-尺寸(20)")
    @TableField("BOX_SIZE2")
    private String boxSize2;

    /**
     * 铁路口岸
     */
    @ApiModelProperty("铁路口岸(20)")
    @TableField("TL_KA")
    private String tlKa;

    /**
     * 总公里数
     */
    @ApiModelProperty("总公里数(22)")
    @TableField("KILOMETERS")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal kilometers;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public OmsOrderFwxmWorkFkEntity() {
        this.setSubClazz(OmsOrderFwxmWorkFkEntity.class);
    }

    public OmsOrderFwxmWorkFkEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public OmsOrderFwxmWorkFkEntity setWorkNo(String workNo) {
        this.workNo = workNo;
        this.nodifySetFiled("workNo", workNo);
        return this;
    }

    public String getWorkNo() {
        this.nodifyGetFiled("workNo");
        return workNo;
    }

    public OmsOrderFwxmWorkFkEntity setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        this.nodifySetFiled("orderNo", orderNo);
        return this;
    }

    public String getOrderNo() {
        this.nodifyGetFiled("orderNo");
        return orderNo;
    }

    public OmsOrderFwxmWorkFkEntity setFwlxCode(String fwlxCode) {
        this.fwlxCode = fwlxCode;
        this.nodifySetFiled("fwlxCode", fwlxCode);
        return this;
    }

    public String getFwlxCode() {
        this.nodifyGetFiled("fwlxCode");
        return fwlxCode;
    }

    public OmsOrderFwxmWorkFkEntity setFwxmCode(String fwxmCode) {
        this.fwxmCode = fwxmCode;
        this.nodifySetFiled("fwxmCode", fwxmCode);
        return this;
    }

    public String getFwxmCode() {
        this.nodifyGetFiled("fwxmCode");
        return fwxmCode;
    }

    public OmsOrderFwxmWorkFkEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public OmsOrderFwxmWorkFkEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public OmsOrderFwxmWorkFkEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public OmsOrderFwxmWorkFkEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public OmsOrderFwxmWorkFkEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public OmsOrderFwxmWorkFkEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public OmsOrderFwxmWorkFkEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public OmsOrderFwxmWorkFkEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public OmsOrderFwxmWorkFkEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public OmsOrderFwxmWorkFkEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public OmsOrderFwxmWorkFkEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public OmsOrderFwxmWorkFkEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public OmsOrderFwxmWorkFkEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public OmsOrderFwxmWorkFkEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public OmsOrderFwxmWorkFkEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public OmsOrderFwxmWorkFkEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public OmsOrderFwxmWorkFkEntity setOpMemo(String opMemo) {
        this.opMemo = opMemo;
        this.nodifySetFiled("opMemo", opMemo);
        return this;
    }

    public String getOpMemo() {
        this.nodifyGetFiled("opMemo");
        return opMemo;
    }

    public OmsOrderFwxmWorkFkEntity setHgcyNum(BigDecimal hgcyNum) {
        this.hgcyNum = hgcyNum;
        this.nodifySetFiled("hgcyNum", hgcyNum);
        return this;
    }

    public BigDecimal getHgcyNum() {
        this.nodifyGetFiled("hgcyNum");
        return hgcyNum;
    }

    public OmsOrderFwxmWorkFkEntity setHgcyMemo(String hgcyMemo) {
        this.hgcyMemo = hgcyMemo;
        this.nodifySetFiled("hgcyMemo", hgcyMemo);
        return this;
    }

    public String getHgcyMemo() {
        this.nodifyGetFiled("hgcyMemo");
        return hgcyMemo;
    }

    public OmsOrderFwxmWorkFkEntity setSjcyNum(BigDecimal sjcyNum) {
        this.sjcyNum = sjcyNum;
        this.nodifySetFiled("sjcyNum", sjcyNum);
        return this;
    }

    public BigDecimal getSjcyNum() {
        this.nodifyGetFiled("sjcyNum");
        return sjcyNum;
    }

    public OmsOrderFwxmWorkFkEntity setSjcyMemo(String sjcyMemo) {
        this.sjcyMemo = sjcyMemo;
        this.nodifySetFiled("sjcyMemo", sjcyMemo);
        return this;
    }

    public String getSjcyMemo() {
        this.nodifyGetFiled("sjcyMemo");
        return sjcyMemo;
    }

    public OmsOrderFwxmWorkFkEntity setJjNoRk(String jjNoRk) {
        this.jjNoRk = jjNoRk;
        this.nodifySetFiled("jjNoRk", jjNoRk);
        return this;
    }

    public String getJjNoRk() {
        this.nodifyGetFiled("jjNoRk");
        return jjNoRk;
    }

    public OmsOrderFwxmWorkFkEntity setJjNoCk(String jjNoCk) {
        this.jjNoCk = jjNoCk;
        this.nodifySetFiled("jjNoCk", jjNoCk);
        return this;
    }

    public String getJjNoCk() {
        this.nodifyGetFiled("jjNoCk");
        return jjNoCk;
    }

    public OmsOrderFwxmWorkFkEntity setTlBcNo(String tlBcNo) {
        this.tlBcNo = tlBcNo;
        this.nodifySetFiled("tlBcNo", tlBcNo);
        return this;
    }

    public String getTlBcNo() {
        this.nodifyGetFiled("tlBcNo");
        return tlBcNo;
    }

    public OmsOrderFwxmWorkFkEntity setTlDate(Date tlDate) {
        this.tlDate = tlDate;
        this.nodifySetFiled("tlDate", tlDate);
        return this;
    }

    public Date getTlDate() {
        this.nodifyGetFiled("tlDate");
        return tlDate;
    }

    public OmsOrderFwxmWorkFkEntity setTlDateStart(Date tlDateStart) {
        this.tlDateStart = tlDateStart;
        this.nodifySetFiled("tlDateStart", tlDateStart);
        return this;
    }

    public Date getTlDateStart() {
        this.nodifyGetFiled("tlDateStart");
        return tlDateStart;
    }

    public OmsOrderFwxmWorkFkEntity setTlDateEnd(Date tlDateEnd) {
        this.tlDateEnd = tlDateEnd;
        this.nodifySetFiled("tlDateEnd", tlDateEnd);
        return this;
    }

    public Date getTlDateEnd() {
        this.nodifyGetFiled("tlDateEnd");
        return tlDateEnd;
    }
    public OmsOrderFwxmWorkFkEntity setTlQfNo(String tlQfNo) {
        this.tlQfNo = tlQfNo;
        this.nodifySetFiled("tlQfNo", tlQfNo);
        return this;
    }

    public String getTlQfNo() {
        this.nodifyGetFiled("tlQfNo");
        return tlQfNo;
    }

    public OmsOrderFwxmWorkFkEntity setTlGfNo(String tlGfNo) {
        this.tlGfNo = tlGfNo;
        this.nodifySetFiled("tlGfNo", tlGfNo);
        return this;
    }

    public String getTlGfNo() {
        this.nodifyGetFiled("tlGfNo");
        return tlGfNo;
    }

    public OmsOrderFwxmWorkFkEntity setTlDcNo(String tlDcNo) {
        this.tlDcNo = tlDcNo;
        this.nodifySetFiled("tlDcNo", tlDcNo);
        return this;
    }

    public String getTlDcNo() {
        this.nodifyGetFiled("tlDcNo");
        return tlDcNo;
    }

    public OmsOrderFwxmWorkFkEntity setTlYdNo(String tlYdNo) {
        this.tlYdNo = tlYdNo;
        this.nodifySetFiled("tlYdNo", tlYdNo);
        return this;
    }

    public String getTlYdNo() {
        this.nodifyGetFiled("tlYdNo");
        return tlYdNo;
    }

    public OmsOrderFwxmWorkFkEntity setTlYjDate(Date tlYjDate) {
        this.tlYjDate = tlYjDate;
        this.nodifySetFiled("tlYjDate", tlYjDate);
        return this;
    }

    public Date getTlYjDate() {
        this.nodifyGetFiled("tlYjDate");
        return tlYjDate;
    }

    public OmsOrderFwxmWorkFkEntity setTlYjDateStart(Date tlYjDateStart) {
        this.tlYjDateStart = tlYjDateStart;
        this.nodifySetFiled("tlYjDateStart", tlYjDateStart);
        return this;
    }

    public Date getTlYjDateStart() {
        this.nodifyGetFiled("tlYjDateStart");
        return tlYjDateStart;
    }

    public OmsOrderFwxmWorkFkEntity setTlYjDateEnd(Date tlYjDateEnd) {
        this.tlYjDateEnd = tlYjDateEnd;
        this.nodifySetFiled("tlYjDateEnd", tlYjDateEnd);
        return this;
    }

    public Date getTlYjDateEnd() {
        this.nodifyGetFiled("tlYjDateEnd");
        return tlYjDateEnd;
    }
    public OmsOrderFwxmWorkFkEntity setHyCm(String hyCm) {
        this.hyCm = hyCm;
        this.nodifySetFiled("hyCm", hyCm);
        return this;
    }

    public String getHyCm() {
        this.nodifyGetFiled("hyCm");
        return hyCm;
    }

    public OmsOrderFwxmWorkFkEntity setHyHc(String hyHc) {
        this.hyHc = hyHc;
        this.nodifySetFiled("hyHc", hyHc);
        return this;
    }

    public String getHyHc() {
        this.nodifyGetFiled("hyHc");
        return hyHc;
    }

    public OmsOrderFwxmWorkFkEntity setHyQfNo(String hyQfNo) {
        this.hyQfNo = hyQfNo;
        this.nodifySetFiled("hyQfNo", hyQfNo);
        return this;
    }

    public String getHyQfNo() {
        this.nodifyGetFiled("hyQfNo");
        return hyQfNo;
    }

    public OmsOrderFwxmWorkFkEntity setHyDcNo(String hyDcNo) {
        this.hyDcNo = hyDcNo;
        this.nodifySetFiled("hyDcNo", hyDcNo);
        return this;
    }

    public String getHyDcNo() {
        this.nodifyGetFiled("hyDcNo");
        return hyDcNo;
    }

    public OmsOrderFwxmWorkFkEntity setHyTdNo(String hyTdNo) {
        this.hyTdNo = hyTdNo;
        this.nodifySetFiled("hyTdNo", hyTdNo);
        return this;
    }

    public String getHyTdNo() {
        this.nodifyGetFiled("hyTdNo");
        return hyTdNo;
    }

    public OmsOrderFwxmWorkFkEntity setHyDate(Date hyDate) {
        this.hyDate = hyDate;
        this.nodifySetFiled("hyDate", hyDate);
        return this;
    }

    public Date getHyDate() {
        this.nodifyGetFiled("hyDate");
        return hyDate;
    }

    public OmsOrderFwxmWorkFkEntity setHyDateStart(Date hyDateStart) {
        this.hyDateStart = hyDateStart;
        this.nodifySetFiled("hyDateStart", hyDateStart);
        return this;
    }

    public Date getHyDateStart() {
        this.nodifyGetFiled("hyDateStart");
        return hyDateStart;
    }

    public OmsOrderFwxmWorkFkEntity setHyDateEnd(Date hyDateEnd) {
        this.hyDateEnd = hyDateEnd;
        this.nodifySetFiled("hyDateEnd", hyDateEnd);
        return this;
    }

    public Date getHyDateEnd() {
        this.nodifyGetFiled("hyDateEnd");
        return hyDateEnd;
    }
    public OmsOrderFwxmWorkFkEntity setHyYjDate(Date hyYjDate) {
        this.hyYjDate = hyYjDate;
        this.nodifySetFiled("hyYjDate", hyYjDate);
        return this;
    }

    public Date getHyYjDate() {
        this.nodifyGetFiled("hyYjDate");
        return hyYjDate;
    }

    public OmsOrderFwxmWorkFkEntity setHyYjDateStart(Date hyYjDateStart) {
        this.hyYjDateStart = hyYjDateStart;
        this.nodifySetFiled("hyYjDateStart", hyYjDateStart);
        return this;
    }

    public Date getHyYjDateStart() {
        this.nodifyGetFiled("hyYjDateStart");
        return hyYjDateStart;
    }

    public OmsOrderFwxmWorkFkEntity setHyYjDateEnd(Date hyYjDateEnd) {
        this.hyYjDateEnd = hyYjDateEnd;
        this.nodifySetFiled("hyYjDateEnd", hyYjDateEnd);
        return this;
    }

    public Date getHyYjDateEnd() {
        this.nodifyGetFiled("hyYjDateEnd");
        return hyYjDateEnd;
    }
    public OmsOrderFwxmWorkFkEntity setKyMbNo(String kyMbNo) {
        this.kyMbNo = kyMbNo;
        this.nodifySetFiled("kyMbNo", kyMbNo);
        return this;
    }

    public String getKyMbNo() {
        this.nodifyGetFiled("kyMbNo");
        return kyMbNo;
    }

    public OmsOrderFwxmWorkFkEntity setKyHbNo(String kyHbNo) {
        this.kyHbNo = kyHbNo;
        this.nodifySetFiled("kyHbNo", kyHbNo);
        return this;
    }

    public String getKyHbNo() {
        this.nodifyGetFiled("kyHbNo");
        return kyHbNo;
    }

    public OmsOrderFwxmWorkFkEntity setKyHkgs(String kyHkgs) {
        this.kyHkgs = kyHkgs;
        this.nodifySetFiled("kyHkgs", kyHkgs);
        return this;
    }

    public String getKyHkgs() {
        this.nodifyGetFiled("kyHkgs");
        return kyHkgs;
    }

    public OmsOrderFwxmWorkFkEntity setKyHbh(String kyHbh) {
        this.kyHbh = kyHbh;
        this.nodifySetFiled("kyHbh", kyHbh);
        return this;
    }

    public String getKyHbh() {
        this.nodifyGetFiled("kyHbh");
        return kyHbh;
    }

    public OmsOrderFwxmWorkFkEntity setKyJcbh(String kyJcbh) {
        this.kyJcbh = kyJcbh;
        this.nodifySetFiled("kyJcbh", kyJcbh);
        return this;
    }

    public String getKyJcbh() {
        this.nodifyGetFiled("kyJcbh");
        return kyJcbh;
    }

    public OmsOrderFwxmWorkFkEntity setKyDate(Date kyDate) {
        this.kyDate = kyDate;
        this.nodifySetFiled("kyDate", kyDate);
        return this;
    }

    public Date getKyDate() {
        this.nodifyGetFiled("kyDate");
        return kyDate;
    }

    public OmsOrderFwxmWorkFkEntity setKyDateStart(Date kyDateStart) {
        this.kyDateStart = kyDateStart;
        this.nodifySetFiled("kyDateStart", kyDateStart);
        return this;
    }

    public Date getKyDateStart() {
        this.nodifyGetFiled("kyDateStart");
        return kyDateStart;
    }

    public OmsOrderFwxmWorkFkEntity setKyDateEnd(Date kyDateEnd) {
        this.kyDateEnd = kyDateEnd;
        this.nodifySetFiled("kyDateEnd", kyDateEnd);
        return this;
    }

    public Date getKyDateEnd() {
        this.nodifyGetFiled("kyDateEnd");
        return kyDateEnd;
    }
    public OmsOrderFwxmWorkFkEntity setKyYjDate(Date kyYjDate) {
        this.kyYjDate = kyYjDate;
        this.nodifySetFiled("kyYjDate", kyYjDate);
        return this;
    }

    public Date getKyYjDate() {
        this.nodifyGetFiled("kyYjDate");
        return kyYjDate;
    }

    public OmsOrderFwxmWorkFkEntity setKyYjDateStart(Date kyYjDateStart) {
        this.kyYjDateStart = kyYjDateStart;
        this.nodifySetFiled("kyYjDateStart", kyYjDateStart);
        return this;
    }

    public Date getKyYjDateStart() {
        this.nodifyGetFiled("kyYjDateStart");
        return kyYjDateStart;
    }

    public OmsOrderFwxmWorkFkEntity setKyYjDateEnd(Date kyYjDateEnd) {
        this.kyYjDateEnd = kyYjDateEnd;
        this.nodifySetFiled("kyYjDateEnd", kyYjDateEnd);
        return this;
    }

    public Date getKyYjDateEnd() {
        this.nodifyGetFiled("kyYjDateEnd");
        return kyYjDateEnd;
    }
    public OmsOrderFwxmWorkFkEntity setXzwtNo(String xzwtNo) {
        this.xzwtNo = xzwtNo;
        this.nodifySetFiled("xzwtNo", xzwtNo);
        return this;
    }

    public String getXzwtNo() {
        this.nodifyGetFiled("xzwtNo");
        return xzwtNo;
    }

    public OmsOrderFwxmWorkFkEntity setSysCode(String sysCode) {
        this.sysCode = sysCode;
        this.nodifySetFiled("sysCode", sysCode);
        return this;
    }

    public String getSysCode() {
        this.nodifyGetFiled("sysCode");
        return sysCode;
    }

    public OmsOrderFwxmWorkFkEntity setBizRegId(String bizRegId) {
        this.bizRegId = bizRegId;
        this.nodifySetFiled("bizRegId", bizRegId);
        return this;
    }

    public String getBizRegId() {
        this.nodifyGetFiled("bizRegId");
        return bizRegId;
    }

    public OmsOrderFwxmWorkFkEntity setDocType(String docType) {
        this.docType = docType;
        this.nodifySetFiled("docType", docType);
        return this;
    }

    public String getDocType() {
        this.nodifyGetFiled("docType");
        return docType;
    }

    public OmsOrderFwxmWorkFkEntity setTdjfzl(BigDecimal tdjfzl) {
        this.tdjfzl = tdjfzl;
        this.nodifySetFiled("tdjfzl", tdjfzl);
        return this;
    }

    public BigDecimal getTdjfzl() {
        this.nodifyGetFiled("tdjfzl");
        return tdjfzl;
    }

    public OmsOrderFwxmWorkFkEntity setCgs(String cgs) {
        this.cgs = cgs;
        this.nodifySetFiled("cgs", cgs);
        return this;
    }

    public String getCgs() {
        this.nodifyGetFiled("cgs");
        return cgs;
    }

    public OmsOrderFwxmWorkFkEntity setPoorQualityDesc(String poorQualityDesc) {
        this.poorQualityDesc = poorQualityDesc;
        this.nodifySetFiled("poorQualityDesc", poorQualityDesc);
        return this;
    }

    public String getPoorQualityDesc() {
        this.nodifyGetFiled("poorQualityDesc");
        return poorQualityDesc;
    }

    public OmsOrderFwxmWorkFkEntity setGoodsProtety(String goodsProtety) {
        this.goodsProtety = goodsProtety;
        this.nodifySetFiled("goodsProtety", goodsProtety);
        return this;
    }

    public String getGoodsProtety() {
        this.nodifyGetFiled("goodsProtety");
        return goodsProtety;
    }

    public OmsOrderFwxmWorkFkEntity setGoodsName(String goodsName) {
        this.goodsName = goodsName;
        this.nodifySetFiled("goodsName", goodsName);
        return this;
    }

    public String getGoodsName() {
        this.nodifyGetFiled("goodsName");
        return goodsName;
    }

    public OmsOrderFwxmWorkFkEntity setWeightTotal(BigDecimal weightTotal) {
        this.weightTotal = weightTotal;
        this.nodifySetFiled("weightTotal", weightTotal);
        return this;
    }

    public BigDecimal getWeightTotal() {
        this.nodifyGetFiled("weightTotal");
        return weightTotal;
    }

    public OmsOrderFwxmWorkFkEntity setPieceTotal(BigDecimal pieceTotal) {
        this.pieceTotal = pieceTotal;
        this.nodifySetFiled("pieceTotal", pieceTotal);
        return this;
    }

    public BigDecimal getPieceTotal() {
        this.nodifyGetFiled("pieceTotal");
        return pieceTotal;
    }

    public OmsOrderFwxmWorkFkEntity setWeightCalc(BigDecimal weightCalc) {
        this.weightCalc = weightCalc;
        this.nodifySetFiled("weightCalc", weightCalc);
        return this;
    }

    public BigDecimal getWeightCalc() {
        this.nodifyGetFiled("weightCalc");
        return weightCalc;
    }

    public OmsOrderFwxmWorkFkEntity setVolumeTotal(BigDecimal volumeTotal) {
        this.volumeTotal = volumeTotal;
        this.nodifySetFiled("volumeTotal", volumeTotal);
        return this;
    }

    public BigDecimal getVolumeTotal() {
        this.nodifyGetFiled("volumeTotal");
        return volumeTotal;
    }

    public OmsOrderFwxmWorkFkEntity setKhOrderNo(String khOrderNo) {
        this.khOrderNo = khOrderNo;
        this.nodifySetFiled("khOrderNo", khOrderNo);
        return this;
    }

    public String getKhOrderNo() {
        this.nodifyGetFiled("khOrderNo");
        return khOrderNo;
    }

    public OmsOrderFwxmWorkFkEntity setPkyType(String pkyType) {
        this.pkyType = pkyType;
        this.nodifySetFiled("pkyType", pkyType);
        return this;
    }

    public String getPkyType() {
        this.nodifyGetFiled("pkyType");
        return pkyType;
    }

    public OmsOrderFwxmWorkFkEntity setCjfs(String cjfs) {
        this.cjfs = cjfs;
        this.nodifySetFiled("cjfs", cjfs);
        return this;
    }

    public String getCjfs() {
        this.nodifyGetFiled("cjfs");
        return cjfs;
    }

    public OmsOrderFwxmWorkFkEntity setQyg(String qyg) {
        this.qyg = qyg;
        this.nodifySetFiled("qyg", qyg);
        return this;
    }

    public String getQyg() {
        this.nodifyGetFiled("qyg");
        return qyg;
    }

    public OmsOrderFwxmWorkFkEntity setMdg(String mdg) {
        this.mdg = mdg;
        this.nodifySetFiled("mdg", mdg);
        return this;
    }

    public String getMdg() {
        this.nodifyGetFiled("mdg");
        return mdg;
    }

    public OmsOrderFwxmWorkFkEntity setTdyq(String tdyq) {
        this.tdyq = tdyq;
        this.nodifySetFiled("tdyq", tdyq);
        return this;
    }

    public String getTdyq() {
        this.nodifyGetFiled("tdyq");
        return tdyq;
    }

    public OmsOrderFwxmWorkFkEntity setGq(String gq) {
        this.gq = gq;
        this.nodifySetFiled("gq", gq);
        return this;
    }

    public String getGq() {
        this.nodifyGetFiled("gq");
        return gq;
    }

    public OmsOrderFwxmWorkFkEntity setHx(String hx) {
        this.hx = hx;
        this.nodifySetFiled("hx", hx);
        return this;
    }

    public String getHx() {
        this.nodifyGetFiled("hx");
        return hx;
    }

    public OmsOrderFwxmWorkFkEntity setZx(String zx) {
        this.zx = zx;
        this.nodifySetFiled("zx", zx);
        return this;
    }

    public String getZx() {
        this.nodifyGetFiled("zx");
        return zx;
    }

    public OmsOrderFwxmWorkFkEntity setPx(String px) {
        this.px = px;
        this.nodifySetFiled("px", px);
        return this;
    }

    public String getPx() {
        this.nodifyGetFiled("px");
        return px;
    }

    public OmsOrderFwxmWorkFkEntity setZdfhr(String zdfhr) {
        this.zdfhr = zdfhr;
        this.nodifySetFiled("zdfhr", zdfhr);
        return this;
    }

    public String getZdfhr() {
        this.nodifyGetFiled("zdfhr");
        return zdfhr;
    }

    public OmsOrderFwxmWorkFkEntity setZdshr(String zdshr) {
        this.zdshr = zdshr;
        this.nodifySetFiled("zdshr", zdshr);
        return this;
    }

    public String getZdshr() {
        this.nodifyGetFiled("zdshr");
        return zdshr;
    }

    public OmsOrderFwxmWorkFkEntity setZdtzr(String zdtzr) {
        this.zdtzr = zdtzr;
        this.nodifySetFiled("zdtzr", zdtzr);
        return this;
    }

    public String getZdtzr() {
        this.nodifyGetFiled("zdtzr");
        return zdtzr;
    }

    public OmsOrderFwxmWorkFkEntity setFhr(String fhr) {
        this.fhr = fhr;
        this.nodifySetFiled("fhr", fhr);
        return this;
    }

    public String getFhr() {
        this.nodifyGetFiled("fhr");
        return fhr;
    }

    public OmsOrderFwxmWorkFkEntity setTzr(String tzr) {
        this.tzr = tzr;
        this.nodifySetFiled("tzr", tzr);
        return this;
    }

    public String getTzr() {
        this.nodifyGetFiled("tzr");
        return tzr;
    }

    public OmsOrderFwxmWorkFkEntity setMt(String mt) {
        this.mt = mt;
        this.nodifySetFiled("mt", mt);
        return this;
    }

    public String getMt() {
        this.nodifyGetFiled("mt");
        return mt;
    }

    public OmsOrderFwxmWorkFkEntity setJhd(String jhd) {
        this.jhd = jhd;
        this.nodifySetFiled("jhd", jhd);
        return this;
    }

    public String getJhd() {
        this.nodifyGetFiled("jhd");
        return jhd;
    }

    public OmsOrderFwxmWorkFkEntity setYftkZd(String yftkZd) {
        this.yftkZd = yftkZd;
        this.nodifySetFiled("yftkZd", yftkZd);
        return this;
    }

    public String getYftkZd() {
        this.nodifyGetFiled("yftkZd");
        return yftkZd;
    }

    public OmsOrderFwxmWorkFkEntity setZftkZd(String zftkZd) {
        this.zftkZd = zftkZd;
        this.nodifySetFiled("zftkZd", zftkZd);
        return this;
    }

    public String getZftkZd() {
        this.nodifyGetFiled("zftkZd");
        return zftkZd;
    }

    public OmsOrderFwxmWorkFkEntity setYftkFd(String yftkFd) {
        this.yftkFd = yftkFd;
        this.nodifySetFiled("yftkFd", yftkFd);
        return this;
    }

    public String getYftkFd() {
        this.nodifyGetFiled("yftkFd");
        return yftkFd;
    }

    public OmsOrderFwxmWorkFkEntity setZftkFd(String zftkFd) {
        this.zftkFd = zftkFd;
        this.nodifySetFiled("zftkFd", zftkFd);
        return this;
    }

    public String getZftkFd() {
        this.nodifyGetFiled("zftkFd");
        return zftkFd;
    }

    public OmsOrderFwxmWorkFkEntity setTdzzr(String tdzzr) {
        this.tdzzr = tdzzr;
        this.nodifySetFiled("tdzzr", tdzzr);
        return this;
    }

    public String getTdzzr() {
        this.nodifyGetFiled("tdzzr");
        return tdzzr;
    }

    public OmsOrderFwxmWorkFkEntity setWh(String wh) {
        this.wh = wh;
        this.nodifySetFiled("wh", wh);
        return this;
    }

    public String getWh() {
        this.nodifyGetFiled("wh");
        return wh;
    }

    public OmsOrderFwxmWorkFkEntity setDclx(String dclx) {
        this.dclx = dclx;
        this.nodifySetFiled("dclx", dclx);
        return this;
    }

    public String getDclx() {
        this.nodifyGetFiled("dclx");
        return dclx;
    }

    public OmsOrderFwxmWorkFkEntity setDcdl(String dcdl) {
        this.dcdl = dcdl;
        this.nodifySetFiled("dcdl", dcdl);
        return this;
    }

    public String getDcdl() {
        this.nodifyGetFiled("dcdl");
        return dcdl;
    }

    public OmsOrderFwxmWorkFkEntity setCdsb(String cdsb) {
        this.cdsb = cdsb;
        this.nodifySetFiled("cdsb", cdsb);
        return this;
    }

    public String getCdsb() {
        this.nodifyGetFiled("cdsb");
        return cdsb;
    }

    public OmsOrderFwxmWorkFkEntity setZzgCode(String zzgCode) {
        this.zzgCode = zzgCode;
        this.nodifySetFiled("zzgCode", zzgCode);
        return this;
    }

    public String getZzgCode() {
        this.nodifyGetFiled("zzgCode");
        return zzgCode;
    }

    public OmsOrderFwxmWorkFkEntity setWeightTotalSj(BigDecimal weightTotalSj) {
        this.weightTotalSj = weightTotalSj;
        this.nodifySetFiled("weightTotalSj", weightTotalSj);
        return this;
    }

    public BigDecimal getWeightTotalSj() {
        this.nodifyGetFiled("weightTotalSj");
        return weightTotalSj;
    }

    public OmsOrderFwxmWorkFkEntity setPieceTotalSj(BigDecimal pieceTotalSj) {
        this.pieceTotalSj = pieceTotalSj;
        this.nodifySetFiled("pieceTotalSj", pieceTotalSj);
        return this;
    }

    public BigDecimal getPieceTotalSj() {
        this.nodifyGetFiled("pieceTotalSj");
        return pieceTotalSj;
    }

    public OmsOrderFwxmWorkFkEntity setWeightCalcSj(BigDecimal weightCalcSj) {
        this.weightCalcSj = weightCalcSj;
        this.nodifySetFiled("weightCalcSj", weightCalcSj);
        return this;
    }

    public BigDecimal getWeightCalcSj() {
        this.nodifyGetFiled("weightCalcSj");
        return weightCalcSj;
    }

    public OmsOrderFwxmWorkFkEntity setVolumeTotalSj(BigDecimal volumeTotalSj) {
        this.volumeTotalSj = volumeTotalSj;
        this.nodifySetFiled("volumeTotalSj", volumeTotalSj);
        return this;
    }

    public BigDecimal getVolumeTotalSj() {
        this.nodifyGetFiled("volumeTotalSj");
        return volumeTotalSj;
    }

    public OmsOrderFwxmWorkFkEntity setWeightTotalZd(BigDecimal weightTotalZd) {
        this.weightTotalZd = weightTotalZd;
        this.nodifySetFiled("weightTotalZd", weightTotalZd);
        return this;
    }

    public BigDecimal getWeightTotalZd() {
        this.nodifyGetFiled("weightTotalZd");
        return weightTotalZd;
    }

    public OmsOrderFwxmWorkFkEntity setPieceTotalZd(BigDecimal pieceTotalZd) {
        this.pieceTotalZd = pieceTotalZd;
        this.nodifySetFiled("pieceTotalZd", pieceTotalZd);
        return this;
    }

    public BigDecimal getPieceTotalZd() {
        this.nodifyGetFiled("pieceTotalZd");
        return pieceTotalZd;
    }

    public OmsOrderFwxmWorkFkEntity setWeightCalcZd(BigDecimal weightCalcZd) {
        this.weightCalcZd = weightCalcZd;
        this.nodifySetFiled("weightCalcZd", weightCalcZd);
        return this;
    }

    public BigDecimal getWeightCalcZd() {
        this.nodifyGetFiled("weightCalcZd");
        return weightCalcZd;
    }

    public OmsOrderFwxmWorkFkEntity setVolumeTotalZd(BigDecimal volumeTotalZd) {
        this.volumeTotalZd = volumeTotalZd;
        this.nodifySetFiled("volumeTotalZd", volumeTotalZd);
        return this;
    }

    public BigDecimal getVolumeTotalZd() {
        this.nodifyGetFiled("volumeTotalZd");
        return volumeTotalZd;
    }

    public OmsOrderFwxmWorkFkEntity setGwdl(String gwdl) {
        this.gwdl = gwdl;
        this.nodifySetFiled("gwdl", gwdl);
        return this;
    }

    public String getGwdl() {
        this.nodifyGetFiled("gwdl");
        return gwdl;
    }

    public OmsOrderFwxmWorkFkEntity setZdmdd(String zdmdd) {
        this.zdmdd = zdmdd;
        this.nodifySetFiled("zdmdd", zdmdd);
        return this;
    }

    public String getZdmdd() {
        this.nodifyGetFiled("zdmdd");
        return zdmdd;
    }

    public OmsOrderFwxmWorkFkEntity setSjhb(String sjhb) {
        this.sjhb = sjhb;
        this.nodifySetFiled("sjhb", sjhb);
        return this;
    }

    public String getSjhb() {
        this.nodifyGetFiled("sjhb");
        return sjhb;
    }

    public OmsOrderFwxmWorkFkEntity setWhAdd(String whAdd) {
        this.whAdd = whAdd;
        this.nodifySetFiled("whAdd", whAdd);
        return this;
    }

    public String getWhAdd() {
        this.nodifyGetFiled("whAdd");
        return whAdd;
    }

    public OmsOrderFwxmWorkFkEntity setGdQty(BigDecimal gdQty) {
        this.gdQty = gdQty;
        this.nodifySetFiled("gdQty", gdQty);
        return this;
    }

    public BigDecimal getGdQty() {
        this.nodifyGetFiled("gdQty");
        return gdQty;
    }

    public OmsOrderFwxmWorkFkEntity setYsgj(String ysgj) {
        this.ysgj = ysgj;
        this.nodifySetFiled("ysgj", ysgj);
        return this;
    }

    public String getYsgj() {
        this.nodifyGetFiled("ysgj");
        return ysgj;
    }

    public OmsOrderFwxmWorkFkEntity setCrk(String crk) {
        this.crk = crk;
        this.nodifySetFiled("crk", crk);
        return this;
    }

    public String getCrk() {
        this.nodifyGetFiled("crk");
        return crk;
    }

    public OmsOrderFwxmWorkFkEntity setRkDate(Date rkDate) {
        this.rkDate = rkDate;
        this.nodifySetFiled("rkDate", rkDate);
        return this;
    }

    public Date getRkDate() {
        this.nodifyGetFiled("rkDate");
        return rkDate;
    }

    public OmsOrderFwxmWorkFkEntity setRkDateStart(Date rkDateStart) {
        this.rkDateStart = rkDateStart;
        this.nodifySetFiled("rkDateStart", rkDateStart);
        return this;
    }

    public Date getRkDateStart() {
        this.nodifyGetFiled("rkDateStart");
        return rkDateStart;
    }

    public OmsOrderFwxmWorkFkEntity setRkDateEnd(Date rkDateEnd) {
        this.rkDateEnd = rkDateEnd;
        this.nodifySetFiled("rkDateEnd", rkDateEnd);
        return this;
    }

    public Date getRkDateEnd() {
        this.nodifyGetFiled("rkDateEnd");
        return rkDateEnd;
    }
    public OmsOrderFwxmWorkFkEntity setCkDate(Date ckDate) {
        this.ckDate = ckDate;
        this.nodifySetFiled("ckDate", ckDate);
        return this;
    }

    public Date getCkDate() {
        this.nodifyGetFiled("ckDate");
        return ckDate;
    }

    public OmsOrderFwxmWorkFkEntity setCkDateStart(Date ckDateStart) {
        this.ckDateStart = ckDateStart;
        this.nodifySetFiled("ckDateStart", ckDateStart);
        return this;
    }

    public Date getCkDateStart() {
        this.nodifyGetFiled("ckDateStart");
        return ckDateStart;
    }

    public OmsOrderFwxmWorkFkEntity setCkDateEnd(Date ckDateEnd) {
        this.ckDateEnd = ckDateEnd;
        this.nodifySetFiled("ckDateEnd", ckDateEnd);
        return this;
    }

    public Date getCkDateEnd() {
        this.nodifyGetFiled("ckDateEnd");
        return ckDateEnd;
    }
    public OmsOrderFwxmWorkFkEntity setIe(String ie) {
        this.ie = ie;
        this.nodifySetFiled("ie", ie);
        return this;
    }

    public String getIe() {
        this.nodifyGetFiled("ie");
        return ie;
    }

    public OmsOrderFwxmWorkFkEntity setGoodsSize(String goodsSize) {
        this.goodsSize = goodsSize;
        this.nodifySetFiled("goodsSize", goodsSize);
        return this;
    }

    public String getGoodsSize() {
        this.nodifyGetFiled("goodsSize");
        return goodsSize;
    }

    public OmsOrderFwxmWorkFkEntity setLyDcNo(String lyDcNo) {
        this.lyDcNo = lyDcNo;
        this.nodifySetFiled("lyDcNo", lyDcNo);
        return this;
    }

    public String getLyDcNo() {
        this.nodifyGetFiled("lyDcNo");
        return lyDcNo;
    }

    public OmsOrderFwxmWorkFkEntity setCcrn(String ccrn) {
        this.ccrn = ccrn;
        this.nodifySetFiled("ccrn", ccrn);
        return this;
    }

    public String getCcrn() {
        this.nodifyGetFiled("ccrn");
        return ccrn;
    }

    public OmsOrderFwxmWorkFkEntity setLyLldh(String lyLldh) {
        this.lyLldh = lyLldh;
        this.nodifySetFiled("lyLldh", lyLldh);
        return this;
    }

    public String getLyLldh() {
        this.nodifyGetFiled("lyLldh");
        return lyLldh;
    }

    public OmsOrderFwxmWorkFkEntity setBoxNo1(String boxNo1) {
        this.boxNo1 = boxNo1;
        this.nodifySetFiled("boxNo1", boxNo1);
        return this;
    }

    public String getBoxNo1() {
        this.nodifyGetFiled("boxNo1");
        return boxNo1;
    }

    public OmsOrderFwxmWorkFkEntity setBoxFh1(String boxFh1) {
        this.boxFh1 = boxFh1;
        this.nodifySetFiled("boxFh1", boxFh1);
        return this;
    }

    public String getBoxFh1() {
        this.nodifyGetFiled("boxFh1");
        return boxFh1;
    }

    public OmsOrderFwxmWorkFkEntity setBoxPz1(String boxPz1) {
        this.boxPz1 = boxPz1;
        this.nodifySetFiled("boxPz1", boxPz1);
        return this;
    }

    public String getBoxPz1() {
        this.nodifyGetFiled("boxPz1");
        return boxPz1;
    }

    public OmsOrderFwxmWorkFkEntity setBoxType1(String boxType1) {
        this.boxType1 = boxType1;
        this.nodifySetFiled("boxType1", boxType1);
        return this;
    }

    public String getBoxType1() {
        this.nodifyGetFiled("boxType1");
        return boxType1;
    }

    public OmsOrderFwxmWorkFkEntity setBoxSize1(String boxSize1) {
        this.boxSize1 = boxSize1;
        this.nodifySetFiled("boxSize1", boxSize1);
        return this;
    }

    public String getBoxSize1() {
        this.nodifyGetFiled("boxSize1");
        return boxSize1;
    }

    public OmsOrderFwxmWorkFkEntity setBoxNo2(String boxNo2) {
        this.boxNo2 = boxNo2;
        this.nodifySetFiled("boxNo2", boxNo2);
        return this;
    }

    public String getBoxNo2() {
        this.nodifyGetFiled("boxNo2");
        return boxNo2;
    }

    public OmsOrderFwxmWorkFkEntity setBoxFh2(String boxFh2) {
        this.boxFh2 = boxFh2;
        this.nodifySetFiled("boxFh2", boxFh2);
        return this;
    }

    public String getBoxFh2() {
        this.nodifyGetFiled("boxFh2");
        return boxFh2;
    }

    public OmsOrderFwxmWorkFkEntity setBoxPz2(String boxPz2) {
        this.boxPz2 = boxPz2;
        this.nodifySetFiled("boxPz2", boxPz2);
        return this;
    }

    public String getBoxPz2() {
        this.nodifyGetFiled("boxPz2");
        return boxPz2;
    }

    public OmsOrderFwxmWorkFkEntity setBoxType2(String boxType2) {
        this.boxType2 = boxType2;
        this.nodifySetFiled("boxType2", boxType2);
        return this;
    }

    public String getBoxType2() {
        this.nodifyGetFiled("boxType2");
        return boxType2;
    }

    public OmsOrderFwxmWorkFkEntity setBoxSize2(String boxSize2) {
        this.boxSize2 = boxSize2;
        this.nodifySetFiled("boxSize2", boxSize2);
        return this;
    }

    public String getBoxSize2() {
        this.nodifyGetFiled("boxSize2");
        return boxSize2;
    }

    public OmsOrderFwxmWorkFkEntity setTlKa(String tlKa) {
        this.tlKa = tlKa;
        this.nodifySetFiled("tlKa", tlKa);
        return this;
    }

    public String getTlKa() {
        this.nodifyGetFiled("tlKa");
        return tlKa;
    }

    public OmsOrderFwxmWorkFkEntity setKilometers(BigDecimal kilometers) {
        this.kilometers = kilometers;
        this.nodifySetFiled("kilometers", kilometers);
        return this;
    }

    public BigDecimal getKilometers() {
        this.nodifyGetFiled("kilometers");
        return kilometers;
    }

    public OmsOrderFwxmWorkFkEntity setShrConsignee(String shrConsignee) {
        this.shrConsignee = shrConsignee;
        this.nodifySetFiled("shrConsignee", shrConsignee);
        return this;
    }

    public String getShrConsignee() {
        this.nodifyGetFiled("shrConsignee");
        return shrConsignee;
    }
}
