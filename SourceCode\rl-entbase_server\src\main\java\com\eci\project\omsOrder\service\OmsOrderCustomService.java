package com.eci.project.omsOrder.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.*;
import com.eci.common.db.DBHelper;
import com.eci.common.elementui.TreeNode;
import com.eci.common.enums.Enums;
import com.eci.common.enums.OrderEnum;
import com.eci.common.util.StringUtils;
import com.eci.common.utils.CommonLib;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.exception.BaseException;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.crmCustomerKh.dao.CrmCustomerKhDao;
import com.eci.project.crmCustomerKh.entity.CrmCustomerKhEntity;
import com.eci.project.fzgjBdProduct.dao.FzgjBdProductDao;
import com.eci.project.fzgjBdProduct.entity.FzgjBdProductEntity;
import com.eci.project.fzgjBdProductFwxm.dao.FzgjBdProductFwxmDao;
import com.eci.project.fzgjBdProductFwxm.entity.FzgjBdProductFwxmEntity;
import com.eci.project.fzgjBdProductService.dao.FzgjBdProductServiceDao;
import com.eci.project.fzgjBdProductService.entity.FzgjBdProductServiceEntity;
import com.eci.project.fzgjBdServiceItem.dao.FzgjBdServiceItemDao;
import com.eci.project.fzgjBdServiceItem.entity.FzgjBdServiceItemEntity;
import com.eci.project.fzgjBdServiceItem.service.FzgjBdServiceItemService;
import com.eci.project.fzgjBdServiceItemPages.dao.FzgjBdServiceItemPagesDao;
import com.eci.project.fzgjBdServiceItemPages.entity.FzgjBdServiceItemPagesEntity;
import com.eci.project.fzgjBdServiceItemPt.dao.FzgjBdServiceItemPtDao;
import com.eci.project.fzgjBdServiceItemPt.entity.FzgjBdServiceItemPtEntity;
import com.eci.project.fzgjBdServiceItemPt.entity.TreeModel;
import com.eci.project.fzgjBdServiceType.dao.FzgjBdServiceTypeDao;
import com.eci.project.fzgjBdServiceType.entity.FzgjBdServiceTypeEntity;
import com.eci.project.fzgjBdServiceTypeCom.dao.FzgjBdServiceTypeComDao;
import com.eci.project.fzgjBdServiceTypeCom.entity.FzgjBdServiceTypeComEntity;
import com.eci.project.fzgjTaskLimitation.dao.FzgjTaskLimitationDao;
import com.eci.project.fzgjTaskLimitation.entity.FzgjTaskLimitationEntity;
import com.eci.project.fzgjTaskLimitationFixed.dao.FzgjTaskLimitationFixedDao;
import com.eci.project.fzgjTaskLimitationFixed.entity.FzgjTaskLimitationFixedEntity;
import com.eci.project.fzgjTaskLimitationTime.dao.FzgjTaskLimitationTimeDao;
import com.eci.project.fzgjTaskLimitationTime.entity.FzgjTaskLimitationTimeEntity;
import com.eci.project.omsOrder.dao.OmsOrderDao;
import com.eci.project.omsOrder.entity.OmsOrderEntity;
import com.eci.project.omsOrder.validate.OmsOrderVal;
import com.eci.project.omsOrderFw.dao.OmsOrderFwDao;
import com.eci.project.omsOrderFw.entity.OmsOrderFwEntity;
import com.eci.project.omsOrderFw.service.OmsOrderFwService;
import com.eci.project.omsOrderFwxm.dao.OmsOrderFwxmDao;
import com.eci.project.omsOrderFwxm.entity.OmsOrderFwxmEntity;
import com.eci.project.omsOrderFwxmBgbj.dao.OmsOrderFwxmBgbjDao;
import com.eci.project.omsOrderFwxmBgbj.entity.OmsOrderFwxmBgbjEntity;
import com.eci.project.omsOrderFwxmWork.dao.OmsOrderFwxmWorkDao;
import com.eci.project.omsOrderFwxmWork.entity.OmsOrderFwxmWorkEntity;
import com.eci.project.omsOrderFwxmWorkTrace.dao.OmsOrderFwxmWorkTraceDao;
import com.eci.project.omsOrderFwxmWorkTrace.entity.OmsOrderFwxmWorkTraceEntity;
import com.eci.project.omsOrderJdmb.entity.OmsOrderJdmbEntity;
import com.eci.project.omsOrderJdmb.service.OmsOrderJdmbService;
import com.eci.project.omsOrderLog.entity.OmsOrderLogEntity;
import com.eci.project.omsOrderLog.service.OmsOrderLogService;
import com.eci.project.omsOrderPre.dao.OmsOrderPreDao;
import com.eci.project.omsOrderPre.entity.OmsOrderPreEntity;
import com.eci.sso.login.entity.UserInfo;
import com.eci.sso.role.entity.UserContext;
import com.eci.wu.core.EntityBase;
import com.eci.wu.util.DateTime;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 订单表Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-04-14
 */
@Service
@Slf4j
public class OmsOrderCustomService implements EciBaseService<OmsOrderEntity> {

    CommonLib cmn = CommonLib.getInstance();

    @Autowired
    private OmsOrderDao omsOrderDao;

    @Autowired
    private OmsOrderVal omsOrderVal;

    @Autowired
    private OmsOrderFwService omsOrderFwService;

    @Autowired
    private OmsOrderJdmbService omsOrderJdmbService;

    @Autowired
    private OmsOrderPreDao omsOrderPreDao;

    public CodeNameCommon opTypeSearch(String jsonString) {
        if (StringUtils.isEmpty(jsonString)) {
            return null;
        }
        ZsrJson json = ZsrJson.parse(jsonString);
        if (json == null) {
            return null;
        }

        String opType = json.check("opType").getString("opType");
        String consigneeCode = json.check("consigneeCode").getString("consigneeCode");

        String sql = Zsr.String.format("SELECT A.CODE, A.NAME\n" +
                        "        FROM FZGJ_BD_PRODUCT A\n" +
                        "        INNER JOIN FZGJ_BD_OP_TYPE OP\n" +
                        "        ON A.OP_TYPE = OP.CODE\n" +
                        "        AND A.BILL_CODE = OP.SYS_CODE\n" +
                        "        AND OP.STATUS = 'Y'\n" +
                        "        AND A.GROUP_CODE = OP.GROUP_CODE\n" +
                        "        WHERE A.STATUS = 'Y'\n" +
                        "        AND A.GROUP_CODE = {0}\n" +
                        "        AND A.BILL_CODE = 'OMS_ORDER'\n" +
                        "        AND A.OP_TYPE = {1}\n" +
                        "        AND (A.IS_STANDARD = 'Y' OR\n" +
                        "                (A.IS_STANDARD = 'N' AND A.CUSTOMER_CODE = {2}))"
                , cmn.SQLQ(UserContext.getUserInfo().getCompanyCode())
                , cmn.SQLQ(opType)
                , cmn.SQLQ(consigneeCode)

        );

        List<CodeNameCommon> codeNameCommons = DBHelper.selectList(sql, CodeNameCommon.class);
        return codeNameCommons.size() > 0 ? codeNameCommons.get(0) : null;
    }

    /**
     * 获取服务类型
     *
     * @param jsonString
     * @return
     */
    public ZsrBaseEntity GetServiceType(String jsonString) {
        if (StringUtils.isEmpty(jsonString)) {
            return null;
        }
        ZsrJson json = ZsrJson.parse(jsonString);
        if (json == null) {
            return null;
        }

        String opType = json.check("opType").getString("opType");
        if (Zsr.String.IsNullOrWhiteSpace(json.getStringOrDefault("productCode", ""))) {
            return null;
        }
        String productCode = json.check("productCode").getString("productCode");
//        String consigneeCode = json.check("consigneeCode").getString("consigneeCode");

        List<CodeNameCommon> codeNameCommonList = this.orderFwServiceTypeSearch(opType, productCode);

        ZsrBaseEntity result = new ZsrBaseEntity();
        //FZGJ_BD_SERVICE_TYPE
//        result["FWLX"] = response.ListEntity.Upgrade < FZGJ_BD_SERVICE_TYPE > ().Select(s = > new FZGJ_BD_SERVICE_TYPE() {
//            CODE =s.CODE,
//            NAME =s.NAME
//        }).ToList().Downgrade().ToJson(true);
        result.push("FWLX", codeNameCommonList);

        List<FzgjBdProductFwxmEntity> fwxmList = this.FzgjBdProductFwxmSearch(opType, productCode);

        //FZGJ_BD_PRODUCT_FWXM
//        result["FWXM"] = response.DataTable.ToListEntity < FZGJ_BD_PRODUCT_FWXM > ().Select(s = > new FZGJ_BD_PRODUCT_FWXM() {
//            FWXM_CODE =s.FWXM_CODE,
//            PRODUCT_CODE =s.PRODUCT_CODE,
//            OP_TYPE =s.OP_TYPE
//        }).ToList().Downgrade().ToJson(true);
        result.push("FWXM", fwxmList);


        FzgjBdProductEntity fzgjBdProduct = this.FzgjBdProductSearch(opType, productCode, "Y", "OMS_ORDER");
        if (fzgjBdProduct == null) {
            fzgjBdProduct = new FzgjBdProductEntity();
        }
        result.push("PRODUCT", fzgjBdProduct);

        return result;
    }

    private FzgjBdProductEntity FzgjBdProductSearch(String opType, String productCode, String y, String oms_order) {
        return fzgjBdProductDao.select()
                .eq(FzgjBdProductEntity::getOpType, opType)
                .eq(FzgjBdProductEntity::getCode, productCode)
                .eq(FzgjBdProductEntity::getStatus, y)
                .eq(FzgjBdProductEntity::getBillCode, oms_order)
                .eq(FzgjBdProductEntity::getGroupCode, UserContext.getUserInfo().getCompanyCode())
                .one();
    }

    /**
     * 根据操作类型和产品编码获取服务类型
     *
     * @param opType
     * @param productCode
     * @return
     */
    private List<FzgjBdProductFwxmEntity> FzgjBdProductFwxmSearch(String opType, String productCode) {
        return fzgjBdProductFwxmDao.select()
                .eq(FzgjBdProductFwxmEntity::getOpType, opType)
                .eq(FzgjBdProductFwxmEntity::getProductCode, productCode)
                .eq(FzgjBdProductFwxmEntity::getGroupCode, UserContext.getUserInfo().getCompanyCode())
                .list();
    }

    /**
     * 产品服务类型 DAO
     */
    @Autowired
    private FzgjBdProductServiceDao fzgjBdProductServiceDao;
    /**
     * 企业级服务类型 DAO
     */
    @Autowired
    private FzgjBdServiceTypeComDao fzgjBdServiceTypeComDao;
    /**
     * 产品服务项目 DAO
     */
    @Autowired
    private FzgjBdProductFwxmDao fzgjBdProductFwxmDao;
    /**
     * 业务产品 DAO
     */
    @Autowired
    private FzgjBdProductDao fzgjBdProductDao;

    /**
     * 根据操作类型和产品编码获取服务类型
     *
     * @param opType      操作类型
     * @param productCode 产品编码
     * @return
     */
    private List<CodeNameCommon> orderFwServiceTypeSearch(String opType, String productCode) {
        // 提前获取公司代码，避免多次调用 UserContext
        String groupCode = UserContext.getUserInfo().getCompanyCode();

        // 第一步：查询符合条件的产品服务列表
        List<FzgjBdProductServiceEntity> productServiceList = fzgjBdProductServiceDao
                .select()
                .eq(FzgjBdProductServiceEntity::getOpType, opType)
                .eq(FzgjBdProductServiceEntity::getProductCode, productCode)
                .eq(FzgjBdProductServiceEntity::getGroupCode, groupCode)
                .list();

        if (productServiceList.isEmpty()) {
            return Collections.emptyList(); // 无数据时直接返回空集合
        }

        // 提取所有 serviceNo
        List<String> serviceNos = productServiceList.stream()
                .map(FzgjBdProductServiceEntity::getServiceNo)
                .collect(Collectors.toList());

        // 第二步：查询服务类型并转换为 CodeNameCommon 列表
        return fzgjBdServiceTypeComDao.select()
//                .eq(FzgjBdServiceTypeComEntity::getParentid, "0")
//                .or().isNull(FzgjBdServiceTypeComEntity::getParentid)
                .eq(FzgjBdServiceTypeComEntity::getStatus, Enums.YNStatus.Y.getCode())
                .in(FzgjBdServiceTypeComEntity::getCode, serviceNos)
                .eq(FzgjBdServiceTypeComEntity::getGroupCode, groupCode)
                .orderBy(false, FzgjBdServiceTypeComEntity::getSeq)
                .list()
                .stream()
                .map(entity -> new CodeNameCommon(entity.getCode(), entity.getName()))
                .collect(Collectors.toList());
    }

    /**
     * 订单的服务类型
     */
    @Autowired
    private OmsOrderFwDao omsOrderFwDao;

    /**
     * 订单服务项目
     */
    @Autowired
    private OmsOrderFwxmDao omsOrderFwxmDao;

    @Autowired
    private FzgjBdServiceItemService fzgjBdServiceItemService;

    /**
     * 获取服务项目树形结构
     * @param jsonString
     * @return
     */
    public ZsrBaseEntity GetNodeList(String jsonString) {
        ZsrBaseEntity result = new ZsrBaseEntity();
        if (StringUtils.isEmpty(jsonString)) {
            return result;
        }
        ZsrJson json = ZsrJson.parse(jsonString);
        if (json == null) {
            return result;
        }
        String selectedCode = json.getString("sGuid");
        String rootCode = json.getString("rGuid");
        String orderNo = json.getString("orderNo");

        List<String> listSelectedCode = Zsr.String.string2List(selectedCode);
        List<String> listRootCode = Zsr.String.string2List(rootCode);
        List<String> listFwxmCode = new ArrayList<>();
        List<String> listFwlxCode = new ArrayList<>();

        if (!Zsr.String.IsNullOrWhiteSpace(orderNo)) {

            // 已保存的服务类型
            listFwlxCode = omsOrderFwDao.select()
                    .eq(OmsOrderFwEntity::getOrderNo, orderNo)
                    .list()
                    .stream()
                    .map(OmsOrderFwEntity::getFwlxCode)
                    .distinct()
                    .collect(Collectors.toList());

            List<CodeNameCommon> fwlxCodeNameList = new ArrayList<>();

            String groupCode = UserContext.getUserInfo().getCompanyCode();
            if (listFwlxCode.size() > 0) {
                // 第二步：查询服务类型并转换为 CodeNameCommon 列表
                fwlxCodeNameList = fzgjBdServiceTypeComDao.select()
//                        .eq(FzgjBdServiceTypeComEntity::getParentid, "0")
                        .eq(FzgjBdServiceTypeComEntity::getStatus, Enums.YNStatus.Y.getCode())
                        .in(FzgjBdServiceTypeComEntity::getCode, listFwlxCode)
                        .eq(FzgjBdServiceTypeComEntity::getGroupCode, groupCode)
                        .orderBy(false, FzgjBdServiceTypeComEntity::getSeq)
                        .list()
                        .stream()
                        .map(entity -> new CodeNameCommon(entity.getCode(), entity.getName()))
                        .collect(Collectors.toList());
            }

            // 服务项目
            List<OmsOrderFwxmEntity> fwxmEntities = omsOrderFwxmDao.select()
                    .eq(OmsOrderFwxmEntity::getOrderNo, orderNo).list();
            fwxmEntities.forEach(fwxm -> {
                String fwxmCode = fwxm.getFwxmCode();
                if (!Zsr.String.IsNullOrWhiteSpace(fwxmCode) && !listFwxmCode.contains(fwxmCode)) {
                    listFwxmCode.add(fwxmCode);
                }
            });
            // 服务类型，已保存的
            result.push("fwlxSelected", fwlxCodeNameList);

            if (listFwxmCode.size() == 0) {
                listFwxmCode.add("300200014");
                listFwxmCode.add("300200001");
                listFwxmCode.add("300100600910");
            }
            // 服务项目，已保存的
            result.push("fwxmSelected", listFwxmCode);
        }

        //获取服务类型
        List<FzgjBdServiceTypeEntity> fzgjBdServiceTypeComEntities = this.FzgjBdServiceTypeComSearch();

        // 获取所有服务项目配置页面URL TODO:暂时取消
        List<FzgjBdServiceItemPagesEntity> listFwxmPage = this.OrderFwxmPageSearch("");
        //获取平台所有服务项目
        List<FzgjBdServiceItemPtEntity> fzgjBdServiceItemPtEntityList = this.getServiceItemPt();

        List<TreeModel> treeModels = fzgjBdServiceItemService.selectTree(UserContext.getUserInfo().getCompanyCode());
        List<FzgjBdServiceItemEntity> fzgjBdServiceItemEntities = getDefaultCheck(treeModels);

        OmsHandlerTreeBuilder.TreeResult resultTree =
                OmsHandlerTreeBuilder.buildTreeWithParentMap(fzgjBdServiceTypeComEntities, fzgjBdServiceItemPtEntityList,
                        fzgjBdServiceItemEntities, listFwxmPage);

// 获取树结构用于展示
        List<TreeNode> treeNodes = resultTree.getTree();
        result.push("TREE_NODE", treeNodes);

        return result;
    }

    /**
     * 客户下单自助，获取左侧树节点
     *
     * @param jsonString
     * @return
     */
    public ZsrBaseEntity GetNodeListZizhu(String jsonString) {
        ZsrBaseEntity result = new ZsrBaseEntity();
        if (StringUtils.isEmpty(jsonString)) {
            return result;
        }
        ZsrJson json = ZsrJson.parse(jsonString);
        if (json == null) {
            return result;
        }
        String selectedCode = json.getString("sGuid");
        String rootCode = json.getString("rGuid");
        String preNo = json.getString("orderNo");

        List<String> listSelectedCode = Zsr.String.string2List(selectedCode);
        List<String> listRootCode = Zsr.String.string2List(rootCode);
        List<String> listFwxmCode = new ArrayList<>();
        List<String> listFwlxCode = new ArrayList<>();

        if (!Zsr.String.IsNullOrWhiteSpace(preNo)) {

            // 已保存的服务类型
            listFwlxCode = omsOrderFwDao.select()
                    .eq(OmsOrderFwEntity::getPreNo, preNo)
                    .list()
                    .stream()
                    .map(OmsOrderFwEntity::getFwlxCode)
                    .distinct()
                    .collect(Collectors.toList());

            List<CodeNameCommon> fwlxCodeNameList = new ArrayList<>();

            String groupCode = UserContext.getUserInfo().getCompanyCode();
            if (listFwlxCode.size() > 0) {
                // 第二步：查询服务类型并转换为 CodeNameCommon 列表
                fwlxCodeNameList = fzgjBdServiceTypeComDao.select()
//                        .eq(FzgjBdServiceTypeComEntity::getParentid, "0")
                        .eq(FzgjBdServiceTypeComEntity::getStatus, Enums.YNStatus.Y.getCode())
                        .in(FzgjBdServiceTypeComEntity::getCode, listFwlxCode)
                        .eq(FzgjBdServiceTypeComEntity::getGroupCode, groupCode)
                        .orderBy(false, FzgjBdServiceTypeComEntity::getSeq)
                        .list()
                        .stream()
                        .map(entity -> new CodeNameCommon(entity.getCode(), entity.getName()))
                        .collect(Collectors.toList());
            }

            // 服务项目
            List<OmsOrderFwxmEntity> fwxmEntities = omsOrderFwxmDao.select()
                    .eq(OmsOrderFwxmEntity::getPreNo, preNo).list();
            fwxmEntities.forEach(fwxm -> {
                String fwxmCode = fwxm.getFwxmCode();
                if (!Zsr.String.IsNullOrWhiteSpace(fwxmCode) && !listFwxmCode.contains(fwxmCode)) {
                    listFwxmCode.add(fwxmCode);
                }
            });
            // 服务类型，已保存的
            result.push("fwlxSelected", fwlxCodeNameList);

            if (listFwxmCode.size() == 0) {
                listFwxmCode.add("300200014");
                listFwxmCode.add("300200001");
                listFwxmCode.add("300100600910");
            }
            // 服务项目，已保存的
            result.push("fwxmSelected", listFwxmCode);
        }

        //获取服务类型
        List<FzgjBdServiceTypeEntity> fzgjBdServiceTypeComEntities = this.FzgjBdServiceTypeComSearch();

        // 获取所有服务项目配置页面URL TODO:暂时取消
        List<FzgjBdServiceItemPagesEntity> listFwxmPage = this.OrderFwxmPageSearch("");

        //获取平台所有服务项目
        List<FzgjBdServiceItemPtEntity> fzgjBdServiceItemPtEntityList = this.getServiceItemPt();

        List<TreeModel> treeModels = fzgjBdServiceItemService.selectTree(UserContext.getUserInfo().getCompanyCode());
        List<FzgjBdServiceItemEntity> fzgjBdServiceItemEntities = getDefaultCheck(treeModels);

        OmsHandlerTreeBuilder.TreeResult resultTree =
                OmsHandlerTreeBuilder.buildTreeWithParentMap(fzgjBdServiceTypeComEntities,
                        fzgjBdServiceItemPtEntityList,
                        fzgjBdServiceItemEntities, listFwxmPage);

        // 获取树结构用于展示
        List<TreeNode> treeNodes = resultTree.getTree();
        result.push("TREE_NODE", treeNodes);

        return result;
    }


    /**
     * 已经授权过的服务项目
     * @param types
     * @param services
     * @return
     */
    /**
     * 主方法，获取默认勾选的服务项
     */
    public List<FzgjBdServiceTypeComEntity> getDefaultCheckType(List<TreeModel> services) {
        // 1. 先对输入的列表进行空值检查，这是一个好习惯
        if (services == null || services.isEmpty()) {
            return Collections.emptyList(); // 返回一个不可变的空列表
        }

        // 2. 使用 stream 进行处理
        List<FzgjBdServiceItemEntity> collect = services.stream()
                // 过滤 null 的 TreeModel 对象，防止后续 p.getXXX() 抛出空指针
                .filter(Objects::nonNull)
                // 过滤出 orgChecked == 1 的项
                .filter(p -> p.getOrgChecked() == 0) // 获取 orgChecked 属性的方法
                // 将 TreeModel 对象映射为 FzgjBdServiceItemEntity 对象
                .map(this::convertTreeModelToEntity)
                .collect(Collectors.toList());

        List<FzgjBdServiceTypeComEntity> collect1 = collect.stream().map(p -> {
            FzgjBdServiceTypeComEntity entity = new FzgjBdServiceTypeComEntity();
            entity.setGuid(p.getGuid());
            entity.setCode(p.getCode());
            entity.setName(p.getName());
            return entity;
        }).collect(Collectors.toList());
        return collect1;
    }


    /**
     * 已经授权过的服务项目
     * @param types
     * @param services
     * @return
     */
    /**
     * 主方法，获取默认勾选的服务项
     */
    public List<FzgjBdServiceItemEntity> getDefaultCheck(List<TreeModel> services) {
        // 1. 先对输入的列表进行空值检查，这是一个好习惯
        if (services == null || services.isEmpty()) {
            return Collections.emptyList(); // 返回一个不可变的空列表
        }

        // 2. 使用 stream 进行处理
        return services.stream()
                // 过滤 null 的 TreeModel 对象，防止后续 p.getXXX() 抛出空指针
                .filter(Objects::nonNull)
                // 过滤出 orgChecked == 1 的项
                .filter(p -> p.getOrgChecked() == 1) // 获取 orgChecked 属性的方法
                // 将 TreeModel 对象映射为 FzgjBdServiceItemEntity 对象
                .map(this::convertTreeModelToEntity)
                .collect(Collectors.toList());
    }

    /**
     * 辅助方法：将 TreeModel 安全地转换为 FzgjBdServiceItemEntity
     * 这个方法负责处理所有的转换逻辑和潜在的异常
     *
     * @param p TreeModel 对象
     * @return 转换后的 FzgjBdServiceItemEntity 对象
     */
    private FzgjBdServiceItemEntity convertTreeModelToEntity(TreeModel p) {
        FzgjBdServiceItemEntity entity = new FzgjBdServiceItemEntity();

        entity.setGuid(p.getId());
        entity.setCode(p.getCode());
        entity.setName(p.getLabel());
        entity.setParentid(p.getParentid());
        entity.setOwnedService(p.getOwnedService());

        // --- 关键部分：安全地设置 seq 属性 ---
        // 假设当 getSeq() 返回 null、空字符串或无效数字时，我们希望将 seq 设置为 0.0f
        Float defaultSeq = 0.0f;

        // 使用 Optional 来优雅地处理
        Float seqValue = Optional.ofNullable(p.getSeq()) // 1. 允许 p.getSeq() 为 null
                .flatMap(this::parseFloatSafely)     // 2. 尝试将值安全地转换为 Float
                .orElse(defaultSeq);                  // 3. 如果失败或为空，则使用默认值

        entity.setSeq(seqValue);

        return entity;
    }

    /**
     * 辅助方法：安全地将字符串解析为 Float
     *
     * @param value 可能为 null 或非数字的字符串
     * @return 如果成功则返回包含 Float 的 Optional，否则返回空的 Optional
     */
    private Optional<Float> parseFloatSafely(String value) {
        if (value == null || value.trim().isEmpty()) {
            return Optional.empty();
        }
        try {
            return Optional.of(Float.valueOf(value));
        } catch (NumberFormatException e) {
            // 在生产环境中，使用日志框架记录错误会更好
            return Optional.empty();
        }
    }


    public static Optional<Float> toFloat(String value) {
        if (value == null || value.trim().isEmpty()) {
            return Optional.empty();
        }
        try {
            return Optional.of(Float.valueOf(value));
        } catch (NumberFormatException e) {
            return Optional.empty();
        }
    }

    @Autowired
    private FzgjBdServiceItemPtDao fzgjBdServiceItemPtDao;

    /**
     * 获取平台所有服务项目
     */
    private List<FzgjBdServiceItemPtEntity> getServiceItemPt() {

        return fzgjBdServiceItemPtDao.select()
                .eq(FzgjBdServiceItemPtEntity::getStatus, Enums.YNStatus.Y.getCode())
                .list();
    }

    /**
     * 获取所有服务项目
     */
    private List<FzgjBdServiceItemEntity> OrderFwSearchSource() {
        return fzgjBdServiceItemDao.select()
                .eq(FzgjBdServiceItemEntity::getGroupCode, UserContext.getUserInfo().getCompanyCode())
                .eq(FzgjBdServiceItemEntity::getStatus, Enums.YNStatus.Y.getCode())
                .eq(FzgjBdServiceItemEntity::getIsZyNoJd, Enums.YNStatus.N.getCode())
                .list();
    }


    /**
     * 服务项目配置 DAO
     */
    @Autowired
    private FzgjBdServiceItemDao fzgjBdServiceItemDao;


    /**
     * 页面配置 DAO
     */
    @Autowired
    private FzgjBdServiceItemPagesDao fzgjBdServiceItemPagesDao;

    /**
     * 获取所有服务项目配置页面URL
     *
     * @param orderFwxm
     * @return
     */
    private List<FzgjBdServiceItemPagesEntity> OrderFwxmPageSearch(String orderFwxm) {

        if (!Zsr.String.IsNullOrWhiteSpace(orderFwxm)) {
            return fzgjBdServiceItemPagesDao.OrderFwxmPageSearch(UserContext.getUserInfo().getCompanyCode(), Zsr.String.toInCondition(orderFwxm));
        } else {
            return fzgjBdServiceItemPagesDao.select()
//                    .eq(FzgjBdServiceItemPagesEntity::getGroupCode, UserContext.getUserInfo().getCompanyCode())
                    .eq(FzgjBdServiceItemPagesEntity::getStatus, Enums.YNStatus.Y.getCode())
                    .list();
        }
    }

    @Autowired
    private FzgjBdServiceTypeDao fzgjBdServiceTypeDao;

    /**
     * 获取基础服务类型
     *
     * @return
     */
    private List<FzgjBdServiceTypeEntity> FzgjBdServiceTypeSearch(List<String> codeList) {
        return fzgjBdServiceTypeDao.select()
                .in(FzgjBdServiceTypeEntity::getCode, codeList)
                .list();
    }

    /**
     * 获取服务类型
     *
     * @return
     */
    private List<FzgjBdServiceTypeEntity> FzgjBdServiceTypeComSearch() {
        // 1、先查询当前企业已授权的服务类型
        List<FzgjBdServiceTypeComEntity> fzgjBdServiceTypeComEntities = fzgjBdServiceTypeComDao.select()
                .eq(FzgjBdServiceTypeComEntity::getStatus, Enums.YNStatus.Y.getCode())
                .eq(FzgjBdServiceTypeComEntity::getGroupCode, UserContext.getUserInfo().getCompanyCode())
                .list();
        List<String> typeCodeList = fzgjBdServiceTypeComEntities.stream().map(p -> p.getCode()).collect(Collectors.toList());
        // 把授权的服务类型和平台的基础服务类型挂钩，得到正确的code和name
        List<FzgjBdServiceTypeEntity> fzgjBdServiceTypeEntities = FzgjBdServiceTypeSearch(typeCodeList);
        return fzgjBdServiceTypeEntities;

    }


    @Autowired
    private CrmCustomerKhDao crmCustomerKhDao;

    @Autowired
    private OmsOrderLogService omsOrderLogService;


    /**
     * 确认接单/批量接单
     *
     * @param listOrderNo  订单号
     * @param cancelRemark 取消原因
     */
    public void OrderStatusSave(OrderEnum.OrderOpType type, List<String> listOrderNo, String cancelRemark) {
        // 参数预处理
        if (listOrderNo == null || listOrderNo.isEmpty()) {
            throw new BaseException("订单编号列表不能为空！");
        }

        // 过滤空字符串
        listOrderNo = listOrderNo.stream()
                .filter(s -> !Zsr.String.IsNullOrWhiteSpace(s))
                .collect(Collectors.toList());

        if (listOrderNo.isEmpty()) {
            throw new BaseException("过滤后无有效订单编号！");
        }

        // 查询订单数据
        List<OmsOrderEntity> listOrder = omsOrderDao.select()
                .in(OmsOrderEntity::getOrderNo, listOrderNo)
                .list();

        if (listOrder.size() != listOrderNo.size()) {
            throw new BaseException("存在不存在的订单，请检查订单编号！");
        }

        // 获取用户信息
        UserInfo userInfo = UserContext.getUserInfo();
        if (userInfo == null) {
            throw new BaseException("用户信息为空，请重新登录！");
        }

        String companyCode = userInfo.getCompanyCode();
        String companyName = userInfo.getCompanyName();
        String userLoginNo = userInfo.getUserLoginNo();
        String userName = userInfo.getTrueName();

        // QRJD 特殊验证
        if (type.equals(OrderEnum.OrderOpType.QRJD)) {
            this.QrjdValidate(listOrderNo);
        }

        // FF 订单查询
        List<OmsOrderEntity> listFfOrder = this.GetQrjdFfOrder(listOrderNo);

        // 处理每个订单
        for (OmsOrderEntity order : listOrder) {
            if (!OrderEnum.OrderStatus.ZC.getCode().equals(order.getStatus())) {
                continue; // 跳过不满足条件的订单
            }

            switch (type) {
                case QRJD:
                    handleConfirmOrder(order, companyCode, companyName, userLoginNo, userName, listFfOrder);
                    break;
                case QXQRJD:
                    handleCancelConfirmOrder(order);
                    break;
                case ZF:
                case QXZF:
                case XZFF:
                case TD:
                case QXTD:
                case JA:
                    // TODO: 后续扩展其他操作
                    break;
                default:
                    throw new BaseException("不支持的操作类型：" + type);
            }

            omsOrderDao.updateByEntityId(order);
        }
    }

    private void handleConfirmOrder(OmsOrderEntity order, String companyCode, String companyName,
                                    String userLoginNo, String userName, List<OmsOrderEntity> listFfOrder) {

        // 检查客户是否冻结
        Optional.ofNullable(crmCustomerKhDao.select()
                        .eq(CrmCustomerKhEntity::getGroupCode, companyCode)
                        .eq(CrmCustomerKhEntity::getCompanyCode, companyCode)
                        .eq(CrmCustomerKhEntity::getCustomerCode, order.getConsigneeCode())
                        .one())
                .ifPresent(customer -> {
                    if ("Y".equals(customer.getJdFreeze())) {
                        throw new BaseException("该委托方已冻结接单功能，请在业务伙伴查看状态！");
                    }
                });

        boolean exists = listFfOrder.stream()
                .anyMatch(ff -> ff.getOrderNo().equals(order.getOrderNo()));

        if (exists) {
            order.setStage(OrderEnum.OrderStage.FF.getCode());
            order.setSendUser(userLoginNo);
            order.setSendUserName(userName);
            order.setSendDate(new Date());
            order.setSendNode(companyCode);
            order.setSendNodeName(companyName);
        } else {
            OrderWorkTraceReset(order.getOrderNo());
            order.setStage(OrderEnum.OrderStage.JD.getCode());
        }

        order.setStatus(OrderEnum.OrderStatus.SX.getCode());
        order.setConfirmDate(new Date());

        OmsOrderLogEntity logEntity = new OmsOrderLogEntity();
        logEntity.setOrderNo(order.getOrderNo());
        logEntity.setOperName("确认接单");
        omsOrderLogService.writeLog(logEntity);
    }


    private void handleCancelConfirmOrder(OmsOrderEntity order) {
        if (!OrderEnum.OrderStatus.SX.getCode().equals(order.getStatus())) {
            throw new BaseException("只有生效状态的订单才能进行取消接单操作！");
        }

        if (OrderEnum.OrderStage.FF.getCode().equals(order.getStage())) {
            throw new BaseException("已协作分发的单不能取消接单！");
        }

        order.setStatus(OrderEnum.OrderStatus.ZC.getCode());
        order.setStage("");
        order.setOpDate(new Date());
    }

    /**
     * 协助任务
     */
    @Autowired
    private OmsOrderFwxmWorkDao omsOrderFwxmWorkDao;

    /**
     * 报关报检数据
     */
    @Autowired
    private OmsOrderFwxmBgbjDao omsOrderFwxmBgbjDao;

    // 分发的逻辑
    private void sendFenFaDataTypes(OmsOrderEntity order, String trueName, String companyCode) {
        // 1、获取订单表头数据
        OmsOrderEntity orderEntity = omsOrderDao.select().eq(OmsOrderEntity::getOrderNo, order.getOrderNo()).one();

        // 2、获取协作任务数据
        List<OmsOrderFwxmWorkEntity> workList = omsOrderFwxmWorkDao.select()
                .eq(OmsOrderFwxmWorkEntity::getOrderNo, order.getOrderNo())
                .eq(OmsOrderFwxmWorkEntity::getGroupCode, companyCode)
                .list();

        // 3.报关报检数据
        List<OmsOrderFwxmBgbjEntity> bgbjList = omsOrderFwxmBgbjDao.select()
                .eq(OmsOrderFwxmBgbjEntity::getOrderNo, order.getOrderNo())
                .eq(OmsOrderFwxmBgbjEntity::getGroupCode, companyCode)
                .list();

    }


    @Autowired
    private OmsOrderFwxmWorkTraceDao omsOrderFwxmWorkTraceDao;
    @Autowired
    private FzgjTaskLimitationDao fzgjTaskLimitationDao;

    private void OrderWorkTraceReset(String orderNo) {
        List<OmsOrderFwxmWorkEntity> workList = omsOrderFwxmWorkDao.select()
                .eq(OmsOrderFwxmWorkEntity::getOrderNo, orderNo)
                .eq(OmsOrderFwxmWorkEntity::getGroupCode, UserContext.getUserInfo().getCompanyCode())
                .list();

        if (workList.size() > 0) {
            this.PlanOkDataReset(workList.stream().map(x -> x.getWorkNo()).collect(Collectors.toList()));
        }
    }

    /**
     * 作业环节计划完成时间自动计算
     *
     * @param workNo
     */
    public void PlanOkDataReset(List<String> workNo) {

        for (int i = 0; i < workNo.size(); i++) {
            OmsOrderFwxmWorkEntity work = omsOrderFwxmWorkDao.select().eq(OmsOrderFwxmWorkEntity::getWorkNo, workNo.get(i)).one();
            if (work == null) {
                throw new BaseException("协作任务不存在!");
            }

            List<OmsOrderFwxmWorkTraceEntity> traceList = omsOrderFwxmWorkTraceDao.select()
                    .eq(OmsOrderFwxmWorkTraceEntity::getWorkNo, workNo.get(i)).list();

//            if (traceList.size() == 0) {
//                throw new BaseException("协作任务环节不存在");
//            }

            traceList.forEach(item -> {
                FzgjTaskLimitationEntity limitation = fzgjTaskLimitationDao.select().eq(FzgjTaskLimitationEntity::getCode, item.getLinkCode())
                        .eq(FzgjTaskLimitationEntity::getGroupCode, UserContext.getUserInfo().getCompanyCode())
                        .eq(FzgjTaskLimitationEntity::getTargetCode, work.getFwxmCode()).one();

                if (limitation != null) {
                    Date okDate = this.RuleFixedValidate(limitation.getGuid(), item.getOrderNo());

                    if (okDate != null) {
                        item.setPlanOkDate(okDate);
                        omsOrderFwxmWorkTraceDao.updateByEntityId(item);
                    }
                }
            });

        }
    }

    @Autowired
    private FzgjTaskLimitationTimeDao FzgjTaskLimitationDao;


    @Autowired
    private FzgjTaskLimitationFixedDao fzgjTaskLimitationFixedDao;

    /// <summary>
///
/// </summary>
/// <param name="context"></param>
/// <param name="limitationGuid"></param>
    private Date RuleFixedValidate(String limitationGuid, String orderNo) {
        List<FzgjTaskLimitationTimeEntity> times = FzgjTaskLimitationDao.select()
                .eq(FzgjTaskLimitationTimeEntity::getLimitationGuid, limitationGuid)
                .orderBy(true, FzgjTaskLimitationTimeEntity::getSeq)
                .list();
        times.forEach(itm -> {
            boolean flag = false;


            List<FzgjTaskLimitationFixedEntity> fixeds = fzgjTaskLimitationFixedDao.select().eq(FzgjTaskLimitationFixedEntity::getLimitationTimeGuid, itm.getGuid()
            ).list();

            List<String> lsLineNo = fixeds.stream().map(x -> x.getLineNo()).distinct().sorted().collect(Collectors.toList());//  .Distinct().OrderBy(x = > x).ToList();

            //校验每一组条件
            lsLineNo.forEach(no -> {
//                List<ConditionExpression> _lsRuleFixedCon = (
//                        from item in fixeds
//                where item.LINE_NO == no
//                select new ConditionExpression
//                {
//                    ELEMENT_CODE = item.ELEMENT_CODE,
//                            ELEMENAT_CODE_NAME = item.ELEMENT_CODE_NAME,
//                            LOGICAL = item.LOGICAL,
//                            ELEMENT_VALUE = item.ELEMENT_VALUE,
//                            ELEENT_VALUE_NAME = item.ELEMENT_VALUE_NAME
//                }
//                )
//
//            });
//            foreach(var no in lsLineNo)
//            {
//                List<ConditionExpression> _lsRuleFixedCon = (
//                        from item in fixeds
//                where item.LINE_NO == no
//                select new ConditionExpression
//                {
//                    ELEMENT_CODE = item.ELEMENT_CODE,
//                            ELEMENAT_CODE_NAME = item.ELEMENT_CODE_NAME,
//                            LOGICAL = item.LOGICAL,
//                            ELEMENT_VALUE = item.ELEMENT_VALUE,
//                            ELEENT_VALUE_NAME = item.ELEMENT_VALUE_NAME
//                }).ToList();
//
//                if (!ConditionCheck(context, _lsRuleFixedCon, orderNo)) {
//                    flag = false;
//                    break;
//                } else {
//                    flag = true;
//                }
//            }
//
//            if (flag) {
//                FZGJ_TASK_DATE date = FZGJ_TASK_DATE.DAL.Select().Where(x = > x.CODE = time.VALID_TIME_CODE).
//                SingleOrDefault(context.Transaction);
//                string sql = date.SQL.Replace("#ORDER_NO#", orderNo);
//                DataTable dt = DBHelper.GetDataTable(sql, context.Transaction);
//                if (dt.HasRow() && !dt.Rows[0][0].ToString().NullOrEmpty()) {
//                    return Convert.ToDateTime(dt.Rows[0][0]).AddMinutes(Convert.ToDouble(time.REACH_STANDDARD));
//                }
//                break;

            });

        });
        return null;
    }


    private void QrjdValidate(List<String> listOrderNo) {

        boolean exists = omsOrderDao.select()
                .ne(OmsOrderEntity::getStatus, OrderEnum.OrderStatus.ZC)
                .in(OmsOrderEntity::getStatus, (listOrderNo))
                .exists();

        if (exists) {
            throw new BaseException("只有暂存状态的订单才能进行接单操作！");
        }
//            List<string> listOrderNo = orderNos.ToList();
//            checkSql = $@" SELECT XL.* FROM OMS_ORDER_FWXM_TMS_XL_XL XL INNER JOIN OMS_ORDER O ON XL.ORDER_NO = O.ORDER_NO AND XL.GROUP_CODE = O.GROUP_CODE WHERE XL.ORDER_NO  IN {listOrderNo.ToInCondition()}";
//            List<ORDER_FWXM_TMS_XL_XL> listTxmXlXl = DBHelper.GetDataTable(checkSql, context.Transaction).ToListEntity<ORDER_FWXM_TMS_XL_XL>();
//
//            if (listTxmXlXl.Any())
//            {
//                checkSql = $@" SELECT 1 FROM OMS_ORDER_FWXM_TMS_XL_XL XL
//                INNER JOIN OMS_ORDER O ON XL.ORDER_NO = O.ORDER_NO AND XL.GROUP_CODE = O.GROUP_CODE
//                LEFT JOIN OMS_ORDER_FWXM_WORK W ON XL.WORK_NO = W.WORK_NO AND XL.GROUP_CODE = W.GROUP_CODE
//                WHERE XL.ORDER_NO  IN {listTxmXlXl.Select(s => s.ORDER_NO).ToList().ToInCondition()}
//                AND W.WORK_NO IS NULL";
//                if (DBHelper.Exists(checkSql, context.Transaction))
//                {
//                    throw new BaseException("请保存运输服务信息！");
//                }
//            }

    }

    /**
     * 确认接单查询没有协作任务的订单
     *
     * @param orderNos
     * @return
     */
    public List<OmsOrderEntity> GetQrjdFfOrder(List<String> orderNos) {
        List<OmsOrderEntity> list = new ArrayList<>();

        if (orderNos == null || orderNos.isEmpty()) { // 使用 isEmpty() 检查列表是否为空
            return list;
        }

        // 1. 构建 SQL 查询字符串，使用占位符 '?'
        // 为列表中的每个元素生成一个 '?' 占位符
        // 例如：如果 orderNos 有 3 个元素，则生成 "(?, ?, ?)"
        String placeholders = orderNos.stream()
                .map(o -> "?")
                .collect(Collectors.joining(", "));

        String querySql = "SELECT O.* FROM OMS_ORDER O WHERE NOT EXISTS " +
                "(SELECT 1 FROM OMS_ORDER_FWXM_WORK W WHERE W.GROUP_CODE = O.GROUP_CODE AND W.ORDER_NO = O.ORDER_NO) " +
                "AND O.ORDER_NO IN (" + placeholders + ")"; // 将占位符字符串拼接到 IN 条件中

        //  String querySql = "SELECT O.* FROM OMS_ORDER O WHERE NOT EXISTS (SELECT 1 FROM OMS_ORDER_FWXM_WORK W WHERE W.GROUP_CODE = O.GROUP_CODE  AND W.ORDER_NO = O.ORDER_NO) AND O.ORDER_NO IN {orderNos.ToList().ToInCondition()}";
        return DBHelper.selectList(querySql, OmsOrderEntity.class, orderNos);
    }


    @Override
    public TgPageInfo queryPageList(OmsOrderEntity entity) {
        EciQuery<OmsOrderEntity> eciQuery = EciQuery.buildQuery(entity);
        List<OmsOrderEntity> entities = omsOrderDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderEntity save(OmsOrderEntity entity) {
        // 返回实体对象
        OmsOrderEntity omsOrderEntity = null;
        omsOrderVal.saveValidate(entity, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            omsOrderEntity = omsOrderDao.insertOne(entity);

        } else {

            omsOrderEntity = omsOrderDao.updateByEntityId(entity);

        }
        return omsOrderEntity;
    }

    @Override
    public List<OmsOrderEntity> selectList(OmsOrderEntity entity) {
        return omsOrderDao.selectList(entity);
    }

    @Override
    public OmsOrderEntity selectOneById(Serializable id) {
        return omsOrderDao.selectById(id);
    }


    @Override
    public void insertBatch(List<OmsOrderEntity> list) {
        omsOrderDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return omsOrderDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return omsOrderDao.deleteById(id);
    }


    /**
     * 获取订单信息
     **/
    public List<EntityBase> getOrder(String preNo, String bizRegId, String sysDocNo) {
        String companyCode = UserContext.getUserInfo().getCompanyCode();
        String groupCode = UserContext.getUserInfo().getCompanyCode();

        String sql = " SELECT A.* FROM OMS_ORDER A WHERE A.PRE_NO IN (SELECT P.PRE_NO FROM OMS_ORDER_PRE P WHERE ( ( P.PRE_NO !=" + cmn.SQLQ(preNo) + " AND P.SYS_CODE = 'OMS') OR (P.PRE_NO = " + cmn.SQLQ(preNo) + "AND P.SYS_CODE !='OMS'))  AND P.SYS_DOC_NO = " + cmn.SQLQ(sysDocNo) + " AND P.BIZ_REG_ID = " + cmn.SQLQ(bizRegId) + " AND P.COMPANY_CODE =" + cmn.SQLQ(companyCode) + " AND P.GROUP_CODE =" + cmn.SQLQ(groupCode) + " ) AND A.COMPANY_CODE =" + cmn.SQLQ(companyCode) + " AND A.GROUP_CODE =" + cmn.SQLQ(groupCode) + " AND A.STATUS !=" + cmn.SQLQ(Enums.OrderStatus.ZF.getCode()) + "";

        List<EntityBase> list = DBHelper.selectList(sql, EntityBase.class);
        return list;
    }

    /**
     *
     */
    public OmsOrderEntity save(OmsOrderEntity requestOrder, boolean isAdd, String saveType, String batchNo) {

        if (requestOrder == null) {
            throw new BaseException("传入参数为空！");
        }

        orderParamToCode(requestOrder);
        omsOrderVal.SaveValidate(requestOrder, isAdd, saveType);

        if (isAdd) {
            OmsOrderEntity order = new OmsOrderEntity();
            BeanUtils.copyProperties(requestOrder, order);

            order.setJdUser(DataExtend.toCode(order.getJdUser()));
            order.setFwlxName(omsOrderFwService.GetName(order.getFwlxCode()));
            order.setIsQrjd(StringUtils.isNull(order.getIsQrjd()) ? Enums.YNStatus.N.getCode() : order.getIsQrjd());
            order.setIsXzff(StringUtils.isNull(order.getIsXzff()) ? Enums.YNStatus.N.getCode() : order.getIsXzff());
            initOrder(order, saveType);
            order.setIsAutoAr(StringUtils.isNull(order.getIsAutoAr()) ? Enums.YNStatus.N.getCode() : order.getIsAutoAr());

            // 批次号  顺序号
            order.setSysOrderBatch(batchNo);
            if (StringUtils.hasValue(batchNo)) {
                order.setSeqBatch(getSeqBatchBybatchNo(batchNo));
            }

            initOrder(order, saveType);

            if (saveType.equals(OrderEnum.OrderSaveType.SAVETEMPLATE)) {

                OmsOrderJdmbEntity omsOrderJdmbEntity = new OmsOrderJdmbEntity();
                BeanUtils.copyProperties(requestOrder, omsOrderJdmbEntity);

                OmsOrderJdmbEntity orderMb = omsOrderJdmbService.save(omsOrderJdmbEntity, isAdd);
                order.setStatus(null);// 状态置为空，订单查询时 不查询出模板订单数据。
                order.setOrderNo(orderMb.getOrderNo());  // 保存模板时，订单号字段存 模板编号。
                order.setSysOrderBatch(null);

                // region T029188 在订单中点击“生成接单模板”时不带必填勾选项（确认接单必填项和协作分发必填项）；
                order.setIsQrjd(Enums.YNStatus.N.getCode());
                order.setIsXzff(Enums.YNStatus.N.getCode());

                order.setPreOrderNo(null);
                order.setUdf1(null);
                // #endregion
            } else {
                order.setOrderNo(NoManager.createOrderNo(""));

                // 需求 R011031
                if (StringUtils.hasValue(order.getPreNo())) {
                    QueryWrapper queryWrapper = new QueryWrapper();
                    queryWrapper.eq("PRE_NO", order.getPreNo());
                    queryWrapper.eq("SYS_CODE", "OMS");
                    List<OmsOrderPreEntity> preEntt = omsOrderPreDao.selectList(queryWrapper);

                    if (preEntt != null && preEntt.size() > 0) {
                        OmsOrderPreEntity rows_pre = preEntt.get(0);
                        order.setPreOrderNo(rows_pre.getSysDocNo());

                        QueryWrapper queryWrapper_order = new QueryWrapper();
                        queryWrapper_order.eq("PRE_NO", rows_pre.getSysDocNo());
                        List<OmsOrderEntity> orderEntt = omsOrderDao.selectList(queryWrapper_order);
                        if (orderEntt != null && orderEntt.size() > 0) {
                            order.setUdf1(orderEntt.get(0).getUdf1() + "." + order.getOrderNo());
                        }
                    } else {
                        order.setUdf1(order.getOrderNo());
                    }

                } else {
                    order.setUdf1(order.getOrderNo());
                }
            }

            order.setCreateDate(new java.util.Date());
            order.setUpdateDate(new java.util.Date());
            order.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            order.setCreateUserName(UserContext.getUserInfo().getTrueName());
            order.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            order.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            order.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            order.setGroupName(UserContext.getUserInfo().getCompanyName());
            order.setNodeCode(UserContext.getUserInfo().getDeptCode());
            order.setNodeName(UserContext.getUserInfo().getDeptName());
            order.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            order.setCompanyName(UserContext.getUserInfo().getCompanyName());
            order = omsOrderDao.insertOne(order);

            return order;
        } else {

            return null;
        }
    }


    private void initOrder(OmsOrderEntity order, String saveType) {
        order.setDataOk(Enums.YNStatus.N.getCode());
        order.setDataOkDate(null);
        order.setArapOk(Enums.YNStatus.N.getCode());
        order.setArapOkDate(null);
        order.setOpCompleteOk(Enums.YNStatus.N.getCode());
        order.setOpCompleteOkDate(null);
        order.setApOk(Enums.YNStatus.N.getCode());
        order.setApOkDate(null);

        order.setStatus(Enums.OrderStatus.ZC.getCode());
        order.setStage(null);
        if (!OrderEnum.OrderSaveType.XTSH.equals(saveType)) {
            order.setPreNo(null);
            order.setBizRegId(null);
            // 保存订单模板  不写BIZ_REG_ID,协同订单审核需赋值为ORDER_PRE.BIZ_REG_ID已在调用方法前赋值。
            if (!OrderEnum.OrderSaveType.SAVETEMPLATE.equals(saveType)) {
                order.setBizRegId(IdWorker.get32UUID());
            }
        }

        order.setIsCancel(Enums.YNStatus.N.getCode());
        order.setCancelFlag(Enums.YNStatus.N.getCode());
        order.setCancelReason(null);
        order.setCancelDate(null);
        order.setCancelNode(null);
        order.setCancelNodeName(null);
        order.setCancelRemark(null);
        order.setCancelUser(null);
        order.setCancelUserName(null);

        order.setAccountMode(null);

        order.setAuditDate(null);
        order.setAuditMemo(null);
        order.setAuditStatus(null);
        order.setAuditNode(null);
        order.setAuditNodeName(null);
        order.setAuditUser(null);
        order.setAuditUserName(null);
        order.setConfirmDate(null);
        order.setConfirmUser(null);
    }

    private void orderParamToCode(OmsOrderEntity order) {
        order.setConsigneeCode(DataExtend.toCode(order.getConsigneeCode()));
        order.setShipper(DataExtend.toCode(order.getShipper()));
        order.setReceiver(DataExtend.toCode(order.getReceiver()));
        order.setCustomerBu(DataExtend.toCode(order.getCustomerBu()));
        order.setOpType(DataExtend.toCode(order.getOpType()));
        order.setProductCode(DataExtend.toCode(order.getProductCode()));
        order.setFkfaCode(DataExtend.toCode(order.getFkfaCode()));
        order.setXzfaNo(DataExtend.toCode(order.getXzfaNo()));
        order.setJdUser(DataExtend.toCode(order.getJdUser()));
    }


    private String getSeqBatchBybatchNo(String batchNo) {

        String res = "";

        QueryWrapper query = new QueryWrapper();
        query.eq("SYS_ORDER_BATCH", batchNo);
        query.orderByDesc("SEQ_BATCH");
        List<OmsOrderEntity> orderDt = omsOrderDao.selectList(query);
        if (orderDt != null && orderDt.size() > 0) {
            res = (orderDt.get(0).getSeqBatch() + 1);
        } else {
            res = "1";
        }

        return res;
    }
}