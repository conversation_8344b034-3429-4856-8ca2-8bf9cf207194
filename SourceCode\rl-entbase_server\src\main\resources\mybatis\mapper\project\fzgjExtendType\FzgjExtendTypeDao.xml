<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.fzgjExtendType.dao.FzgjExtendTypeDao">
    <resultMap type="FzgjExtendTypeEntity" id="FzgjExtendTypeResult">
        <result property="guid" column="GUID"/>
        <result property="typeCode" column="TYPE_CODE"/>
        <result property="typeName" column="TYPE_NAME"/>
        <result property="status" column="STATUS"/>
        <result property="memo" column="MEMO"/>
        <result property="sysJsd" column="SYS_JSD"/>
    </resultMap>

    <sql id="selectFzgjExtendTypeEntityVo">
        select
            GUID,
            TYPE_CODE,
            TYPE_NAME,
            STATUS,
            MEMO,
            SYS_JSD
        from FZGJ_EXTEND_TYPE
    </sql>
</mapper>