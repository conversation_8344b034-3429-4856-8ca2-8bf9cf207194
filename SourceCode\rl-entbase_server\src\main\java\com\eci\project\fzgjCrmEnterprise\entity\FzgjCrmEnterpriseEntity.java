package com.eci.project.fzgjCrmEnterprise.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.common.DictField;
import com.eci.common.ZsrBaseEntity;
import com.eci.common.validations.ZsrValidation;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 注册企业对象 FZGJ_CRM_ENTERPRISE
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-03-13
*/
@ApiModel("注册企业")
@TableName("FZGJ_CRM_ENTERPRISE")
@FieldNameConstants
public class FzgjCrmEnterpriseEntity extends ZsrBaseEntity {
    /**
    * GUID
    */
    @ApiModelProperty("GUID(32)")
    @TableId("GUID")
    private String guid;

    /**
    * 客户代码
    */
    @ApiModelProperty("客户代码(20)")
    @TableField("CODE")
    @ZsrValidation(name = "客户代码", required = true, length = 20)
    private String code;

    /**
    * 客户名称
    */
    @ApiModelProperty("客户名称(100)")
    @TableField("NAME")
    @ZsrValidation(name = "客户名称", required = true, length = 100)
    private String name;

    /**
    * 客户简称
    */
    @ApiModelProperty("客户简称(50)")
    @TableField("SHORT_NAME")
    private String shortName;

    /**
    * 英文名称
    */
    @ApiModelProperty("英文名称(100)")
    @TableField("EN_NAME")
    private String enName;

    /**
    * 别称
    */
    @ApiModelProperty("别称(50)")
    @TableField("OTHER_NAME")
    private String otherName;

    /**
    * 海关代码
    */
    @ApiModelProperty("海关代码(10)")
    @TableField("CUSTOM_NO")
    private String customNo;

    /**
    * 地址
    */
    @ApiModelProperty("地址(80)")
    @TableField("ADDRESS")
    private String address;

    /**
    * 英文地址
    */
    @ApiModelProperty("英文地址(80)")
    @TableField("ADDRESS_EN")
    private String addressEn;

    /**
    * 联系人
    */
    @ApiModelProperty("联系人(10)")
    @TableField("PERSON")
    private String person;

    /**
    * 联系电话1
    */
    @ApiModelProperty("联系电话1(15)")
    @TableField("TEL1")
    private String tel1;

    /**
    * 联系人电话2
    */
    @ApiModelProperty("联系人电话2(15)")
    @TableField("TEL2")
    private String tel2;

    /**
    * 传真
    */
    @ApiModelProperty("传真(15)")
    @TableField("FAX")
    private String fax;

    /**
    * 邮箱
    */
    @ApiModelProperty("邮箱(40)")
    @TableField("EMAIL")
    private String email;

    /**
    * 邮编
    */
    @ApiModelProperty("邮编(8)")
    @TableField("ZIP")
    private String zip;

    /**
    * 国家
    */
    @ApiModelProperty("国家(10)")
    @TableField("COUNTRY")
    private String country;

    /**
    * 省/州
    */
    @ApiModelProperty("省/州(10)")
    @TableField("STATE")
    private String state;

    /**
    * 城市
    */
    @ApiModelProperty("城市(10)")
    @TableField("CITY")
    private String city;

    /**
    * 业务伙伴类型
    */
    @ApiModelProperty("业务伙伴类型(200)")
    @TableField("COMPANY_TYPE")
    private String companyType;

    /**
    * 业务伙伴类型
    */
    @ApiModelProperty("业务伙伴类型(300)")
    @TableField("COMPANY_TYPE_NAME")
    private String companyTypeName;

    /**
    * 企业B2B唯一码
    */
    @ApiModelProperty("企业B2B唯一码(40)")
    @TableField("CUSTOMER_B2B")
    private String customerB2b;

    /**
    * 税号
    */
    @ApiModelProperty("税号(30)")
    @TableField("TAX")
    private String tax;

    /**
    * 登录页logo
    */
    @ApiModelProperty("登录页logo(200)")
    @TableField("LOGO")
    private String logo;

    /**
    * 平台名称
    */
    @ApiModelProperty("平台名称(200)")
    @TableField("APPNAME")
    private String appname;

    /**
    * 截至使用日期
    */
    @ApiModelProperty("截至使用日期(7)")
    @TableField("END_USER_DATE")
    private Date endUserDate;

    @ApiModelProperty("截至使用日期开始")
    @TableField(exist=false)
    private Date endUserDateStart;

    @ApiModelProperty("截至使用日期结束")
    @TableField(exist=false)
    private Date endUserDateEnd;

    /**
    * 状态
    */
    @ApiModelProperty("状态(1)")
    @TableField("STATUS")
//    @DictField(data = {
//            "{'code':'Y','name':'是'}",
//            "{'code':'N','name':'否'}"
//    })
    @DictField(sql = "select 'Y' CODE, '是' NAME\n" +
            "  from dual\n" +
            "union all\n" +
            "select 'N' CODE, '否' NAME\n" +
            "  from dual")
    private String status;

    /**
    * 备注
    */
    @ApiModelProperty("备注(200)")
    @TableField("MEMO")
    private String memo;

    /**
    * CREATE_USER
    */
    @ApiModelProperty("CREATE_USER(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * CREATE_DATE
    */
    @ApiModelProperty("CREATE_DATE(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("CREATE_DATE开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("CREATE_DATE结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * UPDATE_USER
    */
    @ApiModelProperty("UPDATE_USER(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * UPDATE_DATE
    */
    @ApiModelProperty("UPDATE_DATE(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("UPDATE_DATE开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("UPDATE_DATE结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 编码规则前缀
    */
    @ApiModelProperty("编码规则前缀(50)")
    @TableField("STARTNO")
    private String startno;

    /**
    * 自动结算转入实际应收应付 Y: 是 N： 否
    */
    @ApiModelProperty("自动结算转入实际应收应付 Y: 是 N： 否(1)")
    @TableField("AUTO_ADD_TO_APAR")
    private String autoAddToApar;

    /**
    * CREATE_USER
    */
    @ApiModelProperty("CREATE_USER(200)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * UPDATE_USER
    */
    @ApiModelProperty("UPDATE_USER(200)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 数据归属集团代码
    */
    @ApiModelProperty("数据归属集团代码(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 数据归属集团名称
    */
    @ApiModelProperty("数据归属集团名称(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 税率
    */
    @ApiModelProperty("税率(22)")
    @TableField("TAX_RATE")
    private Float taxRate;

    /**
    * 发票类型
    */
    @ApiModelProperty("发票类型(20)")
    @TableField("INV_TYPE")
    private String invType;

    /**
    * 私有云系统地址
    */
    @ApiModelProperty("私有云系统地址(200)")
    @TableField("SYS_URL")
    private String sysUrl;

    /**
    * 客户端企业代码
    */
    @ApiModelProperty("客户端企业代码(50)")
    @TableField("CLIENT_COMPANY_CODE")
    private String clientCompanyCode;

    /**
    * 私有权限平台企业GUID
    */
    @ApiModelProperty("私有权限平台企业GUID(50)")
    @TableField("SSO_COMPANY_GUID")
    private String ssoCompanyGuid;

    /**
    * 操作页logo
    */
    @ApiModelProperty("操作页logo(200)")
    @TableField("LOGO_CZ")
    private String logoCz;

    /**
    * 登录页背景图
    */
    @ApiModelProperty("登录页背景图(200)")
    @TableField("GROUD_PIC")
    private String groudPic;

    /**
    * 企业隶属集团代码
    */
    @ApiModelProperty("企业隶属集团代码(50)")
    @TableField("LS_GROUP_CODE")
    private String lsGroupCode;

    /**
    * 企业隶属集团名称
    */
    @ApiModelProperty("企业隶属集团名称(200)")
    @TableField("LS_GROUP_NAME")
    private String lsGroupName;

    /**
    * 是否集团级业务伙伴
    */
    @ApiModelProperty("是否集团级业务伙伴(1)")
    @TableField("IS_GROUP")
    private String isGroup;

    /**
    * 统一社会信用代码
    */
    @ApiModelProperty("统一社会信用代码(50)")
    @TableField("TY_CODE")
    private String tyCode;

    /**
    * 平台发送邮箱地址
    */
    @ApiModelProperty("平台发送邮箱地址(200)")
    @TableField("SMTP_EMAIL")
    private String smtpEmail;

    /**
    * 平台发送邮箱用户名
    */
    @ApiModelProperty("平台发送邮箱用户名(100)")
    @TableField("SMTP_EMAIL_USER")
    private String smtpEmailUser;

    /**
    * 平台发送邮箱密码
    */
    @ApiModelProperty("平台发送邮箱密码(100)")
    @TableField("SMTP_EMAIL_PWD")
    private String smtpEmailPwd;

    /**
    * 平台发送邮箱服务器
    */
    @ApiModelProperty("平台发送邮箱服务器(100)")
    @TableField("SMTP_EMAIL_HOST")
    private String smtpEmailHost;

    /**
    * 平台发送邮箱服务器端口
    */
    @ApiModelProperty("平台发送邮箱服务器端口(10)")
    @TableField("SMTP_EMAIL_PORT")
    private String smtpEmailPort;

    /**
    * 仓库数限制
    */
    @ApiModelProperty("仓库数限制(22)")
    @TableField("WH_NUM")
    private Integer whNum;

    /**
    * 所属运行公司
    */
    @ApiModelProperty("所属运行公司(40)")
    @TableField("FCOMPANY_CODE")
    private String fcompanyCode;

    /**
    * 组织代码
    */
    @ApiModelProperty("组织代码(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 组织名称
    */
    @ApiModelProperty("组织名称(200)")
    @TableField("NODE_NAME")
    private String nodeName;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public FzgjCrmEnterpriseEntity() {
        this.setSubClazz(FzgjCrmEnterpriseEntity.class);
    }

    public FzgjCrmEnterpriseEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public FzgjCrmEnterpriseEntity setCode(String code) {
        this.code = code;
        this.nodifySetFiled("code", code);
        return this;
    }

    public String getCode() {
        this.nodifyGetFiled("code");
        return code;
    }

    public FzgjCrmEnterpriseEntity setName(String name) {
        this.name = name;
        this.nodifySetFiled("name", name);
        return this;
    }

    public String getName() {
        this.nodifyGetFiled("name");
        return name;
    }

    public FzgjCrmEnterpriseEntity setShortName(String shortName) {
        this.shortName = shortName;
        this.nodifySetFiled("shortName", shortName);
        return this;
    }

    public String getShortName() {
        this.nodifyGetFiled("shortName");
        return shortName;
    }

    public FzgjCrmEnterpriseEntity setEnName(String enName) {
        this.enName = enName;
        this.nodifySetFiled("enName", enName);
        return this;
    }

    public String getEnName() {
        this.nodifyGetFiled("enName");
        return enName;
    }

    public FzgjCrmEnterpriseEntity setOtherName(String otherName) {
        this.otherName = otherName;
        this.nodifySetFiled("otherName", otherName);
        return this;
    }

    public String getOtherName() {
        this.nodifyGetFiled("otherName");
        return otherName;
    }

    public FzgjCrmEnterpriseEntity setCustomNo(String customNo) {
        this.customNo = customNo;
        this.nodifySetFiled("customNo", customNo);
        return this;
    }

    public String getCustomNo() {
        this.nodifyGetFiled("customNo");
        return customNo;
    }

    public FzgjCrmEnterpriseEntity setAddress(String address) {
        this.address = address;
        this.nodifySetFiled("address", address);
        return this;
    }

    public String getAddress() {
        this.nodifyGetFiled("address");
        return address;
    }

    public FzgjCrmEnterpriseEntity setAddressEn(String addressEn) {
        this.addressEn = addressEn;
        this.nodifySetFiled("addressEn", addressEn);
        return this;
    }

    public String getAddressEn() {
        this.nodifyGetFiled("addressEn");
        return addressEn;
    }

    public FzgjCrmEnterpriseEntity setPerson(String person) {
        this.person = person;
        this.nodifySetFiled("person", person);
        return this;
    }

    public String getPerson() {
        this.nodifyGetFiled("person");
        return person;
    }

    public FzgjCrmEnterpriseEntity setTel1(String tel1) {
        this.tel1 = tel1;
        this.nodifySetFiled("tel1", tel1);
        return this;
    }

    public String getTel1() {
        this.nodifyGetFiled("tel1");
        return tel1;
    }

    public FzgjCrmEnterpriseEntity setTel2(String tel2) {
        this.tel2 = tel2;
        this.nodifySetFiled("tel2", tel2);
        return this;
    }

    public String getTel2() {
        this.nodifyGetFiled("tel2");
        return tel2;
    }

    public FzgjCrmEnterpriseEntity setFax(String fax) {
        this.fax = fax;
        this.nodifySetFiled("fax", fax);
        return this;
    }

    public String getFax() {
        this.nodifyGetFiled("fax");
        return fax;
    }

    public FzgjCrmEnterpriseEntity setEmail(String email) {
        this.email = email;
        this.nodifySetFiled("email", email);
        return this;
    }

    public String getEmail() {
        this.nodifyGetFiled("email");
        return email;
    }

    public FzgjCrmEnterpriseEntity setZip(String zip) {
        this.zip = zip;
        this.nodifySetFiled("zip", zip);
        return this;
    }

    public String getZip() {
        this.nodifyGetFiled("zip");
        return zip;
    }

    public FzgjCrmEnterpriseEntity setCountry(String country) {
        this.country = country;
        this.nodifySetFiled("country", country);
        return this;
    }

    public String getCountry() {
        this.nodifyGetFiled("country");
        return country;
    }

    public FzgjCrmEnterpriseEntity setState(String state) {
        this.state = state;
        this.nodifySetFiled("state", state);
        return this;
    }

    public String getState() {
        this.nodifyGetFiled("state");
        return state;
    }

    public FzgjCrmEnterpriseEntity setCity(String city) {
        this.city = city;
        this.nodifySetFiled("city", city);
        return this;
    }

    public String getCity() {
        this.nodifyGetFiled("city");
        return city;
    }

    public FzgjCrmEnterpriseEntity setCompanyType(String companyType) {
        this.companyType = companyType;
        this.nodifySetFiled("companyType", companyType);
        return this;
    }

    public String getCompanyType() {
        this.nodifyGetFiled("companyType");
        return companyType;
    }

    public FzgjCrmEnterpriseEntity setCompanyTypeName(String companyTypeName) {
        this.companyTypeName = companyTypeName;
        this.nodifySetFiled("companyTypeName", companyTypeName);
        return this;
    }

    public String getCompanyTypeName() {
        this.nodifyGetFiled("companyTypeName");
        return companyTypeName;
    }

    public FzgjCrmEnterpriseEntity setCustomerB2b(String customerB2b) {
        this.customerB2b = customerB2b;
        this.nodifySetFiled("customerB2b", customerB2b);
        return this;
    }

    public String getCustomerB2b() {
        this.nodifyGetFiled("customerB2b");
        return customerB2b;
    }

    public FzgjCrmEnterpriseEntity setTax(String tax) {
        this.tax = tax;
        this.nodifySetFiled("tax", tax);
        return this;
    }

    public String getTax() {
        this.nodifyGetFiled("tax");
        return tax;
    }

    public FzgjCrmEnterpriseEntity setLogo(String logo) {
        this.logo = logo;
        this.nodifySetFiled("logo", logo);
        return this;
    }

    public String getLogo() {
        this.nodifyGetFiled("logo");
        return logo;
    }

    public FzgjCrmEnterpriseEntity setAppname(String appname) {
        this.appname = appname;
        this.nodifySetFiled("appname", appname);
        return this;
    }

    public String getAppname() {
        this.nodifyGetFiled("appname");
        return appname;
    }

    public FzgjCrmEnterpriseEntity setEndUserDate(Date endUserDate) {
        this.endUserDate = endUserDate;
        this.nodifySetFiled("endUserDate", endUserDate);
        return this;
    }

    public Date getEndUserDate() {
        this.nodifyGetFiled("endUserDate");
        return endUserDate;
    }

    public FzgjCrmEnterpriseEntity setEndUserDateStart(Date endUserDateStart) {
        this.endUserDateStart = endUserDateStart;
        this.nodifySetFiled("endUserDateStart", endUserDateStart);
        return this;
    }

    public Date getEndUserDateStart() {
        this.nodifyGetFiled("endUserDateStart");
        return endUserDateStart;
    }

    public FzgjCrmEnterpriseEntity setEndUserDateEnd(Date endUserDateEnd) {
        this.endUserDateEnd = endUserDateEnd;
        this.nodifySetFiled("endUserDateEnd", endUserDateEnd);
        return this;
    }

    public Date getEndUserDateEnd() {
        this.nodifyGetFiled("endUserDateEnd");
        return endUserDateEnd;
    }
    public FzgjCrmEnterpriseEntity setStatus(String status) {
        this.status = status;
        this.nodifySetFiled("status", status);
        return this;
    }

    public String getStatus() {
        this.nodifyGetFiled("status");
        return status;
    }

    public FzgjCrmEnterpriseEntity setMemo(String memo) {
        this.memo = memo;
        this.nodifySetFiled("memo", memo);
        return this;
    }

    public String getMemo() {
        this.nodifyGetFiled("memo");
        return memo;
    }

    public FzgjCrmEnterpriseEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public FzgjCrmEnterpriseEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public FzgjCrmEnterpriseEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public FzgjCrmEnterpriseEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public FzgjCrmEnterpriseEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public FzgjCrmEnterpriseEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public FzgjCrmEnterpriseEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public FzgjCrmEnterpriseEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public FzgjCrmEnterpriseEntity setStartno(String startno) {
        this.startno = startno;
        this.nodifySetFiled("startno", startno);
        return this;
    }

    public String getStartno() {
        this.nodifyGetFiled("startno");
        return startno;
    }

    public FzgjCrmEnterpriseEntity setAutoAddToApar(String autoAddToApar) {
        this.autoAddToApar = autoAddToApar;
        this.nodifySetFiled("autoAddToApar", autoAddToApar);
        return this;
    }

    public String getAutoAddToApar() {
        this.nodifyGetFiled("autoAddToApar");
        return autoAddToApar;
    }

    public FzgjCrmEnterpriseEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public FzgjCrmEnterpriseEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public FzgjCrmEnterpriseEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public FzgjCrmEnterpriseEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public FzgjCrmEnterpriseEntity setTaxRate(Float taxRate) {
        this.taxRate = taxRate;
        this.nodifySetFiled("taxRate", taxRate);
        return this;
    }

    public Float getTaxRate() {
        this.nodifyGetFiled("taxRate");
        return taxRate;
    }

    public FzgjCrmEnterpriseEntity setInvType(String invType) {
        this.invType = invType;
        this.nodifySetFiled("invType", invType);
        return this;
    }

    public String getInvType() {
        this.nodifyGetFiled("invType");
        return invType;
    }

    public FzgjCrmEnterpriseEntity setSysUrl(String sysUrl) {
        this.sysUrl = sysUrl;
        this.nodifySetFiled("sysUrl", sysUrl);
        return this;
    }

    public String getSysUrl() {
        this.nodifyGetFiled("sysUrl");
        return sysUrl;
    }

    public FzgjCrmEnterpriseEntity setClientCompanyCode(String clientCompanyCode) {
        this.clientCompanyCode = clientCompanyCode;
        this.nodifySetFiled("clientCompanyCode", clientCompanyCode);
        return this;
    }

    public String getClientCompanyCode() {
        this.nodifyGetFiled("clientCompanyCode");
        return clientCompanyCode;
    }

    public FzgjCrmEnterpriseEntity setSsoCompanyGuid(String ssoCompanyGuid) {
        this.ssoCompanyGuid = ssoCompanyGuid;
        this.nodifySetFiled("ssoCompanyGuid", ssoCompanyGuid);
        return this;
    }

    public String getSsoCompanyGuid() {
        this.nodifyGetFiled("ssoCompanyGuid");
        return ssoCompanyGuid;
    }

    public FzgjCrmEnterpriseEntity setLogoCz(String logoCz) {
        this.logoCz = logoCz;
        this.nodifySetFiled("logoCz", logoCz);
        return this;
    }

    public String getLogoCz() {
        this.nodifyGetFiled("logoCz");
        return logoCz;
    }

    public FzgjCrmEnterpriseEntity setGroudPic(String groudPic) {
        this.groudPic = groudPic;
        this.nodifySetFiled("groudPic", groudPic);
        return this;
    }

    public String getGroudPic() {
        this.nodifyGetFiled("groudPic");
        return groudPic;
    }

    public FzgjCrmEnterpriseEntity setLsGroupCode(String lsGroupCode) {
        this.lsGroupCode = lsGroupCode;
        this.nodifySetFiled("lsGroupCode", lsGroupCode);
        return this;
    }

    public String getLsGroupCode() {
        this.nodifyGetFiled("lsGroupCode");
        return lsGroupCode;
    }

    public FzgjCrmEnterpriseEntity setLsGroupName(String lsGroupName) {
        this.lsGroupName = lsGroupName;
        this.nodifySetFiled("lsGroupName", lsGroupName);
        return this;
    }

    public String getLsGroupName() {
        this.nodifyGetFiled("lsGroupName");
        return lsGroupName;
    }

    public FzgjCrmEnterpriseEntity setIsGroup(String isGroup) {
        this.isGroup = isGroup;
        this.nodifySetFiled("isGroup", isGroup);
        return this;
    }

    public String getIsGroup() {
        this.nodifyGetFiled("isGroup");
        return isGroup;
    }

    public FzgjCrmEnterpriseEntity setTyCode(String tyCode) {
        this.tyCode = tyCode;
        this.nodifySetFiled("tyCode", tyCode);
        return this;
    }

    public String getTyCode() {
        this.nodifyGetFiled("tyCode");
        return tyCode;
    }

    public FzgjCrmEnterpriseEntity setSmtpEmail(String smtpEmail) {
        this.smtpEmail = smtpEmail;
        this.nodifySetFiled("smtpEmail", smtpEmail);
        return this;
    }

    public String getSmtpEmail() {
        this.nodifyGetFiled("smtpEmail");
        return smtpEmail;
    }

    public FzgjCrmEnterpriseEntity setSmtpEmailUser(String smtpEmailUser) {
        this.smtpEmailUser = smtpEmailUser;
        this.nodifySetFiled("smtpEmailUser", smtpEmailUser);
        return this;
    }

    public String getSmtpEmailUser() {
        this.nodifyGetFiled("smtpEmailUser");
        return smtpEmailUser;
    }

    public FzgjCrmEnterpriseEntity setSmtpEmailPwd(String smtpEmailPwd) {
        this.smtpEmailPwd = smtpEmailPwd;
        this.nodifySetFiled("smtpEmailPwd", smtpEmailPwd);
        return this;
    }

    public String getSmtpEmailPwd() {
        this.nodifyGetFiled("smtpEmailPwd");
        return smtpEmailPwd;
    }

    public FzgjCrmEnterpriseEntity setSmtpEmailHost(String smtpEmailHost) {
        this.smtpEmailHost = smtpEmailHost;
        this.nodifySetFiled("smtpEmailHost", smtpEmailHost);
        return this;
    }

    public String getSmtpEmailHost() {
        this.nodifyGetFiled("smtpEmailHost");
        return smtpEmailHost;
    }

    public FzgjCrmEnterpriseEntity setSmtpEmailPort(String smtpEmailPort) {
        this.smtpEmailPort = smtpEmailPort;
        this.nodifySetFiled("smtpEmailPort", smtpEmailPort);
        return this;
    }

    public String getSmtpEmailPort() {
        this.nodifyGetFiled("smtpEmailPort");
        return smtpEmailPort;
    }

    public FzgjCrmEnterpriseEntity setWhNum(Integer whNum) {
        this.whNum = whNum;
        this.nodifySetFiled("whNum", whNum);
        return this;
    }

    public Integer getWhNum() {
        this.nodifyGetFiled("whNum");
        return whNum;
    }

    public FzgjCrmEnterpriseEntity setFcompanyCode(String fcompanyCode) {
        this.fcompanyCode = fcompanyCode;
        this.nodifySetFiled("fcompanyCode", fcompanyCode);
        return this;
    }

    public String getFcompanyCode() {
        this.nodifyGetFiled("fcompanyCode");
        return fcompanyCode;
    }

    public FzgjCrmEnterpriseEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public FzgjCrmEnterpriseEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

}
