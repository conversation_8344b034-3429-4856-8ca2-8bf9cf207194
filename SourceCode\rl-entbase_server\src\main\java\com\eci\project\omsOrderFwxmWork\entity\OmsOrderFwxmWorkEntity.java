package com.eci.project.omsOrderFwxmWork.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;

import java.util.Date;


/**
* 供方协作任务对象 OMS_ORDER_FWXM_WORK
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-04-21
*/
@ApiModel("供方协作任务")
@TableName("OMS_ORDER_FWXM_WORK")
@FieldNameConstants
public class OmsOrderFwxmWorkEntity extends EciBaseEntity{
    /**
    * 协作任务编号
    */
    @ApiModelProperty("协作任务编号(50)")
    @TableField("WORK_NO")
    private String workNo;

    /**
    * 订单号
    */
    @ApiModelProperty("订单号(36)")
    @TableField("ORDER_NO")
    private String orderNo;

    /**
    * 委托单编号
    */
    @ApiModelProperty("委托单编号(36)")
    @TableField("PRE_NO")
    private String preNo;

    /**
    * 协作服务项目
    */
    @ApiModelProperty("协作服务项目(20)")
    @TableField("FWXM_CODE")
    private String fwxmCode;

    /**
    * 协作任务序号
    */
    @ApiModelProperty("协作任务序号(22)")
    @TableField("TASK_SEQ")
    private Integer taskSeq;

    /**
    * 前序任务
    */
    @ApiModelProperty("前序任务(50)")
    @TableField("TASK_PRE")
    private String taskPre;

    /**
    * 是否外包
    */
    @ApiModelProperty("是否外包(1)")
    @TableField("IS_WB")
    private String isWb;

    /**
    * 供应商业务伙伴代码
    */
    @ApiModelProperty("供应商业务伙伴代码(50)")
    @TableField("GYS_CODE")
    private String gysCode;

    /**
    * 内部协作组织代码
    */
    @ApiModelProperty("内部协作组织代码(50)")
    @TableField("NODE_CODE_NB")
    private String nodeCodeNb;

    /**
    * 供应商报价单号
    */
    @ApiModelProperty("供应商报价单号(50)")
    @TableField("QUOTE_CODE")
    private String quoteCode;

    /**
    * 状态
    */
    @ApiModelProperty("状态(20)")
    @TableField("STATUS")
    private String status;

    /**
    * 阶段，去掉不用了
    */
    @ApiModelProperty("阶段，去掉不用了(20)")
    @TableField("STAGE")
    private String stage;

    /**
    * 作业系统(根据SYS_CODE 及fwxm_code从供应商合作服务表查询（SYS_CODE=CUSTOMER_CODE）FKZYSJFS 为PT，查询fzgj_bd_service_item表jd_bill,'own'为SYS_CODE)
    */
    @ApiModelProperty("作业系统(根据SYS_CODE 及fwxm_code从供应商合作服务表查询（SYS_CODE=CUSTOMER_CODE）FKZYSJFS 为PT，查询fzgj_bd_service_item表jd_bill,'own'为SYS_CODE)(10)")
    @TableField("SYS_CODE")
    private String sysCode;

    /**
    * 作业系统委托单据类型
    */
    @ApiModelProperty("作业系统委托单据类型(40)")
    @TableField("BILL_CODE")
    private String billCode;

    /**
    * 作业系统委托单据编号
    */
    @ApiModelProperty("作业系统委托单据编号(50)")
    @TableField("DOC_NO")
    private String docNo;

    /**
    * 供应商反馈作业数据反馈方式
    */
    @ApiModelProperty("供应商反馈作业数据反馈方式(20)")
    @TableField("RESPONSE_CODE")
    private String responseCode;

    /**
    * 分发时间
    */
    @ApiModelProperty("分发时间(7)")
    @TableField("SEND_DATE")
    private Date sendDate;

    @ApiModelProperty("分发时间开始")
    @TableField(exist=false)
    private Date sendDateStart;

    @ApiModelProperty("分发时间结束")
    @TableField(exist=false)
    private Date sendDateEnd;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 最后修改日期
    */
    @ApiModelProperty("最后修改日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("最后修改日期开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("最后修改日期结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(200)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * GUID
    */
    @ApiModelProperty("GUID(36)")
    @TableId("GUID")
    private String guid;

    /**
    * 发送标记
    */
    @ApiModelProperty("发送标记(1)")
    @TableField("SEND_FLAG")
    private String sendFlag;

    /**
    * 分发人/调度员
    */
    @ApiModelProperty("分发人/调度员(20)")
    @TableField("SEND_USER")
    private String sendUser;

    /**
    * 分发组织
    */
    @ApiModelProperty("分发组织(36)")
    @TableField("SEND_NODE_CODE")
    private String sendNodeCode;

    /**
    * 分发组织
    */
    @ApiModelProperty("分发组织(200)")
    @TableField("SEND_NODE_NAME")
    private String sendNodeName;

    /**
    * 分发人/调度员
    */
    @ApiModelProperty("分发人/调度员(50)")
    @TableField("SEND_USER_NAME")
    private String sendUserName;

    /**
    * 作业完成时间
    */
    @ApiModelProperty("作业完成时间(7)")
    @TableField("OP_COMPLETE_OK_DATE")
    private Date opCompleteOkDate;

    @ApiModelProperty("作业完成时间开始")
    @TableField(exist=false)
    private Date opCompleteOkDateStart;

    @ApiModelProperty("作业完成时间结束")
    @TableField(exist=false)
    private Date opCompleteOkDateEnd;

    /**
    * 作业完成标识
    */
    @ApiModelProperty("作业完成标识(1)")
    @TableField("OP_COMPLETE_OK")
    private String opCompleteOk;

    /**
    * 作业数据齐全时间
    */
    @ApiModelProperty("作业数据齐全时间(7)")
    @TableField("DATA_OK_DATE")
    private Date dataOkDate;

    @ApiModelProperty("作业数据齐全时间开始")
    @TableField(exist=false)
    private Date dataOkDateStart;

    @ApiModelProperty("作业数据齐全时间结束")
    @TableField(exist=false)
    private Date dataOkDateEnd;

    /**
    * 作业数据齐全标志（源数据齐全）
    */
    @ApiModelProperty("作业数据齐全标志（源数据齐全）(1)")
    @TableField("DATA_OK")
    private String dataOk;

    /**
    * 结算完成标记（应付齐全）
    */
    @ApiModelProperty("结算完成标记（应付齐全）(1)")
    @TableField("ARAP_OK")
    private String arapOk;

    /**
    * 结算完成日期
    */
    @ApiModelProperty("结算完成日期(7)")
    @TableField("ARAP_OK_DATE")
    private Date arapOkDate;

    @ApiModelProperty("结算完成日期开始")
    @TableField(exist=false)
    private Date arapOkDateStart;

    @ApiModelProperty("结算完成日期结束")
    @TableField(exist=false)
    private Date arapOkDateEnd;

    /**
    * 协作委托单编号
    */
    @ApiModelProperty("协作委托单编号(36)")
    @TableField("XZWT_NO")
    private String xzwtNo;

    /**
    * 服务类型代码
    */
    @ApiModelProperty("服务类型代码(20)")
    @TableField("FWLX_CODE")
    private String fwlxCode;

    /**
    * 业务数据唯一注册编号
    */
    @ApiModelProperty("业务数据唯一注册编号(50)")
    @TableField("BIZ_REG_ID")
    private String bizRegId;

    /**
    * 上级订单的协作任务编号
    */
    @ApiModelProperty("上级订单的协作任务编号(50)")
    @TableField("PRE_WORK_NO")
    private String preWorkNo;

    /**
    * 需应付结算
    */
    @ApiModelProperty("需应付结算(1)")
    @TableField("IS_AP")
    private String isAp;

    /**
    * 协作任务层级关系
    */
    @ApiModelProperty("协作任务层级关系(500)")
    @TableField("UDF1")
    private String udf1;

    /**
    * 是否有上游协作任务
    */
    @ApiModelProperty("是否有上游协作任务(100)")
    @TableField("UDF2")
    private String udf2;

    /**
    * 快捷系统表体更新标识
    */
    @ApiModelProperty("快捷系统表体更新标识(100)")
    @TableField("UDF3")
    private String udf3;

    /**
    * 快捷系统表头更新标识
    */
    @ApiModelProperty("快捷系统表头更新标识(100)")
    @TableField("UDF4")
    private String udf4;

    /**
    * 快捷系统单号
    */
    @ApiModelProperty("快捷系统单号(100)")
    @TableField("UDF5")
    private String udf5;

    /**
    * 是否发送快捷系统
    */
    @ApiModelProperty("是否发送快捷系统(50)")
    @TableField("UDF6")
    private String udf6;

    /**
    * UDF7
    */
    @ApiModelProperty("UDF7(50)")
    @TableField("UDF7")
    private String udf7;

    /**
    * UDF8
    */
    @ApiModelProperty("UDF8(20)")
    @TableField("UDF8")
    private String udf8;

    /**
    * UDF9
    */
    @ApiModelProperty("UDF9(7)")
    @TableField("UDF9")
    private Date udf9;

    @ApiModelProperty("UDF9开始")
    @TableField(exist=false)
    private Date udf9Start;

    @ApiModelProperty("UDF9结束")
    @TableField(exist=false)
    private Date udf9End;

    /**
    * UDF10
    */
    @ApiModelProperty("UDF10(7)")
    @TableField("UDF10")
    private Date udf10;

    @ApiModelProperty("UDF10开始")
    @TableField(exist=false)
    private Date udf10Start;

    @ApiModelProperty("UDF10结束")
    @TableField(exist=false)
    private Date udf10End;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public OmsOrderFwxmWorkEntity setWorkNo(String workNo) {
        this.workNo = workNo;
        return this;
    }

    public String getWorkNo() {
        return workNo;
    }

    public OmsOrderFwxmWorkEntity setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        return this;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public OmsOrderFwxmWorkEntity setPreNo(String preNo) {
        this.preNo = preNo;
        return this;
    }

    public String getPreNo() {
        return preNo;
    }

    public OmsOrderFwxmWorkEntity setFwxmCode(String fwxmCode) {
        this.fwxmCode = fwxmCode;
        return this;
    }

    public String getFwxmCode() {
        return fwxmCode;
    }

    public OmsOrderFwxmWorkEntity setTaskSeq(Integer taskSeq) {
        this.taskSeq = taskSeq;
        return this;
    }

    public Integer getTaskSeq() {
        return taskSeq;
    }

    public OmsOrderFwxmWorkEntity setTaskPre(String taskPre) {
        this.taskPre = taskPre;
        return this;
    }

    public String getTaskPre() {
        return taskPre;
    }

    public OmsOrderFwxmWorkEntity setIsWb(String isWb) {
        this.isWb = isWb;
        return this;
    }

    public String getIsWb() {
        return isWb;
    }

    public OmsOrderFwxmWorkEntity setGysCode(String gysCode) {
        this.gysCode = gysCode;
        return this;
    }

    public String getGysCode() {
        return gysCode;
    }

    public OmsOrderFwxmWorkEntity setNodeCodeNb(String nodeCodeNb) {
        this.nodeCodeNb = nodeCodeNb;
        return this;
    }

    public String getNodeCodeNb() {
        return nodeCodeNb;
    }

    public OmsOrderFwxmWorkEntity setQuoteCode(String quoteCode) {
        this.quoteCode = quoteCode;
        return this;
    }

    public String getQuoteCode() {
        return quoteCode;
    }

    public OmsOrderFwxmWorkEntity setStatus(String status) {
        this.status = status;
        return this;
    }

    public String getStatus() {
        return status;
    }

    public OmsOrderFwxmWorkEntity setStage(String stage) {
        this.stage = stage;
        return this;
    }

    public String getStage() {
        return stage;
    }

    public OmsOrderFwxmWorkEntity setSysCode(String sysCode) {
        this.sysCode = sysCode;
        return this;
    }

    public String getSysCode() {
        return sysCode;
    }

    public OmsOrderFwxmWorkEntity setBillCode(String billCode) {
        this.billCode = billCode;
        return this;
    }

    public String getBillCode() {
        return billCode;
    }

    public OmsOrderFwxmWorkEntity setDocNo(String docNo) {
        this.docNo = docNo;
        return this;
    }

    public String getDocNo() {
        return docNo;
    }

    public OmsOrderFwxmWorkEntity setResponseCode(String responseCode) {
        this.responseCode = responseCode;
        return this;
    }

    public String getResponseCode() {
        return responseCode;
    }

    public OmsOrderFwxmWorkEntity setSendDate(Date sendDate) {
        this.sendDate = sendDate;
        return this;
    }

    public Date getSendDate() {
        return sendDate;
    }

    public OmsOrderFwxmWorkEntity setSendDateStart(Date sendDateStart) {
        this.sendDateStart = sendDateStart;
        return this;
    }

    public Date getSendDateStart() {
        return sendDateStart;
    }

    public OmsOrderFwxmWorkEntity setSendDateEnd(Date sendDateEnd) {
        this.sendDateEnd = sendDateEnd;
        return this;
    }

    public Date getSendDateEnd() {
        return sendDateEnd;
    }
    public OmsOrderFwxmWorkEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        return this;
    }

    public String getCreateUser() {
        return createUser;
    }

    public OmsOrderFwxmWorkEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        return this;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public OmsOrderFwxmWorkEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        return this;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public OmsOrderFwxmWorkEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        return this;
    }

    public Date getCreateDateStart() {
        return createDateStart;
    }

    public OmsOrderFwxmWorkEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        return this;
    }

    public Date getCreateDateEnd() {
        return createDateEnd;
    }
    public OmsOrderFwxmWorkEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        return this;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public OmsOrderFwxmWorkEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        return this;
    }

    public String getUpdateUserName() {
        return updateUserName;
    }

    public OmsOrderFwxmWorkEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        return this;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public OmsOrderFwxmWorkEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        return this;
    }

    public Date getUpdateDateStart() {
        return updateDateStart;
    }

    public OmsOrderFwxmWorkEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        return this;
    }

    public Date getUpdateDateEnd() {
        return updateDateEnd;
    }
    public OmsOrderFwxmWorkEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        return this;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public OmsOrderFwxmWorkEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        return this;
    }

    public String getCompanyName() {
        return companyName;
    }

    public OmsOrderFwxmWorkEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        return this;
    }

    public String getNodeCode() {
        return nodeCode;
    }

    public OmsOrderFwxmWorkEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        return this;
    }

    public String getNodeName() {
        return nodeName;
    }

    public OmsOrderFwxmWorkEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        return this;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public OmsOrderFwxmWorkEntity setGroupName(String groupName) {
        this.groupName = groupName;
        return this;
    }

    public String getGroupName() {
        return groupName;
    }

    public OmsOrderFwxmWorkEntity setGuid(String guid) {
        this.guid = guid;
        return this;
    }

    public String getGuid() {
        return guid;
    }

    public OmsOrderFwxmWorkEntity setSendFlag(String sendFlag) {
        this.sendFlag = sendFlag;
        return this;
    }

    public String getSendFlag() {
        return sendFlag;
    }

    public OmsOrderFwxmWorkEntity setSendUser(String sendUser) {
        this.sendUser = sendUser;
        return this;
    }

    public String getSendUser() {
        return sendUser;
    }

    public OmsOrderFwxmWorkEntity setSendNodeCode(String sendNodeCode) {
        this.sendNodeCode = sendNodeCode;
        return this;
    }

    public String getSendNodeCode() {
        return sendNodeCode;
    }

    public OmsOrderFwxmWorkEntity setSendNodeName(String sendNodeName) {
        this.sendNodeName = sendNodeName;
        return this;
    }

    public String getSendNodeName() {
        return sendNodeName;
    }

    public OmsOrderFwxmWorkEntity setSendUserName(String sendUserName) {
        this.sendUserName = sendUserName;
        return this;
    }

    public String getSendUserName() {
        return sendUserName;
    }

    public OmsOrderFwxmWorkEntity setOpCompleteOkDate(Date opCompleteOkDate) {
        this.opCompleteOkDate = opCompleteOkDate;
        return this;
    }

    public Date getOpCompleteOkDate() {
        return opCompleteOkDate;
    }

    public OmsOrderFwxmWorkEntity setOpCompleteOkDateStart(Date opCompleteOkDateStart) {
        this.opCompleteOkDateStart = opCompleteOkDateStart;
        return this;
    }

    public Date getOpCompleteOkDateStart() {
        return opCompleteOkDateStart;
    }

    public OmsOrderFwxmWorkEntity setOpCompleteOkDateEnd(Date opCompleteOkDateEnd) {
        this.opCompleteOkDateEnd = opCompleteOkDateEnd;
        return this;
    }

    public Date getOpCompleteOkDateEnd() {
        return opCompleteOkDateEnd;
    }
    public OmsOrderFwxmWorkEntity setOpCompleteOk(String opCompleteOk) {
        this.opCompleteOk = opCompleteOk;
        return this;
    }

    public String getOpCompleteOk() {
        return opCompleteOk;
    }

    public OmsOrderFwxmWorkEntity setDataOkDate(Date dataOkDate) {
        this.dataOkDate = dataOkDate;
        return this;
    }

    public Date getDataOkDate() {
        return dataOkDate;
    }

    public OmsOrderFwxmWorkEntity setDataOkDateStart(Date dataOkDateStart) {
        this.dataOkDateStart = dataOkDateStart;
        return this;
    }

    public Date getDataOkDateStart() {
        return dataOkDateStart;
    }

    public OmsOrderFwxmWorkEntity setDataOkDateEnd(Date dataOkDateEnd) {
        this.dataOkDateEnd = dataOkDateEnd;
        return this;
    }

    public Date getDataOkDateEnd() {
        return dataOkDateEnd;
    }
    public OmsOrderFwxmWorkEntity setDataOk(String dataOk) {
        this.dataOk = dataOk;
        return this;
    }

    public String getDataOk() {
        return dataOk;
    }

    public OmsOrderFwxmWorkEntity setArapOk(String arapOk) {
        this.arapOk = arapOk;
        return this;
    }

    public String getArapOk() {
        return arapOk;
    }

    public OmsOrderFwxmWorkEntity setArapOkDate(Date arapOkDate) {
        this.arapOkDate = arapOkDate;
        return this;
    }

    public Date getArapOkDate() {
        return arapOkDate;
    }

    public OmsOrderFwxmWorkEntity setArapOkDateStart(Date arapOkDateStart) {
        this.arapOkDateStart = arapOkDateStart;
        return this;
    }

    public Date getArapOkDateStart() {
        return arapOkDateStart;
    }

    public OmsOrderFwxmWorkEntity setArapOkDateEnd(Date arapOkDateEnd) {
        this.arapOkDateEnd = arapOkDateEnd;
        return this;
    }

    public Date getArapOkDateEnd() {
        return arapOkDateEnd;
    }
    public OmsOrderFwxmWorkEntity setXzwtNo(String xzwtNo) {
        this.xzwtNo = xzwtNo;
        return this;
    }

    public String getXzwtNo() {
        return xzwtNo;
    }

    public OmsOrderFwxmWorkEntity setFwlxCode(String fwlxCode) {
        this.fwlxCode = fwlxCode;
        return this;
    }

    public String getFwlxCode() {
        return fwlxCode;
    }

    public OmsOrderFwxmWorkEntity setBizRegId(String bizRegId) {
        this.bizRegId = bizRegId;
        return this;
    }

    public String getBizRegId() {
        return bizRegId;
    }

    public OmsOrderFwxmWorkEntity setPreWorkNo(String preWorkNo) {
        this.preWorkNo = preWorkNo;
        return this;
    }

    public String getPreWorkNo() {
        return preWorkNo;
    }

    public OmsOrderFwxmWorkEntity setIsAp(String isAp) {
        this.isAp = isAp;
        return this;
    }

    public String getIsAp() {
        return isAp;
    }

    public OmsOrderFwxmWorkEntity setUdf1(String udf1) {
        this.udf1 = udf1;
        return this;
    }

    public String getUdf1() {
        return udf1;
    }

    public OmsOrderFwxmWorkEntity setUdf2(String udf2) {
        this.udf2 = udf2;
        return this;
    }

    public String getUdf2() {
        return udf2;
    }

    public OmsOrderFwxmWorkEntity setUdf3(String udf3) {
        this.udf3 = udf3;
        return this;
    }

    public String getUdf3() {
        return udf3;
    }

    public OmsOrderFwxmWorkEntity setUdf4(String udf4) {
        this.udf4 = udf4;
        return this;
    }

    public String getUdf4() {
        return udf4;
    }

    public OmsOrderFwxmWorkEntity setUdf5(String udf5) {
        this.udf5 = udf5;
        return this;
    }

    public String getUdf5() {
        return udf5;
    }

    public OmsOrderFwxmWorkEntity setUdf6(String udf6) {
        this.udf6 = udf6;
        return this;
    }

    public String getUdf6() {
        return udf6;
    }

    public OmsOrderFwxmWorkEntity setUdf7(String udf7) {
        this.udf7 = udf7;
        return this;
    }

    public String getUdf7() {
        return udf7;
    }

    public OmsOrderFwxmWorkEntity setUdf8(String udf8) {
        this.udf8 = udf8;
        return this;
    }

    public String getUdf8() {
        return udf8;
    }

    public OmsOrderFwxmWorkEntity setUdf9(Date udf9) {
        this.udf9 = udf9;
        return this;
    }

    public Date getUdf9() {
        return udf9;
    }

    public OmsOrderFwxmWorkEntity setUdf9Start(Date udf9Start) {
        this.udf9Start = udf9Start;
        return this;
    }

    public Date getUdf9Start() {
        return udf9Start;
    }

    public OmsOrderFwxmWorkEntity setUdf9End(Date udf9End) {
        this.udf9End = udf9End;
        return this;
    }

    public Date getUdf9End() {
        return udf9End;
    }
    public OmsOrderFwxmWorkEntity setUdf10(Date udf10) {
        this.udf10 = udf10;
        return this;
    }

    public Date getUdf10() {
        return udf10;
    }

    public OmsOrderFwxmWorkEntity setUdf10Start(Date udf10Start) {
        this.udf10Start = udf10Start;
        return this;
    }

    public Date getUdf10Start() {
        return udf10Start;
    }

    public OmsOrderFwxmWorkEntity setUdf10End(Date udf10End) {
        this.udf10End = udf10End;
        return this;
    }

    public Date getUdf10End() {
        return udf10End;
    }
}
