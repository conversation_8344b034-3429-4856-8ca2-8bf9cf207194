package com.eci.project.etmsBdTruckCertificateQz.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.etmsBdTruckCertificateQz.entity.EtmsBdTruckCertificateSearchEntity;
import com.eci.project.etmsBdTruckCertificateQz.service.EtmsBdTruckCertificateQzService;
import com.eci.project.etmsBdTruckCertificateQz.entity.EtmsBdTruckCertificateQzEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 证件管理Controller
*
* @<NAME_EMAIL>
* @date 2025-04-27
*/
@Api(tags = "证件管理")
@RestController
@RequestMapping("/etmsBdTruckCertificateQz")
public class EtmsBdTruckCertificateQzController extends EciBaseController {

    @Autowired
    private EtmsBdTruckCertificateQzService etmsBdTruckCertificateQzService;


    @ApiOperation("证件管理:保存")
    @EciLog(title = "证件管理:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody EtmsBdTruckCertificateQzEntity entity){
        EtmsBdTruckCertificateQzEntity etmsBdTruckCertificateQzEntity =etmsBdTruckCertificateQzService.save(entity);
        return ResponseMsgUtil.success(10001,etmsBdTruckCertificateQzEntity);
    }


    @ApiOperation("证件管理:查询列表")
    @EciLog(title = "证件管理:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody EtmsBdTruckCertificateQzEntity entity){
        List<EtmsBdTruckCertificateQzEntity> etmsBdTruckCertificateQzEntities = etmsBdTruckCertificateQzService.selectList(entity);
        return ResponseMsgUtil.success(10001,etmsBdTruckCertificateQzEntities);
    }


    @ApiOperation("证件管理:分页查询列表")
    @EciLog(title = "证件管理:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody EtmsBdTruckCertificateQzEntity entity){
        TgPageInfo tgPageInfo = etmsBdTruckCertificateQzService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("证件管理:根据ID查一条")
    @EciLog(title = "证件管理:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody EtmsBdTruckCertificateQzEntity entity){
        EtmsBdTruckCertificateQzEntity  etmsBdTruckCertificateQzEntity = etmsBdTruckCertificateQzService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,etmsBdTruckCertificateQzEntity);
    }


    @ApiOperation("证件管理:根据ID删除一条")
    @EciLog(title = "证件管理:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody EtmsBdTruckCertificateQzEntity entity){
        int count = etmsBdTruckCertificateQzService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("证件管理:根据ID字符串删除多条")
    @EciLog(title = "证件管理:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody EtmsBdTruckCertificateQzEntity entity) {
        int count = etmsBdTruckCertificateQzService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }

    @ApiOperation("车辆资质审核:车辆证件管理分页查询列表")
    @EciLog(title = "车辆资质审核:车辆证件管理分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectCertificatePageList")
    @EciAction()
    public ResponseMsg selectCertificatePageList(@RequestBody EtmsBdTruckCertificateSearchEntity entity){
        TgPageInfo tgPageInfo = etmsBdTruckCertificateQzService.queryCertificatePageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }
}