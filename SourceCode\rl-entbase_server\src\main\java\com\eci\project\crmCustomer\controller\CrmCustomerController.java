package com.eci.project.crmCustomer.controller;

import com.alibaba.fastjson.JSON;
import com.eci.cache.config.TgCacheHelper;
import com.eci.cache.handler.TgRedisCacheHandler;
import com.eci.common.util.*;
import com.eci.common.web.BllContext;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.crmCustomer.service.CrmCustomerService;
import com.eci.project.crmCustomer.entity.CrmCustomerEntity;
import com.eci.project.crmCustomerHead.entity.CrmCustomerHeadEntity;
import com.eci.project.crmCustomerHead.service.CrmCustomerHeadService;
import com.eci.project.crmCustomerHzfw.entity.CrmCustomerHzfwEntity;
import com.eci.project.crmCustomerHzfw.service.CrmCustomerHzfwService;
import com.eci.project.crmCustomerRole.entity.CrmCustomerRoleEntity;
import com.eci.project.crmCustomerRole.service.CrmCustomerRoleService;
import com.eci.project.etmsBdDriver.entity.EtmsBdDriverEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 业务伙伴Controller
*
* @<NAME_EMAIL>
* @date 2025-04-29
*/
@Api(tags = "业务伙伴")
@RestController
@RequestMapping("/crmCustomer")
public class CrmCustomerController extends EciBaseController {

    @Autowired
    private CrmCustomerService crmCustomerService;
    @Autowired
    private CrmCustomerHzfwService crmCustomerHzfwService;
    @Autowired
    private CrmCustomerHeadService crmCustomerHeadService;

    @Autowired
    private CrmCustomerRoleService crmCustomerRoleService;

    @ApiOperation("业务伙伴:保存")
    @EciLog(title = "业务伙伴:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody CrmCustomerEntity entity) throws Exception {
        if(crmCustomerService.tycodeExist(entity.getTyCode(),entity.getGuid())){
            throw new Exception("统一信用代码重复");
        }
        if(crmCustomerService.codeExist(entity.getCode(),entity.getGuid())){
            throw new Exception("企业代码重复");
        }
        if(crmCustomerService.Validate(entity.getName()
                ,entity.getGuid()
                ,"name")){
            throw new Exception("企业名称重复");
        }
        if(crmCustomerService.Validate(entity.getShortName()
                ,entity.getGuid()
                ,"shortName")){
            throw new Exception("企业简称重复");
        }
        if(crmCustomerService.Validate(entity.getCustomNo()
                ,entity.getGuid()
                ,"customNo")){
            throw new Exception("企业海关代码");
        }

        if(crmCustomerService.Validate(entity.getCustomerB2b()
                ,entity.getGuid()
                ,"customer_b2b")){
            throw new Exception("企业B2B唯一码");
        }
        if(BllContext.getBusinessType()==BusinessType.INSERT){
            entity.setManageStatus("0");
        }
        //当保存是更新操作时，同时状态是退回状态 自动将状态修改为暂存
        if(BllContext.getBusinessType()==BusinessType.UPDATE&&entity.getManageStatus().equals("-1")){
            entity.setManageStatus("0");
        }
        CrmCustomerHeadEntity head=JSON.parseObject(JSON.toJSONString(entity), CrmCustomerHeadEntity.class);
        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            CrmCustomerHzfwEntity hzfw= crmCustomerHzfwService.buildData(entity.getCode());//构造客户合作服务
            crmCustomerHzfwService.save(hzfw);
        }else{
            CrmCustomerHeadEntity temp=crmCustomerHeadService.selectOneByCode(entity.getCode());
            head.setGuid(temp.getGuid());
        }
        //存在角色code的时候，构造角色entity
        if(entity.getRoleCode()!=null&&!entity.getRoleCode().isEmpty()){
            List<CrmCustomerRoleEntity> roles= crmCustomerRoleService.BuildEntity(entity.getRoleCode()
                    , entity.getCode());
            crmCustomerRoleService.deleteByCustomerCode(entity.getCode());//删除掉关联的角色
            crmCustomerRoleService.insertBatch(roles);//新增角色
        }
        CrmCustomerEntity crmCustomerEntity =crmCustomerService.save(entity);
        head.setGuid(crmCustomerEntity.getGuid());
        crmCustomerHeadService.save(head);
        return ResponseMsgUtil.success(10001,crmCustomerEntity);
    }


    @ApiOperation("业务伙伴:查询列表")
    @EciLog(title = "业务伙伴:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody CrmCustomerEntity entity){
        List<CrmCustomerEntity> crmCustomerEntities = crmCustomerService.selectList(entity);
        return ResponseMsgUtil.success(10001,crmCustomerEntities);
    }


    @ApiOperation("业务伙伴:分页查询列表")
    @EciLog(title = "业务伙伴:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody CrmCustomerEntity entity){
        TgPageInfo tgPageInfo = crmCustomerService.queryPageList(entity);
        return ResponseMsgUtil.successPlus(10001,tgPageInfo);
    }


    @ApiOperation("业务伙伴:根据ID查一条")
    @EciLog(title = "业务伙伴:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody CrmCustomerEntity entity){
        CrmCustomerEntity  crmCustomerEntity = crmCustomerService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.successPlus(10001,crmCustomerEntity);
    }


    @ApiOperation("业务伙伴:根据ID删除一条")
    @EciLog(title = "业务伙伴:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody CrmCustomerEntity entity){
        int count = crmCustomerService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("业务伙伴:根据ID字符串删除多条")
    @EciLog(title = "业务伙伴:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody CrmCustomerEntity entity) {
        int count = crmCustomerService.deleteByIds(entity.getIds());
        crmCustomerHeadService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }
    @ApiOperation("业务伙伴:自动生成统一信用代码")
    @EciLog(title = "业务伙伴:自动生成统一信用代码", businessType = BusinessType.INSERT)
    @PostMapping("/AutoBuildCode")
    @EciAction()
    public ResponseMsg AutoBuildCode(@RequestBody CrmCustomerEntity entity){
        String code=crmCustomerService.AutoBuildCode(entity.getGuid());
        return ResponseMsgUtil.success(10001,code);
    }

    @ApiOperation("业务伙伴:验证统一信用代码")
    @EciLog(title = "业务伙伴:验证统一信用代码", businessType = BusinessType.INSERT)
    @PostMapping("/tycodeExist")
    @EciAction()
    public ResponseMsg tycodeExist(@RequestBody CrmCustomerEntity entity){
        boolean exist=crmCustomerService.tycodeExist(entity.getTyCode(),entity.getGuid());
        return ResponseMsgUtil.success(10001,exist);
    }

    @ApiOperation("业务伙伴:验证统一信用代码")
    @EciLog(title = "业务伙伴:验证统一信用代码", businessType = BusinessType.INSERT)
    @PostMapping("/codeExist")
    @EciAction()
    public ResponseMsg codeExist(@RequestBody CrmCustomerEntity entity){
        boolean exist=crmCustomerService.codeExist(entity.getCode(),entity.getGuid());
        return ResponseMsgUtil.success(10001,exist);
    }

    @ApiOperation("业务伙伴:验证统一信用代码")
    @EciLog(title = "业务伙伴:验证统一信用代码", businessType = BusinessType.INSERT)
    @PostMapping("/Validate")
    @EciAction()
    public ResponseMsg Validate(@RequestBody CrmCustomerEntity entity){
        boolean exist=crmCustomerService.Validate(entity.getRequestParams().get("value").toString()
                ,entity.getGuid()
                ,entity.getRequestParams().get("type").toString());
        return ResponseMsgUtil.success(10001,exist);
    }



    @ApiOperation("业务伙伴:送审")
    @EciLog(title = "业务伙伴:送审", businessType = BusinessType.DELETE)
    @PostMapping("/SendAudit")
    @EciAction()
    public ResponseMsg SendAudit(@RequestBody CrmCustomerEntity entity) {
        crmCustomerService.SendAudit(entity.getIds());
        return ResponseMsgUtil.success(10001);
    }

    @ApiOperation("业务伙伴:审批退回")
    @EciLog(title = "业务伙伴:审批退回", businessType = BusinessType.DELETE)
    @PostMapping("/AuditBack")
    @EciAction()
    public ResponseMsg AuditBack(@RequestBody CrmCustomerEntity entity) {
        crmCustomerService.AuditBack(entity.getIds(),entity.getAuditRemark());
        return ResponseMsgUtil.success(10001);
    }

    @ApiOperation("业务伙伴:审批通过")
    @EciLog(title = "业务伙伴:审批通过", businessType = BusinessType.DELETE)
    @PostMapping("/AuditSuccess")
    @EciAction()
    public ResponseMsg AuditSuccess(@RequestBody CrmCustomerEntity entity) {
        crmCustomerService.AuditSuccess(entity.getIds());
        return ResponseMsgUtil.success(10001);
    }
}