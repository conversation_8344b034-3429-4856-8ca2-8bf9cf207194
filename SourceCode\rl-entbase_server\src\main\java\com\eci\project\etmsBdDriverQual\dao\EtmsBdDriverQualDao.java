package com.eci.project.etmsBdDriverQual.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.eci.project.etmsBdDriver.entity.DriverInfo;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.etmsBdDriverQual.entity.EtmsBdDriverQualEntity;

import java.util.List;


/**
* 司机从业资格证Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-04-17
*/
public interface EtmsBdDriverQualDao extends EciBaseDao<EtmsBdDriverQualEntity> {
    List<EtmsBdDriverQualEntity> queryPages(@Param(Constants.WRAPPER) Wrapper queryWrapper);
}