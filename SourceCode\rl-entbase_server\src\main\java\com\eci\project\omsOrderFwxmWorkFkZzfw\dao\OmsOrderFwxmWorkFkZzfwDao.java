package com.eci.project.omsOrderFwxmWorkFkZzfw.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.omsOrderFwxmWorkFkZzfw.entity.OmsOrderFwxmWorkFkZzfwEntity;


/**
* 反馈内容-作业信息:其他增值服务信息Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-05-22
*/
public interface OmsOrderFwxmWorkFkZzfwDao extends EciBaseDao<OmsOrderFwxmWorkFkZzfwEntity> {

}