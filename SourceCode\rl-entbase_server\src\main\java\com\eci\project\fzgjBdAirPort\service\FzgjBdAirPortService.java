package com.eci.project.fzgjBdAirPort.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjBdAirPort.dao.FzgjBdAirPortDao;
import com.eci.project.fzgjBdAirPort.entity.FzgjBdAirPortEntity;
import com.eci.project.fzgjBdAirPort.validate.FzgjBdAirPortVal;

import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 空运港口Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-03-14
*/
@Service
@Slf4j
public class FzgjBdAirPortService implements EciBaseService<FzgjBdAirPortEntity> {

    @Autowired
    private FzgjBdAirPortDao fzgjBdAirPortDao;

    @Autowired
    private FzgjBdAirPortVal fzgjBdAirPortVal;


    @Override
    public TgPageInfo queryPageList(FzgjBdAirPortEntity entity) {
        EciQuery<FzgjBdAirPortEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjBdAirPortEntity> entities = fzgjBdAirPortDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjBdAirPortEntity save(FzgjBdAirPortEntity entity) {
        entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
        entity.setGroupName(UserContext.getUserInfo().getCompanyName());
        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setCreateDate(new java.util.Date());
            entity.setUpdateDate(new java.util.Date());
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        } else {
            entity.setUpdateDate(new java.util.Date());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        }
        // 返回实体对象
        FzgjBdAirPortEntity fzgjBdAirPortEntity = null;
        fzgjBdAirPortVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjBdAirPortEntity = fzgjBdAirPortDao.insertOne(entity);

        }else{

            fzgjBdAirPortEntity = fzgjBdAirPortDao.updateByEntityId(entity);

        }
        return fzgjBdAirPortEntity;
    }

    @Override
    public List<FzgjBdAirPortEntity> selectList(FzgjBdAirPortEntity entity) {
        return fzgjBdAirPortDao.selectList(entity);
    }

    @Override
    public FzgjBdAirPortEntity selectOneById(Serializable id) {
        return fzgjBdAirPortDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjBdAirPortEntity> list) {
        fzgjBdAirPortDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjBdAirPortDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjBdAirPortDao.deleteById(id);
    }

}