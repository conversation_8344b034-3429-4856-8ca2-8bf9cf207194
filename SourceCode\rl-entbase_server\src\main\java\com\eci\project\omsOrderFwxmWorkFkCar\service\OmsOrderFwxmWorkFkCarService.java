package com.eci.project.omsOrderFwxmWorkFkCar.service;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.DictFieldUtils;
import com.eci.common.Extensions;
import com.eci.common.ZsrBaseEntity;
import com.eci.common.ZsrJson;
import com.eci.common.db.DBHelper;
import com.eci.common.utils.CommonLib;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.exception.BaseException;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.omsOrderFwxmCrkZzfw.service.OmsOrderFwxmCrkZzfwService;
import com.eci.project.omsOrderFwxmWork.dao.OmsOrderFwxmWorkDao;
import com.eci.project.omsOrderFwxmWork.entity.OmsOrderFwxmWorkEntity;
import com.eci.project.omsOrderFwxmWorkFk.dao.OmsOrderFwxmWorkFkDao;
import com.eci.project.omsOrderFwxmWorkFk.entity.OmsOrderFwxmWorkFkEntity;
import com.eci.project.omsOrderFwxmWorkFkCar.dao.OmsOrderFwxmWorkFkCarDao;
import com.eci.project.omsOrderFwxmWorkFkCar.entity.OmsOrderFwxmWorkFkCarEntity;
import com.eci.project.omsOrderFwxmWorkFkCar.entity.OmsOrderFwxmWorkFkCarExdEntity;
import com.eci.project.omsOrderFwxmWorkFkCar.entity.ResponseFKCarEditEntity;
import com.eci.project.omsOrderFwxmWorkFkCar.validate.OmsOrderFwxmWorkFkCarVal;
import com.eci.project.omsOrderFwxmWorkFkZzfw.entity.OmsOrderFwxmWorkFkZzfwEntity;
import com.eci.project.omsOrderFwxmWorkFkZzfw.service.OmsOrderFwxmWorkFkZzfwService;
import com.eci.project.omsOrderFwxmWorkTrace.dao.OmsOrderFwxmWorkTraceDao;
import com.eci.project.omsOrderFwxmWorkTrace.entity.OmsOrderFwxmWorkTraceEntity;
import com.eci.sso.login.entity.UserInfo;
import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;


/**
 * 反馈内容-车辆Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-05-21
 */
@Service
@Slf4j
public class OmsOrderFwxmWorkFkCarService implements EciBaseService<OmsOrderFwxmWorkFkCarEntity> {

    CommonLib cmn = CommonLib.getInstance();

    @Autowired
    private OmsOrderFwxmWorkFkCarDao omsOrderFwxmWorkFkCarDao;

    @Autowired
    private OmsOrderFwxmWorkFkCarVal omsOrderFwxmWorkFkCarVal;

    @Autowired
    private OmsOrderFwxmWorkFkDao omsOrderFwxmWorkFkDao;

    @Autowired
    private OmsOrderFwxmWorkTraceDao omsOrderFwxmWorkTraceDao;

    @Autowired
    private OmsOrderFwxmWorkDao omsOrderFwxmWorkDao;

    @Autowired
    private Extensions extensions;

    @Autowired
    private OmsOrderFwxmWorkFkZzfwService omsOrderFwxmWorkFkZzfwService;


    @Autowired
    private OmsOrderFwxmCrkZzfwService omsOrderFwxmCrkZzfwService;

    @Override
    public TgPageInfo queryPageList(OmsOrderFwxmWorkFkCarEntity entity) {
        EciQuery<OmsOrderFwxmWorkFkCarEntity> eciQuery = EciQuery.buildQuery(entity);
        List<OmsOrderFwxmWorkFkCarEntity> entities = omsOrderFwxmWorkFkCarDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderFwxmWorkFkCarEntity save(OmsOrderFwxmWorkFkCarEntity entity) {
        // 返回实体对象
        OmsOrderFwxmWorkFkCarEntity omsOrderFwxmWorkFkCarEntity = null;
        omsOrderFwxmWorkFkCarVal.saveValidate(entity, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            omsOrderFwxmWorkFkCarEntity = omsOrderFwxmWorkFkCarDao.insertOne(entity);

        } else {

            omsOrderFwxmWorkFkCarEntity = omsOrderFwxmWorkFkCarDao.updateByEntityId(entity);

        }
        return omsOrderFwxmWorkFkCarEntity;
    }

    @Override
    public List<OmsOrderFwxmWorkFkCarEntity> selectList(OmsOrderFwxmWorkFkCarEntity entity) {
        return omsOrderFwxmWorkFkCarDao.selectList(entity);
    }

    @Override
    public OmsOrderFwxmWorkFkCarEntity selectOneById(Serializable id) {
        return omsOrderFwxmWorkFkCarDao.selectById(id);
    }


    @Override
    public void insertBatch(List<OmsOrderFwxmWorkFkCarEntity> list) {
        omsOrderFwxmWorkFkCarDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return omsOrderFwxmWorkFkCarDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return omsOrderFwxmWorkFkCarDao.deleteById(id);
    }

    /**
     * 保存
     **/
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrderFwxmWorkFkCar(String jsonString) {

        ZsrJson jsonStr = ZsrJson.parse(jsonString);
        OmsOrderFwxmWorkFkEntity fkentity = jsonStr.check("entity").getObject("entity", OmsOrderFwxmWorkFkEntity.class);
        List<OmsOrderFwxmWorkFkCarEntity> fkZyCarList = jsonStr.check("fkCarList").getList("fkCarList", OmsOrderFwxmWorkFkCarEntity.class);
        List<OmsOrderFwxmWorkFkZzfwEntity> dateQTFWList = jsonStr.check("dataQtfwList").getList("dataQtfwList", OmsOrderFwxmWorkFkZzfwEntity.class);
        if (fkentity == null) {
            throw new BaseException("entity 解析失败");
        }

        // 作业说明
        List<OmsOrderFwxmWorkFkEntity> oldFkList =
                omsOrderFwxmWorkFkDao.select().eq(OmsOrderFwxmWorkFkEntity::getBizRegId, fkentity.getBizRegId())
                        .list();

        if (oldFkList.size() == 0) {
            UserInfo userInfo = UserContext.getUserInfo();

            String sql = "INSERT INTO OMS_ORDER_FWXM_WORK_FK(GUID, WORK_NO, BIZ_REG_ID,ORDER_NO, FWXM_CODE, XZWT_NO,CREATE_USER, CREATE_USER_NAME, CREATE_DATE" +
                    ", UPDATE_USER, UPDATE_USER_NAME, UPDATE_DATE, COMPANY_CODE, COMPANY_NAME, NODE_CODE, NODE_NAME, GROUP_CODE, GROUP_NAME, OP_MEMO)\n" +
                    "VALUES(" + cmn.SQLQ(IdWorker.get32UUID()) + "," + cmn.SQLQ(fkentity.getWorkNo()) + "," + cmn.SQLQ(fkentity.getBizRegId()) + "," + cmn.SQLQ(fkentity.getOrderNo()) + "" +
                    "," + cmn.SQLQ(fkentity.getFwxmCode()) + "," + cmn.SQLQ(fkentity.getXzwtNo()) + "," + cmn.SQLQ(userInfo.getUserLoginNo()) + "" +
                    "," + cmn.SQLQ(userInfo.getTrueName()) + ",sysdate," + cmn.SQLQ(userInfo.getUserLoginNo()) + "," + cmn.SQLQ(userInfo.getTrueName()) + "" +
                    ",sysdate," + cmn.SQLQ(userInfo.getCompanyCode()) + "," + cmn.SQLQ(userInfo.getCompanyName()) + "," + cmn.SQLQ(userInfo.getDeptCode()) + "" +
                    "," + cmn.SQLQ(userInfo.getDeptName()) + "," + cmn.SQLQ(userInfo.getCompanyCode()) + "," + cmn.SQLQ(userInfo.getCompanyName()) + "," + cmn.SQLQ(fkentity.getOpMemo()) + ")";
            DBHelper.execute(sql);
        } else {
            OmsOrderFwxmWorkFkEntity oldFk = oldFkList.get(0);
            oldFk.setWorkNo(fkentity.getWorkNo());
            oldFk.setBizRegId(fkentity.getBizRegId());
            oldFk.setXzwtNo(fkentity.getXzwtNo());
            oldFk.setFwxmCode(fkentity.getFwxmCode());
            oldFk.setOrderNo(fkentity.getOrderNo());
            oldFk.setOpMemo(fkentity.getOpMemo());

            oldFk.setUpdateDate(new java.util.Date());
            oldFk.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            oldFk.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            oldFk.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            oldFk.setGroupName(UserContext.getUserInfo().getCompanyName());
            oldFk.setNodeCode(UserContext.getUserInfo().getDeptCode());
            oldFk.setNodeName(UserContext.getUserInfo().getDeptName());
            oldFk.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            oldFk.setCompanyName(UserContext.getUserInfo().getCompanyName());
            omsOrderFwxmWorkFkDao.updateByEntityId(oldFk);
        }

        // 反馈-车辆
        List<OmsOrderFwxmWorkFkCarEntity> oldFkCar = omsOrderFwxmWorkFkCarDao.select()
                .eq(OmsOrderFwxmWorkFkCarEntity::getBizRegId, fkentity.getBizRegId())
                .list();

        List<Date> takeDate = new ArrayList<>();
        List<Date> returnDate = new ArrayList<>();

        for (OmsOrderFwxmWorkFkCarEntity fkCar : fkZyCarList) {

            if (oldFkCar != null && oldFkCar.size() > 0) {
                fkCar.setUpdateDate(new java.util.Date());
                fkCar.setWorkNo(fkentity.getWorkNo());
                fkCar.setBizRegId(fkentity.getBizRegId());
                fkCar.setXzwtNo(fkentity.getXzwtNo());
                fkCar.setFwxmCode(fkentity.getFwxmCode());
                fkCar.setOrderNo(fkentity.getOrderNo());
                fkCar.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
                fkCar.setUpdateUserName(UserContext.getUserInfo().getTrueName());
                fkCar.setGroupCode(UserContext.getUserInfo().getCompanyCode());
                fkCar.setGroupName(UserContext.getUserInfo().getCompanyName());
                fkCar.setNodeCode(UserContext.getUserInfo().getDeptCode());
                fkCar.setNodeName(UserContext.getUserInfo().getDeptName());
                fkCar.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
                fkCar.setCompanyName(UserContext.getUserInfo().getCompanyName());
                omsOrderFwxmWorkFkCarDao.updateByEntityId(fkCar);
            } else {
                fkCar.setGuid(IdWorker.get32UUID());
                fkCar.setWorkNo(fkentity.getWorkNo());
                fkCar.setBizRegId(fkentity.getBizRegId());
                fkCar.setXzwtNo(fkentity.getXzwtNo());
                fkCar.setFwxmCode(fkentity.getFwxmCode());
                fkCar.setOrderNo(fkentity.getOrderNo());
                fkCar.setCreateDate(new java.util.Date());
                fkCar.setUpdateDate(new java.util.Date());
                fkCar.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
                fkCar.setCreateUserName(UserContext.getUserInfo().getTrueName());
                fkCar.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
                fkCar.setUpdateUserName(UserContext.getUserInfo().getTrueName());
                fkCar.setGroupCode(UserContext.getUserInfo().getCompanyCode());
                fkCar.setGroupName(UserContext.getUserInfo().getCompanyName());
                fkCar.setNodeCode(UserContext.getUserInfo().getDeptCode());
                fkCar.setNodeName(UserContext.getUserInfo().getDeptName());
                fkCar.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
                fkCar.setCompanyName(UserContext.getUserInfo().getCompanyName());
                omsOrderFwxmWorkFkCarDao.insertOne(fkCar);
            }

            if (fkCar.getTakeDate() != null) {
                takeDate.add(fkCar.getTakeDate());
            }
            if (fkCar.getReturnDate() != null) {
                returnDate.add(fkCar.getReturnDate());
            }
        }

        List<OmsOrderFwxmWorkTraceEntity> traceList = omsOrderFwxmWorkTraceDao.select()
                .eq(OmsOrderFwxmWorkTraceEntity::getBizRegId, fkentity.getBizRegId())
                .list();

        if (!traceList.isEmpty()) {
            takeDate.sort(Comparator.naturalOrder());
            returnDate.sort(Comparator.naturalOrder());

            for (OmsOrderFwxmWorkTraceEntity item : traceList) {
                if (item.getActualOkDate() != null) {
                    continue;
                }

                if ("GLYS_TH".equals(item.getLinkCode())) {
                    if (!takeDate.isEmpty()) {
                        item.setActualOkDate(takeDate.get(0));
                    }
                } else if ("GLYS_DH".equals(item.getLinkCode())) {
                    if (!returnDate.isEmpty()) {
                        item.setActualOkDate(returnDate.get(returnDate.size() - 1));
                    }
                }

                item.setUpdateDate(new java.util.Date());
                item.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
                item.setUpdateUserName(UserContext.getUserInfo().getTrueName());
                omsOrderFwxmWorkTraceDao.updateByEntityId(item);
            }
        }

        // 反馈-其他作业事项
        if (dateQTFWList != null && dateQTFWList.size() > 0) {
            omsOrderFwxmWorkFkZzfwService.saveZZFW_QT(fkentity, dateQTFWList);
        }

        List<OmsOrderFwxmWorkEntity> workList = omsOrderFwxmWorkDao.select()
                .eq(OmsOrderFwxmWorkEntity::getWorkNo, fkentity.getWorkNo())
                .list();
        if (workList != null && workList.size() > 0) {
            extensions.addOmsGh(workList.get(0).getOrderNo());
        }

        return true;
    }

    /***
     * frmFeedbackEtms -反馈加载
     * */
    public ZsrBaseEntity fkCarEditLoad(OmsOrderFwxmWorkFkEntity entity) {

        ZsrBaseEntity reseponseEntity = new ZsrBaseEntity();

        String workNo = entity.getWorkNo();
        String bizRegId = entity.getBizRegId();

        // 1. 查询 fkCar 数据
        List<OmsOrderFwxmWorkFkCarEntity> fkCarList = omsOrderFwxmWorkFkCarDao.select()
                .eq(OmsOrderFwxmWorkFkCarEntity::getBizRegId, bizRegId)
                .list();

        List<OmsOrderFwxmWorkFkCarEntity> resFkCarList = new ArrayList<>();

        if (fkCarList.isEmpty()) {
            List<OmsOrderFwxmWorkFkCarExdEntity> carList = getCarList(workNo);
            for (OmsOrderFwxmWorkFkCarExdEntity row : carList) {
                Integer trainQty = row.getTrainQty();
                for (int j = 0; j < trainQty; j++) {
                    resFkCarList.add(row); // 复制行
                }
            }
        } else {
            resFkCarList = search(entity);
        }

        // 反馈基础信息
        List<OmsOrderFwxmWorkFkEntity> fkList = omsOrderFwxmWorkFkDao.select().
                eq(OmsOrderFwxmWorkFkEntity::getBizRegId, bizRegId)
                .list();
        reseponseEntity.push("fk", fkList);

        // 反馈表头信息
        DictFieldUtils.handleDictFields(resFkCarList);
        reseponseEntity.push("fkCar", resFkCarList);

        // 其他作业事项
        List<OmsOrderFwxmWorkFkZzfwEntity> zzfwList = omsOrderFwxmCrkZzfwService.loadQtKcZzfw(bizRegId);
        DictFieldUtils.handleDictFields(zzfwList);
        reseponseEntity.push("dtDateQTFW", zzfwList);

        return reseponseEntity;
    }

    /**
     * 其他服务
     */
    public List<OmsOrderFwxmWorkFkCarExdEntity> getCarList(String workNo) {

        String sql = "SELECT '' AS GPS_NO,\n" +
                "       '' AS TAKE_DATE,\n" +
                "       '' AS RETURN_DATE,\n" +
                "       A.LINE_NO GUID,\n" +
                "      (SELECT EXTEND.NAME FROM FZGJ_EXTEND_DATA EXTEND WHERE EXTEND.STATUS='Y' AND EXTEND.GROUP_CODE=A.GROUP_CODE AND ROWNUM=1) CAR_TYPE_NAME,\n" +
                "       '' AS CAR_NO,\n" +
                "      (SELECT FLOOR.NAME  FROM FZGJ_LEASED_FLOOR FLOOR WHERE FLOOR.CODE = A.VEHICLE_SIZE AND FLOOR.GROUP_CODE = A.GROUP_CODE AND ROWNUM=1) CAR_SIZE_NAME,\n" +
                "       '' AS TRAILER_NO,\n" +
                "       '' AS DRIVER_NAME,\n" +
                "       '' AS TEL,\n" +
                "       '' AS ID_CARD,\n" +
                "       '' AS WEIGHT,\n" +
                "       '' AS BOX_NO,\n" +
                "       '' AS PCD_NO,\n" +
                "       A.TRAIN_QTY\n" +
                "    FROM OMS_ORDER_FWXM_TMS_XL_XL_LY_CL A\n" +
                "    WHERE A.SEQ_NO =(SELECT B.SEQ_NO\n" +
                "                    FROM OMS_ORDER_FWXM_TMS_XL_XL B\n" +
                "                    WHERE B.WORK_NO =" + cmn.SQLQ(workNo) + ")\n" +
                "        AND A.GROUP_CODE = " + cmn.SQLQ(UserContext.getUserInfo().getCompanyCode()) + "";

        return DBHelper.selectList(sql, OmsOrderFwxmWorkFkCarExdEntity.class);
    }


    /**
     * 表头
     **/
    public List<OmsOrderFwxmWorkFkCarEntity> search(OmsOrderFwxmWorkFkEntity entity) {

        String sql = "SELECT A.GUID,A.ORDER_NO,A.XZWT_NO,A.WORK_NO,A.FWXM_CODE\n" +
                "       ,(SELECT EXTEND.NAME FROM FZGJ_EXTEND_DATA EXTEND WHERE EXTEND.STATUS='Y' AND EXTEND.GROUP_CODE=A.GROUP_CODE AND ROWNUM=1) CAR_TYPE_NAME\n" +
                "       ,(SELECT FLOOR.NAME  FROM FZGJ_LEASED_FLOOR FLOOR WHERE FLOOR.CODE = A.CAR_SIZE AND FLOOR.GROUP_CODE = A.GROUP_CODE AND ROWNUM=1)  CAR_SIZE_NAME\n" +
                "       ,A.CAR_NO,A.CAR_TYPE,A.CAR_SIZE,A.CREATE_USER,A.CREATE_USER_NAME\n" +
                "       ,A.CREATE_DATE,A.UPDATE_USER,A.UPDATE_USER_NAME,A.UPDATE_DATE,A.COMPANY_CODE\n" +
                "       ,A.COMPANY_NAME,A.NODE_CODE,A.NODE_NAME,A.GROUP_CODE,A.GROUP_NAME\n" +
                "       ,A.TAKE_DATE,A.RETURN_DATE,A.GPS_NO,A.SYS_CODE,A.PCD_NO\n" +
                "       ,A.TRAILER_NO,A.DRIVER_NAME,A.TEL,A.ID_CARD,A.WEIGHT\n" +
                "       ,A.BOX_NO,A.BIZ_REG_ID,A.CAR_COLOR\n" +
                "       FROM OMS_ORDER_FWXM_WORK_FK_CAR  A";

        sql += " WHERE 1=1 ";
        sql += " AND A.WORK_NO=" + cmn.SQLQ(entity.getWorkNo());
        sql += " AND A.BIZ_REG_ID=" + cmn.SQLQ(entity.getBizRegId());

        return DBHelper.selectList(sql, OmsOrderFwxmWorkFkCarEntity.class);
    }
}