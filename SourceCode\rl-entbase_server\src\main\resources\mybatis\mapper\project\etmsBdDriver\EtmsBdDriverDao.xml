<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.etmsBdDriver.dao.EtmsBdDriverDao">
    <resultMap type="EtmsBdDriverEntity" id="EtmsBdDriverResult">
        <result property="guid" column="GUID"/>
        <result property="name" column="NAME"/>
        <result property="phone" column="PHONE"/>
        <result property="isGk" column="IS_GK"/>
        <result property="userId" column="USER_ID"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createCompany" column="CREATE_COMPANY"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="memo" column="MEMO"/>
        <result property="truckNo" column="TRUCK_NO"/>
        <result property="status" column="STATUS"/>
        <result property="driverNo" column="DRIVER_NO"/>
        <result property="driverDate" column="DRIVER_DATE"/>
        <result property="isDriver" column="IS_DRIVER"/>
        <result property="idNumber" column="ID_NUMBER"/>
        <result property="quasiCarType" column="QUASI_CAR_TYPE"/>
        <result property="supercargo" column="SUPERCARGO"/>
        <result property="birthDate" column="BIRTH_DATE"/>
        <result property="sex" column="SEX"/>
        <result property="address" column="ADDRESS"/>
        <result property="partnerGuid" column="PARTNER_GUID"/>
        <result property="loginNo" column="LOGIN_NO"/>
        <result property="isUseApp" column="IS_USE_APP"/>
        <result property="ssoCompanyGuid" column="SSO_COMPANY_GUID"/>
        <result property="supercargoPhone" column="SUPERCARGO_PHONE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="jgIszc" column="JG_ISZC"/>
        <result property="sjztIszc" column="SJZT_ISZC"/>
    </resultMap>

    <sql id="selectEtmsBdDriverEntityVo">
        select
            GUID,
            NAME,
            PHONE,
            IS_GK,
            USER_ID,
            CREATE_DATE,
            CREATE_USER,
            CREATE_COMPANY,
            UPDATE_DATE,
            UPDATE_USER,
            MEMO,
            TRUCK_NO,
            STATUS,
            DRIVER_NO,
            DRIVER_DATE,
            IS_DRIVER,
            ID_NUMBER,
            QUASI_CAR_TYPE,
            SUPERCARGO,
            BIRTH_DATE,
            SEX,
            ADDRESS,
            PARTNER_GUID,
            LOGIN_NO,
            IS_USE_APP,
            SSO_COMPANY_GUID,
            SUPERCARGO_PHONE,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            CREATE_USER_NAME,
            UPDATE_USER_NAME,
            JG_ISZC,
            SJZT_ISZC
        from ETMS_BD_DRIVER
    </sql>
    <select id="queryPages" parameterType="com.baomidou.mybatisplus.core.conditions.Wrapper"
            resultType="com.eci.project.etmsBdDriver.entity.DriverInfo">
        select *
        from (SELECT A.GUID,
                     A.MANAGE_STATUS,
                     A.NAME,
                     A.PHONE,
                     A.ID_NUMBER,
                     A.IS_GK,A.IS_DRIVER,A.IS_USE_APP,A.QUASI_CAR_TYPE,
                     (CASE A.SEX WHEN 'M' THEN '男' WHEN 'F' THEN '女' END)        AS SEX,
                     (CASE A.IS_GK WHEN 'Y' THEN '否' WHEN 'N' THEN '是' END)      AS IS_GK_NAME,
                     (CASE A.IS_DRIVER WHEN 'Y' THEN '是' WHEN 'N' THEN '否' END)  AS IS_DRIVER_NAME,
                     (CASE A.STATUS WHEN 'Y' THEN '是' WHEN 'N' THEN '否' END)     AS STATUS,
                     (CASE A.IS_USE_APP WHEN 'Y' THEN '是' WHEN 'N' THEN '否' END) AS IS_USE_APP_NAME,
                     A.DRIVER_NO,
                     (SELECT MAX(T.NAME)
                      FROM ETMS_BD_QUASI_CAR_TYPE T
                      WHERE T.CODE = QUASI_CAR_TYPE)                                QUASI_CAR_TYPE_NAME,
                     A.USER_ID,
                     A.CREATE_DATE,
                     A.DRIVER_DATE,
                     A.PARTNER_GUID,
                     (SELECT MAX(NAME)
                      FROM CRM_CUSTOMER X
                      WHERE X.GUID = A.PARTNER_GUID)                             AS partnerGuidName,

                     A.MEMO,
                     A.TRUCK_NO,
                     A.LOGIN_NO,
                     A.CREATE_COMPANY,
                     A.UPDATE_DATE,
                     A.UPDATE_USER_NAME as updateUserName,
                     (SELECT ROUND(AVG(C.BCZHPF), 2)
                      FROM ETMS_TRUCK_ORDER_PJ C
                               LEFT JOIN ETMS_TRUCK_ORDER B ON C.TRUCK_ORDER_GUID = B.GUID
                      WHERE B.DRIVER_NAME = A.USER_ID)                           AS PJ
              FROM ETMS_BD_DRIVER A) A ${ew.customSqlSegment}

    </select>
</mapper>