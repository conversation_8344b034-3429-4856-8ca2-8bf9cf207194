<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.etmsBdTruckGcQz.dao.EtmsBdTruckGcQzDao">
    <resultMap type="EtmsBdTruckGcQzEntity" id="EtmsBdTruckGcQzResult">
        <result property="guid" column="GUID"/>
        <result property="truckGuid" column="TRUCK_GUID"/>
        <result property="gcNo" column="GC_NO"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="modMark" column="MOD_MARK"/>
    </resultMap>

    <sql id="selectEtmsBdTruckGcQzEntityVo">
        select
            GUID,
            TRUCK_GUID,
            GC_NO,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            CREATE_USER_NAME,
            UPDATE_USER_NAME,
            CREATE_DATE,
            UPDATE_DATE,
            MOD_MARK
        from ETMS_BD_TRUCK_GC_QZ
    </sql>
</mapper>