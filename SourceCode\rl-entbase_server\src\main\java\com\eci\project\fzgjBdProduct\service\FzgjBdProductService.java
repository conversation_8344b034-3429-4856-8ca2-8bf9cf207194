package com.eci.project.fzgjBdProduct.service;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjBdProduct.dao.FzgjBdProductDao;
import com.eci.project.fzgjBdProduct.entity.FzgjBdProductEntity;
import com.eci.project.fzgjBdProduct.validate.FzgjBdProductVal;

import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 业务产品Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-01
*/
@Service
@Slf4j
public class FzgjBdProductService implements EciBaseService<FzgjBdProductEntity> {

    @Autowired
    private FzgjBdProductDao fzgjBdProductDao;

    @Autowired
    private FzgjBdProductVal fzgjBdProductVal;


    @Override
    public TgPageInfo queryPageList(FzgjBdProductEntity entity) {
        EciQuery<FzgjBdProductEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjBdProductEntity> entities = fzgjBdProductDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjBdProductEntity save(FzgjBdProductEntity entity) {

        // 返回实体对象
        FzgjBdProductEntity fzgjBdProductEntity = null;
        fzgjBdProductVal.saveValidate(entity,BllContext.getBusinessType());
        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setGuid(IdWorker.getIdStr());
            entity.setCreateDate(new java.util.Date());
            entity.setUpdateDate(new java.util.Date());
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        } else {
            entity.setUpdateDate(new java.util.Date());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        }
        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjBdProductEntity = fzgjBdProductDao.insertOne(entity);

        }else{

            fzgjBdProductEntity = fzgjBdProductDao.updateByEntityId(entity);

        }
        return fzgjBdProductEntity;
    }

    @Override
    public List<FzgjBdProductEntity> selectList(FzgjBdProductEntity entity) {
        return fzgjBdProductDao.selectList(entity);
    }

    @Override
    public FzgjBdProductEntity selectOneById(Serializable id) {
        return fzgjBdProductDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjBdProductEntity> list) {
        fzgjBdProductDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjBdProductDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjBdProductDao.deleteById(id);
    }

}