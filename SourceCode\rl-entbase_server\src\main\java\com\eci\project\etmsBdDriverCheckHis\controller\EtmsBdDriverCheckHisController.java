package com.eci.project.etmsBdDriverCheckHis.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.etmsBdDriverCheckHis.service.EtmsBdDriverCheckHisService;
import com.eci.project.etmsBdDriverCheckHis.entity.EtmsBdDriverCheckHisEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 司机体检历史Controller
*
* @<NAME_EMAIL>
* @date 2025-04-22
*/
@Api(tags = "司机体检历史")
@RestController
@RequestMapping("/etmsBdDriverCheckHis")
public class EtmsBdDriverCheckHisController extends EciBaseController {

    @Autowired
    private EtmsBdDriverCheckHisService etmsBdDriverCheckHisService;


    @ApiOperation("司机体检历史:保存")
    @EciLog(title = "司机体检历史:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody EtmsBdDriverCheckHisEntity entity){
        EtmsBdDriverCheckHisEntity etmsBdDriverCheckHisEntity =etmsBdDriverCheckHisService.save(entity);
        return ResponseMsgUtil.success(10001,etmsBdDriverCheckHisEntity);
    }


    @ApiOperation("司机体检历史:查询列表")
    @EciLog(title = "司机体检历史:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody EtmsBdDriverCheckHisEntity entity){
        List<EtmsBdDriverCheckHisEntity> etmsBdDriverCheckHisEntities = etmsBdDriverCheckHisService.selectList(entity);
        return ResponseMsgUtil.success(10001,etmsBdDriverCheckHisEntities);
    }


    @ApiOperation("司机体检历史:分页查询列表")
    @EciLog(title = "司机体检历史:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody EtmsBdDriverCheckHisEntity entity){
        TgPageInfo tgPageInfo = etmsBdDriverCheckHisService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("司机体检历史:根据ID查一条")
    @EciLog(title = "司机体检历史:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody EtmsBdDriverCheckHisEntity entity){
        EtmsBdDriverCheckHisEntity  etmsBdDriverCheckHisEntity = etmsBdDriverCheckHisService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,etmsBdDriverCheckHisEntity);
    }


    @ApiOperation("司机体检历史:根据ID删除一条")
    @EciLog(title = "司机体检历史:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody EtmsBdDriverCheckHisEntity entity){
        int count = etmsBdDriverCheckHisService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("司机体检历史:根据ID字符串删除多条")
    @EciLog(title = "司机体检历史:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody EtmsBdDriverCheckHisEntity entity) {
        int count = etmsBdDriverCheckHisService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}