package com.eci.project.crmCustomerKh.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eci.common.db.DBHelper;
import com.eci.common.utils.CommonLib;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.crmCustomerKh.dao.CrmCustomerKhDao;
import com.eci.project.crmCustomerKh.entity.CrmCustomerKhEntity;
import com.eci.project.crmCustomerKh.validate.CrmCustomerKhVal;

import com.eci.sso.role.entity.UserContext;
import com.eci.wu.core.DataTable;
import com.eci.wu.core.EntityBase;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 业务伙伴-客户信息Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-29
*/
@Service
@Slf4j
public class CrmCustomerKhService implements EciBaseService<CrmCustomerKhEntity> {

    @Autowired
    private CrmCustomerKhDao crmCustomerKhDao;

    @Autowired
    private CrmCustomerKhVal crmCustomerKhVal;
    CommonLib cmn = CommonLib.getInstance();

    @Override
    public TgPageInfo queryPageList(CrmCustomerKhEntity entity) {
        EciQuery<CrmCustomerKhEntity> eciQuery = EciQuery.buildQuery(entity);
        List<CrmCustomerKhEntity> entities = crmCustomerKhDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }

    public TgPageInfo searchTax(String code,String name){
        String sql="";
        if(!code.isEmpty())
            sql+=" AND code like "+cmn.SQLQL(code);
        if(!name.isEmpty())
            sql+=" AND code like "+cmn.SQLQL(name);
        Integer pageSize = BllContext.getPaging().getPageSize();
        Integer pageNum = BllContext.getPaging().getPageNum();
        PageHelper.startPage(pageNum, pageSize == -1 ? 0 : pageSize);
        List<EntityBase> list= crmCustomerKhDao.selectTax(sql);
        return EciQuery.getPageInfo(list);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public CrmCustomerKhEntity save(CrmCustomerKhEntity entity) {
        // 返回实体对象
        CrmCustomerKhEntity crmCustomerKhEntity = null;
        crmCustomerKhVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            crmCustomerKhEntity = crmCustomerKhDao.insertOne(entity);

        }else{

            crmCustomerKhEntity = crmCustomerKhDao.updateByEntityId(entity);

        }
        return crmCustomerKhEntity;
    }

    @Override
    public List<CrmCustomerKhEntity> selectList(CrmCustomerKhEntity entity) {
        return crmCustomerKhDao.selectList(entity);
    }

    @Override
    public CrmCustomerKhEntity selectOneById(Serializable id) {
        return crmCustomerKhDao.selectById(id);
    }

    public CrmCustomerKhEntity load(CrmCustomerKhEntity entity){
        QueryWrapper query=new QueryWrapper();
        query.eq("CUSTOMER_CODE",entity.getCustomerCode());
        query.eq("COMPANY_CODE", UserContext.getUserInfo().getCompanyCode());
        return crmCustomerKhDao.selectOne(query);
    }

    @Override
    public void insertBatch(List<CrmCustomerKhEntity> list) {
        crmCustomerKhDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return crmCustomerKhDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return crmCustomerKhDao.deleteById(id);
    }

}