package com.eci.project.omsOrderFwxmWorkFkZzfw.service;

import cn.hutool.core.lang.Pair;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.DataExtend;
import com.eci.common.db.DBHelper;
import com.eci.common.util.StringUtils;
import com.eci.common.utils.CommonLib;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.exception.BaseException;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.omsOrderFwxmWorkFk.entity.OmsOrderFwxmWorkFkEntity;
import com.eci.project.omsOrderFwxmWorkFkZzfw.dao.OmsOrderFwxmWorkFkZzfwDao;
import com.eci.project.omsOrderFwxmWorkFkZzfw.entity.OmsOrderFwxmWorkFkZzfwEntity;
import com.eci.project.omsOrderFwxmWorkFkZzfw.validate.OmsOrderFwxmWorkFkZzfwVal;
import com.eci.sso.role.entity.UserContext;
import com.sun.xml.bind.v2.model.core.ID;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.jdbc.SQL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.security.Key;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 反馈内容-作业信息:其他增值服务信息Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-05-22
 */
@Service
@Slf4j
public class OmsOrderFwxmWorkFkZzfwService implements EciBaseService<OmsOrderFwxmWorkFkZzfwEntity> {

    CommonLib cmn = CommonLib.getInstance();

    @Autowired
    private OmsOrderFwxmWorkFkZzfwDao omsOrderFwxmWorkFkZzfwDao;

    @Autowired
    private OmsOrderFwxmWorkFkZzfwVal omsOrderFwxmWorkFkZzfwVal;


    @Override
    public TgPageInfo queryPageList(OmsOrderFwxmWorkFkZzfwEntity entity) {
        EciQuery<OmsOrderFwxmWorkFkZzfwEntity> eciQuery = EciQuery.buildQuery(entity);
        List<OmsOrderFwxmWorkFkZzfwEntity> entities = omsOrderFwxmWorkFkZzfwDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderFwxmWorkFkZzfwEntity save(OmsOrderFwxmWorkFkZzfwEntity entity) {
        // 返回实体对象
        OmsOrderFwxmWorkFkZzfwEntity omsOrderFwxmWorkFkZzfwEntity = null;
        omsOrderFwxmWorkFkZzfwVal.saveValidate(entity, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            omsOrderFwxmWorkFkZzfwEntity = omsOrderFwxmWorkFkZzfwDao.insertOne(entity);

        } else {

            omsOrderFwxmWorkFkZzfwEntity = omsOrderFwxmWorkFkZzfwDao.updateByEntityId(entity);

        }
        return omsOrderFwxmWorkFkZzfwEntity;
    }

    @Override
    public List<OmsOrderFwxmWorkFkZzfwEntity> selectList(OmsOrderFwxmWorkFkZzfwEntity entity) {
        return omsOrderFwxmWorkFkZzfwDao.selectList(entity);
    }

    @Override
    public OmsOrderFwxmWorkFkZzfwEntity selectOneById(Serializable id) {
        return omsOrderFwxmWorkFkZzfwDao.selectById(id);
    }


    @Override
    public void insertBatch(List<OmsOrderFwxmWorkFkZzfwEntity> list) {
        omsOrderFwxmWorkFkZzfwDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return omsOrderFwxmWorkFkZzfwDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return omsOrderFwxmWorkFkZzfwDao.deleteById(id);
    }

    /**
     * 其他-增值服务-保存
     **/
    public void saveZZFW_QT(OmsOrderFwxmWorkFkEntity fkentity, List<OmsOrderFwxmWorkFkZzfwEntity> dateQTFWList) {

        String bizRegId = fkentity.getBizRegId();

        if (dateQTFWList.size() <= 0) {
            String sqlOne= "DELETE FROM OMS_ORDER_FWXM_WORK_FK_ZZFW WHERE BIZ_REG_ID=" + cmn.SQLQ(bizRegId);
            DBHelper.execute(sqlOne);
        } else {
            Map<Pair<String, String>, Long> grouped = dateQTFWList.stream()
                    .collect(Collectors.groupingBy(
                            item -> Pair.of(item.getOpItem(), item.getOpProperty()),
                            Collectors.counting()
                    ));

            for (Map.Entry<Pair<String, String>, Long> entry : grouped.entrySet()) {
                if (entry.getValue() > 1) {
                    throw new BaseException("增值服务明细存在相同的【作业事项】和【作业属性】,请检查！");
                }
            }

            // 先删除
            String sqlSecond = "DELETE FROM OMS_ORDER_FWXM_WORK_FK_ZZFW WHERE BIZ_REG_ID=" + cmn.SQLQ(bizRegId);
            DBHelper.execute(sqlSecond);

            // 遍历数据表，逐条插入新数据
            for (OmsOrderFwxmWorkFkZzfwEntity row : dateQTFWList) {
                OmsOrderFwxmWorkFkZzfwEntity entityZY = new OmsOrderFwxmWorkFkZzfwEntity();

                // 设置基础字段
                entityZY.setOrderNo(fkentity.getOrderNo());
                entityZY.setXzwtNo(fkentity.getXzwtNo());
                entityZY.setWorkNo(fkentity.getWorkNo());
                entityZY.setFwxmCode(fkentity.getFwxmCode());

                entityZY.setOpItem(DataExtend.toCode(row.getOpItem()));
                entityZY.setOpProperty(DataExtend.toCode(row.getOpProperty()));
                entityZY.setUnit(DataExtend.toCode(row.getUnit()));

                // 设置 OP_QTY_REAL（处理 ToDoubleNullOrEmptyToZero()）
                entityZY.setOpQtyReal(row.getOpQtyReal());

                // 设置 LINE_NUM（如果字段存在）
                entityZY.setLineNum(row.getLineNum());

                // 设置 MEMO
                entityZY.setMemo(row.getMemo());

                // 设置 BIZ_REG_ID
                entityZY.setBizRegId(bizRegId);

                // 设置 JJ_NO_RCK（如果字段存在）
                entityZY.setJjNoRck(row.getJjNoRck());

                // 设置 GUID（如果存在，否则生成新 GUID）
                String guid = StringUtils.hasValue(row.getGuid()) ? row.getGuid() : IdWorker.get32UUID();
                entityZY.setGuid(guid);

                // 设置操作和用户信息
                entityZY.setCreateDate(new java.util.Date());
                entityZY.setUpdateDate(new java.util.Date());
                entityZY.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
                entityZY.setCreateUserName(UserContext.getUserInfo().getTrueName());
                entityZY.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
                entityZY.setUpdateUserName(UserContext.getUserInfo().getTrueName());
                entityZY.setGroupCode(UserContext.getUserInfo().getCompanyCode());
                entityZY.setGroupName(UserContext.getUserInfo().getCompanyName());
                entityZY.setNodeCode(UserContext.getUserInfo().getDeptCode());
                entityZY.setNodeName(UserContext.getUserInfo().getDeptName());
                entityZY.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
                entityZY.setCompanyName(UserContext.getUserInfo().getCompanyName());
                omsOrderFwxmWorkFkZzfwDao.insertOne(entityZY);
            }
        }
    }

}