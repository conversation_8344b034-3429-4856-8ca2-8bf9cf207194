package com.eci.project.fzgjBdFileBiz.service;

import com.eci.common.Zsr;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjBdFileBiz.dao.FzgjBdFileBizDao;
import com.eci.project.fzgjBdFileBiz.entity.FzgjBdFileBizEntity;
import com.eci.project.fzgjBdFileBiz.validate.FzgjBdFileBizVal;

import com.eci.project.fzgjFileType.entity.FzgjFileTypeEntity;
import com.eci.sso.login.entity.UserInfo;
import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;


/**
 * 业务附件类型及授权Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-04-11
 */
@Service
@Slf4j
public class FzgjBdFileBizService implements EciBaseService<FzgjBdFileBizEntity> {

    @Autowired
    private FzgjBdFileBizDao fzgjBdFileBizDao;

    @Autowired
    private FzgjBdFileBizVal fzgjBdFileBizVal;


    @Override
    public TgPageInfo queryPageList(FzgjBdFileBizEntity entity) {
        EciQuery<FzgjBdFileBizEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjBdFileBizEntity> entities = fzgjBdFileBizDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjBdFileBizEntity save(FzgjBdFileBizEntity entity) {
        // 返回实体对象
        FzgjBdFileBizEntity fzgjBdFileBizEntity = null;
        fzgjBdFileBizVal.saveValidate(entity, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjBdFileBizEntity = fzgjBdFileBizDao.insertOne(entity);

        } else {

            fzgjBdFileBizEntity = fzgjBdFileBizDao.updateByEntityId(entity);

        }
        return fzgjBdFileBizEntity;
    }


    @Transactional(rollbackFor = Exception.class)
    public void beforeSaveCleanData(FzgjFileTypeEntity entity) {
//        FZGJ_BD_FILE_BIZ.DAL.Delete().Where($"{" +
//                "FZGJ_BD_FILE_BIZ.Fields.TARGET_CODE}={cmn.SQLQ(fzgjFileType.CODE)} " +
//                "AND {FZGJ_BD_FILE_BIZ.Fields.COMPANY_CODE}={cmn.SQLQ(context.UserInfo.CompanyCode)} " +
//                "AND {FZGJ_BD_FILE_BIZ.Fields.AUTHORITY_TYPE} IN {(new List<string>() { CommonData.FILE_BIZ_AUTHORITY_TYPE.View, CommonData.FILE_BIZ_AUTHORITY_TYPE.Upload }).ToInCondition()}").Execute(context.Transaction);
        fzgjBdFileBizDao.delete()
                .eq(FzgjBdFileBizEntity::getTargetCode, entity.getCode())
                .eq(FzgjBdFileBizEntity::getCompanyCode, UserContext.getUserInfo().getCompanyCode())
                .in(FzgjBdFileBizEntity::getAuthorityType, Arrays.asList("VIEW", "UPLOAD"))
                .execute();

    }

    @Transactional(rollbackFor = Exception.class)
    public FzgjBdFileBizEntity saveByCustom(FzgjBdFileBizEntity entity) {

        entity.setCreateDate(new java.util.Date());
        entity.setUpdateDate(new java.util.Date());
        entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
        entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
        entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
        entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        entity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
        entity.setCompanyName(UserContext.getUserInfo().getCompanyName());
        entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
        entity.setGroupName(UserContext.getUserInfo().getCompanyName());

        // 返回实体对象
        FzgjBdFileBizEntity fzgjBdFileBizEntity = null;
        fzgjBdFileBizVal.saveValidate(entity, BllContext.getBusinessType());
        fzgjBdFileBizEntity = fzgjBdFileBizDao.insertOne(entity);

        return fzgjBdFileBizEntity;
    }

    @Override
    public List<FzgjBdFileBizEntity> selectList(FzgjBdFileBizEntity entity) {
        return fzgjBdFileBizDao.select()
                .eq(FzgjBdFileBizEntity::getTargetCode, entity.getTargetCode())
                .eq(FzgjBdFileBizEntity::getCompanyCode, UserContext.getUserInfo().getCompanyCode())
                .in(FzgjBdFileBizEntity::getAuthorityType, Arrays.asList("VIEW", "UPLOAD"))
                .list();
//        return fzgjBdFileBizDao.selectList(entity);
    }

    @Override
    public FzgjBdFileBizEntity selectOneById(Serializable id) {
        return fzgjBdFileBizDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjBdFileBizEntity> list) {
        fzgjBdFileBizDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjBdFileBizDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjBdFileBizDao.deleteById(id);
    }

}