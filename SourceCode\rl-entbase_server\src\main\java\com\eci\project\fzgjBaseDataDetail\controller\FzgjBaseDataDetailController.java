package com.eci.project.fzgjBaseDataDetail.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.log.annotation.EciAction;
import com.eci.project.fzgjBaseDataDetail.service.IFzgjBaseDataDetailService;
import com.eci.project.fzgjBaseDataDetail.entity.FzgjBaseDataDetailEntity;
import com.eci.project.fzgjBdServiceItem.entity.FzgjBdServiceItemEntity;
import com.eci.wu.core.DataTable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.EciLog;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


/**
* 扩展基础资料明细Controller
*
* @<NAME_EMAIL>
* @date 2025-03-18
*/
@Api(tags = "扩展基础资料明细")
@RestController
@RequestMapping("/fzgjBaseDataDetail")
public class FzgjBaseDataDetailController extends EciBaseController {

    @Autowired
    private IFzgjBaseDataDetailService fzgjBaseDataDetailService;

    /**
    * 根据实体插入数据库
    * @param entity
    * @return
    */
    @ApiOperation("扩展基础资料明细:保存")
    @EciLog(title = "扩展基础资料明细:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public ResponseMsg save(@RequestBody FzgjBaseDataDetailEntity entity){
        return ResponseMsgUtil.success(10001,fzgjBaseDataDetailService.save(entity));
    }

    /**
    * 根据条件查询一个集合
    * @param entity
    * @return
    */
    @ApiOperation("扩展基础资料明细:查询列表")
    @EciLog(title = "扩展基础资料明细:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    public ResponseMsg selectList(@RequestBody FzgjBaseDataDetailEntity entity){
        return ResponseMsgUtil.success(10001,fzgjBaseDataDetailService.selectList(entity));
    }

    /**
    * 分页查询
    * @param entity
    * @return
    */
    @ApiOperation("扩展基础资料明细:分页查询列表")
    @EciLog(title = "扩展基础资料明细:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    public ResponseMsg selectPageList(@RequestBody FzgjBaseDataDetailEntity entity){
        return ResponseMsgUtil.success(10001,fzgjBaseDataDetailService.queryPageList(entity));
    }

    /**
    * 根据ID查一条记录
    * @param entity
    * @return
    */
    @ApiOperation("扩展基础资料明细:根据ID查一条")
    @EciLog(title = "扩展基础资料明细:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    public ResponseMsg selectOneById(@RequestBody FzgjBaseDataDetailEntity entity){
        return ResponseMsgUtil.success(10001,fzgjBaseDataDetailService.selectOneById(entity.getGuid()));
    }


    /**
    * 根据ID删除
    * @param entity
    * @return
    */
    @ApiOperation("扩展基础资料明细:根据ID删除一条")
    @EciLog(title = "扩展基础资料明细:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    public ResponseMsg deleteById(@RequestBody FzgjBaseDataDetailEntity entity){
        return ResponseMsgUtil.success(10001,fzgjBaseDataDetailService.deleteById(entity.getGuid()));
    }

    /**
    * 根据IDS删除
    *
    * @param entity
    * @return
    */
    @ApiOperation("扩展基础资料明细:根据ID字符串删除多条")
    @EciLog(title = "扩展基础资料明细:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    public ResponseMsg deleteByIds(@RequestBody FzgjBaseDataDetailEntity entity) {
        return ResponseMsgUtil.success(10001, fzgjBaseDataDetailService.deleteByIds(entity.getIds()));
    }

    @ApiOperation("扩展基础资料明细:获取基础资料")
    @EciLog(title = "扩展基础资料明细:获取基础资料", businessType = BusinessType.DELETE)
    @PostMapping("/getInfo")
    @EciAction()
    public ResponseMsg getInfo(@RequestBody FzgjBdServiceItemEntity entity) {

        DataTable result = fzgjBaseDataDetailService.getInfo(entity.getGroupCode(),entity.getStatus());
        return ResponseMsgUtil.success(10001,result);
    }
}