package com.eci.project.etmsBdTruckCertificateQz.service;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.db.DBHelper;
import com.eci.common.enums.Enums;
import com.eci.common.util.DateUtils;
import com.eci.common.util.StringUtils;
import com.eci.common.utils.ExportUtils.ExcelProcess;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.ActionType;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.etmsBdTruckCertificateQz.dao.EtmsBdTruckCertificateQzDao;
import com.eci.project.etmsBdTruckCertificateQz.entity.EtmsBdTruckCertificateQzEntity;
import com.eci.project.etmsBdTruckCertificateQz.entity.EtmsBdTruckCertificateSearchEntity;
import com.eci.project.etmsBdTruckCertificateQz.validate.EtmsBdTruckCertificateQzVal;

import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
* 证件管理Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-27
*/
@Service
@Slf4j
public class EtmsBdTruckCertificateQzService implements EciBaseService<EtmsBdTruckCertificateQzEntity> {

    @Autowired
    private EtmsBdTruckCertificateQzDao etmsBdTruckCertificateQzDao;

    @Autowired
    private EtmsBdTruckCertificateQzVal etmsBdTruckCertificateQzVal;


    @Override
    public TgPageInfo queryPageList(EtmsBdTruckCertificateQzEntity entity) {
        EciQuery<EtmsBdTruckCertificateQzEntity> eciQuery = EciQuery.buildQuery(entity);
        List<EtmsBdTruckCertificateQzEntity> entities = etmsBdTruckCertificateQzDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }


    public TgPageInfo queryCertificatePageList(EtmsBdTruckCertificateSearchEntity entity) {
        StringBuilder stringBuilder=new StringBuilder();
        stringBuilder.append("SELECT A.GUID,B.TRUCK_NO,B.CAR_COLOR,A.CERTIFICATE_TYPE,A.CERTIFICATE_NAME,A.CERTIFICATE_NO,A.CERTIFICATE_STATUS, A.ISSUE_DATE,A.VALIDITY_DATE,\n" +
                "(SELECT NAME FROM (SELECT NAME,CODE FROM ETMS_BD_TRUCK_TYPE_CUSTOM) WHERE CODE = B.CLLX AND ROWNUM = 1) CLLX,\n" +
                "(SELECT NAME FROM (SELECT CODE,TRUCK_TYPE,NAME FROM ETMS_BD_TRUCK_SPEC_CUSTOM) WHERE TRUCK_TYPE = B.CLCC AND ROWNUM = 1) CLCC,\n" +
                "B.IS_GK,A.MOD_MARK, (SELECT X.MAPPING_ABBREVIATION FROM ETMS_CRM_PARTNER X WHERE X.GUID=B.PARTNER_GUID AND X.GROUP_CODE=B.GROUP_CODE) PARTNER_NAME,\n" +
                "(SELECT X.NAME FROM ETMS_BD_DRIVER X WHERE X.GUID=B.DRIVER_GUID) DRIVER_NAME,A.COMPANY_NAME,A.CREATE_DATE\n" +
                "FROM ETMS_BD_TRUCK_CERTIFICATE_QZ A INNER JOIN ETMS_BD_TRUCK_QZ B ON A.TRUCK_GUID=B.GUID where 1=1 ");
        if(StringUtils.hasValue(entity.getCertificateType())){
            stringBuilder.append(" AND A.CERTIFICATE_TYPE like '%"+entity.getCertificateType()+"%'");
        }
        if(StringUtils.hasValue(entity.getCertificateName())){
            stringBuilder.append(" AND A.CERTIFICATE_NAME like '%"+entity.getCertificateName()+"%'");
        }
        if(StringUtils.hasValue(entity.getCertificateNo())){
            stringBuilder.append(" AND A.CERTIFICATE_NO like '%"+entity.getCertificateNo()+"%'");
        }
        if(StringUtils.hasValue(entity.getTruckNo())){
            stringBuilder.append(" AND B.TRUCK_NO like '%"+entity.getTruckNo()+"%'");
        }
        if(StringUtils.hasValue(entity.getPartnerName())){
            stringBuilder.append(" AND (SELECT X.MAPPING_ABBREVIATION FROM ETMS_CRM_PARTNER X WHERE X.GUID=B.PARTNER_GUID AND X.GROUP_CODE=B.GROUP_CODE) = '"+entity.getPartnerName()+"'");
        }
        if(StringUtils.hasValue(entity.getDriverName())){
            stringBuilder.append(" AND (SELECT X.NAME FROM ETMS_BD_DRIVER X WHERE X.GUID=B.DRIVER_GUID) = '"+entity.getDriverName()+"'");
        }
        if(StringUtils.hasValue(entity.getCllx())){
            stringBuilder.append(" AND B.CLLX = '"+entity.getCllx()+"'");
        }
        if(StringUtils.hasValue(entity.getClcc())){
            stringBuilder.append(" AND B.CLCC = '"+entity.getClcc()+"'");
        }
        if(StringUtils.hasValue(entity.getIsGk())){
            stringBuilder.append(" AND B.IS_GK = '"+entity.getIsGk()+"'");
        }
        if(StringUtils.hasValue(entity.getCompanyName())){
            stringBuilder.append(" AND A.COMPANY_CODE like '%"+entity.getCompanyName()+"%'");
        }
        if (entity.getIssueDateStart()!=null) {
            stringBuilder.append(" AND A.ISSUE_DATE >= TO_DATE('" + DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss",entity.getIssueDateStart()) + "', 'yyyy-mm-dd hh24:mi:ss')");
        }
        if (entity.getIssueDateEnd()!=null) {
            stringBuilder.append(" AND A.ISSUE_DATE <= TO_DATE('" + DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss",entity.getIssueDateEnd()) + "', 'yyyy-mm-dd hh24:mi:ss')");
        }
        if (entity.getValidityDateStart()!=null) {
            stringBuilder.append(" AND A.VALIDITY_DATE >= TO_DATE('" + DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss",entity.getValidityDateStart()) + "', 'yyyy-mm-dd hh24:mi:ss')");
        }
        if (entity.getValidityDateEnd()!=null) {
            stringBuilder.append(" AND A.VALIDITY_DATE <= TO_DATE('" + DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss",entity.getValidityDateEnd()) + "', 'yyyy-mm-dd hh24:mi:ss')");
        }
        if (entity.getCreateDateStart()!=null) {
            stringBuilder.append(" AND A.CREATE_DATE >= TO_DATE('" + DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss",entity.getCreateDateStart()) + "', 'yyyy-mm-dd hh24:mi:ss')");
        }
        if (entity.getCreateDateEnd()!=null) {
            stringBuilder.append(" AND A.CREATE_DATE <= TO_DATE('" + DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss",entity.getCreateDateEnd()) + "', 'yyyy-mm-dd hh24:mi:ss')");
        }
        if (BllContext.getBusinessType() == BusinessType.EXPORT) {
            if (BllContext.getRequestEntity().getActionType() == ActionType.ASYNC) {
                etmsBdTruckCertificateQzDao.asyncExportDefaultExcel(()->{
                    List<EtmsBdTruckCertificateSearchEntity> entities = DBHelper.selectList(stringBuilder.toString(), EtmsBdTruckCertificateSearchEntity.class);
                    entities.forEach(qzSearchEntity -> {
                        if (qzSearchEntity.getValidityDate() != null && DateUtils.parseDate(entity.getValidityDate().substring(0, 19)).compareTo(DateUtils.parseDate(DateUtils.getDate())) >= 0) {
                            qzSearchEntity.setCertificateStatus("有效");
                        } else {
                            qzSearchEntity.setCertificateStatus("无效");
                        }
                        if (qzSearchEntity.getIsGk().equals("Y")) {
                            qzSearchEntity.setIsGk("是");
                        } else {
                            qzSearchEntity.setIsGk("否");
                        }
                        qzSearchEntity.setIssueDate(qzSearchEntity.getIssueDate().substring(0, 10));
                        qzSearchEntity.setValidityDate(qzSearchEntity.getValidityDate().substring(0, 10));
                        qzSearchEntity.setModMark(qzSearchEntity.getModMark().equals("4") ? "是" : "否");
                    });
                    return entities;
                }, ExcelProcess.BuildConfig("车辆证件管理", EtmsBdTruckCertificateSearchEntity.class));
            } else {
                etmsBdTruckCertificateQzDao.exportDefaultExcel(() -> {
                    List<EtmsBdTruckCertificateSearchEntity> entities = DBHelper.selectList(stringBuilder.toString(), EtmsBdTruckCertificateSearchEntity.class);
                    entities.forEach(qzSearchEntity -> {
                        Date now=DateUtils.parseDate(DateUtils.getDate());
                        if (StringUtils.hasValue(qzSearchEntity.getValidityDate()) && DateUtils.parseDate(qzSearchEntity.getValidityDate().substring(0, 19)).compareTo(now) >= 0) {
                            qzSearchEntity.setCertificateStatus("有效");
                        } else {
                            qzSearchEntity.setCertificateStatus("无效");
                        }
                        if (qzSearchEntity.getIsGk().equals("Y")) {
                            qzSearchEntity.setIsGk("是");
                        } else {
                            qzSearchEntity.setIsGk("否");
                        }
                        qzSearchEntity.setIssueDate(qzSearchEntity.getIssueDate().substring(0, 10));
                        qzSearchEntity.setValidityDate(qzSearchEntity.getValidityDate().substring(0, 10));
                        qzSearchEntity.setModMark(qzSearchEntity.getModMark().equals("4") ? "是" : "否");
                    });
                    return entities;
                }, ExcelProcess.BuildConfig("车辆证件管理", EtmsBdTruckCertificateSearchEntity.class));
            }
            return new TgPageInfo<>();
        }else{
            startPage();
            List<EtmsBdTruckCertificateSearchEntity> entities = DBHelper.selectList(stringBuilder.toString(), EtmsBdTruckCertificateSearchEntity.class);
            entities.forEach(qzSearchEntity -> {
                if(qzSearchEntity.getIsGk().equals("Y")){
                    qzSearchEntity.setIsGk("是");
                }else{
                    qzSearchEntity.setIsGk("否");
                }
                qzSearchEntity.setModMark(qzSearchEntity.getModMark().equals("4") ? "是" : "否");
            });
            return EciQuery.getPageInfo(entities);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public EtmsBdTruckCertificateQzEntity save(EtmsBdTruckCertificateQzEntity entity) {
        // 返回实体对象
        EtmsBdTruckCertificateQzEntity etmsBdTruckCertificateQzEntity = null;
        etmsBdTruckCertificateQzVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setGuid(IdWorker.get32UUID());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setCreateDate(DateUtils.getNowDate());
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
            entity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            entity.setCompanyName(UserContext.getUserInfo().getCompanyName());
            entity.setNodeCode(UserContext.getUserInfo().getDeptCode());
            entity.setNodeName(UserContext.getUserInfo().getDeptName());
            entity.setModMark(Enums.ModMark.XZ.getValue());
            etmsBdTruckCertificateQzEntity = etmsBdTruckCertificateQzDao.insertOne(entity);

        }else{
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            entity.setUpdateDate(DateUtils.getNowDate());
            entity.setModMark(Enums.ModMark.XG.getValue());
            etmsBdTruckCertificateQzEntity = etmsBdTruckCertificateQzDao.updateByEntityId(entity);

        }
        return etmsBdTruckCertificateQzEntity;
    }

    @Override
    public List<EtmsBdTruckCertificateQzEntity> selectList(EtmsBdTruckCertificateQzEntity entity) {
        return etmsBdTruckCertificateQzDao.selectList(entity);
    }

    @Override
    public EtmsBdTruckCertificateQzEntity selectOneById(Serializable id) {
        return etmsBdTruckCertificateQzDao.selectById(id);
    }


    @Override
    public void insertBatch(List<EtmsBdTruckCertificateQzEntity> list) {
        etmsBdTruckCertificateQzDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return etmsBdTruckCertificateQzDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return etmsBdTruckCertificateQzDao.deleteById(id);
    }

}