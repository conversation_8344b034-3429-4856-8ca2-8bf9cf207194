package com.eci.project.omsOrderFwxmWorkFkZzfw.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.common.DictField;
import com.eci.common.ZsrBaseEntity;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.math.BigDecimal;
import java.util.Date;


/**
* 反馈内容-作业信息:其他增值服务信息对象 OMS_ORDER_FWXM_WORK_FK_ZZFW
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-05-22
*/
@ApiModel("反馈内容-作业信息:其他增值服务信息")
@TableName("OMS_ORDER_FWXM_WORK_FK_ZZFW")
@FieldNameConstants
public class OmsOrderFwxmWorkFkZzfwEntity extends ZsrBaseEntity{
    /**
    * 主键
    */
    @ApiModelProperty("主键(40)")
    @TableId("GUID")
    private String guid;

    /**
    * OMS订单号
    */
    @ApiModelProperty("OMS订单号(20)")
    @TableField("ORDER_NO")
    private String orderNo;

    /**
    * 协作委托单编号
    */
    @ApiModelProperty("协作委托单编号(36)")
    @TableField("XZWT_NO")
    private String xzwtNo;

    /**
    * OMS任务协助编号
    */
    @ApiModelProperty("OMS任务协助编号(50)")
    @TableField("WORK_NO")
    private String workNo;

    /**
    * OMS服务项目
    */
    @ApiModelProperty("OMS服务项目(20)")
    @TableField("FWXM_CODE")
    private String fwxmCode;

    /**
    * 作业事项
    */
    @ApiModelProperty("作业事项(20)")
    @TableField("OP_ITEM")
    @DictField(queryKey = "OMS_TASK_MATTER")
    private String opItem;

    /**
    * 作业属性
    */
    @ApiModelProperty("作业属性(20)")
    @TableField("OP_PROPERTY")
    @DictField(queryKey = "OMS_TASK_ATTR")
    private String opProperty;

    /**
    * 计量单位
    */
    @ApiModelProperty("计量单位(20)")
    @TableField("UNIT")
    @DictField(queryKey = "OMS_BD_CHARGE_UNIT")
    private String unit;

    /**
    * 实际作业量
    */
    @ApiModelProperty("实际作业量(22)")
    @TableField("OP_QTY_REAL")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal opQtyReal;

    /**
    * 备注
    */
    @ApiModelProperty("备注(200)")
    @TableField("MEMO")
    private String memo;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 最后修改日期
    */
    @ApiModelProperty("最后修改日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("最后修改日期开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("最后修改日期结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(200)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 业务数据唯一注册编号
    */
    @ApiModelProperty("业务数据唯一注册编号(50)")
    @TableField("BIZ_REG_ID")
    private String bizRegId;

    /**
    * 入出库交接单号
    */
    @ApiModelProperty("入出库交接单号(50)")
    @TableField("JJ_NO_RCK")
    private String jjNoRck;

    /**
    * 行号
    */
    @ApiModelProperty("行号(22)")
    @TableField("LINE_NUM")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal lineNum;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public OmsOrderFwxmWorkFkZzfwEntity() {
        this.setSubClazz(OmsOrderFwxmWorkFkZzfwEntity.class);
    }

    public OmsOrderFwxmWorkFkZzfwEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public OmsOrderFwxmWorkFkZzfwEntity setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        this.nodifySetFiled("orderNo", orderNo);
        return this;
    }

    public String getOrderNo() {
        this.nodifyGetFiled("orderNo");
        return orderNo;
    }

    public OmsOrderFwxmWorkFkZzfwEntity setXzwtNo(String xzwtNo) {
        this.xzwtNo = xzwtNo;
        this.nodifySetFiled("xzwtNo", xzwtNo);
        return this;
    }

    public String getXzwtNo() {
        this.nodifyGetFiled("xzwtNo");
        return xzwtNo;
    }

    public OmsOrderFwxmWorkFkZzfwEntity setWorkNo(String workNo) {
        this.workNo = workNo;
        this.nodifySetFiled("workNo", workNo);
        return this;
    }

    public String getWorkNo() {
        this.nodifyGetFiled("workNo");
        return workNo;
    }

    public OmsOrderFwxmWorkFkZzfwEntity setFwxmCode(String fwxmCode) {
        this.fwxmCode = fwxmCode;
        this.nodifySetFiled("fwxmCode", fwxmCode);
        return this;
    }

    public String getFwxmCode() {
        this.nodifyGetFiled("fwxmCode");
        return fwxmCode;
    }

    public OmsOrderFwxmWorkFkZzfwEntity setOpItem(String opItem) {
        this.opItem = opItem;
        this.nodifySetFiled("opItem", opItem);
        return this;
    }

    public String getOpItem() {
        this.nodifyGetFiled("opItem");
        return opItem;
    }

    public OmsOrderFwxmWorkFkZzfwEntity setOpProperty(String opProperty) {
        this.opProperty = opProperty;
        this.nodifySetFiled("opProperty", opProperty);
        return this;
    }

    public String getOpProperty() {
        this.nodifyGetFiled("opProperty");
        return opProperty;
    }

    public OmsOrderFwxmWorkFkZzfwEntity setUnit(String unit) {
        this.unit = unit;
        this.nodifySetFiled("unit", unit);
        return this;
    }

    public String getUnit() {
        this.nodifyGetFiled("unit");
        return unit;
    }

    public OmsOrderFwxmWorkFkZzfwEntity setOpQtyReal(BigDecimal opQtyReal) {
        this.opQtyReal = opQtyReal;
        this.nodifySetFiled("opQtyReal", opQtyReal);
        return this;
    }

    public BigDecimal getOpQtyReal() {
        this.nodifyGetFiled("opQtyReal");
        return opQtyReal;
    }

    public OmsOrderFwxmWorkFkZzfwEntity setMemo(String memo) {
        this.memo = memo;
        this.nodifySetFiled("memo", memo);
        return this;
    }

    public String getMemo() {
        this.nodifyGetFiled("memo");
        return memo;
    }

    public OmsOrderFwxmWorkFkZzfwEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public OmsOrderFwxmWorkFkZzfwEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public OmsOrderFwxmWorkFkZzfwEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public OmsOrderFwxmWorkFkZzfwEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public OmsOrderFwxmWorkFkZzfwEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public OmsOrderFwxmWorkFkZzfwEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public OmsOrderFwxmWorkFkZzfwEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public OmsOrderFwxmWorkFkZzfwEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public OmsOrderFwxmWorkFkZzfwEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public OmsOrderFwxmWorkFkZzfwEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public OmsOrderFwxmWorkFkZzfwEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public OmsOrderFwxmWorkFkZzfwEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public OmsOrderFwxmWorkFkZzfwEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public OmsOrderFwxmWorkFkZzfwEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public OmsOrderFwxmWorkFkZzfwEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public OmsOrderFwxmWorkFkZzfwEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public OmsOrderFwxmWorkFkZzfwEntity setBizRegId(String bizRegId) {
        this.bizRegId = bizRegId;
        this.nodifySetFiled("bizRegId", bizRegId);
        return this;
    }

    public String getBizRegId() {
        this.nodifyGetFiled("bizRegId");
        return bizRegId;
    }

    public OmsOrderFwxmWorkFkZzfwEntity setJjNoRck(String jjNoRck) {
        this.jjNoRck = jjNoRck;
        this.nodifySetFiled("jjNoRck", jjNoRck);
        return this;
    }

    public String getJjNoRck() {
        this.nodifyGetFiled("jjNoRck");
        return jjNoRck;
    }

    public OmsOrderFwxmWorkFkZzfwEntity setLineNum(BigDecimal lineNum) {
        this.lineNum = lineNum;
        this.nodifySetFiled("lineNum", lineNum);
        return this;
    }

    public BigDecimal getLineNum() {
        this.nodifyGetFiled("lineNum");
        return lineNum;
    }

}
