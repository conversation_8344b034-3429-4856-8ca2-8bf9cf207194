package com.eci.project.omsOrderFwxmCrkZzfw.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.omsOrderFwxmCrkZzfw.service.OmsOrderFwxmCrkZzfwService;
import com.eci.project.omsOrderFwxmCrkZzfw.entity.OmsOrderFwxmCrkZzfwEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 委托内容-仓储-增值服务明细Controller
*
* @<NAME_EMAIL>
* @date 2025-05-21
*/
@Api(tags = "委托内容-仓储-增值服务明细")
@RestController
@RequestMapping("/omsOrderFwxmCrkZzfw")
public class OmsOrderFwxmCrkZzfwController extends EciBaseController {

    @Autowired
    private OmsOrderFwxmCrkZzfwService omsOrderFwxmCrkZzfwService;


    @ApiOperation("委托内容-仓储-增值服务明细:保存")
    @EciLog(title = "委托内容-仓储-增值服务明细:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody OmsOrderFwxmCrkZzfwEntity entity){
        OmsOrderFwxmCrkZzfwEntity omsOrderFwxmCrkZzfwEntity =omsOrderFwxmCrkZzfwService.save(entity);
        return ResponseMsgUtil.success(10001,omsOrderFwxmCrkZzfwEntity);
    }


    @ApiOperation("委托内容-仓储-增值服务明细:查询列表")
    @EciLog(title = "委托内容-仓储-增值服务明细:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody OmsOrderFwxmCrkZzfwEntity entity){
        List<OmsOrderFwxmCrkZzfwEntity> omsOrderFwxmCrkZzfwEntities = omsOrderFwxmCrkZzfwService.selectList(entity);
        return ResponseMsgUtil.success(10001,omsOrderFwxmCrkZzfwEntities);
    }


    @ApiOperation("委托内容-仓储-增值服务明细:分页查询列表")
    @EciLog(title = "委托内容-仓储-增值服务明细:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody OmsOrderFwxmCrkZzfwEntity entity){
        TgPageInfo tgPageInfo = omsOrderFwxmCrkZzfwService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("委托内容-仓储-增值服务明细:根据ID查一条")
    @EciLog(title = "委托内容-仓储-增值服务明细:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody OmsOrderFwxmCrkZzfwEntity entity){
        OmsOrderFwxmCrkZzfwEntity  omsOrderFwxmCrkZzfwEntity = omsOrderFwxmCrkZzfwService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,omsOrderFwxmCrkZzfwEntity);
    }


    @ApiOperation("委托内容-仓储-增值服务明细:根据ID删除一条")
    @EciLog(title = "委托内容-仓储-增值服务明细:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody OmsOrderFwxmCrkZzfwEntity entity){
        int count = omsOrderFwxmCrkZzfwService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("委托内容-仓储-增值服务明细:根据ID字符串删除多条")
    @EciLog(title = "委托内容-仓储-增值服务明细:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody OmsOrderFwxmCrkZzfwEntity entity) {
        int count = omsOrderFwxmCrkZzfwService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}