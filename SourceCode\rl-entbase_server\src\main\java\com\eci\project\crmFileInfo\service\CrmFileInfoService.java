package com.eci.project.crmFileInfo.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eci.common.BaseProperties;
import com.eci.common.utils.ZipUtil;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.crmFileInfo.dao.CrmFileInfoDao;
import com.eci.project.crmFileInfo.entity.CrmFileInfoEntity;
import com.eci.project.crmFileInfo.validate.CrmFileInfoVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
* 附件Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-05-21
*/
@Service
@Slf4j
public class CrmFileInfoService implements EciBaseService<CrmFileInfoEntity> {

    @Autowired
    private CrmFileInfoDao crmFileInfoDao;

    @Autowired
    private CrmFileInfoVal crmFileInfoVal;


    @Override
    public TgPageInfo queryPageList(CrmFileInfoEntity entity) {
        EciQuery<CrmFileInfoEntity> eciQuery = EciQuery.buildQuery(entity);
        List<CrmFileInfoEntity> entities = crmFileInfoDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public CrmFileInfoEntity save(CrmFileInfoEntity entity) {
        // 返回实体对象
        CrmFileInfoEntity crmFileInfoEntity = null;
        crmFileInfoVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            crmFileInfoEntity = crmFileInfoDao.insertOne(entity);

        }else{

            crmFileInfoEntity = crmFileInfoDao.updateByEntityId(entity);

        }
        return crmFileInfoEntity;
    }

    @Override
    public List<CrmFileInfoEntity> selectList(CrmFileInfoEntity entity) {
        QueryWrapper query=new QueryWrapper();
        query.select("(select Name from FZGJ_CRM_FILE_TYPE B where B.CODE=A.FILE_TYPE AND B.GROUP_CODE=A.GROUP_CODE) fileTypeName ",
                "A.*");
        query.eq("FILE_NO",entity.getFileNo());
        return crmFileInfoDao.selectList(query);
    }

    @Override
    public CrmFileInfoEntity selectOneById(Serializable id) {
        return crmFileInfoDao.selectById(id);
    }


    @Override
    public void insertBatch(List<CrmFileInfoEntity> list) {
        crmFileInfoDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return crmFileInfoDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return crmFileInfoDao.deleteById(id);
    }

    /**
     * <AUTHOR>
     * @Description 获取压缩流
     * @Date  2025/5/23 11:19
     * @Param [fileNo]
     * @return com.sun.xml.internal.messaging.saaj.util.ByteOutputStream
     **/
    public byte[]  Compression(String fileNo) throws IOException {
        CrmFileInfoEntity entity=new CrmFileInfoEntity();
        entity.setFileNo(fileNo);
        List<CrmFileInfoEntity> list = selectList(entity);
        List<File> files=new ArrayList<>();
        list.forEach(p->{
            String basePath = BaseProperties.getFilepath()+p.getFilePath();
            File file=new File(basePath);
            if(file.exists()) files.add(file);
        });
        ZipUtil.Compression(files,"D:\\test.zip");
        byte[] compressionToBytes = ZipUtil.CompressionToBytes(files);
        return compressionToBytes;
    }
}