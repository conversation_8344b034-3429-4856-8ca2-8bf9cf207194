package com.eci.project.fzgjCrmEnterpriseService.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjCrmEnterpriseService.entity.FzgjCrmEnterpriseServiceEntity;

import org.springframework.stereotype.Service;


/**
* 注册企业服务Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-03-13
*/
@Service
public class FzgjCrmEnterpriseServiceVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjCrmEnterpriseServiceEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjCrmEnterpriseServiceEntity entity, BusinessType businessType) {

    }

}
