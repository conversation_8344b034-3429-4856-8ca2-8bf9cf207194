package com.eci.project.omsOrderFwxmWorkFkHzqd.entity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * 反馈接单状态的请求对象。
 * 用于将接单结果（确认/拒绝）及相关信息反馈给OMS系统。
 */
@Data
public class FeedbackOrderAcceptance {

    /**
     * OMS协作任务编号。
     * 必填。
     */
    private String WORK_NO;

    /**
     * OMS订单号。
     * 必填。
     */
    private String ORDER_NO;

    /**
     * 受理结果。
     * 必填。可选值通常为 "确认接单" 或 "拒绝接单"。
     */
    private String RESULT;

    /**
     * 作业系统代码。
     * 必填。
     */
    private String SYS_CODE;

    /**
     * 作业系统单据类型。
     * 必填。
     */
    private String DOC_TYPE;

    /**
     * 作业系统单据编号。
     * 可选。当 RESULT 为 "拒绝接单" 时，此字段应为空。
     */
    private String DOC_NO;

    /**
     * 传输时间。
     * 可选。格式通常为 "yyyy-MM-dd HH:mm:ss"。
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date TRN_DATE;
}