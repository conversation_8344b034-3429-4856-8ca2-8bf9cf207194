package com.eci.project.fzgjBaseDataDetail.service;

import com.eci.crud.service.EciBaseService;
import com.eci.project.fzgjBaseDataDetail.entity.FzgjBaseDataDetailEntity;
import com.eci.wu.core.DataTable;


/**
* 扩展基础资料明细Service接口
* 业务逻辑层, 接口代码, 只需要写自定义的部分, 增删改查部分在父级接口已经实现
* @<NAME_EMAIL>
* @date 2025-03-18
*/
public interface IFzgjBaseDataDetailService extends EciBaseService<FzgjBaseDataDetailEntity> {
    /**
     * <AUTHOR>
     * @Description 获取扩展信息，返回一个code,name的table
     * @Date  2025/4/30 9:59
     * @Param [groupCode, status]
     * @return com.eci.wu.core.DataTable
     **/
    public DataTable getInfo(String groupCode,String status);

}
