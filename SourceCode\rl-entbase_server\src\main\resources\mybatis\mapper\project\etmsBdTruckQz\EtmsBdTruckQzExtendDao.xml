<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.etmsBdTruckQz.dao.EtmsBdTruckQzExtendDao">
    <resultMap type="EtmsBdTruckQzExtendEntity" id="EtmsBdTruckQzExtendResult">
        <result property="guid" column="GUID"/>
        <result property="truckSpceGuid" column="TRUCK_SPCE_GUID"/>
        <result property="truckNo" column="TRUCK_NO"/>
        <result property="isGk" column="IS_GK"/>
        <result property="partnerGuid" column="PARTNER_GUID"/>
        <result property="gpsMode" column="GPS_MODE"/>
        <result property="gpsNo" column="GPS_NO"/>
        <result property="driverGuid" column="DRIVER_GUID"/>
        <result property="status" column="STATUS"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createCompany" column="CREATE_COMPANY"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="memo" column="MEMO"/>
        <result property="isUser" column="IS_USER"/>
        <result property="llOil" column="LL_OIL"/>
        <result property="licenseDate" column="LICENSE_DATE"/>
        <result property="ratingDate" column="RATING_DATE"/>
        <result property="operationDate" column="OPERATION_DATE"/>
        <result property="carType" column="CAR_TYPE"/>
        <result property="reside" column="RESIDE"/>
        <result property="link" column="LINK"/>
        <result property="tel" column="TEL"/>
        <result property="truckVin" column="TRUCK_VIN"/>
        <result property="tlength" column="TLENGTH"/>
        <result property="tweight" column="TWEIGHT"/>
        <result property="trailerNo" column="TRAILER_NO"/>
        <result property="attributeCode" column="ATTRIBUTE_CODE"/>
        <result property="orgDepId" column="ORG_DEP_ID"/>
        <result property="orgDepCode" column="ORG_DEP_CODE"/>
        <result property="orgDepName" column="ORG_DEP_NAME"/>
        <result property="ssoCompanyGuid" column="SSO_COMPANY_GUID"/>
        <result property="orgCode" column="ORG_CODE"/>
        <result property="orgName" column="ORG_NAME"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="zbzlKg" column="ZBZL_KG"/>
        <result property="cllx" column="CLLX"/>
        <result property="clcc" column="CLCC"/>
        <result property="carColor" column="CAR_COLOR"/>
        <result property="checkStatus" column="CHECK_STATUS"/>
        <result property="checkUser" column="CHECK_USER"/>
        <result property="checkRmk" column="CHECK_RMK"/>
        <result property="truckType" column="TRUCK_TYPE"/>
        <result property="carLong" column="CAR_LONG"/>
        <result property="carLongType" column="CAR_LONG_TYPE"/>
        <result property="isBt" column="IS_BT"/>
    </resultMap>

    <sql id="selectEtmsBdTruckQzEntityVo">
        select
            GUID,
            TRUCK_SPCE_GUID,
            TRUCK_NO,
            IS_GK,
            PARTNER_GUID,
            GPS_MODE,
            GPS_NO,
            DRIVER_GUID,
            STATUS,
            CREATE_DATE,
            CREATE_USER,
            CREATE_COMPANY,
            UPDATE_DATE,
            UPDATE_USER,
            MEMO,
            IS_USER,
            LL_OIL,
            LICENSE_DATE,
            RATING_DATE,
            OPERATION_DATE,
            CAR_TYPE,
            RESIDE,
            LINK,
            TEL,
            TRUCK_VIN,
            TLENGTH,
            TWEIGHT,
            TRAILER_NO,
            ATTRIBUTE_CODE,
            ORG_DEP_ID,
            ORG_DEP_CODE,
            ORG_DEP_NAME,
            SSO_COMPANY_GUID,
            ORG_CODE,
            ORG_NAME,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            CREATE_USER_NAME,
            UPDATE_USER_NAME,
            ZBZL_KG,
            CLLX,
            CLCC,
            CAR_COLOR,
            CHECK_STATUS,
            CHECK_USER,
            CHECK_RMK,
            TRUCK_TYPE,
            CAR_LONG,
            CAR_LONG_TYPE,
            IS_BT
        from ETMS_BD_TRUCK_QZ
    </sql>
    <select id="selectListInfo" parameterType="EtmsBdTruckQzExtendEntity" resultMap="EtmsBdTruckQzExtendResult">
        SELECT A.GUID,A.DRIVER_GUID,
        A.ATTRIBUTE_CODE,
        A.TRUCK_NO AS TRUCK_NO,
        A.CLLX,
        A.CLCC,
        (SELECT NAME FROM (SELECT NAME,CODE FROM ETMS_BD_TRUCK_TYPE_CUSTOM) WHERE CODE = A.CLLX AND ROWNUM = 1) NEW_CLLX,
        (SELECT NAME FROM (SELECT NAME,CODE FROM ETMS_BD_TRUCK_SPEC_CUSTOM) WHERE CODE = A.CLCC AND ROWNUM = 1) NEW_CLCC,
        A.IS_GK,
        (SELECT X.NAME
        FROM ETMS_CRM_PARTNER X
        WHERE X.GUID = A.PARTNER_GUID
        AND X.GROUP_CODE = A.GROUP_CODE) PARTNER_GUID,
        (SELECT X.NAME FROM ETMS_BD_DRIVER X WHERE X.GUID = A.DRIVER_GUID) DRIVER_NAME,
        A.GPS_MODE,
        A.TRAILER_NO,
        A.GPS_NO,
        A.STATUS,
        A.IS_USER,
        A.MEMO,
        A.LL_OIL,
        A.CREATE_DATE,
        A.CREATE_USER,
        A.CREATE_USER_NAME,
        NVL(ETMS_F_NAME(A.CREATE_COMPANY, 'HHY_COMPANY_NAME'),
        ETMS_F_NAME(A.CREATE_COMPANY, 'EP')) AS CREATE_COMPANY_NAME,
        UPDATE_DATE,
        A.UPDATE_USER,
        A.UPDATE_USER_NAME,
        A.LICENSE_DATE,
        A.RATING_DATE,
        A.OPERATION_DATE,
        A.ORG_DEP_NAME,
        A.ORG_DEP_CODE,
        (CASE
        WHEN (SELECT COUNT(*) FROM ETMS_OP_FILE_QZ T WHERE T.OP_NO = A.GUID) > 0 THEN
        '是'
        ELSE
        '否'
        END) DRIVER_ATT,
        A.CHECK_STATUS,
        A.CHECK_RMK
        FROM ETMS_BD_TRUCK_QZ A
        WHERE 1 = 1
        <if test="truckNo != null and truckNo != ''">
            AND A.TRUCK_NO like '%' || #{truckNo} || '%'
        </if>
        <if test="driverGuid != null and driverGuid != ''">
            AND A.DRIVER_GUID=(SELECT GUID FROM ETMS_BD_DRIVER X WHERE USER_ID=#{driverGuid})
        </if>
        <if test="gpsMode != null and gpsMode != ''">
            AND A.GPS_MODE = #{gpsMode}
        </if>
        <if test="driverAtt != null and driverAtt != ''">
            <choose>
                <when test="driverAtt =='Y'">
                    AND EXISTS(SELECT 1 FROM ETMS_OP_FILE_QZ T WHERE T.OP_NO=A.GUID)
                </when>
                <otherwise>
                    AND NOT EXISTS(SELECT 1 FROM ETMS_OP_FILE_QZ T WHERE T.OP_NO=A.GUID)
                </otherwise>
            </choose>
        </if>
        <if test="gpsNo != null and gpsNo != ''">
            AND A.GPS_NO like '%' || #{gpsNo} || '%'
        </if>
        <if test="status != null and status != ''">
            AND A.STATUS = #{status}
        </if>
        <if test="isUser != null and isUser != ''">
            AND A.IS_USER = #{isUser}
        </if>
        <if test="createCompany != null and createCompany != ''">
            AND A.CREATE_COMPANY = #{createCompany}
        </if>
        <if test="checkStatus != null and checkStatus != ''">
            AND A.CHECK_STATUS = #{checkStatus}
        </if>
        order by A.create_date desc
    </select>
    <select id="selectByOneId" parameterType="String" resultMap="EtmsBdTruckQzExtendResult">
        SELECT A.GUID,A.ZBZL_KG,
               (SELECT CODE||'|'||NAME FROM (SELECT NAME,CODE FROM ETMS_BD_TRUCK_TYPE_CUSTOM) WHERE CODE = A.CLLX AND ROWNUM = 1) CLLX,
               (SELECT CODE||'|'||NAME FROM (SELECT CODE,TRUCK_TYPE,NAME FROM ETMS_BD_TRUCK_SPEC_CUSTOM) WHERE TRUCK_TYPE = A.CLCC AND ROWNUM = 1) CLCC,
               A.CAR_COLOR,
               A.ATTRIBUTE_CODE,
               A.TRUCK_NO,
               A.IS_GK,
               A.PARTNER_GUID,
               (SELECT X.CODE||'|'||X.MAPPING_ABBREVIATION FROM ETMS_CRM_PARTNER X WHERE X.GUID=A.PARTNER_GUID AND X.GROUP_CODE=A.GROUP_CODE) PARTNER_NAME,
               (SELECT X.USER_ID||'|'||X.NAME FROM ETMS_BD_DRIVER X WHERE X.GUID=A.DRIVER_GUID) DRIVER_NAME,
               A.DRIVER_GUID,
               nvl(A.GPS_MODE,'0') AS GPS_MODE,
               A.TRAILER_NO,
               A.GPS_MODE,
               A.GPS_NO,
               A.STATUS,
               A.IS_USER,
               A.MEMO,
               A.LL_OIL,
               A.CREATE_DATE,
               A.CREATE_USER,
               A.CREATE_USER_NAME,
               A.CREATE_COMPANY,
               UPDATE_DATE,
               A.UPDATE_USER,
               A.UPDATE_USER_NAME,
               A.LICENSE_DATE,A.RATING_DATE,A.OPERATION_DATE,
               (SELECT CODE||'|'||NAME FROM (SELECT NAME,CODE FROM ETMS_BD_TRUCK_TYPE_CUSTOM) WHERE CODE = A.TRUCK_TYPE AND ROWNUM = 1) TRUCK_TYPE,
               (SELECT CODE||'|'||NAME FROM (SELECT CODE,TRUCK_TYPE,NAME FROM ETMS_BD_TRUCK_SPEC_CUSTOM) WHERE TRUCK_TYPE = A.TRUCK_SPCE_GUID AND ROWNUM = 1) TRUCK_SPCE_GUID,
               A.ORG_DEP_NAME,A.ORG_DEP_CODE,
               A.CHECK_STATUS,
               A.CAR_LONG,A.CAR_LONG_TYPE,
               A.IS_BT
        FROM ETMS_BD_TRUCK_QZ A
        WHERE 1 = 1 AND A.GUID = #{id}
    </select>
</mapper>
