package com.eci.project.omsOrderFwxmWorkFkHzqd.entity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 反馈-作业数据
 */
@Data
public class FeedbackCollaborationTaskEntity {

    private String ORDER_NO; // OMS订单号
    private String WORK_NO; // 协作任务编号，必填
    private String DEC_NO; // 报关单号
    private BigDecimal DEC_TABLE_NUM; // 报关单明细行数
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date D_DATE; // 申报日期
    private String CHECKBILL_NO; // 关联编号（核注清单号、报关单号、出入库单号）
    private String BJ_TYPE; // 报检类别
    private String JYJY_NO; // 报检号
    private String YDH; // 运单号
    private String FDH; // 分单号
    private String XDH; // 箱单号
    private BigDecimal BGD_ZS; // 报关单张数
    private String ZGDH; // 转关单号
    private String DEC_NO_XT; // 关联报关单号
    private BigDecimal CHECKBILL_TABLE_NUM; // 核注清单明细行数
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date HK_DATE; // 核扣日期
    private String CRKDH; // 出入库单号
    private String CONTRACT_NO; // 合同号
    private String ORIGIN_ARRIVAL_COUNTRY; // 起运国/运抵国
    private String CONTAINER_NO; // 集装箱号
    private String SUPERVISION_MODE; // 监管方式
    private String IS_CHECK; // 是否查验
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date HZQD_DATE; // 核注清单申报日期
    private String CYLX; // 查验类型
    private String I_E_TYPE; // 进出标志：I进E出
    private String I_E_PORT; // 进/出境关别
    private String CUSTOM_CODE; // 申报地海关/主管关区
    private String TRADE_CODE; // 区内企业
    private String YSFS; // 运输方式
    private String JNSFHR; // 境内收发货人
    private String IS_JYJY; // 是否检验检疫YN
    private String PACK_TYPE; // 包装种类（新的代码）
    private String BGD_BG_TYPE; // 企业报关类型
    private String HZQD_BG_TYPE; // (核注清单)报关类型
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date BILL_DATE; // 制单日期
    private String HDCS; // 货代/厂商
    private String BG_FLAG; // 报关标志
    private String TRADE_CODE_OUT; // 区外企业
    private String DEC_TRADE_MODE; // 贸易方式
    private String IS_SD; // 删单标志YN
    private String IS_GD; // 改单标志YN
    private BigDecimal LDS; // 联单数
    private String QS_MAN; // 签收人
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date QS_DATE; // 签收时间
    private String BILL_MAN; // 制单人
    private String TRADE_COUNTRY; // 贸易国
    private String BGD_MEMO; // 制单备注
    private String IS_BJ; // 是否报检

    private List<BodyItem> BODY; // 报关单明细列表
    private List<Attachment> ATTACHMENT_LIST; // 附件列表

}