package com.eci.project.fzgjBdOpType.service;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjBdOpType.dao.FzgjBdOpTypeDao;
import com.eci.project.fzgjBdOpType.entity.FzgjBdOpTypeEntity;
import com.eci.project.fzgjBdOpType.validate.FzgjBdOpTypeVal;

import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 业务类型Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-03-27
*/
@Service
@Slf4j
public class FzgjBdOpTypeService implements EciBaseService<FzgjBdOpTypeEntity> {

    @Autowired
    private FzgjBdOpTypeDao fzgjBdOpTypeDao;

    @Autowired
    private FzgjBdOpTypeVal fzgjBdOpTypeVal;


    @Override
    public TgPageInfo queryPageList(FzgjBdOpTypeEntity entity) {
        EciQuery<FzgjBdOpTypeEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjBdOpTypeEntity> entities = fzgjBdOpTypeDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjBdOpTypeEntity save(FzgjBdOpTypeEntity entity) {
        // 返回实体对象
        FzgjBdOpTypeEntity fzgjBdOpTypeEntity = null;
        fzgjBdOpTypeVal.saveValidate(entity,BllContext.getBusinessType());
        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setGuid(IdWorker.getIdStr());
            entity.setCreateDate(new java.util.Date());
            entity.setUpdateDate(new java.util.Date());
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
        } else {
            entity.setUpdateDate(new java.util.Date());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
        }
        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjBdOpTypeEntity = fzgjBdOpTypeDao.insertOne(entity);

        }else{

            fzgjBdOpTypeEntity = fzgjBdOpTypeDao.updateByEntityId(entity);

        }
        return fzgjBdOpTypeEntity;
    }

    @Override
    public List<FzgjBdOpTypeEntity> selectList(FzgjBdOpTypeEntity entity) {
        return fzgjBdOpTypeDao.selectList(entity);
    }

    @Override
    public FzgjBdOpTypeEntity selectOneById(Serializable id) {
        return fzgjBdOpTypeDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjBdOpTypeEntity> list) {
        fzgjBdOpTypeDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjBdOpTypeDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjBdOpTypeDao.deleteById(id);
    }

}