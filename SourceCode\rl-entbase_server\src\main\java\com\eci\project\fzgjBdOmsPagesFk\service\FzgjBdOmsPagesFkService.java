package com.eci.project.fzgjBdOmsPagesFk.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjBdOmsPagesFk.dao.FzgjBdOmsPagesFkDao;
import com.eci.project.fzgjBdOmsPagesFk.entity.FzgjBdOmsPagesFkEntity;
import com.eci.project.fzgjBdOmsPagesFk.validate.FzgjBdOmsPagesFkVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 订单反馈页面Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-14
*/
@Service
@Slf4j
public class FzgjBdOmsPagesFkService implements EciBaseService<FzgjBdOmsPagesFkEntity> {

    @Autowired
    private FzgjBdOmsPagesFkDao fzgjBdOmsPagesFkDao;

    @Autowired
    private FzgjBdOmsPagesFkVal fzgjBdOmsPagesFkVal;


    @Override
    public TgPageInfo queryPageList(FzgjBdOmsPagesFkEntity entity) {
        EciQuery<FzgjBdOmsPagesFkEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjBdOmsPagesFkEntity> entities = fzgjBdOmsPagesFkDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjBdOmsPagesFkEntity save(FzgjBdOmsPagesFkEntity entity) {
        // 返回实体对象
        FzgjBdOmsPagesFkEntity fzgjBdOmsPagesFkEntity = null;
        fzgjBdOmsPagesFkVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjBdOmsPagesFkEntity = fzgjBdOmsPagesFkDao.insertOne(entity);

        }else{

            fzgjBdOmsPagesFkEntity = fzgjBdOmsPagesFkDao.updateByEntityId(entity);

        }
        return fzgjBdOmsPagesFkEntity;
    }

    @Override
    public List<FzgjBdOmsPagesFkEntity> selectList(FzgjBdOmsPagesFkEntity entity) {
        return fzgjBdOmsPagesFkDao.selectList(entity);
    }

    @Override
    public FzgjBdOmsPagesFkEntity selectOneById(Serializable id) {
        return fzgjBdOmsPagesFkDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjBdOmsPagesFkEntity> list) {
        fzgjBdOmsPagesFkDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjBdOmsPagesFkDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjBdOmsPagesFkDao.deleteById(id);
    }

}