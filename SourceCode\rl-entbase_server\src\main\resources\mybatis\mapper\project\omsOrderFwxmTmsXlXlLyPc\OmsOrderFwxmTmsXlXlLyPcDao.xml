<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.omsOrderFwxmTmsXlXlLyPc.dao.OmsOrderFwxmTmsXlXlLyPcDao">
    <resultMap type="OmsOrderFwxmTmsXlXlLyPcEntity" id="OmsOrderFwxmTmsXlXlLyPcResult">
        <result property="guid" column="GUID"/>
        <result property="preNo" column="PRE_NO"/>
        <result property="orderNo" column="ORDER_NO"/>
        <result property="tmsNo" column="TMS_NO"/>
        <result property="lineNo" column="LINE_NO"/>
        <result property="lyNo" column="LY_NO"/>
        <result property="seqNo" column="SEQ_NO"/>
        <result property="zpSeqNo" column="ZP_SEQ_NO"/>
        <result property="bpSeqNo" column="BP_SEQ_NO"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
    </resultMap>

    <sql id="selectOmsOrderFwxmTmsXlXlLyPcEntityVo">
        select
            GUID,
            PRE_NO,
            ORDER_NO,
            TMS_NO,
            LINE_NO,
            LY_NO,
            SEQ_NO,
            ZP_SEQ_NO,
            BP_SEQ_NO,
            CREATE_USER,
            CREATE_USER_NAME,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_USER_NAME,
            UPDATE_DATE,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME
        from OMS_ORDER_FWXM_TMS_XL_XL_LY_PC
    </sql>
</mapper>