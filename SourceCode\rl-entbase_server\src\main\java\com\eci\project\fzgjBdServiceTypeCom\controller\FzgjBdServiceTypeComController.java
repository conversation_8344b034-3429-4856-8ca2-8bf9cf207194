package com.eci.project.fzgjBdServiceTypeCom.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjBdServiceTypeCom.service.FzgjBdServiceTypeComService;
import com.eci.project.fzgjBdServiceTypeCom.entity.FzgjBdServiceTypeComEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 企业级服务类型Controller
*
* @<NAME_EMAIL>
* @date 2025-03-13
*/
@Api(tags = "企业级服务类型")
@RestController
@RequestMapping("/fzgjBdServiceTypeCom")
public class FzgjBdServiceTypeComController extends EciBaseController {

    @Autowired
    private FzgjBdServiceTypeComService fzgjBdServiceTypeComService;


    @ApiOperation("企业级服务类型:保存")
    @EciLog(title = "企业级服务类型:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody FzgjBdServiceTypeComEntity entity){
        FzgjBdServiceTypeComEntity fzgjBdServiceTypeComEntity =fzgjBdServiceTypeComService.save(entity);
        return ResponseMsgUtil.success(10001,fzgjBdServiceTypeComEntity);
    }


    @ApiOperation("企业级服务类型:查询列表")
    @EciLog(title = "企业级服务类型:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjBdServiceTypeComEntity entity){
        List<FzgjBdServiceTypeComEntity> fzgjBdServiceTypeComEntities = fzgjBdServiceTypeComService.selectList(entity);
        return ResponseMsgUtil.success(10001,fzgjBdServiceTypeComEntities);
    }


    @ApiOperation("企业级服务类型:分页查询列表")
    @EciLog(title = "企业级服务类型:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjBdServiceTypeComEntity entity){
        TgPageInfo tgPageInfo = fzgjBdServiceTypeComService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("企业级服务类型:根据ID查一条")
    @EciLog(title = "企业级服务类型:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjBdServiceTypeComEntity entity){
        FzgjBdServiceTypeComEntity  fzgjBdServiceTypeComEntity = fzgjBdServiceTypeComService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,fzgjBdServiceTypeComEntity);
    }


    @ApiOperation("企业级服务类型:根据ID删除一条")
    @EciLog(title = "企业级服务类型:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjBdServiceTypeComEntity entity){
        int count = fzgjBdServiceTypeComService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("企业级服务类型:根据ID字符串删除多条")
    @EciLog(title = "企业级服务类型:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjBdServiceTypeComEntity entity) {
        int count = fzgjBdServiceTypeComService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}