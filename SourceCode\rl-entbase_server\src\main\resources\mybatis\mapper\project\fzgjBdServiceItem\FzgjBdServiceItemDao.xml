<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.fzgjBdServiceItem.dao.FzgjBdServiceItemDao">
    <resultMap type="FzgjBdServiceItemEntity" id="FzgjBdServiceItemResult">
        <result property="guid" column="GUID"/>
        <result property="code" column="CODE"/>
        <result property="name" column="NAME"/>
        <result property="status" column="STATUS"/>
        <result property="seq" column="SEQ"/>
        <result property="memo" column="MEMO"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="ownedCompany" column="OWNED_COMPANY"/>
        <result property="parentid" column="PARENTID"/>
        <result property="ownedService" column="OWNED_SERVICE"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="pageUrl" column="PAGE_URL"/>
        <result property="selectType" column="SELECT_TYPE"/>
        <result property="ysfs" column="YSFS"/>
        <result property="sysCode" column="SYS_CODE"/>
        <result property="jdBill" column="JD_BILL"/>
        <result property="czBill" column="CZ_BILL"/>
        <result property="feedbackUrl" column="FEEDBACK_URL"/>
        <result property="isZy" column="IS_ZY"/>
        <result property="enName" column="EN_NAME"/>
        <result property="isCsc" column="IS_CSC"/>
        <result property="isWb" column="IS_WB"/>
        <result property="isZyNoJd" column="IS_ZY_NO_JD"/>
        <result property="isSave" column="IS_SAVE"/>
        <result property="jdFwxm" column="JD_FWXM"/>
        <result property="ieType" column="IE_TYPE"/>
        <result property="bgType" column="BG_TYPE"/>
        <result property="isQn" column="IS_QN"/>
    </resultMap>

    <sql id="selectFzgjBdServiceItemEntityVo">
        select
            GUID,
            CODE,
            NAME,
            STATUS,
            SEQ,
            MEMO,
            CREATE_DATE,
            CREATE_USER,
            UPDATE_DATE,
            UPDATE_USER,
            GROUP_CODE,
            GROUP_NAME,
            OWNED_COMPANY,
            PARENTID,
            OWNED_SERVICE,
            CREATE_USER_NAME,
            UPDATE_USER_NAME,
            PAGE_URL,
            SELECT_TYPE,
            YSFS,
            SYS_CODE,
            JD_BILL,
            CZ_BILL,
            FEEDBACK_URL,
            IS_ZY,
            EN_NAME,
            IS_CSC,
            IS_WB,
            IS_ZY_NO_JD,
            IS_SAVE,
            JD_FWXM,
            IE_TYPE,
            BG_TYPE,
            IS_QN
        from FZGJ_BD_SERVICE_ITEM
    </sql>

    <select id="selectTree" parameterType="com.baomidou.mybatisplus.core.conditions.Wrapper"
            resultType="com.eci.project.fzgjBdServiceItemPt.entity.TreeModel">
        SELECT A.GUID as id, A.sys_code sysCode,B.GUID ptguid,A.CODE,A.NAME as label, A.PARENTID,A.owned_Service as ownedService,A.seq,
               case NVL(B.GUID,2) when '2' then 0 else 1 end as checked,case NVL(B.GUID,2) when '2' then 0 else 1 end as orgChecked
        FROM FZGJ_BD_SERVICE_ITEM_PT A left join (
            SELECT GUID,CODE FROM FZGJ_BD_SERVICE_ITEM
                                      ${ew.customSqlSegment}
        ) B on A.CODE=B.CODE where A.STATUS='Y' order by A.SEQ desc

    </select>


    <select id="selectCheckedNodes" parameterType="com.baomidou.mybatisplus.core.conditions.Wrapper"
            resultType="java.lang.String">
        SELECT GUID FROM (select A.GROUP_CODE,B.* from FZGJ_BD_SERVICE_ITEM A inner join FZGJ_BD_SERVICE_ITEM_PT B on A.CODE=B.CODE)
                          ${ew.customSqlSegment}
    </select>
</mapper>