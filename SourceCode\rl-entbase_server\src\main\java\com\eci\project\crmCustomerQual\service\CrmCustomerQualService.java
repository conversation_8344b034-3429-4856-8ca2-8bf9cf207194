package com.eci.project.crmCustomerQual.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.crmCustomerQual.dao.CrmCustomerQualDao;
import com.eci.project.crmCustomerQual.entity.CrmCustomerQualEntity;
import com.eci.project.crmCustomerQual.validate.CrmCustomerQualVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 资质管理Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-05-26
*/
@Service
@Slf4j
public class CrmCustomerQualService implements EciBaseService<CrmCustomerQualEntity> {

    @Autowired
    private CrmCustomerQualDao crmCustomerQualDao;

    @Autowired
    private CrmCustomerQualVal crmCustomerQualVal;


    @Override
    public TgPageInfo queryPageList(CrmCustomerQualEntity entity) {
        EciQuery<CrmCustomerQualEntity> eciQuery = EciQuery.buildQuery(entity);
        List<CrmCustomerQualEntity> entities = crmCustomerQualDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public CrmCustomerQualEntity save(CrmCustomerQualEntity entity) {
        // 返回实体对象
        CrmCustomerQualEntity crmCustomerQualEntity = null;
        crmCustomerQualVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            crmCustomerQualEntity = crmCustomerQualDao.insertOne(entity);

        }else{

            crmCustomerQualEntity = crmCustomerQualDao.updateByEntityId(entity);

        }
        return crmCustomerQualEntity;
    }

    @Override
    public List<CrmCustomerQualEntity> selectList(CrmCustomerQualEntity entity) {
        return crmCustomerQualDao.selectList(entity);
    }

    @Override
    public CrmCustomerQualEntity selectOneById(Serializable id) {
        return crmCustomerQualDao.selectById(id);
    }


    @Override
    public void insertBatch(List<CrmCustomerQualEntity> list) {
        crmCustomerQualDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return crmCustomerQualDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return crmCustomerQualDao.deleteById(id);
    }

}