package com.eci.project.etmsOpFile.validate;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.enums.Enums;
import com.eci.common.util.DateUtils;
import com.eci.common.web.BllContext;
import com.eci.log.enums.BusinessType;

import com.eci.project.etmsOpFile.entity.EtmsOpFileEntity;

import com.eci.sso.role.entity.UserContext;
import org.springframework.stereotype.Service;


/**
* 业务附件Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-22
*/
@Service
public class EtmsOpFileVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(EtmsOpFileEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(EtmsOpFileEntity entity, BusinessType businessType) {
        entity.setUpdateDate(DateUtils.getNowDate());
        entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
        entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setCreateDate(DateUtils.getNowDate());
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
            entity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            entity.setCompanyName(UserContext.getUserInfo().getCompanyName());
            entity.setNodeCode(UserContext.getUserInfo().getDeptCode());
            entity.setNodeName(UserContext.getUserInfo().getDeptName());

        }
    }

}
