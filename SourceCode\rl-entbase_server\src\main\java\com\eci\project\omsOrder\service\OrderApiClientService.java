package com.eci.project.omsOrder.service;

import com.alibaba.fastjson.JSON; // 引入 Fastjson
import com.alibaba.fastjson.serializer.SerializerFeature; // 引入序列化特性
import com.eci.common.ZsrDateUtils;
import com.eci.project.omsOrder.entity.OrderEntity;
import com.eci.project.omsOrder.entity.OrderRequest;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter; // 用于日期时间格式化
import java.util.Date;


public class OrderApiClientService {

    private static final String DEFAULT_CONTENT_TYPE = "application/json";

    // Fastjson 对于 LocalDateTime 的处理
    // Fastjson 1.2.x 默认对 LocalDateTime 支持较差，
    // 需要通过自定义配置或全局配置来处理。
    // 在这里我们演示一种直接在序列化时通过 toJSONString 指定序列化特性的方式
    // 或者在传输之前手动转换为字符串。
    // 另一种更优雅的方式是使用 Fastjson 2.x，它对 Java 8 Time API 支持更好。
    // 如果你坚持使用 Fastjson 1.2.x 并且需要更全局的配置，可以考虑：
    // JSON.DEFFAULT_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
    // JSON.parseObject(json, YourClass.class, Feature.UseLocalDateTime);
    // 但这里我们保持独立的方法调用，避免全局修改。

    /**
     * 发送 POST 请求到指定 URL，并将 OrderRequest 对象作为 JSON 内容发送。
     *
     * @param targetUrl   要发送请求的 URL 地址
     * @param requestBody 包含订单数据的 OrderRequest 对象
     * @return 服务器的响应字符串，如果请求失败则返回 null
     */
    public String postOrder(String targetUrl, OrderRequest requestBody) {
        HttpURLConnection connection = null;
        try {
            URL url = new URL(targetUrl);
            connection = (HttpURLConnection) url.openConnection();

            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", DEFAULT_CONTENT_TYPE);
            connection.setRequestProperty("Accept", DEFAULT_CONTENT_TYPE);
            connection.setDoOutput(true);

            // 使用 Fastjson 将 Java 对象转换为 JSON 字符串
            // WriteMapNullValue: 输出值为null的字段
            // WriteDateUseDateFormat: 对日期进行格式化，而不是输出时间戳
            // DisableCircularReferenceDetect: 禁用循环引用检测
            // 注意：对于 LocalDateTime，Fastjson 1.2.x 默认可能输出数组形式，
            // 确保后端能正确解析，或者考虑将其转换为字符串。
            // 如果后端需要特定的日期格式，你可能需要在 OrderEntity 中使用 @JSONField(format = "yyyy-MM-dd HH:mm:ss")
            // 或者在序列化前手动处理。这里我们假设后端可以处理默认的 LocalDateTime JSON 形式。
            String jsonInputString = JSON.toJSONString(requestBody,
                    SerializerFeature.WriteMapNullValue,
                    SerializerFeature.WriteDateUseDateFormat,
                    SerializerFeature.DisableCircularReferenceDetect);

            // Fastjson 1.2.x 默认对 LocalDateTime 的序列化可能是 [year, month, day, hour, minute, second, nanoOfSecond] 数组形式。
            // 如果你需要 ISO 8601 字符串格式，你可能需要在 OrderEntity 的 LocalDateTime 字段上使用 `@JSONField(format="yyyy-MM-dd HH:mm:ss")`
            // 或者在 Fastjson 配置中全局设置日期格式。
            // 示例：
            // if (requestBody.getEntity() != null) {
            //    if (requestBody.getEntity().getCONFIRM_DATE() != null) {
            //        System.out.println("CONFIRM_DATE before serialization: " + requestBody.getEntity().getCONFIRM_DATE());
            //    }
            // }
            System.out.println("发送的 JSON 内容:\n" + jsonInputString);


            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonInputString.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }

            int responseCode = connection.getResponseCode();
            System.out.println("POST 请求响应码: " + responseCode);

            try (BufferedReader br = new BufferedReader(
                    new InputStreamReader(responseCode >= 200 && responseCode < 300 ? connection.getInputStream() : connection.getErrorStream(), StandardCharsets.UTF_8))) {
                StringBuilder response = new StringBuilder();
                String responseLine;
                while ((responseLine = br.readLine()) != null) {
                    response.append(responseLine.trim());
                }
                return response.toString();
            }

        } catch (Exception e) {
            System.err.println("发送 POST 请求时发生错误: " + e.getMessage());
            e.printStackTrace();
            return null;
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }


    public static void main(String[] args) {
        // 1. 创建 OrderEntity 对象并设置数据
        // 由于使用了 @Data，可以直接设置字段或者使用全参构造器（如果定义了）
        OrderEntity entity = new OrderEntity();
        entity.setORDER_NO("OMS20231026001");
        entity.setWORK_NO("TASK001");
        entity.setFWXM_CODE("报关服务");
        entity.setCUSTOMER_ORDER_NO("CUST-12345");
        entity.setJNSFHR("张三");
        entity.setI_E_TYPE("进"); // 进/出
        entity.setHDCS("货代A");
        entity.setBH_TYPE("普通报关");
        entity.setYSFS("海运");
        entity.setBGD_CUSTOM_CODE("上海海关");
        entity.setJD_NODE("上海分公司");
        entity.setJD_USER("李四");
        entity.setCONFIRM_DATE(ZsrDateUtils.dateToString(new Date())); // 设置当前时间
        entity.setXDH("XDH2023");
        entity.setQTCKH("REF987");
        entity.setBJ_TYPE("一般报检");
        entity.setI_E_PORT("上海港");
        entity.setOTHER_MEMO("特殊要求说明");
        entity.setCUSTOM_CODE("1234567890");
        entity.setOP_DATE(ZsrDateUtils.dateToString(new Date())); // 设置一小时前的时间

        // 2. 创建 OrderRequest 对象，将 OrderEntity 放入其中
        OrderRequest orderRequest = new OrderRequest();
        orderRequest.setEntity(entity); // 因为 @Data，所以有 setEntity 方法

        // 3. 创建 OrderApiClient 实例
        OrderApiClientService apiClient = new OrderApiClientService();

        // 4. 定义 POST 请求的目标 URL
        String targetUrl = "http://your-api-endpoint.com/api/postOrder"; // 替换成你的实际接口地址

        // 5. 发送 POST 请求
        System.out.println("正在发送订单数据...");
        String response = apiClient.postOrder(targetUrl, orderRequest);

        // 6. 打印响应
        if (response != null) {
            System.out.println("收到服务器响应:\n" + response);
        } else {
            System.out.println("发送订单数据失败。");
        }
    }

    /**
     *
     * public static void main(String[] args) {
     *         // 1. 创建 OrderEntity 对象并设置数据
     *         // 由于使用了 @Data，可以直接设置字段或者使用全参构造器（如果定义了）
     *         OrderEntity entity = new OrderEntity();
     *         entity.setORDER_NO("OMS20231026001");
     *         entity.setWORK_NO("TASK001");
     *         entity.setFWXM_CODE("报关服务");
     *         entity.setCUSTOMER_ORDER_NO("CUST-12345");
     *         entity.setJNSFHR("张三");
     *         entity.setI_E_TYPE("进"); // 进/出
     *         entity.setHDCS("货代A");
     *         entity.setBH_TYPE("普通报关");
     *         entity.setYSFS("海运");
     *         entity.setBGD_CUSTOM_CODE("上海海关");
     *         entity.setJD_NODE("上海分公司");
     *         entity.setJD_USER("李四");
     *         entity.setCONFIRM_DATE(LocalDateTime.now()); // 设置当前时间
     *         entity.setXDH("XDH2023");
     *         entity.setQTCKH("REF987");
     *         entity.setBJ_TYPE("一般报检");
     *         entity.setI_E_PORT("上海港");
     *         entity.setOTHER_MEMO("特殊要求说明");
     *         entity.setCUSTOM_CODE("1234567890");
     *         entity.setOP_DATE(LocalDateTime.now().minusHours(1)); // 设置一小时前的时间
     *
     *         // 2. 创建 OrderRequest 对象，将 OrderEntity 放入其中
     *         OrderRequest orderRequest = new OrderRequest();
     *         orderRequest.setEntity(entity); // 因为 @Data，所以有 setEntity 方法
     *
     *         // 3. 创建 OrderApiClient 实例
     *         OrderApiClient apiClient = new OrderApiClient();
     *
     *         // 4. 定义 POST 请求的目标 URL
     *         String targetUrl = "http://your-api-endpoint.com/api/postOrder"; // 替换成你的实际接口地址
     *
     *         // 5. 发送 POST 请求
     *         System.out.println("正在发送订单数据...");
     *         String response = apiClient.postOrder(targetUrl, orderRequest);
     *
     *         // 6. 打印响应
     *         if (response != null) {
     *             System.out.println("收到服务器响应:\n" + response);
     *         } else {
     *             System.out.println("发送订单数据失败。");
     *         }
     *     }
     *
     *
     *
     */
}