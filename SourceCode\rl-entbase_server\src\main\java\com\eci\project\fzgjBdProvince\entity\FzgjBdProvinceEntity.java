package com.eci.project.fzgjBdProvince.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;


/**
* 省对象 FZGJ_BD_PROVINCE
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-03-17
*/
@ApiModel("省")
@TableName("FZGJ_BD_PROVINCE")
public class FzgjBdProvinceEntity extends FzgjBdProvinceBaseEntity{

}
