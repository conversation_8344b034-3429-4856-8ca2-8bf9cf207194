package com.eci.project.fzgjBdSeaPort.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjBdSeaPort.dao.FzgjBdSeaPortDao;
import com.eci.project.fzgjBdSeaPort.entity.FzgjBdSeaPortEntity;
import com.eci.project.fzgjBdSeaPort.validate.FzgjBdSeaPortVal;

import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 海运港口Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-03-14
*/
@Service
@Slf4j
public class FzgjBdSeaPortService implements EciBaseService<FzgjBdSeaPortEntity> {

    @Autowired
    private FzgjBdSeaPortDao fzgjBdSeaPortDao;

    @Autowired
    private FzgjBdSeaPortVal fzgjBdSeaPortVal;


    @Override
    public TgPageInfo queryPageList(FzgjBdSeaPortEntity entity) {
        EciQuery<FzgjBdSeaPortEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjBdSeaPortEntity> entities = fzgjBdSeaPortDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjBdSeaPortEntity save(FzgjBdSeaPortEntity entity) {
        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setCreateDate(new java.util.Date());
            entity.setUpdateDate(new java.util.Date());
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        } else {
            entity.setUpdateDate(new java.util.Date());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        }
        // 返回实体对象
        FzgjBdSeaPortEntity fzgjBdSeaPortEntity = null;
        fzgjBdSeaPortVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjBdSeaPortEntity = fzgjBdSeaPortDao.insertOne(entity);

        }else{

            fzgjBdSeaPortEntity = fzgjBdSeaPortDao.updateByEntityId(entity);

        }
        return fzgjBdSeaPortEntity;
    }

    @Override
    public List<FzgjBdSeaPortEntity> selectList(FzgjBdSeaPortEntity entity) {
        return fzgjBdSeaPortDao.selectList(entity);
    }

    @Override
    public FzgjBdSeaPortEntity selectOneById(Serializable id) {
        return fzgjBdSeaPortDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjBdSeaPortEntity> list) {
        fzgjBdSeaPortDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjBdSeaPortDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjBdSeaPortDao.deleteById(id);
    }

}