package com.eci.project.etmsBdQuasiCarType.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.etmsBdQuasiCarType.dao.EtmsBdQuasiCarTypeDao;
import com.eci.project.etmsBdQuasiCarType.entity.EtmsBdQuasiCarTypeEntity;
import com.eci.project.etmsBdQuasiCarType.validate.EtmsBdQuasiCarTypeVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 准驾车型Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-16
*/
@Service
@Slf4j
public class EtmsBdQuasiCarTypeService implements EciBaseService<EtmsBdQuasiCarTypeEntity> {

    @Autowired
    private EtmsBdQuasiCarTypeDao etmsBdQuasiCarTypeDao;

    @Autowired
    private EtmsBdQuasiCarTypeVal etmsBdQuasiCarTypeVal;


    @Override
    public TgPageInfo queryPageList(EtmsBdQuasiCarTypeEntity entity) {
        EciQuery<EtmsBdQuasiCarTypeEntity> eciQuery = EciQuery.buildQuery(entity);
        List<EtmsBdQuasiCarTypeEntity> entities = etmsBdQuasiCarTypeDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public EtmsBdQuasiCarTypeEntity save(EtmsBdQuasiCarTypeEntity entity) {
        // 返回实体对象
        EtmsBdQuasiCarTypeEntity etmsBdQuasiCarTypeEntity = null;
        etmsBdQuasiCarTypeVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            etmsBdQuasiCarTypeEntity = etmsBdQuasiCarTypeDao.insertOne(entity);

        }else{

            etmsBdQuasiCarTypeEntity = etmsBdQuasiCarTypeDao.updateByEntityId(entity);

        }
        return etmsBdQuasiCarTypeEntity;
    }

    @Override
    public List<EtmsBdQuasiCarTypeEntity> selectList(EtmsBdQuasiCarTypeEntity entity) {
        return etmsBdQuasiCarTypeDao.selectList(entity);
    }

    @Override
    public EtmsBdQuasiCarTypeEntity selectOneById(Serializable id) {
        return etmsBdQuasiCarTypeDao.selectById(id);
    }


    @Override
    public void insertBatch(List<EtmsBdQuasiCarTypeEntity> list) {
        etmsBdQuasiCarTypeDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return etmsBdQuasiCarTypeDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return etmsBdQuasiCarTypeDao.deleteById(id);
    }

}