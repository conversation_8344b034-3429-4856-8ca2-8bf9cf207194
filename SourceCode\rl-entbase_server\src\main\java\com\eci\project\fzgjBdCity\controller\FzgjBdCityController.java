package com.eci.project.fzgjBdCity.controller;

import com.eci.common.ResponseMsgUtilX;
import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.project.fzgjBdCity.service.IFzgjBdCityService;
import com.eci.project.fzgjBdCity.entity.FzgjBdCityEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.EciLog;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


/**
* 市Controller
*
* @<NAME_EMAIL>
* @date 2025-03-17
*/
@Api(tags = "市")
@RestController
@RequestMapping("/fzgjBdCity")
public class FzgjBdCityController extends EciBaseController {

    @Autowired
    private IFzgjBdCityService fzgjBdCityService;

    /**
    * 根据实体插入数据库
    * @param entity
    * @return
    */
    @ApiOperation("市:保存")
    @EciLog(title = "市:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public ResponseMsg save(@RequestBody FzgjBdCityEntity entity){
        return ResponseMsgUtil.success(10001,fzgjBdCityService.save(entity));
    }

    /**
    * 根据条件查询一个集合
    * @param entity
    * @return
    */
    @ApiOperation("市:查询列表")
    @EciLog(title = "市:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    public ResponseMsg selectList(@RequestBody FzgjBdCityEntity entity){
        return ResponseMsgUtil.success(10001,fzgjBdCityService.selectList(entity));
    }

    /**
    * 分页查询
    * @param entity
    * @return
    */
    @ApiOperation("市:分页查询列表")
    @EciLog(title = "市:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    public ResponseMsg selectPageList(@RequestBody FzgjBdCityEntity entity){
        return ResponseMsgUtilX.success(10001,fzgjBdCityService.queryPageList(entity));
    }

    /**
    * 根据ID查一条记录
    * @param entity
    * @return
    */
    @ApiOperation("市:根据ID查一条")
    @EciLog(title = "市:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    public ResponseMsg selectOneById(@RequestBody FzgjBdCityEntity entity){
        return ResponseMsgUtilX.success(10001,fzgjBdCityService.selectOneById(entity.getGuid()));
    }


    /**
    * 根据ID删除
    * @param entity
    * @return
    */
    @ApiOperation("市:根据ID删除一条")
    @EciLog(title = "市:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    public ResponseMsg deleteById(@RequestBody FzgjBdCityEntity entity){
        return ResponseMsgUtil.success(10001,fzgjBdCityService.deleteById(entity.getGuid()));
    }

    /**
    * 根据IDS删除
    *
    * @param entity
    * @return
    */
    @ApiOperation("市:根据ID字符串删除多条")
    @EciLog(title = "市:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    public ResponseMsg deleteByIds(@RequestBody FzgjBdCityEntity entity) {
        return ResponseMsgUtil.success(10001, fzgjBdCityService.deleteByIds(entity.getIds()));
    }


}