package com.eci.project.omsOrderFwxmWorkFkHzqd.validate;

import com.eci.common.enums.Enums;
import com.eci.common.enums.OrderEnum;
import com.eci.common.util.StringUtils;
import com.eci.exception.BaseException;
import com.eci.log.enums.BusinessType;
import com.eci.project.omsOrderFwxmWork.dao.OmsOrderFwxmWorkDao;
import com.eci.project.omsOrderFwxmWork.entity.OmsOrderFwxmWorkEntity;
import com.eci.project.omsOrderFwxmWorkFkHzqd.entity.OmsOrderFwxmWorkFkHzqdEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 反馈内容-核注清单Val接口
 * 验证接口
 *
 * @<NAME_EMAIL>
 * @date 2025-06-03
 */
@Service
public class OmsOrderFwxmWorkFkHzqdVal {

    @Autowired
    private OmsOrderFwxmWorkDao omsOrderFwxmWorkDao;

    /**
     * 删除验证
     *
     * @param entity
     */
    public void deleteValidate(OmsOrderFwxmWorkFkHzqdEntity entity) {

    }

    /**
     * 保存验证
     *
     * @param entity
     */
    public void saveValidate(OmsOrderFwxmWorkFkHzqdEntity entity, BusinessType businessType) {

    }
}
