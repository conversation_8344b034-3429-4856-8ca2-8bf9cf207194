package com.eci.project.omsOrderFwxmWorkXzwt.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.omsOrderFwxmWorkXzwt.service.OmsOrderFwxmWorkXzwtService;
import com.eci.project.omsOrderFwxmWorkXzwt.entity.OmsOrderFwxmWorkXzwtEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 协作委托表Controller
*
* @<NAME_EMAIL>
* @date 2025-04-14
*/
@Api(tags = "协作委托表")
@RestController
@RequestMapping("/omsOrderFwxmWorkXzwt")
public class OmsOrderFwxmWorkXzwtController extends EciBaseController {

    @Autowired
    private OmsOrderFwxmWorkXzwtService omsOrderFwxmWorkXzwtService;


    @ApiOperation("协作委托表:保存")
    @EciLog(title = "协作委托表:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody OmsOrderFwxmWorkXzwtEntity entity){
        OmsOrderFwxmWorkXzwtEntity omsOrderFwxmWorkXzwtEntity =omsOrderFwxmWorkXzwtService.save(entity);
        return ResponseMsgUtil.success(10001,omsOrderFwxmWorkXzwtEntity);
    }


    @ApiOperation("协作委托表:查询列表")
    @EciLog(title = "协作委托表:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody OmsOrderFwxmWorkXzwtEntity entity){
        List<OmsOrderFwxmWorkXzwtEntity> omsOrderFwxmWorkXzwtEntities = omsOrderFwxmWorkXzwtService.selectList(entity);
        return ResponseMsgUtil.success(10001,omsOrderFwxmWorkXzwtEntities);
    }


    @ApiOperation("协作委托表:分页查询列表")
    @EciLog(title = "协作委托表:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody OmsOrderFwxmWorkXzwtEntity entity){
        TgPageInfo tgPageInfo = omsOrderFwxmWorkXzwtService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("协作委托表:根据ID查一条")
    @EciLog(title = "协作委托表:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody OmsOrderFwxmWorkXzwtEntity entity){
        OmsOrderFwxmWorkXzwtEntity  omsOrderFwxmWorkXzwtEntity = omsOrderFwxmWorkXzwtService.selectOneById(entity.getXzwtNo());
        return ResponseMsgUtil.success(10001,omsOrderFwxmWorkXzwtEntity);
    }


    @ApiOperation("协作委托表:根据ID删除一条")
    @EciLog(title = "协作委托表:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody OmsOrderFwxmWorkXzwtEntity entity){
        int count = omsOrderFwxmWorkXzwtService.deleteById(entity.getXzwtNo());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("协作委托表:根据ID字符串删除多条")
    @EciLog(title = "协作委托表:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody OmsOrderFwxmWorkXzwtEntity entity) {
        int count = omsOrderFwxmWorkXzwtService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}