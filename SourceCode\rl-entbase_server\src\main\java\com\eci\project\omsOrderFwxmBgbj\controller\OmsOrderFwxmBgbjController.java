package com.eci.project.omsOrderFwxmBgbj.controller;

import com.eci.common.ResponseMsgUtilX;
import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.omsOrderFwxmBgbj.entity.OmsOrderFwxmBgbjDTOEntity;
import com.eci.project.omsOrderFwxmBgbj.service.OmsOrderFwxmBgbjService;
import com.eci.project.omsOrderFwxmBgbj.entity.OmsOrderFwxmBgbjEntity;
import com.eci.project.omsOrderFwxmWorkTrace.service.OmsOrderFwxmWorkTraceZsrService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 委托内容-报关报检Controller
*
* @<NAME_EMAIL>
* @date 2025-05-14
*/
@Api(tags = "委托内容-报关报检")
@RestController
@RequestMapping("/omsOrderFwxmBgbj")
public class OmsOrderFwxmBgbjController extends EciBaseController {

    @Autowired
    private OmsOrderFwxmBgbjService omsOrderFwxmBgbjService;
    @Autowired
    private OmsOrderFwxmWorkTraceZsrService omsOrderFwxmWorkTraceZsrService;

    @ApiOperation("委托内容-报关报检:保存")
    @EciLog(title = "委托内容-报关报检:新增", businessType = BusinessType.INSERT)
    @PostMapping("/saveBgd")
    @EciAction()
    public ResponseMsg saveBgd(@RequestBody OmsOrderFwxmBgbjDTOEntity entity){
        OmsOrderFwxmBgbjDTOEntity omsOrderFwxmBgbjEntity =omsOrderFwxmBgbjService.saveBGD(entity);
        if(StringUtils.hasValue(entity.getOrderNo())){
            omsOrderFwxmWorkTraceZsrService.WorkTraceAllSave(entity.getOrderNo());
        }else if(StringUtils.hasValue(entity.getPreNo())){
            omsOrderFwxmWorkTraceZsrService.WorkTraceAllSaveZiZhuXiaDan(entity.getPreNo());
        }
        return ResponseMsgUtilX.success(10001,omsOrderFwxmBgbjEntity);
    }

    @ApiOperation("委托内容-报关报检:保存")
    @EciLog(title = "委托内容-报关报检:新增", businessType = BusinessType.INSERT)
    @PostMapping("/saveHzqdBgd")
    @EciAction()
    public ResponseMsg saveHzqdBgd(@RequestBody OmsOrderFwxmBgbjDTOEntity entity){
        OmsOrderFwxmBgbjDTOEntity omsOrderFwxmBgbjEntity =omsOrderFwxmBgbjService.saveMore(entity);
        if(StringUtils.hasValue(entity.getOrderNo())){
            omsOrderFwxmWorkTraceZsrService.WorkTraceAllSave(entity.getOrderNo());
        }else if(StringUtils.hasValue(entity.getPreNo())){
            omsOrderFwxmWorkTraceZsrService.WorkTraceAllSaveZiZhuXiaDan(entity.getPreNo());
        }
        return ResponseMsgUtilX.success(10001,omsOrderFwxmBgbjEntity);
    }


    @ApiOperation("委托内容-报关报检:查询列表")
    @EciLog(title = "委托内容-报关报检:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody OmsOrderFwxmBgbjEntity entity){
        List<OmsOrderFwxmBgbjEntity> omsOrderFwxmBgbjEntities = omsOrderFwxmBgbjService.selectList(entity);
        return ResponseMsgUtil.success(10001,omsOrderFwxmBgbjEntities);
    }


    @ApiOperation("委托内容-报关报检:分页查询列表")
    @EciLog(title = "委托内容-报关报检:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody OmsOrderFwxmBgbjEntity entity){
        TgPageInfo tgPageInfo = omsOrderFwxmBgbjService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("委托内容-报关报检:根据ID查一条")
    @EciLog(title = "委托内容-报关报检:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody OmsOrderFwxmBgbjDTOEntity entity){
        OmsOrderFwxmBgbjDTOEntity  omsOrderFwxmBgbjEntity = omsOrderFwxmBgbjService.selectOneById(entity);
        return ResponseMsgUtilX.success(10001,omsOrderFwxmBgbjEntity);
    }


    @ApiOperation("委托内容-报关报检:根据ID删除一条")
    @EciLog(title = "委托内容-报关报检:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody OmsOrderFwxmBgbjEntity entity){
        int count = omsOrderFwxmBgbjService.deleteById(entity.getBgbjNo());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("委托内容-报关报检:根据ID字符串删除多条")
    @EciLog(title = "委托内容-报关报检:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody OmsOrderFwxmBgbjEntity entity) {
        int count = omsOrderFwxmBgbjService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("委托内容-报关报检:订单接单页面的进出口标记，默认到反馈编辑区")
    @EciLog(title = "委托内容-报关报检:订单接单页面的进出口标记，默认到反馈编辑区", businessType = BusinessType.SELECT)
    @PostMapping("/selectOrderFwxmWorkFkIETYPE")
    @EciAction()
    public ResponseMsg selectOrderFwxmWorkFkGetOrderIETYPE(@RequestBody OmsOrderFwxmBgbjEntity entity){
        List<OmsOrderFwxmBgbjEntity> omsOrderFwxmBgbjEntities = omsOrderFwxmBgbjService.selectOrderFwxmWorkFkGetOrderIETYPE(entity);
        return ResponseMsgUtil.success(10001,omsOrderFwxmBgbjEntities);
    }
}