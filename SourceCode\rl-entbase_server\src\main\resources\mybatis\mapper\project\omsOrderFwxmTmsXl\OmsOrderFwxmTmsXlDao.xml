<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.omsOrderFwxmTmsXl.dao.OmsOrderFwxmTmsXlDao">
    <resultMap type="OmsOrderFwxmTmsXlEntity" id="OmsOrderFwxmTmsXlResult">
        <result property="preNo" column="PRE_NO"/>
        <result property="orderNo" column="ORDER_NO"/>
        <result property="tmsNo" column="TMS_NO"/>
        <result property="lineNo" column="LINE_NO"/>
        <result property="lineSeq" column="LINE_SEQ"/>
        <result property="isMerge" column="IS_MERGE"/>
        <result property="nationalidCounty" column="NATIONALID_COUNTY"/>
        <result property="nationalidProvince" column="NATIONALID_PROVINCE"/>
        <result property="nationalidCity" column="NATIONALID_CITY"/>
        <result property="nationalidRegion" column="NATIONALID_REGION"/>
        <result property="nationalidTown" column="NATIONALID_TOWN"/>
        <result property="terminusidCounty" column="TERMINUSID_COUNTY"/>
        <result property="terminusidProvince" column="TERMINUSID_PROVINCE"/>
        <result property="terminusidCity" column="TERMINUSID_CITY"/>
        <result property="terminusidRegion" column="TERMINUSID_REGION"/>
        <result property="terminusidTown" column="TERMINUSID_TOWN"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="nationalidTownType" column="NATIONALID_TOWN_TYPE"/>
        <result property="terminusidTownType" column="TERMINUSID_TOWN_TYPE"/>
        <result property="isMydl" column="IS_MYDL"/>
        <result property="isZjdb" column="IS_ZJDB"/>
        <result property="isBxfw" column="IS_BXFW"/>
        <result property="crossLine" column="CROSS_LINE"/>
        <result property="crossItem" column="CROSS_ITEM"/>
        <result property="qhc" column="QHC"/>
    </resultMap>

    <sql id="selectOmsOrderFwxmTmsXlEntityVo">
        select
            PRE_NO,
            ORDER_NO,
            TMS_NO,
            LINE_NO,
            LINE_SEQ,
            IS_MERGE,
            NATIONALID_COUNTY,
            NATIONALID_PROVINCE,
            NATIONALID_CITY,
            NATIONALID_REGION,
            NATIONALID_TOWN,
            TERMINUSID_COUNTY,
            TERMINUSID_PROVINCE,
            TERMINUSID_CITY,
            TERMINUSID_REGION,
            TERMINUSID_TOWN,
            CREATE_USER,
            CREATE_USER_NAME,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_USER_NAME,
            UPDATE_DATE,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            NATIONALID_TOWN_TYPE,
            TERMINUSID_TOWN_TYPE,
            IS_MYDL,
            IS_ZJDB,
            IS_BXFW,
            CROSS_LINE,
            CROSS_ITEM,
            QHC
        from OMS_ORDER_FWXM_TMS_XL
    </sql>
</mapper>