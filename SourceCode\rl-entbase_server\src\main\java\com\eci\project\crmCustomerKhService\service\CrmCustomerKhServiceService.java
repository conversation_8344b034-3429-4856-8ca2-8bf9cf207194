package com.eci.project.crmCustomerKhService.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.crmCustomerKhService.dao.CrmCustomerKhServiceDao;
import com.eci.project.crmCustomerKhService.entity.CrmCustomerKhServiceEntity;
import com.eci.project.crmCustomerKhService.validate.CrmCustomerKhServiceVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-05-13
*/
@Service
@Slf4j
public class CrmCustomerKhServiceService implements EciBaseService<CrmCustomerKhServiceEntity> {

    @Autowired
    private CrmCustomerKhServiceDao crmCustomerKhServiceDao;

    @Autowired
    private CrmCustomerKhServiceVal crmCustomerKhServiceVal;


    @Override
    public TgPageInfo queryPageList(CrmCustomerKhServiceEntity entity) {
        EciQuery<CrmCustomerKhServiceEntity> eciQuery = EciQuery.buildQuery(entity);
        eciQuery.select("(SELECT S.NAME FROM FZGJ_EXTEND_DATA S " +
                        " WHERE S.CODE = A.SERVICE_CODE AND S.DATA_TYPE='XM' " +
                        " AND S.STATUS='Y' AND S.GROUP_CODE= A.GROUP_CODE) AS serviceCodeName"
                ,"A.*");
        List<CrmCustomerKhServiceEntity> entities = crmCustomerKhServiceDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public CrmCustomerKhServiceEntity save(CrmCustomerKhServiceEntity entity) {
        // 返回实体对象
        CrmCustomerKhServiceEntity crmCustomerKhServiceEntity = null;
        crmCustomerKhServiceVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            crmCustomerKhServiceEntity = crmCustomerKhServiceDao.insertOne(entity);

        }else{

            crmCustomerKhServiceEntity = crmCustomerKhServiceDao.updateByEntityId(entity);

        }
        return crmCustomerKhServiceEntity;
    }

    @Override
    public List<CrmCustomerKhServiceEntity> selectList(CrmCustomerKhServiceEntity entity) {
        return crmCustomerKhServiceDao.selectList(entity);
    }

    @Override
    public CrmCustomerKhServiceEntity selectOneById(Serializable id) {
        return crmCustomerKhServiceDao.selectById(id);
    }


    @Override
    public void insertBatch(List<CrmCustomerKhServiceEntity> list) {
        crmCustomerKhServiceDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return crmCustomerKhServiceDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return crmCustomerKhServiceDao.deleteById(id);
    }

}