package com.eci.project.omsOrderFwxmTms.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.eci.common.DictField;
import com.eci.common.ZsrBaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class OrderInfoDto extends ZsrBaseEntity {
    /**
     * 2急货
     */
    @DictField(queryKey = "YNKey01")
    private String isJjh;

    /**
     * 3订单号
     */
    private String orderNo;

    /**
     * 4委托方
     */
    private String consigneeCode;

    /**
     * 5客户事业部
     */
    private String customerBu;

    /**
     * 6客户单据编号
     */
    private String customerOrderNo;

    /**
     * 7实际发货方
     */
    @DictField(queryKey = "CRM_CUSTOMER_SFHF")
    private String shipper;

    /**
     * 8实际收货方
     */
    @DictField(queryKey = "CRM_CUSTOMER_SFHF")
    private String receiver;

    /**
     * 操作日期 (YYYY-MM-DD)
     */
    @DictField(useDateFormat = true)
    private String opDate; // Storing as String as per TO_CHAR in SQL

    /**
     * 10业务类型
     */
    private String opType;

    /**
     * 11业务产品/项目
     */
    private String productCode;

    /**
     * 12服务类型
     */
    private String fwlxName;

    /**
     * 13客户付款方案
     */
    private String fkfaCode;

    /**
     * 状态
     */
    private String status;

    /**
     * 公司编码
     */
    private String companyCode;

    /**
     * 14协作方案
     */
    private String xzfaNo;

    /**
     * 15业务备注
     */
    private String bizMemo;

    /**
     * 16创建时间
     */
    private Date createDate;

    /**
     * 17接单组织
     */
    private String jdNode;

    /**
     * 18协同编号 (pre)
     */
    private String preNo;

    /**
     * 作废人
     */
    private String cancelUser;

    /**
     * 作废人名称
     */
    private String cancelUserName;

    /**
     * 作废时间
     */
    private Date cancelDate;

    /**
     * 作废原因
     */
    private String cancelRemark;

    /**
     * 创建人
     */
    private String createUserName;

    /**
     * 执行阶段
     */
    private String stage;

    /**
     * 接单员
     */
    private String jdUser;

    /**
     * 组编码
     */
    private String groupCode;

    /**
     * 业务注册ID
     */
    private String bizRegId;

    /**
     * 接单日期
     */
    private Date confirmDate;

    /**
     * 作业完成 (√ or space)
     */
    private String opCompleteOk; // Based on CASE output

    /**
     * 作业数据齐全 (√ or space)
     */
    private String dataOk; // Based on CASE output

    /**
     * 结算完成日期（结案日期) (√ or space)
     */
    private String arapOk; // Based on CASE output

    /**
     * 结算完成日期（结案日期)
     */
    private Date arapOkDate;

    /**
     * 项目
     */
    private String crossItem;

    /**
     * 确认接单必填项
     */
    @DictField(queryKey = "YNKey")
    private String isQrjd;

    /**
     * 协作分发必填项
     */
    private String isXzff;

    /**
     * 来源系统编号
     */
    private String sysDocNo;

    /**
     * AP确认 (√ or NULL)
     */
    private String apOk; // Based on CASE output

    /**
     * 实际发货方名称
     */
    private String shipperName; // Aliased in SQL

    /**
     * 实际收货方名称
     */
    private String receiverName; // Aliased in SQL

    /**
     * 接单员名称
     */
    private String jdUserName; // Aliased in SQL

    /**
     * 客户付款方案名称
     */
    private String fkfaCodeName; // Aliased in SQL

    /**
     * 服务项目名称
     */
    private String fwxmName; // Aliased in SQL

    /**
     * 是否来自OMS系统
     */
    private String isOms; // Based on CASE EXISTS output

    /**
     * MB单号
     */
    private String mbNo;

    /**
     * HB单号
     */
    private String hbNo;

    /**
     * 入库单号
     */
    private String warehouseInNo;

    /**
     * 总件数
     */
    private Integer pieceTotal; // Assuming this is a count

    /**
     * 用户自定义字段9
     */
    private String udf9;

    /**
     * 系统订单批次
     */
    private String sysOrderBatch;

    /**
     * 客户订单批次
     */
    private String customerOrderBatch;

    /**
     * 去回程
     */
    private String qhc;


    private String productCodeName; // 业务产品/项目‌:ml-citation{ref="1,6" data="citationList"}
    private String xzfaNoName;      // 协作方案‌:ml-citation{ref="2,3" data="citationList"}
    private String jdNodeName;      // 接单组织‌:ml-citation{ref="4,7" data="citationList"}



    // 添加 getFieldValue 方法
    public String getFieldValue(String fieldName) {
        switch (fieldName) {
            case "consigneeCode":
                return this.consigneeCode;
            case "customerBu":
                return this.customerBu;
            case "opType":
                return this.opType;
            case "xzfaNo":
                return this.xzfaNo;
            case "crossItem":
                return this.crossItem;
            case "stage":
                return this.stage;
            case "jdNode":
                return this.jdNode;
            default:
                return null; // 或者抛出异常，根据需求决定
        }
    }
}
