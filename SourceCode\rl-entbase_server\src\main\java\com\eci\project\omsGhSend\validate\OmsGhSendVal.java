package com.eci.project.omsGhSend.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.omsGhSend.entity.OmsGhSendEntity;

import org.springframework.stereotype.Service;


/**
* OMS固化路由表Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-05-16
*/
@Service
public class OmsGhSendVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(OmsGhSendEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(OmsGhSendEntity entity, BusinessType businessType) {

    }

}
