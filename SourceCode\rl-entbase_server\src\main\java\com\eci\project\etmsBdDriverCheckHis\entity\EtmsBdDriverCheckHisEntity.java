package com.eci.project.etmsBdDriverCheckHis.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 司机体检历史对象 ETMS_BD_DRIVER_CHECK_HIS
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-04-22
*/
@ApiModel("司机体检历史")
@TableName("ETMS_BD_DRIVER_CHECK_HIS")
@FieldNameConstants
public class EtmsBdDriverCheckHisEntity extends EciBaseEntity{
    /**
    * GUID
    */
    @ApiModelProperty("GUID(50)")
    @TableId("GUID")
    private String guid;

    /**
    * 司机GUID
    */
    @ApiModelProperty("司机GUID(50)")
    @TableField("DRIVER_GUID")
    private String driverGuid;

    /**
    * 体检日期
    */
    @ApiModelProperty("体检日期(7)")
    @TableField("CHECK_DATE")
    private Date checkDate;

    @ApiModelProperty("体检日期开始")
    @TableField(exist=false)
    private Date checkDateStart;

    @ApiModelProperty("体检日期结束")
    @TableField(exist=false)
    private Date checkDateEnd;

    /**
    * 下次体检日期
    */
    @ApiModelProperty("下次体检日期(7)")
    @TableField("NEXT_DATE")
    private Date nextDate;

    @ApiModelProperty("下次体检日期开始")
    @TableField(exist=false)
    private Date nextDateStart;

    @ApiModelProperty("下次体检日期结束")
    @TableField(exist=false)
    private Date nextDateEnd;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public EtmsBdDriverCheckHisEntity() {
        this.setSubClazz(EtmsBdDriverCheckHisEntity.class);
    }

    public EtmsBdDriverCheckHisEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public EtmsBdDriverCheckHisEntity setDriverGuid(String driverGuid) {
        this.driverGuid = driverGuid;
        this.nodifySetFiled("driverGuid", driverGuid);
        return this;
    }

    public String getDriverGuid() {
        this.nodifyGetFiled("driverGuid");
        return driverGuid;
    }

    public EtmsBdDriverCheckHisEntity setCheckDate(Date checkDate) {
        this.checkDate = checkDate;
        this.nodifySetFiled("checkDate", checkDate);
        return this;
    }

    public Date getCheckDate() {
        this.nodifyGetFiled("checkDate");
        return checkDate;
    }

    public EtmsBdDriverCheckHisEntity setCheckDateStart(Date checkDateStart) {
        this.checkDateStart = checkDateStart;
        this.nodifySetFiled("checkDateStart", checkDateStart);
        return this;
    }

    public Date getCheckDateStart() {
        this.nodifyGetFiled("checkDateStart");
        return checkDateStart;
    }

    public EtmsBdDriverCheckHisEntity setCheckDateEnd(Date checkDateEnd) {
        this.checkDateEnd = checkDateEnd;
        this.nodifySetFiled("checkDateEnd", checkDateEnd);
        return this;
    }

    public Date getCheckDateEnd() {
        this.nodifyGetFiled("checkDateEnd");
        return checkDateEnd;
    }
    public EtmsBdDriverCheckHisEntity setNextDate(Date nextDate) {
        this.nextDate = nextDate;
        this.nodifySetFiled("nextDate", nextDate);
        return this;
    }

    public Date getNextDate() {
        this.nodifyGetFiled("nextDate");
        return nextDate;
    }

    public EtmsBdDriverCheckHisEntity setNextDateStart(Date nextDateStart) {
        this.nextDateStart = nextDateStart;
        this.nodifySetFiled("nextDateStart", nextDateStart);
        return this;
    }

    public Date getNextDateStart() {
        this.nodifyGetFiled("nextDateStart");
        return nextDateStart;
    }

    public EtmsBdDriverCheckHisEntity setNextDateEnd(Date nextDateEnd) {
        this.nextDateEnd = nextDateEnd;
        this.nodifySetFiled("nextDateEnd", nextDateEnd);
        return this;
    }

    public Date getNextDateEnd() {
        this.nodifyGetFiled("nextDateEnd");
        return nextDateEnd;
    }
    public EtmsBdDriverCheckHisEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public EtmsBdDriverCheckHisEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public EtmsBdDriverCheckHisEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public EtmsBdDriverCheckHisEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public EtmsBdDriverCheckHisEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public EtmsBdDriverCheckHisEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public EtmsBdDriverCheckHisEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public EtmsBdDriverCheckHisEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

}
