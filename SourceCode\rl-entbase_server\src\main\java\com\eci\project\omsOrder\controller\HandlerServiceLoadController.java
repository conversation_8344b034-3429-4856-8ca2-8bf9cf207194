package com.eci.project.omsOrder.controller;

import com.eci.common.CodeNameCommon;
import com.eci.common.ZsrBaseEntity;
import com.eci.common.util.ResponseMsg;
import com.eci.common.util.ResponseMsgUtil;
import com.eci.crud.controller.EciBaseController;
import com.eci.log.annotation.EciAction;
import com.eci.log.annotation.EciLog;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.omsOrder.entity.OmsOrderEntity;
import com.eci.project.omsOrder.entity.RequestOmsOrderTracePageEntity;
import com.eci.project.omsOrder.service.OmsOrderCustomService;
import com.eci.project.omsOrder.service.OmsOrderService;
import com.eci.project.omsOrder.service.OmsOrderTraceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 订单操作-表头 Controller
 *
 * @<NAME_EMAIL>
 * @date 2025-04-14
 */
@Api(tags = "数据处理服务")
@RestController
@RequestMapping("/handlerServiceLoad")
public class HandlerServiceLoadController extends EciBaseController {

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OmsOrderCustomService omsOrderCustomService;

    @Autowired
    private OmsOrderTraceService omsOrderTraceService;

    @ApiOperation("订单表:查询列表")
    @EciLog(title = "订单表:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/opTypeSearch")
    @EciAction()
    public ResponseMsg opTypeSearch(@RequestBody String jsonString) {
        CodeNameCommon codeNameCommon = omsOrderCustomService.opTypeSearch(jsonString);
        return ResponseMsgUtil.success(10001, codeNameCommon);
    }


    @ApiOperation("订单表:获取服务类型")
    @EciLog(title = "订单表:获取服务类型", businessType = BusinessType.SELECT)
    @PostMapping("/getServiceType")
    @EciAction()
    public ResponseMsg getServiceType(@RequestBody String jsonString) {
        ZsrBaseEntity zsrBaseEntity = omsOrderCustomService.GetServiceType(jsonString);
        return ResponseMsgUtil.success(10001, zsrBaseEntity);
    }


    @ApiOperation("订单表:保存")
    @EciLog(title = "订单表:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody OmsOrderEntity entity) {
        OmsOrderEntity omsOrderEntity = omsOrderService.save(entity);
        return ResponseMsgUtil.success(10001, omsOrderEntity);
    }


    @ApiOperation("订单表:查询列表")
    @EciLog(title = "订单表:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody OmsOrderEntity entity) {
        List<OmsOrderEntity> omsOrderEntities = omsOrderService.selectList(entity);
        return ResponseMsgUtil.success(10001, omsOrderEntities);
    }


    @ApiOperation("订单表:分页查询列表")
    @EciLog(title = "订单表:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody OmsOrderEntity entity) {
        TgPageInfo tgPageInfo = omsOrderService.queryPageList(entity);
        return ResponseMsgUtil.success(10001, tgPageInfo);
    }


    @ApiOperation("订单表:根据ID查一条")
    @EciLog(title = "订单表:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody OmsOrderEntity entity) {
        OmsOrderEntity omsOrderEntity = omsOrderService.selectOneById(entity.getOrderNo());
        return ResponseMsgUtil.success(10001, omsOrderEntity);
    }


    @ApiOperation("订单表:根据ID删除一条")
    @EciLog(title = "订单表:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody OmsOrderEntity entity) {
        int count = omsOrderService.deleteById(entity.getOrderNo());
        return ResponseMsgUtil.success(10001, count);
    }


    @ApiOperation("订单表:根据ID字符串删除多条")
    @EciLog(title = "订单表:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody OmsOrderEntity entity) {
        int count = omsOrderService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001, count);
    }


    @ApiOperation("作业跟踪及反馈:分页查询列表")
    @EciLog(title = "作业跟踪及反馈:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectTracePageList")
    @EciAction()
    public ResponseMsg selectTracePageList(@RequestBody RequestOmsOrderTracePageEntity entity) {
        TgPageInfo tgPageInfo = omsOrderTraceService.selectTracePageList(entity);
        return ResponseMsgUtil.success(10001, tgPageInfo);
    }
}