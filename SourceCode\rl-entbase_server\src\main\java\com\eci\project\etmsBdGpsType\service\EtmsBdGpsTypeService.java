package com.eci.project.etmsBdGpsType.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.etmsBdGpsType.dao.EtmsBdGpsTypeDao;
import com.eci.project.etmsBdGpsType.entity.EtmsBdGpsTypeEntity;
import com.eci.project.etmsBdGpsType.validate.EtmsBdGpsTypeVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 定位方式Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-03-21
*/
@Service
@Slf4j
public class EtmsBdGpsTypeService implements EciBaseService<EtmsBdGpsTypeEntity> {

    @Autowired
    private EtmsBdGpsTypeDao etmsBdGpsTypeDao;

    @Autowired
    private EtmsBdGpsTypeVal etmsBdGpsTypeVal;


    @Override
    public TgPageInfo queryPageList(EtmsBdGpsTypeEntity entity) {
        EciQuery<EtmsBdGpsTypeEntity> eciQuery = EciQuery.buildQuery(entity);
        List<EtmsBdGpsTypeEntity> entities = etmsBdGpsTypeDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public EtmsBdGpsTypeEntity save(EtmsBdGpsTypeEntity entity) {
        // 返回实体对象
        EtmsBdGpsTypeEntity etmsBdGpsTypeEntity = null;
        etmsBdGpsTypeVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            etmsBdGpsTypeEntity = etmsBdGpsTypeDao.insertOne(entity);

        }else{

            etmsBdGpsTypeEntity = etmsBdGpsTypeDao.updateByEntityId(entity);

        }
        return etmsBdGpsTypeEntity;
    }

    @Override
    public List<EtmsBdGpsTypeEntity> selectList(EtmsBdGpsTypeEntity entity) {
        return etmsBdGpsTypeDao.selectList(entity);
    }

    @Override
    public EtmsBdGpsTypeEntity selectOneById(Serializable id) {
        return etmsBdGpsTypeDao.selectById(id);
    }


    @Override
    public void insertBatch(List<EtmsBdGpsTypeEntity> list) {
        etmsBdGpsTypeDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return etmsBdGpsTypeDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return etmsBdGpsTypeDao.deleteById(id);
    }

}