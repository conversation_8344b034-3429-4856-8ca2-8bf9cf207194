package com.eci.project.etmsOpAttemperLine.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.math.BigDecimal;
import java.util.Date;


/**
* 托运线路站点信息对象 ETMS_OP_ATTEMPER_LINE
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-06-13
*/
@ApiModel("托运线路站点信息")
@TableName("ETMS_OP_ATTEMPER_LINE")
@FieldNameConstants
public class EtmsOpAttemperLineEntity extends EciBaseEntity{
    /**
    * 主键
    */
    @ApiModelProperty("主键(50)")
    @TableId("GUID")
    private String guid;

    /**
    * 业务编号
    */
    @ApiModelProperty("业务编号(20)")
    @TableField("OP_NO")
    private String opNo;

    /**
    * 委托GUID
    */
    @ApiModelProperty("委托GUID(50)")
    @TableField("ATT_GUID")
    private String attGuid;

    /**
    * 用车需求信息GUID
    */
    @ApiModelProperty("用车需求信息GUID(50)")
    @TableField("CAR_GUID")
    private String carGuid;

    /**
    * 站点顺序
    */
    @ApiModelProperty("站点顺序(22)")
    @TableField("STATION_SEQ")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal stationSeq;

    /**
    * 站点类型
    */
    @ApiModelProperty("站点类型(20)")
    @TableField("STATION_TYPE")
    private String stationType;

    /**
    * 要求作业时间
    */
    @ApiModelProperty("要求作业时间(7)")
    @TableField("REQUEST_DATE")
    private Date requestDate;

    @ApiModelProperty("要求作业时间开始")
    @TableField(exist=false)
    private Date requestDateStart;

    @ApiModelProperty("要求作业时间结束")
    @TableField(exist=false)
    private Date requestDateEnd;

    /**
    * 作业地区
    */
    @ApiModelProperty("作业地区(400)")
    @TableField("OP_AREA")
    private String opArea;

    /**
    * 作业地址
    */
    @ApiModelProperty("作业地址(500)")
    @TableField("OP_ADDRESS")
    private String opAddress;

    /**
    * 作业码头
    */
    @ApiModelProperty("作业码头(50)")
    @TableField("OP_WH")
    private String opWh;

    /**
    * 作业联系人
    */
    @ApiModelProperty("作业联系人(100)")
    @TableField("OP_LINK")
    private String opLink;

    /**
    * 作业联系电话
    */
    @ApiModelProperty("作业联系电话(50)")
    @TableField("OP_TEL")
    private String opTel;

    /**
    * 作业要求
    */
    @ApiModelProperty("作业要求(500)")
    @TableField("OP_REQUEST")
    private String opRequest;

    /**
    * 创建企业
    */
    @ApiModelProperty("创建企业(20)")
    @TableField("CREATE_COMPANY")
    private String createCompany;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 编辑人
    */
    @ApiModelProperty("编辑人(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 编辑日期
    */
    @ApiModelProperty("编辑日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("编辑日期开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("编辑日期结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 委托编号
    */
    @ApiModelProperty("委托编号(20)")
    @TableField("ATT_NO")
    private String attNo;

    /**
    * 常用地址简称 
    */
    @ApiModelProperty("常用地址简称 (50)")
    @TableField("OP_ABBREVIATION")
    private String opAbbreviation;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * carGo经度
    */
    @ApiModelProperty("carGo经度(22)")
    @TableField("OP_LONG")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal opLong;

    /**
    * carGo纬度
    */
    @ApiModelProperty("carGo纬度(22)")
    @TableField("OP_LAT")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal opLat;

    /**
    * LOAD经度
    */
    @ApiModelProperty("LOAD经度(22)")
    @TableField("LOAD_LONG")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal loadLong;

    /**
    * LOAD纬度
    */
    @ApiModelProperty("LOAD纬度(22)")
    @TableField("LOAD_LAT")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal loadLat;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public EtmsOpAttemperLineEntity() {
        this.setSubClazz(EtmsOpAttemperLineEntity.class);
    }

    public EtmsOpAttemperLineEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public EtmsOpAttemperLineEntity setOpNo(String opNo) {
        this.opNo = opNo;
        this.nodifySetFiled("opNo", opNo);
        return this;
    }

    public String getOpNo() {
        this.nodifyGetFiled("opNo");
        return opNo;
    }

    public EtmsOpAttemperLineEntity setAttGuid(String attGuid) {
        this.attGuid = attGuid;
        this.nodifySetFiled("attGuid", attGuid);
        return this;
    }

    public String getAttGuid() {
        this.nodifyGetFiled("attGuid");
        return attGuid;
    }

    public EtmsOpAttemperLineEntity setCarGuid(String carGuid) {
        this.carGuid = carGuid;
        this.nodifySetFiled("carGuid", carGuid);
        return this;
    }

    public String getCarGuid() {
        this.nodifyGetFiled("carGuid");
        return carGuid;
    }

    public EtmsOpAttemperLineEntity setStationSeq(BigDecimal stationSeq) {
        this.stationSeq = stationSeq;
        this.nodifySetFiled("stationSeq", stationSeq);
        return this;
    }

    public BigDecimal getStationSeq() {
        this.nodifyGetFiled("stationSeq");
        return stationSeq;
    }

    public EtmsOpAttemperLineEntity setStationType(String stationType) {
        this.stationType = stationType;
        this.nodifySetFiled("stationType", stationType);
        return this;
    }

    public String getStationType() {
        this.nodifyGetFiled("stationType");
        return stationType;
    }

    public EtmsOpAttemperLineEntity setRequestDate(Date requestDate) {
        this.requestDate = requestDate;
        this.nodifySetFiled("requestDate", requestDate);
        return this;
    }

    public Date getRequestDate() {
        this.nodifyGetFiled("requestDate");
        return requestDate;
    }

    public EtmsOpAttemperLineEntity setRequestDateStart(Date requestDateStart) {
        this.requestDateStart = requestDateStart;
        this.nodifySetFiled("requestDateStart", requestDateStart);
        return this;
    }

    public Date getRequestDateStart() {
        this.nodifyGetFiled("requestDateStart");
        return requestDateStart;
    }

    public EtmsOpAttemperLineEntity setRequestDateEnd(Date requestDateEnd) {
        this.requestDateEnd = requestDateEnd;
        this.nodifySetFiled("requestDateEnd", requestDateEnd);
        return this;
    }

    public Date getRequestDateEnd() {
        this.nodifyGetFiled("requestDateEnd");
        return requestDateEnd;
    }
    public EtmsOpAttemperLineEntity setOpArea(String opArea) {
        this.opArea = opArea;
        this.nodifySetFiled("opArea", opArea);
        return this;
    }

    public String getOpArea() {
        this.nodifyGetFiled("opArea");
        return opArea;
    }

    public EtmsOpAttemperLineEntity setOpAddress(String opAddress) {
        this.opAddress = opAddress;
        this.nodifySetFiled("opAddress", opAddress);
        return this;
    }

    public String getOpAddress() {
        this.nodifyGetFiled("opAddress");
        return opAddress;
    }

    public EtmsOpAttemperLineEntity setOpWh(String opWh) {
        this.opWh = opWh;
        this.nodifySetFiled("opWh", opWh);
        return this;
    }

    public String getOpWh() {
        this.nodifyGetFiled("opWh");
        return opWh;
    }

    public EtmsOpAttemperLineEntity setOpLink(String opLink) {
        this.opLink = opLink;
        this.nodifySetFiled("opLink", opLink);
        return this;
    }

    public String getOpLink() {
        this.nodifyGetFiled("opLink");
        return opLink;
    }

    public EtmsOpAttemperLineEntity setOpTel(String opTel) {
        this.opTel = opTel;
        this.nodifySetFiled("opTel", opTel);
        return this;
    }

    public String getOpTel() {
        this.nodifyGetFiled("opTel");
        return opTel;
    }

    public EtmsOpAttemperLineEntity setOpRequest(String opRequest) {
        this.opRequest = opRequest;
        this.nodifySetFiled("opRequest", opRequest);
        return this;
    }

    public String getOpRequest() {
        this.nodifyGetFiled("opRequest");
        return opRequest;
    }

    public EtmsOpAttemperLineEntity setCreateCompany(String createCompany) {
        this.createCompany = createCompany;
        this.nodifySetFiled("createCompany", createCompany);
        return this;
    }

    public String getCreateCompany() {
        this.nodifyGetFiled("createCompany");
        return createCompany;
    }

    public EtmsOpAttemperLineEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public EtmsOpAttemperLineEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public EtmsOpAttemperLineEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public EtmsOpAttemperLineEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public EtmsOpAttemperLineEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public EtmsOpAttemperLineEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public EtmsOpAttemperLineEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public EtmsOpAttemperLineEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public EtmsOpAttemperLineEntity setAttNo(String attNo) {
        this.attNo = attNo;
        this.nodifySetFiled("attNo", attNo);
        return this;
    }

    public String getAttNo() {
        this.nodifyGetFiled("attNo");
        return attNo;
    }

    public EtmsOpAttemperLineEntity setOpAbbreviation(String opAbbreviation) {
        this.opAbbreviation = opAbbreviation;
        this.nodifySetFiled("opAbbreviation", opAbbreviation);
        return this;
    }

    public String getOpAbbreviation() {
        this.nodifyGetFiled("opAbbreviation");
        return opAbbreviation;
    }

    public EtmsOpAttemperLineEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public EtmsOpAttemperLineEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public EtmsOpAttemperLineEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public EtmsOpAttemperLineEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public EtmsOpAttemperLineEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public EtmsOpAttemperLineEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public EtmsOpAttemperLineEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public EtmsOpAttemperLineEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public EtmsOpAttemperLineEntity setOpLong(BigDecimal opLong) {
        this.opLong = opLong;
        this.nodifySetFiled("opLong", opLong);
        return this;
    }

    public BigDecimal getOpLong() {
        this.nodifyGetFiled("opLong");
        return opLong;
    }

    public EtmsOpAttemperLineEntity setOpLat(BigDecimal opLat) {
        this.opLat = opLat;
        this.nodifySetFiled("opLat", opLat);
        return this;
    }

    public BigDecimal getOpLat() {
        this.nodifyGetFiled("opLat");
        return opLat;
    }

    public EtmsOpAttemperLineEntity setLoadLong(BigDecimal loadLong) {
        this.loadLong = loadLong;
        this.nodifySetFiled("loadLong", loadLong);
        return this;
    }

    public BigDecimal getLoadLong() {
        this.nodifyGetFiled("loadLong");
        return loadLong;
    }

    public EtmsOpAttemperLineEntity setLoadLat(BigDecimal loadLat) {
        this.loadLat = loadLat;
        this.nodifySetFiled("loadLat", loadLat);
        return this;
    }

    public BigDecimal getLoadLat() {
        this.nodifyGetFiled("loadLat");
        return loadLat;
    }

}
