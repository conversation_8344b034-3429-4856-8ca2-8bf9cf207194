package com.eci.project.omsOrderFwxmCrkZzfw.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.omsOrderFwxmCrkZzfw.entity.OmsOrderFwxmCrkZzfwEntity;

import org.springframework.stereotype.Service;


/**
* 委托内容-仓储-增值服务明细Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-05-21
*/
@Service
public class OmsOrderFwxmCrkZzfwVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(OmsOrderFwxmCrkZzfwEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(OmsOrderFwxmCrkZzfwEntity entity, BusinessType businessType) {

    }

}
