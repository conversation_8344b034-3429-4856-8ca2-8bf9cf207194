package com.eci.project.fzgjTaskLimitationPt.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.project.fzgjTaskLimitation.entity.FzgjTaskLimitationEntity;
import com.eci.project.fzgjTaskLimitationPt.service.FzgjTaskLimitationPtService;
import com.eci.project.fzgjTaskLimitationPt.service.IFzgjTaskLimitationPtService;
import com.eci.project.fzgjTaskLimitationPt.entity.FzgjTaskLimitationPtEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.EciLog;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


/**
* 平台级作业环节及参考时效Controller
*
* @<NAME_EMAIL>
* @date 2025-03-28
*/
@Api(tags = "平台级作业环节及参考时效")
@RestController
@RequestMapping("/fzgjTaskLimitationPt")
public class FzgjTaskLimitationPtController extends EciBaseController {

    @Autowired
    private IFzgjTaskLimitationPtService fzgjTaskLimitationPtService;

    @Autowired
    private FzgjTaskLimitationPtService fzgjTaskLimitationPtServiceZsr;
    /**
    * 根据实体插入数据库
    * @param entity
    * @return
    */
    @ApiOperation("平台级作业环节及参考时效:保存")
    @EciLog(title = "平台级作业环节及参考时效:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public ResponseMsg save(@RequestBody FzgjTaskLimitationPtEntity entity){
        return ResponseMsgUtil.success(10001,fzgjTaskLimitationPtService.save(entity));
    }

    /**
    * 根据条件查询一个集合
    * @param entity
    * @return
    */
    @ApiOperation("平台级作业环节及参考时效:查询列表")
    @EciLog(title = "平台级作业环节及参考时效:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    public ResponseMsg selectList(@RequestBody FzgjTaskLimitationPtEntity entity){
        return ResponseMsgUtil.success(10001,fzgjTaskLimitationPtService.selectList(entity));
    }

    /**
    * 根据targetCode【服务项目的code】查询作业环节数据
    * @param jsonString
    * @return
    */
    @ApiOperation("平台级作业环节及参考时效:查询列表")
    @EciLog(title = "平台级作业环节及参考时效:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectListByTargetCode")
    public ResponseMsg selectListByTargetCode(@RequestBody String jsonString){
        return ResponseMsgUtil.success(10001,fzgjTaskLimitationPtServiceZsr.selectListByTargetCode(jsonString));
    }
    /**
    * 根据targetCode【服务项目的code】查询作业环节数据
    * @param jsonString
    * @return
    */
    @ApiOperation("平台级作业环节及参考时效:数据上移下移")
    @EciLog(title = "平台级作业环节及参考时效:数据上移下移", businessType = BusinessType.SELECT)
    @PostMapping("/updateSeq")
    public ResponseMsg updateSeq(@RequestBody String jsonString){
        return ResponseMsgUtil.success(10001,fzgjTaskLimitationPtServiceZsr.updateSeq(jsonString));
    }

    /**
    * 分页查询
    * @param entity
    * @return
    */
    @ApiOperation("平台级作业环节及参考时效:分页查询列表")
    @EciLog(title = "平台级作业环节及参考时效:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    public ResponseMsg selectPageList(@RequestBody FzgjTaskLimitationPtEntity entity){
        return ResponseMsgUtil.success(10001,fzgjTaskLimitationPtService.queryPageList(entity));
    }

    /**
    * 根据ID查一条记录
    * @param entity
    * @return
    */
    @ApiOperation("平台级作业环节及参考时效:根据ID查一条")
    @EciLog(title = "平台级作业环节及参考时效:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    public ResponseMsg selectOneById(@RequestBody FzgjTaskLimitationPtEntity entity){
        return ResponseMsgUtil.success(10001,fzgjTaskLimitationPtService.selectOneById(entity.getGuid()));
    }


    /**
    * 根据ID删除
    * @param entity
    * @return
    */
    @ApiOperation("平台级作业环节及参考时效:根据ID删除一条")
    @EciLog(title = "平台级作业环节及参考时效:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    public ResponseMsg deleteById(@RequestBody FzgjTaskLimitationPtEntity entity){
        return ResponseMsgUtil.success(10001,fzgjTaskLimitationPtService.deleteById(entity.getGuid()));
    }

    /**
    * 根据IDS删除
    *
    * @param entity
    * @return
    */
    @ApiOperation("平台级作业环节及参考时效:根据ID字符串删除多条")
    @EciLog(title = "平台级作业环节及参考时效:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    public ResponseMsg deleteByIds(@RequestBody FzgjTaskLimitationPtEntity entity) {
        return ResponseMsgUtil.success(10001, fzgjTaskLimitationPtService.deleteByIds(entity.getIds()));
    }
    /**
     * 查询作业环节(可选)
     *
     * @param entity
     * @return
     */
    @ApiOperation("作业环节及标准时效:根据服务类型查询作业环节(可选)")
    @EciLog(title = "作业环节及标准时效:根据服务类型查询作业环节(可选)", businessType = BusinessType.SELECT)
    @PostMapping("/getTaskChecked")
    public ResponseMsg getTaskChecked(@RequestBody FzgjTaskLimitationEntity entity) {
        return ResponseMsgUtil.success(10001, fzgjTaskLimitationPtService.getTaskChecked(entity.getTargetCode()));
    }

}