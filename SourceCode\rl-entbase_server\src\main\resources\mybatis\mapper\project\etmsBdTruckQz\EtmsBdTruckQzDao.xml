<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.etmsBdTruckQz.dao.EtmsBdTruckQzDao">
    <resultMap type="EtmsBdTruckQzEntity" id="EtmsBdTruckQzResult">
        <result property="guid" column="GUID"/>
        <result property="truckSpceGuid" column="TRUCK_SPCE_GUID"/>
        <result property="truckNo" column="TRUCK_NO"/>
        <result property="isGk" column="IS_GK"/>
        <result property="partnerGuid" column="PARTNER_GUID"/>
        <result property="gpsMode" column="GPS_MODE"/>
        <result property="gpsNo" column="GPS_NO"/>
        <result property="driverGuid" column="DRIVER_GUID"/>
        <result property="status" column="STATUS"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createCompany" column="CREATE_COMPANY"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="memo" column="MEMO"/>
        <result property="isUser" column="IS_USER"/>
        <result property="llOil" column="LL_OIL"/>
        <result property="licenseDate" column="LICENSE_DATE"/>
        <result property="ratingDate" column="RATING_DATE"/>
        <result property="operationDate" column="OPERATION_DATE"/>
        <result property="carType" column="CAR_TYPE"/>
        <result property="reside" column="RESIDE"/>
        <result property="link" column="LINK"/>
        <result property="tel" column="TEL"/>
        <result property="truckVin" column="TRUCK_VIN"/>
        <result property="tlength" column="TLENGTH"/>
        <result property="tweight" column="TWEIGHT"/>
        <result property="trailerNo" column="TRAILER_NO"/>
        <result property="attributeCode" column="ATTRIBUTE_CODE"/>
        <result property="orgDepId" column="ORG_DEP_ID"/>
        <result property="orgDepCode" column="ORG_DEP_CODE"/>
        <result property="orgDepName" column="ORG_DEP_NAME"/>
        <result property="ssoCompanyGuid" column="SSO_COMPANY_GUID"/>
        <result property="orgCode" column="ORG_CODE"/>
        <result property="orgName" column="ORG_NAME"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="zbzlKg" column="ZBZL_KG"/>
        <result property="cllx" column="CLLX"/>
        <result property="clcc" column="CLCC"/>
        <result property="carColor" column="CAR_COLOR"/>
        <result property="checkStatus" column="CHECK_STATUS"/>
        <result property="checkUser" column="CHECK_USER"/>
        <result property="checkRmk" column="CHECK_RMK"/>
        <result property="truckType" column="TRUCK_TYPE"/>
        <result property="carLong" column="CAR_LONG"/>
        <result property="carLongType" column="CAR_LONG_TYPE"/>
        <result property="isBt" column="IS_BT"/>
    </resultMap>
    <sql id="selectEtmsBdTruckQzEntityVo">
        select
            GUID,
            TRUCK_SPCE_GUID,
            TRUCK_NO,
            IS_GK,
            PARTNER_GUID,
            GPS_MODE,
            GPS_NO,
            DRIVER_GUID,
            STATUS,
            CREATE_DATE,
            CREATE_USER,
            CREATE_COMPANY,
            UPDATE_DATE,
            UPDATE_USER,
            MEMO,
            IS_USER,
            LL_OIL,
            LICENSE_DATE,
            RATING_DATE,
            OPERATION_DATE,
            CAR_TYPE,
            RESIDE,
            LINK,
            TEL,
            TRUCK_VIN,
            TLENGTH,
            TWEIGHT,
            TRAILER_NO,
            ATTRIBUTE_CODE,
            ORG_DEP_ID,
            ORG_DEP_CODE,
            ORG_DEP_NAME,
            SSO_COMPANY_GUID,
            ORG_CODE,
            ORG_NAME,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            CREATE_USER_NAME,
            UPDATE_USER_NAME,
            ZBZL_KG,
            CLLX,
            CLCC,
            CAR_COLOR,
            CHECK_STATUS,
            CHECK_USER,
            CHECK_RMK,
            TRUCK_TYPE,
            CAR_LONG,
            CAR_LONG_TYPE,
            IS_BT
        from ETMS_BD_TRUCK_QZ
    </sql>
</mapper>

