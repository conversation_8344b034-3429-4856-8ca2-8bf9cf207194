package com.eci.project.etmsBdTruckGcQz.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.etmsBdTruckGcQz.service.EtmsBdTruckGcQzService;
import com.eci.project.etmsBdTruckGcQz.entity.EtmsBdTruckGcQzEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 挂车号Controller
*
* @<NAME_EMAIL>
* @date 2025-04-16
*/
@Api(tags = "挂车号")
@RestController
@RequestMapping("/etmsBdTruckGcQz")
public class EtmsBdTruckGcQzController extends EciBaseController {

    @Autowired
    private EtmsBdTruckGcQzService etmsBdTruckGcQzService;


    @ApiOperation("挂车号:保存")
    @EciLog(title = "挂车号:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody EtmsBdTruckGcQzEntity entity){
        EtmsBdTruckGcQzEntity etmsBdTruckGcQzEntity =etmsBdTruckGcQzService.save(entity);
        return ResponseMsgUtil.success(10001,etmsBdTruckGcQzEntity);
    }


    @ApiOperation("挂车号:查询列表")
    @EciLog(title = "挂车号:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody EtmsBdTruckGcQzEntity entity){
        List<EtmsBdTruckGcQzEntity> etmsBdTruckGcQzEntities = etmsBdTruckGcQzService.selectList(entity);
        return ResponseMsgUtil.success(10001,etmsBdTruckGcQzEntities);
    }


    @ApiOperation("挂车号:分页查询列表")
    @EciLog(title = "挂车号:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody EtmsBdTruckGcQzEntity entity){
        TgPageInfo tgPageInfo = etmsBdTruckGcQzService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("挂车号:根据ID查一条")
    @EciLog(title = "挂车号:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody EtmsBdTruckGcQzEntity entity){
        EtmsBdTruckGcQzEntity  etmsBdTruckGcQzEntity = etmsBdTruckGcQzService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,etmsBdTruckGcQzEntity);
    }


    @ApiOperation("挂车号:根据ID删除一条")
    @EciLog(title = "挂车号:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody EtmsBdTruckGcQzEntity entity){
        int count = etmsBdTruckGcQzService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("挂车号:根据ID字符串删除多条")
    @EciLog(title = "挂车号:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody EtmsBdTruckGcQzEntity entity) {
        int count = etmsBdTruckGcQzService.deleteByList(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }

}