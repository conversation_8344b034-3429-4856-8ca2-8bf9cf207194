package com.eci.project.fzgjBdDistrict.entity;

import io.swagger.annotations.ApiModelProperty;

/**
 * @ClassName: FzgjBdDistrictRealtionShipEntity
 * @Author: guangyan.mei
 * @Date: 2025/3/26 15:26
 * @Description: TODO
 */
public class FzgjBdDistrictRealtionEntity {

    /**
     * 国家编码
     */
    @ApiModelProperty("countryCode(50)")
    private String countryCode;
    /**
     * 国家名称
     */
    @ApiModelProperty("countryName(50)")
    private String countryName;
    /**
     * 省份编码
     */
    @ApiModelProperty("provinceCode(50)")
    private String provinceCode;
    /**
     * 省份名称
     */
    @ApiModelProperty("provinceName(50)")
    private String provinceName;
    /**
     * 市编码
     */
    @ApiModelProperty("cityCode(50)")
    private String cityCode;

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getCountryName() {
        return countryName;
    }

    public void setCountryName(String countryName) {
        this.countryName = countryName;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getDistrictCode() {
        return districtCode;
    }

    public void setDistrictCode(String districtCode) {
        this.districtCode = districtCode;
    }

    public String getDistrictName() {
        return districtName;
    }

    public void setDistrictName(String districtName) {
        this.districtName = districtName;
    }

    /**
     * 市名称
     */
    @ApiModelProperty("cityName(50)")
    private String cityName;
    /**
     * 区县编码
     */
    @ApiModelProperty("districtCode(50)")
    private String districtCode;
    /**
     * 区县名称
     */
    @ApiModelProperty("districtName(50)")
    private String districtName;

}
