package com.eci.project.fzgjBdOpTypeUser.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjBdOpTypeUser.entity.FzgjBdOpTypeUserEntity;

import org.springframework.stereotype.Service;


/**
* 用户-业务伙伴授权Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-03-27
*/
@Service
public class FzgjBdOpTypeUserVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjBdOpTypeUserEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjBdOpTypeUserEntity entity, BusinessType businessType) {

    }

}
