package com.eci.project.crmCustomerSfhfAddress.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.eci.project.crmCustomerHzfwGys.entity.CrmCustomerHzfwGysEntity;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.crmCustomerSfhfAddress.entity.CrmCustomerSfhfAddressEntity;
import org.apache.ibatis.annotations.Select;

import java.util.List;


/**
* 业务伙伴收发货方常用地区Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-05-20
*/
public interface CrmCustomerSfhfAddressDao extends EciBaseDao<CrmCustomerSfhfAddressEntity> {
    @Select("select * from (\n" +
            "SELECT A.*,B.COUNTRY_CH_NAME country,B.PROVINCE_NAME province,B.CITY_NAME city,B.DISTRICT_NAME district,B.AREA_NAME opAreaName FROM CRM_CUSTOMER_SFHF_ADDRESS A\n" +
            "LEFT JOIN V_FZGJ_BD_AREA B ON A.OP_AREA=B.AREA_CODE AND A.GROUP_CODE=B.GROUP_CODE\n" +
            ") A  ${ew.customSqlSegment}")
    List<CrmCustomerSfhfAddressEntity> selectlist(@Param(Constants.WRAPPER) Wrapper queryWrapper);
}