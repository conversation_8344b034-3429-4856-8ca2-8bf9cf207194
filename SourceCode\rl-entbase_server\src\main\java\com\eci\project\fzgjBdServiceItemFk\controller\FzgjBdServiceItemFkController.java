package com.eci.project.fzgjBdServiceItemFk.controller;

import com.eci.common.ZsrJson;
import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjBdOmsPagesFk.entity.FzgjBdOmsPagesFkEntity;
import com.eci.project.fzgjBdOmsPagesFk.service.FzgjBdOmsPagesFkService;
import com.eci.project.fzgjBdServiceItemFk.service.FzgjBdServiceItemFkService;
import com.eci.project.fzgjBdServiceItemFk.entity.FzgjBdServiceItemFkEntity;
import com.eci.project.fzgjBdServiceItemPages.entity.FzgjBdServiceItemPagesEntity;
import com.eci.project.fzgjBdServiceItemPagesPt.entity.FzgjBdServiceItemPagesPtEntity;
import com.eci.sso.login.entity.UserInfo;
import com.eci.sso.role.entity.UserContext;
import com.eci.wu.core.DataTable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
* 企业服务项目对应反馈页面Controller
*
* @<NAME_EMAIL>
* @date 2025-04-14
*/
@Api(tags = "企业服务项目对应反馈页面")
@RestController
@RequestMapping("/fzgjBdServiceItemFk")
public class FzgjBdServiceItemFkController extends EciBaseController {

    @Autowired
    private FzgjBdServiceItemFkService fzgjBdServiceItemFkService;


    @Autowired
    private FzgjBdOmsPagesFkService fzgjBdOmsPagesFkService;



    @ApiOperation("企业服务项目对应反馈页面:查询列表")
    @EciLog(title = "企业服务项目对应反馈页面:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjBdServiceItemFkEntity entity){
        List<FzgjBdServiceItemFkEntity> fzgjBdServiceItemFkEntities = fzgjBdServiceItemFkService.selectList(entity);
        return ResponseMsgUtil.success(10001,fzgjBdServiceItemFkEntities);
    }


    @ApiOperation("企业服务项目对应反馈页面:分页查询列表")
    @EciLog(title = "企业服务项目对应反馈页面:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjBdServiceItemFkEntity entity){
        TgPageInfo tgPageInfo = fzgjBdServiceItemFkService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("企业服务项目对应反馈页面:根据ID查一条")
    @EciLog(title = "企业服务项目对应反馈页面:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjBdServiceItemFkEntity entity){
        FzgjBdServiceItemFkEntity  fzgjBdServiceItemFkEntity = fzgjBdServiceItemFkService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,fzgjBdServiceItemFkEntity);
    }


    @ApiOperation("企业服务项目对应反馈页面:根据ID删除一条")
    @EciLog(title = "企业服务项目对应反馈页面:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjBdServiceItemFkEntity entity){
        int count = fzgjBdServiceItemFkService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("平台服务项目对应页面编辑区:根据ID字符串删除多条")
    @EciLog(title = "平台服务项目对应页面编辑区:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody String jsonstring) {
        ZsrJson zsrJson= ZsrJson.parse(jsonstring);
        if(!zsrJson.exists("listkey")) return ResponseMsgUtil.error(10000,"请选择需要移除的业务产品/项目");
        List<String> listKey=zsrJson.getList("listkey",String.class);

        int count = fzgjBdServiceItemFkService.deleteByIds(String.join(",",listKey));
        return ResponseMsgUtil.success(10001,count);
    }
    @ApiOperation("平台级服务项目:获取订单编辑区")
    @EciLog(title = "平台级服务项目:获取订单编辑区", businessType = BusinessType.SELECT)
    @PostMapping("/getCheckEditItem")
    @EciAction()
    public ResponseMsg getCheckEditItem(@RequestBody FzgjBdServiceItemPagesEntity entity){
        DataTable dt= fzgjBdServiceItemFkService.getCheckEditItem(entity.getServiceItemCode());
        return ResponseMsgUtil.success(10001,dt);
    }
    @ApiOperation("平台服务项目对应页面编辑区:保存")
    @EciLog(title = "平台服务项目对应页面编辑区:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody String jsonstring){
        ZsrJson zsrJson= ZsrJson.parse(jsonstring);
        if(!zsrJson.exists("listkey")) return ResponseMsgUtil.error(10000,"请选择需要添加的业务产品/项目");
        if(!zsrJson.exists("serviceItemId")) return ResponseMsgUtil.error(10000,"未找到服务ID");
        List<String> listKey=zsrJson.getList("listkey",String.class);
        String serviceItemId= zsrJson.getString("serviceItemId");
        UserInfo user= UserContext.getUserInfo();
        List<FzgjBdServiceItemFkEntity> list=new ArrayList<>();
        listKey.forEach(p->{
            FzgjBdOmsPagesFkEntity entity= fzgjBdOmsPagesFkService.selectOneById(p);
            FzgjBdServiceItemFkEntity saveModel=new FzgjBdServiceItemFkEntity();
            saveModel.setCode(entity.getCode());
            saveModel.setServiceitemid(serviceItemId);
            saveModel.setMemo(entity.getMemo());
            saveModel.setName(entity.getName());
            saveModel.setStatus(entity.getStatus());
            saveModel.setCompanyCode(user.getCompanyCode());
            saveModel.setCompanyName(user.getCompanyName());
            saveModel.setSeq(Float.valueOf(entity.getSeq()));
            saveModel.setGroupCode(user.getCompanyCode());
            saveModel.setGroupName(user.getCompanyName());
            saveModel.setCreateDate(new Date());
            saveModel.setUpdateDate(new Date());
            saveModel.setCreateUser(user.getUserLoginNo());
            saveModel.setCreateUserName(user.getTrueName());
            saveModel.setUpdateUser(user.getUserLoginNo());
            saveModel.setUpdateUserName(user.getTrueName());
            list.add(saveModel);
        });
        fzgjBdServiceItemFkService.insertBatch(list);
        return ResponseMsgUtil.success(10001);
    }


}