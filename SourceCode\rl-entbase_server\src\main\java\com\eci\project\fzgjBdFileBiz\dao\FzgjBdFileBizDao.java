package com.eci.project.fzgjBdFileBiz.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.fzgjBdFileBiz.entity.FzgjBdFileBizEntity;


/**
* 业务附件类型及授权Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-04-11
*/
public interface FzgjBdFileBizDao extends EciBaseDao<FzgjBdFileBizEntity> {

}