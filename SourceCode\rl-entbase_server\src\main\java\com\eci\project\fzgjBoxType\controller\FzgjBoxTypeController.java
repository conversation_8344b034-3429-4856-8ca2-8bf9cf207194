package com.eci.project.fzgjBoxType.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjBoxType.service.FzgjBoxTypeService;
import com.eci.project.fzgjBoxType.entity.FzgjBoxTypeEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 集装箱类型Controller
*
* @<NAME_EMAIL>
* @date 2025-04-17
*/
@Api(tags = "集装箱类型")
@RestController
@RequestMapping("/fzgjBoxType")
public class FzgjBoxTypeController extends EciBaseController {

    @Autowired
    private FzgjBoxTypeService fzgjBoxTypeService;


    @ApiOperation("集装箱类型:保存")
    @EciLog(title = "集装箱类型:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody FzgjBoxTypeEntity entity){
        FzgjBoxTypeEntity fzgjBoxTypeEntity =fzgjBoxTypeService.save(entity);
        return ResponseMsgUtil.success(10001,fzgjBoxTypeEntity);
    }


    @ApiOperation("集装箱类型:查询列表")
    @EciLog(title = "集装箱类型:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjBoxTypeEntity entity){
        List<FzgjBoxTypeEntity> fzgjBoxTypeEntities = fzgjBoxTypeService.selectList(entity);
        return ResponseMsgUtil.success(10001,fzgjBoxTypeEntities);
    }


    @ApiOperation("集装箱类型:分页查询列表")
    @EciLog(title = "集装箱类型:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjBoxTypeEntity entity){
        TgPageInfo tgPageInfo = fzgjBoxTypeService.queryPageList(entity);
        return ResponseMsgUtil.successPlus(10001,tgPageInfo);
    }


    @ApiOperation("集装箱类型:根据ID查一条")
    @EciLog(title = "集装箱类型:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjBoxTypeEntity entity){
        FzgjBoxTypeEntity  fzgjBoxTypeEntity = fzgjBoxTypeService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,fzgjBoxTypeEntity);
    }


    @ApiOperation("集装箱类型:根据ID删除一条")
    @EciLog(title = "集装箱类型:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjBoxTypeEntity entity){
        int count = fzgjBoxTypeService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("集装箱类型:根据ID字符串删除多条")
    @EciLog(title = "集装箱类型:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjBoxTypeEntity entity) {
        int count = fzgjBoxTypeService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}