package com.eci.project.omsGhSend.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.omsGhSend.entity.OmsGhSendEntity;


/**
* OMS固化路由表Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-05-16
*/
public interface OmsGhSendDao extends EciBaseDao<OmsGhSendEntity> {

}