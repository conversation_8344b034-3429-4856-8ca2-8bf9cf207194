package com.eci.project.omsOrderFwxmZhysXl.service;

import com.eci.common.db.DBHelper;
import com.eci.common.util.StringUtils;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.omsOrderFwxmTmsXlXl.entity.OmsOrderFwxmTmsXlXlDTOEntity;
import com.eci.project.omsOrderFwxmZhysXl.dao.OmsOrderFwxmZhysXlDao;
import com.eci.project.omsOrderFwxmZhysXl.entity.OmsOrderFwxmZhysXlDTOEntity;
import com.eci.project.omsOrderFwxmZhysXl.entity.OmsOrderFwxmZhysXlEntity;
import com.eci.project.omsOrderFwxmZhysXl.validate.OmsOrderFwxmZhysXlVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 综合运输-线路Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-06-13
*/
@Service
@Slf4j
public class OmsOrderFwxmZhysXlService implements EciBaseService<OmsOrderFwxmZhysXlEntity> {

    @Autowired
    private OmsOrderFwxmZhysXlDao omsOrderFwxmZhysXlDao;

    @Autowired
    private OmsOrderFwxmZhysXlVal omsOrderFwxmZhysXlVal;


    @Override
    public TgPageInfo queryPageList(OmsOrderFwxmZhysXlEntity entity) {
        EciQuery<OmsOrderFwxmZhysXlEntity> eciQuery = EciQuery.buildQuery(entity);
        List<OmsOrderFwxmZhysXlEntity> entities = omsOrderFwxmZhysXlDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderFwxmZhysXlEntity save(OmsOrderFwxmZhysXlEntity entity) {
        // 返回实体对象
        OmsOrderFwxmZhysXlEntity omsOrderFwxmZhysXlEntity = null;
        omsOrderFwxmZhysXlVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            omsOrderFwxmZhysXlEntity = omsOrderFwxmZhysXlDao.insertOne(entity);

        }else{

            omsOrderFwxmZhysXlEntity = omsOrderFwxmZhysXlDao.updateByEntityId(entity);

        }
        return omsOrderFwxmZhysXlEntity;
    }

    @Override
    public List<OmsOrderFwxmZhysXlEntity> selectList(OmsOrderFwxmZhysXlEntity entity) {
        return omsOrderFwxmZhysXlDao.selectList(entity);
    }

    @Override
    public OmsOrderFwxmZhysXlEntity selectOneById(Serializable id) {
        return omsOrderFwxmZhysXlDao.selectById(id);
    }


    @Override
    public void insertBatch(List<OmsOrderFwxmZhysXlEntity> list) {
        omsOrderFwxmZhysXlDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return omsOrderFwxmZhysXlDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return omsOrderFwxmZhysXlDao.deleteById(id);
    }

    public List<OmsOrderFwxmZhysXlDTOEntity> getOrderFwxmTmsXlSearch(OmsOrderFwxmZhysXlDTOEntity entity){
        StringBuilder stringBuilder=new StringBuilder();
        stringBuilder.append("SELECT  /*ECI_SELECT*/ A.LINE_NO,A.ORDER_NO,C.PRE_NO,A.XZRWBH ");
        stringBuilder.append("        ,A.LINE_SEQ,A.DCYSXL,(case when A.DCYSXL = '*********' then '国际公路' else '国内公路' end) AS FWXM_NAME,A.XZRWBH,A.CREATE_USER  ");
        stringBuilder.append("        ,A.CREATE_USER_NAME,A.CREATE_DATE,A.UPDATE_USER,A.UPDATE_USER_NAME,A.UPDATE_DATE ");
        stringBuilder.append("        ,A.COMPANY_CODE,A.COMPANY_NAME,A.NODE_CODE,A.NODE_NAME,A.GROUP_CODE");
        stringBuilder.append("        ,A.GROUP_NAME  /*ECI_SELECT*/ ");
        stringBuilder.append(" FROM OMS_ORDER_FWXM_ZHYS_XL A ");
        stringBuilder.append(" INNER JOIN OMS_ORDER_FWXM_ZHYS C ON C.ZHYS_NO=A.ZHYS_GUID");
        stringBuilder.append(" WHERE 1=1");
        if(StringUtils.hasValue(entity.getOrderNo())){
            stringBuilder.append(" AND A.ORDER_NO ='" + entity.getOrderNo() + "'");
        }
        if(StringUtils.hasValue(entity.getPreNo())){
            stringBuilder.append(" AND C.PRE_NO = '" + entity.getPreNo() + "'");
        }
        if(StringUtils.hasValue(entity.getFwxmCode())){
            stringBuilder.append(" AND C.FWXM_CODE = '" + entity.getFwxmCode() + "'");
        }
        List<OmsOrderFwxmZhysXlDTOEntity> list= DBHelper.selectList(stringBuilder.toString(), OmsOrderFwxmZhysXlDTOEntity.class);
        return list;
    }

}