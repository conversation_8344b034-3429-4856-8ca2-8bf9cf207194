<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.omsOrderFwxmWorkXzwt.dao.OmsOrderFwxmWorkXzwtDao">
    <resultMap type="OmsOrderFwxmWorkXzwtEntity" id="OmsOrderFwxmWorkXzwtResult">
        <result property="xzwtNo" column="XZWT_NO"/>
        <result property="orderNo" column="ORDER_NO"/>
        <result property="billCode" column="BILL_CODE"/>
        <result property="docNo" column="DOC_NO"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="sendFlag" column="SEND_FLAG"/>
        <result property="sendUser" column="SEND_USER"/>
        <result property="sendNodeCode" column="SEND_NODE_CODE"/>
        <result property="sendNodeName" column="SEND_NODE_NAME"/>
        <result property="sendUserName" column="SEND_USER_NAME"/>
        <result property="opCompleteOkDate" column="OP_COMPLETE_OK_DATE"/>
        <result property="opCompleteOk" column="OP_COMPLETE_OK"/>
        <result property="dataOkDate" column="DATA_OK_DATE"/>
        <result property="dataOk" column="DATA_OK"/>
        <result property="arapOk" column="ARAP_OK"/>
        <result property="arapOkDate" column="ARAP_OK_DATE"/>
        <result property="gysCode" column="GYS_CODE"/>
        <result property="gysName" column="GYS_NAME"/>
        <result property="nodeCodeNb" column="NODE_CODE_NB"/>
        <result property="nodeNameNb" column="NODE_NAME_NB"/>
        <result property="gysGroupCode" column="GYS_GROUP_CODE"/>
        <result property="gysGroupName" column="GYS_GROUP_NAME"/>
        <result property="responseCode" column="RESPONSE_CODE"/>
        <result property="bizRegId" column="BIZ_REG_ID"/>
        <result property="isAp" column="IS_AP"/>
    </resultMap>

    <sql id="selectOmsOrderFwxmWorkXzwtEntityVo">
        select
            XZWT_NO,
            ORDER_NO,
            BILL_CODE,
            DOC_NO,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            SEND_FLAG,
            SEND_USER,
            SEND_NODE_CODE,
            SEND_NODE_NAME,
            SEND_USER_NAME,
            OP_COMPLETE_OK_DATE,
            OP_COMPLETE_OK,
            DATA_OK_DATE,
            DATA_OK,
            ARAP_OK,
            ARAP_OK_DATE,
            GYS_CODE,
            GYS_NAME,
            NODE_CODE_NB,
            NODE_NAME_NB,
            GYS_GROUP_CODE,
            GYS_GROUP_NAME,
            RESPONSE_CODE,
            BIZ_REG_ID,
            IS_AP
        from OMS_ORDER_FWXM_WORK_XZWT
    </sql>
</mapper>