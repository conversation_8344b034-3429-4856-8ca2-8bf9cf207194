package com.eci.project.omsOrderFwxmWorkTrace.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 作业跟踪对象 OMS_ORDER_FWXM_WORK_TRACE
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-06-05
*/
@ApiModel("作业跟踪")
@TableName("OMS_ORDER_FWXM_WORK_TRACE")
@FieldNameConstants
public class OmsOrderFwxmWorkTraceEntity extends EciBaseEntity{
    /**
    * GUID
    */
    @ApiModelProperty("GUID(36)")
    @TableId("GUID")
    private String guid;

    /**
    * 协作任务编号
    */
    @ApiModelProperty("协作任务编号(50)")
    @TableField("WORK_NO")
    private String workNo;

    /**
    * 订单号
    */
    @ApiModelProperty("订单号(36)")
    @TableField("ORDER_NO")
    private String orderNo;

    /**
    * 委托单编号
    */
    @ApiModelProperty("委托单编号(36)")
    @TableField("PRE_NO")
    private String preNo;

    /**
    * 环节顺序
    */
    @ApiModelProperty("环节顺序(22)")
    @TableField("LINK_SEQ")
    private Integer linkSeq;

    /**
    * 环节代码
    */
    @ApiModelProperty("环节代码(20)")
    @TableField("LINK_CODE")
    private String linkCode;

    /**
    * 计划完成时间
    */
    @ApiModelProperty("计划完成时间(7)")
    @TableField("PLAN_OK_DATE")
    private Date planOkDate;

    @ApiModelProperty("计划完成时间开始")
    @TableField(exist=false)
    private Date planOkDateStart;

    @ApiModelProperty("计划完成时间结束")
    @TableField(exist=false)
    private Date planOkDateEnd;

    /**
    * 实际完成时间
    */
    @ApiModelProperty("实际完成时间(7)")
    @TableField("ACTUAL_OK_DATE")
    private Date actualOkDate;

    @ApiModelProperty("实际完成时间开始")
    @TableField(exist=false)
    private Date actualOkDateStart;

    @ApiModelProperty("实际完成时间结束")
    @TableField(exist=false)
    private Date actualOkDateEnd;

    /**
    * 作业描述
    */
    @ApiModelProperty("作业描述(500)")
    @TableField("JOBD")
    private String jobd;

    /**
    * 是否异常
    */
    @ApiModelProperty("是否异常(1)")
    @TableField("IS_EXCEPTION")
    private String isException;

    /**
    * 是否延迟
    */
    @ApiModelProperty("是否延迟(1)")
    @TableField("IS_DELAY")
    private String isDelay;

    /**
    * 数据录入方(供应商/我司)
    */
    @ApiModelProperty("数据录入方(供应商/我司)(10)")
    @TableField("SJLRF")
    private String sjlrf;

    /**
    * 数据录入方式(我司作业平台/对外接口)
    */
    @ApiModelProperty("数据录入方式(我司作业平台/对外接口)(10)")
    @TableField("SJLR_CODE")
    private String sjlrCode;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 最后修改日期
    */
    @ApiModelProperty("最后修改日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("最后修改日期开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("最后修改日期结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(200)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * OMS_ORDER_FEXM_WORK  GUID
    */
    @ApiModelProperty("OMS_ORDER_FEXM_WORK  GUID(50)")
    @TableField("WORK_GUID")
    private String workGuid;

    /**
    * 业务数据唯一注册编号
    */
    @ApiModelProperty("业务数据唯一注册编号(50)")
    @TableField("BIZ_REG_ID")
    private String bizRegId;

    /**
    * 是否发送邮件（香远）
    */
    @ApiModelProperty("是否发送邮件（香远）(1)")
    @TableField("IS_SEND_EMAIL")
    private String isSendEmail;

    /**
    * 是否撤销YN
    */
    @ApiModelProperty("是否撤销YN(50)")
    @TableField("IS_REBACK")
    private String isReback;

    /**
    * 作业系统代码
    */
    @ApiModelProperty("作业系统代码(50)")
    @TableField("SYS_CODE")
    private String sysCode;

    /**
    * 传输时间
    */
    @ApiModelProperty("传输时间(7)")
    @TableField("TRN_DATE")
    private Date trnDate;

    @ApiModelProperty("传输时间开始")
    @TableField(exist=false)
    private Date trnDateStart;

    @ApiModelProperty("传输时间结束")
    @TableField(exist=false)
    private Date trnDateEnd;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public OmsOrderFwxmWorkTraceEntity() {
        this.setSubClazz(OmsOrderFwxmWorkTraceEntity.class);
    }

    public OmsOrderFwxmWorkTraceEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public OmsOrderFwxmWorkTraceEntity setWorkNo(String workNo) {
        this.workNo = workNo;
        this.nodifySetFiled("workNo", workNo);
        return this;
    }

    public String getWorkNo() {
        this.nodifyGetFiled("workNo");
        return workNo;
    }

    public OmsOrderFwxmWorkTraceEntity setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        this.nodifySetFiled("orderNo", orderNo);
        return this;
    }

    public String getOrderNo() {
        this.nodifyGetFiled("orderNo");
        return orderNo;
    }

    public OmsOrderFwxmWorkTraceEntity setPreNo(String preNo) {
        this.preNo = preNo;
        this.nodifySetFiled("preNo", preNo);
        return this;
    }

    public String getPreNo() {
        this.nodifyGetFiled("preNo");
        return preNo;
    }

    public OmsOrderFwxmWorkTraceEntity setLinkSeq(Integer linkSeq) {
        this.linkSeq = linkSeq;
        this.nodifySetFiled("linkSeq", linkSeq);
        return this;
    }

    public Integer getLinkSeq() {
        this.nodifyGetFiled("linkSeq");
        return linkSeq;
    }

    public OmsOrderFwxmWorkTraceEntity setLinkCode(String linkCode) {
        this.linkCode = linkCode;
        this.nodifySetFiled("linkCode", linkCode);
        return this;
    }

    public String getLinkCode() {
        this.nodifyGetFiled("linkCode");
        return linkCode;
    }

    public OmsOrderFwxmWorkTraceEntity setPlanOkDate(Date planOkDate) {
        this.planOkDate = planOkDate;
        this.nodifySetFiled("planOkDate", planOkDate);
        return this;
    }

    public Date getPlanOkDate() {
        this.nodifyGetFiled("planOkDate");
        return planOkDate;
    }

    public OmsOrderFwxmWorkTraceEntity setPlanOkDateStart(Date planOkDateStart) {
        this.planOkDateStart = planOkDateStart;
        this.nodifySetFiled("planOkDateStart", planOkDateStart);
        return this;
    }

    public Date getPlanOkDateStart() {
        this.nodifyGetFiled("planOkDateStart");
        return planOkDateStart;
    }

    public OmsOrderFwxmWorkTraceEntity setPlanOkDateEnd(Date planOkDateEnd) {
        this.planOkDateEnd = planOkDateEnd;
        this.nodifySetFiled("planOkDateEnd", planOkDateEnd);
        return this;
    }

    public Date getPlanOkDateEnd() {
        this.nodifyGetFiled("planOkDateEnd");
        return planOkDateEnd;
    }
    public OmsOrderFwxmWorkTraceEntity setActualOkDate(Date actualOkDate) {
        this.actualOkDate = actualOkDate;
        this.nodifySetFiled("actualOkDate", actualOkDate);
        return this;
    }

    public Date getActualOkDate() {
        this.nodifyGetFiled("actualOkDate");
        return actualOkDate;
    }

    public OmsOrderFwxmWorkTraceEntity setActualOkDateStart(Date actualOkDateStart) {
        this.actualOkDateStart = actualOkDateStart;
        this.nodifySetFiled("actualOkDateStart", actualOkDateStart);
        return this;
    }

    public Date getActualOkDateStart() {
        this.nodifyGetFiled("actualOkDateStart");
        return actualOkDateStart;
    }

    public OmsOrderFwxmWorkTraceEntity setActualOkDateEnd(Date actualOkDateEnd) {
        this.actualOkDateEnd = actualOkDateEnd;
        this.nodifySetFiled("actualOkDateEnd", actualOkDateEnd);
        return this;
    }

    public Date getActualOkDateEnd() {
        this.nodifyGetFiled("actualOkDateEnd");
        return actualOkDateEnd;
    }
    public OmsOrderFwxmWorkTraceEntity setJobd(String jobd) {
        this.jobd = jobd;
        this.nodifySetFiled("jobd", jobd);
        return this;
    }

    public String getJobd() {
        this.nodifyGetFiled("jobd");
        return jobd;
    }

    public OmsOrderFwxmWorkTraceEntity setIsException(String isException) {
        this.isException = isException;
        this.nodifySetFiled("isException", isException);
        return this;
    }

    public String getIsException() {
        this.nodifyGetFiled("isException");
        return isException;
    }

    public OmsOrderFwxmWorkTraceEntity setIsDelay(String isDelay) {
        this.isDelay = isDelay;
        this.nodifySetFiled("isDelay", isDelay);
        return this;
    }

    public String getIsDelay() {
        this.nodifyGetFiled("isDelay");
        return isDelay;
    }

    public OmsOrderFwxmWorkTraceEntity setSjlrf(String sjlrf) {
        this.sjlrf = sjlrf;
        this.nodifySetFiled("sjlrf", sjlrf);
        return this;
    }

    public String getSjlrf() {
        this.nodifyGetFiled("sjlrf");
        return sjlrf;
    }

    public OmsOrderFwxmWorkTraceEntity setSjlrCode(String sjlrCode) {
        this.sjlrCode = sjlrCode;
        this.nodifySetFiled("sjlrCode", sjlrCode);
        return this;
    }

    public String getSjlrCode() {
        this.nodifyGetFiled("sjlrCode");
        return sjlrCode;
    }

    public OmsOrderFwxmWorkTraceEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public OmsOrderFwxmWorkTraceEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public OmsOrderFwxmWorkTraceEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public OmsOrderFwxmWorkTraceEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public OmsOrderFwxmWorkTraceEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public OmsOrderFwxmWorkTraceEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public OmsOrderFwxmWorkTraceEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public OmsOrderFwxmWorkTraceEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public OmsOrderFwxmWorkTraceEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public OmsOrderFwxmWorkTraceEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public OmsOrderFwxmWorkTraceEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public OmsOrderFwxmWorkTraceEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public OmsOrderFwxmWorkTraceEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public OmsOrderFwxmWorkTraceEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public OmsOrderFwxmWorkTraceEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public OmsOrderFwxmWorkTraceEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public OmsOrderFwxmWorkTraceEntity setWorkGuid(String workGuid) {
        this.workGuid = workGuid;
        this.nodifySetFiled("workGuid", workGuid);
        return this;
    }

    public String getWorkGuid() {
        this.nodifyGetFiled("workGuid");
        return workGuid;
    }

    public OmsOrderFwxmWorkTraceEntity setBizRegId(String bizRegId) {
        this.bizRegId = bizRegId;
        this.nodifySetFiled("bizRegId", bizRegId);
        return this;
    }

    public String getBizRegId() {
        this.nodifyGetFiled("bizRegId");
        return bizRegId;
    }

    public OmsOrderFwxmWorkTraceEntity setIsSendEmail(String isSendEmail) {
        this.isSendEmail = isSendEmail;
        this.nodifySetFiled("isSendEmail", isSendEmail);
        return this;
    }

    public String getIsSendEmail() {
        this.nodifyGetFiled("isSendEmail");
        return isSendEmail;
    }

    public OmsOrderFwxmWorkTraceEntity setIsReback(String isReback) {
        this.isReback = isReback;
        this.nodifySetFiled("isReback", isReback);
        return this;
    }

    public String getIsReback() {
        this.nodifyGetFiled("isReback");
        return isReback;
    }

    public OmsOrderFwxmWorkTraceEntity setSysCode(String sysCode) {
        this.sysCode = sysCode;
        this.nodifySetFiled("sysCode", sysCode);
        return this;
    }

    public String getSysCode() {
        this.nodifyGetFiled("sysCode");
        return sysCode;
    }

    public OmsOrderFwxmWorkTraceEntity setTrnDate(Date trnDate) {
        this.trnDate = trnDate;
        this.nodifySetFiled("trnDate", trnDate);
        return this;
    }

    public Date getTrnDate() {
        this.nodifyGetFiled("trnDate");
        return trnDate;
    }

    public OmsOrderFwxmWorkTraceEntity setTrnDateStart(Date trnDateStart) {
        this.trnDateStart = trnDateStart;
        this.nodifySetFiled("trnDateStart", trnDateStart);
        return this;
    }

    public Date getTrnDateStart() {
        this.nodifyGetFiled("trnDateStart");
        return trnDateStart;
    }

    public OmsOrderFwxmWorkTraceEntity setTrnDateEnd(Date trnDateEnd) {
        this.trnDateEnd = trnDateEnd;
        this.nodifySetFiled("trnDateEnd", trnDateEnd);
        return this;
    }

    public Date getTrnDateEnd() {
        this.nodifyGetFiled("trnDateEnd");
        return trnDateEnd;
    }
}
