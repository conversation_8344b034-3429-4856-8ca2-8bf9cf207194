package com.eci.project.omsOrderFwxmTmsXl.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 委托内容-委托线路对象 OMS_ORDER_FWXM_TMS_XL
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-04-14
*/
@ApiModel("委托内容-委托线路")
@TableName("OMS_ORDER_FWXM_TMS_XL")
@FieldNameConstants
public class OmsOrderFwxmTmsXlEntity extends EciBaseEntity{
    /**
    * 委托单编号
    */
    @ApiModelProperty("委托单编号(36)")
    @TableField("PRE_NO")
    private String preNo;

    /**
    * 订单号
    */
    @ApiModelProperty("订单号(36)")
    @TableField("ORDER_NO")
    private String orderNo;

    /**
    * 需求唯一编号
    */
    @ApiModelProperty("需求唯一编号(36)")
    @TableField("TMS_NO")
    private String tmsNo;

    /**
    * 结算线路唯一编号
    */
    @ApiModelProperty("结算线路唯一编号(36)")
    @TableId("LINE_NO")
    private String lineNo;

    /**
    * 结算线路序号
    */
    @ApiModelProperty("结算线路序号(22)")
    @TableField("LINE_SEQ")
    private Integer lineSeq;

    /**
    * 是否为合并线路
    */
    @ApiModelProperty("是否为合并线路(1)")
    @TableField("IS_MERGE")
    private String isMerge;

    /**
    * 结算起始地-国家
    */
    @ApiModelProperty("结算起始地-国家(20)")
    @TableField("NATIONALID_COUNTY")
    private String nationalidCounty;

    /**
    * 结算起始地-省
    */
    @ApiModelProperty("结算起始地-省(20)")
    @TableField("NATIONALID_PROVINCE")
    private String nationalidProvince;

    /**
    * 结算起始地-市
    */
    @ApiModelProperty("结算起始地-市(20)")
    @TableField("NATIONALID_CITY")
    private String nationalidCity;

    /**
    * 结算起始地-区县
    */
    @ApiModelProperty("结算起始地-区县(20)")
    @TableField("NATIONALID_REGION")
    private String nationalidRegion;

    /**
    * 结算起始地-乡镇/港口/站点
    */
    @ApiModelProperty("结算起始地-乡镇/港口/站点(20)")
    @TableField("NATIONALID_TOWN")
    private String nationalidTown;

    /**
    * 结算目的地-国家
    */
    @ApiModelProperty("结算目的地-国家(20)")
    @TableField("TERMINUSID_COUNTY")
    private String terminusidCounty;

    /**
    * 结算目的地-省
    */
    @ApiModelProperty("结算目的地-省(20)")
    @TableField("TERMINUSID_PROVINCE")
    private String terminusidProvince;

    /**
    * 结算目的地-市
    */
    @ApiModelProperty("结算目的地-市(20)")
    @TableField("TERMINUSID_CITY")
    private String terminusidCity;

    /**
    * 结算目的地-区县
    */
    @ApiModelProperty("结算目的地-区县(20)")
    @TableField("TERMINUSID_REGION")
    private String terminusidRegion;

    /**
    * 结算目的地-乡镇/港口/站点
    */
    @ApiModelProperty("结算目的地-乡镇/港口/站点(20)")
    @TableField("TERMINUSID_TOWN")
    private String terminusidTown;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 最后修改日期
    */
    @ApiModelProperty("最后修改日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("最后修改日期开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("最后修改日期结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(200)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 结算起始地类型
    */
    @ApiModelProperty("结算起始地类型(20)")
    @TableField("NATIONALID_TOWN_TYPE")
    private String nationalidTownType;

    /**
    * 结算目的地类型
    */
    @ApiModelProperty("结算目的地类型(20)")
    @TableField("TERMINUSID_TOWN_TYPE")
    private String terminusidTownType;

    /**
    * 是否需要贸易代理
    */
    @ApiModelProperty("是否需要贸易代理(1)")
    @TableField("IS_MYDL")
    private String isMydl;

    /**
    * 是否需要证件代办
    */
    @ApiModelProperty("是否需要证件代办(1)")
    @TableField("IS_ZJDB")
    private String isZjdb;

    /**
    * 是否需要保险服务
    */
    @ApiModelProperty("是否需要保险服务(1)")
    @TableField("IS_BXFW")
    private String isBxfw;

    /**
    * 跨境班车线路
    */
    @ApiModelProperty("跨境班车线路(50)")
    @TableField("CROSS_LINE")
    private String crossLine;

    /**
    * 项目
    */
    @ApiModelProperty("项目(50)")
    @TableField("CROSS_ITEM")
    private String crossItem;

    /**
    * 去回程
    */
    @ApiModelProperty("去回程(2)")
    @TableField("QHC")
    private String qhc;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public OmsOrderFwxmTmsXlEntity() {
        this.setSubClazz(OmsOrderFwxmTmsXlEntity.class);
    }

    public OmsOrderFwxmTmsXlEntity setPreNo(String preNo) {
        this.preNo = preNo;
        this.nodifySetFiled("preNo", preNo);
        return this;
    }

    public String getPreNo() {
        this.nodifyGetFiled("preNo");
        return preNo;
    }

    public OmsOrderFwxmTmsXlEntity setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        this.nodifySetFiled("orderNo", orderNo);
        return this;
    }

    public String getOrderNo() {
        this.nodifyGetFiled("orderNo");
        return orderNo;
    }

    public OmsOrderFwxmTmsXlEntity setTmsNo(String tmsNo) {
        this.tmsNo = tmsNo;
        this.nodifySetFiled("tmsNo", tmsNo);
        return this;
    }

    public String getTmsNo() {
        this.nodifyGetFiled("tmsNo");
        return tmsNo;
    }

    public OmsOrderFwxmTmsXlEntity setLineNo(String lineNo) {
        this.lineNo = lineNo;
        this.nodifySetFiled("lineNo", lineNo);
        return this;
    }

    public String getLineNo() {
        this.nodifyGetFiled("lineNo");
        return lineNo;
    }

    public OmsOrderFwxmTmsXlEntity setLineSeq(Integer lineSeq) {
        this.lineSeq = lineSeq;
        this.nodifySetFiled("lineSeq", lineSeq);
        return this;
    }

    public Integer getLineSeq() {
        this.nodifyGetFiled("lineSeq");
        return lineSeq;
    }

    public OmsOrderFwxmTmsXlEntity setIsMerge(String isMerge) {
        this.isMerge = isMerge;
        this.nodifySetFiled("isMerge", isMerge);
        return this;
    }

    public String getIsMerge() {
        this.nodifyGetFiled("isMerge");
        return isMerge;
    }

    public OmsOrderFwxmTmsXlEntity setNationalidCounty(String nationalidCounty) {
        this.nationalidCounty = nationalidCounty;
        this.nodifySetFiled("nationalidCounty", nationalidCounty);
        return this;
    }

    public String getNationalidCounty() {
        this.nodifyGetFiled("nationalidCounty");
        return nationalidCounty;
    }

    public OmsOrderFwxmTmsXlEntity setNationalidProvince(String nationalidProvince) {
        this.nationalidProvince = nationalidProvince;
        this.nodifySetFiled("nationalidProvince", nationalidProvince);
        return this;
    }

    public String getNationalidProvince() {
        this.nodifyGetFiled("nationalidProvince");
        return nationalidProvince;
    }

    public OmsOrderFwxmTmsXlEntity setNationalidCity(String nationalidCity) {
        this.nationalidCity = nationalidCity;
        this.nodifySetFiled("nationalidCity", nationalidCity);
        return this;
    }

    public String getNationalidCity() {
        this.nodifyGetFiled("nationalidCity");
        return nationalidCity;
    }

    public OmsOrderFwxmTmsXlEntity setNationalidRegion(String nationalidRegion) {
        this.nationalidRegion = nationalidRegion;
        this.nodifySetFiled("nationalidRegion", nationalidRegion);
        return this;
    }

    public String getNationalidRegion() {
        this.nodifyGetFiled("nationalidRegion");
        return nationalidRegion;
    }

    public OmsOrderFwxmTmsXlEntity setNationalidTown(String nationalidTown) {
        this.nationalidTown = nationalidTown;
        this.nodifySetFiled("nationalidTown", nationalidTown);
        return this;
    }

    public String getNationalidTown() {
        this.nodifyGetFiled("nationalidTown");
        return nationalidTown;
    }

    public OmsOrderFwxmTmsXlEntity setTerminusidCounty(String terminusidCounty) {
        this.terminusidCounty = terminusidCounty;
        this.nodifySetFiled("terminusidCounty", terminusidCounty);
        return this;
    }

    public String getTerminusidCounty() {
        this.nodifyGetFiled("terminusidCounty");
        return terminusidCounty;
    }

    public OmsOrderFwxmTmsXlEntity setTerminusidProvince(String terminusidProvince) {
        this.terminusidProvince = terminusidProvince;
        this.nodifySetFiled("terminusidProvince", terminusidProvince);
        return this;
    }

    public String getTerminusidProvince() {
        this.nodifyGetFiled("terminusidProvince");
        return terminusidProvince;
    }

    public OmsOrderFwxmTmsXlEntity setTerminusidCity(String terminusidCity) {
        this.terminusidCity = terminusidCity;
        this.nodifySetFiled("terminusidCity", terminusidCity);
        return this;
    }

    public String getTerminusidCity() {
        this.nodifyGetFiled("terminusidCity");
        return terminusidCity;
    }

    public OmsOrderFwxmTmsXlEntity setTerminusidRegion(String terminusidRegion) {
        this.terminusidRegion = terminusidRegion;
        this.nodifySetFiled("terminusidRegion", terminusidRegion);
        return this;
    }

    public String getTerminusidRegion() {
        this.nodifyGetFiled("terminusidRegion");
        return terminusidRegion;
    }

    public OmsOrderFwxmTmsXlEntity setTerminusidTown(String terminusidTown) {
        this.terminusidTown = terminusidTown;
        this.nodifySetFiled("terminusidTown", terminusidTown);
        return this;
    }

    public String getTerminusidTown() {
        this.nodifyGetFiled("terminusidTown");
        return terminusidTown;
    }

    public OmsOrderFwxmTmsXlEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public OmsOrderFwxmTmsXlEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public OmsOrderFwxmTmsXlEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public OmsOrderFwxmTmsXlEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public OmsOrderFwxmTmsXlEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public OmsOrderFwxmTmsXlEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public OmsOrderFwxmTmsXlEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public OmsOrderFwxmTmsXlEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public OmsOrderFwxmTmsXlEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public OmsOrderFwxmTmsXlEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public OmsOrderFwxmTmsXlEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public OmsOrderFwxmTmsXlEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public OmsOrderFwxmTmsXlEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public OmsOrderFwxmTmsXlEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public OmsOrderFwxmTmsXlEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public OmsOrderFwxmTmsXlEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public OmsOrderFwxmTmsXlEntity setNationalidTownType(String nationalidTownType) {
        this.nationalidTownType = nationalidTownType;
        this.nodifySetFiled("nationalidTownType", nationalidTownType);
        return this;
    }

    public String getNationalidTownType() {
        this.nodifyGetFiled("nationalidTownType");
        return nationalidTownType;
    }

    public OmsOrderFwxmTmsXlEntity setTerminusidTownType(String terminusidTownType) {
        this.terminusidTownType = terminusidTownType;
        this.nodifySetFiled("terminusidTownType", terminusidTownType);
        return this;
    }

    public String getTerminusidTownType() {
        this.nodifyGetFiled("terminusidTownType");
        return terminusidTownType;
    }

    public OmsOrderFwxmTmsXlEntity setIsMydl(String isMydl) {
        this.isMydl = isMydl;
        this.nodifySetFiled("isMydl", isMydl);
        return this;
    }

    public String getIsMydl() {
        this.nodifyGetFiled("isMydl");
        return isMydl;
    }

    public OmsOrderFwxmTmsXlEntity setIsZjdb(String isZjdb) {
        this.isZjdb = isZjdb;
        this.nodifySetFiled("isZjdb", isZjdb);
        return this;
    }

    public String getIsZjdb() {
        this.nodifyGetFiled("isZjdb");
        return isZjdb;
    }

    public OmsOrderFwxmTmsXlEntity setIsBxfw(String isBxfw) {
        this.isBxfw = isBxfw;
        this.nodifySetFiled("isBxfw", isBxfw);
        return this;
    }

    public String getIsBxfw() {
        this.nodifyGetFiled("isBxfw");
        return isBxfw;
    }

    public OmsOrderFwxmTmsXlEntity setCrossLine(String crossLine) {
        this.crossLine = crossLine;
        this.nodifySetFiled("crossLine", crossLine);
        return this;
    }

    public String getCrossLine() {
        this.nodifyGetFiled("crossLine");
        return crossLine;
    }

    public OmsOrderFwxmTmsXlEntity setCrossItem(String crossItem) {
        this.crossItem = crossItem;
        this.nodifySetFiled("crossItem", crossItem);
        return this;
    }

    public String getCrossItem() {
        this.nodifyGetFiled("crossItem");
        return crossItem;
    }

    public OmsOrderFwxmTmsXlEntity setQhc(String qhc) {
        this.qhc = qhc;
        this.nodifySetFiled("qhc", qhc);
        return this;
    }

    public String getQhc() {
        this.nodifyGetFiled("qhc");
        return qhc;
    }

}
