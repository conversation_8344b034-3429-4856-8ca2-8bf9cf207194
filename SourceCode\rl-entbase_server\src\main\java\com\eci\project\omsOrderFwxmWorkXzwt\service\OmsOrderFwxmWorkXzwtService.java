package com.eci.project.omsOrderFwxmWorkXzwt.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.omsOrderFwxmWorkXzwt.dao.OmsOrderFwxmWorkXzwtDao;
import com.eci.project.omsOrderFwxmWorkXzwt.entity.OmsOrderFwxmWorkXzwtEntity;
import com.eci.project.omsOrderFwxmWorkXzwt.validate.OmsOrderFwxmWorkXzwtVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 协作委托表Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-14
*/
@Service
@Slf4j
public class OmsOrderFwxmWorkXzwtService implements EciBaseService<OmsOrderFwxmWorkXzwtEntity> {

    @Autowired
    private OmsOrderFwxmWorkXzwtDao omsOrderFwxmWorkXzwtDao;

    @Autowired
    private OmsOrderFwxmWorkXzwtVal omsOrderFwxmWorkXzwtVal;


    @Override
    public TgPageInfo queryPageList(OmsOrderFwxmWorkXzwtEntity entity) {
        EciQuery<OmsOrderFwxmWorkXzwtEntity> eciQuery = EciQuery.buildQuery(entity);
        List<OmsOrderFwxmWorkXzwtEntity> entities = omsOrderFwxmWorkXzwtDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderFwxmWorkXzwtEntity save(OmsOrderFwxmWorkXzwtEntity entity) {
        // 返回实体对象
        OmsOrderFwxmWorkXzwtEntity omsOrderFwxmWorkXzwtEntity = null;
        omsOrderFwxmWorkXzwtVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            omsOrderFwxmWorkXzwtEntity = omsOrderFwxmWorkXzwtDao.insertOne(entity);

        }else{

            omsOrderFwxmWorkXzwtEntity = omsOrderFwxmWorkXzwtDao.updateByEntityId(entity);

        }
        return omsOrderFwxmWorkXzwtEntity;
    }

    @Override
    public List<OmsOrderFwxmWorkXzwtEntity> selectList(OmsOrderFwxmWorkXzwtEntity entity) {
        return omsOrderFwxmWorkXzwtDao.selectList(entity);
    }

    @Override
    public OmsOrderFwxmWorkXzwtEntity selectOneById(Serializable id) {
        return omsOrderFwxmWorkXzwtDao.selectById(id);
    }


    @Override
    public void insertBatch(List<OmsOrderFwxmWorkXzwtEntity> list) {
        omsOrderFwxmWorkXzwtDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return omsOrderFwxmWorkXzwtDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return omsOrderFwxmWorkXzwtDao.deleteById(id);
    }

}