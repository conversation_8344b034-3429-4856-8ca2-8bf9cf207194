package com.eci.project.omsOrderFwxmWorkFkZzfw.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.omsOrderFwxmWorkFkZzfw.entity.OmsOrderFwxmWorkFkZzfwEntity;

import org.springframework.stereotype.Service;


/**
* 反馈内容-作业信息:其他增值服务信息Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-05-22
*/
@Service
public class OmsOrderFwxmWorkFkZzfwVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(OmsOrderFwxmWorkFkZzfwEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(OmsOrderFwxmWorkFkZzfwEntity entity, BusinessType businessType) {

    }

}
