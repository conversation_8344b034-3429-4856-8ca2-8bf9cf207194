package com.eci.common;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: DataExtend
 * @Author: guangyan.mei
 * @Date: 2025/4/23 15:56
 * @Description: TODO
 */
public class DataExtend {

    //
    // 摘要:
    // 获取字符串的代码部分
    //
    // 参数:
    //   key:
    public static String toCode(String key) {
        if (key == null || key.isEmpty()) {
            return "";
        }

        // 使用 split("\\|") 分隔字符串
        String[] parts = key.split("\\|");

        // 取第一个元素（默认索引 0）
        if (parts.length > 0) {
            return parts[0].trim();
        } else {
            return "";
        }
    }

    //
    // 摘要:
    //     转换成('A','B') 注意包括括号
    //
    // 参数:
    //   key:
    public static String toInCondition(String key) {
        StringBuilder text = new StringBuilder();
        String[] array = key.split(",");

        for (String str : array) {
            // 去掉可能的空格并添加到结果中
            text.append(",'").append(str.trim()).append("'");
        }

        // 拼接最终结果并返回
        return "(" + text.substring(1) + ")";
    }

    /**
     * 将传入的是1,2,3 这种格式需要解串生成List
     */
    public static List<String> convertList(String key) {
        List<String> typeList = new ArrayList<>();
        if (key != null) {
            String[] typeStr = key.split(",");
            for (int i = 0; i < typeStr.length; i++) {
                typeList.add(typeStr[i]);
            }
        }

        return typeList;
    }

    //
    // 摘要:
    //     列表数据转化成 A,B,C格式
    //
    // 参数:
    //   list:
    public static String toOneString(List<String> list) {
        if (list == null || list.isEmpty()) {
            return "";
        }
        return String.join(",", list);
    }

    //
    // 摘要:
    //     字符转成数值，如果是空则转成0
    //
    // 参数:
    //   key:
    public static double toDoubleNullOrEmptyToZero(String key) {
        return 0.0;
    }

    //
    // 摘要:
    //     判断是否是True Y、1 、true
    //
    // 参数:
    //   key:
    public static boolean toBool(String key) {
        int result;
        switch (key) {
            default:
                result = ((key == "True") ? 1 : 0);
                break;
            case "y":
            case "Y":
            case "1":
            case "true":
                result = 1;
                break;
        }

        return (byte) result != 0;
    }
}
