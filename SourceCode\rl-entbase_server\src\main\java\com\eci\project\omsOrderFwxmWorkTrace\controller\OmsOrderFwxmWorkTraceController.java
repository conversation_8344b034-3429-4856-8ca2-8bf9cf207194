package com.eci.project.omsOrderFwxmWorkTrace.controller;

import com.eci.common.ZsrJson;
import com.eci.common.util.ResponseMsg;
import com.eci.common.util.ResponseMsgUtil;
import com.eci.crud.controller.EciBaseController;
import com.eci.log.annotation.EciLog;
import com.eci.log.enums.BusinessType;
import com.eci.project.omsOrderFwxmWorkTrace.entity.OmsOrderFwxmWorkTraceEntity;
import com.eci.project.omsOrderFwxmWorkTrace.service.OmsOrderFwxmWorkTraceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 作业跟踪Controller
 *
 * @<NAME_EMAIL>
 * @date 2025-04-22
 */
@Api(tags = "作业跟踪")
@RestController
@RequestMapping("/omsOrderFwxmWorkTrace")
public class OmsOrderFwxmWorkTraceController extends EciBaseController {

    @Autowired
    private OmsOrderFwxmWorkTraceService omsOrderFwxmWorkTraceService;


    @ApiOperation("作业跟踪:保存")
    @EciLog(title = "作业跟踪:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    //@EciAction() //框架core,sso,component 需升级到2.2.5
    public ResponseMsg save(@RequestBody OmsOrderFwxmWorkTraceEntity entity) {
        return ResponseMsgUtil.success(10001, omsOrderFwxmWorkTraceService.save(entity));
    }


    @ApiOperation("作业跟踪:查询列表")
    @EciLog(title = "作业跟踪:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    //@EciAction() //框架core,sso,component 需升级到2.2.5
    public ResponseMsg selectList(@RequestBody OmsOrderFwxmWorkTraceEntity entity) {
        return ResponseMsgUtil.success(10001, omsOrderFwxmWorkTraceService.selectList(entity));
    }


    @ApiOperation("作业跟踪:分页查询列表")
    @EciLog(title = "作业跟踪:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    //@EciAction() //框架core,sso,component 需升级到2.2.5
    public ResponseMsg selectPageList(@RequestBody OmsOrderFwxmWorkTraceEntity entity) {
        return ResponseMsgUtil.success(10001, omsOrderFwxmWorkTraceService.queryPageList(entity));
    }


    @ApiOperation("作业跟踪:根据ID查一条")
    @EciLog(title = "作业跟踪:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    //@EciAction() //框架core,sso,component 需升级到2.2.5
    public ResponseMsg selectOneById(@RequestBody OmsOrderFwxmWorkTraceEntity entity) {
        return ResponseMsgUtil.success(10001, omsOrderFwxmWorkTraceService.selectOneById(entity.getGuid()));
    }


    @ApiOperation("作业跟踪:根据ID删除一条")
    @EciLog(title = "作业跟踪:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    //@EciAction() //框架core,sso,component 需升级到2.2.5
    public ResponseMsg deleteById(@RequestBody OmsOrderFwxmWorkTraceEntity entity) {
        return ResponseMsgUtil.success(10001, omsOrderFwxmWorkTraceService.deleteById(entity.getGuid()));
    }


    @ApiOperation("作业跟踪:根据ID字符串删除多条")
    @EciLog(title = "作业跟踪:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    //@EciAction() //框架core,sso,component 需升级到2.2.5
    public ResponseMsg deleteByIds(@RequestBody OmsOrderFwxmWorkTraceEntity entity) {
        return ResponseMsgUtil.success(10001, omsOrderFwxmWorkTraceService.deleteByIds(entity.getIds()));
    }

    @ApiOperation("作业跟踪:批量保存实际完成时间")
    @EciLog(title = "作业跟踪:批量保存实际完成时间", businessType = BusinessType.OTHER)
    @PostMapping("/plSave")
    //@EciAction() //框架core,sso,component 需升级到2.2.5
    public ResponseMsg plSave(@RequestBody String jsonstr) {
        List<OmsOrderFwxmWorkTraceEntity> omsOrderFwxmWorkTraceEntities = ZsrJson.parse(jsonstr).getList("jsonstr", OmsOrderFwxmWorkTraceEntity.class);
        return ResponseMsgUtil.success(10001, omsOrderFwxmWorkTraceService.plSave(omsOrderFwxmWorkTraceEntities));
    }


}