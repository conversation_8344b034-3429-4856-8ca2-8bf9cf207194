package com.eci.project.omsIBillStatus.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.omsIBillStatus.entity.OmsIBillStatusEntity;

import org.springframework.stereotype.Service;


/**
* 单据状态Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-05-16
*/
@Service
public class OmsIBillStatusVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(OmsIBillStatusEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(OmsIBillStatusEntity entity, BusinessType businessType) {

    }

}
