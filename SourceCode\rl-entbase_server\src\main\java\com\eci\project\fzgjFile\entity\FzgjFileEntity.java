package com.eci.project.fzgjFile.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.math.BigDecimal;
import java.util.Date;


/**
* 附件对象 FZGJ_FILE
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-06-04
*/
@ApiModel("附件")
@TableName("FZGJ_FILE")
@FieldNameConstants
public class FzgjFileEntity extends EciBaseEntity{
    /**
    * 主键
    */
    @ApiModelProperty("主键(36)")
    @TableId("GUID")
    private String guid;

    /**
    * 单据唯一编号
    */
    @ApiModelProperty("单据唯一编号(36)")
    @TableField("GOODS_GUID")
    private String goodsGuid;

    /**
    * 附件说明
    */
    @ApiModelProperty("附件说明(200)")
    @TableField("MEMO")
    private String memo;

    /**
    * 所属附件类型
    */
    @ApiModelProperty("所属附件类型(50)")
    @TableField("FILE_TYPE")
    private String fileType;

    /**
    * 附件地址
    */
    @ApiModelProperty("附件地址(1,000)")
    @TableField("URL")
    private String url;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 修改人
    */
    @ApiModelProperty("修改人(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 修改日期
    */
    @ApiModelProperty("修改日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("修改日期开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("修改日期结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 原始文件名
    */
    @ApiModelProperty("原始文件名(100)")
    @TableField("ORIGIN_FILE_NAME")
    private String originFileName;

    /**
    * 文件编号
    */
    @ApiModelProperty("文件编号(100)")
    @TableField("FILE_NO")
    private String fileNo;

    /**
    * 单据类型
    */
    @ApiModelProperty("单据类型(20)")
    @TableField("DATA_OBJECT")
    private String dataObject;

    /**
    * 作业系统
    */
    @ApiModelProperty("作业系统(10)")
    @TableField("SYS_CODE")
    private String sysCode;

    /**
    * 订单号、业务数据唯一注册编号
    */
    @ApiModelProperty("订单号、业务数据唯一注册编号(50)")
    @TableField("ORDER_NO")
    private String orderNo;

    /**
    * 文件格式
    */
    @ApiModelProperty("文件格式(20)")
    @TableField("FILE_FORMAT")
    private String fileFormat;

    /**
    * 来源(人工上传/扫描系统传入)
    */
    @ApiModelProperty("来源(人工上传/扫描系统传入)(10)")
    @TableField("SOURCE")
    private String source;

    /**
    * 状态
    */
    @ApiModelProperty("状态(20)")
    @TableField("STATUS")
    private String status;

    /**
    * 组织编码（部门）
    */
    @ApiModelProperty("组织编码（部门）(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 组织名称（部门名称）
    */
    @ApiModelProperty("组织名称（部门名称）(100)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 企业编码
    */
    @ApiModelProperty("企业编码(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 企业名称
    */
    @ApiModelProperty("企业名称(100)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 集团编码
    */
    @ApiModelProperty("集团编码(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 集团名称
    */
    @ApiModelProperty("集团名称(100)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 创建人名称
    */
    @ApiModelProperty("创建人名称(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 修改人名称
    */
    @ApiModelProperty("修改人名称(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 经度
    */
    @ApiModelProperty("经度(22)")
    @TableField("LONGITUDE")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal longitude;

    /**
    * 纬度
    */
    @ApiModelProperty("纬度(22)")
    @TableField("LATITUDE")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal latitude;

    /**
    * 小程序 货代/ETMS 现场作业拍摄时间
    */
    @ApiModelProperty("小程序 货代/ETMS 现场作业拍摄时间(7)")
    @TableField("START_TIME")
    private Date startTime;

    @ApiModelProperty("小程序 货代/ETMS 现场作业拍摄时间开始")
    @TableField(exist=false)
    private Date startTimeStart;

    @ApiModelProperty("小程序 货代/ETMS 现场作业拍摄时间结束")
    @TableField(exist=false)
    private Date startTimeEnd;

    /**
    * 小程序 货代/ETMS 现场作业拍摄地址
    */
    @ApiModelProperty("小程序 货代/ETMS 现场作业拍摄地址(100)")
    @TableField("START_ADDRESS")
    private String startAddress;

    /**
    * 小程序 货代/ETMS 现场作业上传时间
    */
    @ApiModelProperty("小程序 货代/ETMS 现场作业上传时间(7)")
    @TableField("SEND_TIME")
    private Date sendTime;

    @ApiModelProperty("小程序 货代/ETMS 现场作业上传时间开始")
    @TableField(exist=false)
    private Date sendTimeStart;

    @ApiModelProperty("小程序 货代/ETMS 现场作业上传时间结束")
    @TableField(exist=false)
    private Date sendTimeEnd;

    /**
    * 小程序 货代/ETMS 现场作业上传地址
    */
    @ApiModelProperty("小程序 货代/ETMS 现场作业上传地址(100)")
    @TableField("SEND_ADDRESS")
    private String sendAddress;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public FzgjFileEntity() {
        this.setSubClazz(FzgjFileEntity.class);
    }

    public FzgjFileEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public FzgjFileEntity setGoodsGuid(String goodsGuid) {
        this.goodsGuid = goodsGuid;
        this.nodifySetFiled("goodsGuid", goodsGuid);
        return this;
    }

    public String getGoodsGuid() {
        this.nodifyGetFiled("goodsGuid");
        return goodsGuid;
    }

    public FzgjFileEntity setMemo(String memo) {
        this.memo = memo;
        this.nodifySetFiled("memo", memo);
        return this;
    }

    public String getMemo() {
        this.nodifyGetFiled("memo");
        return memo;
    }

    public FzgjFileEntity setFileType(String fileType) {
        this.fileType = fileType;
        this.nodifySetFiled("fileType", fileType);
        return this;
    }

    public String getFileType() {
        this.nodifyGetFiled("fileType");
        return fileType;
    }

    public FzgjFileEntity setUrl(String url) {
        this.url = url;
        this.nodifySetFiled("url", url);
        return this;
    }

    public String getUrl() {
        this.nodifyGetFiled("url");
        return url;
    }

    public FzgjFileEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public FzgjFileEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public FzgjFileEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public FzgjFileEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public FzgjFileEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public FzgjFileEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public FzgjFileEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public FzgjFileEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public FzgjFileEntity setOriginFileName(String originFileName) {
        this.originFileName = originFileName;
        this.nodifySetFiled("originFileName", originFileName);
        return this;
    }

    public String getOriginFileName() {
        this.nodifyGetFiled("originFileName");
        return originFileName;
    }

    public FzgjFileEntity setFileNo(String fileNo) {
        this.fileNo = fileNo;
        this.nodifySetFiled("fileNo", fileNo);
        return this;
    }

    public String getFileNo() {
        this.nodifyGetFiled("fileNo");
        return fileNo;
    }

    public FzgjFileEntity setDataObject(String dataObject) {
        this.dataObject = dataObject;
        this.nodifySetFiled("dataObject", dataObject);
        return this;
    }

    public String getDataObject() {
        this.nodifyGetFiled("dataObject");
        return dataObject;
    }

    public FzgjFileEntity setSysCode(String sysCode) {
        this.sysCode = sysCode;
        this.nodifySetFiled("sysCode", sysCode);
        return this;
    }

    public String getSysCode() {
        this.nodifyGetFiled("sysCode");
        return sysCode;
    }

    public FzgjFileEntity setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        this.nodifySetFiled("orderNo", orderNo);
        return this;
    }

    public String getOrderNo() {
        this.nodifyGetFiled("orderNo");
        return orderNo;
    }

    public FzgjFileEntity setFileFormat(String fileFormat) {
        this.fileFormat = fileFormat;
        this.nodifySetFiled("fileFormat", fileFormat);
        return this;
    }

    public String getFileFormat() {
        this.nodifyGetFiled("fileFormat");
        return fileFormat;
    }

    public FzgjFileEntity setSource(String source) {
        this.source = source;
        this.nodifySetFiled("source", source);
        return this;
    }

    public String getSource() {
        this.nodifyGetFiled("source");
        return source;
    }

    public FzgjFileEntity setStatus(String status) {
        this.status = status;
        this.nodifySetFiled("status", status);
        return this;
    }

    public String getStatus() {
        this.nodifyGetFiled("status");
        return status;
    }

    public FzgjFileEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public FzgjFileEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public FzgjFileEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public FzgjFileEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public FzgjFileEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public FzgjFileEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public FzgjFileEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public FzgjFileEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public FzgjFileEntity setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
        this.nodifySetFiled("longitude", longitude);
        return this;
    }

    public BigDecimal getLongitude() {
        this.nodifyGetFiled("longitude");
        return longitude;
    }

    public FzgjFileEntity setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
        this.nodifySetFiled("latitude", latitude);
        return this;
    }

    public BigDecimal getLatitude() {
        this.nodifyGetFiled("latitude");
        return latitude;
    }

    public FzgjFileEntity setStartTime(Date startTime) {
        this.startTime = startTime;
        this.nodifySetFiled("startTime", startTime);
        return this;
    }

    public Date getStartTime() {
        this.nodifyGetFiled("startTime");
        return startTime;
    }

    public FzgjFileEntity setStartTimeStart(Date startTimeStart) {
        this.startTimeStart = startTimeStart;
        this.nodifySetFiled("startTimeStart", startTimeStart);
        return this;
    }

    public Date getStartTimeStart() {
        this.nodifyGetFiled("startTimeStart");
        return startTimeStart;
    }

    public FzgjFileEntity setStartTimeEnd(Date startTimeEnd) {
        this.startTimeEnd = startTimeEnd;
        this.nodifySetFiled("startTimeEnd", startTimeEnd);
        return this;
    }

    public Date getStartTimeEnd() {
        this.nodifyGetFiled("startTimeEnd");
        return startTimeEnd;
    }
    public FzgjFileEntity setStartAddress(String startAddress) {
        this.startAddress = startAddress;
        this.nodifySetFiled("startAddress", startAddress);
        return this;
    }

    public String getStartAddress() {
        this.nodifyGetFiled("startAddress");
        return startAddress;
    }

    public FzgjFileEntity setSendTime(Date sendTime) {
        this.sendTime = sendTime;
        this.nodifySetFiled("sendTime", sendTime);
        return this;
    }

    public Date getSendTime() {
        this.nodifyGetFiled("sendTime");
        return sendTime;
    }

    public FzgjFileEntity setSendTimeStart(Date sendTimeStart) {
        this.sendTimeStart = sendTimeStart;
        this.nodifySetFiled("sendTimeStart", sendTimeStart);
        return this;
    }

    public Date getSendTimeStart() {
        this.nodifyGetFiled("sendTimeStart");
        return sendTimeStart;
    }

    public FzgjFileEntity setSendTimeEnd(Date sendTimeEnd) {
        this.sendTimeEnd = sendTimeEnd;
        this.nodifySetFiled("sendTimeEnd", sendTimeEnd);
        return this;
    }

    public Date getSendTimeEnd() {
        this.nodifyGetFiled("sendTimeEnd");
        return sendTimeEnd;
    }
    public FzgjFileEntity setSendAddress(String sendAddress) {
        this.sendAddress = sendAddress;
        this.nodifySetFiled("sendAddress", sendAddress);
        return this;
    }

    public String getSendAddress() {
        this.nodifyGetFiled("sendAddress");
        return sendAddress;
    }

}
