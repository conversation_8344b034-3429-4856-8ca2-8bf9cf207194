package com.eci.project.etmsBdQuasiCarType.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.etmsBdQuasiCarType.entity.EtmsBdQuasiCarTypeEntity;

import org.springframework.stereotype.Service;


/**
* 准驾车型Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-16
*/
@Service
public class EtmsBdQuasiCarTypeVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(EtmsBdQuasiCarTypeEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(EtmsBdQuasiCarTypeEntity entity, BusinessType businessType) {

    }

}
