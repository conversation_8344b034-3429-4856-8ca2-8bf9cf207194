package com.eci.project.fzgjTaskLimitationPt.service;

import com.eci.crud.service.EciBaseService;
import com.eci.project.fzgjTaskLimitationPt.entity.FzgjTaskLimitationPtEntity;
import com.eci.wu.core.EntityBase;

import java.util.List;
import java.util.Map;


/**
* 平台级作业环节及参考时效Service接口
* 业务逻辑层, 接口代码, 只需要写自定义的部分, 增删改查部分在父级接口已经实现
* @<NAME_EMAIL>
* @date 2025-03-28
*/
public interface IFzgjTaskLimitationPtService extends EciBaseService<FzgjTaskLimitationPtEntity> {

        List<Map<String, Object>> getTaskChecked(String targetCode);
}
