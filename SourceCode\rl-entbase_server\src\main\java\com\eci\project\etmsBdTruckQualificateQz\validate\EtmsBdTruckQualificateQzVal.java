package com.eci.project.etmsBdTruckQualificateQz.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.etmsBdTruckQualificateQz.entity.EtmsBdTruckQualificateQzEntity;

import org.springframework.stereotype.Service;


/**
* 资质管理Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-25
*/
@Service
public class EtmsBdTruckQualificateQzVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(EtmsBdTruckQualificateQzEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(EtmsBdTruckQualificateQzEntity entity, BusinessType businessType) {

    }

}
