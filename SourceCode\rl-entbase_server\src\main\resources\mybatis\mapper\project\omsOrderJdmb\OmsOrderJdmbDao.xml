<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.omsOrderJdmb.dao.OmsOrderJdmbDao">
    <resultMap type="OmsOrderJdmbEntity" id="OmsOrderJdmbResult">
        <result property="orderNo" column="ORDER_NO"/>
        <result property="jdmbNo" column="JDMB_NO"/>
        <result property="consigneeCode" column="CONSIGNEE_CODE"/>
        <result property="shipper" column="SHIPPER"/>
        <result property="receiver" column="RECEIVER"/>
        <result property="opType" column="OP_TYPE"/>
        <result property="mbType" column="MB_TYPE"/>
        <result property="isUse" column="IS_USE"/>
        <result property="productCode" column="PRODUCT_CODE"/>
        <result property="fwlxCode" column="FWLX_CODE"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="jdNodeCode" column="JD_NODE_CODE"/>
        <result property="jdNodeName" column="JD_NODE_NAME"/>
        <result property="mbmc" column="MBMC"/>
    </resultMap>

    <sql id="selectOmsOrderJdmbEntityVo">
        select
            ORDER_NO,
            JDMB_NO,
            CONSIGNEE_CODE,
            SHIPPER,
            RECEIVER,
            OP_TYPE,
            MB_TYPE,
            IS_USE,
            PRODUCT_CODE,
            FWLX_CODE,
            CREATE_USER,
            CREATE_USER_NAME,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_USER_NAME,
            UPDATE_DATE,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            JD_NODE_CODE,
            JD_NODE_NAME,
            MBMC
        from OMS_ORDER_JDMB
    </sql>
</mapper>