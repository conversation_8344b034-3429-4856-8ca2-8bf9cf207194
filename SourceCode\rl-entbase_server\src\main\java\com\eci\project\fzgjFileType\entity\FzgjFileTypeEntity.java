package com.eci.project.fzgjFileType.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.common.DictField;
import com.eci.common.ZsrBaseEntity;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 附件类型对象 FZGJ_FILE_TYPE
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-04-11
*/
@ApiModel("附件类型")
@TableName("FZGJ_FILE_TYPE")
@FieldNameConstants
public class FzgjFileTypeEntity extends ZsrBaseEntity {
    /**
    * GUID
    */
    @ApiModelProperty("GUID(100)")
    @TableId("GUID")
    private String guid;

    /**
    * 代码
    */
    @ApiModelProperty("代码(50)")
    @TableField("CODE")
    private String code;

    /**
    * 名称
    */
    @ApiModelProperty("名称(100)")
    @TableField("NAME")
    private String name;

    /**
    * 备注
    */
    @ApiModelProperty("备注(100)")
    @TableField("MEMO")
    private String memo;

    /**
    * 状态
    */
    @ApiModelProperty("状态(1)")
    @TableField("STATUS")
    @DictField(queryKey = "YNKey")
    private String status;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(100)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 修改人
    */
    @ApiModelProperty("修改人(100)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 修改日期
    */
    @ApiModelProperty("修改日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("修改日期开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("修改日期结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 拼音简称
    */
    @ApiModelProperty("拼音简称(100)")
    @TableField("SHORT_NAME")
    private String shortName;

    /**
    * 顺序
    */
    @ApiModelProperty("顺序(22)")
    @TableField("SEQ")
    private Integer seq;

    /**
    * 组织编码（部门）
    */
    @ApiModelProperty("组织编码（部门）(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 组织名称（部门名称）
    */
    @ApiModelProperty("组织名称（部门名称）(200)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 企业编码
    */
    @ApiModelProperty("企业编码(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 企业名称
    */
    @ApiModelProperty("企业名称(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 集团编码
    */
    @ApiModelProperty("集团编码(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 集团名称
    */
    @ApiModelProperty("集团名称(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 创建人名称
    */
    @ApiModelProperty("创建人名称(200)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 修改人名称
    */
    @ApiModelProperty("修改人名称(200)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 查看限制 
    */
    @ApiModelProperty("查看限制 (20)")
    @TableField("VIEW_LIMIT")
    @DictField(queryKey = "YNKey")
    private String viewLimit;

    /**
    * 上传限制
    */
    @ApiModelProperty("上传限制(20)")
    @TableField("UPLOAD_LIMIT")
    @DictField(queryKey = "YNKey")
    private String uploadLimit;

    /**
    * 文档系统代码
    */
    @ApiModelProperty("文档系统代码(20)")
    @TableField("CTC_SYS_CODE")
    private String ctcSysCode;

    /**
    * 海关系统代码
    */
    @ApiModelProperty("海关系统代码(20)")
    @TableField("HG_SYS_CODE")
    private String hgSysCode;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public FzgjFileTypeEntity() {
        this.setSubClazz(FzgjFileTypeEntity.class);
    }

    public FzgjFileTypeEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public FzgjFileTypeEntity setCode(String code) {
        this.code = code;
        this.nodifySetFiled("code", code);
        return this;
    }

    public String getCode() {
        this.nodifyGetFiled("code");
        return code;
    }

    public FzgjFileTypeEntity setName(String name) {
        this.name = name;
        this.nodifySetFiled("name", name);
        return this;
    }

    public String getName() {
        this.nodifyGetFiled("name");
        return name;
    }

    public FzgjFileTypeEntity setMemo(String memo) {
        this.memo = memo;
        this.nodifySetFiled("memo", memo);
        return this;
    }

    public String getMemo() {
        this.nodifyGetFiled("memo");
        return memo;
    }

    public FzgjFileTypeEntity setStatus(String status) {
        this.status = status;
        this.nodifySetFiled("status", status);
        return this;
    }

    public String getStatus() {
        this.nodifyGetFiled("status");
        return status;
    }

    public FzgjFileTypeEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public FzgjFileTypeEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public FzgjFileTypeEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public FzgjFileTypeEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public FzgjFileTypeEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public FzgjFileTypeEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public FzgjFileTypeEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public FzgjFileTypeEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public FzgjFileTypeEntity setShortName(String shortName) {
        this.shortName = shortName;
        this.nodifySetFiled("shortName", shortName);
        return this;
    }

    public String getShortName() {
        this.nodifyGetFiled("shortName");
        return shortName;
    }

    public FzgjFileTypeEntity setSeq(Integer seq) {
        this.seq = seq;
        this.nodifySetFiled("seq", seq);
        return this;
    }

    public Integer getSeq() {
        this.nodifyGetFiled("seq");
        return seq;
    }

    public FzgjFileTypeEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public FzgjFileTypeEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public FzgjFileTypeEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public FzgjFileTypeEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public FzgjFileTypeEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public FzgjFileTypeEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public FzgjFileTypeEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public FzgjFileTypeEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public FzgjFileTypeEntity setViewLimit(String viewLimit) {
        this.viewLimit = viewLimit;
        this.nodifySetFiled("viewLimit", viewLimit);
        return this;
    }

    public String getViewLimit() {
        this.nodifyGetFiled("viewLimit");
        return viewLimit;
    }

    public FzgjFileTypeEntity setUploadLimit(String uploadLimit) {
        this.uploadLimit = uploadLimit;
        this.nodifySetFiled("uploadLimit", uploadLimit);
        return this;
    }

    public String getUploadLimit() {
        this.nodifyGetFiled("uploadLimit");
        return uploadLimit;
    }

    public FzgjFileTypeEntity setCtcSysCode(String ctcSysCode) {
        this.ctcSysCode = ctcSysCode;
        this.nodifySetFiled("ctcSysCode", ctcSysCode);
        return this;
    }

    public String getCtcSysCode() {
        this.nodifyGetFiled("ctcSysCode");
        return ctcSysCode;
    }

    public FzgjFileTypeEntity setHgSysCode(String hgSysCode) {
        this.hgSysCode = hgSysCode;
        this.nodifySetFiled("hgSysCode", hgSysCode);
        return this;
    }

    public String getHgSysCode() {
        this.nodifyGetFiled("hgSysCode");
        return hgSysCode;
    }

}
