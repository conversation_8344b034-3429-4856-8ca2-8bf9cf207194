package com.eci.project.fzgjBdOpTypeUser.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjBdOpTypeUser.dao.FzgjBdOpTypeUserDao;
import com.eci.project.fzgjBdOpTypeUser.entity.FzgjBdOpTypeUserEntity;
import com.eci.project.fzgjBdOpTypeUser.validate.FzgjBdOpTypeUserVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 用户-业务伙伴授权Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-03-27
*/
@Service
@Slf4j
public class FzgjBdOpTypeUserService implements EciBaseService<FzgjBdOpTypeUserEntity> {

    @Autowired
    private FzgjBdOpTypeUserDao fzgjBdOpTypeUserDao;

    @Autowired
    private FzgjBdOpTypeUserVal fzgjBdOpTypeUserVal;


    @Override
    public TgPageInfo queryPageList(FzgjBdOpTypeUserEntity entity) {
        EciQuery<FzgjBdOpTypeUserEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjBdOpTypeUserEntity> entities = fzgjBdOpTypeUserDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjBdOpTypeUserEntity save(FzgjBdOpTypeUserEntity entity) {
        // 返回实体对象
        FzgjBdOpTypeUserEntity fzgjBdOpTypeUserEntity = null;
        fzgjBdOpTypeUserVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjBdOpTypeUserEntity = fzgjBdOpTypeUserDao.insertOne(entity);

        }else{

            fzgjBdOpTypeUserEntity = fzgjBdOpTypeUserDao.updateByEntityId(entity);

        }
        return fzgjBdOpTypeUserEntity;
    }

    @Override
    public List<FzgjBdOpTypeUserEntity> selectList(FzgjBdOpTypeUserEntity entity) {
        return fzgjBdOpTypeUserDao.selectList(entity);
    }

    @Override
    public FzgjBdOpTypeUserEntity selectOneById(Serializable id) {
        return fzgjBdOpTypeUserDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjBdOpTypeUserEntity> list) {
        fzgjBdOpTypeUserDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjBdOpTypeUserDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjBdOpTypeUserDao.deleteById(id);
    }

}