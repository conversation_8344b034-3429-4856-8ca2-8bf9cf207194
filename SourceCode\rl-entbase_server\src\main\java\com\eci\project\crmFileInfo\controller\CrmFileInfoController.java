package com.eci.project.crmFileInfo.controller;

import com.eci.common.BaseProperties;
import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.crmFileInfo.service.CrmFileInfoService;
import com.eci.project.crmFileInfo.entity.CrmFileInfoEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.List;

/**
* 附件Controller
*
* @<NAME_EMAIL>
* @date 2025-05-21
*/
@Api(tags = "附件")
@RestController
@RequestMapping("/crmFileInfo")
public class CrmFileInfoController extends EciBaseController {

    @Autowired
    private CrmFileInfoService crmFileInfoService;


    @ApiOperation("附件:保存")
    @EciLog(title = "附件:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody CrmFileInfoEntity entity){
        CrmFileInfoEntity crmFileInfoEntity =crmFileInfoService.save(entity);
        return ResponseMsgUtil.success(10001,crmFileInfoEntity);
    }


    @ApiOperation("附件:查询列表")
    @EciLog(title = "附件:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody CrmFileInfoEntity entity){
        List<CrmFileInfoEntity> crmFileInfoEntities = crmFileInfoService.selectList(entity);
        return ResponseMsgUtil.success(10001,crmFileInfoEntities);
    }


    @ApiOperation("附件:分页查询列表")
    @EciLog(title = "附件:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody CrmFileInfoEntity entity){
        TgPageInfo tgPageInfo = crmFileInfoService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("附件:根据ID查一条")
    @EciLog(title = "附件:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody CrmFileInfoEntity entity){
        CrmFileInfoEntity  crmFileInfoEntity = crmFileInfoService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,crmFileInfoEntity);
    }


    @ApiOperation("附件:根据ID删除一条")
    @EciLog(title = "附件:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody CrmFileInfoEntity entity){
        int count = crmFileInfoService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("附件:根据ID字符串删除多条")
    @EciLog(title = "附件:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody CrmFileInfoEntity entity) {
        int count = crmFileInfoService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }
    @ApiOperation("附件:附件下载")
    @EciLog(title = "附件:附件下载", businessType = BusinessType.DELETE)
    @GetMapping("/downLoadAttr")
    @EciAction()
    public void downLoadAttr(String fid) throws Exception {
        CrmFileInfoEntity entity= crmFileInfoService.selectOneById(fid);
        if(entity==null) throw new Exception("文件不存在");
        String basePath = BaseProperties.getFilepath()+entity.getFilePath();
        File file=new File(basePath);
        if(!file.exists())
            throw new Exception("文件不存在");
        try {
            HttpServletResponse response = ServletUtils.getResponse();
            ServletOutputStream outputStream = null;
            outputStream = response.getOutputStream();
            response.setHeader("Content-Disposition", "attachment;filename=" + file.getName());

            FileInputStream inStream = new FileInputStream(file);
            byte[] buf = new byte[4096];
            int readLength;
            while((readLength = inStream.read(buf))!= -1){
                outputStream.write(buf, 0, readLength);
            }
            inStream.close();
            outputStream.flush();
            outputStream.close();
        }catch (Exception ex){

        }
    }
    @ApiOperation("附件:附件下载")
    @EciLog(title = "附件:附件下载", businessType = BusinessType.DELETE)
    @GetMapping("/downLoadAttrs")
    @EciAction()
    public void downLoadAttrs(String fileno) throws Exception {
        // 调用 ZipUtil 的 Compression 方法获取压缩后的字节流
        byte[] zipBytes = crmFileInfoService.Compression(fileno);

        HttpServletResponse response = ServletUtils.getResponse();
        response.setContentType("application/zip");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileno + ".zip");

        try (InputStream inStream = new ByteArrayInputStream(zipBytes);
             ServletOutputStream outputStream = response.getOutputStream()) {

            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            outputStream.flush();
        } catch (IOException e) {
            // 可以记录日志
            throw new IOException("文件下载过程中发生错误", e);
        }
    }

}