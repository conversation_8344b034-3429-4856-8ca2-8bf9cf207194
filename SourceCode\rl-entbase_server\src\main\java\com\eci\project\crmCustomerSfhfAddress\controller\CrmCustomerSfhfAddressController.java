package com.eci.project.crmCustomerSfhfAddress.controller;

import com.eci.common.util.*;
import com.eci.common.web.BllContext;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.crmCustomer.entity.CrmCustomerEntity;
import com.eci.project.crmCustomer.service.CrmCustomerService;
import com.eci.project.crmCustomerSfhfAddress.service.CrmCustomerSfhfAddressService;
import com.eci.project.crmCustomerSfhfAddress.entity.CrmCustomerSfhfAddressEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 业务伙伴收发货方常用地区Controller
*
* @<NAME_EMAIL>
* @date 2025-05-20
*/
@Api(tags = "业务伙伴收发货方常用地区")
@RestController
@RequestMapping("/crmCustomerSfhfAddress")
public class CrmCustomerSfhfAddressController extends EciBaseController {

    @Autowired
    private CrmCustomerSfhfAddressService crmCustomerSfhfAddressService;
    @Autowired
    private CrmCustomerService crmCustomerService;


    @ApiOperation("业务伙伴收发货方常用地区:保存")
    @EciLog(title = "业务伙伴收发货方常用地区:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody CrmCustomerSfhfAddressEntity entity){
        CrmCustomerSfhfAddressEntity crmCustomerSfhfAddressEntity =crmCustomerSfhfAddressService.save(entity);
        return ResponseMsgUtil.success(10001,crmCustomerSfhfAddressEntity);
    }


    @ApiOperation("业务伙伴收发货方常用地区:查询列表")
    @EciLog(title = "业务伙伴收发货方常用地区:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody CrmCustomerSfhfAddressEntity entity){
        List<CrmCustomerSfhfAddressEntity> crmCustomerSfhfAddressEntities = crmCustomerSfhfAddressService.selectList(entity);
        return ResponseMsgUtil.success(10001,crmCustomerSfhfAddressEntities);
    }


    @ApiOperation("业务伙伴收发货方常用地区:分页查询列表")
    @EciLog(title = "业务伙伴收发货方常用地区:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody CrmCustomerSfhfAddressEntity entity){
        TgPageInfo tgPageInfo = crmCustomerSfhfAddressService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("业务伙伴收发货方常用地区:根据ID查一条")
    @EciLog(title = "业务伙伴收发货方常用地区:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody CrmCustomerSfhfAddressEntity entity){
        CrmCustomerSfhfAddressEntity  crmCustomerSfhfAddressEntity = crmCustomerSfhfAddressService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,crmCustomerSfhfAddressEntity);
    }


    @ApiOperation("业务伙伴收发货方常用地区:根据ID删除一条")
    @EciLog(title = "业务伙伴收发货方常用地区:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody CrmCustomerSfhfAddressEntity entity){
        int count = crmCustomerSfhfAddressService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("业务伙伴收发货方常用地区:根据ID字符串删除多条")
    @EciLog(title = "业务伙伴收发货方常用地区:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody CrmCustomerSfhfAddressEntity entity) {
        int count = crmCustomerSfhfAddressService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }

    @ApiOperation("业务伙伴收发货方常用地区:快捷创建地址")
    @EciLog(title = "业务伙伴收发货方常用地区:快捷创建地址", businessType = BusinessType.SELECT)
    @PostMapping("/fastCreate")
    @EciAction()
    public ResponseMsg fastCreate(@RequestBody CrmCustomerSfhfAddressEntity entity) throws Exception {
        CrmCustomerEntity customer= crmCustomerService.selectOneById(entity.getCustomerGuid());
        if(customer==null) throw new Exception("业务伙伴查找失败！");
        entity.setOpAddress(customer.getAddress());
        entity.setOpArea(customer.getDistrict());
        entity.setOpLink(customer.getPerson());
        entity.setOpTel(customer.getTel());
        entity.setCustomerCode(customer.getCode());
        entity.setIsMr("Y");
        entity.setStatus("Y");
        entity= crmCustomerSfhfAddressService.save(entity);
        return ResponseMsgUtil.success(10001,entity);
    }
}