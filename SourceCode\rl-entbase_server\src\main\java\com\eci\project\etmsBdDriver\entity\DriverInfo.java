package com.eci.project.etmsBdDriver.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.codeConvert.EciCode;
import com.eci.common.utils.ExportUtils.Excel;
import com.eci.crud.entity.EciBaseEntity;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

/**
 * <Description>TODO <br>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/15$
 */
@Data
public class DriverInfo extends EciBaseEntity {
    public String guid;                    // 主键
    @Excel(value = "姓名",order = 1)
    public String name;                    // 姓名
    @Excel(value = "司机管理状态",order = 16)
    @EciCode("Driver_Manage_Status")
    public String manageStatus;
    @Excel(value = "手机号",order = 3)
    public String phone;                   // 电话
    @Excel(value = "身份证号",order = 4)
    public String idNumber;                // 身份证号
    @Excel(value = "性别",order = 2)
    public String sex;                     // 性别（男/女）
    public String isGk;                    // 是否挂靠（是/否）
    @Excel(value = "自有人员",order = 9)
    public String isGkName;
    public String isDriver;                // 是否司机（是/否）
    @Excel(value = "司机",order = 6)
    public String isDriverName;                // 是否司机（是/否）
    @Excel(value = "状态",order = 13)
    public String status;                  // 状态（是/否）
    public String isUseApp;                // 是否使用APP（是/否）
    @Excel(value = "使用司机在途",order = 12)
    public String isUseAppName;                // 是否使用APP（是/否）
    public String driverNo;                // 司机编号
    public String quasiCarType;            // 准驾车型
    @Excel(value = "准驾车型",order = 7)
    public String quasiCarTypeName;            // 准驾车型
    public String statusName;              // 状态名称
    @Excel(value = "用户代码",order = 0)
    public String userId;                  // 用户ID
    public String createDate;              // 创建日期
    @Excel(value = "驾驶证换证日期",order = 8)
    public String driverDate;              // 司机日期
    public String partnerGuid;             // 合作伙伴简称
    @Excel(value = "所属业务伙伴",order = 10)
    public String partnerGuidName;             // 合作伙伴简称
    @Excel(value = "默认押运员",order = 11)
    public String supercargo;              // 押运员
    public String createUserName;          // 创建人姓名
    public String createCompanyName;       // 创建公司名称
    @Excel(value = "修改时间",order = 14)
    public String updateDate;              // 更新日期
    @Excel(value = "修改人",order = 15)
    public String updateUserName;          // 更新人姓名
    public String memo;                    // 备注
    public String truckNo;                 // 车牌号
    @Excel(value = "登录账号",order = 12)
    public String loginNo;                 // 登录号
    public String createCompany;           // 创建公司
    @Excel(value = "考评等级",order = 5)
    public String pj;
}
