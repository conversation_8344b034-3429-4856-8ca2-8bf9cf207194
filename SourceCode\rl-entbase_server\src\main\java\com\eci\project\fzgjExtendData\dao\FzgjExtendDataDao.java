package com.eci.project.fzgjExtendData.dao;

import com.eci.crud.dao.EciBaseDao;
import com.eci.project.fzgjExtendData.entity.FzgjExtendDataEntity;
import com.eci.project.fzgjExtendData.entity.FzgjExtendDataPageEntity;

import java.util.List;


/**
 * 扩展基础资料Dao层
 * 接口层, 直接查询数据库使用
 *
 * @<NAME_EMAIL>
 * @date 2025-03-19
 */
public interface FzgjExtendDataDao extends EciBaseDao<FzgjExtendDataEntity> {

    /**
     * 列表查询
     * */
    List<FzgjExtendDataPageEntity> selectListInfo(FzgjExtendDataEntity fzgjExtendDataEntity);

}
