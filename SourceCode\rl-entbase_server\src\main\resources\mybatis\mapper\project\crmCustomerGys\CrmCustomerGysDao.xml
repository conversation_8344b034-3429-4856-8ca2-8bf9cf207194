<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.crmCustomerGys.dao.CrmCustomerGysDao">
    <resultMap type="CrmCustomerGysEntity" id="CrmCustomerGysResult">
        <result property="guid" column="GUID"/>
        <result property="customerCode" column="CUSTOMER_CODE"/>
        <result property="payFreeze" column="PAY_FREEZE"/>
        <result property="payDateline" column="PAY_DATELINE"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="gysType" column="GYS_TYPE"/>
        <result property="gysTypeName" column="GYS_TYPE_NAME"/>
        <result property="beginDate" column="BEGIN_DATE"/>
        <result property="endDate" column="END_DATE"/>
    </resultMap>

    <sql id="selectCrmCustomerGysEntityVo">
        select
            GUID,
            CUSTOMER_CODE,
            PAY_FREEZE,
            PAY_DATELINE,
            CREATE_USER,
            CREATE_USER_NAME,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_USER_NAME,
            UPDATE_DATE,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            GYS_TYPE,
            GYS_TYPE_NAME,
            BEGIN_DATE,
            END_DATE
        from CRM_CUSTOMER_GYS
    </sql>
</mapper>