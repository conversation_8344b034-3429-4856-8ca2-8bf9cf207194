package com.eci.project.crmCustomerHzfwGys.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.crmCustomerHzfwGys.entity.CrmCustomerHzfwGysEntity;
import org.apache.ibatis.annotations.Select;

import java.util.List;


/**
* 供应商合作服务Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-05-13
*/
public interface CrmCustomerHzfwGysDao extends EciBaseDao<CrmCustomerHzfwGysEntity> {
    @Select("select D.BELONG_GROUP beLongGroup,A.CODE FWXM_CODE,A.SYS_CODE fwxmsyscode,A.GUID itemGuid,A.ParentId,case NVL(B.GUID,2) when '2' then 0 else 1 end as Checked,A.Name,B.guid as GUID,C.CODE FWLX_CODE,B.FKZYSJFS,B.SYS_CODE,B.IS_AP" +
            " from FZGJ_BD_SERVICE_ITEM A \n" +
            "INNER JOIN FZGJ_BD_SERVICE_TYPE C on A.Owned_Service=C.GUID " +
            "LEFT JOIN FZGJ_BASE_DATA_DETAIL D on A.SYS_CODE=D.CODE AND D.STATUS='Y'" +
            "left join CRM_CUSTOMER_HZFW_GYS B on A.CODE=B.FWXM_CODE and B.customer_code=#{customerCode} ${ew.customSqlSegment}")
    List<CrmCustomerHzfwGysEntity> selectTreeWhitGYS(@Param(Constants.WRAPPER) Wrapper queryWrapper, @Param("customerCode") String customerCode);

    @Select("select * from (select B.GUID typeGuid,D.Name fwxmName,C.GUID comTypeGuid,C.NAME fwlxName,A.* from CRM_CUSTOMER_HZFW_GYS A \n" +
            "inner join  FZGJ_BD_SERVICE_TYPE  B on A.FWLX_CODE=B.CODE " +
            "inner join FZGJ_BD_SERVICE_ITEM D on D.CODE=A.FWXM_CODE AND D.GROUP_CODE=A.GROUP_CODE " +
            "INNER JOIN FZGJ_BD_SERVICE_TYPE_COM C ON A.FWLX_CODE=C.CODE  and C.GROUP_CODE=A.GROUP_CODE) A ${ew.customSqlSegment}")
    List<CrmCustomerHzfwGysEntity> selectlist(@Param(Constants.WRAPPER) Wrapper queryWrapper);
}