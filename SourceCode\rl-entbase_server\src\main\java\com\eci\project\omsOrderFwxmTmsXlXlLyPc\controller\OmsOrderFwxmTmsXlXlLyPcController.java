package com.eci.project.omsOrderFwxmTmsXlXlLyPc.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.omsOrderFwxmTmsXlXlLyPc.entity.OmsOrderFwxmTmsXlXlLyPcDTOEntity;
import com.eci.project.omsOrderFwxmTmsXlXlLyPc.service.OmsOrderFwxmTmsXlXlLyPcService;
import com.eci.project.omsOrderFwxmTmsXlXlLyPc.entity.OmsOrderFwxmTmsXlXlLyPcEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 * 委托内容-程运序列-陆运-拼车Controller
 *
 * @<NAME_EMAIL>
 * @date 2025-06-05
 */
@Api(tags = "委托内容-程运序列-陆运-拼车")
@RestController
@RequestMapping("/omsOrderFwxmTmsXlXlLyPc")
public class OmsOrderFwxmTmsXlXlLyPcController extends EciBaseController {

    @Autowired
    private OmsOrderFwxmTmsXlXlLyPcService omsOrderFwxmTmsXlXlLyPcService;


    @ApiOperation("委托内容-程运序列-陆运-拼车:保存")
    @EciLog(title = "委托内容-程运序列-陆运-拼车:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody OmsOrderFwxmTmsXlXlLyPcEntity entity){
        OmsOrderFwxmTmsXlXlLyPcEntity omsOrderFwxmTmsXlXlLyPcEntity =omsOrderFwxmTmsXlXlLyPcService.save(entity);
        return ResponseMsgUtil.success(10001,omsOrderFwxmTmsXlXlLyPcEntity);
    }


    @ApiOperation("委托内容-程运序列-陆运-拼车:查询列表")
    @EciLog(title = "委托内容-程运序列-陆运-拼车:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody OmsOrderFwxmTmsXlXlLyPcEntity entity){
        List<OmsOrderFwxmTmsXlXlLyPcEntity> omsOrderFwxmTmsXlXlLyPcEntities = omsOrderFwxmTmsXlXlLyPcService.selectList(entity);
        return ResponseMsgUtil.success(10001,omsOrderFwxmTmsXlXlLyPcEntities);
    }


    @ApiOperation("委托内容-程运序列-陆运-拼车:分页查询列表")
    @EciLog(title = "委托内容-程运序列-陆运-拼车:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody OmsOrderFwxmTmsXlXlLyPcDTOEntity entity){
        TgPageInfo tgPageInfo = omsOrderFwxmTmsXlXlLyPcService.searchPageList(entity);
        return ResponseMsgUtil.successPlus(10001,tgPageInfo);
    }


    @ApiOperation("委托内容-程运序列-陆运-拼车:根据ID查一条")
    @EciLog(title = "委托内容-程运序列-陆运-拼车:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody OmsOrderFwxmTmsXlXlLyPcEntity entity){
        OmsOrderFwxmTmsXlXlLyPcEntity  omsOrderFwxmTmsXlXlLyPcEntity = omsOrderFwxmTmsXlXlLyPcService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,omsOrderFwxmTmsXlXlLyPcEntity);
    }


    @ApiOperation("委托内容-程运序列-陆运-拼车:根据ID删除一条")
    @EciLog(title = "委托内容-程运序列-陆运-拼车:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody OmsOrderFwxmTmsXlXlLyPcEntity entity){
        int count = omsOrderFwxmTmsXlXlLyPcService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("委托内容-程运序列-陆运-拼车:根据ID字符串删除多条")
    @EciLog(title = "委托内容-程运序列-陆运-拼车:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody OmsOrderFwxmTmsXlXlLyPcEntity entity) {
        int count = omsOrderFwxmTmsXlXlLyPcService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }

    @ApiOperation("委托内容-程运序列-陆运-拼车:可拼承运序列")
    @EciLog(title = "委托内容-程运序列-陆运-拼车:可拼承运序列", businessType = BusinessType.INSERT)
    @PostMapping("/addDpc")
    @EciAction()
    public ResponseMsg addDpc(@RequestBody OmsOrderFwxmTmsXlXlLyPcDTOEntity entity) {
        int count = omsOrderFwxmTmsXlXlLyPcService.addDpc(entity);
        return ResponseMsgUtil.success(10001,count);
    }

    @ApiOperation("委托内容-程运序列-陆运-拼车:移除被拼明细")
    @EciLog(title = "委托内容-程运序列-陆运-拼车:移除被拼明细", businessType = BusinessType.DELETE)
    @PostMapping("/cancelPc")
    @EciAction()
    public ResponseMsg cancelPc(@RequestBody OmsOrderFwxmTmsXlXlLyPcDTOEntity entity) {
        int count = omsOrderFwxmTmsXlXlLyPcService.cancelPc(entity);
        return ResponseMsgUtil.success(10001,count);
    }

    @ApiOperation("委托内容-程运序列-陆运-拼车:查询自拼明细数量")
    @EciLog(title = "委托内容-程运序列-陆运-拼车:查询自拼明细数量", businessType = BusinessType.SELECT)
    @PostMapping("/getZpNum")
    @EciAction()
    public ResponseMsg getZpNum(@RequestBody OmsOrderFwxmTmsXlXlLyPcDTOEntity entity) {
        Long count = omsOrderFwxmTmsXlXlLyPcService.getZpNum(entity.getZpSeqNo());
        return ResponseMsgUtil.success(10001,count);
    }
}