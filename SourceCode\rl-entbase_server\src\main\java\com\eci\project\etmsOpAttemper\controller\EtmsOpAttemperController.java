package com.eci.project.etmsOpAttemper.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.etmsOpAttemper.service.EtmsOpAttemperService;
import com.eci.project.etmsOpAttemper.entity.EtmsOpAttemperEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 运输委托信息Controller
*
* @<NAME_EMAIL>
* @date 2025-06-13
*/
@Api(tags = "运输委托信息")
@RestController
@RequestMapping("/etmsOpAttemper")
public class EtmsOpAttemperController extends EciBaseController {

    @Autowired
    private EtmsOpAttemperService etmsOpAttemperService;


    @ApiOperation("运输委托信息:保存")
    @EciLog(title = "运输委托信息:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody EtmsOpAttemperEntity entity){
        EtmsOpAttemperEntity etmsOpAttemperEntity =etmsOpAttemperService.save(entity);
        return ResponseMsgUtil.success(10001,etmsOpAttemperEntity);
    }


    @ApiOperation("运输委托信息:查询列表")
    @EciLog(title = "运输委托信息:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody EtmsOpAttemperEntity entity){
        List<EtmsOpAttemperEntity> etmsOpAttemperEntities = etmsOpAttemperService.selectList(entity);
        return ResponseMsgUtil.success(10001,etmsOpAttemperEntities);
    }


    @ApiOperation("运输委托信息:分页查询列表")
    @EciLog(title = "运输委托信息:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody EtmsOpAttemperEntity entity){
        TgPageInfo tgPageInfo = etmsOpAttemperService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("运输委托信息:根据ID查一条")
    @EciLog(title = "运输委托信息:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody EtmsOpAttemperEntity entity){
        EtmsOpAttemperEntity  etmsOpAttemperEntity = etmsOpAttemperService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,etmsOpAttemperEntity);
    }


    @ApiOperation("运输委托信息:根据ID删除一条")
    @EciLog(title = "运输委托信息:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody EtmsOpAttemperEntity entity){
        int count = etmsOpAttemperService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("运输委托信息:根据ID字符串删除多条")
    @EciLog(title = "运输委托信息:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody EtmsOpAttemperEntity entity) {
        int count = etmsOpAttemperService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}