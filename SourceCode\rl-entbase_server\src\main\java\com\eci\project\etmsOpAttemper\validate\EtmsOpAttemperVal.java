package com.eci.project.etmsOpAttemper.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.etmsOpAttemper.entity.EtmsOpAttemperEntity;

import org.springframework.stereotype.Service;


/**
* 运输委托信息Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-06-13
*/
@Service
public class EtmsOpAttemperVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(EtmsOpAttemperEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(EtmsOpAttemperEntity entity, BusinessType businessType) {

    }

}
