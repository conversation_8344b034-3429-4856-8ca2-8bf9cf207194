package com.eci.project.omsOrderGoods.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.common.DictField;
import com.eci.common.ZsrBaseEntity;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 货物信息表对象 OMS_ORDER_GOODS
 * 可以自己扩展字段
 *
 * @<NAME_EMAIL>
 * @date 2025-06-10
 */
@ApiModel("货物信息表")
@TableName("OMS_ORDER_GOODS")
@FieldNameConstants
public class OmsOrderGoodsEntity extends ZsrBaseEntity {
    /**
     * GUID
     */
    @ApiModelProperty("GUID(36)")
    @TableId("GUID")
    private String guid;

    /**
     * 订单号
     */
    @ApiModelProperty("订单号(36)")
    @TableField("ORDER_NO")
    private String orderNo;

    /**
     * 委托单编号
     */
    @ApiModelProperty("委托单编号(36)")
    @TableField("PRE_NO")
    private String preNo;

    /**
     * 总毛重(kg)
     */
    @ApiModelProperty("总毛重(kg)(22)")
    @TableField("WEIGHT_TOTAL")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal weightTotal;

    /**
     * 计费重量(kg)
     */
    @ApiModelProperty("计费重量(kg)(22)")
    @TableField("WEIGHT_CALC")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal weightCalc;

    /**
     * 总体积(m3)
     */
    @ApiModelProperty("总体积(m3)(22)")
    @TableField("VOLUME_TOTAL")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal volumeTotal;

    /**
     * 品名
     */
    @ApiModelProperty("品名(50)")
    @TableField("GOODS_NAME")
    private String goodsName;

    /**
     * 货物属性
     */
    @ApiModelProperty("货物属性(50)")
    @TableField("GOODS_PROTETY")
    @DictField(queryKey = "OMS_GOODS_ATTR")
    private String goodsProtety;

    /**
     * 客户合同号
     */
    @ApiModelProperty("客户合同号(4,000)")
    @TableField("CONTRACT_NO")
    private String contractNo;

    /**
     * 客户发票号
     */
    @ApiModelProperty("客户发票号(500)")
    @TableField("INVOICE_NO")
    private String invoiceNo;

    /**
     * 客户订单号(即客户单据编号)
     */
    @ApiModelProperty("客户订单号(即客户单据编号)(100)")
    @TableField("CUSTOMER_ORDER_NO")
    private String customerOrderNo;

    /**
     * 是否要求防震
     */
    @ApiModelProperty("是否要求防震(1)")
    @TableField("IS_GOODS_FZ")
    private String isGoodsFz;

    /**
     * 是否要求防倾斜
     */
    @ApiModelProperty("是否要求防倾斜(1)")
    @TableField("IS_GOODS_FQX")
    private String isGoodsFqx;

    /**
     * 温度要求-下限
     */
    @ApiModelProperty("温度要求-下限(22)")
    @TableField("WD_MIN")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal wdMin;

    /**
     * 温度要求-上限
     */
    @ApiModelProperty("温度要求-上限(22)")
    @TableField("WD_MAX")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal wdMax;

    /**
     * 湿度要求-下限
     */
    @ApiModelProperty("湿度要求-下限(22)")
    @TableField("SD_MIN")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal sdMin;

    /**
     * 湿度要求-上限
     */
    @ApiModelProperty("湿度要求-上限(22)")
    @TableField("SD_MAX")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal sdMax;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
     * 创建日期
     */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist = false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist = false)
    private Date createDateEnd;

    /**
     * 最后修改人
     */
    @ApiModelProperty("最后修改人(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
     * 最后修改人
     */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
     * 最后修改日期
     */
    @ApiModelProperty("最后修改日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("最后修改日期开始")
    @TableField(exist = false)
    private Date updateDateStart;

    @ApiModelProperty("最后修改日期结束")
    @TableField(exist = false)
    private Date updateDateEnd;

    /**
     * 归属公司
     */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
     * 归属公司
     */
    @ApiModelProperty("归属公司(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
     * 归属组织
     */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
     * 归属组织
     */
    @ApiModelProperty("归属组织(200)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
     * 归属集团
     */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
     * 归属集团
     */
    @ApiModelProperty("归属集团(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
     * 货源地
     */
    @ApiModelProperty("货源地(20)")
    @TableField("HYD")
    @DictField(queryKey = "OMS_BD_AREA")
    private String hyd;

    /**
     * 总净重(kg)
     */
    @ApiModelProperty("总净重(kg)(22)")
    @TableField("NET_TOTAL")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal netTotal;

    /**
     * 监管方式
     */
    @ApiModelProperty("监管方式(10)")
    @TableField("JGFS")
    @DictField(queryKey = "OMS_BD_TRADE")
    private String jgfs;

    /**
     * 进仓编号
     */
    @ApiModelProperty("进仓编号(255)")
    @TableField("WAREHOUSE_IN_NO")
    private String warehouseInNo;

    /**
     * 主单号
     */
    @ApiModelProperty("主单号(255)")
    @TableField("MB_NO")
    private String mbNo;

    /**
     * 分单号
     */
    @ApiModelProperty("分单号(255)")
    @TableField("HB_NO")
    private String hbNo;

    /**
     * 总件数
     */
    @ApiModelProperty("总件数(22)")
    @TableField("PIECE_TOTAL")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal pieceTotal;

    /**
     * 提货单号
     */
    @ApiModelProperty("提货单号(50)")
    @TableField("THDH")
    private String thdh;

    /**
     * 经销商代码
     */
    @ApiModelProperty("经销商代码(100)")
    @TableField("JXS_CODE")
    private String jxsCode;

    /**
     * UN号数
     */
    @ApiModelProperty("UN号数(22)")
    @TableField("UN_NUM")
    private Integer unNum;

    /**
     * 数量(大桶)
     */
    @ApiModelProperty("数量(大桶)(22)")
    @TableField("QTY_DT")
    private Integer qtyDt;

    /**
     * 总托数
     */
    @ApiModelProperty("总托数(22)")
    @TableField("ZTS")
    private Integer zts;

    /**
     * 直发CSD/船名
     */
    @ApiModelProperty("直发CSD/船名(100)")
    @TableField("ZFCSD")
    private String zfcsd;

    /**
     * 数量(小桶)
     */
    @ApiModelProperty("数量(小桶)(22)")
    @TableField("QTY_XT")
    private Integer qtyXt;

    /**
     * 成交方式
     */
    @ApiModelProperty("成交方式(10)")
    @TableField("CJFS")
    @DictField(queryKey = "OMS_BD_DEAL")
    private String cjfs;

    /**
     * 包装类型
     */
    @ApiModelProperty("包装类型(500)")
    @TableField("PACK_TYPE")
    private String packType;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public OmsOrderGoodsEntity() {
        this.setSubClazz(OmsOrderGoodsEntity.class);
    }

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public OmsOrderGoodsEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getOrderNo() {
        this.nodifyGetFiled("orderNo");
        return orderNo;
    }

    public OmsOrderGoodsEntity setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        this.nodifySetFiled("orderNo", orderNo);
        return this;
    }

    public String getPreNo() {
        this.nodifyGetFiled("preNo");
        return preNo;
    }

    public OmsOrderGoodsEntity setPreNo(String preNo) {
        this.preNo = preNo;
        this.nodifySetFiled("preNo", preNo);
        return this;
    }

    public BigDecimal getWeightTotal() {
        this.nodifyGetFiled("weightTotal");
        return weightTotal;
    }

    public OmsOrderGoodsEntity setWeightTotal(BigDecimal weightTotal) {
        this.weightTotal = weightTotal;
        this.nodifySetFiled("weightTotal", weightTotal);
        return this;
    }

    public BigDecimal getWeightCalc() {
        this.nodifyGetFiled("weightCalc");
        return weightCalc;
    }

    public OmsOrderGoodsEntity setWeightCalc(BigDecimal weightCalc) {
        this.weightCalc = weightCalc;
        this.nodifySetFiled("weightCalc", weightCalc);
        return this;
    }

    public BigDecimal getVolumeTotal() {
        this.nodifyGetFiled("volumeTotal");
        return volumeTotal;
    }

    public OmsOrderGoodsEntity setVolumeTotal(BigDecimal volumeTotal) {
        this.volumeTotal = volumeTotal;
        this.nodifySetFiled("volumeTotal", volumeTotal);
        return this;
    }

    public String getGoodsName() {
        this.nodifyGetFiled("goodsName");
        return goodsName;
    }

    public OmsOrderGoodsEntity setGoodsName(String goodsName) {
        this.goodsName = goodsName;
        this.nodifySetFiled("goodsName", goodsName);
        return this;
    }

    public String getGoodsProtety() {
        this.nodifyGetFiled("goodsProtety");
        return goodsProtety;
    }

    public OmsOrderGoodsEntity setGoodsProtety(String goodsProtety) {
        this.goodsProtety = goodsProtety;
        this.nodifySetFiled("goodsProtety", goodsProtety);
        return this;
    }

    public String getContractNo() {
        this.nodifyGetFiled("contractNo");
        return contractNo;
    }

    public OmsOrderGoodsEntity setContractNo(String contractNo) {
        this.contractNo = contractNo;
        this.nodifySetFiled("contractNo", contractNo);
        return this;
    }

    public String getInvoiceNo() {
        this.nodifyGetFiled("invoiceNo");
        return invoiceNo;
    }

    public OmsOrderGoodsEntity setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
        this.nodifySetFiled("invoiceNo", invoiceNo);
        return this;
    }

    public String getCustomerOrderNo() {
        this.nodifyGetFiled("customerOrderNo");
        return customerOrderNo;
    }

    public OmsOrderGoodsEntity setCustomerOrderNo(String customerOrderNo) {
        this.customerOrderNo = customerOrderNo;
        this.nodifySetFiled("customerOrderNo", customerOrderNo);
        return this;
    }

    public String getIsGoodsFz() {
        this.nodifyGetFiled("isGoodsFz");
        return isGoodsFz;
    }

    public OmsOrderGoodsEntity setIsGoodsFz(String isGoodsFz) {
        this.isGoodsFz = isGoodsFz;
        this.nodifySetFiled("isGoodsFz", isGoodsFz);
        return this;
    }

    public String getIsGoodsFqx() {
        this.nodifyGetFiled("isGoodsFqx");
        return isGoodsFqx;
    }

    public OmsOrderGoodsEntity setIsGoodsFqx(String isGoodsFqx) {
        this.isGoodsFqx = isGoodsFqx;
        this.nodifySetFiled("isGoodsFqx", isGoodsFqx);
        return this;
    }

    public BigDecimal getWdMin() {
        this.nodifyGetFiled("wdMin");
        return wdMin;
    }

    public OmsOrderGoodsEntity setWdMin(BigDecimal wdMin) {
        this.wdMin = wdMin;
        this.nodifySetFiled("wdMin", wdMin);
        return this;
    }

    public BigDecimal getWdMax() {
        this.nodifyGetFiled("wdMax");
        return wdMax;
    }

    public OmsOrderGoodsEntity setWdMax(BigDecimal wdMax) {
        this.wdMax = wdMax;
        this.nodifySetFiled("wdMax", wdMax);
        return this;
    }

    public BigDecimal getSdMin() {
        this.nodifyGetFiled("sdMin");
        return sdMin;
    }

    public OmsOrderGoodsEntity setSdMin(BigDecimal sdMin) {
        this.sdMin = sdMin;
        this.nodifySetFiled("sdMin", sdMin);
        return this;
    }

    public BigDecimal getSdMax() {
        this.nodifyGetFiled("sdMax");
        return sdMax;
    }

    public OmsOrderGoodsEntity setSdMax(BigDecimal sdMax) {
        this.sdMax = sdMax;
        this.nodifySetFiled("sdMax", sdMax);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public OmsOrderGoodsEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public OmsOrderGoodsEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public OmsOrderGoodsEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public OmsOrderGoodsEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }

    public OmsOrderGoodsEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public OmsOrderGoodsEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public OmsOrderGoodsEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public OmsOrderGoodsEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public OmsOrderGoodsEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }

    public OmsOrderGoodsEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public OmsOrderGoodsEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public OmsOrderGoodsEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public OmsOrderGoodsEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public OmsOrderGoodsEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public OmsOrderGoodsEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public OmsOrderGoodsEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getHyd() {
        this.nodifyGetFiled("hyd");
        return hyd;
    }

    public OmsOrderGoodsEntity setHyd(String hyd) {
        this.hyd = hyd;
        this.nodifySetFiled("hyd", hyd);
        return this;
    }

    public BigDecimal getNetTotal() {
        this.nodifyGetFiled("netTotal");
        return netTotal;
    }

    public OmsOrderGoodsEntity setNetTotal(BigDecimal netTotal) {
        this.netTotal = netTotal;
        this.nodifySetFiled("netTotal", netTotal);
        return this;
    }

    public String getJgfs() {
        this.nodifyGetFiled("jgfs");
        return jgfs;
    }

    public OmsOrderGoodsEntity setJgfs(String jgfs) {
        this.jgfs = jgfs;
        this.nodifySetFiled("jgfs", jgfs);
        return this;
    }

    public String getWarehouseInNo() {
        this.nodifyGetFiled("warehouseInNo");
        return warehouseInNo;
    }

    public OmsOrderGoodsEntity setWarehouseInNo(String warehouseInNo) {
        this.warehouseInNo = warehouseInNo;
        this.nodifySetFiled("warehouseInNo", warehouseInNo);
        return this;
    }

    public String getMbNo() {
        this.nodifyGetFiled("mbNo");
        return mbNo;
    }

    public OmsOrderGoodsEntity setMbNo(String mbNo) {
        this.mbNo = mbNo;
        this.nodifySetFiled("mbNo", mbNo);
        return this;
    }

    public String getHbNo() {
        this.nodifyGetFiled("hbNo");
        return hbNo;
    }

    public OmsOrderGoodsEntity setHbNo(String hbNo) {
        this.hbNo = hbNo;
        this.nodifySetFiled("hbNo", hbNo);
        return this;
    }

    public BigDecimal getPieceTotal() {
        this.nodifyGetFiled("pieceTotal");
        return pieceTotal;
    }

    public OmsOrderGoodsEntity setPieceTotal(BigDecimal pieceTotal) {
        this.pieceTotal = pieceTotal;
        this.nodifySetFiled("pieceTotal", pieceTotal);
        return this;
    }

    public String getThdh() {
        this.nodifyGetFiled("thdh");
        return thdh;
    }

    public OmsOrderGoodsEntity setThdh(String thdh) {
        this.thdh = thdh;
        this.nodifySetFiled("thdh", thdh);
        return this;
    }

    public String getJxsCode() {
        this.nodifyGetFiled("jxsCode");
        return jxsCode;
    }

    public OmsOrderGoodsEntity setJxsCode(String jxsCode) {
        this.jxsCode = jxsCode;
        this.nodifySetFiled("jxsCode", jxsCode);
        return this;
    }

    public Integer getUnNum() {
        this.nodifyGetFiled("unNum");
        return unNum;
    }

    public OmsOrderGoodsEntity setUnNum(Integer unNum) {
        this.unNum = unNum;
        this.nodifySetFiled("unNum", unNum);
        return this;
    }

    public Integer getQtyDt() {
        this.nodifyGetFiled("qtyDt");
        return qtyDt;
    }

    public OmsOrderGoodsEntity setQtyDt(Integer qtyDt) {
        this.qtyDt = qtyDt;
        this.nodifySetFiled("qtyDt", qtyDt);
        return this;
    }

    public Integer getZts() {
        this.nodifyGetFiled("zts");
        return zts;
    }

    public OmsOrderGoodsEntity setZts(Integer zts) {
        this.zts = zts;
        this.nodifySetFiled("zts", zts);
        return this;
    }

    public String getZfcsd() {
        this.nodifyGetFiled("zfcsd");
        return zfcsd;
    }

    public OmsOrderGoodsEntity setZfcsd(String zfcsd) {
        this.zfcsd = zfcsd;
        this.nodifySetFiled("zfcsd", zfcsd);
        return this;
    }

    public Integer getQtyXt() {
        this.nodifyGetFiled("qtyXt");
        return qtyXt;
    }

    public OmsOrderGoodsEntity setQtyXt(Integer qtyXt) {
        this.qtyXt = qtyXt;
        this.nodifySetFiled("qtyXt", qtyXt);
        return this;
    }

    public String getCjfs() {
        this.nodifyGetFiled("cjfs");
        return cjfs;
    }

    public OmsOrderGoodsEntity setCjfs(String cjfs) {
        this.cjfs = cjfs;
        this.nodifySetFiled("cjfs", cjfs);
        return this;
    }

    public String getPackType() {
        this.nodifyGetFiled("packType");
        return packType;
    }

    public OmsOrderGoodsEntity setPackType(String packType) {
        this.packType = packType;
        this.nodifySetFiled("packType", packType);
        return this;
    }

}
