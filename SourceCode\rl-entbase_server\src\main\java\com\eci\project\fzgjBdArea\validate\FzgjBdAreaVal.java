package com.eci.project.fzgjBdArea.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjBdArea.entity.FzgjBdAreaBaseEntity;
import com.eci.project.fzgjBdArea.entity.FzgjBdAreaEntity;

import org.springframework.stereotype.Service;


/**
* 公路乡镇地区Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-03-26
*/
@Service
public class FzgjBdAreaVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjBdAreaEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjBdAreaBaseEntity entity, BusinessType businessType) {

    }

}
