package com.eci.project.fzgjBdBill.validate;

import com.eci.common.validations.ZsrValidation;
import com.eci.common.validations.ZsrValidationUtil;
import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjBdBill.entity.FzgjBdBillEntity;

import org.springframework.stereotype.Service;


/**
* 单据类型Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-03-19
*/
@Service
public class FzgjBdBillVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjBdBillEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjBdBillEntity entity, BusinessType businessType) throws IllegalAccessException {
        ZsrValidationUtil.validation(entity);
    }

}
