package com.eci.project.fzgjExceptionType.service;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.util.StringUtils;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.exception.BaseException;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjBaseDataDetail.dao.FzgjBaseDataDetailDao;
import com.eci.project.fzgjBaseDataDetail.entity.FzgjBaseDataDetailEntity;
import com.eci.project.fzgjExceptionType.dao.FzgjExceptionTypeDao;
import com.eci.project.fzgjExceptionType.entity.*;
import com.eci.project.fzgjExceptionType.validate.FzgjExceptionTypeVal;
import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 异常类型Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-04-02
 */
@Service
@Slf4j
public class FzgjExceptionTypeService implements EciBaseService<FzgjExceptionTypeEntity> {

    @Autowired
    private FzgjExceptionTypeDao fzgjExceptionTypeDao;

    @Autowired
    private FzgjExceptionTypeVal fzgjExceptionTypeVal;

    @Autowired
    private FzgjBaseDataDetailDao fzgjBaseDataDetailDao;

    @Override
    public TgPageInfo queryPageList(FzgjExceptionTypeEntity entity) {
        EciQuery<FzgjExceptionTypeEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjExceptionTypeEntity> entities = fzgjExceptionTypeDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjExceptionTypeEntity save(FzgjExceptionTypeEntity entity) {
        // 返回实体对象
        FzgjExceptionTypeEntity fzgjExceptionTypeEntity = null;
        fzgjExceptionTypeVal.saveValidate(entity, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setGuid(IdWorker.get32UUID());
            entity.setCreateDate(new java.util.Date());
            entity.setUpdateDate(new java.util.Date());
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
            entity.setNodeCode(UserContext.getUserInfo().getDeptCode());
            entity.setNodeName(UserContext.getUserInfo().getDeptName());
            entity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            entity.setCompanyName(UserContext.getUserInfo().getCompanyName());

            // 新增时，当前树下的最大序号+1
            FzgjExceptionTypeEntity maxSeqEntity = new FzgjExceptionTypeEntity();
            maxSeqEntity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            maxSeqEntity.setSysCode(entity.getSysCode());
            Integer maxSeq = searchMaxSeq(maxSeqEntity);
            entity.setSeq(maxSeq + 1);

            fzgjExceptionTypeEntity = fzgjExceptionTypeDao.insertOne(entity);

        } else {
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            entity.setUpdateDate(new java.util.Date());
            fzgjExceptionTypeEntity = fzgjExceptionTypeDao.updateByEntityId(entity);

        }
        return fzgjExceptionTypeEntity;
    }

    /**
     * 获取最大的序号
     */
    public Integer searchMaxSeq(FzgjExceptionTypeEntity entity) {

        List<FzgjExceptionTypeEntity> list = fzgjExceptionTypeDao.selectList(entity);

        if (list.size() > 0) {
            FzgjExceptionTypeEntity maxItem = Collections.max(list, Comparator.comparingInt(FzgjExceptionTypeEntity::getSeq));

            return maxItem.getSeq();
        }

        return 0;
    }

    @Override
    public List<FzgjExceptionTypeEntity> selectList(FzgjExceptionTypeEntity entity) {
        return fzgjExceptionTypeDao.selectList(entity);
    }

    @Override
    public FzgjExceptionTypeEntity selectOneById(Serializable id) {
        return fzgjExceptionTypeDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjExceptionTypeEntity> list) {
        fzgjExceptionTypeDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjExceptionTypeDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjExceptionTypeDao.deleteById(id);
    }

    /**
     * 获取作业系统数据
     ***/
    public List<ResNodeItemEntity> searchNodeList(ReqNodeItemEntity entity) {

        List<ResNodeItemEntity> nodeItemList = new ArrayList<>();

        if (StringUtils.isEmpty(entity.getId())) {

            FzgjBaseDataDetailEntity fzgjBaseDataDetailEntity = new FzgjBaseDataDetailEntity();
            fzgjBaseDataDetailEntity.setGroupCode("SYS"); // 动态条件

            List<FzgjBaseDataDetailEntity> dataDetailList = fzgjBaseDataDetailDao.selectListInfo(fzgjBaseDataDetailEntity);
            if (dataDetailList != null && dataDetailList.size() > 0) {
                dataDetailList.forEach(item -> {
                    ResNodeItemEntity resNodeItemEntity = new ResNodeItemEntity();
                    resNodeItemEntity.setId(item.getCode());
                    resNodeItemEntity.setLabel(item.getName());
                    resNodeItemEntity.setState("open");
                    nodeItemList.add(resNodeItemEntity);
                });
            }
        }

        return nodeItemList;
    }


    /**
     * 获取异常类型数据
     **/
    public List<ResExceptionTypeNodeListEntity> searchExceptionTypeNodeList(ReqExceptionTypeNodeEntity entity) {

        List<ResExceptionTypeNodeListEntity> resExceptionTypeNodeList = new ArrayList<>();

        if (StringUtils.isNotEmpty(entity.getSysCode())) {

            // 1-主节点
            ResExceptionTypeNodeListEntity resExceptionTypeNodeListEntity = new ResExceptionTypeNodeListEntity();
            resExceptionTypeNodeListEntity.setId("0");
            resExceptionTypeNodeListEntity.setLabel(entity.getSysName());
            resExceptionTypeNodeListEntity.setState("open");

            // 2-异常类型节点
            FzgjExceptionTypeEntity fzgjExceptionTypeEntity = new FzgjExceptionTypeEntity();
            fzgjExceptionTypeEntity.setSysCode(entity.getSysCode());
            fzgjExceptionTypeEntity.setStatus(entity.getStatus());
            List<FzgjExceptionTypeEntity> nodeList = fzgjExceptionTypeDao.selectList(fzgjExceptionTypeEntity);
            nodeList = nodeList.stream().sorted(Comparator.comparing(p -> p.getSeq())).collect(Collectors.toList());

            List<ResNodeItemEntity> bases = new ArrayList<>();

            nodeList.forEach(item -> {
                ResNodeItemEntity resNodeItemEntity = new ResNodeItemEntity();
                resNodeItemEntity.setId(item.getGuid());
                resNodeItemEntity.setLabel(item.getName());
                resNodeItemEntity.setState("open");
                bases.add(resNodeItemEntity);
            });

            if (bases.size() > 0) {
                resExceptionTypeNodeListEntity.setChildren(bases);
            }

            resExceptionTypeNodeList.add(resExceptionTypeNodeListEntity);
        }

        return resExceptionTypeNodeList;
    }

    /**
     * 异常类型数据序号上下移动
     */
    @Transactional(rollbackFor = Exception.class)
    public FzgjExceptionTypeEntity changeExceptionTypeSeq(ReqExceptionTypeSeqEntity entity) {

        if (entity.getGuid().isEmpty()) {
            throw new BaseException("异常类型为空");
        }

        entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());

        // 1-异常类型是否存在
        FzgjExceptionTypeEntity fzgjExceptionTypeEntity = fzgjExceptionTypeDao.selectOne(entity);
        if (fzgjExceptionTypeEntity == null) {
            throw new BaseException("异常类型不存在");
        }

        // 当前序号
        int exceptionTypeEntitySeq = fzgjExceptionTypeEntity.getSeq();

        // 序号变更后的实体
        FzgjExceptionTypeEntity exceptionTypechangeEntity = new FzgjExceptionTypeEntity();
        // 当前节点序号±1查询实体
        FzgjExceptionTypeEntity searchEntity = new FzgjExceptionTypeEntity();
        searchEntity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
        searchEntity.setSysCode(fzgjExceptionTypeEntity.getSysCode());

        // 2-上下移序号变更
        if (entity.getType().equals("UP")) {
            if (exceptionTypeEntitySeq == 1) {
                throw new BaseException("上移失败，当前节点序号为1");
            }

            searchEntity.setSeq(exceptionTypeEntitySeq - 1);
            exceptionTypechangeEntity = fzgjExceptionTypeDao.selectOne(searchEntity);
            if (exceptionTypechangeEntity == null) {
                throw new BaseException("数据异常");
            }

            exceptionTypechangeEntity.setSeq(exceptionTypechangeEntity.getSeq() + 1);

            fzgjExceptionTypeEntity.setSeq(exceptionTypeEntitySeq - 1);

        } else {

            FzgjExceptionTypeEntity maxSeqSearchEntity = new FzgjExceptionTypeEntity();
            maxSeqSearchEntity.setSysCode(fzgjExceptionTypeEntity.getSysCode());
            maxSeqSearchEntity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            int seq = searchMaxSeq(maxSeqSearchEntity);
            if (exceptionTypeEntitySeq == seq) {
                throw new BaseException("无法下移");
            }

            searchEntity.setSeq(exceptionTypeEntitySeq + 1);
            exceptionTypechangeEntity = fzgjExceptionTypeDao.selectOne(searchEntity);
            if (exceptionTypechangeEntity == null) {
                throw new BaseException("数据异常");
            }

            exceptionTypechangeEntity.setSeq(exceptionTypechangeEntity.getSeq() - 1);

            fzgjExceptionTypeEntity.setSeq(exceptionTypeEntitySeq + 1);
        }

        exceptionTypechangeEntity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
        exceptionTypechangeEntity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        exceptionTypechangeEntity.setUpdateDate(new java.util.Date());

        fzgjExceptionTypeEntity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
        fzgjExceptionTypeEntity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        fzgjExceptionTypeEntity.setUpdateDate(new java.util.Date());

        fzgjExceptionTypeDao.updateByEntityId(exceptionTypechangeEntity);

        fzgjExceptionTypeDao.updateByEntityId(fzgjExceptionTypeEntity);

        return fzgjExceptionTypeEntity;
    }

}