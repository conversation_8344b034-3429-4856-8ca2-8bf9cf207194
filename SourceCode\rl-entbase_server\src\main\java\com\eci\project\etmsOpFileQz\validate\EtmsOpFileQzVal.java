package com.eci.project.etmsOpFileQz.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.etmsOpFileQz.entity.EtmsOpFileQzEntity;

import org.springframework.stereotype.Service;


/**
* 业务附件Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-17
*/
@Service
public class EtmsOpFileQzVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(EtmsOpFileQzEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(EtmsOpFileQzEntity entity, BusinessType businessType) {

    }

}
