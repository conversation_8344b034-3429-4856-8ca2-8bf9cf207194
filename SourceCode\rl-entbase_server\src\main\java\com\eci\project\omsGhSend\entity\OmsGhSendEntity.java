package com.eci.project.omsGhSend.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.math.BigDecimal;
import java.util.Date;


/**
* OMS固化路由表对象 OMS_GH_SEND
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-05-16
*/
@ApiModel("OMS固化路由表")
@TableName("OMS_GH_SEND")
@FieldNameConstants
public class OmsGhSendEntity extends EciBaseEntity{
    @ApiModelProperty("(40)")
    @TableField("GUID")
    private String guid;

    /**
    * 订单号
    */
    @ApiModelProperty("订单号(40)")
    @TableField("ORDER_NO")
    private String orderNo;

    /**
    * 处理状态（0未处理 1处理成功 4处理失败）
    */
    @ApiModelProperty("处理状态（0未处理 1处理成功 4处理失败）(1)")
    @TableField("OP_FLAG")
    private String opFlag;

    /**
    * 处理日志
    */
    @ApiModelProperty("处理日志(4,000)")
    @TableField("OP_MESSAGE")
    private String opMessage;

    /**
    * 处理时间
    */
    @ApiModelProperty("处理时间(7)")
    @TableField("OP_DATE")
    private Date opDate;

    @ApiModelProperty("处理时间开始")
    @TableField(exist=false)
    private Date opDateStart;

    @ApiModelProperty("处理时间结束")
    @TableField(exist=false)
    private Date opDateEnd;

    /**
    * 新增时间
    */
    @ApiModelProperty("新增时间(7)")
    @TableField("RECODE_DATE")
    private Date recodeDate;

    @ApiModelProperty("新增时间开始")
    @TableField(exist=false)
    private Date recodeDateStart;

    @ApiModelProperty("新增时间结束")
    @TableField(exist=false)
    private Date recodeDateEnd;

    /**
    * 备注
    */
    @ApiModelProperty("备注(4,000)")
    @TableField("REMARK")
    private String remark;

    /**
    * 处理次数
    */
    @ApiModelProperty("处理次数(22)")
    @TableField("OP_NUM")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal opNum;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public OmsGhSendEntity() {
        this.setSubClazz(OmsGhSendEntity.class);
    }

    public OmsGhSendEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public OmsGhSendEntity setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        this.nodifySetFiled("orderNo", orderNo);
        return this;
    }

    public String getOrderNo() {
        this.nodifyGetFiled("orderNo");
        return orderNo;
    }

    public OmsGhSendEntity setOpFlag(String opFlag) {
        this.opFlag = opFlag;
        this.nodifySetFiled("opFlag", opFlag);
        return this;
    }

    public String getOpFlag() {
        this.nodifyGetFiled("opFlag");
        return opFlag;
    }

    public OmsGhSendEntity setOpMessage(String opMessage) {
        this.opMessage = opMessage;
        this.nodifySetFiled("opMessage", opMessage);
        return this;
    }

    public String getOpMessage() {
        this.nodifyGetFiled("opMessage");
        return opMessage;
    }

    public OmsGhSendEntity setOpDate(Date opDate) {
        this.opDate = opDate;
        this.nodifySetFiled("opDate", opDate);
        return this;
    }

    public Date getOpDate() {
        this.nodifyGetFiled("opDate");
        return opDate;
    }

    public OmsGhSendEntity setOpDateStart(Date opDateStart) {
        this.opDateStart = opDateStart;
        this.nodifySetFiled("opDateStart", opDateStart);
        return this;
    }

    public Date getOpDateStart() {
        this.nodifyGetFiled("opDateStart");
        return opDateStart;
    }

    public OmsGhSendEntity setOpDateEnd(Date opDateEnd) {
        this.opDateEnd = opDateEnd;
        this.nodifySetFiled("opDateEnd", opDateEnd);
        return this;
    }

    public Date getOpDateEnd() {
        this.nodifyGetFiled("opDateEnd");
        return opDateEnd;
    }
    public OmsGhSendEntity setRecodeDate(Date recodeDate) {
        this.recodeDate = recodeDate;
        this.nodifySetFiled("recodeDate", recodeDate);
        return this;
    }

    public Date getRecodeDate() {
        this.nodifyGetFiled("recodeDate");
        return recodeDate;
    }

    public OmsGhSendEntity setRecodeDateStart(Date recodeDateStart) {
        this.recodeDateStart = recodeDateStart;
        this.nodifySetFiled("recodeDateStart", recodeDateStart);
        return this;
    }

    public Date getRecodeDateStart() {
        this.nodifyGetFiled("recodeDateStart");
        return recodeDateStart;
    }

    public OmsGhSendEntity setRecodeDateEnd(Date recodeDateEnd) {
        this.recodeDateEnd = recodeDateEnd;
        this.nodifySetFiled("recodeDateEnd", recodeDateEnd);
        return this;
    }

    public Date getRecodeDateEnd() {
        this.nodifyGetFiled("recodeDateEnd");
        return recodeDateEnd;
    }
    public OmsGhSendEntity setRemark(String remark) {
        this.remark = remark;
        this.nodifySetFiled("remark", remark);
        return this;
    }

    public String getRemark() {
        this.nodifyGetFiled("remark");
        return remark;
    }

    public OmsGhSendEntity setOpNum(BigDecimal opNum) {
        this.opNum = opNum;
        this.nodifySetFiled("opNum", opNum);
        return this;
    }

    public BigDecimal getOpNum() {
        this.nodifyGetFiled("opNum");
        return opNum;
    }

}
