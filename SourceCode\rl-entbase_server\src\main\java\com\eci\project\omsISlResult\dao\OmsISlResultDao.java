package com.eci.project.omsISlResult.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.omsISlResult.entity.OmsISlResultEntity;


/**
* 受理结果及单据编号Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-04-21
*/
public interface OmsISlResultDao extends EciBaseDao<OmsISlResultEntity> {

}