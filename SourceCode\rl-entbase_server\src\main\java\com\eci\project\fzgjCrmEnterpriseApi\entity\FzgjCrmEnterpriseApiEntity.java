package com.eci.project.fzgjCrmEnterpriseApi.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 注册企业发送接口对象 FZGJ_CRM_ENTERPRISE_API
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-03-13
*/
@ApiModel("注册企业发送接口")
@TableName("FZGJ_CRM_ENTERPRISE_API")
@FieldNameConstants
public class FzgjCrmEnterpriseApiEntity extends EciBaseEntity{
    /**
    * GUID
    */
    @ApiModelProperty("GUID(50)")
    @TableId("GUID")
    private String guid;

    /**
    * 业务伙伴代码
    */
    @ApiModelProperty("业务伙伴代码(36)")
    @TableField("CRM_CODE")
    private String crmCode;

    /**
    * 接口类型
    */
    @ApiModelProperty("接口类型(50)")
    @TableField("API_TYPE")
    private String apiType;

    /**
    * 接口名称
    */
    @ApiModelProperty("接口名称(20)")
    @TableField("API_NAME")
    private String apiName;

    /**
    * 接口名称
    */
    @ApiModelProperty("接口名称(1)")
    @TableField("API_VALUE")
    private String apiValue;

    /**
    * CREATE_USER
    */
    @ApiModelProperty("CREATE_USER(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * CREATE_DATE
    */
    @ApiModelProperty("CREATE_DATE(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("CREATE_DATE开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("CREATE_DATE结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * UPDATE_USER
    */
    @ApiModelProperty("UPDATE_USER(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * UPDATE_DATE
    */
    @ApiModelProperty("UPDATE_DATE(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("UPDATE_DATE开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("UPDATE_DATE结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * CREATE_USER
    */
    @ApiModelProperty("CREATE_USER(200)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * UPDATE_USER
    */
    @ApiModelProperty("UPDATE_USER(200)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public FzgjCrmEnterpriseApiEntity() {
        this.setSubClazz(FzgjCrmEnterpriseApiEntity.class);
    }

    public FzgjCrmEnterpriseApiEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public FzgjCrmEnterpriseApiEntity setCrmCode(String crmCode) {
        this.crmCode = crmCode;
        this.nodifySetFiled("crmCode", crmCode);
        return this;
    }

    public String getCrmCode() {
        this.nodifyGetFiled("crmCode");
        return crmCode;
    }

    public FzgjCrmEnterpriseApiEntity setApiType(String apiType) {
        this.apiType = apiType;
        this.nodifySetFiled("apiType", apiType);
        return this;
    }

    public String getApiType() {
        this.nodifyGetFiled("apiType");
        return apiType;
    }

    public FzgjCrmEnterpriseApiEntity setApiName(String apiName) {
        this.apiName = apiName;
        this.nodifySetFiled("apiName", apiName);
        return this;
    }

    public String getApiName() {
        this.nodifyGetFiled("apiName");
        return apiName;
    }

    public FzgjCrmEnterpriseApiEntity setApiValue(String apiValue) {
        this.apiValue = apiValue;
        this.nodifySetFiled("apiValue", apiValue);
        return this;
    }

    public String getApiValue() {
        this.nodifyGetFiled("apiValue");
        return apiValue;
    }

    public FzgjCrmEnterpriseApiEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public FzgjCrmEnterpriseApiEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public FzgjCrmEnterpriseApiEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public FzgjCrmEnterpriseApiEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public FzgjCrmEnterpriseApiEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public FzgjCrmEnterpriseApiEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public FzgjCrmEnterpriseApiEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public FzgjCrmEnterpriseApiEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public FzgjCrmEnterpriseApiEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public FzgjCrmEnterpriseApiEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

}
