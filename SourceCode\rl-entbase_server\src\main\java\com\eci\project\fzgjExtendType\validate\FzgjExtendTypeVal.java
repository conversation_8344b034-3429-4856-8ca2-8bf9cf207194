package com.eci.project.fzgjExtendType.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjExtendType.entity.FzgjExtendTypeEntity;

import org.springframework.stereotype.Service;


/**
* 扩展基础资料类型Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-03-19
*/
@Service
public class FzgjExtendTypeVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjExtendTypeEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjExtendTypeEntity entity, BusinessType businessType) {

    }

}
