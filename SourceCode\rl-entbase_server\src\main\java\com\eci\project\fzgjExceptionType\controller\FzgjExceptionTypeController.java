package com.eci.project.fzgjExceptionType.controller;

import com.eci.common.util.ResponseMsg;
import com.eci.common.util.ResponseMsgUtil;
import com.eci.crud.controller.EciBaseController;
import com.eci.log.annotation.EciAction;
import com.eci.log.annotation.EciLog;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjExceptionType.entity.FzgjExceptionTypeEntity;
import com.eci.project.fzgjExceptionType.entity.ReqExceptionTypeNodeEntity;
import com.eci.project.fzgjExceptionType.entity.ReqExceptionTypeSeqEntity;
import com.eci.project.fzgjExceptionType.entity.ReqNodeItemEntity;
import com.eci.project.fzgjExceptionType.service.FzgjExceptionTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.swing.text.html.parser.Entity;
import java.util.List;

/**
 * 异常类型Controller
 *
 * @<NAME_EMAIL>
 * @date 2025-04-02
 */
@Api(tags = "异常类型")
@RestController
@RequestMapping("/fzgjExceptionType")
public class FzgjExceptionTypeController extends EciBaseController {

    @Autowired
    private FzgjExceptionTypeService fzgjExceptionTypeService;


    @ApiOperation("异常类型:保存")
    @EciLog(title = "异常类型:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody FzgjExceptionTypeEntity entity) {
        FzgjExceptionTypeEntity fzgjExceptionTypeEntity = fzgjExceptionTypeService.save(entity);
        return ResponseMsgUtil.success(10001, fzgjExceptionTypeEntity);
    }


    @ApiOperation("异常类型:查询列表")
    @EciLog(title = "异常类型:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjExceptionTypeEntity entity) {
        List<FzgjExceptionTypeEntity> fzgjExceptionTypeEntities = fzgjExceptionTypeService.selectList(entity);
        return ResponseMsgUtil.success(10001, fzgjExceptionTypeEntities);
    }


    @ApiOperation("异常类型:分页查询列表")
    @EciLog(title = "异常类型:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjExceptionTypeEntity entity) {
        TgPageInfo tgPageInfo = fzgjExceptionTypeService.queryPageList(entity);
        return ResponseMsgUtil.success(10001, tgPageInfo);
    }


    @ApiOperation("异常类型:根据ID查一条")
    @EciLog(title = "异常类型:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjExceptionTypeEntity entity) {
        FzgjExceptionTypeEntity fzgjExceptionTypeEntity = fzgjExceptionTypeService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001, fzgjExceptionTypeEntity);
    }


    @ApiOperation("异常类型:根据ID删除一条")
    @EciLog(title = "异常类型:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjExceptionTypeEntity entity) {
        int count = fzgjExceptionTypeService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001, count);
    }


    @ApiOperation("异常类型:根据ID字符串删除多条")
    @EciLog(title = "异常类型:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjExceptionTypeEntity entity) {
        int count = fzgjExceptionTypeService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001, count);
    }

    @ApiOperation("异常类型:获取作业系统数据")
    @EciLog(title = "异常类型:获取作业系统数据", businessType = BusinessType.SELECT)
    @PostMapping("/searchNodeList")
    @EciAction()
    public ResponseMsg searchNodeList(@RequestBody ReqNodeItemEntity entity) {
        return ResponseMsgUtil.success(10001, fzgjExceptionTypeService.searchNodeList(entity));
    }

    @ApiOperation("异常类型:获取异常类型数据")
    @EciLog(title = "异常类型:获取异常类型数据", businessType = BusinessType.SELECT)
    @PostMapping("/searchExceptionTypeNodeList")
    @EciAction()
    public ResponseMsg searchExceptionTypeNodeList(@RequestBody ReqExceptionTypeNodeEntity entity) {
        return ResponseMsgUtil.success(10001, fzgjExceptionTypeService.searchExceptionTypeNodeList(entity));
    }

    @ApiOperation("异常类型:序号上下移")
    @EciLog(title = "异常类型:序号上下移", businessType = BusinessType.SELECT)
    @PostMapping("/changeExceptionTypeSeq")
    @EciAction()
    public ResponseMsg changeExceptionTypeSeq(@RequestBody ReqExceptionTypeSeqEntity entity) {
        return ResponseMsgUtil.success(10001, fzgjExceptionTypeService.changeExceptionTypeSeq(entity));
    }


}