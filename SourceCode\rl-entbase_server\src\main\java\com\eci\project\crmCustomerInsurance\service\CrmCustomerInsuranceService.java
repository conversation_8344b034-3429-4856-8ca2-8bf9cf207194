package com.eci.project.crmCustomerInsurance.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.crmCustomerInsurance.dao.CrmCustomerInsuranceDao;
import com.eci.project.crmCustomerInsurance.entity.CrmCustomerInsuranceEntity;
import com.eci.project.crmCustomerInsurance.validate.CrmCustomerInsuranceVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 司机保险管理Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-05-26
*/
@Service
@Slf4j
public class CrmCustomerInsuranceService implements EciBaseService<CrmCustomerInsuranceEntity> {

    @Autowired
    private CrmCustomerInsuranceDao crmCustomerInsuranceDao;

    @Autowired
    private CrmCustomerInsuranceVal crmCustomerInsuranceVal;


    @Override
    public TgPageInfo queryPageList(CrmCustomerInsuranceEntity entity) {
        EciQuery<CrmCustomerInsuranceEntity> eciQuery = EciQuery.buildQuery(entity);
        List<CrmCustomerInsuranceEntity> entities = crmCustomerInsuranceDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public CrmCustomerInsuranceEntity save(CrmCustomerInsuranceEntity entity) {
        // 返回实体对象
        CrmCustomerInsuranceEntity crmCustomerInsuranceEntity = null;
        crmCustomerInsuranceVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            crmCustomerInsuranceEntity = crmCustomerInsuranceDao.insertOne(entity);

        }else{

            crmCustomerInsuranceEntity = crmCustomerInsuranceDao.updateByEntityId(entity);

        }
        return crmCustomerInsuranceEntity;
    }

    @Override
    public List<CrmCustomerInsuranceEntity> selectList(CrmCustomerInsuranceEntity entity) {
        return crmCustomerInsuranceDao.selectList(entity);
    }

    @Override
    public CrmCustomerInsuranceEntity selectOneById(Serializable id) {
        return crmCustomerInsuranceDao.selectById(id);
    }


    @Override
    public void insertBatch(List<CrmCustomerInsuranceEntity> list) {
        crmCustomerInsuranceDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return crmCustomerInsuranceDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return crmCustomerInsuranceDao.deleteById(id);
    }

}