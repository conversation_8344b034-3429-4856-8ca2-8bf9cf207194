package com.eci.project.fzgjTaskLimitationPt.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eci.common.Zsr;
import com.eci.common.web.BllContext;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjTaskLimitation.dao.FzgjTaskLimitationDao;
import com.eci.project.fzgjTaskLimitation.entity.FzgjTaskLimitationEntity;
import com.eci.project.fzgjTaskLimitationPt.dao.FzgjTaskLimitationPtDao;
import com.eci.project.fzgjTaskLimitationPt.entity.FzgjTaskLimitationPtBaseEntity;
import com.eci.project.fzgjTaskLimitationPt.entity.FzgjTaskLimitationPtEntity;
import com.eci.project.fzgjTaskLimitationPt.service.IFzgjTaskLimitationPtService;
import com.eci.project.fzgjTaskLimitationPt.validate.FzgjTaskLimitationPtVal;

import com.eci.sso.role.entity.UserContext;
import com.eci.wu.core.EntityBase;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.lang.reflect.Array;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 平台级作业环节及参考时效Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-03-28
 */
@Service
@Slf4j
public class FzgjTaskLimitationPtServiceImpl implements IFzgjTaskLimitationPtService {
    @Autowired
    private FzgjTaskLimitationPtDao fzgjTaskLimitationPtDao;

    @Autowired
    private FzgjTaskLimitationDao fzgjTaskLimitationDao;

    @Autowired
    private FzgjTaskLimitationPtVal fzgjTaskLimitationPtVal;


    @Override
    public TgPageInfo queryPageList(FzgjTaskLimitationPtEntity entity) {
        EciQuery<FzgjTaskLimitationPtEntity> eciQuery = EciQuery.doQuery(entity);
        List<FzgjTaskLimitationPtEntity> entities = fzgjTaskLimitationPtDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjTaskLimitationPtEntity save(FzgjTaskLimitationPtEntity entity) {

        if (BllContext.getBusinessType() == BusinessType.INSERT && Zsr.String.IsNullOrWhiteSpace(entity.getGuid())) {
            entity.setCreateDate(new java.util.Date());
            entity.setUpdateDate(new java.util.Date());
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        } else {
            entity.setUpdateDate(new java.util.Date());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        }
        // 返回实体对象
        FzgjTaskLimitationPtEntity fzgjTaskLimitationPtEntity = null;
        fzgjTaskLimitationPtVal.saveValidate(entity, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT && Zsr.String.IsNullOrWhiteSpace(entity.getGuid())) {
            // 查询出所有的，在加上1，就是新的顺序
            List<FzgjTaskLimitationPtEntity> data = fzgjTaskLimitationPtDao.select()
                    .eq(FzgjTaskLimitationPtEntity::getTargetCode, entity.getTargetCode())
                    .list();
            entity.setSeq(data.size() + 1);
            fzgjTaskLimitationPtEntity = fzgjTaskLimitationPtDao.insertOne(entity);

        } else {

            fzgjTaskLimitationPtEntity = fzgjTaskLimitationPtDao.updateByEntityId(entity);

        }
        return fzgjTaskLimitationPtEntity;
    }

    @Override
    public List<FzgjTaskLimitationPtEntity> selectList(FzgjTaskLimitationPtEntity entity) {
        return fzgjTaskLimitationPtDao.selectList(entity);
    }

    @Override
    public FzgjTaskLimitationPtEntity selectOneById(Serializable id) {
        return fzgjTaskLimitationPtDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjTaskLimitationPtEntity> list) {
        fzgjTaskLimitationPtDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjTaskLimitationPtDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjTaskLimitationPtDao.deleteById(id);
    }

    @Override
    public List<Map<String, Object>> getTaskChecked(String targetCode) {
        QueryWrapper<FzgjTaskLimitationPtEntity> queryWrapper_pt = new QueryWrapper<FzgjTaskLimitationPtEntity>();
        queryWrapper_pt.eq("status", "Y");
        queryWrapper_pt.eq("TARGET_CODE", targetCode);
        List<FzgjTaskLimitationPtEntity> taskPtList = fzgjTaskLimitationPtDao.selectList(queryWrapper_pt).stream().sorted(Comparator.comparing(FzgjTaskLimitationPtBaseEntity::getSeq)).collect(Collectors.toList());

        QueryWrapper<FzgjTaskLimitationEntity> queryWrapper = new QueryWrapper<FzgjTaskLimitationEntity>();
        queryWrapper.eq("GROUP_CODE", UserContext.getUserInfo().getCompanyCode());
        queryWrapper.eq("TARGET_CODE", targetCode);
        List<FzgjTaskLimitationEntity> taskList = fzgjTaskLimitationDao.selectList(queryWrapper).stream().sorted(Comparator.comparing(FzgjTaskLimitationEntity::getSeq)).collect(Collectors.toList());


        List<Map<String, Object>> list = new ArrayList<>();
        ArrayList checkedkeys = new ArrayList();
        Map<String, Object> map = null;
        if (taskPtList.size() > 0) {
            for (FzgjTaskLimitationPtEntity taskPt : taskPtList) {
                map = new HashMap<>();
                map.put("id", taskPt.getGuid());
                map.put("label", taskPt.getName());
                map.put("children", null);

                List<FzgjTaskLimitationEntity> codeList = taskList.stream().filter(task -> task.getCode().equals(taskPt.getCode())).collect(Collectors.toList());
                if (codeList.size() > 0) {
                    checkedkeys.add(taskPt.getGuid());
                }
                map.put("taskid", codeList.size() > 0 ? codeList.get(0).getGuid() : null);
                map.put("seq", codeList.size() > 0 ? codeList.get(0).getSeq() : null);
                list.add(map);
            }
        }
        map = new HashMap<>();
        map.put("checkedkeys", checkedkeys);
        list.add(map);
        return list;

    }
}