package com.eci.project.crmCustomerRole.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eci.common.db.DBHelper;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.crmCustomerRole.dao.CrmCustomerRoleDao;
import com.eci.project.crmCustomerRole.entity.CrmCustomerRoleEntity;
import com.eci.project.crmCustomerRole.validate.CrmCustomerRoleVal;

import com.eci.sso.role.entity.UserContext;
import com.eci.wu.core.DataTable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
* 业务伙伴角色Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-29
*/
@Service
@Slf4j
public class CrmCustomerRoleService implements EciBaseService<CrmCustomerRoleEntity> {

    @Autowired
    private CrmCustomerRoleDao crmCustomerRoleDao;

    @Autowired
    private CrmCustomerRoleVal crmCustomerRoleVal;


    @Override
    public TgPageInfo queryPageList(CrmCustomerRoleEntity entity) {
        EciQuery<CrmCustomerRoleEntity> eciQuery = EciQuery.buildQuery(entity);
        List<CrmCustomerRoleEntity> entities = crmCustomerRoleDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public CrmCustomerRoleEntity save(CrmCustomerRoleEntity entity) {
        // 返回实体对象
        CrmCustomerRoleEntity crmCustomerRoleEntity = null;
        crmCustomerRoleVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            crmCustomerRoleEntity = crmCustomerRoleDao.insertOne(entity);

        }else{

            crmCustomerRoleEntity = crmCustomerRoleDao.updateByEntityId(entity);

        }
        return crmCustomerRoleEntity;
    }

    @Override
    public List<CrmCustomerRoleEntity> selectList(CrmCustomerRoleEntity entity) {
        return crmCustomerRoleDao.selectList(entity);
    }

    @Override
    public CrmCustomerRoleEntity selectOneById(Serializable id) {
        return crmCustomerRoleDao.selectById(id);
    }


    @Override
    public void insertBatch(List<CrmCustomerRoleEntity> list) {
        crmCustomerRoleDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return crmCustomerRoleDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return crmCustomerRoleDao.deleteById(id);
    }
    /**
     * <AUTHOR>
     * @Description 根据客户Code删除
     * @Date  2025/5/8 14:01
     * @Param [customerCode]
     * @return int
     **/
    public int deleteByCustomerCode(String customerCode){
        QueryWrapper query=new QueryWrapper();
        query.eq("CUSTOMER_CODE",customerCode);
        return crmCustomerRoleDao.delete(query);
    }
    public List<CrmCustomerRoleEntity> BuildEntity(String roles,String customerCode){
        String sql=String.format("SELECT CODE,NAME FROM FZGJ_BASE_DATA_DETAIL WHERE GROUP_CODE='ROLE' and CODE in (%s)",roles);
        DataTable dt= DBHelper.getDataTable(sql);
        if(dt==null||dt.rows.size()<=0) return null;
        List<CrmCustomerRoleEntity> list=new ArrayList<>();
        dt.rows.forEach(p->{
            CrmCustomerRoleEntity entity=new CrmCustomerRoleEntity();
            entity.setCustomerCode(customerCode);
            entity.setCode(p.getString("CODE"));
            entity.setName(p.getString("NAME"));
            entity.setStatus("Y");
            AddUserInfo(entity,BusinessType.INSERT);
            list.add(entity);
        });
        return list;
    }

    private void AddUserInfo(CrmCustomerRoleEntity entity,BusinessType businessType){
        entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        entity.setUpdateDate(new Date());
        entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
        if(businessType==BusinessType.INSERT){
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateDate(new Date());
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
            entity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            entity.setCompanyName(UserContext.getUserInfo().getCompanyName());
        }
    }
}