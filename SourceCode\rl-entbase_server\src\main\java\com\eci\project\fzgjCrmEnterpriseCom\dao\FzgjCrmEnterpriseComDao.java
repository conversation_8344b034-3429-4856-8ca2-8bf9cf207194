package com.eci.project.fzgjCrmEnterpriseCom.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.fzgjCrmEnterpriseCom.entity.FzgjCrmEnterpriseComEntity;


/**
* 平台受理企业Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-03-13
*/
public interface FzgjCrmEnterpriseComDao extends EciBaseDao<FzgjCrmEnterpriseComEntity> {

}