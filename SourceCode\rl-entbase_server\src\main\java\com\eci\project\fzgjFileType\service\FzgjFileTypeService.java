package com.eci.project.fzgjFileType.service;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.Zsr;
import com.eci.common.ZsrJson;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjBdFileBiz.entity.FzgjBdFileBizEntity;
import com.eci.project.fzgjBdFileBiz.service.FzgjBdFileBizService;
import com.eci.project.fzgjCrmEnterpriseSys.entity.FzgjCrmEnterpriseSysEntity;
import com.eci.project.fzgjFileType.dao.FzgjFileTypeDao;
import com.eci.project.fzgjFileType.entity.FzgjFileTypeEntity;
import com.eci.project.fzgjFileType.validate.FzgjFileTypeVal;

import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.List;


/**
 * 附件类型Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-04-11
 */
@Service
@Slf4j
public class FzgjFileTypeService implements EciBaseService<FzgjFileTypeEntity> {

    @Autowired
    private FzgjFileTypeDao fzgjFileTypeDao;

    @Autowired
    private FzgjFileTypeVal fzgjFileTypeVal;


    @Autowired
    private FzgjBdFileBizService fzgjBdFileBizService;


    @Override
    public TgPageInfo queryPageList(FzgjFileTypeEntity entity) {
        EciQuery<FzgjFileTypeEntity> eciQuery = EciQuery.buildQuery(entity);
        eciQuery.eq(FzgjFileTypeEntity::getGroupCode, UserContext.getUserInfo().getCompanyCode());
        eciQuery.orderBy(false, FzgjFileTypeEntity::getCreateDate);
        List<FzgjFileTypeEntity> entities = fzgjFileTypeDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjFileTypeEntity save(FzgjFileTypeEntity entity) {
        // 返回实体对象
        FzgjFileTypeEntity fzgjFileTypeEntity = null;
        fzgjFileTypeVal.saveValidate(entity, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjFileTypeEntity = fzgjFileTypeDao.insertOne(entity);

        } else {

            fzgjFileTypeEntity = fzgjFileTypeDao.updateByEntityId(entity);

        }
        return fzgjFileTypeEntity;
    }


    @Transactional(rollbackFor = Exception.class)
    public FzgjFileTypeEntity save(String jsonString) {
        ZsrJson zsrJson = ZsrJson.parse(jsonString);
        FzgjFileTypeEntity entity = zsrJson.toObject(FzgjFileTypeEntity.class);

        List<FzgjCrmEnterpriseSysEntity> viewLimitTypeList = zsrJson.getList("viewLimitType", FzgjCrmEnterpriseSysEntity.class);
        List<FzgjCrmEnterpriseSysEntity> uploadLimitTypeList = zsrJson.getList("uploadLimitType", FzgjCrmEnterpriseSysEntity.class);

        // 返回实体对象
        FzgjFileTypeEntity fzgjFileTypeEntity = null;
        fzgjFileTypeVal.saveValidate(entity, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT || Zsr.String.IsNullOrWhiteSpace(entity.getGuid())) {
            entity.setCreateDate(new java.util.Date());
            entity.setUpdateDate(new java.util.Date());
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
        } else {
            entity.setUpdateDate(new java.util.Date());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
        }

        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setGuid(IdWorker.get32UUID());
            fzgjFileTypeEntity = fzgjFileTypeDao.insertOne(entity);

        } else {

            fzgjFileTypeEntity = fzgjFileTypeDao.updateByEntityId(entity);

        }

        addFileTypeBizData(viewLimitTypeList, uploadLimitTypeList, fzgjFileTypeEntity);

        return fzgjFileTypeEntity;
    }

    /**
     * 增加文件类型业务数据
     * @param viewLimitTypeList
     * @param uploadLimitTypeList
     * @param fzgjFileTypeEntity
     */
    @Transactional(rollbackFor = Exception.class)
    private void addFileTypeBizData(List<FzgjCrmEnterpriseSysEntity> viewLimitTypeList, List<FzgjCrmEnterpriseSysEntity> uploadLimitTypeList, FzgjFileTypeEntity fzgjFileTypeEntity) {
        final String code = fzgjFileTypeEntity.getCode();
        // 插入之前，先删除
        fzgjBdFileBizService.beforeSaveCleanData(fzgjFileTypeEntity);
        viewLimitTypeList.forEach(child -> {
            FzgjBdFileBizEntity bdFileBizAdd = new FzgjBdFileBizEntity();
            bdFileBizAdd.setGuid(IdWorker.get32UUID());
            bdFileBizAdd.setStatus("Y");
            bdFileBizAdd.setTargetCode(code);// TARGET_CODE = fzgjFileTypeEntity.getCode();
            bdFileBizAdd.setAuthorityType("VIEW");
            bdFileBizAdd.setTargetSystemCode(child.getSysCode());
            bdFileBizAdd.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            bdFileBizAdd.setGroupName(UserContext.getUserInfo().getCompanyName());
            // 调用插入数据方法
            fzgjBdFileBizService.saveByCustom(bdFileBizAdd);
        });
        uploadLimitTypeList.forEach(child -> {
            FzgjBdFileBizEntity bdFileBizAdd = new FzgjBdFileBizEntity();
            bdFileBizAdd.setGuid(IdWorker.get32UUID());
            bdFileBizAdd.setStatus("Y");
            bdFileBizAdd.setTargetCode(code);// TARGET_CODE = fzgjFileTypeEntity.getCode();
            bdFileBizAdd.setAuthorityType("UPLOAD");
            bdFileBizAdd.setTargetSystemCode(child.getSysCode());
            bdFileBizAdd.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            bdFileBizAdd.setGroupName(UserContext.getUserInfo().getCompanyName());
            // 调用插入数据方法
            fzgjBdFileBizService.saveByCustom(bdFileBizAdd);
        });
    }

    @Override
    public List<FzgjFileTypeEntity> selectList(FzgjFileTypeEntity entity) {
        return fzgjFileTypeDao.selectList(entity);
    }


    /**
     * 获取当前登录公司的所有附件文件类型
     * @return
     */
    public List<FzgjFileTypeEntity> getFileTypeList() {
        return fzgjFileTypeDao.select()
                .eq(FzgjFileTypeEntity::getGroupCode, UserContext.getUserInfo().getCompanyCode())
                .list();
    }

    @Override
    public FzgjFileTypeEntity selectOneById(Serializable id) {
        return fzgjFileTypeDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjFileTypeEntity> list) {
        fzgjFileTypeDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjFileTypeDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjFileTypeDao.deleteById(id);
    }

}