package com.eci.project.omsISlResult.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.project.omsISlResult.service.OmsISlResultService;
import com.eci.project.omsISlResult.entity.OmsISlResultEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.EciLog;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


/**
* 受理结果及单据编号Controller
*
* @<NAME_EMAIL>
* @date 2025-04-21
*/
@Api(tags = "受理结果及单据编号")
@RestController
@RequestMapping("/omsISlResult")
public class OmsISlResultController extends EciBaseController {

    @Autowired
    private OmsISlResultService omsISlResultService;


    @ApiOperation("受理结果及单据编号:保存")
    @EciLog(title = "受理结果及单据编号:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    //@EciAction() //框架core,sso,component 需升级到2.2.5
    public ResponseMsg save(@RequestBody OmsISlResultEntity entity){
        return ResponseMsgUtil.success(10001,omsISlResultService.save(entity));
    }


    @ApiOperation("受理结果及单据编号:查询列表")
    @EciLog(title = "受理结果及单据编号:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    //@EciAction() //框架core,sso,component 需升级到2.2.5
    public ResponseMsg selectList(@RequestBody OmsISlResultEntity entity){
        return ResponseMsgUtil.success(10001,omsISlResultService.selectList(entity));
    }


    @ApiOperation("受理结果及单据编号:分页查询列表")
    @EciLog(title = "受理结果及单据编号:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    //@EciAction() //框架core,sso,component 需升级到2.2.5
    public ResponseMsg selectPageList(@RequestBody OmsISlResultEntity entity){
        return ResponseMsgUtil.success(10001,omsISlResultService.queryPageList(entity));
    }


    @ApiOperation("受理结果及单据编号:根据ID查一条")
    @EciLog(title = "受理结果及单据编号:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    //@EciAction() //框架core,sso,component 需升级到2.2.5
    public ResponseMsg selectOneById(@RequestBody OmsISlResultEntity entity){
        return ResponseMsgUtil.success(10001,omsISlResultService.selectOneById(entity.getGuid()));
    }


    @ApiOperation("受理结果及单据编号:根据ID删除一条")
    @EciLog(title = "受理结果及单据编号:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    //@EciAction() //框架core,sso,component 需升级到2.2.5
    public ResponseMsg deleteById(@RequestBody OmsISlResultEntity entity){
        return ResponseMsgUtil.success(10001,omsISlResultService.deleteById(entity.getGuid()));
    }


    @ApiOperation("受理结果及单据编号:根据ID字符串删除多条")
    @EciLog(title = "受理结果及单据编号:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    //@EciAction() //框架core,sso,component 需升级到2.2.5
    public ResponseMsg deleteByIds(@RequestBody OmsISlResultEntity entity) {
        return ResponseMsgUtil.success(10001, omsISlResultService.deleteByIds(entity.getIds()));
    }


}