package com.eci.project.fzgjCrmEnterprise.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.eci.common.db.DBHelper;
import com.eci.project.fzgjCrmEnterprise.entity.FzgjCrmEnterpriseEntityDto;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.fzgjCrmEnterprise.entity.FzgjCrmEnterpriseEntity;

import java.util.List;


/**
* 注册企业Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-03-13
*/
public interface FzgjCrmEnterpriseDao extends EciBaseDao<FzgjCrmEnterpriseEntity> {


    /**
     * 查询又表数据-作业系统
     * @param queryWrapper
     * @return
     */
    List<FzgjCrmEnterpriseEntityDto> selectRightTable(@Param(Constants.WRAPPER) Wrapper queryWrapper);
}