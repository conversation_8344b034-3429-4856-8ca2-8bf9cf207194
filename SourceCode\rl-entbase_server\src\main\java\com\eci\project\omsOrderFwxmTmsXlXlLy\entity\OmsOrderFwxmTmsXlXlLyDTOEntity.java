package com.eci.project.omsOrderFwxmTmsXlXlLy.entity;


import com.eci.common.DictField;

public class OmsOrderFwxmTmsXlXlLyDTOEntity extends OmsOrderFwxmTmsXlXlLyEntity{
    private String isWb;
    @DictField(queryKey = "OMS_SSO_NODE")
    private String nodeCodeNb;
    @DictField(queryKey = "CRM_CUSTOMER_GYS")
    private String gysCode;
    private String zpNum;
    private String zpSeqNo;
    private String zhysxlNo;

    public String getIsWb() {
        return isWb;
    }

    public void setIsWb(String isWb) {
        this.isWb = isWb;
    }

    public String getNodeCodeNb() {
        return nodeCodeNb;
    }

    public void setNodeCodeNb(String nodeCodeNb) {
        this.nodeCodeNb = nodeCodeNb;
    }

    public String getGysCode() {
        return gysCode;
    }

    public void setGysCode(String gysCode) {
        this.gysCode = gysCode;
    }
    public String getZpNum() {
        return zpNum;
    }
    public void setZpNum(String zpNum) {
        this.zpNum = zpNum;
    }
    public String getZpSeqNo() {
        return zpSeqNo;
    }
    public void setZpSeqNo(String zpSeqNo) {
        this.zpSeqNo = zpSeqNo;
    }

    public String getZhysxlNo() {
        return zhysxlNo;
    }
    public void setZhysxlNo(String zhysxlNo) {
        this.zhysxlNo = zhysxlNo;
    }

    public String xldata;
    public String cldata;

    public String getXldata() {
        return xldata;
    }

    public void setXldata(String xldata) {
        this.xldata = xldata;
    }

    public String getCldata() {
        return cldata;
    }

    public void setCldata(String cldata) {
        this.cldata = cldata;
    }
}
