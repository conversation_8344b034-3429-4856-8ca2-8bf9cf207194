package com.eci.project.crmCustomer.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.crmCustomer.entity.CrmCustomerEntity;

import java.util.List;


/**
* 业务伙伴Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-04-29
*/
public interface CrmCustomerDao extends EciBaseDao<CrmCustomerEntity> {
    public List<CrmCustomerEntity> selectList1(@Param(Constants.WRAPPER) Wrapper queryWrapper
            , @Param("groupcode") String groupcode
            , @Param("companycode") String companycode);
}