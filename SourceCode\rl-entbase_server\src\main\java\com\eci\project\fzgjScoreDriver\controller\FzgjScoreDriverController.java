package com.eci.project.fzgjScoreDriver.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjScoreDriver.service.FzgjScoreDriverService;
import com.eci.project.fzgjScoreDriver.entity.FzgjScoreDriverEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 企业评分Controller
*
* @<NAME_EMAIL>
* @date 2025-06-16
*/
@Api(tags = "企业评分")
@RestController
@RequestMapping("/fzgjScoreDriver")
public class FzgjScoreDriverController extends EciBaseController {

    @Autowired
    private FzgjScoreDriverService fzgjScoreDriverService;


    @ApiOperation("企业评分:保存")
    @EciLog(title = "企业评分:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody FzgjScoreDriverEntity entity){
        FzgjScoreDriverEntity fzgjScoreDriverEntity =fzgjScoreDriverService.save(entity);
        return ResponseMsgUtil.success(10001,fzgjScoreDriverEntity);
    }


    @ApiOperation("企业评分:查询列表")
    @EciLog(title = "企业评分:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjScoreDriverEntity entity){
        List<FzgjScoreDriverEntity> fzgjScoreDriverEntities = fzgjScoreDriverService.selectList(entity);
        return ResponseMsgUtil.success(10001,fzgjScoreDriverEntities);
    }


    @ApiOperation("企业评分:分页查询列表")
    @EciLog(title = "企业评分:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjScoreDriverEntity entity){
        TgPageInfo tgPageInfo = fzgjScoreDriverService.queryPageList(entity);
        return ResponseMsgUtil.successPlus(10001,tgPageInfo);
    }


    @ApiOperation("企业评分:根据ID查一条")
    @EciLog(title = "企业评分:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjScoreDriverEntity entity){
        FzgjScoreDriverEntity  fzgjScoreDriverEntity = fzgjScoreDriverService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,fzgjScoreDriverEntity);
    }


    @ApiOperation("企业评分:根据ID删除一条")
    @EciLog(title = "企业评分:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjScoreDriverEntity entity){
        int count = fzgjScoreDriverService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("企业评分:根据ID字符串删除多条")
    @EciLog(title = "企业评分:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjScoreDriverEntity entity) {
        int count = fzgjScoreDriverService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}