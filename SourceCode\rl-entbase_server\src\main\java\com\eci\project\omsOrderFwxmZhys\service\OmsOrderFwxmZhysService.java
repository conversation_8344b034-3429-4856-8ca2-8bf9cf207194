package com.eci.project.omsOrderFwxmZhys.service;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.DictFieldUtils;
import com.eci.common.ZsrBaseEntity;
import com.eci.common.ZsrJson;
import com.eci.common.db.DBHelper;
import com.eci.common.util.DateUtils;
import com.eci.common.util.StringUtils;
import com.eci.common.utils.CommonLib;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.exception.BaseException;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.omsOrderFwxmWorkTrace.service.OmsOrderFwxmWorkTraceZsrService;
import com.eci.project.omsOrderFwxmZhys.dao.OmsOrderFwxmZhysDao;
import com.eci.project.omsOrderFwxmZhys.entity.OmsOrderFwxmZhysEntity;
import com.eci.project.omsOrderFwxmZhys.entity.ResOmsOrderFwxmZhysEntity;
import com.eci.project.omsOrderFwxmZhys.validate.OmsOrderFwxmZhysVal;
import com.eci.project.omsOrderFwxmZhysXl.dao.OmsOrderFwxmZhysXlDao;
import com.eci.project.omsOrderFwxmZhysXl.entity.OmsOrderFwxmZhysXlEntity;
import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * 服务项目-综合运输Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-06-13
 */
@Service
@Slf4j
public class OmsOrderFwxmZhysService implements EciBaseService<OmsOrderFwxmZhysEntity> {

    CommonLib cmn = CommonLib.getInstance();

    @Autowired
    private OmsOrderFwxmZhysDao omsOrderFwxmZhysDao;

    @Autowired
    private OmsOrderFwxmZhysVal omsOrderFwxmZhysVal;

    @Autowired
    private OmsOrderFwxmZhysXlDao omsOrderFwxmZhysXlDao;

    @Autowired
    private OmsOrderFwxmWorkTraceZsrService omsOrderFwxmWorkTraceZsrService;

    @Override
    public TgPageInfo queryPageList(OmsOrderFwxmZhysEntity entity) {
        EciQuery<OmsOrderFwxmZhysEntity> eciQuery = EciQuery.buildQuery(entity);
        List<OmsOrderFwxmZhysEntity> entities = omsOrderFwxmZhysDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderFwxmZhysEntity save(OmsOrderFwxmZhysEntity entity) {
        // 返回实体对象
        OmsOrderFwxmZhysEntity omsOrderFwxmZhysEntity = null;
        omsOrderFwxmZhysVal.saveValidate(entity, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            omsOrderFwxmZhysEntity = omsOrderFwxmZhysDao.insertOne(entity);

        } else {

            omsOrderFwxmZhysEntity = omsOrderFwxmZhysDao.updateByEntityId(entity);

        }
        return omsOrderFwxmZhysEntity;
    }

    @Override
    public List<OmsOrderFwxmZhysEntity> selectList(OmsOrderFwxmZhysEntity entity) {
        return omsOrderFwxmZhysDao.selectList(entity);
    }

    @Override
    public OmsOrderFwxmZhysEntity selectOneById(Serializable id) {
        return omsOrderFwxmZhysDao.selectById(id);
    }


    @Override
    public void insertBatch(List<OmsOrderFwxmZhysEntity> list) {
        omsOrderFwxmZhysDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return omsOrderFwxmZhysDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return omsOrderFwxmZhysDao.deleteById(id);
    }


    /***
     * 综合运输加载
     * */
    public ZsrBaseEntity loadOmsOrderZHYS(OmsOrderFwxmZhysEntity entity) {

        ZsrBaseEntity responseEntity = new ZsrBaseEntity();

        // 综合运输信息
        List<OmsOrderFwxmZhysEntity> zhysList = new ArrayList<>();

        if (StringUtils.hasValue(entity.getOrderNo())) {
            zhysList = omsOrderFwxmZhysDao.select()
                    .eq(OmsOrderFwxmZhysEntity::getGroupCode, UserContext.getUserInfo().getCompanyCode())
                    .eq(OmsOrderFwxmZhysEntity::getOrderNo, entity.getOrderNo())
                    .list();
        }
        if (StringUtils.hasValue(entity.getPreNo())) {
            zhysList = omsOrderFwxmZhysDao.select()
                    .eq(OmsOrderFwxmZhysEntity::getGroupCode, UserContext.getUserInfo().getCompanyCode())
                    .eq(OmsOrderFwxmZhysEntity::getPreNo, entity.getPreNo())
                    .list();
        }

        String zhysGuid = null;// 主表id
        if (zhysList.size() > 0) {
            OmsOrderFwxmZhysEntity zhysEntity = zhysList.get(0);
            DictFieldUtils.handleDictFields(zhysEntity);
            responseEntity.push("entity", zhysEntity);
            zhysGuid = zhysEntity.getZhysNo();
        }

        // 线路信息
        if (StringUtils.hasValue(zhysGuid)) {
            List<OmsOrderFwxmZhysXlEntity> xlList = omsOrderFwxmZhysXlDao.select()
                    .eq(OmsOrderFwxmZhysXlEntity::getZhysGuid, zhysGuid)
                    .orderByAsc("LINE_SEQ")
                    .list();

            DictFieldUtils.handleDictFields(xlList);
            responseEntity.push("list", xlList);
        }

        return responseEntity;
    }

    /**
     * 综合运输保存
     */
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderFwxmZhysEntity saveOmsOrderZHYS(String jsonString) {
        // 返回实体
        OmsOrderFwxmZhysEntity omsOrderFwxmZhysEntity = null;

        // 解析
        ZsrJson jsonStr = ZsrJson.parse(jsonString);
        OmsOrderFwxmZhysEntity entity = jsonStr.check("entity").getObject("entity", OmsOrderFwxmZhysEntity.class);
        List<OmsOrderFwxmZhysXlEntity> xlList = jsonStr.check("list").getList("list", OmsOrderFwxmZhysXlEntity.class);

        // 验证
        omsOrderFwxmZhysVal.saveOmsOrderZHYS(entity, xlList);

        // 查询
        List<OmsOrderFwxmZhysEntity> oldList = new ArrayList<>();
        if (StringUtils.hasValue(entity.getOrderNo())) {
            oldList = omsOrderFwxmZhysDao.select()
                    .eq(OmsOrderFwxmZhysEntity::getGroupCode, UserContext.getUserInfo().getCompanyCode())
                    .eq(OmsOrderFwxmZhysEntity::getOrderNo, entity.getOrderNo())
                    .list();
        }
        if (StringUtils.hasValue(entity.getPreNo())) {
            oldList = omsOrderFwxmZhysDao.select()
                    .eq(OmsOrderFwxmZhysEntity::getGroupCode, UserContext.getUserInfo().getCompanyCode())
                    .eq(OmsOrderFwxmZhysEntity::getPreNo, entity.getPreNo())
                    .list();
        }

        boolean isAdd = true;
        String zhysGuid = null;
        OmsOrderFwxmZhysEntity updateEntity = new OmsOrderFwxmZhysEntity();
        if (oldList.size() > 0) {
            updateEntity = oldList.get(0);
            zhysGuid = updateEntity.getZhysNo();
            isAdd = false;
        }

        entity.setZzfwMydl(StringUtils.hasValue(entity.getZzfwMydl()) && entity.getZzfwMydl().equals("true") ? "Y" : "N");
        entity.setZzfwZjdb(StringUtils.hasValue(entity.getZzfwZjdb()) && entity.getZzfwZjdb().equals("true") ? "Y" : "N");
        entity.setZzfwBxfw(StringUtils.hasValue(entity.getZzfwBxfw()) && entity.getZzfwBxfw().equals("true") ? "Y" : "N");

        if (isAdd) {
            zhysGuid = IdWorker.get32UUID();
            entity.setZhysNo(zhysGuid);
            entity.setCreateDate(DateUtils.getNowDate());
            entity.setCreateUser(UserContext.getUserInfo().getUserId());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setUpdateDate(DateUtils.getNowDate());
            entity.setUpdateUser(UserContext.getUserInfo().getUserId());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            entity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            entity.setCompanyName(UserContext.getUserInfo().getCompanyName());
            entity.setNodeCode(UserContext.getUserInfo().getDeptCode());
            entity.setNodeName(UserContext.getUserInfo().getDeptName());
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
            omsOrderFwxmZhysEntity = omsOrderFwxmZhysDao.insertOne(entity);
        } else {
            // 修改综合运输信息
            BeanUtils.copyProperties(entity, updateEntity);
            updateEntity.setUpdateDate(DateUtils.getNowDate());
            updateEntity.setUpdateUser(UserContext.getUserInfo().getUserId());
            updateEntity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            omsOrderFwxmZhysEntity = omsOrderFwxmZhysDao.updateByEntityId(updateEntity);
        }

        // 线路信息
        // 1-删除
        if (xlList.size() <= 0) {
            if (StringUtils.hasValue(updateEntity.getZhysNo())) {
                String delSql = "DELETE FROM OMS_ORDER_FWXM_ZHYS_XL WHERE ZHYS_GUID=" + cmn.SQLQ(updateEntity.getZhysNo());
                DBHelper.execute(delSql);
            }
        } else {
            // 2-新增
            for (OmsOrderFwxmZhysXlEntity item : xlList) {
                OmsOrderFwxmZhysXlEntity omsOrderFwxmZhysXlEntity = new OmsOrderFwxmZhysXlEntity();
                BeanUtils.copyProperties(item, omsOrderFwxmZhysXlEntity);
                omsOrderFwxmZhysXlEntity.setZhysGuid(zhysGuid);
                omsOrderFwxmZhysXlEntity.setOrderNo(entity.getOrderNo());
                omsOrderFwxmZhysXlEntity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
                omsOrderFwxmZhysXlEntity.setCompanyName(UserContext.getUserInfo().getCompanyName());
                omsOrderFwxmZhysXlEntity.setNodeCode(UserContext.getUserInfo().getDeptCode());
                omsOrderFwxmZhysXlEntity.setNodeName(UserContext.getUserInfo().getDeptName());
                omsOrderFwxmZhysXlEntity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
                omsOrderFwxmZhysXlEntity.setGroupName(UserContext.getUserInfo().getCompanyName());

                if (StringUtils.isEmpty(omsOrderFwxmZhysXlEntity.getDcysxl())) {
                    throw new BaseException("单程运输线路为空");
                }

                // 有唯一序号时，做修改；没有唯一序号时做新增
                if (StringUtils.hasValue(omsOrderFwxmZhysXlEntity.getLineNo())) {
                    omsOrderFwxmZhysXlEntity.setUpdateDate(DateUtils.getNowDate());
                    omsOrderFwxmZhysXlEntity.setUpdateUser(UserContext.getUserInfo().getUserId());
                    omsOrderFwxmZhysXlEntity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
                    omsOrderFwxmZhysXlDao.updateByEntityId(omsOrderFwxmZhysXlEntity);
                } else {
                    omsOrderFwxmZhysXlEntity.setLineNo(IdWorker.get32UUID());
                    omsOrderFwxmZhysXlEntity.setCreateDate(DateUtils.getNowDate());
                    omsOrderFwxmZhysXlEntity.setCreateUser(UserContext.getUserInfo().getUserId());
                    omsOrderFwxmZhysXlEntity.setCreateUserName(UserContext.getUserInfo().getTrueName());
                    omsOrderFwxmZhysXlDao.insertOne(omsOrderFwxmZhysXlEntity);
                }
            }
        }

        // 协作任务
        if (StringUtils.hasValue(entity.getOrderNo())) {
            omsOrderFwxmWorkTraceZsrService.WorkTraceAllSave(entity.getOrderNo());
        } else if (StringUtils.hasValue(entity.getPreNo())) {
            omsOrderFwxmWorkTraceZsrService.WorkTraceAllSaveZiZhuXiaDan(entity.getPreNo());
        }

        return omsOrderFwxmZhysEntity;
    }


}