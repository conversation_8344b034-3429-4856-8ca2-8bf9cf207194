<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.omsOrderFwxmWorkFkZzfw.dao.OmsOrderFwxmWorkFkZzfwDao">
    <resultMap type="OmsOrderFwxmWorkFkZzfwEntity" id="OmsOrderFwxmWorkFkZzfwResult">
        <result property="guid" column="GUID"/>
        <result property="orderNo" column="ORDER_NO"/>
        <result property="xzwtNo" column="XZWT_NO"/>
        <result property="workNo" column="WORK_NO"/>
        <result property="fwxmCode" column="FWXM_CODE"/>
        <result property="opItem" column="OP_ITEM"/>
        <result property="opProperty" column="OP_PROPERTY"/>
        <result property="unit" column="UNIT"/>
        <result property="opQtyReal" column="OP_QTY_REAL"/>
        <result property="memo" column="MEMO"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="bizRegId" column="BIZ_REG_ID"/>
        <result property="jjNoRck" column="JJ_NO_RCK"/>
        <result property="lineNum" column="LINE_NUM"/>
    </resultMap>

    <sql id="selectOmsOrderFwxmWorkFkZzfwEntityVo">
        select
            GUID,
            ORDER_NO,
            XZWT_NO,
            WORK_NO,
            FWXM_CODE,
            OP_ITEM,
            OP_PROPERTY,
            UNIT,
            OP_QTY_REAL,
            MEMO,
            CREATE_USER,
            CREATE_USER_NAME,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_USER_NAME,
            UPDATE_DATE,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            BIZ_REG_ID,
            JJ_NO_RCK,
            LINE_NUM
        from OMS_ORDER_FWXM_WORK_FK_ZZFW
    </sql>
</mapper>