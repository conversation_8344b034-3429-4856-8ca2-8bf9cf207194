<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.dhlResponseRecord.dao.DhlResponseRecordDao">
    <resultMap type="DhlResponseRecordEntity" id="DhlResponseRecordResult">
        <result property="guid" column="GUID"/>
        <result property="state" column="STATE"/>
        <result property="msg" column="MSG"/>
        <result property="requestGuid" column="REQUEST_GUID"/>
        <result property="isSucessSend" column="IS_SUCESS_SEND"/>
        <result property="isSucessReceive" column="IS_SUCESS_RECEIVE"/>
        <result property="fsfs" column="FSFS"/>
        <result property="opDate" column="OP_DATE"/>
        <result property="memo" column="MEMO"/>
        <result property="sendNum" column="SEND_NUM"/>
        <result property="sendFlag" column="SEND_FLAG"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
    </resultMap>

    <sql id="selectDhlResponseRecordEntityVo">
        select
            GUID,
            STATE,
            MSG,
            REQUEST_GUID,
            IS_SUCESS_SEND,
            IS_SUCESS_RECEIVE,
            FSFS,
            OP_DATE,
            MEMO,
            SEND_NUM,
            SEND_FLAG,
            CREATE_USER,
            CREATE_USER_NAME,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_USER_NAME,
            UPDATE_DATE,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME
        from DHL_RESPONSE_RECORD
    </sql>
</mapper>