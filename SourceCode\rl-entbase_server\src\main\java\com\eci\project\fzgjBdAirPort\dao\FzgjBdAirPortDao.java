package com.eci.project.fzgjBdAirPort.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.fzgjBdAirPort.entity.FzgjBdAirPortEntity;


/**
* 空运港口Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-03-14
*/
public interface FzgjBdAirPortDao extends EciBaseDao<FzgjBdAirPortEntity> {

}