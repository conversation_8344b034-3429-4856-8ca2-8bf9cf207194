package com.eci.project.etmsBdTruckLimitedQz.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.db.DBHelper;
import com.eci.common.enums.Enums;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.etmsBdTruckEjwhQz.entity.EtmsBdTruckEjwhQzDTOEntity;
import com.eci.project.etmsBdTruckEjwhQz.entity.EtmsBdTruckEjwhQzEntity;
import com.eci.project.etmsBdTruckLimitedQz.dao.EtmsBdTruckLimitedQzDao;
import com.eci.project.etmsBdTruckLimitedQz.entity.EtmsBdTruckLimitedQzDTOEntity;
import com.eci.project.etmsBdTruckLimitedQz.entity.EtmsBdTruckLimitedQzEntity;
import com.eci.project.etmsBdTruckLimitedQz.validate.EtmsBdTruckLimitedQzVal;

import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.Arrays;
import java.util.List;


/**
* 车辆年审历史Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-16
*/
@Service
@Slf4j
public class EtmsBdTruckLimitedQzService implements EciBaseService<EtmsBdTruckLimitedQzEntity> {

    @Autowired
    private EtmsBdTruckLimitedQzDao etmsBdTruckLimitedQzDao;

    @Autowired
    private EtmsBdTruckLimitedQzVal etmsBdTruckLimitedQzVal;


    @Override
    public TgPageInfo queryPageList(EtmsBdTruckLimitedQzEntity entity) {
        startPage();
        String sql="select BD.CHECK_STATUS,B.GUID, B.LIMITED_DATE,B.NEXT_DATE from ETMS_BD_TRUCK_QZ BD\n" +
                "            RIGHT join ETMS_BD_TRUCK_LIMITED_QZ B on B.TRUCK_GUID = BD.guid where B.TRUCK_GUID=?";
        List<EtmsBdTruckLimitedQzDTOEntity> pageInfo = DBHelper.selectList(sql, EtmsBdTruckLimitedQzDTOEntity.class, entity.getTruckGuid());
        return EciQuery.getPageInfo(pageInfo);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public EtmsBdTruckLimitedQzEntity save(EtmsBdTruckLimitedQzEntity entity) {
        // 返回实体对象
        EtmsBdTruckLimitedQzEntity etmsBdTruckLimitedQzEntity = null;
        etmsBdTruckLimitedQzVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setGuid(IdWorker.get32UUID());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
            entity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            entity.setCompanyName(UserContext.getUserInfo().getCompanyName());
            entity.setNodeCode(UserContext.getUserInfo().getDeptCode());
            entity.setNodeName(UserContext.getUserInfo().getDeptName());
            entity.setModMark(Enums.ModMark.XZ.getValue());
            etmsBdTruckLimitedQzEntity = etmsBdTruckLimitedQzDao.insertOne(entity);

        }else{
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            entity.setModMark(Enums.ModMark.XG.getValue());
            etmsBdTruckLimitedQzEntity = etmsBdTruckLimitedQzDao.updateByEntityId(entity);

        }
        return etmsBdTruckLimitedQzEntity;
    }

    @Override
    public List<EtmsBdTruckLimitedQzEntity> selectList(EtmsBdTruckLimitedQzEntity entity) {
        return etmsBdTruckLimitedQzDao.selectList(entity);
    }

    @Override
    public EtmsBdTruckLimitedQzEntity selectOneById(Serializable id) {
        return etmsBdTruckLimitedQzDao.selectById(id);
    }


    @Override
    public void insertBatch(List<EtmsBdTruckLimitedQzEntity> list) {
        etmsBdTruckLimitedQzDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return etmsBdTruckLimitedQzDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return etmsBdTruckLimitedQzDao.deleteById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public int deleteByList(String ids) {
        List<String> list= Arrays.asList(ids.split(","));
        int count=0;
        for(String str:list){
            QueryWrapper<EtmsBdTruckLimitedQzEntity> queryWrapper=new QueryWrapper<>();
            queryWrapper.eq("GUID",str);
            count+=etmsBdTruckLimitedQzDao.delete(queryWrapper);
        }
        return count;
    }
}