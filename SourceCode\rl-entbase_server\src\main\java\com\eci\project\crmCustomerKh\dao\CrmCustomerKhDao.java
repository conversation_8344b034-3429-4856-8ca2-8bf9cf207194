package com.eci.project.crmCustomerKh.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.eci.wu.core.DataTable;
import com.eci.wu.core.EntityBase;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.crmCustomerKh.entity.CrmCustomerKhEntity;
import org.apache.ibatis.annotations.Select;

import java.util.List;


/**
* 业务伙伴-客户信息Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-04-29
*/
public interface CrmCustomerKhDao extends EciBaseDao<CrmCustomerKhEntity> {
    @Select("SELECT INV_TYPE.CODE as \"CODE\" ,INV_TYPE.NAME AS \"NAME\",INV_TAX.TAX AS \"TAX\" FROM FZGJ_BD_INVOICE_TYPE   INV_TYPE,\n" +
            "FZGJ_BD_INVOICE_TAX   INV_TAX \n" +
            "WHERE INV_TYPE.GUID=INV_TAX.FGUID AND INV_TYPE.STATUS='Y' AND INV_TAX.AR_AP = 'AR' ${where}")
    public List<EntityBase> selectTax(@Param("where") String where);
}