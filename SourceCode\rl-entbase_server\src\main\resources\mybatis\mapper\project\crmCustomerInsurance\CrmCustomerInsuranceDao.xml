<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.crmCustomerInsurance.dao.CrmCustomerInsuranceDao">
    <resultMap type="CrmCustomerInsuranceEntity" id="CrmCustomerInsuranceResult">
        <result property="guid" column="GUID"/>
        <result property="customerGuid" column="CUSTOMER_GUID"/>
        <result property="policyNo" column="POLICY_NO"/>
        <result property="insurer" column="INSURER"/>
        <result property="insuranceType" column="INSURANCE_TYPE"/>
        <result property="startDate" column="START_DATE"/>
        <result property="endDate" column="END_DATE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="status" column="STATUS"/>
        <result property="amount" column="AMOUNT"/>
        <result property="insuranceTerm" column="INSURANCE_TERM"/>
        <result property="memo" column="MEMO"/>
        <result property="policyholder" column="POLICYHOLDER"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>

    <sql id="selectCrmCustomerInsuranceEntityVo">
        select
            GUID,
            CUSTOMER_GUID,
            POLICY_NO,
            INSURER,
            INSURANCE_TYPE,
            START_DATE,
            END_DATE,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            CREATE_USER_NAME,
            UPDATE_USER_NAME,
            STATUS,
            AMOUNT,
            INSURANCE_TERM,
            MEMO,
            POLICYHOLDER,
            CREATE_USER,
            UPDATE_USER,
            CREATE_DATE,
            UPDATE_DATE
        from CRM_CUSTOMER_INSURANCE
    </sql>
</mapper>