<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.fzgjBdProductService.dao.FzgjBdProductServiceDao">
    <resultMap type="FzgjBdProductServiceEntity" id="FzgjBdProductServiceResult">
        <result property="guid" column="GUID"/>
        <result property="productCode" column="PRODUCT_CODE"/>
        <result property="serviceNo" column="SERVICE_NO"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="opType" column="OP_TYPE"/>
    </resultMap>

    <sql id="selectFzgjBdProductServiceEntityVo">
        select
            GUID,
            PRODUCT_CODE,
            SERVICE_NO,
            COMPANY_CODE,
            COMPANY_NAME,
            GROUP_CODE,
            GROUP_NAME,
            OP_TYPE
        from FZGJ_BD_PRODUCT_SERVICE
    </sql>
</mapper>