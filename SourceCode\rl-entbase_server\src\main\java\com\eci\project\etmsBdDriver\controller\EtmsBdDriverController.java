package com.eci.project.etmsBdDriver.controller;

import com.eci.common.util.*;
import com.eci.common.web.BllContext;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.etmsBdDriver.entity.queryEntity;
import com.eci.project.etmsBdDriver.service.EtmsBdDriverService;
import com.eci.project.etmsBdDriver.entity.EtmsBdDriverEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 司机基础信息Controller
*
* @<NAME_EMAIL>
* @date 2025-04-14
*/
@Api(tags = "司机基础信息")
@RestController
@RequestMapping("/etmsBdDriver")
public class EtmsBdDriverController extends EciBaseController {

    @Autowired
    private EtmsBdDriverService etmsBdDriverService;


    @ApiOperation("司机基础信息:保存")
    @EciLog(title = "司机基础信息:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody EtmsBdDriverEntity entity) throws Exception {
        if(BllContext.getBusinessType() == BusinessType.INSERT&&etmsBdDriverService.userIdExist(entity.getUserId()))
            throw new Exception("用户编号已存在");
        entity.setIsGk("Y");//设置外协司机是挂靠
        entity.setIsDriver("Y");//设置是驾驶员
        EtmsBdDriverEntity etmsBdDriverEntity =etmsBdDriverService.save(entity);
        return ResponseMsgUtil.success(10001,etmsBdDriverEntity);
    }


    @ApiOperation("司机基础信息:查询列表")
    @EciLog(title = "司机基础信息:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody EtmsBdDriverEntity entity){
        List<EtmsBdDriverEntity> etmsBdDriverEntities = etmsBdDriverService.selectList(entity);
        return ResponseMsgUtil.success(10001,etmsBdDriverEntities);
    }


    @ApiOperation("司机基础信息:分页查询列表")
    @EciLog(title = "司机基础信息:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody EtmsBdDriverEntity entity){
        TgPageInfo tgPageInfo = etmsBdDriverService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }

    @ApiOperation("司机基础信息:分页查询列表")
    @EciLog(title = "司机基础信息:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList1")
    @EciAction()
    public ResponseMsg selectPageList1(@RequestBody queryEntity entity){
        TgPageInfo tgPageInfo = etmsBdDriverService.queryPages(entity);
        return ResponseMsgUtil.successPlus(10001,tgPageInfo);
    }


    @ApiOperation("司机基础信息:根据ID查一条")
    @EciLog(title = "司机基础信息:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody EtmsBdDriverEntity entity){
        EtmsBdDriverEntity  etmsBdDriverEntity = etmsBdDriverService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.successPlus(10001,etmsBdDriverEntity);
    }


    @ApiOperation("司机基础信息:根据ID删除一条")
    @EciLog(title = "司机基础信息:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody EtmsBdDriverEntity entity){
        int count = etmsBdDriverService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("司机基础信息:根据ID字符串删除多条")
    @EciLog(title = "司机基础信息:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody EtmsBdDriverEntity entity) {
        int count = etmsBdDriverService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }

    @ApiOperation("司机基础信息:送审")
    @EciLog(title = "司机基础信息:送审", businessType = BusinessType.DELETE)
    @PostMapping("/SendAudit")
    @EciAction()
    public ResponseMsg SendAudit(@RequestBody EtmsBdDriverEntity entity) {
        etmsBdDriverService.SendAudit(entity.getIds());
        return ResponseMsgUtil.success(10001);
    }

    @ApiOperation("司机基础信息:审批退回")
    @EciLog(title = "司机基础信息:审批退回", businessType = BusinessType.DELETE)
    @PostMapping("/AuditBack")
    @EciAction()
    public ResponseMsg AuditBack(@RequestBody EtmsBdDriverEntity entity) {
        etmsBdDriverService.AuditBack(entity.getIds());
        return ResponseMsgUtil.success(10001);
    }

    @ApiOperation("司机基础信息:审批通过")
    @EciLog(title = "司机基础信息:审批通过", businessType = BusinessType.DELETE)
    @PostMapping("/AuditSuccess")
    @EciAction()
    public ResponseMsg AuditSuccess(@RequestBody EtmsBdDriverEntity entity) {
        etmsBdDriverService.AuditSuccess(entity.getIds());
        return ResponseMsgUtil.success(10001);
    }
}