package com.eci.project.fzgjBdOmsPages.service.impl;

import com.eci.common.web.BllContext;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjBdOmsPages.dao.FzgjBdOmsPagesDao;
import com.eci.project.fzgjBdOmsPages.entity.FzgjBdOmsPagesEntity;
import com.eci.project.fzgjBdOmsPages.service.IFzgjBdOmsPagesService;
import com.eci.project.fzgjBdOmsPages.validate.FzgjBdOmsPagesVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 录入订单编辑页面Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-03-19
*/
@Service
@Slf4j
public class FzgjBdOmsPagesServiceImpl implements IFzgjBdOmsPagesService
{
    @Autowired
    private FzgjBdOmsPagesDao fzgjBdOmsPagesDao;

    @Autowired
    private FzgjBdOmsPagesVal fzgjBdOmsPagesVal;


    @Override
    public TgPageInfo queryPageList(FzgjBdOmsPagesEntity entity) {
        EciQuery<FzgjBdOmsPagesEntity> eciQuery = EciQuery.doQuery(entity);
        List<FzgjBdOmsPagesEntity> entities = fzgjBdOmsPagesDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjBdOmsPagesEntity save(FzgjBdOmsPagesEntity entity) {
        // 返回实体对象
        FzgjBdOmsPagesEntity fzgjBdOmsPagesEntity = null;
        fzgjBdOmsPagesVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjBdOmsPagesEntity = fzgjBdOmsPagesDao.insertOne(entity);

        }else{

            fzgjBdOmsPagesEntity = fzgjBdOmsPagesDao.updateByEntityId(entity);

        }
        return fzgjBdOmsPagesEntity;
    }

    @Override
    public List<FzgjBdOmsPagesEntity> selectList(FzgjBdOmsPagesEntity entity) {
        return fzgjBdOmsPagesDao.selectList(entity);
    }

    @Override
    public FzgjBdOmsPagesEntity selectOneById(Serializable id) {
        return fzgjBdOmsPagesDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjBdOmsPagesEntity> list) {
        fzgjBdOmsPagesDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjBdOmsPagesDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjBdOmsPagesDao.deleteById(id);
    }

}