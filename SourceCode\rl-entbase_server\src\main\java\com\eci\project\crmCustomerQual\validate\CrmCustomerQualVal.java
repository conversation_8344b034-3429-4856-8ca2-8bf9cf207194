package com.eci.project.crmCustomerQual.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.crmCustomerQual.entity.CrmCustomerQualEntity;

import com.eci.sso.role.entity.UserContext;
import org.springframework.stereotype.Service;

import java.util.Date;


/**
* 资质管理Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-05-26
*/
@Service
public class CrmCustomerQualVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(CrmCustomerQualEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(CrmCustomerQualEntity entity, BusinessType businessType) {
        entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        entity.setUpdateDate(new Date());
        entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
        if(businessType==BusinessType.INSERT){
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateDate(new Date());
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
            entity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            entity.setCompanyName(UserContext.getUserInfo().getCompanyName());
        }
    }

}
