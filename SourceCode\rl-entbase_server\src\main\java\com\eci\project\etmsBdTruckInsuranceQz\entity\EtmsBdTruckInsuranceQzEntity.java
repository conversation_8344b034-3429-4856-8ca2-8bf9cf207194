package com.eci.project.etmsBdTruckInsuranceQz.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 保险管理对象 ETMS_BD_TRUCK_INSURANCE_QZ
 * 可以自己扩展字段
 * @<NAME_EMAIL>
 * @date 2025-04-28
 */
@ApiModel("保险管理")
@TableName("ETMS_BD_TRUCK_INSURANCE_QZ")
@FieldNameConstants
public class EtmsBdTruckInsuranceQzEntity extends EciBaseEntity{
    /**
     * GUID
     */
    @ApiModelProperty("GUID(50)")
    @TableField("GUID")
    private String guid;

    /**
     * GUID
     */
    @ApiModelProperty("GUID(50)")
    @TableField("TRUCK_GUID")
    private String truckGuid;

    /**
     * 保险编号
     */
    @ApiModelProperty("保险编号(30)")
    @TableField("POLICY_NO")
    private String policyNo;

    /**
     * 保险公司
     */
    @ApiModelProperty("保险公司(200)")
    @TableField("INSURER")
    private String insurer;

    /**
     * 保险类型
     */
    @ApiModelProperty("保险类型(40)")
    @TableField("INSURANCE_TYPE")
    private String insuranceType;

    /**
     * 保险开始日期
     */
    @ApiModelProperty("保险开始日期(7)")
    @TableField("START_DATE")
    private Date startDate;

    @ApiModelProperty("保险开始日期开始")
    @TableField(exist=false)
    private Date startDateStart;

    @ApiModelProperty("保险开始日期结束")
    @TableField(exist=false)
    private Date startDateEnd;

    /**
     * 保险结束日期
     */
    @ApiModelProperty("保险结束日期(7)")
    @TableField("END_DATE")
    private Date endDate;

    @ApiModelProperty("保险结束日期开始")
    @TableField(exist=false)
    private Date endDateStart;

    @ApiModelProperty("保险结束日期结束")
    @TableField(exist=false)
    private Date endDateEnd;

    /**
     * 归属公司
     */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
     * 归属公司
     */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
     * 归属组织
     */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
     * 归属组织
     */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
     * 归属集团
     */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
     * 归属集团
     */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
     * 最后修改人
     */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
     * 修改标志（0：不变，1：修改，2：删除，3：新增）
     */
    @ApiModelProperty("修改标志（0：不变，1：修改，2：删除，3：新增）(50)")
    @TableField("MOD_MARK")
    private String modMark;

    /**
     * 投保人/公司名称
     */
    @ApiModelProperty("投保人/公司名称(100)")
    @TableField("INSURED_NAME")
    private String insuredName;

    /**
     * 保险状态
     */
    @ApiModelProperty("保险状态(22)")
    @TableField("INSURANCE_STATUS")
    private Integer insuranceStatus;

    /**
     * 保险金额
     */
    @ApiModelProperty("保险金额(22)")
    @TableField("INSURANCE_MONEY")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal insuranceMoney;

    /**
     * 投保期限
     */
    @ApiModelProperty("投保期限(100)")
    @TableField("INSURANCE_PERIOD")
    private String insurancePeriod;

    /**
     * 备注
     */
    @ApiModelProperty("备注(500)")
    @TableField("REMARK")
    private String remark;

    /**
     * 创建日期
     */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
     * 最后修改时间
     */
    @ApiModelProperty("最后修改时间(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("最后修改时间开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("最后修改时间结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public EtmsBdTruckInsuranceQzEntity() {
        this.setSubClazz(EtmsBdTruckInsuranceQzEntity.class);
    }

    public EtmsBdTruckInsuranceQzEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public EtmsBdTruckInsuranceQzEntity setTruckGuid(String truckGuid) {
        this.truckGuid = truckGuid;
        this.nodifySetFiled("truckGuid", truckGuid);
        return this;
    }

    public String getTruckGuid() {
        this.nodifyGetFiled("truckGuid");
        return truckGuid;
    }

    public EtmsBdTruckInsuranceQzEntity setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
        this.nodifySetFiled("policyNo", policyNo);
        return this;
    }

    public String getPolicyNo() {
        this.nodifyGetFiled("policyNo");
        return policyNo;
    }

    public EtmsBdTruckInsuranceQzEntity setInsurer(String insurer) {
        this.insurer = insurer;
        this.nodifySetFiled("insurer", insurer);
        return this;
    }

    public String getInsurer() {
        this.nodifyGetFiled("insurer");
        return insurer;
    }

    public EtmsBdTruckInsuranceQzEntity setInsuranceType(String insuranceType) {
        this.insuranceType = insuranceType;
        this.nodifySetFiled("insuranceType", insuranceType);
        return this;
    }

    public String getInsuranceType() {
        this.nodifyGetFiled("insuranceType");
        return insuranceType;
    }

    public EtmsBdTruckInsuranceQzEntity setStartDate(Date startDate) {
        this.startDate = startDate;
        this.nodifySetFiled("startDate", startDate);
        return this;
    }

    public Date getStartDate() {
        this.nodifyGetFiled("startDate");
        return startDate;
    }

    public EtmsBdTruckInsuranceQzEntity setStartDateStart(Date startDateStart) {
        this.startDateStart = startDateStart;
        this.nodifySetFiled("startDateStart", startDateStart);
        return this;
    }

    public Date getStartDateStart() {
        this.nodifyGetFiled("startDateStart");
        return startDateStart;
    }

    public EtmsBdTruckInsuranceQzEntity setStartDateEnd(Date startDateEnd) {
        this.startDateEnd = startDateEnd;
        this.nodifySetFiled("startDateEnd", startDateEnd);
        return this;
    }

    public Date getStartDateEnd() {
        this.nodifyGetFiled("startDateEnd");
        return startDateEnd;
    }
    public EtmsBdTruckInsuranceQzEntity setEndDate(Date endDate) {
        this.endDate = endDate;
        this.nodifySetFiled("endDate", endDate);
        return this;
    }

    public Date getEndDate() {
        this.nodifyGetFiled("endDate");
        return endDate;
    }

    public EtmsBdTruckInsuranceQzEntity setEndDateStart(Date endDateStart) {
        this.endDateStart = endDateStart;
        this.nodifySetFiled("endDateStart", endDateStart);
        return this;
    }

    public Date getEndDateStart() {
        this.nodifyGetFiled("endDateStart");
        return endDateStart;
    }

    public EtmsBdTruckInsuranceQzEntity setEndDateEnd(Date endDateEnd) {
        this.endDateEnd = endDateEnd;
        this.nodifySetFiled("endDateEnd", endDateEnd);
        return this;
    }

    public Date getEndDateEnd() {
        this.nodifyGetFiled("endDateEnd");
        return endDateEnd;
    }
    public EtmsBdTruckInsuranceQzEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public EtmsBdTruckInsuranceQzEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public EtmsBdTruckInsuranceQzEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public EtmsBdTruckInsuranceQzEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public EtmsBdTruckInsuranceQzEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public EtmsBdTruckInsuranceQzEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public EtmsBdTruckInsuranceQzEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public EtmsBdTruckInsuranceQzEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public EtmsBdTruckInsuranceQzEntity setModMark(String modMark) {
        this.modMark = modMark;
        this.nodifySetFiled("modMark", modMark);
        return this;
    }

    public String getModMark() {
        this.nodifyGetFiled("modMark");
        return modMark;
    }

    public EtmsBdTruckInsuranceQzEntity setInsuredName(String insuredName) {
        this.insuredName = insuredName;
        this.nodifySetFiled("insuredName", insuredName);
        return this;
    }

    public String getInsuredName() {
        this.nodifyGetFiled("insuredName");
        return insuredName;
    }

    public EtmsBdTruckInsuranceQzEntity setInsuranceStatus(Integer insuranceStatus) {
        this.insuranceStatus = insuranceStatus;
        this.nodifySetFiled("insuranceStatus", insuranceStatus);
        return this;
    }

    public Integer getInsuranceStatus() {
        this.nodifyGetFiled("insuranceStatus");
        return insuranceStatus;
    }

    public EtmsBdTruckInsuranceQzEntity setInsuranceMoney(BigDecimal insuranceMoney) {
        this.insuranceMoney = insuranceMoney;
        this.nodifySetFiled("insuranceMoney", insuranceMoney);
        return this;
    }

    public BigDecimal getInsuranceMoney() {
        this.nodifyGetFiled("insuranceMoney");
        return insuranceMoney;
    }

    public EtmsBdTruckInsuranceQzEntity setInsurancePeriod(String insurancePeriod) {
        this.insurancePeriod = insurancePeriod;
        this.nodifySetFiled("insurancePeriod", insurancePeriod);
        return this;
    }

    public String getInsurancePeriod() {
        this.nodifyGetFiled("insurancePeriod");
        return insurancePeriod;
    }

    public EtmsBdTruckInsuranceQzEntity setRemark(String remark) {
        this.remark = remark;
        this.nodifySetFiled("remark", remark);
        return this;
    }

    public String getRemark() {
        this.nodifyGetFiled("remark");
        return remark;
    }

    public EtmsBdTruckInsuranceQzEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public EtmsBdTruckInsuranceQzEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public EtmsBdTruckInsuranceQzEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public EtmsBdTruckInsuranceQzEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public EtmsBdTruckInsuranceQzEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public EtmsBdTruckInsuranceQzEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
}
