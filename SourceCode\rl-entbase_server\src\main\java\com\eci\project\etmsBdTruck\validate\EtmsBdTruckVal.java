package com.eci.project.etmsBdTruck.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.etmsBdTruck.entity.EtmsBdTruckEntity;

import org.springframework.stereotype.Service;


/**
* 车辆信息Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-11
*/
@Service
public class EtmsBdTruckVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(EtmsBdTruckEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(EtmsBdTruckEntity entity, BusinessType businessType) {

    }

}
