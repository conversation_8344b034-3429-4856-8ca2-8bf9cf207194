package com.eci.project.fzgjCrmContractTypeWarn.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.fzgjCrmContractTypeWarn.entity.FzgjCrmContractTypeWarnEntity;


/**
* 合同种类及预警时效Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-04-10
*/
public interface FzgjCrmContractTypeWarnDao extends EciBaseDao<FzgjCrmContractTypeWarnEntity> {

}