package com.eci.project.fzgjScoreoption.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.codeConvert.EciCode;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 评分标准设置对象 FZGJ_SCOREOPTION
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-06-16
*/
@ApiModel("评分标准设置")
@TableName("FZGJ_SCOREOPTION")
@FieldNameConstants
public class FzgjScoreoptionEntity extends EciBaseEntity{
    /**
    * GUID
    */
    @ApiModelProperty("GUID(50)")
    @TableId("GUID")
    private String guid;

    /**
    * 规则名称
    */
    @ApiModelProperty("规则名称(50)")
    @TableField("ROLE_NAME")
    private String roleName;

    /**
    * 评分类目
    */
    @ApiModelProperty("评分类目(10)")
    @TableField("CATEGORY")
    private String category;

    /**
    * 运算公式
    */
    @ApiModelProperty("运算公式(10)")
    @TableField("FORMULA")
    private String formula;

    /**
    * 运算数值
    */
    @ApiModelProperty("运算数值(50)")
    @TableField("ROLE_VALUE")
    private String roleValue;

    /**
    * 分值
    */
    @ApiModelProperty("分值(22)")
    @TableField("ROLE_SCORE")
    private Integer roleScore;

    /**
    * 状态
    */
    @ApiModelProperty("状态(1)")
    @TableField("STATUS")
    @EciCode("YNKey")
    private String status;

    @ApiModelProperty("(50)")
    @TableField("CREATE_USER")
    private String createUser;

    @ApiModelProperty("(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("结束")
    @TableField(exist=false)
    private Date createDateEnd;

    @ApiModelProperty("(50)")
    @TableField("UPDATE_USER")
    private String updateUser;

    @ApiModelProperty("(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    @ApiModelProperty("(200)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    @ApiModelProperty("(200)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 类型
    */
    @ApiModelProperty("类型(22)")
    @TableField("TYPE")
    private Integer type;

    /**
    * 备注
    */
    @ApiModelProperty("备注(200)")
    @TableField("REMARK")
    private String remark;

    /**
    * 企业code
    */
    @ApiModelProperty("企业code(100)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 企业名称
    */
    @ApiModelProperty("企业名称(100)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 集团code
    */
    @ApiModelProperty("集团code(100)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 集团名称
    */
    @ApiModelProperty("集团名称(100)")
    @TableField("GROUP_NAME")
    private String groupName;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public FzgjScoreoptionEntity() {
        this.setSubClazz(FzgjScoreoptionEntity.class);
    }

    public FzgjScoreoptionEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public FzgjScoreoptionEntity setRoleName(String roleName) {
        this.roleName = roleName;
        this.nodifySetFiled("roleName", roleName);
        return this;
    }

    public String getRoleName() {
        this.nodifyGetFiled("roleName");
        return roleName;
    }

    public FzgjScoreoptionEntity setCategory(String category) {
        this.category = category;
        this.nodifySetFiled("category", category);
        return this;
    }

    public String getCategory() {
        this.nodifyGetFiled("category");
        return category;
    }

    public FzgjScoreoptionEntity setFormula(String formula) {
        this.formula = formula;
        this.nodifySetFiled("formula", formula);
        return this;
    }

    public String getFormula() {
        this.nodifyGetFiled("formula");
        return formula;
    }

    public FzgjScoreoptionEntity setRoleValue(String roleValue) {
        this.roleValue = roleValue;
        this.nodifySetFiled("roleValue", roleValue);
        return this;
    }

    public String getRoleValue() {
        this.nodifyGetFiled("roleValue");
        return roleValue;
    }

    public FzgjScoreoptionEntity setRoleScore(Integer roleScore) {
        this.roleScore = roleScore;
        this.nodifySetFiled("roleScore", roleScore);
        return this;
    }

    public Integer getRoleScore() {
        this.nodifyGetFiled("roleScore");
        return roleScore;
    }

    public FzgjScoreoptionEntity setStatus(String status) {
        this.status = status;
        this.nodifySetFiled("status", status);
        return this;
    }

    public String getStatus() {
        this.nodifyGetFiled("status");
        return status;
    }

    public FzgjScoreoptionEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public FzgjScoreoptionEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public FzgjScoreoptionEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public FzgjScoreoptionEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public FzgjScoreoptionEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public FzgjScoreoptionEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public FzgjScoreoptionEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public FzgjScoreoptionEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public FzgjScoreoptionEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public FzgjScoreoptionEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public FzgjScoreoptionEntity setType(Integer type) {
        this.type = type;
        this.nodifySetFiled("type", type);
        return this;
    }

    public Integer getType() {
        this.nodifyGetFiled("type");
        return type;
    }

    public FzgjScoreoptionEntity setRemark(String remark) {
        this.remark = remark;
        this.nodifySetFiled("remark", remark);
        return this;
    }

    public String getRemark() {
        this.nodifyGetFiled("remark");
        return remark;
    }

    public FzgjScoreoptionEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public FzgjScoreoptionEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public FzgjScoreoptionEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public FzgjScoreoptionEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

}
