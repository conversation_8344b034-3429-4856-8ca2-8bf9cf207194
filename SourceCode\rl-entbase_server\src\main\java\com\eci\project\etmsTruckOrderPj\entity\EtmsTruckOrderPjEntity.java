package com.eci.project.etmsTruckOrderPj.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.math.BigDecimal;
import java.util.Date;


/**
* 对象 ETMS_TRUCK_ORDER_PJ
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-04-22
*/
@ApiModel("")
@TableName("ETMS_TRUCK_ORDER_PJ")
@FieldNameConstants
public class EtmsTruckOrderPjEntity extends EciBaseEntity{
    /**
    * 主键
    */
    @ApiModelProperty("主键(50)")
    @TableId("GUID")
    private String guid;

    @ApiModelProperty("(50)")
    @TableField("TRUCK_ORDER_GUID")
    private String truckOrderGuid;

    @ApiModelProperty("(200)")
    @TableField("LOGIN_NO")
    private String loginNo;

    @ApiModelProperty("(2)")
    @TableField("FWTDDF")
    private String fwtddf;

    @ApiModelProperty("(2)")
    @TableField("YSSXDF")
    private String yssxdf;

    @ApiModelProperty("(2)")
    @TableField("XSAQDF")
    private String xsaqdf;

    @ApiModelProperty("(2)")
    @TableField("HDSXDF")
    private String hdsxdf;

    /**
    * 备注
    */
    @ApiModelProperty("备注(200)")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty("(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    @ApiModelProperty("(50)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 组织代码
    */
    @ApiModelProperty("组织代码(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    @ApiModelProperty("(50)")
    @TableField("NODE_NAME")
    private String nodeName;

    @ApiModelProperty("(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    @ApiModelProperty("(50)")
    @TableField("GROUP_NAME")
    private String groupName;

    @ApiModelProperty("(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    @ApiModelProperty("(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 创建时间
    */
    @ApiModelProperty("创建时间(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建时间开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建时间结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 修改人
    */
    @ApiModelProperty("修改人(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 修改时间
    */
    @ApiModelProperty("修改时间(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("修改时间开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("修改时间结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    @ApiModelProperty("(22)")
    @TableField("BCZHPF")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal bczhpf;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public EtmsTruckOrderPjEntity() {
        this.setSubClazz(EtmsTruckOrderPjEntity.class);
    }

    public EtmsTruckOrderPjEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public EtmsTruckOrderPjEntity setTruckOrderGuid(String truckOrderGuid) {
        this.truckOrderGuid = truckOrderGuid;
        this.nodifySetFiled("truckOrderGuid", truckOrderGuid);
        return this;
    }

    public String getTruckOrderGuid() {
        this.nodifyGetFiled("truckOrderGuid");
        return truckOrderGuid;
    }

    public EtmsTruckOrderPjEntity setLoginNo(String loginNo) {
        this.loginNo = loginNo;
        this.nodifySetFiled("loginNo", loginNo);
        return this;
    }

    public String getLoginNo() {
        this.nodifyGetFiled("loginNo");
        return loginNo;
    }

    public EtmsTruckOrderPjEntity setFwtddf(String fwtddf) {
        this.fwtddf = fwtddf;
        this.nodifySetFiled("fwtddf", fwtddf);
        return this;
    }

    public String getFwtddf() {
        this.nodifyGetFiled("fwtddf");
        return fwtddf;
    }

    public EtmsTruckOrderPjEntity setYssxdf(String yssxdf) {
        this.yssxdf = yssxdf;
        this.nodifySetFiled("yssxdf", yssxdf);
        return this;
    }

    public String getYssxdf() {
        this.nodifyGetFiled("yssxdf");
        return yssxdf;
    }

    public EtmsTruckOrderPjEntity setXsaqdf(String xsaqdf) {
        this.xsaqdf = xsaqdf;
        this.nodifySetFiled("xsaqdf", xsaqdf);
        return this;
    }

    public String getXsaqdf() {
        this.nodifyGetFiled("xsaqdf");
        return xsaqdf;
    }

    public EtmsTruckOrderPjEntity setHdsxdf(String hdsxdf) {
        this.hdsxdf = hdsxdf;
        this.nodifySetFiled("hdsxdf", hdsxdf);
        return this;
    }

    public String getHdsxdf() {
        this.nodifyGetFiled("hdsxdf");
        return hdsxdf;
    }

    public EtmsTruckOrderPjEntity setRemark(String remark) {
        this.remark = remark;
        this.nodifySetFiled("remark", remark);
        return this;
    }

    public String getRemark() {
        this.nodifyGetFiled("remark");
        return remark;
    }

    public EtmsTruckOrderPjEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public EtmsTruckOrderPjEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public EtmsTruckOrderPjEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public EtmsTruckOrderPjEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public EtmsTruckOrderPjEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public EtmsTruckOrderPjEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public EtmsTruckOrderPjEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public EtmsTruckOrderPjEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public EtmsTruckOrderPjEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public EtmsTruckOrderPjEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public EtmsTruckOrderPjEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public EtmsTruckOrderPjEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public EtmsTruckOrderPjEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public EtmsTruckOrderPjEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public EtmsTruckOrderPjEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public EtmsTruckOrderPjEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public EtmsTruckOrderPjEntity setBczhpf(BigDecimal bczhpf) {
        this.bczhpf = bczhpf;
        this.nodifySetFiled("bczhpf", bczhpf);
        return this;
    }

    public BigDecimal getBczhpf() {
        this.nodifyGetFiled("bczhpf");
        return bczhpf;
    }

}
