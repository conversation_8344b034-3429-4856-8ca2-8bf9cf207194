package com.eci.project.omsOrderFwxmBgbj.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.Zsr;
import com.eci.common.db.DBHelper;
import com.eci.common.util.DateUtils;
import com.eci.common.util.EciSqlUtl;
import com.eci.common.util.StringUtils;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.exception.BaseException;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjBdServiceItem.dao.FzgjBdServiceItemDao;
import com.eci.project.fzgjBdServiceItem.entity.FzgjBdServiceItemEntity;
import com.eci.project.omsOrder.dao.OmsOrderDao;
import com.eci.project.omsOrder.entity.OmsOrderEntity;
import com.eci.project.omsOrderFwxm.dao.OmsOrderFwxmDao;
import com.eci.project.omsOrderFwxm.entity.OmsOrderFwxmEntity;
import com.eci.project.omsOrderFwxmBgbj.dao.OmsOrderFwxmBgbjDao;
import com.eci.project.omsOrderFwxmBgbj.entity.OmsOrderFwxmBgbjDTOEntity;
import com.eci.project.omsOrderFwxmBgbj.entity.OmsOrderFwxmBgbjEntity;
import com.eci.project.omsOrderFwxmBgbj.validate.OmsOrderFwxmBgbjVal;
import com.eci.project.omsOrderFwxmWork.dao.OmsOrderFwxmWorkDao;
import com.eci.project.omsOrderFwxmWork.entity.OmsOrderFwxmWorkEntity;
import com.eci.project.omsOrderPre.dao.OmsOrderPreDao;
import com.eci.project.omsOrderPre.entity.OmsOrderPreEntity;
import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.List;
import java.util.Random;
import java.util.UUID;


/**
 * 委托内容-报关报检Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-05-14
 */
@Service
@Slf4j
public class OmsOrderFwxmBgbjService implements EciBaseService<OmsOrderFwxmBgbjEntity> {

    @Autowired
    private OmsOrderFwxmBgbjDao omsOrderFwxmBgbjDao;
    @Autowired
    private OmsOrderFwxmDao omsOrderFwxmDao;
    @Autowired
    private OmsOrderDao omsOrderDao;
    @Autowired
    private OmsOrderPreDao omsOrderPreDao;
    @Autowired
    private OmsOrderFwxmWorkDao omsOrderFwxmWorkDao;
    @Autowired
    private FzgjBdServiceItemDao fzgjBdServiceItemDao;

    @Autowired
    private OmsOrderFwxmBgbjVal omsOrderFwxmBgbjVal;

    public static String getUniqueCode(int length) {
        UUID uuid = UUID.randomUUID();
        String combined = uuid.toString().replaceAll("-", "").toUpperCase(); // 直接使用UUID的字符串形式
        return combined.length() >= length
                ? combined.substring(0, length)
                : combined + getUniqueCode(length - combined.length()); // 递归补足长度
    }

    @Override
    public TgPageInfo queryPageList(OmsOrderFwxmBgbjEntity entity) {
        EciQuery<OmsOrderFwxmBgbjEntity> eciQuery = EciQuery.buildQuery(entity);
        List<OmsOrderFwxmBgbjEntity> entities = omsOrderFwxmBgbjDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }

    /**
     * 保存报关报检-报关单信息
     *
     * @param entity
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderFwxmBgbjDTOEntity saveBGD(OmsOrderFwxmBgbjDTOEntity entity) {
        // 返回实体对象
        OmsOrderFwxmBgbjEntity omsOrderFwxmBgbjEntity = new OmsOrderFwxmBgbjEntity();
        omsOrderFwxmBgbjVal.saveValidate(entity, BllContext.getBusinessType());
        OmsOrderEntity omsOrderEntity =null;
        OmsOrderPreEntity omsOrderPreEntity = new OmsOrderPreEntity();
        if(StringUtils.hasValue(entity.getOrderNo())){
            QueryWrapper<OmsOrderEntity> queryWrapper_order = new QueryWrapper<>();
            queryWrapper_order.eq("ORDER_NO", entity.getOrderNo());
            omsOrderEntity= omsOrderDao.selectOne(queryWrapper_order);
        }else if (StringUtils.hasValue(entity.getPreNo())) {
            QueryWrapper<OmsOrderPreEntity> queryWrapper_pre = new QueryWrapper<>();
            queryWrapper_pre.eq("PRE_NO", entity.getPreNo());
            omsOrderPreEntity = omsOrderPreDao.selectOne(queryWrapper_pre);
        }

        QueryWrapper<OmsOrderFwxmBgbjEntity> queryWrapper_bgbj = new QueryWrapper<>();
        if(omsOrderEntity!=null){
            queryWrapper_bgbj.eq("ORDER_NO", omsOrderEntity.getOrderNo());
        }else if(omsOrderPreEntity!=null){
            queryWrapper_bgbj.eq("PRE_NO", omsOrderPreEntity.getPreNo());
        }
        queryWrapper_bgbj.eq("FWXM_CODE", entity.getFwxmCode());
        queryWrapper_bgbj.eq("GROUP_CODE", UserContext.getUserInfo().getCompanyCode());
        boolean isAdd = omsOrderFwxmBgbjDao.selectList(queryWrapper_bgbj).size() <= 0;

        QueryWrapper<OmsOrderFwxmEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("FWXM_CODE", entity.getFwxmCode());
        queryWrapper.eq("GROUP_CODE", UserContext.getUserInfo().getCompanyCode());
        if (StringUtils.hasValue(entity.getOrderNo())) {
            queryWrapper.eq("ORDER_NO", entity.getOrderNo());
        } else if (StringUtils.hasValue(entity.getPreNo())) {
            queryWrapper.eq("PRE_NO", entity.getPreNo());
        }
        List<OmsOrderFwxmEntity> omsOrderFwxmEntities = omsOrderFwxmDao.selectList(queryWrapper);
        OmsOrderFwxmEntity omsOrderFwxmEntity = new OmsOrderFwxmEntity();
        if (omsOrderFwxmEntities.size() > 0) {
            //更新订单服务项目
            omsOrderFwxmEntity = omsOrderFwxmEntities.get(0);
            omsOrderFwxmEntity.setUpdateDate(DateUtils.getNowDate());
            omsOrderFwxmEntity.setUpdateUser(UserContext.getUserInfo().getUserId());
            omsOrderFwxmEntity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            omsOrderFwxmEntity.setOtherMemo(entity.getOtherMemo());
            omsOrderFwxmDao.updateByEntityId(omsOrderFwxmEntity);
        }
        OmsOrderFwxmWorkEntity orderFwxmWorkEntityNew = null;
        List<OmsOrderFwxmWorkEntity> omsOrderFwxmWorkEntities = LoadByNo(StringUtils.hasValue(entity.getOrderNo()) ? entity.getOrderNo() : entity.getPreNo(), entity.getFwxmCode());
        if (omsOrderFwxmWorkEntities.size() == 0) {
            //更新订单服务项目委托
            orderFwxmWorkEntityNew = new OmsOrderFwxmWorkEntity();
            orderFwxmWorkEntityNew.setGuid(IdWorker.get32UUID());
            orderFwxmWorkEntityNew.setNodeCodeNb(entity.getNodeCodeNb());
            orderFwxmWorkEntityNew.setGysCode(entity.getGysCode());
            orderFwxmWorkEntityNew.setFwxmCode(omsOrderFwxmEntity.getFwxmCode());
            orderFwxmWorkEntityNew.setOrderNo(omsOrderFwxmEntity.getOrderNo());
            orderFwxmWorkEntityNew.setPreNo(omsOrderFwxmEntity.getPreNo());
            orderFwxmWorkEntityNew.setIsWb(entity.getIsWb());
            if (orderFwxmWorkEntityNew.getIsWb().equals("Y")) {
                orderFwxmWorkEntityNew.setNodeCodeNb("");
            } else {
                orderFwxmWorkEntityNew.setGysCode("");
            }
            Random random = new Random();
            int randomNumber = 1000 + random.nextInt(9000);
            orderFwxmWorkEntityNew.setWorkNo((omsOrderEntity!=null?omsOrderEntity.getOrderNo():omsOrderPreEntity.getPreNo()) + "-" + orderFwxmWorkEntityNew.getFwxmCode() + "-" + randomNumber);

            if (omsOrderEntity!=null) {
                if (!StringUtils.hasValue(omsOrderEntity.getPreOrderNo())) {
                    orderFwxmWorkEntityNew.setUdf1(orderFwxmWorkEntityNew.getWorkNo());
                } else if (StringUtils.hasValue(orderFwxmWorkEntityNew.getUdf1())) {
                    orderFwxmWorkEntityNew.setUdf1(orderFwxmWorkEntityNew.getUdf1() + orderFwxmWorkEntityNew.getWorkNo());
                }
            }
            orderFwxmWorkEntityNew.setFwlxCode(entity.getFwxmCode().substring(0, 3));
            orderFwxmWorkEntityNew.setCreateDate(DateUtils.getNowDate());
            orderFwxmWorkEntityNew.setCreateUser(UserContext.getUserInfo().getUserId());
            orderFwxmWorkEntityNew.setCreateUserName(UserContext.getUserInfo().getTrueName());
            orderFwxmWorkEntityNew.setUpdateDate(DateUtils.getNowDate());
            orderFwxmWorkEntityNew.setUpdateUser(UserContext.getUserInfo().getUserId());
            orderFwxmWorkEntityNew.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            orderFwxmWorkEntityNew.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            orderFwxmWorkEntityNew.setCompanyName(UserContext.getUserInfo().getCompanyName());
            orderFwxmWorkEntityNew.setNodeCode(UserContext.getUserInfo().getDeptCode());
            orderFwxmWorkEntityNew.setNodeName(UserContext.getUserInfo().getDeptName());
            orderFwxmWorkEntityNew.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            orderFwxmWorkEntityNew.setGroupName(UserContext.getUserInfo().getCompanyName());
            orderFwxmWorkEntityNew.setUdf7(entity.getFwxmCode());
            omsOrderFwxmWorkDao.insertOne(orderFwxmWorkEntityNew);

        } else {
            orderFwxmWorkEntityNew = omsOrderFwxmWorkEntities.get(0);
            //电子口岸入网申请不修改ORDER_FWXM_WORK
            if (!entity.getFwxmCode().equals("*********")) {
                if (orderFwxmWorkEntityNew.getIsWb().equals("Y")) {
                    orderFwxmWorkEntityNew.setNodeCodeNb("");
                } else {
                    orderFwxmWorkEntityNew.setGysCode("");
                }
                orderFwxmWorkEntityNew.setFwxmCode(omsOrderFwxmEntity.getFwxmCode());
                orderFwxmWorkEntityNew.setOrderNo(omsOrderFwxmEntity.getOrderNo());
                orderFwxmWorkEntityNew.setPreNo(omsOrderFwxmEntity.getPreNo());
                orderFwxmWorkEntityNew.setUpdateDate(DateUtils.getNowDate());
                orderFwxmWorkEntityNew.setUpdateUser(UserContext.getUserInfo().getUserId());
                orderFwxmWorkEntityNew.setUpdateUserName(UserContext.getUserInfo().getTrueName());
                omsOrderFwxmWorkDao.updateByEntityId(orderFwxmWorkEntityNew);
            }
        }
        if (entity.getFwxmCode().equals("800100")) {
            if (entity.getBhType().equals("1")) {
                if (!entity.getiEType().equals("1") && !entity.getiEType().equals("2")) {
                    throw new BaseException("报关模式为口岸通关时,进/出口栏位只能选择进口/出口");
                }
            }
        }

        BeanUtils.copyProperties(entity, omsOrderFwxmBgbjEntity);
        if (isAdd) {
            omsOrderFwxmBgbjEntity.setWorkNo(orderFwxmWorkEntityNew.getWorkNo());
            omsOrderFwxmBgbjEntity.setBgbjNo("C" + UserContext.getUserInfo().getUserLoginNo() + getUniqueCode(11));
            entity.setWorkNo(omsOrderFwxmBgbjEntity.getWorkNo());
            entity.setBgbjNo(omsOrderFwxmBgbjEntity.getBgbjNo());
            omsOrderFwxmBgbjEntity.setCreateDate(DateUtils.getNowDate());
            omsOrderFwxmBgbjEntity.setCreateUser(UserContext.getUserInfo().getUserId());
            omsOrderFwxmBgbjEntity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            omsOrderFwxmBgbjEntity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            omsOrderFwxmBgbjEntity.setCompanyName(UserContext.getUserInfo().getCompanyName());
            omsOrderFwxmBgbjEntity.setNodeCode(UserContext.getUserInfo().getDeptCode());
            omsOrderFwxmBgbjEntity.setNodeName(UserContext.getUserInfo().getDeptName());
            omsOrderFwxmBgbjEntity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            omsOrderFwxmBgbjEntity.setGroupName(UserContext.getUserInfo().getCompanyName());
            omsOrderFwxmBgbjDao.insertOne(omsOrderFwxmBgbjEntity);

        } else {
            omsOrderFwxmBgbjEntity.setUpdateDate(DateUtils.getNowDate());
            omsOrderFwxmBgbjEntity.setUpdateUser(UserContext.getUserInfo().getUserId());
            omsOrderFwxmBgbjEntity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            omsOrderFwxmBgbjDao.updateByEntityId(omsOrderFwxmBgbjEntity);

        }
        return entity;
    }

    /**
     * 保存报关报检-二合一、三合一信息
     *
     * @param entity
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderFwxmBgbjDTOEntity saveMore(OmsOrderFwxmBgbjDTOEntity entity) {
        OmsOrderFwxmBgbjEntity omsOrderFwxmBgbjEntity = null;
        omsOrderFwxmBgbjVal.saveValidate(entity, BllContext.getBusinessType());
        OmsOrderEntity omsOrderEntity = null;
        OmsOrderPreEntity omsOrderPreEntity = new OmsOrderPreEntity();
        if (StringUtils.hasValue(entity.getOrderNo())) {
            QueryWrapper<OmsOrderEntity> queryWrapper_order = new QueryWrapper<>();
            queryWrapper_order.eq("ORDER_NO", entity.getOrderNo());
            omsOrderEntity = omsOrderDao.selectOne(queryWrapper_order);
        } else if (StringUtils.hasValue(entity.getPreNo())) {
            QueryWrapper<OmsOrderPreEntity> queryWrapper_pre = new QueryWrapper<>();
            queryWrapper_pre.eq("PRE_NO", entity.getPreNo());
            omsOrderPreEntity = omsOrderPreDao.selectOne(queryWrapper_pre);
        }
        QueryWrapper<OmsOrderFwxmBgbjEntity> queryWrapper_bgbj = new QueryWrapper<>();
        if(omsOrderEntity!=null){
            queryWrapper_bgbj.eq("ORDER_NO", omsOrderEntity.getOrderNo());
        }else if(omsOrderPreEntity!=null){
            queryWrapper_bgbj.eq("PRE_NO", omsOrderPreEntity.getPreNo());
        }
        queryWrapper_bgbj.eq("FWXM_CODE", entity.getFwxmCode());
        queryWrapper_bgbj.eq("GROUP_CODE", UserContext.getUserInfo().getCompanyCode());
        boolean isAdd = true;
        List<OmsOrderFwxmBgbjEntity> fwxmBgbjList = omsOrderFwxmBgbjDao.selectList(queryWrapper_bgbj);
        if (fwxmBgbjList.size() > 0) {
            isAdd = false;
        }
        QueryWrapper<OmsOrderFwxmEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("FWXM_CODE", entity.getFwxmCode());
        queryWrapper.eq("GROUP_CODE", UserContext.getUserInfo().getCompanyCode());
        if (StringUtils.hasValue(entity.getOrderNo())) {
            queryWrapper.eq("ORDER_NO", entity.getOrderNo());
        } else if (StringUtils.hasValue(entity.getPreNo())) {
            queryWrapper.eq("PRE_NO", entity.getPreNo());
        }
        List<OmsOrderFwxmEntity> omsOrderFwxmEntities = omsOrderFwxmDao.selectList(queryWrapper);
        OmsOrderFwxmEntity omsOrderFwxmEntity = new OmsOrderFwxmEntity();
        if (omsOrderFwxmEntities.size() > 0) {
            //更新订单服务项目
            omsOrderFwxmEntity = omsOrderFwxmEntities.get(0);
            omsOrderFwxmEntity.setUpdateDate(DateUtils.getNowDate());
            omsOrderFwxmEntity.setUpdateUser(UserContext.getUserInfo().getUserId());
            omsOrderFwxmEntity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            omsOrderFwxmEntity.setOtherMemo(entity.getOtherMemo());
            omsOrderFwxmDao.updateByEntityId(omsOrderFwxmEntity);
        }
        //查询服务项目
        QueryWrapper<FzgjBdServiceItemEntity> queryWrapper_item = new QueryWrapper<>();
        queryWrapper_item.eq("CODE", entity.getFwxmCode());
        queryWrapper_item.eq("GROUP_CODE", UserContext.getUserInfo().getCompanyCode());
        List<FzgjBdServiceItemEntity> fzgjBdServiceItemEntities = fzgjBdServiceItemDao.selectList(queryWrapper_item);
        FzgjBdServiceItemEntity entity_item = fzgjBdServiceItemEntities.size() > 0 ? fzgjBdServiceItemEntities.get(0) : new FzgjBdServiceItemEntity();

        if (isAdd) {
            /*新增多条work*/
            String[] fwxmCode = entity.getFwxmCode().split("\\|");
            OmsOrderFwxmWorkEntity workEntity1 = null;
            StringBuilder workNo = new StringBuilder();
            for (String s : fwxmCode) {
                workEntity1 = new OmsOrderFwxmWorkEntity();
                workEntity1.setFwxmCode(s);
                workEntity1.setOrderNo(entity.getOrderNo());
                workEntity1.setPreNo(omsOrderEntity != null ? omsOrderEntity.getPreNo() : omsOrderPreEntity.getPreNo());
                workEntity1.setBillCode(entity_item.getJdBill());
                workEntity1.setGuid(IdWorker.get32UUID());
                workEntity1.setNodeCodeNb(entity.getNodeCodeNb());
                workEntity1.setGysCode(entity.getGysCode());
                workEntity1.setIsWb(entity.getIsWb());
                if (workEntity1.getIsWb().equals("Y")) {
                    workEntity1.setNodeCodeNb("");
                } else {
                    workEntity1.setGysCode("");
                }

                workEntity1.setWorkNo((omsOrderEntity != null ? omsOrderEntity.getOrderNo():omsOrderPreEntity.getPreNo()) + "-" + s + "-" + getUniqueCode(4));

                if (omsOrderEntity != null && !StringUtils.hasValue(omsOrderEntity.getPreOrderNo())) {
                    workEntity1.setUdf1(workEntity1.getWorkNo());
                } else if (StringUtils.hasValue(workEntity1.getUdf1())) {
                    workEntity1.setUdf1(workEntity1.getUdf1() + workEntity1.getWorkNo());
                }
                workEntity1.setFwlxCode(s.substring(0, 3));
                workEntity1.setCreateDate(DateUtils.getNowDate());
                workEntity1.setCreateUser(UserContext.getUserInfo().getUserId());
                workEntity1.setCreateUserName(UserContext.getUserInfo().getTrueName());
                workEntity1.setUpdateDate(DateUtils.getNowDate());
                workEntity1.setUpdateUser(UserContext.getUserInfo().getUserId());
                workEntity1.setUpdateUserName(UserContext.getUserInfo().getTrueName());
                workEntity1.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
                workEntity1.setCompanyName(UserContext.getUserInfo().getCompanyName());
                workEntity1.setNodeCode(UserContext.getUserInfo().getDeptCode());
                workEntity1.setNodeName(UserContext.getUserInfo().getDeptName());
                workEntity1.setGroupCode(UserContext.getUserInfo().getCompanyCode());
                workEntity1.setGroupName(UserContext.getUserInfo().getCompanyName());
                workEntity1.setUdf7(entity.getFwxmCode());
                omsOrderFwxmWorkDao.insertOne(workEntity1);
                workNo.append(workEntity1.getWorkNo() + ",");

                //多条报关报检保存
                omsOrderFwxmBgbjEntity = new OmsOrderFwxmBgbjEntity();
                BeanUtils.copyProperties(entity, omsOrderFwxmBgbjEntity);
                omsOrderFwxmBgbjEntity.setWorkNo(omsOrderEntity != null ? workEntity1.getWorkNo() : "");
                omsOrderFwxmBgbjEntity.setBgbjNo("C" + UserContext.getUserInfo().getUserLoginNo() + getUniqueCode(11));
                omsOrderFwxmBgbjEntity.setCreateDate(DateUtils.getNowDate());
                omsOrderFwxmBgbjEntity.setCreateUser(UserContext.getUserInfo().getUserId());
                omsOrderFwxmBgbjEntity.setCreateUserName(UserContext.getUserInfo().getTrueName());
                omsOrderFwxmBgbjEntity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
                omsOrderFwxmBgbjEntity.setCompanyName(UserContext.getUserInfo().getCompanyName());
                omsOrderFwxmBgbjEntity.setNodeCode(UserContext.getUserInfo().getDeptCode());
                omsOrderFwxmBgbjEntity.setNodeName(UserContext.getUserInfo().getDeptName());
                omsOrderFwxmBgbjEntity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
                omsOrderFwxmBgbjEntity.setGroupName(UserContext.getUserInfo().getCompanyName());
                omsOrderFwxmBgbjDao.insertOne(omsOrderFwxmBgbjEntity);
            }
            entity.setWorkNo(workNo.toString().substring(0, workNo.length() - 1));

        } else {
            List<OmsOrderFwxmWorkEntity> omsOrderFwxmWorkEntities = LoadByNo(StringUtils.hasValue(entity.getOrderNo())?entity.getOrderNo():entity.getPreNo(), entity.getFwxmCode());
            for (OmsOrderFwxmWorkEntity omsOrderFwxmWorkUpdate : omsOrderFwxmWorkEntities) {
                omsOrderFwxmWorkUpdate.setOrderNo(entity.getOrderNo());
                omsOrderFwxmWorkUpdate.setPreNo(omsOrderEntity!=null?omsOrderEntity.getPreNo():omsOrderPreEntity.getPreNo());
                omsOrderFwxmWorkUpdate.setNodeCodeNb(entity.getNodeCodeNb());
                omsOrderFwxmWorkUpdate.setGysCode(entity.getGysCode());
                omsOrderFwxmWorkUpdate.setIsWb(entity.getIsWb());
                if (omsOrderFwxmWorkUpdate.getIsWb().equals("Y")) {
                    omsOrderFwxmWorkUpdate.setNodeCodeNb("");
                } else {
                    omsOrderFwxmWorkUpdate.setGysCode("");
                }
                omsOrderFwxmWorkUpdate.setUpdateDate(DateUtils.getNowDate());
                omsOrderFwxmWorkUpdate.setUpdateUser(UserContext.getUserInfo().getUserId());
                omsOrderFwxmWorkUpdate.setUpdateUserName(UserContext.getUserInfo().getTrueName());
                omsOrderFwxmWorkDao.updateByEntityId(omsOrderFwxmWorkUpdate);

            }

            for (OmsOrderFwxmBgbjEntity omsOrderFwxmBgbjUpdate : fwxmBgbjList) {
                String workNo = omsOrderFwxmBgbjUpdate.getWorkNo();
                BeanUtils.copyProperties(entity, omsOrderFwxmBgbjUpdate);
                omsOrderFwxmBgbjUpdate.setWorkNo(workNo);
                omsOrderFwxmBgbjUpdate.setUpdateDate(DateUtils.getNowDate());
                omsOrderFwxmBgbjUpdate.setUpdateUser(UserContext.getUserInfo().getUserId());
                omsOrderFwxmBgbjUpdate.setUpdateUserName(UserContext.getUserInfo().getTrueName());
                omsOrderFwxmBgbjDao.updateByEntityId(omsOrderFwxmBgbjUpdate);
            }
        }
        return entity;

    }

    @Override
    public List<OmsOrderFwxmBgbjEntity> selectList(OmsOrderFwxmBgbjEntity entity) {
        return omsOrderFwxmBgbjDao.selectList(entity);
    }

    public OmsOrderFwxmBgbjDTOEntity selectOneById(OmsOrderFwxmBgbjEntity entity) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("select A.IS_PTSB,A.PRE_NO,A.ORDER_NO,A.WORK_NO,A.I_E_TYPE,A.JNSHR,A.JNFHR,A.I_E_PORT,D.NODE_CODE_NB,D.GYS_CODE,D.IS_WB,A.CUSTOM_CODE,A.BGD_CUSTOM_CODE,B.OTHER_MEMO,A.BGD_CUSTOM_CODE,A.BG_TYPE_COMPANY,A.HDCS,A.YSFS_COMPANY,A.QTCKH,A.XDH,A.YSFS,A.DEC_NO,A.BJ_TYPE,A.CUSTOM_CODE,A.TRADE_CODE,A.QW_COMPANY,A.IS_PTSB_HZQD,A.IS_PTSB_CRKD");
        stringBuilder.append(" from  OMS_ORDER_FWXM_BGBJ A");
        stringBuilder.append(" LEFT JOIN  OMS_ORDER_FWXM B ON A.FWXM_CODE = B.FWXM_CODE AND A.GROUP_CODE =B.GROUP_CODE AND A.ORDER_NO = B.ORDER_NO");
        stringBuilder.append(" LEFT JOIN  OMS_ORDER_FWXM_WORK D ON A.WORK_NO = D.WORK_NO");
        stringBuilder.append(" WHERE A.FWXM_CODE ='" + entity.getFwxmCode() + "' and A.company_code='" + UserContext.getUserInfo().getCompanyCode() + "'");
        stringBuilder.append(" and (A.ORDER_NO ='" + entity.getOrderNo() + "' or A.PRE_NO ='"+entity.getPreNo()+"')");
        List<OmsOrderFwxmBgbjDTOEntity> list = DBHelper.selectList(stringBuilder.toString(), OmsOrderFwxmBgbjDTOEntity.class);
        StringBuilder strWorkNo = new StringBuilder();
        if (list.size() > 1) {
            for (OmsOrderFwxmBgbjDTOEntity dtoEntity : list) {
                strWorkNo.append(dtoEntity.getWorkNo() + ",");
            }
        }
        if (strWorkNo.length() > 0) {
            list.get(0).setWorkNo(strWorkNo.substring(0, strWorkNo.length() - 1));
        }
        return list.size() > 0 ? list.get(0) : new OmsOrderFwxmBgbjDTOEntity();
    }

    @Override
    public void insertBatch(List<OmsOrderFwxmBgbjEntity> list) {
        omsOrderFwxmBgbjDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return omsOrderFwxmBgbjDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return omsOrderFwxmBgbjDao.deleteById(id);
    }

    public List<OmsOrderFwxmWorkEntity> LoadByNo(String orderNo, String fwxmCode) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("SELECT A.GUID,A.WORK_NO,A.ORDER_NO,A.PRE_NO,A.FWXM_CODE,A.TASK_SEQ");
        stringBuilder.append("    ,A.TASK_PRE,A.IS_WB,");
        stringBuilder.append("   ( case when NVL(A.GYS_CODE,' ')=' ' then '' else OMS_CODE_NAMECOM(A.GYS_CODE,A.GROUP_CODE,'CRM_CUSTOMER_GYS') end  ) GYS_CODE ,");
        stringBuilder.append("   ( case when  NVL(A.NODE_CODE_NB,' ')=' ' then '' else OMS_CODE_NAME(A.NODE_CODE_NB,'OMS_SSO_NODE') end ) NODE_CODE_NB ,");
        stringBuilder.append("   A.QUOTE_CODE");
        stringBuilder.append("   ,A.STATUS,A.STAGE,A.SYS_CODE,A.BILL_CODE,A.DOC_NO");
        stringBuilder.append("   ,A.RESPONSE_CODE,A.SEND_DATE,A.CREATE_USER");
        stringBuilder.append("   ,A.CREATE_USER_NAME,A.CREATE_DATE,A.UPDATE_USER,A.UPDATE_USER_NAME,A.UPDATE_DATE");
        stringBuilder.append("   ,A.COMPANY_CODE,A.COMPANY_NAME,A.NODE_CODE,A.NODE_NAME,A.GROUP_CODE");
        stringBuilder.append("   ,A.GROUP_NAME ");
        stringBuilder.append("  FROM OMS_ORDER_FWXM_WORK A ");
        stringBuilder.append("  WHERE (A.ORDER_NO='" + orderNo + "' or A.PRE_NO='" + orderNo + "') AND A.GROUP_CODE ='" + UserContext.getUserInfo().getCompanyCode() + "' AND A.UDF7='" + fwxmCode + "'");
        List<OmsOrderFwxmWorkEntity> data = DBHelper.selectList(stringBuilder.toString(), OmsOrderFwxmWorkEntity.class);
        return data;
    }


    /**
     * 查询-订单接单页面的进出口标记，默认到反馈编辑区
     **/
    public List<OmsOrderFwxmBgbjEntity> selectOrderFwxmWorkFkGetOrderIETYPE(OmsOrderFwxmBgbjEntity entity) {

        int lastStr = entity.getWorkNo().lastIndexOf('-');
        String work_no = lastStr > 0 ? entity.getWorkNo().substring(0, lastStr) : entity.getWorkNo();

        String sql = " SELECT  I_E_TYPE ||'|'||OMS_NAME(A.I_E_TYPE, 'OMS_BD_IE') AS I_E_TYPE\n" +
                "FROM OMS_ORDER_FWXM_BGBJ A  WHERE work_no like  '%" + work_no + "%'";

        return DBHelper.selectList(sql, OmsOrderFwxmBgbjEntity.class);
    }
}