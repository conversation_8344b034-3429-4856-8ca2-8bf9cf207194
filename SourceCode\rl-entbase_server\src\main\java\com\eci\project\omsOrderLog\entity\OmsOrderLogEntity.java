package com.eci.project.omsOrderLog.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 订单操作日志信息对象 OMS_ORDER_LOG
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-06-19
*/
@ApiModel("订单操作日志信息")
@TableName("OMS_ORDER_LOG")
@FieldNameConstants
public class OmsOrderLogEntity extends EciBaseEntity{
    /**
    * 订单号
    */
    @ApiModelProperty("订单号(50)")
    @TableField("ORDER_NO")
    private String orderNo;

    /**
    * 业务类型
    */
    @ApiModelProperty("业务类型(50)")
    @TableField("BIZ_TYPE")
    private String bizType;

    /**
    * 操作名称
    */
    @ApiModelProperty("操作名称(255)")
    @TableField("OPER_NAME")
    private String operName;

    /**
    * 操作日期
    */
    @ApiModelProperty("操作日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("操作日期开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("操作日期结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 详细内容
    */
    @ApiModelProperty("详细内容(1,000)")
    @TableField("DATA_DETAIL")
    private String dataDetail;

    /**
    * 备注
    */
    @ApiModelProperty("备注(1,000)")
    @TableField("MEMO")
    private String memo;

    /**
    * 操作用户姓名
    */
    @ApiModelProperty("操作用户姓名(50)")
    @TableField("USER_NAME")
    private String userName;

    /**
    * 用户真实姓名
    */
    @ApiModelProperty("用户真实姓名(50)")
    @TableField("TRUE_NAME")
    private String trueName;

    /**
    * 公司代码
    */
    @ApiModelProperty("公司代码(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 公司名称
    */
    @ApiModelProperty("公司名称(255)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 主键
    */
    @ApiModelProperty("主键(50)")
    @TableField("GUID")
    private String guid;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public OmsOrderLogEntity() {
        this.setSubClazz(OmsOrderLogEntity.class);
    }

    public OmsOrderLogEntity setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        this.nodifySetFiled("orderNo", orderNo);
        return this;
    }

    public String getOrderNo() {
        this.nodifyGetFiled("orderNo");
        return orderNo;
    }

    public OmsOrderLogEntity setBizType(String bizType) {
        this.bizType = bizType;
        this.nodifySetFiled("bizType", bizType);
        return this;
    }

    public String getBizType() {
        this.nodifyGetFiled("bizType");
        return bizType;
    }

    public OmsOrderLogEntity setOperName(String operName) {
        this.operName = operName;
        this.nodifySetFiled("operName", operName);
        return this;
    }

    public String getOperName() {
        this.nodifyGetFiled("operName");
        return operName;
    }

    public OmsOrderLogEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public OmsOrderLogEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public OmsOrderLogEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public OmsOrderLogEntity setDataDetail(String dataDetail) {
        this.dataDetail = dataDetail;
        this.nodifySetFiled("dataDetail", dataDetail);
        return this;
    }

    public String getDataDetail() {
        this.nodifyGetFiled("dataDetail");
        return dataDetail;
    }

    public OmsOrderLogEntity setMemo(String memo) {
        this.memo = memo;
        this.nodifySetFiled("memo", memo);
        return this;
    }

    public String getMemo() {
        this.nodifyGetFiled("memo");
        return memo;
    }

    public OmsOrderLogEntity setUserName(String userName) {
        this.userName = userName;
        this.nodifySetFiled("userName", userName);
        return this;
    }

    public String getUserName() {
        this.nodifyGetFiled("userName");
        return userName;
    }

    public OmsOrderLogEntity setTrueName(String trueName) {
        this.trueName = trueName;
        this.nodifySetFiled("trueName", trueName);
        return this;
    }

    public String getTrueName() {
        this.nodifyGetFiled("trueName");
        return trueName;
    }

    public OmsOrderLogEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public OmsOrderLogEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public OmsOrderLogEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

}
