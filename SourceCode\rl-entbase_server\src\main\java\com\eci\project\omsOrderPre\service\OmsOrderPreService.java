package com.eci.project.omsOrderPre.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.Log;
import com.eci.common.NoManager;
import com.eci.common.Zsr;
import com.eci.common.ZsrJson;
import com.eci.common.db.DBHelper;
import com.eci.common.enums.Enums;
import com.eci.common.enums.OrderEnum;
import com.eci.common.util.StringUtils;
import com.eci.common.utils.CommonLib;
import com.eci.common.validations.ZsrValidationUtil;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.exception.BaseException;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.dhlResponseRecord.service.DhlResponseRecordService;
import com.eci.project.fzgjTaskLimitationPt.dao.FzgjTaskLimitationPtDao;
import com.eci.project.fzgjTaskLimitationPt.entity.FzgjTaskLimitationPtEntity;
import com.eci.project.omsISlResult.dao.OmsISlResultDao;
import com.eci.project.omsISlResult.entity.OmsISlResultEntity;
import com.eci.project.omsOrder.dao.OmsOrderDao;
import com.eci.project.omsOrder.entity.OmsOrderEntity;
import com.eci.project.omsOrder.entity.RequestOmsOrderTracePageEntity;
import com.eci.project.omsOrder.service.OmsOrderService;
import com.eci.project.omsOrderFw.dao.OmsOrderFwDao;
import com.eci.project.omsOrderFw.entity.OmsOrderFwEntity;
import com.eci.project.omsOrderFwxm.dao.OmsOrderFwxmDao;
import com.eci.project.omsOrderFwxm.entity.OmsOrderFwxmEntity;
import com.eci.project.omsOrderFwxmWork.dao.OmsOrderFwxmWorkDao;
import com.eci.project.omsOrderFwxmWork.entity.OmsOrderFwxmWorkEntity;
import com.eci.project.omsOrderFwxmWork.service.OmsOrderFwxmWorkService;
import com.eci.project.omsOrderFwxmWorkTrace.dao.OmsOrderFwxmWorkTraceDao;
import com.eci.project.omsOrderFwxmWorkTrace.entity.OmsOrderFwxmWorkTraceEntity;
import com.eci.project.omsOrderFwxmWorkTrace.service.OmsOrderFwxmWorkTraceService;
import com.eci.project.omsOrderFwxmWorkTrace.service.OmsOrderFwxmWorkTraceZsrService;
import com.eci.project.omsOrderLog.entity.OmsOrderLogEntity;
import com.eci.project.omsOrderLog.service.OmsOrderLogService;
import com.eci.project.omsOrderPre.dao.OmsOrderPreDao;
import com.eci.project.omsOrderPre.entity.OmsOrderPreEntity;
import com.eci.project.omsOrderPre.entity.ReqOmsOrderPrePageEntity;
import com.eci.project.omsOrderPre.entity.ReqOmsOrderPreStatusEntity;
import com.eci.project.omsOrderPre.entity.ResOmsOrderPrePageEntity;
import com.eci.project.omsOrderPre.validate.OmsOrderPreVal;
import com.eci.sso.login.entity.UserInfo;
import com.eci.sso.role.entity.UserContext;
import com.eci.wu.core.EntityBase;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 客户委托单Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-04-15
 */
@Service
@Slf4j
public class OmsOrderPreService implements EciBaseService<OmsOrderPreEntity> {

    CommonLib cmn = CommonLib.getInstance();
    @Autowired
    private OmsOrderPreDao omsOrderPreDao;
    @Autowired
    private OmsOrderPreVal omsOrderPreVal;

    @Autowired
    private OmsOrderDao omsOrderDao;

    @Autowired
    private OmsOrderFwxmWorkDao omsOrderFwxmWorkDao;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OmsISlResultDao omsISlResultDao;

    @Autowired
    private OmsOrderFwxmWorkService omsOrderFwxmWorkService;

    @Autowired
    private OmsOrderFwxmWorkTraceService omsOrderFwxmWorkTraceService;

    @Autowired
    private DhlResponseRecordService dhlResponseRecordService;

    @Autowired
    private OmsOrderFwxmWorkTraceZsrService omsOrderFwxmWorkTraceZsrService;

    /**
     * 订单操作日志
     */
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    /**
     * 订单的服务类型
     */
    @Autowired
    private OmsOrderFwDao omsOrderFwDao;
    /**
     * 订单服务项目
     */
    @Autowired
    private OmsOrderFwxmDao omsOrderFwxmDao;
    @Autowired
    private FzgjTaskLimitationPtDao fzgjTaskLimitationPtDao;
    @Autowired
    private OmsOrderFwxmWorkTraceDao omsOrderFwxmWorkTraceDao;

    @Override
    public TgPageInfo queryPageList(OmsOrderPreEntity entity) {
        EciQuery<OmsOrderPreEntity> eciQuery = EciQuery.buildQuery(entity);
        List<OmsOrderPreEntity> entities = omsOrderPreDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }

    /**
     * 重写-分页列表查询
     */
    public TgPageInfo selectOmsPageList(ReqOmsOrderPrePageEntity entity) {
        String sql = selectPageSql(entity);
        startPage();
        List<ResOmsOrderPrePageEntity> entities = DBHelper.selectList(sql, ResOmsOrderPrePageEntity.class);
        return EciQuery.getPageInfo(entities);
    }

    /**
     * 重写-分页列表查询-自助下单待提交
     */
    public TgPageInfo selectPageListZiZhu(ReqOmsOrderPrePageEntity entity) {
//        String sql = selectPageSqlForZiZhu(entity);
//        startPage();
//        List<ResOmsOrderPrePageEntity> entities = DBHelper.selectList(sql, ResOmsOrderPrePageEntity.class);
//        return EciQuery.getPageInfo(entities);

        EciQuery<OmsOrderPreEntity> eciQuery = EciQuery.buildQuery(entity);
//        // 待审核列表条件
        eciQuery.and(e -> e.eq(OmsOrderPreEntity::getStatus, Enums.PreOrderStatus.ZC.getCode())
                .or()
                .eq(OmsOrderPreEntity::getStatus, Enums.PreOrderStatus.SHTH.getCode()
                ))  ;
        eciQuery.and(e -> e.ne(OmsOrderPreEntity::getAuditStatus, Enums.PreOrderCh.TG.getCode())
                .or()
                .isNull(OmsOrderPreEntity::getAuditStatus));

        List<OmsOrderPreEntity> entities = omsOrderPreDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }

    /**
     * 重写-分页列表查询-待审核
     */
    public TgPageInfo selectPageListZiZhuDaiShen(ReqOmsOrderPrePageEntity entity) {

        EciQuery<OmsOrderPreEntity> eciQuery = EciQuery.buildQuery(entity);
//        // 待审核列表条件
        eciQuery.eq(OmsOrderPreEntity::getStatus, Enums.PreOrderStatus.XDDS.getCode());
        eciQuery.and(e -> e.ne(OmsOrderPreEntity::getAuditStatus, Enums.PreOrderCh.TG.getCode())
                .or()
                .isNull(OmsOrderPreEntity::getAuditStatus));

        List<OmsOrderPreEntity> entities = omsOrderPreDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }

    /**
     * 重写-分页列表查询-最新作业状态
     */
    public TgPageInfo selectPageListZuoYeZhuangTai(ReqOmsOrderPrePageEntity entity) {
        List<String> statusList = Arrays.asList(
                Enums.PreOrderStatus.YSL.getCode(),
                Enums.PreOrderStatus.ZYWC.getCode()
        );
        EciQuery<OmsOrderPreEntity> eciQuery = EciQuery.buildQuery(entity);
//        // 状态条件
        eciQuery.and(e -> e.in(OmsOrderPreEntity::getStatus, statusList));
        eciQuery.and(e -> e.eq(OmsOrderPreEntity::getAuditStatus, Enums.PreOrderCh.TG.getCode()));

        List<OmsOrderPreEntity> entities = omsOrderPreDao.queryPageList(eciQuery);

        TgPageInfo<OmsOrderPreEntity> pageInfo = EciQuery.getPageInfo(entities);
        pageInfo.getList().forEach(e -> {
            Map<String, String> stringMap = getLatestCompletedNodeName(e);
            e.push("zuoYeStatus", String.join(",", stringMap.values()));
        });

        return pageInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderPreEntity save(OmsOrderPreEntity entity) {
        // 返回实体对象
        OmsOrderPreEntity omsOrderPreEntity = null;
        omsOrderPreVal.saveValidate(entity, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            omsOrderPreEntity = omsOrderPreDao.insertOne(entity);

        } else {

            omsOrderPreEntity = omsOrderPreDao.updateByEntityId(entity);

        }
        return omsOrderPreEntity;
    }

    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderPreEntity saveZiZhuXiaDan(OmsOrderPreEntity entity) {
        // 缓存用户信息，避免多次调用
        UserInfo userInfo = UserContext.getUserInfo();
        String trueName = userInfo.getTrueName();
        String companyCode = userInfo.getCompanyCode();
        ZsrValidationUtil.validation(entity);
        OmsOrderPreEntity omsOrderEntity = null;

        if (BllContext.getBusinessType() == BusinessType.INSERT || Zsr.String.IsNullOrWhiteSpace(entity.getPreNo())) {
            // 使用订单号创建生成器生成订单号，1表示手动创建的订单
            entity.setPreNo(NoManager.createOrderPreNo());
            // 新增的订单，初始状态为暂存
            entity.setStatus(Enums.PreOrderStatus.ZC.getCode());
            // 新增订单，订单执行阶段初始化状态为暂存
            entity.setStage(OrderEnum.OrderStage.ZC.getCode());
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
            entity.setXdDate(new Date());
            entity.setSysCode("CSC");
            entity.setSysDocNo("CSC");
            omsOrderEntity = omsOrderPreDao.insertOne(entity);

            // 2. 更新服务类型
            handleServiceTypes(omsOrderEntity, trueName, companyCode);

            // 3. 更新服务项目
            handleServiceTypesItem(omsOrderEntity, trueName, companyCode);

            // 操作日志记录
            OmsOrderLogEntity logEntity = new OmsOrderLogEntity();
            logEntity.setOrderNo(entity.getPreNo());
            logEntity.setOperName("新增订单");
            logEntity.setBizType(OrderEnum.OrderLogStatus.ZC.getCode());
            omsOrderLogService.writeLog(logEntity);
        } else { // 更新逻辑
            // 1. 更新订单主表信息
            omsOrderEntity = omsOrderPreDao.updateByEntityId(entity);

            // 2. 更新服务类型
            updateServiceTypes(entity, trueName, companyCode);

            // 3. 更新服务项目
            updateServiceTypesItem(entity, trueName, companyCode);

            // 操作日志记录
            OmsOrderLogEntity logEntity = new OmsOrderLogEntity();
            logEntity.setOrderNo(entity.getPreNo());
            logEntity.setOperName("修改订单");
            omsOrderLogService.writeLog(logEntity);
        }

        return omsOrderEntity;
    }

    private void updateServiceTypes(OmsOrderPreEntity entity, String trueName, String companyCode) {
        String newFwlxCode = entity.getFwlxCode();
        if (Zsr.String.IsNullOrWhiteSpace(newFwlxCode)) {
            return;
        }

        String preNo = entity.getPreNo();
        List<OmsOrderFwEntity> existingFwList = omsOrderFwDao.select()
                .eq(OmsOrderFwEntity::getPreNo, preNo)
                .list();

        List<String> newFwCodeList = Zsr.String.string2List(newFwlxCode);
        Set<String> existingFwCodeSet = existingFwList.stream()
                .map(OmsOrderFwEntity::getFwlxCode)
                .collect(Collectors.toSet());

        // 需要新增的服务类型
        List<String> toAddFwCodeList = newFwCodeList.stream()
                .filter(fwCode -> !existingFwCodeSet.contains(fwCode))
                .collect(Collectors.toList());

        // 执行新增
        toAddFwCodeList.forEach(fw -> {
            OmsOrderFwEntity orderFwEntity = new OmsOrderFwEntity();
            orderFwEntity.setGuid(IdWorker.getIdStr());
            orderFwEntity.setPreNo(preNo);
            orderFwEntity.setFwlxCode(fw);
            orderFwEntity.setCreateDate(new Date());
            orderFwEntity.setCreateUser(trueName);
            orderFwEntity.setNodeCode(companyCode);
            orderFwEntity.setCompanyCode(companyCode);
            orderFwEntity.setGroupCode(companyCode);
            omsOrderFwDao.insertOne(orderFwEntity);
        });

        // 需要删除的服务类型
        List<String> toDeleteFwCodeList = existingFwList.stream()
                .filter(fw -> !newFwCodeList.contains(fw.getFwlxCode()))
                .map(OmsOrderFwEntity::getFwlxCode)
                .collect(Collectors.toList());

        if (!toDeleteFwCodeList.isEmpty()) {
            omsOrderFwDao.delete()
                    .eq(OmsOrderFwEntity::getPreNo, preNo)
                    .in(OmsOrderFwEntity::getFwlxCode, toDeleteFwCodeList)
                    .execute();
        }
    }

    /**
     * 新增的时候，增加服务项目
     *
     * @param entity
     * @param trueName
     * @param companyCode
     */
    private void handleServiceTypesItem(OmsOrderPreEntity entity, String trueName, String companyCode) {
        String fwlxItem = entity.getFwlxItem();
        if (!Zsr.String.IsNullOrWhiteSpace(fwlxItem)) {
            List<String> fwCodeList = Zsr.String.string2List(fwlxItem);
            String orderNo = entity.getPreNo();

            fwCodeList.forEach(fw -> {
                OmsOrderFwxmEntity fwEntity = omsOrderFwxmDao.select()
                        .eq(OmsOrderFwxmEntity::getFwlxCode, fw)
                        .eq(OmsOrderFwxmEntity::getPreNo, orderNo).one();

                if (fwEntity == null) {
                    OmsOrderFwxmEntity fwxmEntity = new OmsOrderFwxmEntity();
                    fwxmEntity.setPreNo(orderNo);
                    fwxmEntity.setFwlxCode(Zsr.String.safeSubstring(fw, 3));
                    fwxmEntity.setFwxmCode(fw);
                    fwxmEntity.setCreateDate(new Date());
                    fwxmEntity.setCreateUser(trueName);
                    fwxmEntity.setNodeCode(companyCode);
                    fwxmEntity.setCompanyCode(companyCode);
                    fwxmEntity.setGroupCode(companyCode);
                    omsOrderFwxmDao.insertOne(fwxmEntity);
                }
            });
        }
    }

    /**
     * 新增的时候，新增服务类型
     *
     * @param entity
     * @param trueName
     * @param companyCode
     */
    private void handleServiceTypes(OmsOrderPreEntity entity, String trueName, String companyCode) {
        String fwlxCode = entity.getFwlxCode();
        if (!Zsr.String.IsNullOrWhiteSpace(fwlxCode)) {
            List<String> fwCodeList = Zsr.String.string2List(fwlxCode);
            String orderNo = entity.getPreNo();

            fwCodeList.forEach(fw -> {
                OmsOrderFwEntity fwEntity = omsOrderFwDao.select()
                        .eq(OmsOrderFwEntity::getFwlxCode, fw)
                        .eq(OmsOrderFwEntity::getPreNo, orderNo).one();

                if (fwEntity == null) {
                    OmsOrderFwEntity orderFwEntity = new OmsOrderFwEntity();
                    orderFwEntity.setPreNo(orderNo);
                    orderFwEntity.setFwlxCode(fw);
                    orderFwEntity.setCreateDate(new Date());
                    orderFwEntity.setCreateUser(trueName);
                    orderFwEntity.setNodeCode(companyCode);
                    orderFwEntity.setCompanyCode(companyCode);
                    orderFwEntity.setGroupCode(companyCode);
                    omsOrderFwDao.insertOne(orderFwEntity);
                }
            });
        }
    }

    /**
     * 跟新服务项目
     *
     * @param entity
     * @param trueName
     * @param companyCode
     */
    private void updateServiceTypesItem(OmsOrderPreEntity entity, String trueName, String companyCode) {
        String fwlxItem = entity.getFwlxItem();
        if (Zsr.String.IsNullOrWhiteSpace(fwlxItem)) {
            return;
        }

        String preNo = entity.getPreNo();

        // 查询已有服务项
        List<OmsOrderFwxmEntity> existingFwList = omsOrderFwxmDao.select()
                .eq(OmsOrderFwxmEntity::getPreNo, preNo)
                .list();

        // 新传入的 fwxmCode 列表
        List<String> newFwCodeList = Zsr.String.string2List(fwlxItem);

        // 已有的 fwxmCode 集合
        Set<String> existingFwCodeSet = existingFwList.stream()
                .map(OmsOrderFwxmEntity::getFwxmCode)
                .collect(Collectors.toSet());

        // 需要新增的服务项
        List<String> toAddFwCodeList = newFwCodeList.stream()
                .filter(fwCode -> !existingFwCodeSet.contains(fwCode))
                .collect(Collectors.toList());

        // 执行新增
        toAddFwCodeList.forEach(fwCode -> {
            OmsOrderFwxmEntity orderFwxmEntity = new OmsOrderFwxmEntity();
            orderFwxmEntity.setGuid(IdWorker.getIdStr());
            orderFwxmEntity.setPreNo(preNo);
            orderFwxmEntity.setFwlxCode(Zsr.String.safeSubstring(fwCode, 3)); // 根据业务决定怎么处理
            orderFwxmEntity.setFwxmCode(fwCode);
            orderFwxmEntity.setCreateDate(new Date());
            orderFwxmEntity.setCreateUser(trueName);
            orderFwxmEntity.setNodeCode(companyCode);
            orderFwxmEntity.setCompanyCode(companyCode);
            orderFwxmEntity.setGroupCode(companyCode);
            omsOrderFwxmDao.insertOne(orderFwxmEntity);
        });

        // 需要删除的服务项
        List<String> toDeleteFwCodeList = existingFwList.stream()
                .filter(fw -> !newFwCodeList.contains(fw.getFwxmCode())) // ✅ 改成用 fwxmCode 对比
                .map(OmsOrderFwxmEntity::getFwxmCode)
                .collect(Collectors.toList());

        if (!toDeleteFwCodeList.isEmpty()) {
            omsOrderFwxmDao.delete()
                    .eq(OmsOrderFwxmEntity::getPreNo, preNo)
                    .in(OmsOrderFwxmEntity::getFwxmCode, toDeleteFwCodeList)
                    .execute();
        }
    }

    @Override
    public List<OmsOrderPreEntity> selectList(OmsOrderPreEntity entity) {
        return omsOrderPreDao.selectList(entity);
    }

    @Override
    public OmsOrderPreEntity selectOneById(Serializable id) {
        return omsOrderPreDao.selectById(id);
    }

    /**
     * 加载
     **/
    public ResOmsOrderPrePageEntity selectOneAudit(String preNo) {
        if (!StringUtils.hasValue(preNo)) {
            throw new BaseException("perNo参数为空");
        }

        String sql = "  SELECT\n" +
                "        (SELECT X.ORDER_NO FROM OMS_ORDER X WHERE X.PRE_NO = B.PRE_NO AND ROWNUM=1) AS ORDER_NO,\n" +
                "         B.PRE_NO,--1协同编号\n" +
                "         B.SYS_CODE,  --2来源系统\n" +
                "         B.SYS_DOC_NO,--3来源系统业务编号\n" +
                "         B.XD_DATE,--4下单时间\n" +
                "         B.XD_USER, --5下单人\n" +
                "        (SELECT TRUENAME FROM FZGJ_SSO_USER A WHERE STATUS='Y' AND  A.USERNAME = B.XD_USER AND ROWNUM=1) AS XD_USER_NAME, -- --5下单人\n" +
                "         B.CONSIGNEE_CODE, --6委托方\n" +
                "         (SELECT NAME  FROM CRM_CUSTOMER A WHERE A.STATUS='Y' AND A.GROUP_CODE = B.GROUP_CODE AND A.CODE = B.CONSIGNEE_CODE AND ROWNUM=1) CONSIGNEE_CODE_NAME , --委托方名称\n" +
                "         B.CUSTOMER_BU, --7客户事业部\n" +
                "         (SELECT MAX(A.NAME) FROM CRM_CUSTOMER_KHSYB A WHERE A.STATUS = 'Y' AND A.GROUP_CODE = B.GROUP_CODE  AND CODE = B.CUSTOMER_BU AND ROWNUM=1) CUSTOMER_BU_NAME," +
                "         B.SHIPPER,--8实际发货方\n" +
                "         B.RECEIVER,--9实际收货方\n" +
                "        (SELECT A.NAME FROM CRM_CUSTOMER A WHERE A.STATUS='Y' AND A.CODE= B.SHIPPER AND A.GROUP_CODE=B.GROUP_CODE AND ROWNUM=1 ) AS SHIPPER_NAME ,----9实际发货方\n" +
                "        (SELECT A.NAME FROM CRM_CUSTOMER A WHERE A.STATUS='Y' AND A.CODE= B.RECEIVER AND A.GROUP_CODE=B.GROUP_CODE AND ROWNUM=1) AS RECEIVER_NAME, ----9实际发货方\n" +
                "         B.OP_DATE,--10业务日期\n" +
                "         DECODE(B.IS_JJH,'Y','是','1','是','否') IS_JJH, --11急货\n" +
                "         B.OP_TYPE,--12业务类型\n" +
                "         (SELECT  NAME  FROM FZGJ_BD_OP_TYPE A  WHERE A.STATUS = 'Y'  AND A.SYS_CODE = 'OMS_ORDER' AND CODE=B.OP_TYPE AND ROWNUM=1) OP_TYPE_NAME ,--业务类型名称\n" +
                "         B.PRODUCT_CODE, --13业务产品/项目\n" +
                "        (SELECT F.NAME FROM FZGJ_BD_PRODUCT F WHERE F.BILL_CODE='OMS_ORDER' AND F.GROUP_CODE = B.GROUP_CODE AND F.CODE = B.PRODUCT_CODE AND F.OP_TYPE = B.OP_TYPE AND ROWNUM=1) AS PRODUCT_CODE_NAME ,--业务产品/项目名称\n" +
                "         B.FWLX_NAME, --14服务类型\n" +
                "         B.CUSTOMER_ORDER_NO, --15客户单据号\n" +
                "         B.REQUEST_OK_DATE, --16要求完成时间\n" +
                "         B.TEL,   --17联系电话\n" +
                "         B.E_MAIL, --18电子邮箱\n" +
                "         B.BIZ_MEMO, --19业务备注\n" +
                "         B.AUDIT_MEMO, --20审核备注\n" +
                "         DECODE(B.AUDIT_STATUS,'TG','通过','TH','退回','') AUDIT_STATUS, --21审核状态\n" +
                "         B.AUDIT_USER_NAME AUDIT_USER, --22审核人\n" +
                "         B.AUDIT_DATE, --23审核时间\n" +
                "         B.GROUP_CODE,\n" +
                "         B.BIZ_REG_ID,\n" +
                "         (SELECT NAME  FROM FZGJ_EXTEND_DATA DATA,OMS_ORDER_FWXM_TMS TMS, OMS_ORDER_FWXM_TMS_XL X\n" +
                "    WHERE DATA.STATUS = 'Y'\n" +
                "    AND DATA.CODE = X.CROSS_ITEM AND DATA.GROUP_CODE = X.GROUP_CODE\n" +
                "    AND TMS.TMS_NO = X.TMS_NO AND TMS.ORDER_NO = B.SYS_DOC_NO AND ROWNUM=1) CROSS_ITEM_NAME," +
                "    (SELECT NAME  FROM FZGJ_EXTEND_DATA DATA,OMS_ORDER_FWXM_TMS TMS, OMS_ORDER_FWXM_TMS_XL X\n" +
                "    WHERE DATA.STATUS = 'Y'\n" +
                "    AND DATA.CODE = X.CROSS_LINE AND DATA.GROUP_CODE = X.GROUP_CODE\n" +
                "    AND TMS.TMS_NO = X.TMS_NO AND TMS.ORDER_NO = B.SYS_DOC_NO AND ROWNUM=1) CROSS_LINE_NAME " +
                " FROM OMS_ORDER_PRE B";
        sql += " WHERE 1=1 ";

        sql += " AND PRE_NO=" + cmn.SQLQ(preNo);

        List<ResOmsOrderPrePageEntity> list = DBHelper.selectList(sql, ResOmsOrderPrePageEntity.class);
        return list != null && list.size() > 0 ? list.get(0) : null;
    }

    /**
     * 加载
     **/
    public OmsOrderPreEntity selectOneZiZhu(String preNo) {
        if (!StringUtils.hasValue(preNo)) {
            throw new BaseException("perNo参数为空");
        }

        List<OmsOrderPreEntity> omsOrderPreEntities = omsOrderPreDao.select()
                .eq(OmsOrderPreEntity::getPreNo, preNo)
                .list();
        if (omsOrderPreEntities == null || omsOrderPreEntities.size() <= 0) {
            return new OmsOrderPreEntity();
        }
        OmsOrderPreEntity omsOrderPreEntity = omsOrderPreEntities.get(0);

        List<OmsOrderEntity> omsOrderEntities = omsOrderDao.select()
                .eq(OmsOrderEntity::getPreNo, preNo)
                .list();
        OmsOrderEntity orderEntity = omsOrderEntities.size() > 0 ? omsOrderEntities.get(0) : null;

        if (orderEntity == null) {
            omsOrderPreEntity.push("isShow", false);
            omsOrderPreEntity.push("orderNo", "");
        } else {
            omsOrderPreEntity.push("isShow", true);
            omsOrderPreEntity.push("orderNo", orderEntity.getOrderNo());
        }

        return omsOrderPreEntity;
    }

    @Override
    public void insertBatch(List<OmsOrderPreEntity> list) {
        omsOrderPreDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return omsOrderPreDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return omsOrderPreDao.deleteById(id);
    }

    /**
     * 修改审核状态
     */
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderPreEntity updateSHStatus(ReqOmsOrderPreStatusEntity entity) {

        OmsOrderPreEntity omsOrderPreEntity = null;

        OmsOrderEntity orderRe = new OmsOrderEntity();

        if (StringUtils.isEmpty(entity.getPreNo())) {
            throw new BaseException("协同编码为空");
        }
        if (StringUtils.isEmpty(entity.getType())) {
            throw new BaseException("审核状态为空");
        }

        // 1-查询订单是否存在
        OmsOrderPreEntity omsOrderPreQueryEntity = new OmsOrderPreEntity();
        omsOrderPreQueryEntity.setPreNo(entity.getPreNo());
        OmsOrderPreEntity preOrderEntity = omsOrderPreDao.selectOne(omsOrderPreQueryEntity);
        if (preOrderEntity == null) {
            throw new BaseException("协同订单不存在");
        }
        if (!preOrderEntity.getGroupCode().equals(UserContext.getUserInfo().getCompanyCode())) {
            throw new BaseException("当前登陆用户所属集团与该数据不是同一集团，不能审核！");
        }

        // 2-审核状态
        if (entity.getType().equals(Enums.PreOrderCh.TG.getCode())) { // 通过
            preOrderEntity.setAuditDate(new java.util.Date());
            preOrderEntity.setAuditNode(UserContext.getUserInfo().getDeptCode());
            preOrderEntity.setAuditNodeName(UserContext.getUserInfo().getDeptName());
            preOrderEntity.setAuditUser(UserContext.getUserInfo().getUserLoginNo());
            preOrderEntity.setAuditUserName(UserContext.getUserInfo().getTrueName());
            preOrderEntity.setAuditStatus(Enums.PreOrderCh.TG.getCode());
            preOrderEntity.setUpdateDate(new java.util.Date());
            preOrderEntity.setJdUser(preOrderEntity.getAuditUser());
            preOrderEntity.setJdUserName(preOrderEntity.getAuditUserName());
            preOrderEntity.setJdNode(preOrderEntity.getAuditNode());
            preOrderEntity.setJdCompany(UserContext.getUserInfo().getCompanyCode());
            preOrderEntity.setJdGroupCode(UserContext.getUserInfo().getCompanyCode());
            preOrderEntity.setStatus(Enums.PreOrderStatus.YSL.getCode());
            omsOrderPreEntity = omsOrderPreDao.updateByEntityId(preOrderEntity);

            // omsOrder中是否有作废订单
            OmsOrderEntity omsOrderQueryEntity = new OmsOrderEntity();
            omsOrderQueryEntity.setPreNo(entity.getPreNo());
            omsOrderQueryEntity.setStatus(Enums.OrderStatus.ZF.getCode());
            omsOrderQueryEntity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            OmsOrderEntity oldOrder = omsOrderDao.selectOne(omsOrderQueryEntity);
            if (oldOrder != null) {  // 如果是作废退回然后重新审核的协同订单
                oldOrder.setShipper(preOrderEntity.getShipper());
                oldOrder.setReceiver(preOrderEntity.getReceiver());
                oldOrder.setCustomerOrderNo(preOrderEntity.getCustomerOrderNo());
                oldOrder.setOpType(preOrderEntity.getOpType());
                oldOrder.setProductCode(preOrderEntity.getProductCode());
                oldOrder.setIsJjh(preOrderEntity.getIsJjh());
                oldOrder.setOpDate(preOrderEntity.getOpDate());
                oldOrder.setIsQrjd(preOrderEntity.getIsQrjd());
                oldOrder.setIsXzff(Enums.YNStatus.N.getCode());
                oldOrder.setStatus(Enums.OrderStatus.ZC.getCode());
                oldOrder.setIsCancel(Enums.YNStatus.N.getCode());
                oldOrder.setCancelDate(null);
                oldOrder.setCancelNode(null);
                oldOrder.setCancelNodeName(null);
                oldOrder.setCancelUser(null);
                oldOrder.setCancelUserName(null);
                omsOrderDao.updateByEntityId(oldOrder);

                orderRe = oldOrder;

            } else {
                OmsOrderEntity addOrderEntity = new OmsOrderEntity();

                BeanUtils.copyProperties(preOrderEntity, addOrderEntity);

                addOrderEntity.setConfirmDate(new java.util.Date());
                addOrderEntity.setJdUser(preOrderEntity.getAuditUser());
                addOrderEntity.setJdNode(preOrderEntity.getAuditNode());
                addOrderEntity.setJdCompany(UserContext.getUserInfo().getCompanyCode());
                addOrderEntity.setFromData(OrderEnum.OrderFromData.XT.getCode());
                addOrderEntity.setBizRegId(preOrderEntity.getBizRegId());
                addOrderEntity.setIsQrjd(preOrderEntity.getIsQrjd());
                addOrderEntity.setIsXzff(Enums.YNStatus.N.getCode());

                // 将账册号根据收货公司默认带出到订单对应栏位里
                List<EntityBase> consineeCodeEntity = orderConsineeCodeSearch(addOrderEntity.getReceiver());
                if (consineeCodeEntity != null && consineeCodeEntity.size() > 0) {
                    addOrderEntity.setEmsNo(consineeCodeEntity.get(0).getString("NBS_EMS_NO"));
                }

                // 新增订单
                addOrderEntity = omsOrderService.save(addOrderEntity, true, OrderEnum.OrderSaveType.XTSH.getCode(), "");

                orderRe = addOrderEntity;
            }

            // 修改订单信息的 ORDER_NO
            updateOrderInfoOrderNo(preOrderEntity.getPreNo(), orderRe.getOrderNo());
            // 调用生成WorkTrace方法
            omsOrderFwxmWorkTraceZsrService.WorkTraceAllSave(orderRe.getOrderNo());
            // 修改协同订单WORK_NO使之与OMS订单WORK_NO生成规则一致;
            // omsOrderFwxmWorkService.resetWorkNo(orderRe.getPreNo(), orderRe.getOrderNo());
            // 调用生成WorkTrace方法
//            if (StringUtils.hasValue(orderRe.getOrderNo())) {
 //              omsOrderFwxmWorkTraceZsrService.WorkTraceAllSave(orderRe.getOrderNo());
//            } else if (StringUtils.hasValue(orderRe.getPreNo())) {
//                omsOrderFwxmWorkTraceZsrService.WorkTraceAllSaveZiZhuXiaDan(orderRe.getPreNo());
//            }

        } else { // 退回

            if (StringUtils.isEmpty(entity.getAuditMemo())) {
                throw new BaseException("退回原因为空");
            }

            preOrderEntity.setAuditDate(new java.util.Date());
            preOrderEntity.setAuditNode(UserContext.getUserInfo().getDeptCode());
            preOrderEntity.setAuditNodeName(UserContext.getUserInfo().getDeptName());
            preOrderEntity.setAuditUser(UserContext.getUserInfo().getUserLoginNo());
            preOrderEntity.setAuditUserName(UserContext.getUserInfo().getTrueName());
            preOrderEntity.setAuditStatus(Enums.PreOrderCh.TH.getCode()); // 退回状态
            preOrderEntity.setAuditMemo(entity.getAuditMemo()); // 退回原因
            preOrderEntity.setStatus(Enums.PreOrderStatus.SHTH.getCode());
            preOrderEntity.setUpdateDate(new java.util.Date());
            omsOrderPreEntity = omsOrderPreDao.updateByEntityId(preOrderEntity);
        }

        if (StringUtils.hasValue(preOrderEntity.getSysCode()) && preOrderEntity.getSysCode().equals("OMS")) {  // SYS_CODE = OMS  需要返回审核结果
            xzffToOmsResult(preOrderEntity, orderRe.getOrderNo());
        }

        return omsOrderPreEntity;
    }

    /**
     * 生成列表查询sql语句
     */
    public String selectPageSql(ReqOmsOrderPrePageEntity entity) {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        String sql = "";

        sql += "  SELECT\n" +
                "        (SELECT X.ORDER_NO FROM OMS_ORDER X WHERE X.PRE_NO = B.PRE_NO AND ROWNUM=1) AS ORDER_NO,\n" +
                "         B.PRE_NO,--1协同编号\n" +
                "         B.SYS_CODE,  --2来源系统\n" +
                "         B.SYS_DOC_NO,--3来源系统业务编号\n" +
                "         B.XD_DATE,--4下单时间\n" +
                "         B.XD_USER, --5下单人\n" +
                "        (SELECT TRUENAME FROM FZGJ_SSO_USER A WHERE STATUS='Y' AND  A.USERNAME = B.XD_USER AND ROWNUM=1) AS XD_USER_NAME, -- --5下单人\n" +
                "         B.CONSIGNEE_CODE, --6委托方\n" +
                "         (SELECT NAME  FROM CRM_CUSTOMER A WHERE A.STATUS='Y' AND A.GROUP_CODE = B.GROUP_CODE AND A.CODE = B.CONSIGNEE_CODE AND ROWNUM=1) CONSIGNEE_CODE_NAME , --委托方名称\n" +
                "         B.CUSTOMER_BU, --7客户事业部\n" +
                "         (SELECT MAX(A.NAME) FROM CRM_CUSTOMER_KHSYB A WHERE A.STATUS = 'Y' AND A.GROUP_CODE = B.GROUP_CODE  AND CODE = B.CUSTOMER_BU AND ROWNUM=1) CUSTOMER_BU_NAME," +
                "         B.SHIPPER,--8实际发货方\n" +
                "         B.RECEIVER,--9实际收货方\n" +
                "        (SELECT A.NAME FROM CRM_CUSTOMER A WHERE A.STATUS='Y' AND A.CODE= B.SHIPPER AND A.GROUP_CODE=B.GROUP_CODE AND ROWNUM=1 ) AS SHIPPER_NAME ,----9实际发货方\n" +
                "        (SELECT A.NAME FROM CRM_CUSTOMER A WHERE A.STATUS='Y' AND A.CODE= B.RECEIVER AND A.GROUP_CODE=B.GROUP_CODE AND ROWNUM=1) AS RECEIVER_NAME, ----9实际发货方\n" +
                "         B.OP_DATE,--10业务日期\n" +
                "         DECODE(B.IS_JJH,'Y','是','1','是','否') IS_JJH, --11急货\n" +
                "         B.OP_TYPE,--12业务类型\n" +
                "         (SELECT  NAME  FROM FZGJ_BD_OP_TYPE A  WHERE A.STATUS = 'Y'  AND A.SYS_CODE = 'OMS_ORDER' AND CODE=B.OP_TYPE AND ROWNUM=1) OP_TYPE_NAME ,--业务类型名称\n" +
                "         B.PRODUCT_CODE, --13业务产品/项目\n" +
                "        (SELECT F.NAME FROM FZGJ_BD_PRODUCT F WHERE F.BILL_CODE='OMS_ORDER' AND F.GROUP_CODE = B.GROUP_CODE AND F.CODE = B.PRODUCT_CODE AND F.OP_TYPE = B.OP_TYPE AND ROWNUM=1) AS PRODUCT_CODE_NAME ,--业务产品/项目名称\n" +
                "         B.FWLX_NAME, --14服务类型\n" +
                "         B.CUSTOMER_ORDER_NO, --15客户单据号\n" +
                "         B.REQUEST_OK_DATE, --16要求完成时间\n" +
                "         B.TEL,   --17联系电话\n" +
                "         B.E_MAIL, --18电子邮箱\n" +
                "         B.BIZ_MEMO, --19业务备注\n" +
                "         B.AUDIT_MEMO, --20审核备注\n" +
                "         DECODE(B.AUDIT_STATUS,'TG','通过','TH','退回','') AUDIT_STATUS, --21审核状态\n" +
                "         B.AUDIT_USER_NAME AUDIT_USER, --22审核人\n" +
                "         B.AUDIT_DATE, --23审核时间\n" +
                "         B.GROUP_CODE,\n" +
                "         B.BIZ_REG_ID,\n" +
                "         (SELECT NAME  FROM FZGJ_EXTEND_DATA DATA,OMS_ORDER_FWXM_TMS TMS, OMS_ORDER_FWXM_TMS_XL X\n" +
                "    WHERE DATA.STATUS = 'Y'\n" +
                "    AND DATA.CODE = X.CROSS_ITEM AND DATA.GROUP_CODE = X.GROUP_CODE\n" +
                "    AND TMS.TMS_NO = X.TMS_NO AND TMS.ORDER_NO = B.SYS_DOC_NO AND ROWNUM=1) CROSS_ITEM_NAME," +
                "    (SELECT NAME  FROM FZGJ_EXTEND_DATA DATA,OMS_ORDER_FWXM_TMS TMS, OMS_ORDER_FWXM_TMS_XL X\n" +
                "    WHERE DATA.STATUS = 'Y'\n" +
                "    AND DATA.CODE = X.CROSS_LINE AND DATA.GROUP_CODE = X.GROUP_CODE\n" +
                "    AND TMS.TMS_NO = X.TMS_NO AND TMS.ORDER_NO = B.SYS_DOC_NO AND ROWNUM=1) CROSS_LINE_NAME " +
                " FROM OMS_ORDER_PRE B";
        sql += " WHERE 1=1 ";
        sql += " AND B.GROUP_CODE=" + cmn.SQLQ(UserContext.getUserInfo().getCompanyCode());

        /**查询条件*/
        // 来源系统业务编号
        if (StringUtils.isNotEmpty(entity.getSysDocNo())) {
            sql += " AND B.SYS_DOC_NO LIKE " + cmn.SQLQL(entity.getSysDocNo());
        }
        // 订单号
        if (StringUtils.isNotEmpty(entity.getOrderNo())) {
            sql += " AND EXISTS(SELECT 1 FROM OMS_ORDER OMSORDER  WHERE OMSORDER.PRE_NO =B.PRE_NO AND OMSORDER.ORDER_NO=" + cmn.SQLQL(entity.getOrderNo()) + ") ";
        }
        if (StringUtils.isNotEmpty(entity.getCustomerOrderNo())) {
            sql += " AND B.CUSTOMER_ORDER_NO LIKE " + cmn.SQLQL(entity.getCustomerOrderNo());
        }
        if (StringUtils.isNotEmpty(entity.getConsigneeCode())) {
            sql += " AND B.CONSIGNEE_CODE= " + cmn.SQLQ(entity.getConsigneeCode());
        }
        if (StringUtils.isNotEmpty(entity.getIsJjh())) {
            sql += " AND B.IS_JJH= " + cmn.SQLQ(entity.getIsJjh());
        }
        if (StringUtils.isNotEmpty(entity.getOpType())) {
            sql += " AND B.OP_TYPE= " + cmn.SQLQ(entity.getOpType());
        }
        if (StringUtils.isNotEmpty(entity.getProductCode())) {
            sql += " AND B.PRODUCT_CODE= " + cmn.SQLQ(entity.getProductCode());
        }
        if (StringUtils.isNotEmpty(entity.getFwlxCode())) {
            sql += " AND B.FWLX_CODE= " + cmn.SQLQ(entity.getFwlxCode());
        }
        if (StringUtils.isNotEmpty(entity.getPreNo())) {
            sql += " AND B.PRE_NO LIKE " + cmn.SQLQL(entity.getPreNo());
        }

        // 下单时间
        if (entity.getXdDateStart() != null) {
            String strStartXDDate = sdf.format(entity.getXdDateStart());
            sql += " AND B.XD_DATE>=TO_DATE(" + cmn.SQLQ(strStartXDDate) + ",'yyyy-MM-dd')";
        }
        if (entity.getXdDateEnd() != null) {
            String strEndXDDate = sdf.format(entity.getXdDateStart());
            sql += " AND B.XD_DATE<=TO_DATE(" + cmn.SQLQ(strEndXDDate) + ",'yyyy-MM-dd')";
        }

        // 要求完成日期
        if (entity.getRequestOkDateStart() != null) {
            String strStartRDate = sdf.format(entity.getRequestOkDateStart());
            sql += " AND B.REQUEST_OK_DATE>=TO_DATE(" + cmn.SQLQ(strStartRDate) + ",'yyyy-MM-dd')";
        }
        if (entity.getRequestOkDateEnd() != null) {
            String strEndRDate = sdf.format(entity.getRequestOkDateEnd());
            sql += " AND B.REQUEST_OK_DATE<=TO_DATE(" + cmn.SQLQ(strEndRDate) + ",'yyyy-MM-dd')";
        }

        if (StringUtils.isNotEmpty(entity.getShipper())) {
            sql += " AND B.SHIPPER LIKE " + cmn.SQLQL(entity.getShipper());
        }
        if (StringUtils.isNotEmpty(entity.getReceiver())) {
            sql += " AND B.RECEIVER LIKE " + cmn.SQLQL(entity.getReceiver());
        }
        // 项目
        if (StringUtils.isNotEmpty(entity.getCrossItem())) {
            sql += "AND EXISTS(SELECT 1 FROM OMS_ORDER_FWXM_TMS TMS, OMS_ORDER_FWXM_TMS_XL X WHERE TMS.ORDER_NO = B.SYS_DOC_NO\n" +
                    "AND TMS.TMS_NO = X.TMS_NO AND X.CROSS_ITEM = " + cmn.SQLQ(entity.getCrossItem()) + ")";
        }
        // 线路
        if (StringUtils.isNotEmpty(entity.getCrossLine())) {
            sql += " AND EXISTS(SELECT 1 FROM OMS_ORDER_FWXM_TMS TMS, OMS_ORDER_FWXM_TMS_XL X WHERE TMS.ORDER_NO = B.SYS_DOC_NO AND TMS.TMS_NO = X.TMS_NO AND X.CROSS_LINE = " + cmn.SQLQ(entity.getCrossLine()) + ")";
        }

        // 审核结果
        if (StringUtils.isNotEmpty(entity.getAuditStatus())) {
            sql += " AND B.AUDIT_STATUS=" + cmn.SQLQ(entity.getAuditStatus());
        }

        // 调用方法-设置查询权限
        sql += orderPreSelUserinfoSql(true, entity.getPageType());

        if (!Zsr.String.IsNullOrWhiteSpace(entity.getType())) {
            if (entity.getType().equals("Y")) { // 已审核列表条件
                sql += " AND (B.AUDIT_STATUS =" + cmn.SQLQ(Enums.PreOrderCh.TG.getCode()) + "or B.AUDIT_STATUS =" + cmn.SQLQ(Enums.PreOrderCh.TH.getCode()) + ")";
                sql += " ORDER BY B.XD_DATE DESC ";
            } else { // 待审核列表条件
                sql += " AND B.STATUS=" + cmn.SQLQ(Enums.PreOrderStatus.XDDS.getCode()) + " AND  (B.AUDIT_STATUS !=" + cmn.SQLQ(Enums.PreOrderCh.TG.getCode()) + "  OR B.AUDIT_STATUS IS NULL)";
                sql += " ORDER BY B.AUDIT_DATE DESC ";
            }
        }

        return sql;
    }

    /**
     * 生成列表查询sql语句
     */
    public String selectPageSqlForZiZhu(ReqOmsOrderPrePageEntity entity) {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        String sql = "";

        sql += "  SELECT B.CUSTOMER_ORDER_NO, --15客户单据号\n" +
                "         B.CONSIGNEE_CODE, --6委托方\n" +
                "         B.SHIPPER, --8实际发货方\n" +
                "         B.RECEIVER, --9实际收货方\n" +
                "         B.OP_DATE, --10业务日期\n" +
                "         B.IS_JJH, --11急货\n" +
                "         B.OP_TYPE, --12业务类型\n" +
                "         (SELECT NAME\n" +
                "            FROM FZGJ_BD_OP_TYPE A\n" +
                "           WHERE A.STATUS = 'Y'\n" +
                "             AND A.SYS_CODE = 'OMS_ORDER'\n" +
                "             AND CODE = B.OP_TYPE\n" +
                "             AND ROWNUM = 1) OP_TYPE_NAME, --业务类型名称\n" +
                "         B.PRODUCT_CODE, --13业务产品/项目\n" +
                "         (SELECT F.NAME\n" +
                "            FROM FZGJ_BD_PRODUCT F\n" +
                "           WHERE F.BILL_CODE = 'OMS_ORDER'\n" +
                "             AND F.GROUP_CODE = B.GROUP_CODE\n" +
                "             AND F.CODE = B.PRODUCT_CODE\n" +
                "             AND F.OP_TYPE = B.OP_TYPE\n" +
                "             AND ROWNUM = 1) AS PRODUCT_CODE_NAME, --业务产品/项目名称\n" +
                "         B.FWLX_NAME, --14服务类型\n" +
                "         B.PRE_NO, --1协同编号\n" +
                "         B.REQUEST_OK_DATE, --16要求完成时间\n" +
                "         B.TEL, --17联系电话\n" +
                "         B.E_MAIL, --18电子邮箱\n" +
                "         B.BIZ_MEMO, --19业务备注\n" +
                "         (SELECT X.ORDER_NO FROM OMS_ORDER X WHERE X.PRE_NO = B.PRE_NO) AS ORDER_NO,\n" +
                "         \n" +
                "         B.SYS_CODE, --2来源系统\n" +
                "         B.SYS_DOC_NO, --3来源系统业务编号\n" +
                "         B.XD_DATE, --4下单时间\n" +
                "         B.XD_USER, --5下单人\n" +
                "         (SELECT TRUENAME\n" +
                "            FROM FZGJ_SSO_USER A\n" +
                "           WHERE STATUS = 'Y'\n" +
                "             AND A.USERNAME = B.XD_USER\n" +
                "             AND ROWNUM = 1) AS XD_USER_NAME, -- --5下单人\n" +
                "         \n" +
                "         (SELECT NAME\n" +
                "            FROM CRM_CUSTOMER A\n" +
                "           WHERE A.STATUS = 'Y'\n" +
                "             AND A.GROUP_CODE = B.GROUP_CODE\n" +
                "             AND A.CODE = B.CONSIGNEE_CODE\n" +
                "             AND ROWNUM = 1) CONSIGNEE_CODE_NAME, --委托方名称\n" +
                "         B.CUSTOMER_BU, --7客户事业部\n" +
                "         (SELECT MAX(A.NAME)\n" +
                "            FROM CRM_CUSTOMER_KHSYB A\n" +
                "           WHERE A.STATUS = 'Y'\n" +
                "             AND A.GROUP_CODE = B.GROUP_CODE\n" +
                "             AND CODE = B.CUSTOMER_BU\n" +
                "             AND ROWNUM = 1) CUSTOMER_BU_NAME,\n" +
                "         \n" +
                "         (SELECT A.NAME\n" +
                "            FROM CRM_CUSTOMER A\n" +
                "           WHERE A.STATUS = 'Y'\n" +
                "             AND A.CODE = B.SHIPPER\n" +
                "             AND A.GROUP_CODE = B.GROUP_CODE\n" +
                "             AND ROWNUM = 1) AS SHIPPER_NAME, ----9实际发货方\n" +
                "         (SELECT A.NAME\n" +
                "            FROM CRM_CUSTOMER A\n" +
                "           WHERE A.STATUS = 'Y'\n" +
                "             AND A.CODE = B.RECEIVER\n" +
                "             AND A.GROUP_CODE = B.GROUP_CODE\n" +
                "             AND ROWNUM = 1) AS RECEIVER_NAME, ----9实际发货方  \n" +
                "         B.AUDIT_MEMO, --20审核备注\n" +
                "         DECODE(B.AUDIT_STATUS, 'TG', '通过', 'TH', '退回', '') AUDIT_STATUS, --21审核状态\n" +
                "         B.AUDIT_USER_NAME AUDIT_USER, --22审核人\n" +
                "         B.AUDIT_DATE, --23审核时间\n" +
                "         B.GROUP_CODE,\n" +
                "         B.BIZ_REG_ID,\n" +
                "         (SELECT NAME\n" +
                "            FROM FZGJ_EXTEND_DATA      DATA,\n" +
                "                 OMS_ORDER_FWXM_TMS    TMS,\n" +
                "                 OMS_ORDER_FWXM_TMS_XL X\n" +
                "           WHERE DATA.STATUS = 'Y'\n" +
                "             AND DATA.CODE = X.CROSS_ITEM\n" +
                "             AND DATA.GROUP_CODE = X.GROUP_CODE\n" +
                "             AND TMS.TMS_NO = X.TMS_NO\n" +
                "             AND TMS.ORDER_NO = B.SYS_DOC_NO\n" +
                "             AND ROWNUM = 1) CROSS_ITEM_NAME,\n" +
                "         (SELECT NAME\n" +
                "            FROM FZGJ_EXTEND_DATA      DATA,\n" +
                "                 OMS_ORDER_FWXM_TMS    TMS,\n" +
                "                 OMS_ORDER_FWXM_TMS_XL X\n" +
                "           WHERE DATA.STATUS = 'Y'\n" +
                "             AND DATA.CODE = X.CROSS_LINE\n" +
                "             AND DATA.GROUP_CODE = X.GROUP_CODE\n" +
                "             AND TMS.TMS_NO = X.TMS_NO\n" +
                "             AND TMS.ORDER_NO = B.SYS_DOC_NO\n" +
                "             AND ROWNUM = 1) CROSS_LINE_NAME\n" +
                "    FROM OMS_ORDER_PRE B\n";
        sql += " WHERE 1=1 ";
        sql += " AND B.GROUP_CODE=" + cmn.SQLQ(UserContext.getUserInfo().getCompanyCode());

        /**查询条件*/
        // 来源系统业务编号
        if (StringUtils.isNotEmpty(entity.getSysDocNo())) {
            sql += " AND B.SYS_DOC_NO LIKE " + cmn.SQLQL(entity.getSysDocNo());
        }
        // 订单号
        if (StringUtils.isNotEmpty(entity.getOrderNo())) {
            sql += " AND EXISTS(SELECT 1 FROM OMS_ORDER OMSORDER  WHERE OMSORDER.PRE_NO =B.PRE_NO AND OMSORDER.ORDER_NO=" + cmn.SQLQL(entity.getOrderNo()) + ") ";
        }
        if (StringUtils.isNotEmpty(entity.getCustomerOrderNo())) {
            sql += " AND B.CUSTOMER_ORDER_NO LIKE " + cmn.SQLQL(entity.getCustomerOrderNo());
        }
        if (StringUtils.isNotEmpty(entity.getConsigneeCode())) {
            sql += " AND B.CONSIGNEE_CODE= " + cmn.SQLQ(entity.getConsigneeCode());
        }
        if (StringUtils.isNotEmpty(entity.getIsJjh())) {
            sql += " AND B.IS_JJH= " + cmn.SQLQ(entity.getIsJjh());
        }
        if (StringUtils.isNotEmpty(entity.getOpType())) {
            sql += " AND B.OP_TYPE= " + cmn.SQLQ(entity.getOpType());
        }
        if (StringUtils.isNotEmpty(entity.getProductCode())) {
            sql += " AND B.PRODUCT_CODE= " + cmn.SQLQ(entity.getProductCode());
        }
        if (StringUtils.isNotEmpty(entity.getFwlxCode())) {
            sql += " AND B.FWLX_CODE= " + cmn.SQLQ(entity.getFwlxCode());
        }
        if (StringUtils.isNotEmpty(entity.getPreNo())) {
            sql += " AND B.PRE_NO LIKE " + cmn.SQLQL(entity.getPreNo());
        }

        // 下单时间
        if (entity.getXdDateStart() != null) {
            String strStartXDDate = sdf.format(entity.getXdDateStart());
            sql += " AND B.XD_DATE>=TO_DATE(" + cmn.SQLQ(strStartXDDate) + ",'yyyy-MM-dd')";
        }
        if (entity.getXdDateEnd() != null) {
            String strEndXDDate = sdf.format(entity.getXdDateStart());
            sql += " AND B.XD_DATE<=TO_DATE(" + cmn.SQLQ(strEndXDDate) + ",'yyyy-MM-dd')";
        }

        // 要求完成日期
        if (entity.getRequestOkDateStart() != null) {
            String strStartRDate = sdf.format(entity.getRequestOkDateStart());
            sql += " AND B.REQUEST_OK_DATE>=TO_DATE(" + cmn.SQLQ(strStartRDate) + ",'yyyy-MM-dd')";
        }
        if (entity.getRequestOkDateEnd() != null) {
            String strEndRDate = sdf.format(entity.getRequestOkDateEnd());
            sql += " AND B.REQUEST_OK_DATE<=TO_DATE(" + cmn.SQLQ(strEndRDate) + ",'yyyy-MM-dd')";
        }

        if (StringUtils.isNotEmpty(entity.getShipper())) {
            sql += " AND B.SHIPPER LIKE " + cmn.SQLQL(entity.getShipper());
        }
        if (StringUtils.isNotEmpty(entity.getReceiver())) {
            sql += " AND B.RECEIVER LIKE " + cmn.SQLQL(entity.getReceiver());
        }
        // 项目
        if (StringUtils.isNotEmpty(entity.getCrossItem())) {
            sql += "AND EXISTS(SELECT 1 FROM OMS_ORDER_FWXM_TMS TMS, OMS_ORDER_FWXM_TMS_XL X WHERE TMS.ORDER_NO = B.SYS_DOC_NO\n" +
                    "AND TMS.TMS_NO = X.TMS_NO AND X.CROSS_ITEM = " + cmn.SQLQ(entity.getCrossItem()) + ")";
        }
        // 线路
        if (StringUtils.isNotEmpty(entity.getCrossLine())) {
            sql += " AND EXISTS(SELECT 1 FROM OMS_ORDER_FWXM_TMS TMS, OMS_ORDER_FWXM_TMS_XL X WHERE TMS.ORDER_NO = B.SYS_DOC_NO AND TMS.TMS_NO = X.TMS_NO AND X.CROSS_LINE = " + cmn.SQLQ(entity.getCrossLine()) + ")";
        }

        // 审核结果
        if (StringUtils.isNotEmpty(entity.getAuditStatus())) {
            sql += " AND B.AUDIT_STATUS=" + cmn.SQLQ(entity.getAuditStatus());
        }

        // 调用方法-设置查询权限
        sql += orderPreSelUserinfoSql(true, entity.getPageType());

        if (!Zsr.String.IsNullOrWhiteSpace(entity.getType())) {
            if (entity.getType().equals("Y")) { // 已审核列表条件
                sql += " AND (B.AUDIT_STATUS =" + cmn.SQLQ(Enums.PreOrderCh.TG.getCode()) + "or B.AUDIT_STATUS =" + cmn.SQLQ(Enums.PreOrderCh.TH.getCode()) + ")";
                sql += " ORDER BY B.XD_DATE DESC NULLS LAST";
            } else { // 待审核列表条件
                sql += " AND B.STATUS=" + cmn.SQLQ(Enums.PreOrderStatus.ZC.getCode()) + " AND  (B.AUDIT_STATUS !=" + cmn.SQLQ(Enums.PreOrderCh.TG.getCode()) + "  OR B.AUDIT_STATUS IS NULL)";
                sql += " ORDER BY B.AUDIT_DATE DESC NULLS LAST";
            }
        } else {
            // 待审核列表条件
            sql += " AND B.STATUS=" + cmn.SQLQ(Enums.PreOrderStatus.ZC.getCode())
                    + " AND  (B.AUDIT_STATUS !=" + cmn.SQLQ(Enums.PreOrderCh.TG.getCode())
                    + "  OR B.AUDIT_STATUS IS NULL)";
            sql += " ORDER BY B.AUDIT_DATE DESC NULLS LAST";
        }
        return sql;
    }

    /**
     * 生成列表查询sql语句
     */
    public String selectPageSqlForZiZhuDaiShenHe(ReqOmsOrderPrePageEntity entity) {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        String sql = "";

        sql += "   SELECT B.CUSTOMER_ORDER_NO, --15客户单据号\n" +
                "         B.CONSIGNEE_CODE, --6委托方\n" +
                "         B.SHIPPER, --8实际发货方\n" +
                "         B.RECEIVER, --9实际收货方\n" +
                "         B.OP_DATE, --10业务日期\n" +
                "         B.IS_JJH, --11急货\n" +
                "         B.OP_TYPE, --12业务类型\n" +
                "         B.PRODUCT_CODE, --13业务产品/项目\n" +
                "         B.FWLX_NAME, --14服务类型\n" +
                "         B.PRE_NO, --1协同编号\n" +
                "         B.REQUEST_OK_DATE, --16要求完成时间\n" +
                "         B.TEL, --17联系电话\n" +
                "         B.E_MAIL, --18电子邮箱\n" +
                "         B.BIZ_MEMO, --19业务备注\n" +
                "         B.SYS_CODE, --2来源系统\n" +
                "         B.SYS_DOC_NO, --3来源系统业务编号\n" +
                "         B.XD_DATE, --4下单时间\n" +
                "         B.XD_USER, --5下单人\n" +
                "         B.CUSTOMER_BU, --7客户事业部\n" +
                "         B.AUDIT_MEMO, --20审核备注\n" +
                "         B.AUDIT_STATUS, --21审核状态\n" +
                "         B.AUDIT_USER_NAME AUDIT_USER, --22审核人\n" +
                "         B.AUDIT_DATE, --23审核时间\n" +
                "         B.GROUP_CODE,\n" +
                "         B.BIZ_REG_ID    \n" +
                "    FROM OMS_ORDER_PRE B ";
        sql += " WHERE 1=1 ";
        sql += " AND B.GROUP_CODE=" + cmn.SQLQ(UserContext.getUserInfo().getCompanyCode());

        /**查询条件*/
        // 来源系统业务编号
        if (StringUtils.isNotEmpty(entity.getSysDocNo())) {
            sql += " AND B.SYS_DOC_NO LIKE " + cmn.SQLQL(entity.getSysDocNo());
        }
        // 订单号
        if (StringUtils.isNotEmpty(entity.getOrderNo())) {
            sql += " AND EXISTS(SELECT 1 FROM OMS_ORDER OMSORDER  WHERE OMSORDER.PRE_NO =B.PRE_NO AND OMSORDER.ORDER_NO=" + cmn.SQLQL(entity.getOrderNo()) + ") ";
        }
        if (StringUtils.isNotEmpty(entity.getCustomerOrderNo())) {
            sql += " AND B.CUSTOMER_ORDER_NO LIKE " + cmn.SQLQL(entity.getCustomerOrderNo());
        }
        if (StringUtils.isNotEmpty(entity.getConsigneeCode())) {
            sql += " AND B.CONSIGNEE_CODE= " + cmn.SQLQ(entity.getConsigneeCode());
        }
        if (StringUtils.isNotEmpty(entity.getIsJjh())) {
            sql += " AND B.IS_JJH= " + cmn.SQLQ(entity.getIsJjh());
        }
        if (StringUtils.isNotEmpty(entity.getOpType())) {
            sql += " AND B.OP_TYPE= " + cmn.SQLQ(entity.getOpType());
        }
        if (StringUtils.isNotEmpty(entity.getProductCode())) {
            sql += " AND B.PRODUCT_CODE= " + cmn.SQLQ(entity.getProductCode());
        }
        if (StringUtils.isNotEmpty(entity.getFwlxCode())) {
            sql += " AND B.FWLX_CODE= " + cmn.SQLQ(entity.getFwlxCode());
        }
        if (StringUtils.isNotEmpty(entity.getPreNo())) {
            sql += " AND B.PRE_NO LIKE " + cmn.SQLQL(entity.getPreNo());
        }

        // 下单时间
        if (entity.getXdDateStart() != null) {
            String strStartXDDate = sdf.format(entity.getXdDateStart());
            sql += " AND B.XD_DATE>=TO_DATE(" + cmn.SQLQ(strStartXDDate) + ",'yyyy-MM-dd')";
        }
        if (entity.getXdDateEnd() != null) {
            String strEndXDDate = sdf.format(entity.getXdDateStart());
            sql += " AND B.XD_DATE<=TO_DATE(" + cmn.SQLQ(strEndXDDate) + ",'yyyy-MM-dd')";
        }

        // 要求完成日期
        if (entity.getRequestOkDateStart() != null) {
            String strStartRDate = sdf.format(entity.getRequestOkDateStart());
            sql += " AND B.REQUEST_OK_DATE>=TO_DATE(" + cmn.SQLQ(strStartRDate) + ",'yyyy-MM-dd')";
        }
        if (entity.getRequestOkDateEnd() != null) {
            String strEndRDate = sdf.format(entity.getRequestOkDateEnd());
            sql += " AND B.REQUEST_OK_DATE<=TO_DATE(" + cmn.SQLQ(strEndRDate) + ",'yyyy-MM-dd')";
        }

        if (StringUtils.isNotEmpty(entity.getShipper())) {
            sql += " AND B.SHIPPER LIKE " + cmn.SQLQL(entity.getShipper());
        }
        if (StringUtils.isNotEmpty(entity.getReceiver())) {
            sql += " AND B.RECEIVER LIKE " + cmn.SQLQL(entity.getReceiver());
        }
        // 项目
        if (StringUtils.isNotEmpty(entity.getCrossItem())) {
            sql += "AND EXISTS(SELECT 1 FROM OMS_ORDER_FWXM_TMS TMS, OMS_ORDER_FWXM_TMS_XL X WHERE TMS.ORDER_NO = B.SYS_DOC_NO\n" +
                    "AND TMS.TMS_NO = X.TMS_NO AND X.CROSS_ITEM = " + cmn.SQLQ(entity.getCrossItem()) + ")";
        }
        // 线路
        if (StringUtils.isNotEmpty(entity.getCrossLine())) {
            sql += " AND EXISTS(SELECT 1 FROM OMS_ORDER_FWXM_TMS TMS, OMS_ORDER_FWXM_TMS_XL X WHERE TMS.ORDER_NO = B.SYS_DOC_NO AND TMS.TMS_NO = X.TMS_NO AND X.CROSS_LINE = " + cmn.SQLQ(entity.getCrossLine()) + ")";
        }

        // 审核结果
        if (StringUtils.isNotEmpty(entity.getAuditStatus())) {
            sql += " AND B.AUDIT_STATUS=" + cmn.SQLQ(entity.getAuditStatus());
        }

        // 调用方法-设置查询权限
        sql += orderPreSelUserinfoSql(true, entity.getPageType());

        // 待审核列表条件
        sql += " AND B.STATUS=" + cmn.SQLQ(Enums.PreOrderStatus.XDDS.getCode())
                + " AND  (B.AUDIT_STATUS !=" + cmn.SQLQ(Enums.PreOrderCh.TG.getCode())
                + "  OR B.AUDIT_STATUS IS NULL)";
        sql += " ORDER BY B.AUDIT_DATE DESC NULLS LAST";

        return sql;
    }

    /*
     * 根据权限平台配置的type 的值（C：公司级；G：集团级） 查询数据的时候
     * 作业中 和作业完成  UserInfo.NodeCode()
     * 待评价 待提交 待审核 UserInfo.CscNodeCode()
     * bool orderOrPre
     * true: pre表
     * false: order表
     */
    public String orderPreSelUserinfoSql(boolean orderOrPre, String pageType) {
        String condition = "";
        UserInfo userInfo = UserContext.getUserInfo();
        //pre
        if (orderOrPre) {
            if (StringUtils.isNotEmpty(pageType)) {
                if (pageType.equals(Enums.RoleType.USER)) {
                    condition += " AND B.JD_USER=" + cmn.SQLQ(userInfo.getTrueName());
                } else if (pageType.equals(Enums.RoleType.NODE)) {
                    condition += " AND B.NODE_CODE=" + cmn.SQLQ(userInfo.getDeptCode());
                } else if (pageType.equals(Enums.RoleType.COMPANY)) {
                    condition += " AND B.COMPANY_CODE=" + cmn.SQLQ(userInfo.getCompanyCode());
                } else if (pageType.equals(Enums.RoleType.GROUP)) {
                    condition += " AND B.GROUP_CODE=" + cmn.SQLQ(userInfo.getCompanyCode());
                } else {
                    condition += " AND B.COMPANY_CODE=" + cmn.SQLQ(userInfo.getCompanyCode());
                }
            }
        }
        //ORDER
        else {
            if (StringUtils.isNotEmpty(pageType)) {
                if (pageType.equals(Enums.RoleType.USER)) {
                    condition += " AND A.CREATE_USER=" + cmn.SQLQ(userInfo.getTrueName());
                } else if (pageType.equals(Enums.RoleType.NODE)) {
                    condition += " AND A.NODE_CODE=" + cmn.SQLQ(userInfo.getDeptCode());
                } else if (pageType.equals(Enums.RoleType.COMPANY)) {
                    condition += " AND A.COMPANY_CODE=" + cmn.SQLQ(userInfo.getCompanyCode());
                } else if (pageType.equals(Enums.RoleType.GROUP)) {
                    condition += " AND A.GROUP_CODE=" + cmn.SQLQ(userInfo.getCompanyCode());
                } else {
                    condition += " AND A.COMPANY_CODE=" + cmn.SQLQ(userInfo.getCompanyCode());
                }
            }
        }

        return condition;
    }

    /**
     * 修改订单信息 OREER_NO字段
     *
     * @param preNo   原订单号
     * @param orderNo 新订单号
     * @return 是否更新成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOrderInfoOrderNo(String preNo, String orderNo) {

        try {
            String groupCode = UserContext.getUserInfo().getCompanyCode();
            String updateTemplate = "UPDATE %s SET ORDER_NO = %s WHERE PRE_NO = %s AND GROUP_CODE = %s";

            // 添加多个表的更新语句
            String[] tables = {
                    "OMS_ORDER_GOODS_PACK", "OMS_ORDER_GOODS_COST",
                    "OMS_ORDER_GOODS", "OMS_ORDER_FWXM_WORK_TRACE", "OMS_ORDER_FWXM_WORK",
                    "OMS_ORDER_FWXM_TMS_XL_XL_LY_XL", "OMS_ORDER_FWXM_TMS_XL_XL_LY_PC",
                    "OMS_ORDER_FWXM_TMS_XL_XL_LY_CL", "OMS_ORDER_FWXM_TMS_XL_XL_LY",
                    "OMS_ORDER_FWXM_TMS_XL_XL", "OMS_ORDER_FWXM_TMS_XL", "OMS_ORDER_FWXM_TMS",
                    "OMS_ORDER_FWXM_CRK_ZZFW", "OMS_ORDER_FWXM_BGBJ", "OMS_ORDER_FWXM", "OMS_ORDER_FW",
                    "OMS_ORDER_FWXM_ZHYS"
            };

            for (String table : tables) {
                String sql = String.format(
                        updateTemplate,
                        table,
                        cmn.SQLQ(orderNo),
                        cmn.SQLQ(preNo),
                        cmn.SQLQ(groupCode)
                );

                DBHelper.execute(sql);
            }

        } catch (Exception ex) {
            Log.info("updateOrderInfoOrderNo-批量更新错误:" + ex.getMessage(), "updateOrderInfoOrderNo");
            return false;
        }

        return true;
    }

    /// <summary>
    /// 协作分发至OMS 审核结果反馈至上级
    /// </summary>
    /// <param name="orderPre">当前协同订单信息</param>
    /// <param name="orderNo">作业系统单据编号 ，审核通过：传当前订单号，不通过传空</param>
    @Transactional
    public void xzffToOmsResult(OmsOrderPreEntity orderPre, String orderNo) {
        String result = StringUtils.hasValue(orderNo) ? "Y" : "N";

        List<OmsOrderFwxmWorkEntity> listWork = omsOrderFwxmWorkDao.select()
                .eq(OmsOrderFwxmWorkEntity::getPreNo, orderPre.getPreNo())
                .list();

        for (OmsOrderFwxmWorkEntity work : listWork) {
            if (StringUtils.hasValue(work.getPreWorkNo())) {  // preWorkNo有值就新增数据
                OmsISlResultEntity iSlResult = new OmsISlResultEntity();
                iSlResult.setGuid(IdWorker.get32UUID());
                iSlResult.setWorkNo(work.getPreWorkNo()); // OMS协作任务编号
                iSlResult.setXzwtNo(""); // OMS协作委托单号,可以不传
                iSlResult.setOrderNo(orderPre.getSysCode());  // OMS订单号      ORDER   BIZ_BZ
                iSlResult.setResult(result); // 受理结果    Y  OR N
                iSlResult.setSysCode("OMS"); // 作业系统代码
                iSlResult.setDocType("OMS_ORDER");  // 作业系统单据类型
                iSlResult.setDocNo(orderNo);        // 作业系统单据编号
                iSlResult.setTrnDate(new java.util.Date()); // 传输时间
                iSlResult.setOpDate(new java.util.Date());  // 传输时间
                omsISlResultDao.insert(iSlResult);
            }
        }
    }

    public List<EntityBase> orderConsineeCodeSearch(String code) {

        String sql = "SELECT *FROM (\n" +
                "        SELECT D.NBS_EMS_NO,A.CODE, A.NAME,A.SHORT_NAME,A.EN_NAME,(CASE WHEN A.IS_NB='Y' THEN '是' ELSE '否' END) IS_NB,\n" +
                "        C.CH_NAME AS COUNTRY_NAME,P.NAME AS PROVINCE_NAME,E.NAME AS CITY_NAME\n" +
                "        FROM CRM_CUSTOMER A\n" +
                "        LEFT JOIN WMS_CUSTOMER_EXTEN D on D.CUSTOMER_GUID=A.GUID\n" +
                "        LEFT JOIN FZGJ_BD_COUNTRY C ON A.COUNTRY=C.CODE\n" +
                "        LEFT JOIN FZGJ_BD_PROVINCE P ON A.PROVINCE=P.GUID AND C.CODE=P.COUNTRY_ID\n" +
                "        LEFT JOIN FZGJ_BD_CITY E ON A.CITY=E.GUID AND P.GUID=E.PROVINCE_ID\n" +
                "        WHERE A.STATUS = 'Y'\n" +
                "        AND A.ROLE_CODE LIKE '%1%'\n" +
                "        AND A.GROUP_CODE = " + cmn.SQLQ(UserContext.getUserInfo().getCompanyCode()) + "\n" +
                "        AND (A.COMPANY_CODE='-' OR A.COMPANY_CODE=" + cmn.SQLQ(UserContext.getUserInfo().getCompanyCode()) + ")) A\n" +
                "        where A.CODE=" + cmn.SQLQ(code) + " ";
        return DBHelper.selectList(sql, EntityBase.class);
    }

    /**
     * 确认下单
     *
     * @param jsonString
     * @return
     */
    public Object xiaDan(String jsonString) {
        ZsrJson zsrJson = ZsrJson.parse(jsonString);
        String orderNoVar = "orderNo";
        String preNo = zsrJson.check(orderNoVar).getString(orderNoVar);
        OmsOrderPreEntity one = omsOrderPreDao.select()
                .eq(OmsOrderPreEntity::getPreNo, preNo).one();
        if (one == null) {
            throw new BaseException("协同单号不存在");
        }
        one.setStatus(Enums.PreOrderStatus.XDDS.getCode());
        return omsOrderPreDao.updateByEntityId(one);
    }

    /**
     * 获取每个服务项目的最新作业状态
     *
     * @param preEntity
     * @return
     */
    public Map<String, String> getLatestCompletedNodeName(OmsOrderPreEntity preEntity) {
        // 1. 查询服务项目
        List<OmsOrderFwxmWorkEntity> entityList = omsOrderFwxmWorkDao.select()
                .eq(OmsOrderFwxmWorkEntity::getPreNo, preEntity.getPreNo())
                .list();

        if (entityList == null || entityList.isEmpty()) { // 使用 isEmpty() 更为推荐
            return Collections.emptyMap(); // 返回空Map比返回null更安全
        }

        // 2. 提取服务项目编码
        List<String> fwxmCodeList = entityList.stream()
                .map(OmsOrderFwxmWorkEntity::getFwxmCode)
                .collect(Collectors.toList());

        // 3. 查询所有相关的限制节点
        List<FzgjTaskLimitationPtEntity> limitationPtEntityList = fzgjTaskLimitationPtDao.select()
                .in(FzgjTaskLimitationPtEntity::getTargetCode, fwxmCodeList)
                .list();

        // 4. 按服务项目分组
        Map<String, List<FzgjTaskLimitationPtEntity>> taskMap =
                limitationPtEntityList.stream()
                        .collect(Collectors.groupingBy(FzgjTaskLimitationPtEntity::getTargetCode));

        // 5. 查询工作轨迹
        List<OmsOrderFwxmWorkTraceEntity> workTraceEntityList = omsOrderFwxmWorkTraceDao.select()
                .eq(OmsOrderFwxmWorkTraceEntity::getOrderNo, entityList.get(0).getOrderNo())
                .list();

        // 【优化点 1】将工作轨迹列表转换为Set，用于快速查找
        // 创建一个由 "code:seq" 组成的Set，用于O(1)复杂度的快速查找
        Set<String> completedTraceKeys = workTraceEntityList.stream()
                .map(trace -> trace.getLinkCode() + ":" + trace.getLinkSeq())
                .collect(Collectors.toSet());

        Map<String, String> resultMap = new HashMap<>();

        // 【优化点 2】合并循环，一次遍历解决问题
        taskMap.forEach((fwxmCode, limitations) -> {
            // 对于每一个服务项目，直接在它的“关卡”列表中筛选和查找
            Optional<FzgjTaskLimitationPtEntity> latestCompletedNode = limitations.stream()
                    // 筛选出已完成的节点
                    .filter(limitation -> {
                        String key = limitation.getCode() + ":" + limitation.getSeq();
                        return completedTraceKeys.contains(key);
                    })
                    // 在已完成的节点中，根据seq寻找最大的一个
                    .max(Comparator.comparing(FzgjTaskLimitationPtEntity::getSeq));

            // 如果找到了最新的已完成节点，则记录其名称
            latestCompletedNode.ifPresent(entity -> resultMap.put(fwxmCode, entity.getName()));
        });

        return resultMap;
    }

}