package com.eci.project.etmsBdCargoType.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.etmsBdCargoType.dao.EtmsBdCargoTypeDao;
import com.eci.project.etmsBdCargoType.entity.EtmsBdCargoTypeEntity;
import com.eci.project.etmsBdCargoType.validate.EtmsBdCargoTypeVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 货物形态Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-03-21
*/
@Service
@Slf4j
public class EtmsBdCargoTypeService implements EciBaseService<EtmsBdCargoTypeEntity> {

    @Autowired
    private EtmsBdCargoTypeDao etmsBdCargoTypeDao;

    @Autowired
    private EtmsBdCargoTypeVal etmsBdCargoTypeVal;


    @Override
    public TgPageInfo queryPageList(EtmsBdCargoTypeEntity entity) {
        EciQuery<EtmsBdCargoTypeEntity> eciQuery = EciQuery.buildQuery(entity);
        List<EtmsBdCargoTypeEntity> entities = etmsBdCargoTypeDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public EtmsBdCargoTypeEntity save(EtmsBdCargoTypeEntity entity) {
        // 返回实体对象
        EtmsBdCargoTypeEntity etmsBdCargoTypeEntity = null;
        etmsBdCargoTypeVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            etmsBdCargoTypeEntity = etmsBdCargoTypeDao.insertOne(entity);

        }else{

            etmsBdCargoTypeEntity = etmsBdCargoTypeDao.updateByEntityId(entity);

        }
        return etmsBdCargoTypeEntity;
    }

    @Override
    public List<EtmsBdCargoTypeEntity> selectList(EtmsBdCargoTypeEntity entity) {
        return etmsBdCargoTypeDao.selectList(entity);
    }

    @Override
    public EtmsBdCargoTypeEntity selectOneById(Serializable id) {
        return etmsBdCargoTypeDao.selectById(id);
    }


    @Override
    public void insertBatch(List<EtmsBdCargoTypeEntity> list) {
        etmsBdCargoTypeDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return etmsBdCargoTypeDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return etmsBdCargoTypeDao.deleteById(id);
    }

}