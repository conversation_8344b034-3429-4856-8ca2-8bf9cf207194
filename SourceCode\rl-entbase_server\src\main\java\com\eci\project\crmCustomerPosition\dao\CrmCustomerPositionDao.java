package com.eci.project.crmCustomerPosition.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.crmCustomerPosition.entity.CrmCustomerPositionEntity;


/**
* 合作伙伴职务Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-05-26
*/
public interface CrmCustomerPositionDao extends EciBaseDao<CrmCustomerPositionEntity> {

}