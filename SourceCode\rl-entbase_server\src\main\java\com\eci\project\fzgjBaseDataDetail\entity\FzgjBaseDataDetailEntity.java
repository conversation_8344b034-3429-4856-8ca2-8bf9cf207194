package com.eci.project.fzgjBaseDataDetail.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;



/**
* 扩展基础资料明细对象 FZGJ_BASE_DATA_DETAIL
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-03-18
*/
@ApiModel("扩展基础资料明细")
@TableName("FZGJ_BASE_DATA_DETAIL")
public class FzgjBaseDataDetailEntity extends FzgjBaseDataDetailBaseEntity{

}
