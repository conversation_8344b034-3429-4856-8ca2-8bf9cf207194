package com.eci.project.fzgjTaskLimitationTime.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjTaskLimitationTime.entity.FzgjTaskLimitationTimeEntity;

import org.springframework.stereotype.Service;


/**
* 作业环节基准时效Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-05-22
*/
@Service
public class FzgjTaskLimitationTimeVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjTaskLimitationTimeEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjTaskLimitationTimeEntity entity, BusinessType businessType) {

    }

}
