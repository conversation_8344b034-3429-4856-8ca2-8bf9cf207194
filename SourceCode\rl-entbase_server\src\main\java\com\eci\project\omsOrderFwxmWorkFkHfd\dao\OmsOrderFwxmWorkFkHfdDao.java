package com.eci.project.omsOrderFwxmWorkFkHfd.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.omsOrderFwxmWorkFkHfd.entity.OmsOrderFwxmWorkFkHfdEntity;


/**
* 反馈内容-核放单Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-06-03
*/
public interface OmsOrderFwxmWorkFkHfdDao extends EciBaseDao<OmsOrderFwxmWorkFkHfdEntity> {

}