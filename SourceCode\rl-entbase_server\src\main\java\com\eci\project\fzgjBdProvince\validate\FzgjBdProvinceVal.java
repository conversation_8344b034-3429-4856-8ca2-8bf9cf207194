package com.eci.project.fzgjBdProvince.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjBdProvince.entity.FzgjBdProvinceEntity;

import org.springframework.stereotype.Service;


/**
* 省Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-03-17
*/
@Service
public class FzgjBdProvinceVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjBdProvinceEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjBdProvinceEntity entity, BusinessType businessType) {

    }

}
