package com.eci.project.fzgjBdBill.service;

import com.eci.common.Zsr;
import com.eci.common.ZsrJson;
import com.eci.common.ZsrPair;
import com.eci.common.db.DBHelper;
import com.eci.common.utils.CommonLib;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjBdBill.common.TreeNodeDTO;
import com.eci.project.fzgjBdBill.dao.FzgjBdBillDao;
import com.eci.project.fzgjBdBill.entity.FzgjBdBillEntity;
import com.eci.project.fzgjBdBill.entity.FzgjBdProductDto;
import com.eci.project.fzgjBdBill.entity.YeWuChanPingQueryDto;
import com.eci.project.fzgjBdBill.validate.FzgjBdBillVal;

import com.eci.project.fzgjBdOpType.dao.FzgjBdOpTypeDao;
import com.eci.project.fzgjBdOpType.entity.FzgjBdOpTypeEntity;
import com.eci.project.fzgjBdProductService.service.FzgjBdProductServiceService;
import com.eci.sso.role.entity.UserContext;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * 单据类型Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-03-19
 */
@Service
@Slf4j
public class FzgjBdBillService implements EciBaseService<FzgjBdBillEntity> {

    @Autowired
    private FzgjBdBillDao fzgjBdBillDao;
    @Autowired
    private FzgjBdOpTypeDao fzgjBdOpTypeDao;

    @Autowired
    private FzgjBdBillVal fzgjBdBillVal;

    @Autowired
    private FzgjBdProductServiceService fzgjBdProductServiceService;

    /**
     * 1. 获取根节点数据
     * <remark>这是加载第1颗树的方法</remark>
     */
    public TreeNodeDTO getTreeRoot() {
        FzgjBdBillEntity entity = fzgjBdBillDao.select().eq(FzgjBdBillEntity::getParentid, "-").one();

        List<FzgjBdBillEntity> allNodes = MergeData(entity);
        TreeNodeDTO treeNodeDTO = new TreeNodeDTO();
        treeNodeDTO.setId(entity.getGuid());
        treeNodeDTO.setLabel(entity.getName());
        treeNodeDTO.setChildren(buildTree(allNodes, "0"));
        return treeNodeDTO;
    }

    /**
     * 构建elementui tree树
     * <remark>这是加载第1颗树的方法</remark>
     *
     * @param nodes
     * @param parentId
     * @return
     */
    private List<TreeNodeDTO> buildTree(List<FzgjBdBillEntity> nodes, String parentId) {
        return nodes.stream()
                .filter(node -> parentId == null ? node.getParentid() == null : parentId.equals(node.getParentid()))
                .map(node -> {
                    TreeNodeDTO dto = new TreeNodeDTO();
                    dto.setId(String.valueOf(node.getGuid())); // 前端需要字符串类型的 id
                    dto.setLabel(node.getName());
                    List<TreeNodeDTO> children = buildTree(nodes, node.getGuid());
                    if (!children.isEmpty()) {
                        dto.setChildren(children);
                    }
                    return dto;
                })
                .collect(Collectors.toList());
    }

    /**
     * 合并数据
     * <remark>这是加载第1颗树的方法</remark>
     *
     * @param entity
     * @return
     */
    private List<FzgjBdBillEntity> MergeData(FzgjBdBillEntity entity) {
        List<FzgjBdBillEntity> baseData = getTreeRootNodesData(entity.getGuid());
        List<FzgjBdBillEntity> dataWithChildren = getTreeRootNodesData2(entity.getGuid());
        // 使用 Map 优化合并性能（以 ID 为键）
        Map<String, FzgjBdBillEntity> mergedMap = new HashMap<>();

        // 先放入基础数据（会被后续的 dataWithChildren 覆盖）
        baseData.forEach(item -> mergedMap.put(item.getGuid(), item));

        // 合并逻辑：存在则覆盖，不存在则新增
        dataWithChildren.forEach(item -> mergedMap.put(item.getGuid(), item));

        // 转换为 List 并按主键排序（保持顺序可选）
        return new ArrayList<>(mergedMap.values());
    }


    /**
     * 1.1 加载根节点下面的数据
     * <remark>这是加载第1颗树的方法</remark>
     */
    public List<FzgjBdBillEntity> getTreeRootNodesData(String parentId) {
        List<FzgjBdBillEntity> list = fzgjBdBillDao.select()
                .eq(FzgjBdBillEntity::getStatus, "Y")
                .eq(FzgjBdBillEntity::getParentid, parentId)
                .list();
        return list;
    }


    /**
     * 1.2 加载根节点下面的数据
     * <remark>这是加载第1颗树的方法</remark>
     */
    public List<FzgjBdBillEntity> getTreeRootNodesData2(String parentId) {
        List<FzgjBdBillEntity> list = fzgjBdBillDao.select()
                .eq(FzgjBdBillEntity::getStatus, "Y")
                .eq(FzgjBdBillEntity::getParentid, parentId)
                .exists("SELECT 1 FROM FZGJ_BD_BILL B WHERE A.GUID = B.PARENTID")
                .list();
        return list;
    }


    /**
     * 2. 当根节点展开之后，点击根节点下面的节点之后，获取子节点数据,数据原来：<ref="getTreeRoot()" />方法
     * <remark>这是加载第2颗树的方法</remark>
     *
     * @param jsonString
     */
    public List<TreeNodeDTO> getTreeDataByRootId(String jsonString) {
        String id = ZsrJson.parse(jsonString).check("id").getString("id");
        List<TreeNodeDTO> list = fzgjBdBillDao.select().eq(FzgjBdBillEntity::getGuid, id).list().stream().map(item -> {
            TreeNodeDTO dto = new TreeNodeDTO();
            dto.setId(String.valueOf(item.getGuid()));
            dto.setLabel(item.getName());
            dto.setChildren(build2TreeData(id));
            return dto;
        }).collect(Collectors.toList());
        return list;
    }

    /**
     * 2. 当根节点展开之后，点击根节点下面的节点之后，获取子节点数据,数据原来：<ref="getTreeRoot()" />方法
     * <remark>这是加载第2颗树的方法</remark>
     *
     * @param id
     */
    public List<TreeNodeDTO> build2TreeData(String id) {
        List<TreeNodeDTO> list = fzgjBdOpTypeDao.select()
                .eq(FzgjBdOpTypeEntity::getGroupCode, UserContext.getUserInfo().getCompanyCode())
                .eq(FzgjBdOpTypeEntity::getClassGuid, id)
                .list()
                .stream().map(item -> {
                    TreeNodeDTO dto = new TreeNodeDTO();
                    dto.setId(String.valueOf(item.getGuid()));
                    dto.setLabel(item.getName());
                    return dto;
                }).collect(Collectors.toList());
        return list;
    }

    /**
     * 2. 当第二颗树展开后，点击节点，获取该节点信息，把数据传递给第三个列表数据
     * <remark>这是加载第2颗树的方法-点击第二颗树节点的方法</remark>
     *
     * @param jsonString
     */
    public FzgjBdOpTypeEntity getNodeTree2ItemInfoById(String jsonString) {
        String id = ZsrJson.parse(jsonString).check("id").getString("id");
        FzgjBdOpTypeEntity one = fzgjBdOpTypeDao.select()
                .eq(FzgjBdOpTypeEntity::getGroupCode, UserContext.getUserInfo().getCompanyCode())
                .eq(FzgjBdOpTypeEntity::getGuid, id)
                .one();
        return one;
    }

    /**
     * 2. 当第二颗树展开后，点击节点，获取该节点信息，把数据传递给第三个列表数据
     * <remark>这是加载第2颗树的方法-点击第二颗树节点的方法</remark>
     *
     * @param jsonString
     */
    public FzgjBdOpTypeEntity getFormDeatil(String jsonString) {
        String id = ZsrJson.parse(jsonString).check("id").getString("id");
        FzgjBdOpTypeEntity one = fzgjBdOpTypeDao.select()
                .eq(FzgjBdOpTypeEntity::getGroupCode, UserContext.getUserInfo().getCompanyCode())
                .eq(FzgjBdOpTypeEntity::getGuid, id)
                .one();
        if (one != null && !Zsr.String.IsNullOrWhiteSpace(one.getSysCode())) {
            FzgjBdBillEntity fzgjBdBillEntity = fzgjBdBillDao.select()
                    .eq(FzgjBdBillEntity::getCode, one.getSysCode())
                    .one();
            one.push("masterName", fzgjBdBillEntity.getName());
        }
        return one;
    }

    CommonLib cmn = CommonLib.getInstance();

    /**
     * 查询业务产品数据表格数据
     *
     * @param queryDto
     * @return
     */
    public TgPageInfo queryYeWuChanPingTablePageList(YeWuChanPingQueryDto queryDto) {
        try {
            String sql = "SELECT A.GUID, " +
                    "       A.CODE, " +
                    "       A.NAME, " +
                    "       A.STATUS, " +
                    "       A.SEQ, " +
                    "       A.XZFA_NO, " +
                    "       A.MEMO, " +
                    "       A.CREATE_USER, " +
                    "       A.CREATE_DATE, " +
                    "       A.UPDATE_USER, " +
                    "       A.UPDATE_DATE, " +
                    "       A.OP_TYPE, " +
                    "       A.COMPANY_CODE, " +
                    "       A.CREATE_USER_NAME, " +
                    "       A.UPDATE_USER_NAME, " +
                    "       A.COMPANY_NAME, " +
                    "       A.GROUP_CODE, " +
                    "       A.GROUP_NAME, " +
                    "       A.EN_NAME, " +
                    "       A.BILL_CODE, " +
                    "       A.IS_FWXM_EDIT, " +
//                    "       (SELECT WM_CONCAT(DISTINCT " +
//                    "                         FZGJ_NAME(SERVICE_NO, 'FZGJ_BD_SERVICE_TYPE')) " +
//                    "          FROM (SELECT B.SERVICE_NO, C.SEQ, B.PRODUCT_CODE, B.OP_TYPE " +
//                    "                  FROM FZGJ_BD_PRODUCT_SERVICE B " +
//                    "                  LEFT JOIN FZGJ_BD_SERVICE_TYPE C " +
//                    "                    ON C.CODE = B.SERVICE_NO " +
//                    "                 ORDER BY C.SEQ) " +
//                    "         WHERE PRODUCT_CODE = A.CODE " +
//                    "           AND OP_TYPE = A.OP_TYPE) SERVICE_TYPE_NAME, " +
                    "       (SELECT T.NAME " +
                    "          FROM FZGJ_BD_OP_TYPE T " +
                    "         WHERE T.CODE = A.OP_TYPE " +
                    "           AND T.SYS_CODE = A.BILL_CODE " +
                    "           AND T.GROUP_CODE = A.GROUP_CODE " +
                    "           AND T.STATUS = 'Y') AS OP_TYPE_NAME, " +
                    "       A.IS_STANDARD, " +
                    "       A.CUSTOMER_CODE " +
//                    "       FZGJ_NAME(A.STATUS, 'FZGJ_ISUSE') AS STATUS_NAME, " +
//                    "       FZGJ_NAME(A.BILL_CODE, 'FZGJ_BD_BILL') AS BILL_CODE_NAME, " +
//                    "       FZGJ_NAME(A.IS_STANDARD, 'FZGJ_ISUSE') AS IS_STANDARD_NAME, " +
//                    "       FZGJ_NAMECOM(A.CUSTOMER_CODE, 'CQDM', 'CRM_CUSTOMER_KH') AS CUSTOMER_CODE_NAME " +
                    "  FROM FZGJ_BD_PRODUCT A " +
                    " WHERE A.GROUP_CODE = " + cmn.SQLQ(UserContext.getUserInfo().getCompanyCode()) +
                    "   AND A.OP_TYPE =  " + cmn.SQLQ(queryDto.getOpType()) +
                    "   AND A.BILL_CODE = " + cmn.SQLQ(queryDto.getBillCode()) +
                    "   AND A.STATUS =   " + cmn.SQLQ(queryDto.getStatus());
            TgPageInfo<FzgjBdProductDto> pageInfo = DBHelper.selectPageList(sql, FzgjBdProductDto.class);


            // 1. 收集所有需要查询的 productCode 和 opType
            List<String> productCodes = pageInfo.getList().stream()
                    .map(FzgjBdProductDto::getCode)
                    .distinct()
                    .collect(Collectors.toList());

            List<String> opTypes = pageInfo.getList().stream()
                    .map(FzgjBdProductDto::getOpType)
                    .distinct()
                    .collect(Collectors.toList());

            // 2. 一次性查询获取所有需要的服务类型名称
            Map<String, String> serviceTypeNamesMap = fzgjBdProductServiceService.getServiceTypeNamesBatch(productCodes, opTypes);


            // 3. 遍历结果，将服务类型名称设置到对应的 DTO 中 (使用 ExecutorService)
            int numThreads = Math.min(pageInfo.getList().size(), Runtime.getRuntime().availableProcessors()); // 设置线程数，可以根据实际情况调整
            ExecutorService executorService = Executors.newFixedThreadPool(numThreads);
            List<Runnable> tasks = new ArrayList<>();

            for (FzgjBdProductDto item : pageInfo.getList()) {
                tasks.add(() -> {
                    String key = item.getCode() + "-" + item.getOpType();
                    String serviceTypeNames = serviceTypeNamesMap.get(key);
                    item.push("serviceTypeName", serviceTypeNames);
                });
            }

            for (Runnable task : tasks) {
                executorService.submit(task);
            }

            executorService.shutdown();
            executorService.awaitTermination(Long.MAX_VALUE, TimeUnit.MILLISECONDS); // 等待所有任务完成

//
//            for (FzgjBdProductDto item : pageInfo.getList()) {
//                String key = item.getCode() + "-" + item.getOpType();
//                String serviceTypeNames = serviceTypeNamesMap.get(key);
////                String serviceTypeNames = fzgjBdProductServiceService.getServiceTypeNames(item.getCode(), item.getOpType());
//                item.push("serviceTypeName", serviceTypeNames);
//            }




            return pageInfo;
        } catch (Exception e) {
            log.error("查询业务产品数据表格数据失败", e);
            return new TgPageInfo();
        }
    }


    /**
     * 查询业务产品数据表格数据
     *
     * <remark>注销的代码原因：因为不需要在进行点击弹出关联的功能，所以不需要这样查询了</remark>
     *
     * @param jsonString
     * @return
     */
    public TgPageInfo queryYeWuChanPingTablePageListServiceType(String jsonString) {
        ZsrJson json = ZsrJson.parse(jsonString).check("opType");
        String productCode = json.getStringOrDefault("code", "");
        String opType = json.getStringOrDefault("opType", "");
        String status = json.getStringOrDefault("status", "Y");
        String groupCode = UserContext.getUserInfo().getCompanyCode();
        try {
            String sql = "SELECT A.GUID, " +
                    "       A.CODE, " +
                    "       A.NAME, " +
                    "       A.STATUS, " +
                    "       A.SEQ, " +
                    "       A.MEMO, " +
                    "       A.CREATE_USER, " +
                    "       A.CREATE_DATE, " +
                    "       A.UPDATE_USER, " +
                    "       A.UPDATE_DATE, " +
                    "       A.CREATE_USER_NAME, " +
                    "       A.UPDATE_USER_NAME, " +
                    "       A.PARENTID, " +
                    "       A.CLASS_GUID, " +
                    "       A.CLASS_CODE, " +
                    "       A.EN_NAME, " +
                    "       A.TRN_DATE, " +
                    "       A.SELECT_TYPE, " +
//                    "       (SELECT WM_CONCAT(DISTINCT FZGJ_NAMECOM(T.FWXM_CODE, " +
//                    "                                      T.GROUP_CODE, " +
//                    "                                      'FZGJ_BD_SERVICE_ITEM')) " +
//                    "          FROM FZGJ_BD_PRODUCT_FWXM T " +
//                    "         WHERE T.SERVICE_NO = A.CODE " +
//                    "           AND T.OP_TYPE =  " + cmn.SQLQ(opType) +
//                    "           AND T.PRODUCT_CODE = " + cmn.SQLQ(productCode) +
//                    "           AND T.GROUP_CODE = " + cmn.SQLQ(groupCode) + ") FWXM_NAME, " +
//                    "       (SELECT WM_CONCAT(DISTINCT FZGJ_NAMECOM(T.FWXM_CODE, " +
//                    "                                      T.GROUP_CODE, " +
//                    "                                      'FZGJ_BD_SERVICE_ITEM')) " +
//                    "          FROM FZGJ_BD_PRODUCT_FWXM_FF T " +
//                    "         WHERE T.SERVICE_NO = A.CODE " +
//                    "           AND T.OP_TYPE =  " + cmn.SQLQ(opType) +
//                    "           AND T.PRODUCT_CODE = " + cmn.SQLQ(productCode) +
//                    " AND T.GROUP_CODE = " +cmn.SQLQ(groupCode)+
//                    "            ) FWXM_NAME_FF, " +
                    "       CASE " +
                    "         WHEN EXISTS (SELECT 1 " +
                    "                 FROM FZGJ_BD_PRODUCT_SERVICE B " +
                    "                WHERE B.SERVICE_NO = A.CODE " +
                    "                  AND B.OP_TYPE =  " + cmn.SQLQ(opType) +
                    "                  AND B.PRODUCT_CODE = " + cmn.SQLQ(productCode) +
                    " AND B.GROUP_CODE = " + cmn.SQLQ(groupCode) +
                    "                  ) THEN " +
                    "          'Y' " +
                    "         ELSE " +
                    "          'N' " +
                    "       END AS IS_CHECKED " +
                    "  FROM FZGJ_BD_SERVICE_TYPE_COM A " +
                    " WHERE A.GUID != '0' " +
                    "   AND A.STATUS = " + cmn.SQLQ(status) +
                    " AND A.GROUP_CODE = " + cmn.SQLQ(groupCode) +
                    " ORDER BY A.SEQ ";

            TgPageInfo<FzgjBdProductDto> pageInfo = DBHelper.selectPageList(sql, FzgjBdProductDto.class);
            return pageInfo;
        } catch (Exception e) {
            log.error("查询业务产品数据表格数据失败", e);
            return new TgPageInfo();
        }
    }


    @Override
    public TgPageInfo queryPageList(FzgjBdBillEntity entity) {
        EciQuery<FzgjBdBillEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjBdBillEntity> entities = fzgjBdBillDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }


    @SneakyThrows
    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjBdBillEntity save(FzgjBdBillEntity entity) {

        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setParentid(String.valueOf(0));
            entity.setCreateDate(new java.util.Date());
            entity.setUpdateDate(new java.util.Date());
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        } else {
            entity.setUpdateDate(new java.util.Date());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        }
        // 返回实体对象
        FzgjBdBillEntity fzgjBdBillEntity = null;
        fzgjBdBillVal.saveValidate(entity, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjBdBillEntity = fzgjBdBillDao.insertOne(entity);

        } else {

            fzgjBdBillEntity = fzgjBdBillDao.updateByEntityId(entity);

        }
        return fzgjBdBillEntity;
    }

    @Override
    public List<FzgjBdBillEntity> selectList(FzgjBdBillEntity entity) {
        return fzgjBdBillDao.selectList(entity);
    }

    @Override
    public FzgjBdBillEntity selectOneById(Serializable id) {
        return fzgjBdBillDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjBdBillEntity> list) {
        fzgjBdBillDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjBdBillDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjBdBillDao.deleteById(id);
    }

}