package com.eci.project.etmsBdCargoType.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.etmsBdCargoType.entity.EtmsBdCargoTypeEntity;

import com.eci.sso.role.entity.UserContext;
import org.springframework.stereotype.Service;

import java.util.Date;


/**
* 货物形态Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-03-21
*/
@Service
public class EtmsBdCargoTypeVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(EtmsBdCargoTypeEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(EtmsBdCargoTypeEntity entity, BusinessType businessType) {
        entity.setUpdateUser(UserContext.getUserInfo().getUserId());
        entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        entity.setUpdateDate(new Date());
        if(businessType==BusinessType.INSERT){
            entity.setCreateUser(UserContext.getUserInfo().getUserId());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setCreateDate(new Date());
        }
    }

}
