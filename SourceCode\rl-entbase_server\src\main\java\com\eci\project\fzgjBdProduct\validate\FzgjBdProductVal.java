package com.eci.project.fzgjBdProduct.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjBdProduct.entity.FzgjBdProductEntity;

import org.springframework.stereotype.Service;


/**
* 业务产品Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-01
*/
@Service
public class FzgjBdProductVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjBdProductEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjBdProductEntity entity, BusinessType businessType) {

    }

}
