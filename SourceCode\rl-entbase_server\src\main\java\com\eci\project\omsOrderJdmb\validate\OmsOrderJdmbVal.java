package com.eci.project.omsOrderJdmb.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.omsOrderJdmb.entity.OmsOrderJdmbEntity;

import org.springframework.stereotype.Service;


/**
* 接单模板Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-24
*/
@Service
public class OmsOrderJdmbVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(OmsOrderJdmbEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(OmsOrderJdmbEntity entity, BusinessType businessType) {

    }

}
