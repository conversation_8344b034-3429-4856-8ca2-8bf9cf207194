package com.eci.project.omsReceiveHistory.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.omsReceiveHistory.service.OmsReceiveHistoryService;
import com.eci.project.omsReceiveHistory.entity.OmsReceiveHistoryEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 报文接收记录Controller
*
* @<NAME_EMAIL>
* @date 2025-06-04
*/
@Api(tags = "报文接收记录")
@RestController
@RequestMapping("/omsReceiveHistory")
public class OmsReceiveHistoryController extends EciBaseController {

    @Autowired
    private OmsReceiveHistoryService omsReceiveHistoryService;


    @ApiOperation("报文接收记录:保存")
    @EciLog(title = "报文接收记录:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody OmsReceiveHistoryEntity entity){
        OmsReceiveHistoryEntity omsReceiveHistoryEntity =omsReceiveHistoryService.save(entity);
        return ResponseMsgUtil.success(10001,omsReceiveHistoryEntity);
    }


    @ApiOperation("报文接收记录:查询列表")
    @EciLog(title = "报文接收记录:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody OmsReceiveHistoryEntity entity){
        List<OmsReceiveHistoryEntity> omsReceiveHistoryEntities = omsReceiveHistoryService.selectList(entity);
        return ResponseMsgUtil.success(10001,omsReceiveHistoryEntities);
    }


    @ApiOperation("报文接收记录:分页查询列表")
    @EciLog(title = "报文接收记录:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody OmsReceiveHistoryEntity entity){
        TgPageInfo tgPageInfo = omsReceiveHistoryService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("报文接收记录:根据ID查一条")
    @EciLog(title = "报文接收记录:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody OmsReceiveHistoryEntity entity){
        OmsReceiveHistoryEntity  omsReceiveHistoryEntity = omsReceiveHistoryService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,omsReceiveHistoryEntity);
    }


    @ApiOperation("报文接收记录:根据ID删除一条")
    @EciLog(title = "报文接收记录:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody OmsReceiveHistoryEntity entity){
        int count = omsReceiveHistoryService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("报文接收记录:根据ID字符串删除多条")
    @EciLog(title = "报文接收记录:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody OmsReceiveHistoryEntity entity) {
        int count = omsReceiveHistoryService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}