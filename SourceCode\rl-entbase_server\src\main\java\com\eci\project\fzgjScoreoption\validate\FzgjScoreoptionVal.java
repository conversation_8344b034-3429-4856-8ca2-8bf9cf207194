package com.eci.project.fzgjScoreoption.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjScoreoption.entity.FzgjScoreoptionEntity;

import com.eci.sso.role.entity.UserContext;
import org.springframework.stereotype.Service;

import java.util.Date;


/**
* 评分标准设置Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-06-16
*/
@Service
public class FzgjScoreoptionVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjScoreoptionEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjScoreoptionEntity entity, BusinessType businessType) {
        entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        entity.setUpdateDate(new Date());
        entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
        if(businessType==BusinessType.INSERT){
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateDate(new Date());
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
            entity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            entity.setCompanyName(UserContext.getUserInfo().getCompanyName());
        }
    }

}
