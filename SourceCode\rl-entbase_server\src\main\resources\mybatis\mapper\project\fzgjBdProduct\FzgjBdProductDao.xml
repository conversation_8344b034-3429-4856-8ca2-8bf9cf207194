<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.fzgjBdProduct.dao.FzgjBdProductDao">
    <resultMap type="FzgjBdProductEntity" id="FzgjBdProductResult">
        <result property="guid" column="GUID"/>
        <result property="code" column="CODE"/>
        <result property="name" column="NAME"/>
        <result property="status" column="STATUS"/>
        <result property="seq" column="SEQ"/>
        <result property="memo" column="MEMO"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="opType" column="OP_TYPE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="enName" column="EN_NAME"/>
        <result property="billCode" column="BILL_CODE"/>
        <result property="isStandard" column="IS_STANDARD"/>
        <result property="customerCode" column="CUSTOMER_CODE"/>
        <result property="isFwxmEdit" column="IS_FWXM_EDIT"/>
        <result property="xzfaNo" column="XZFA_NO"/>
        <result property="picName" column="PIC_NAME"/>
        <result property="picPath" column="PIC_PATH"/>
        <result property="picStream" column="PIC_STREAM"/>
        <result property="isCsc" column="IS_CSC"/>
        <result property="udf1" column="UDF1"/>
        <result property="udf2" column="UDF2"/>
        <result property="udf3" column="UDF3"/>
    </resultMap>

    <sql id="selectFzgjBdProductEntityVo">
        select
            GUID,
            CODE,
            NAME,
            STATUS,
            SEQ,
            MEMO,
            CREATE_USER,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_DATE,
            OP_TYPE,
            COMPANY_CODE,
            CREATE_USER_NAME,
            UPDATE_USER_NAME,
            COMPANY_NAME,
            GROUP_CODE,
            GROUP_NAME,
            EN_NAME,
            BILL_CODE,
            IS_STANDARD,
            CUSTOMER_CODE,
            IS_FWXM_EDIT,
            XZFA_NO,
            PIC_NAME,
            PIC_PATH,
            PIC_STREAM,
            IS_CSC,
            UDF1,
            UDF2,
            UDF3
        from FZGJ_BD_PRODUCT
    </sql>
</mapper>