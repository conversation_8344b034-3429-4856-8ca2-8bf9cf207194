<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.etmsBdTruckCertificateQz.dao.EtmsBdTruckCertificateQzDao">
    <resultMap type="EtmsBdTruckCertificateQzEntity" id="EtmsBdTruckCertificateQzResult">
        <result property="guid" column="GUID"/>
        <result property="truckGuid" column="TRUCK_GUID"/>
        <result property="certificateType" column="CERTIFICATE_TYPE"/>
        <result property="certificateName" column="CERTIFICATE_NAME"/>
        <result property="certificateNo" column="CERTIFICATE_NO"/>
        <result property="certificateStatus" column="CERTIFICATE_STATUS"/>
        <result property="issueDate" column="ISSUE_DATE"/>
        <result property="validityDate" column="VALIDITY_DATE"/>
        <result property="remark" column="REMARK"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="modMark" column="MOD_MARK"/>
    </resultMap>

    <sql id="selectEtmsBdTruckCertificateQzEntityVo">
        select
            GUID,
            TRUCK_GUID,
            CERTIFICATE_TYPE,
            CERTIFICATE_NAME,
            CERTIFICATE_NO,
            CERTIFICATE_STATUS,
            ISSUE_DATE,
            VALIDITY_DATE,
            REMARK,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            CREATE_USER_NAME,
            UPDATE_USER_NAME,
            CREATE_DATE,
            UPDATE_DATE,
            MOD_MARK
        from ETMS_BD_TRUCK_CERTIFICATE_QZ
    </sql>
</mapper>