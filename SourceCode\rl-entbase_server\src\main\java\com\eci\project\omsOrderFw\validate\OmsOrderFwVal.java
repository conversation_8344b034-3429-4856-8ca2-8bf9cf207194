package com.eci.project.omsOrderFw.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.omsOrderFw.entity.OmsOrderFwEntity;

import org.springframework.stereotype.Service;


/**
* 订单服务类型Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-24
*/
@Service
public class OmsOrderFwVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(OmsOrderFwEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(OmsOrderFwEntity entity, BusinessType businessType) {

    }

}
