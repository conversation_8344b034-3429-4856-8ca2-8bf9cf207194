package com.eci.project.fzgjBoxSize.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.fzgjBoxSize.entity.FzgjBoxSizeEntity;


/**
* 集装箱尺寸Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-04-17
*/
public interface FzgjBoxSizeDao extends EciBaseDao<FzgjBoxSizeEntity> {

}