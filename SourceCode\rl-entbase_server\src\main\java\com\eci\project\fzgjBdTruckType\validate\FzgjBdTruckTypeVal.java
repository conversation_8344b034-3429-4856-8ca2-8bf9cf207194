package com.eci.project.fzgjBdTruckType.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjBdTruckType.entity.FzgjBdTruckTypeEntity;

import com.eci.sso.role.entity.UserContext;
import org.springframework.stereotype.Service;

import java.util.Date;


/**
* 车辆类型Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-03-14
*/
@Service
public class FzgjBdTruckTypeVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjBdTruckTypeEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjBdTruckTypeEntity entity, BusinessType businessType) {
        //TODO entity.Group_Code是集团编码 但是现有权限平台没有集团这个概念 暂时先不管
        entity.setUpdateUser(UserContext.getUserInfo().getUserId());
        entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        entity.setUpdateDate(new Date());
        if(businessType==BusinessType.INSERT){
            entity.setCreateUser(UserContext.getUserInfo().getUserId());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setCreateDate(new Date());
//            entity.setNodeCode(UserContext.getUserInfo().getDeptCode());
//            entity.setNodeName(UserContext.getUserInfo().getDeptName());
//            entity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
//            entity.setCompanyName(UserContext.getUserInfo().getCompanyName());
        }
    }

}
