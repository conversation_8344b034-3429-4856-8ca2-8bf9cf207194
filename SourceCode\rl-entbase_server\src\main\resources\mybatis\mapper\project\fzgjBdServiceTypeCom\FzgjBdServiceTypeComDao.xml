<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.fzgjBdServiceTypeCom.dao.FzgjBdServiceTypeComDao">
    <resultMap type="FzgjBdServiceTypeComEntity" id="FzgjBdServiceTypeComResult">
        <result property="guid" column="GUID"/>
        <result property="code" column="CODE"/>
        <result property="name" column="NAME"/>
        <result property="status" column="STATUS"/>
        <result property="seq" column="SEQ"/>
        <result property="memo" column="MEMO"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="parentid" column="PARENTID"/>
        <result property="classGuid" column="CLASS_GUID"/>
        <result property="classCode" column="CLASS_CODE"/>
        <result property="enName" column="EN_NAME"/>
        <result property="trnDate" column="TRN_DATE"/>
        <result property="selectType" column="SELECT_TYPE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
    </resultMap>

    <sql id="selectFzgjBdServiceTypeComEntityVo">
        select
            GUID,
            CODE,
            NAME,
            STATUS,
            SEQ,
            MEMO,
            CREATE_USER,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_DATE,
            CREATE_USER_NAME,
            UPDATE_USER_NAME,
            PARENTID,
            CLASS_GUID,
            CLASS_CODE,
            EN_NAME,
            TRN_DATE,
            SELECT_TYPE,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME
        from FZGJ_BD_SERVICE_TYPE_COM
    </sql>
</mapper>