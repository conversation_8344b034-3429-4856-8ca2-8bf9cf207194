package com.eci.project.fzgjCrmFileType.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjCrmFileType.service.FzgjCrmFileTypeService;
import com.eci.project.fzgjCrmFileType.entity.FzgjCrmFileTypeEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* CRM附件类型Controller
*
* @<NAME_EMAIL>
* @date 2025-04-10
*/
@Api(tags = "CRM附件类型")
@RestController
@RequestMapping("/fzgjCrmFileType")
public class FzgjCrmFileTypeController extends EciBaseController {

    @Autowired
    private FzgjCrmFileTypeService fzgjCrmFileTypeService;


    @ApiOperation("CRM附件类型:保存")
    @EciLog(title = "CRM附件类型:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody FzgjCrmFileTypeEntity entity){
        FzgjCrmFileTypeEntity fzgjCrmFileTypeEntity =fzgjCrmFileTypeService.save(entity);
        return ResponseMsgUtil.success(10001,fzgjCrmFileTypeEntity);
    }


    @ApiOperation("CRM附件类型:查询列表")
    @EciLog(title = "CRM附件类型:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjCrmFileTypeEntity entity){
        List<FzgjCrmFileTypeEntity> fzgjCrmFileTypeEntities = fzgjCrmFileTypeService.selectList(entity);
        return ResponseMsgUtil.success(10001,fzgjCrmFileTypeEntities);
    }


    @ApiOperation("CRM附件类型:分页查询列表")
    @EciLog(title = "CRM附件类型:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjCrmFileTypeEntity entity){
        TgPageInfo tgPageInfo = fzgjCrmFileTypeService.queryPageList(entity);
        return ResponseMsgUtil.successPlus(10001,tgPageInfo);
    }


    @ApiOperation("CRM附件类型:根据ID查一条")
    @EciLog(title = "CRM附件类型:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjCrmFileTypeEntity entity){
        FzgjCrmFileTypeEntity  fzgjCrmFileTypeEntity = fzgjCrmFileTypeService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,fzgjCrmFileTypeEntity);
    }


    @ApiOperation("CRM附件类型:根据ID删除一条")
    @EciLog(title = "CRM附件类型:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjCrmFileTypeEntity entity){
        int count = fzgjCrmFileTypeService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("CRM附件类型:根据ID字符串删除多条")
    @EciLog(title = "CRM附件类型:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjCrmFileTypeEntity entity) {
        int count = fzgjCrmFileTypeService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}