package com.eci.project.fzgjCrmEnterpriseSys.controller;

import com.eci.common.ResponseMsgUtilX;
import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjCrmEnterpriseSys.service.FzgjCrmEnterpriseSysService;
import com.eci.project.fzgjCrmEnterpriseSys.entity.FzgjCrmEnterpriseSysEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 作业系统-数据归属集团Controller
*
* @<NAME_EMAIL>
* @date 2025-03-13
*/
@Api(tags = "作业系统-数据归属集团")
@RestController
    @RequestMapping("/fzgjCrmEnterpriseSys")
public class FzgjCrmEnterpriseSysController extends EciBaseController {

    @Autowired
    private FzgjCrmEnterpriseSysService fzgjCrmEnterpriseSysService;


    @ApiOperation("作业系统-数据归属集团:保存")
    @EciLog(title = "作业系统-数据归属集团:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody FzgjCrmEnterpriseSysEntity entity){
        FzgjCrmEnterpriseSysEntity fzgjCrmEnterpriseSysEntity =fzgjCrmEnterpriseSysService.save(entity);
        return ResponseMsgUtil.success(10001,fzgjCrmEnterpriseSysEntity);
    }


    @ApiOperation("作业系统-数据归属集团:查询列表")
    @EciLog(title = "作业系统-数据归属集团:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjCrmEnterpriseSysEntity entity){
        List<FzgjCrmEnterpriseSysEntity> fzgjCrmEnterpriseSysEntities = fzgjCrmEnterpriseSysService.selectList(entity);
        return ResponseMsgUtilX.success(10001,fzgjCrmEnterpriseSysEntities);
    }


    @ApiOperation("作业系统-数据归属集团:分页查询列表")
    @EciLog(title = "作业系统-数据归属集团:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjCrmEnterpriseSysEntity entity){
        TgPageInfo tgPageInfo = fzgjCrmEnterpriseSysService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("作业系统-数据归属集团:根据ID查一条")
    @EciLog(title = "作业系统-数据归属集团:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjCrmEnterpriseSysEntity entity){
        FzgjCrmEnterpriseSysEntity  fzgjCrmEnterpriseSysEntity = fzgjCrmEnterpriseSysService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,fzgjCrmEnterpriseSysEntity);
    }


    @ApiOperation("作业系统-数据归属集团:根据ID删除一条")
    @EciLog(title = "作业系统-数据归属集团:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjCrmEnterpriseSysEntity entity){
        int count = fzgjCrmEnterpriseSysService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("作业系统-数据归属集团:根据ID字符串删除多条")
    @EciLog(title = "作业系统-数据归属集团:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjCrmEnterpriseSysEntity entity) {
        int count = fzgjCrmEnterpriseSysService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}