package com.eci.project.fzgjBdServiceItemPagesPt.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjBdServiceItemPagesPt.service.FzgjBdServiceItemPagesPtService;
import com.eci.project.fzgjBdServiceItemPagesPt.entity.FzgjBdServiceItemPagesPtEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 企业服务项目对应页面编辑区Controller
*
* @<NAME_EMAIL>
* @date 2025-04-01
*/
@Api(tags = "企业服务项目对应页面编辑区")
@RestController
@RequestMapping("/fzgjBdServiceItemPagesPt")
public class FzgjBdServiceItemPagesPtController extends EciBaseController {

    @Autowired
    private FzgjBdServiceItemPagesPtService fzgjBdServiceItemPagesPtService;


    @ApiOperation("企业服务项目对应页面编辑区:保存")
    @EciLog(title = "企业服务项目对应页面编辑区:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody FzgjBdServiceItemPagesPtEntity entity){
        FzgjBdServiceItemPagesPtEntity fzgjBdServiceItemPagesPtEntity =fzgjBdServiceItemPagesPtService.save(entity);
        return ResponseMsgUtil.success(10001,fzgjBdServiceItemPagesPtEntity);
    }


    @ApiOperation("企业服务项目对应页面编辑区:查询列表")
    @EciLog(title = "企业服务项目对应页面编辑区:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjBdServiceItemPagesPtEntity entity){
        List<FzgjBdServiceItemPagesPtEntity> fzgjBdServiceItemPagesPtEntities = fzgjBdServiceItemPagesPtService.selectList(entity);
        return ResponseMsgUtil.success(10001,fzgjBdServiceItemPagesPtEntities);
    }


    @ApiOperation("企业服务项目对应页面编辑区:分页查询列表")
    @EciLog(title = "企业服务项目对应页面编辑区:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjBdServiceItemPagesPtEntity entity){
        TgPageInfo tgPageInfo = fzgjBdServiceItemPagesPtService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("企业服务项目对应页面编辑区:根据ID查一条")
    @EciLog(title = "企业服务项目对应页面编辑区:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjBdServiceItemPagesPtEntity entity){
        FzgjBdServiceItemPagesPtEntity  fzgjBdServiceItemPagesPtEntity = fzgjBdServiceItemPagesPtService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,fzgjBdServiceItemPagesPtEntity);
    }


    @ApiOperation("企业服务项目对应页面编辑区:根据ID删除一条")
    @EciLog(title = "企业服务项目对应页面编辑区:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjBdServiceItemPagesPtEntity entity){
        int count = fzgjBdServiceItemPagesPtService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("企业服务项目对应页面编辑区:根据ID字符串删除多条")
    @EciLog(title = "企业服务项目对应页面编辑区:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjBdServiceItemPagesPtEntity entity) {
        int count = fzgjBdServiceItemPagesPtService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}