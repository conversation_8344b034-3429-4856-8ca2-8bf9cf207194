package com.eci.project.fzgjGoodsAttr.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjGoodsAttr.entity.FzgjGoodsAttrEntity;

import org.springframework.stereotype.Service;


/**
* 货物属性Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-01
*/
@Service
public class FzgjGoodsAttrVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjGoodsAttrEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjGoodsAttrEntity entity, BusinessType businessType) {

    }

}
