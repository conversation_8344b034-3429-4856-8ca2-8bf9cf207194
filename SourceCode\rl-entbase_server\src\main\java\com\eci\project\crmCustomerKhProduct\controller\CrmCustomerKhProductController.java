package com.eci.project.crmCustomerKhProduct.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.crmCustomerKhProduct.service.CrmCustomerKhProductService;
import com.eci.project.crmCustomerKhProduct.entity.CrmCustomerKhProductEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 客户信息-关联业务产品Controller
*
* @<NAME_EMAIL>
* @date 2025-05-12
*/
@Api(tags = "客户信息-关联业务产品")
@RestController
@RequestMapping("/crmCustomerKhProduct")
public class CrmCustomerKhProductController extends EciBaseController {

    @Autowired
    private CrmCustomerKhProductService crmCustomerKhProductService;


    @ApiOperation("客户信息-关联业务产品:保存")
    @EciLog(title = "客户信息-关联业务产品:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody CrmCustomerKhProductEntity entity){
        CrmCustomerKhProductEntity crmCustomerKhProductEntity =crmCustomerKhProductService.save(entity);
        return ResponseMsgUtil.success(10001,crmCustomerKhProductEntity);
    }


    @ApiOperation("客户信息-关联业务产品:查询列表")
    @EciLog(title = "客户信息-关联业务产品:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody CrmCustomerKhProductEntity entity){
        List<CrmCustomerKhProductEntity> crmCustomerKhProductEntities = crmCustomerKhProductService.selectList(entity);
        return ResponseMsgUtil.success(10001,crmCustomerKhProductEntities);
    }


    @ApiOperation("客户信息-关联业务产品:分页查询列表")
    @EciLog(title = "客户信息-关联业务产品:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody CrmCustomerKhProductEntity entity){
        TgPageInfo tgPageInfo = crmCustomerKhProductService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("客户信息-关联业务产品:根据ID查一条")
    @EciLog(title = "客户信息-关联业务产品:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody CrmCustomerKhProductEntity entity){
        CrmCustomerKhProductEntity  crmCustomerKhProductEntity = crmCustomerKhProductService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,crmCustomerKhProductEntity);
    }


    @ApiOperation("客户信息-关联业务产品:根据ID删除一条")
    @EciLog(title = "客户信息-关联业务产品:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody CrmCustomerKhProductEntity entity){
        int count = crmCustomerKhProductService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("客户信息-关联业务产品:根据ID字符串删除多条")
    @EciLog(title = "客户信息-关联业务产品:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody CrmCustomerKhProductEntity entity) {
        int count = crmCustomerKhProductService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}