package com.eci.project.etmsBdTruckInsuranceQz.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.etmsBdTruckInsuranceQz.entity.EtmsBdTruckInsuranceQzEntity;

import org.springframework.stereotype.Service;


/**
* 车辆保险历史Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-15
*/
@Service
public class EtmsBdTruckInsuranceQzVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(EtmsBdTruckInsuranceQzEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(EtmsBdTruckInsuranceQzEntity entity, BusinessType businessType) {

    }

}
