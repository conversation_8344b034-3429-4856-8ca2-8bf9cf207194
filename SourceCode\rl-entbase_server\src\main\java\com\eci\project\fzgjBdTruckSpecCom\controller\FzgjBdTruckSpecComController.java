package com.eci.project.fzgjBdTruckSpecCom.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.project.fzgjBdTruckSpecCom.service.IFzgjBdTruckSpecComService;
import com.eci.project.fzgjBdTruckSpecCom.entity.FzgjBdTruckSpecComEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.EciLog;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


/**
* 计费车辆尺寸Controller
*
* @<NAME_EMAIL>
* @date 2025-03-25
*/
@Api(tags = "计费车辆尺寸")
@RestController
@RequestMapping("/fzgjBdTruckSpecCom")
public class FzgjBdTruckSpecComController extends EciBaseController {

    @Autowired
    private IFzgjBdTruckSpecComService fzgjBdTruckSpecComService;

    /**
    * 根据实体插入数据库
    * @param entity
    * @return
    */
    @ApiOperation("计费车辆尺寸:保存")
    @EciLog(title = "计费车辆尺寸:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public ResponseMsg save(@RequestBody FzgjBdTruckSpecComEntity entity){
        return ResponseMsgUtil.success(10001,fzgjBdTruckSpecComService.save(entity));
    }

    /**
    * 根据条件查询一个集合
    * @param entity
    * @return
    */
    @ApiOperation("计费车辆尺寸:查询列表")
    @EciLog(title = "计费车辆尺寸:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    public ResponseMsg selectList(@RequestBody FzgjBdTruckSpecComEntity entity){
        return ResponseMsgUtil.successPlus(10001,fzgjBdTruckSpecComService.selectList(entity));
    }

    /**
    * 分页查询
    * @param entity
    * @return
    */
    @ApiOperation("计费车辆尺寸:分页查询列表")
    @EciLog(title = "计费车辆尺寸:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    public ResponseMsg selectPageList(@RequestBody FzgjBdTruckSpecComEntity entity){
        return ResponseMsgUtil.successPlus(10001,fzgjBdTruckSpecComService.queryPageList(entity));
    }

    /**
    * 根据ID查一条记录
    * @param entity
    * @return
    */
    @ApiOperation("计费车辆尺寸:根据ID查一条")
    @EciLog(title = "计费车辆尺寸:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    public ResponseMsg selectOneById(@RequestBody FzgjBdTruckSpecComEntity entity){
        return ResponseMsgUtil.successPlus(10001,fzgjBdTruckSpecComService.selectOneById(entity.getGuid()));
    }


    /**
    * 根据ID删除
    * @param entity
    * @return
    */
    @ApiOperation("计费车辆尺寸:根据ID删除一条")
    @EciLog(title = "计费车辆尺寸:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    public ResponseMsg deleteById(@RequestBody FzgjBdTruckSpecComEntity entity){
        return ResponseMsgUtil.success(10001,fzgjBdTruckSpecComService.deleteById(entity.getGuid()));
    }

    /**
    * 根据IDS删除
    *
    * @param entity
    * @return
    */
    @ApiOperation("计费车辆尺寸:根据ID字符串删除多条")
    @EciLog(title = "计费车辆尺寸:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    public ResponseMsg deleteByIds(@RequestBody FzgjBdTruckSpecComEntity entity) {
        return ResponseMsgUtil.success(10001, fzgjBdTruckSpecComService.deleteByIds(entity.getIds()));
    }


}