package com.eci.project.fzgjBoxSize.service;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjBoxSize.dao.FzgjBoxSizeDao;
import com.eci.project.fzgjBoxSize.entity.FzgjBoxSizeEntity;
import com.eci.project.fzgjBoxSize.validate.FzgjBoxSizeVal;

import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
* 集装箱尺寸Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-17
*/
@Service
@Slf4j
public class FzgjBoxSizeService implements EciBaseService<FzgjBoxSizeEntity> {

    @Autowired
    private FzgjBoxSizeDao fzgjBoxSizeDao;

    @Autowired
    private FzgjBoxSizeVal fzgjBoxSizeVal;


    @Override
    public TgPageInfo queryPageList(FzgjBoxSizeEntity entity) {
        EciQuery<FzgjBoxSizeEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjBoxSizeEntity> entities = fzgjBoxSizeDao.queryPageList(eciQuery);

        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjBoxSizeEntity save(FzgjBoxSizeEntity entity) {
        // 返回实体对象
        FzgjBoxSizeEntity fzgjBoxSizeEntity = null;
        fzgjBoxSizeVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setGuid(IdWorker.getIdStr());
            entity.setCreateDate(new Date());
            entity.setCreateUser(UserContext.getUserInfo().getUserId());
            entity.setCreateUserName(UserContext.getUserInfo().getUserNickname());
            entity.setUpdateDate(new Date());
            entity.setUpdateUser(UserContext.getUserInfo().getUserId());
            entity.setUpdateUserName(UserContext.getUserInfo().getUserNickname());
            entity.setNodeCode(UserContext.getUserInfo().getCompanyCode());
            entity.setNodeName(UserContext.getUserInfo().getCompanyName());
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
            entity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            entity.setCompanyName(UserContext.getUserInfo().getCompanyName());
            fzgjBoxSizeEntity = fzgjBoxSizeDao.insertOne(entity);

        }else{

            entity.setUpdateDate(new Date());
            entity.setUpdateUser(UserContext.getUserInfo().getUserId());
            entity.setUpdateUserName(UserContext.getUserInfo().getUserNickname());
            entity.setNodeCode(UserContext.getUserInfo().getCompanyCode());
            entity.setNodeName(UserContext.getUserInfo().getCompanyName());
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
            entity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            entity.setCompanyName(UserContext.getUserInfo().getCompanyName());
            fzgjBoxSizeEntity = fzgjBoxSizeDao.updateByEntityId(entity);

        }
        return fzgjBoxSizeEntity;
    }

    @Override
    public List<FzgjBoxSizeEntity> selectList(FzgjBoxSizeEntity entity) {
        return fzgjBoxSizeDao.selectList(entity);
    }

    @Override
    public FzgjBoxSizeEntity selectOneById(Serializable id) {
        return fzgjBoxSizeDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjBoxSizeEntity> list) {
        fzgjBoxSizeDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjBoxSizeDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjBoxSizeDao.deleteById(id);
    }

}