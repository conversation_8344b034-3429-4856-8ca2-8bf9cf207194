package com.eci.project.etmsBdGpsType.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.etmsBdGpsType.service.EtmsBdGpsTypeService;
import com.eci.project.etmsBdGpsType.entity.EtmsBdGpsTypeEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 定位方式Controller
*
* @<NAME_EMAIL>
* @date 2025-03-21
*/
@Api(tags = "定位方式")
@RestController
@RequestMapping("/etmsBdGpsType")
public class EtmsBdGpsTypeController extends EciBaseController {

    @Autowired
    private EtmsBdGpsTypeService etmsBdGpsTypeService;


    @ApiOperation("定位方式:保存")
    @EciLog(title = "定位方式:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody EtmsBdGpsTypeEntity entity){
        EtmsBdGpsTypeEntity etmsBdGpsTypeEntity =etmsBdGpsTypeService.save(entity);
        return ResponseMsgUtil.success(10001,etmsBdGpsTypeEntity);
    }


    @ApiOperation("定位方式:查询列表")
    @EciLog(title = "定位方式:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody EtmsBdGpsTypeEntity entity){
        List<EtmsBdGpsTypeEntity> etmsBdGpsTypeEntities = etmsBdGpsTypeService.selectList(entity);
        return ResponseMsgUtil.success(10001,etmsBdGpsTypeEntities);
    }


    @ApiOperation("定位方式:分页查询列表")
    @EciLog(title = "定位方式:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody EtmsBdGpsTypeEntity entity){
        TgPageInfo tgPageInfo = etmsBdGpsTypeService.queryPageList(entity);
        return ResponseMsgUtil.successPlus(10001,tgPageInfo);
    }


    @ApiOperation("定位方式:根据ID查一条")
    @EciLog(title = "定位方式:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody EtmsBdGpsTypeEntity entity){
        EtmsBdGpsTypeEntity  etmsBdGpsTypeEntity = etmsBdGpsTypeService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,etmsBdGpsTypeEntity);
    }


    @ApiOperation("定位方式:根据ID删除一条")
    @EciLog(title = "定位方式:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody EtmsBdGpsTypeEntity entity){
        int count = etmsBdGpsTypeService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("定位方式:根据ID字符串删除多条")
    @EciLog(title = "定位方式:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody EtmsBdGpsTypeEntity entity) {
        int count = etmsBdGpsTypeService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}