package com.eci.project.etmsOpAttemperCar.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.math.BigDecimal;
import java.util.Date;


/**
* 用车需求信息对象 ETMS_OP_ATTEMPER_CAR
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-06-13
*/
@ApiModel("用车需求信息")
@TableName("ETMS_OP_ATTEMPER_CAR")
@FieldNameConstants
public class EtmsOpAttemperCarEntity extends EciBaseEntity{
    /**
    * 主键
    */
    @ApiModelProperty("主键(50)")
    @TableId("GUID")
    private String guid;

    /**
    * 委托GUID
    */
    @ApiModelProperty("委托GUID(50)")
    @TableField("ATT_GUID")
    private String attGuid;

    /**
    * 业务编号
    */
    @ApiModelProperty("业务编号(20)")
    @TableField("OP_NO")
    private String opNo;

    /**
    * 整车/零担/快递
    */
    @ApiModelProperty("整车/零担/快递(20)")
    @TableField("DELIVERY_TYPE")
    private String deliveryType;

    /**
    * 整车-车辆类别
    */
    @ApiModelProperty("整车-车辆类别(20)")
    @TableField("ZC_TYPE")
    private String zcType;

    /**
    * 整车-车辆规格
    */
    @ApiModelProperty("整车-车辆规格(20)")
    @TableField("ZC_MODEL")
    private String zcModel;

    /**
    * 整车车数
    */
    @ApiModelProperty("整车车数(22)")
    @TableField("ZC_NUM")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal zcNum;

    /**
    * 零担计费依据
    */
    @ApiModelProperty("零担计费依据(20)")
    @TableField("LD_BALANCE")
    private String ldBalance;

    /**
    * 是否集装箱
    */
    @ApiModelProperty("是否集装箱(1)")
    @TableField("IS_JZX")
    private String isJzx;

    /**
    * 状态
    */
    @ApiModelProperty("状态(10)")
    @TableField("STATUS")
    private String status;

    /**
    * 创建企业
    */
    @ApiModelProperty("创建企业(20)")
    @TableField("CREATE_COMPANY")
    private String createCompany;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 编辑人
    */
    @ApiModelProperty("编辑人(50)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 编辑日期
    */
    @ApiModelProperty("编辑日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("编辑日期开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("编辑日期结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 用车需求编号
    */
    @ApiModelProperty("用车需求编号(20)")
    @TableField("OPCAR_NO")
    private String opcarNo;

    /**
    * 最远目的地
    */
    @ApiModelProperty("最远目的地(50)")
    @TableField("FARATHEST_END")
    private String farathestEnd;

    /**
    * 所属部门ID
    */
    @ApiModelProperty("所属部门ID(50)")
    @TableField("ORG_DEP_ID")
    private String orgDepId;

    /**
    * 所属部门CODE
    */
    @ApiModelProperty("所属部门CODE(50)")
    @TableField("ORG_DEP_CODE")
    private String orgDepCode;

    /**
    * 所属部门名称
    */
    @ApiModelProperty("所属部门名称(50)")
    @TableField("ORG_DEP_NAME")
    private String orgDepName;

    /**
    * 运输完成时间
    */
    @ApiModelProperty("运输完成时间(7)")
    @TableField("FINISHED_DATE")
    private Date finishedDate;

    @ApiModelProperty("运输完成时间开始")
    @TableField(exist=false)
    private Date finishedDateStart;

    @ApiModelProperty("运输完成时间结束")
    @TableField(exist=false)
    private Date finishedDateEnd;

    /**
    * 委托编号
    */
    @ApiModelProperty("委托编号(20)")
    @TableField("ATT_NO")
    private String attNo;

    /**
    * 件数
    */
    @ApiModelProperty("件数(200)")
    @TableField("PIECES")
    private String pieces;

    /**
    * 重量
    */
    @ApiModelProperty("重量(200)")
    @TableField("WEIGHT")
    private String weight;

    /**
    * 体积
    */
    @ApiModelProperty("体积(200)")
    @TableField("VOLUME")
    private String volume;

    /**
    * 货物
    */
    @ApiModelProperty("货物(500)")
    @TableField("CARGO")
    private String cargo;

    /**
    * 计费车辆规格*车数
    */
    @ApiModelProperty("计费车辆规格*车数(200)")
    @TableField("TRUCK_SPEC")
    private String truckSpec;

    /**
    * 发货方业务伙伴
    */
    @ApiModelProperty("发货方业务伙伴(400)")
    @TableField("SENDER_CODE")
    private String senderCode;

    /**
    * 收货方业务伙伴
    */
    @ApiModelProperty("收货方业务伙伴(400)")
    @TableField("CONSIGNEE_CODE")
    private String consigneeCode;

    /**
    * 起始要求作业时间
    */
    @ApiModelProperty("起始要求作业时间(7)")
    @TableField("START_REQUEST_DATE")
    private Date startRequestDate;

    @ApiModelProperty("起始要求作业时间开始")
    @TableField(exist=false)
    private Date startRequestDateStart;

    @ApiModelProperty("起始要求作业时间结束")
    @TableField(exist=false)
    private Date startRequestDateEnd;

    /**
    * 终到要求作业时间
    */
    @ApiModelProperty("终到要求作业时间(7)")
    @TableField("END_REQUEST_DATE")
    private Date endRequestDate;

    @ApiModelProperty("终到要求作业时间开始")
    @TableField(exist=false)
    private Date endRequestDateStart;

    @ApiModelProperty("终到要求作业时间结束")
    @TableField(exist=false)
    private Date endRequestDateEnd;

    /**
    * 起始地
    */
    @ApiModelProperty("起始地(400)")
    @TableField("START_OP_AREA")
    private String startOpArea;

    /**
    * 终到地
    */
    @ApiModelProperty("终到地(400)")
    @TableField("END_OP_AREA")
    private String endOpArea;

    /**
    * 线路HTML冗余字段 
    */
    @ApiModelProperty("线路HTML冗余字段 (4,000)")
    @TableField("CAR_LINE")
    private String carLine;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 车辆性质
    */
    @ApiModelProperty("车辆性质(50)")
    @TableField("ATTRIBUTE_CODE")
    private String attributeCode;

    @ApiModelProperty("(20)")
    @TableField("PC_TYPE")
    private String pcType;

    @ApiModelProperty("(50)")
    @TableField("PC_NO")
    private String pcNo;

    @ApiModelProperty("(50)")
    @TableField("PC_CAR_GUID")
    private String pcCarGuid;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public EtmsOpAttemperCarEntity() {
        this.setSubClazz(EtmsOpAttemperCarEntity.class);
    }

    public EtmsOpAttemperCarEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public EtmsOpAttemperCarEntity setAttGuid(String attGuid) {
        this.attGuid = attGuid;
        this.nodifySetFiled("attGuid", attGuid);
        return this;
    }

    public String getAttGuid() {
        this.nodifyGetFiled("attGuid");
        return attGuid;
    }

    public EtmsOpAttemperCarEntity setOpNo(String opNo) {
        this.opNo = opNo;
        this.nodifySetFiled("opNo", opNo);
        return this;
    }

    public String getOpNo() {
        this.nodifyGetFiled("opNo");
        return opNo;
    }

    public EtmsOpAttemperCarEntity setDeliveryType(String deliveryType) {
        this.deliveryType = deliveryType;
        this.nodifySetFiled("deliveryType", deliveryType);
        return this;
    }

    public String getDeliveryType() {
        this.nodifyGetFiled("deliveryType");
        return deliveryType;
    }

    public EtmsOpAttemperCarEntity setZcType(String zcType) {
        this.zcType = zcType;
        this.nodifySetFiled("zcType", zcType);
        return this;
    }

    public String getZcType() {
        this.nodifyGetFiled("zcType");
        return zcType;
    }

    public EtmsOpAttemperCarEntity setZcModel(String zcModel) {
        this.zcModel = zcModel;
        this.nodifySetFiled("zcModel", zcModel);
        return this;
    }

    public String getZcModel() {
        this.nodifyGetFiled("zcModel");
        return zcModel;
    }

    public EtmsOpAttemperCarEntity setZcNum(BigDecimal zcNum) {
        this.zcNum = zcNum;
        this.nodifySetFiled("zcNum", zcNum);
        return this;
    }

    public BigDecimal getZcNum() {
        this.nodifyGetFiled("zcNum");
        return zcNum;
    }

    public EtmsOpAttemperCarEntity setLdBalance(String ldBalance) {
        this.ldBalance = ldBalance;
        this.nodifySetFiled("ldBalance", ldBalance);
        return this;
    }

    public String getLdBalance() {
        this.nodifyGetFiled("ldBalance");
        return ldBalance;
    }

    public EtmsOpAttemperCarEntity setIsJzx(String isJzx) {
        this.isJzx = isJzx;
        this.nodifySetFiled("isJzx", isJzx);
        return this;
    }

    public String getIsJzx() {
        this.nodifyGetFiled("isJzx");
        return isJzx;
    }

    public EtmsOpAttemperCarEntity setStatus(String status) {
        this.status = status;
        this.nodifySetFiled("status", status);
        return this;
    }

    public String getStatus() {
        this.nodifyGetFiled("status");
        return status;
    }

    public EtmsOpAttemperCarEntity setCreateCompany(String createCompany) {
        this.createCompany = createCompany;
        this.nodifySetFiled("createCompany", createCompany);
        return this;
    }

    public String getCreateCompany() {
        this.nodifyGetFiled("createCompany");
        return createCompany;
    }

    public EtmsOpAttemperCarEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public EtmsOpAttemperCarEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public EtmsOpAttemperCarEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public EtmsOpAttemperCarEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public EtmsOpAttemperCarEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public EtmsOpAttemperCarEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public EtmsOpAttemperCarEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public EtmsOpAttemperCarEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public EtmsOpAttemperCarEntity setOpcarNo(String opcarNo) {
        this.opcarNo = opcarNo;
        this.nodifySetFiled("opcarNo", opcarNo);
        return this;
    }

    public String getOpcarNo() {
        this.nodifyGetFiled("opcarNo");
        return opcarNo;
    }

    public EtmsOpAttemperCarEntity setFarathestEnd(String farathestEnd) {
        this.farathestEnd = farathestEnd;
        this.nodifySetFiled("farathestEnd", farathestEnd);
        return this;
    }

    public String getFarathestEnd() {
        this.nodifyGetFiled("farathestEnd");
        return farathestEnd;
    }

    public EtmsOpAttemperCarEntity setOrgDepId(String orgDepId) {
        this.orgDepId = orgDepId;
        this.nodifySetFiled("orgDepId", orgDepId);
        return this;
    }

    public String getOrgDepId() {
        this.nodifyGetFiled("orgDepId");
        return orgDepId;
    }

    public EtmsOpAttemperCarEntity setOrgDepCode(String orgDepCode) {
        this.orgDepCode = orgDepCode;
        this.nodifySetFiled("orgDepCode", orgDepCode);
        return this;
    }

    public String getOrgDepCode() {
        this.nodifyGetFiled("orgDepCode");
        return orgDepCode;
    }

    public EtmsOpAttemperCarEntity setOrgDepName(String orgDepName) {
        this.orgDepName = orgDepName;
        this.nodifySetFiled("orgDepName", orgDepName);
        return this;
    }

    public String getOrgDepName() {
        this.nodifyGetFiled("orgDepName");
        return orgDepName;
    }

    public EtmsOpAttemperCarEntity setFinishedDate(Date finishedDate) {
        this.finishedDate = finishedDate;
        this.nodifySetFiled("finishedDate", finishedDate);
        return this;
    }

    public Date getFinishedDate() {
        this.nodifyGetFiled("finishedDate");
        return finishedDate;
    }

    public EtmsOpAttemperCarEntity setFinishedDateStart(Date finishedDateStart) {
        this.finishedDateStart = finishedDateStart;
        this.nodifySetFiled("finishedDateStart", finishedDateStart);
        return this;
    }

    public Date getFinishedDateStart() {
        this.nodifyGetFiled("finishedDateStart");
        return finishedDateStart;
    }

    public EtmsOpAttemperCarEntity setFinishedDateEnd(Date finishedDateEnd) {
        this.finishedDateEnd = finishedDateEnd;
        this.nodifySetFiled("finishedDateEnd", finishedDateEnd);
        return this;
    }

    public Date getFinishedDateEnd() {
        this.nodifyGetFiled("finishedDateEnd");
        return finishedDateEnd;
    }
    public EtmsOpAttemperCarEntity setAttNo(String attNo) {
        this.attNo = attNo;
        this.nodifySetFiled("attNo", attNo);
        return this;
    }

    public String getAttNo() {
        this.nodifyGetFiled("attNo");
        return attNo;
    }

    public EtmsOpAttemperCarEntity setPieces(String pieces) {
        this.pieces = pieces;
        this.nodifySetFiled("pieces", pieces);
        return this;
    }

    public String getPieces() {
        this.nodifyGetFiled("pieces");
        return pieces;
    }

    public EtmsOpAttemperCarEntity setWeight(String weight) {
        this.weight = weight;
        this.nodifySetFiled("weight", weight);
        return this;
    }

    public String getWeight() {
        this.nodifyGetFiled("weight");
        return weight;
    }

    public EtmsOpAttemperCarEntity setVolume(String volume) {
        this.volume = volume;
        this.nodifySetFiled("volume", volume);
        return this;
    }

    public String getVolume() {
        this.nodifyGetFiled("volume");
        return volume;
    }

    public EtmsOpAttemperCarEntity setCargo(String cargo) {
        this.cargo = cargo;
        this.nodifySetFiled("cargo", cargo);
        return this;
    }

    public String getCargo() {
        this.nodifyGetFiled("cargo");
        return cargo;
    }

    public EtmsOpAttemperCarEntity setTruckSpec(String truckSpec) {
        this.truckSpec = truckSpec;
        this.nodifySetFiled("truckSpec", truckSpec);
        return this;
    }

    public String getTruckSpec() {
        this.nodifyGetFiled("truckSpec");
        return truckSpec;
    }

    public EtmsOpAttemperCarEntity setSenderCode(String senderCode) {
        this.senderCode = senderCode;
        this.nodifySetFiled("senderCode", senderCode);
        return this;
    }

    public String getSenderCode() {
        this.nodifyGetFiled("senderCode");
        return senderCode;
    }

    public EtmsOpAttemperCarEntity setConsigneeCode(String consigneeCode) {
        this.consigneeCode = consigneeCode;
        this.nodifySetFiled("consigneeCode", consigneeCode);
        return this;
    }

    public String getConsigneeCode() {
        this.nodifyGetFiled("consigneeCode");
        return consigneeCode;
    }

    public EtmsOpAttemperCarEntity setStartRequestDate(Date startRequestDate) {
        this.startRequestDate = startRequestDate;
        this.nodifySetFiled("startRequestDate", startRequestDate);
        return this;
    }

    public Date getStartRequestDate() {
        this.nodifyGetFiled("startRequestDate");
        return startRequestDate;
    }

    public EtmsOpAttemperCarEntity setStartRequestDateStart(Date startRequestDateStart) {
        this.startRequestDateStart = startRequestDateStart;
        this.nodifySetFiled("startRequestDateStart", startRequestDateStart);
        return this;
    }

    public Date getStartRequestDateStart() {
        this.nodifyGetFiled("startRequestDateStart");
        return startRequestDateStart;
    }

    public EtmsOpAttemperCarEntity setStartRequestDateEnd(Date startRequestDateEnd) {
        this.startRequestDateEnd = startRequestDateEnd;
        this.nodifySetFiled("startRequestDateEnd", startRequestDateEnd);
        return this;
    }

    public Date getStartRequestDateEnd() {
        this.nodifyGetFiled("startRequestDateEnd");
        return startRequestDateEnd;
    }
    public EtmsOpAttemperCarEntity setEndRequestDate(Date endRequestDate) {
        this.endRequestDate = endRequestDate;
        this.nodifySetFiled("endRequestDate", endRequestDate);
        return this;
    }

    public Date getEndRequestDate() {
        this.nodifyGetFiled("endRequestDate");
        return endRequestDate;
    }

    public EtmsOpAttemperCarEntity setEndRequestDateStart(Date endRequestDateStart) {
        this.endRequestDateStart = endRequestDateStart;
        this.nodifySetFiled("endRequestDateStart", endRequestDateStart);
        return this;
    }

    public Date getEndRequestDateStart() {
        this.nodifyGetFiled("endRequestDateStart");
        return endRequestDateStart;
    }

    public EtmsOpAttemperCarEntity setEndRequestDateEnd(Date endRequestDateEnd) {
        this.endRequestDateEnd = endRequestDateEnd;
        this.nodifySetFiled("endRequestDateEnd", endRequestDateEnd);
        return this;
    }

    public Date getEndRequestDateEnd() {
        this.nodifyGetFiled("endRequestDateEnd");
        return endRequestDateEnd;
    }
    public EtmsOpAttemperCarEntity setStartOpArea(String startOpArea) {
        this.startOpArea = startOpArea;
        this.nodifySetFiled("startOpArea", startOpArea);
        return this;
    }

    public String getStartOpArea() {
        this.nodifyGetFiled("startOpArea");
        return startOpArea;
    }

    public EtmsOpAttemperCarEntity setEndOpArea(String endOpArea) {
        this.endOpArea = endOpArea;
        this.nodifySetFiled("endOpArea", endOpArea);
        return this;
    }

    public String getEndOpArea() {
        this.nodifyGetFiled("endOpArea");
        return endOpArea;
    }

    public EtmsOpAttemperCarEntity setCarLine(String carLine) {
        this.carLine = carLine;
        this.nodifySetFiled("carLine", carLine);
        return this;
    }

    public String getCarLine() {
        this.nodifyGetFiled("carLine");
        return carLine;
    }

    public EtmsOpAttemperCarEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public EtmsOpAttemperCarEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public EtmsOpAttemperCarEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public EtmsOpAttemperCarEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public EtmsOpAttemperCarEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public EtmsOpAttemperCarEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public EtmsOpAttemperCarEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public EtmsOpAttemperCarEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public EtmsOpAttemperCarEntity setAttributeCode(String attributeCode) {
        this.attributeCode = attributeCode;
        this.nodifySetFiled("attributeCode", attributeCode);
        return this;
    }

    public String getAttributeCode() {
        this.nodifyGetFiled("attributeCode");
        return attributeCode;
    }

    public EtmsOpAttemperCarEntity setPcType(String pcType) {
        this.pcType = pcType;
        this.nodifySetFiled("pcType", pcType);
        return this;
    }

    public String getPcType() {
        this.nodifyGetFiled("pcType");
        return pcType;
    }

    public EtmsOpAttemperCarEntity setPcNo(String pcNo) {
        this.pcNo = pcNo;
        this.nodifySetFiled("pcNo", pcNo);
        return this;
    }

    public String getPcNo() {
        this.nodifyGetFiled("pcNo");
        return pcNo;
    }

    public EtmsOpAttemperCarEntity setPcCarGuid(String pcCarGuid) {
        this.pcCarGuid = pcCarGuid;
        this.nodifySetFiled("pcCarGuid", pcCarGuid);
        return this;
    }

    public String getPcCarGuid() {
        this.nodifyGetFiled("pcCarGuid");
        return pcCarGuid;
    }

}
