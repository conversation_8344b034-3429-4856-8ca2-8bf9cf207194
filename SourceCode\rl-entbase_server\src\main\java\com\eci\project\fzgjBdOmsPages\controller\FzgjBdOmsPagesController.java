package com.eci.project.fzgjBdOmsPages.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.project.fzgjBdOmsPages.service.IFzgjBdOmsPagesService;
import com.eci.project.fzgjBdOmsPages.entity.FzgjBdOmsPagesEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.EciLog;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


/**
* 录入订单编辑页面Controller
*
* @<NAME_EMAIL>
* @date 2025-03-19
*/
@Api(tags = "录入订单编辑页面")
@RestController
@RequestMapping("/fzgjBdOmsPages")
public class FzgjBdOmsPagesController extends EciBaseController {

    @Autowired
    private IFzgjBdOmsPagesService fzgjBdOmsPagesService;

    /**
    * 根据实体插入数据库
    * @param entity
    * @return
    */
    @ApiOperation("录入订单编辑页面:保存")
    @EciLog(title = "录入订单编辑页面:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public ResponseMsg save(@RequestBody FzgjBdOmsPagesEntity entity){
        return ResponseMsgUtil.success(10001,fzgjBdOmsPagesService.save(entity));
    }

    /**
    * 根据条件查询一个集合
    * @param entity
    * @return
    */
    @ApiOperation("录入订单编辑页面:查询列表")
    @EciLog(title = "录入订单编辑页面:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    public ResponseMsg selectList(@RequestBody FzgjBdOmsPagesEntity entity){
        return ResponseMsgUtil.success(10001,fzgjBdOmsPagesService.selectList(entity));
    }

    /**
    * 分页查询
    * @param entity
    * @return
    */
    @ApiOperation("录入订单编辑页面:分页查询列表")
    @EciLog(title = "录入订单编辑页面:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    public ResponseMsg selectPageList(@RequestBody FzgjBdOmsPagesEntity entity){
        return ResponseMsgUtil.success(10001,fzgjBdOmsPagesService.queryPageList(entity));
    }

    /**
    * 根据ID查一条记录
    * @param entity
    * @return
    */
    @ApiOperation("录入订单编辑页面:根据ID查一条")
    @EciLog(title = "录入订单编辑页面:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    public ResponseMsg selectOneById(@RequestBody FzgjBdOmsPagesEntity entity){
        return ResponseMsgUtil.success(10001,fzgjBdOmsPagesService.selectOneById(entity.getGuid()));
    }


    /**
    * 根据ID删除
    * @param entity
    * @return
    */
    @ApiOperation("录入订单编辑页面:根据ID删除一条")
    @EciLog(title = "录入订单编辑页面:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    public ResponseMsg deleteById(@RequestBody FzgjBdOmsPagesEntity entity){
        return ResponseMsgUtil.success(10001,fzgjBdOmsPagesService.deleteById(entity.getGuid()));
    }

    /**
    * 根据IDS删除
    *
    * @param entity
    * @return
    */
    @ApiOperation("录入订单编辑页面:根据ID字符串删除多条")
    @EciLog(title = "录入订单编辑页面:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    public ResponseMsg deleteByIds(@RequestBody FzgjBdOmsPagesEntity entity) {
        return ResponseMsgUtil.success(10001, fzgjBdOmsPagesService.deleteByIds(entity.getIds()));
    }


}