package com.eci.project.crmCustomerPosition.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 合作伙伴职务对象 CRM_CUSTOMER_POSITION
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-05-26
*/
@ApiModel("合作伙伴职务")
@TableName("CRM_CUSTOMER_POSITION")
@FieldNameConstants
public class CrmCustomerPositionEntity extends EciBaseEntity{
    /**
    * GUID
    */
    @ApiModelProperty("GUID(50)")
    @TableId("GUID")
    private String guid;

    /**
    * GUID
    */
    @ApiModelProperty("GUID(50)")
    @TableField("CUSTOMER_GUID")
    private String customerGuid;

    @ApiModelProperty("(30)")
    @TableField("LEGAL_PERSON")
    private String legalPerson;

    /**
    * 身份证号
    */
    @ApiModelProperty("身份证号(20)")
    @TableField("CARD_NO")
    private String cardNo;

    /**
    * 职位
    */
    @ApiModelProperty("职位(40)")
    @TableField("POSITION")
    private String position;

    /**
    * 任职开始日期
    */
    @ApiModelProperty("任职开始日期(7)")
    @TableField("START_DATE")
    private Date startDate;

    @ApiModelProperty("任职开始日期开始")
    @TableField(exist=false)
    private Date startDateStart;

    @ApiModelProperty("任职开始日期结束")
    @TableField(exist=false)
    private Date startDateEnd;

    /**
    * 任职结束日期
    */
    @ApiModelProperty("任职结束日期(7)")
    @TableField("END_DATE")
    private Date endDate;

    @ApiModelProperty("任职结束日期开始")
    @TableField(exist=false)
    private Date endDateStart;

    @ApiModelProperty("任职结束日期结束")
    @TableField(exist=false)
    private Date endDateEnd;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 备注
    */
    @ApiModelProperty("备注(20)")
    @TableField("MEMO")
    private String memo;

    @ApiModelProperty("(20)")
    @TableField("CREATE_USER")
    private String createUser;

    @ApiModelProperty("(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    @ApiModelProperty("(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("结束")
    @TableField(exist=false)
    private Date createDateEnd;

    @ApiModelProperty("(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public CrmCustomerPositionEntity() {
        this.setSubClazz(CrmCustomerPositionEntity.class);
    }

    public CrmCustomerPositionEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public CrmCustomerPositionEntity setCustomerGuid(String customerGuid) {
        this.customerGuid = customerGuid;
        this.nodifySetFiled("customerGuid", customerGuid);
        return this;
    }

    public String getCustomerGuid() {
        this.nodifyGetFiled("customerGuid");
        return customerGuid;
    }

    public CrmCustomerPositionEntity setLegalPerson(String legalPerson) {
        this.legalPerson = legalPerson;
        this.nodifySetFiled("legalPerson", legalPerson);
        return this;
    }

    public String getLegalPerson() {
        this.nodifyGetFiled("legalPerson");
        return legalPerson;
    }

    public CrmCustomerPositionEntity setCardNo(String cardNo) {
        this.cardNo = cardNo;
        this.nodifySetFiled("cardNo", cardNo);
        return this;
    }

    public String getCardNo() {
        this.nodifyGetFiled("cardNo");
        return cardNo;
    }

    public CrmCustomerPositionEntity setPosition(String position) {
        this.position = position;
        this.nodifySetFiled("position", position);
        return this;
    }

    public String getPosition() {
        this.nodifyGetFiled("position");
        return position;
    }

    public CrmCustomerPositionEntity setStartDate(Date startDate) {
        this.startDate = startDate;
        this.nodifySetFiled("startDate", startDate);
        return this;
    }

    public Date getStartDate() {
        this.nodifyGetFiled("startDate");
        return startDate;
    }

    public CrmCustomerPositionEntity setStartDateStart(Date startDateStart) {
        this.startDateStart = startDateStart;
        this.nodifySetFiled("startDateStart", startDateStart);
        return this;
    }

    public Date getStartDateStart() {
        this.nodifyGetFiled("startDateStart");
        return startDateStart;
    }

    public CrmCustomerPositionEntity setStartDateEnd(Date startDateEnd) {
        this.startDateEnd = startDateEnd;
        this.nodifySetFiled("startDateEnd", startDateEnd);
        return this;
    }

    public Date getStartDateEnd() {
        this.nodifyGetFiled("startDateEnd");
        return startDateEnd;
    }
    public CrmCustomerPositionEntity setEndDate(Date endDate) {
        this.endDate = endDate;
        this.nodifySetFiled("endDate", endDate);
        return this;
    }

    public Date getEndDate() {
        this.nodifyGetFiled("endDate");
        return endDate;
    }

    public CrmCustomerPositionEntity setEndDateStart(Date endDateStart) {
        this.endDateStart = endDateStart;
        this.nodifySetFiled("endDateStart", endDateStart);
        return this;
    }

    public Date getEndDateStart() {
        this.nodifyGetFiled("endDateStart");
        return endDateStart;
    }

    public CrmCustomerPositionEntity setEndDateEnd(Date endDateEnd) {
        this.endDateEnd = endDateEnd;
        this.nodifySetFiled("endDateEnd", endDateEnd);
        return this;
    }

    public Date getEndDateEnd() {
        this.nodifyGetFiled("endDateEnd");
        return endDateEnd;
    }
    public CrmCustomerPositionEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public CrmCustomerPositionEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public CrmCustomerPositionEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public CrmCustomerPositionEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public CrmCustomerPositionEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public CrmCustomerPositionEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public CrmCustomerPositionEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public CrmCustomerPositionEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public CrmCustomerPositionEntity setMemo(String memo) {
        this.memo = memo;
        this.nodifySetFiled("memo", memo);
        return this;
    }

    public String getMemo() {
        this.nodifyGetFiled("memo");
        return memo;
    }

    public CrmCustomerPositionEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public CrmCustomerPositionEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public CrmCustomerPositionEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public CrmCustomerPositionEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public CrmCustomerPositionEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public CrmCustomerPositionEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public CrmCustomerPositionEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public CrmCustomerPositionEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
}
