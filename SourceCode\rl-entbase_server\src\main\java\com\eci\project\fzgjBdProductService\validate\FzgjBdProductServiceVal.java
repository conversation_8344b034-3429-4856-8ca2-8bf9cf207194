package com.eci.project.fzgjBdProductService.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjBdProductService.entity.FzgjBdProductServiceEntity;

import org.springframework.stereotype.Service;


/**
* 产品服务类型Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-01
*/
@Service
public class FzgjBdProductServiceVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjBdProductServiceEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjBdProductServiceEntity entity, BusinessType businessType) {

    }

}
