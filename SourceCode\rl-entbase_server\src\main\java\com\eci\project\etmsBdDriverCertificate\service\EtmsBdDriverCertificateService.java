package com.eci.project.etmsBdDriverCertificate.service;

import com.eci.common.db.DBHelper;
import com.eci.common.util.DateUtils;
import com.eci.common.util.StringUtils;
import com.eci.common.utils.ExportUtils.ExcelProcess;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.ActionType;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.etmsBdDriver.entity.EtmsBdDriverEntity;
import com.eci.project.etmsBdDriverCertificate.dao.EtmsBdDriverCertificateDao;
import com.eci.project.etmsBdDriverCertificate.entity.EtmsBdDriverCertificateEntity;
import com.eci.project.etmsBdDriverCertificate.entity.EtmsBdDriverCertificateSearchEntity;
import com.eci.project.etmsBdDriverCertificate.validate.EtmsBdDriverCertificateVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
* 司机证件管理Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-28
*/
@Service
@Slf4j
public class EtmsBdDriverCertificateService implements EciBaseService<EtmsBdDriverCertificateEntity> {

    @Autowired
    private EtmsBdDriverCertificateDao etmsBdDriverCertificateDao;

    @Autowired
    private EtmsBdDriverCertificateVal etmsBdDriverCertificateVal;


    @Override
    public TgPageInfo queryPageList(EtmsBdDriverCertificateEntity entity) {
        EciQuery<EtmsBdDriverCertificateEntity> eciQuery = EciQuery.buildQuery(entity);
        eciQuery.select("(case WHEN TRUNC(SYSDATE)>END_DATE THEN '失效' else '有效' end) statusName",
                "A.*");
        List<EtmsBdDriverCertificateEntity> entities = etmsBdDriverCertificateDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }

    public TgPageInfo queryCertificatePageList(EtmsBdDriverCertificateSearchEntity entity) {
        StringBuilder stringBuilder=new StringBuilder();
        stringBuilder.append("SELECT  A.NAME,A.PHONE,A.IS_GK,B.CERTIFICATE_NO,B.CERTIFICATE_TYPE,B.DRIVER_LICENSE_TYPE,B.DRIVERING_YEARS,(SELECT X.MAPPING_ABBREVIATION FROM ETMS_CRM_PARTNER X WHERE X.GUID=A.PARTNER_GUID AND X.GROUP_CODE=A.GROUP_CODE) PARTNER_NAME, B.START_DATE,B.END_DATE,B.CREATE_DATE,B.COMPANY_NAME\n" +
                "FROM ETMS_BD_DRIVER A INNER JOIN ETMS_BD_DRIVER_CERTIFICATE B ON A.GUID=B.DRIVER_GUID where 1=1 \n");
        if(StringUtils.hasValue(entity.getName())){
            stringBuilder.append(" AND A.NAME like '%"+entity.getName()+"%'");
        }
        if(StringUtils.hasValue(entity.getPhone())){
            stringBuilder.append(" AND A.PHONE like '%"+entity.getPhone()+"%'");
        }
        if(StringUtils.hasValue(entity.getCertificateNo())){
            stringBuilder.append(" AND B.CERTIFICATE_NO like '%"+entity.getCertificateNo()+"%'");
        }
        if(StringUtils.hasValue(entity.getDriverLicenseType())){
            stringBuilder.append(" AND B.DRIVER_LICENSE_TYPE like '%"+entity.getDriverLicenseType()+"%'");
        }
        if(StringUtils.hasValue(entity.getPartnerName())){
            stringBuilder.append(" AND (SELECT X.MAPPING_ABBREVIATION FROM ETMS_CRM_PARTNER X WHERE X.GUID=A.PARTNER_GUID AND X.GROUP_CODE=A.GROUP_CODE) = '"+entity.getPartnerName()+"'");
        }
        if(StringUtils.hasValue(entity.getCompanyName())){
            stringBuilder.append(" AND B.COMPANY_NAME like '%"+entity.getCompanyName()+"%'");
        }
        if (entity.getStartDateStart()!=null) {
            stringBuilder.append(" AND B.START_DATE >= TO_DATE('" + DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss",entity.getStartDateStart()) + "', 'yyyy-mm-dd hh24:mi:ss')");
        }
        if (entity.getStartDateEnd()!=null) {
            stringBuilder.append(" AND B.START_DATE <= TO_DATE('" + DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss",entity.getStartDateEnd()) + "', 'yyyy-mm-dd hh24:mi:ss')");
        }
        if (entity.getEndDateStart()!=null) {
            stringBuilder.append(" AND B.END_DATE >= TO_DATE('" + DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss",entity.getEndDateStart()) + "', 'yyyy-mm-dd hh24:mi:ss')");
        }
        if (entity.getEndDateEnd()!=null) {
            stringBuilder.append(" AND B.END_DATE <= TO_DATE('" + DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss",entity.getEndDateEnd()) + "', 'yyyy-mm-dd hh24:mi:ss')");
        }
        if (entity.getCreateDateStart()!=null) {
            stringBuilder.append(" AND B.CREATE_DATE >= TO_DATE('" + DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss",entity.getCreateDateStart()) + "', 'yyyy-mm-dd hh24:mi:ss')");
        }
        if (entity.getCreateDateEnd()!=null) {
            stringBuilder.append(" AND B.CREATE_DATE <= TO_DATE('" + DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss",entity.getCreateDateEnd()) + "', 'yyyy-mm-dd hh24:mi:ss')");
        }
        if (BllContext.getBusinessType() == BusinessType.EXPORT) {
            if (BllContext.getRequestEntity().getActionType() == ActionType.ASYNC) {
                etmsBdDriverCertificateDao.asyncExportDefaultExcel(()->{
                    List<EtmsBdDriverCertificateSearchEntity> entities = DBHelper.selectList(stringBuilder.toString(), EtmsBdDriverCertificateSearchEntity.class);
                    entities.forEach(qzSearchEntity -> {
                        if (StringUtils.hasValue(qzSearchEntity.getEndDate()) && DateUtils.parseDate(qzSearchEntity.getEndDate().substring(0,19)).compareTo(DateUtils.parseDate(DateUtils.getDate())) >= 0) {
                            qzSearchEntity.setStatus("有效");
                        } else {
                            qzSearchEntity.setStatus("无效");
                        }
                        if (qzSearchEntity.getIsGk().equals("Y")) {
                            qzSearchEntity.setIsGk("是");
                        } else {
                            qzSearchEntity.setIsGk("否");
                        }
                        qzSearchEntity.setStartDate(qzSearchEntity.getStartDate().substring(0, 10));
                        qzSearchEntity.setEndDate(qzSearchEntity.getEndDate().substring(0, 10));
                        qzSearchEntity.setModMark("否");
                    });
                    return entities;
                }, ExcelProcess.BuildConfig("司机证件管理", EtmsBdDriverCertificateSearchEntity.class));
            } else {
                etmsBdDriverCertificateDao.exportDefaultExcel(() -> {
                    List<EtmsBdDriverCertificateSearchEntity> entities = DBHelper.selectList(stringBuilder.toString(), EtmsBdDriverCertificateSearchEntity.class);
                    entities.forEach(qzSearchEntity -> {
                        if (StringUtils.hasValue(qzSearchEntity.getEndDate()) && DateUtils.parseDate(qzSearchEntity.getEndDate().substring(0,19)).compareTo(DateUtils.parseDate(DateUtils.getDate())) >= 0) {
                            qzSearchEntity.setStatus("有效");
                        } else {
                            qzSearchEntity.setStatus("无效");
                        }
                        if (qzSearchEntity.getIsGk().equals("Y")) {
                            qzSearchEntity.setIsGk("是");
                        } else {
                            qzSearchEntity.setIsGk("否");
                        }
                        qzSearchEntity.setStartDate(qzSearchEntity.getStartDate().substring(0, 10));
                        qzSearchEntity.setEndDate(qzSearchEntity.getEndDate().substring(0, 10));
                        qzSearchEntity.setModMark("否");
                    });
                    return entities;
                }, ExcelProcess.BuildConfig("司机证件管理", EtmsBdDriverCertificateSearchEntity.class));
            }
            return new TgPageInfo<>();
        }else{
            startPage();
            List<EtmsBdDriverCertificateSearchEntity> entities = DBHelper.selectList(stringBuilder.toString(), EtmsBdDriverCertificateSearchEntity.class);
            entities.forEach(qzSearchEntity -> {
                Date now=DateUtils.parseDate(DateUtils.getDate());
                if (StringUtils.hasValue(qzSearchEntity.getEndDate()) && DateUtils.parseDate(qzSearchEntity.getEndDate().substring(0, 19)).compareTo(now) >= 0) {
                    qzSearchEntity.setStatus("有效");
                } else {
                    qzSearchEntity.setStatus("无效");
                }
                if(qzSearchEntity.getIsGk().equals("Y")){
                    qzSearchEntity.setIsGk("是");
                }else{
                    qzSearchEntity.setIsGk("否");
                }
                qzSearchEntity.setModMark("否");
            });
            return EciQuery.getPageInfo(entities);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public EtmsBdDriverCertificateEntity save(EtmsBdDriverCertificateEntity entity) {
        // 返回实体对象
        EtmsBdDriverCertificateEntity etmsBdDriverCertificateEntity = null;
        etmsBdDriverCertificateVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            etmsBdDriverCertificateEntity = etmsBdDriverCertificateDao.insertOne(entity);

        }else{

            etmsBdDriverCertificateEntity = etmsBdDriverCertificateDao.updateByEntityId(entity);

        }
        return etmsBdDriverCertificateEntity;
    }

    @Override
    public List<EtmsBdDriverCertificateEntity> selectList(EtmsBdDriverCertificateEntity entity) {
        return etmsBdDriverCertificateDao.selectList(entity);
    }

    @Override
    public EtmsBdDriverCertificateEntity selectOneById(Serializable id) {
        EciQuery<EtmsBdDriverCertificateEntity> eciQuery = EciQuery.buildQuery(new EtmsBdDriverCertificateEntity());
        eciQuery.select("(case WHEN TRUNC(SYSDATE)>END_DATE THEN '失效' else '有效' end) statusName",
                "A.*");

        eciQuery.eq("A.guid",id.toString());
        EtmsBdDriverCertificateEntity result=  etmsBdDriverCertificateDao.selectOne(eciQuery);
        return result;
        //return etmsBdDriverCertificateDao.selectById(id);
    }


    @Override
    public void insertBatch(List<EtmsBdDriverCertificateEntity> list) {
        etmsBdDriverCertificateDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return etmsBdDriverCertificateDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return etmsBdDriverCertificateDao.deleteById(id);
    }

}