package com.eci.project.fzgjBdOpType.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjBdOpType.service.FzgjBdOpTypeService;
import com.eci.project.fzgjBdOpType.entity.FzgjBdOpTypeEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 业务类型Controller
*
* @<NAME_EMAIL>
* @date 2025-03-27
*/
@Api(tags = "业务类型")
@RestController
@RequestMapping("/fzgjBdOpType")
public class FzgjBdOpTypeController extends EciBaseController {

    @Autowired
    private FzgjBdOpTypeService fzgjBdOpTypeService;


    @ApiOperation("业务类型:保存")
    @EciLog(title = "业务类型:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody FzgjBdOpTypeEntity entity){
        FzgjBdOpTypeEntity fzgjBdOpTypeEntity =fzgjBdOpTypeService.save(entity);
        return ResponseMsgUtil.success(10001,fzgjBdOpTypeEntity);
    }


    @ApiOperation("业务类型:查询列表")
    @EciLog(title = "业务类型:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjBdOpTypeEntity entity){
        List<FzgjBdOpTypeEntity> fzgjBdOpTypeEntities = fzgjBdOpTypeService.selectList(entity);
        return ResponseMsgUtil.success(10001,fzgjBdOpTypeEntities);
    }


    @ApiOperation("业务类型:分页查询列表")
    @EciLog(title = "业务类型:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjBdOpTypeEntity entity){
        TgPageInfo tgPageInfo = fzgjBdOpTypeService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("业务类型:根据ID查一条")
    @EciLog(title = "业务类型:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjBdOpTypeEntity entity){
        FzgjBdOpTypeEntity  fzgjBdOpTypeEntity = fzgjBdOpTypeService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,fzgjBdOpTypeEntity);
    }


    @ApiOperation("业务类型:根据ID删除一条")
    @EciLog(title = "业务类型:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjBdOpTypeEntity entity){
        int count = fzgjBdOpTypeService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("业务类型:根据ID字符串删除多条")
    @EciLog(title = "业务类型:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjBdOpTypeEntity entity) {
        int count = fzgjBdOpTypeService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}