package com.eci.project.fzgjTaskLimitationTime.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.math.BigDecimal;
import java.util.Date;


/**
* 作业环节基准时效对象 FZGJ_TASK_LIMITATION_TIME
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-05-22
*/
@ApiModel("作业环节基准时效")
@TableName("FZGJ_TASK_LIMITATION_TIME")
@FieldNameConstants
public class FzgjTaskLimitationTimeEntity extends EciBaseEntity{
    /**
    * GUID
    */
    @ApiModelProperty("GUID(36)")
    @TableId("GUID")
    private String guid;

    /**
    * LIMITATION.GUID
    */
    @ApiModelProperty("LIMITATION.GUID(50)")
    @TableField("LIMITATION_GUID")
    private String limitationGuid;

    /**
    * 时效基准参数编码
    */
    @ApiModelProperty("时效基准参数编码(200)")
    @TableField("VALID_TIME_CODE")
    private String validTimeCode;

    /**
    * 达标时效
    */
    @ApiModelProperty("达标时效(22)")
    @TableField("REACH_STANDDARD")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal reachStanddard;

    /**
    * 时效单位
    */
    @ApiModelProperty("时效单位(200)")
    @TableField("TIME_UNIT")
    private String timeUnit;

    /**
    * 条件(冗余)
    */
    @ApiModelProperty("条件(冗余)(4,000)")
    @TableField("FIXED")
    private String fixed;

    /**
    * 条件代码(冗余)
    */
    @ApiModelProperty("条件代码(冗余)(4,000)")
    @TableField("FIXED_CODE")
    private String fixedCode;

    /**
    * 顺序
    */
    @ApiModelProperty("顺序(22)")
    @TableField("SEQ")
    private Integer seq;

    /**
    * 备注
    */
    @ApiModelProperty("备注(200)")
    @TableField("MEMO")
    private String memo;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 创建人名称
    */
    @ApiModelProperty("创建人名称(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 创建时间
    */
    @ApiModelProperty("创建时间(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建时间开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建时间结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 修改人
    */
    @ApiModelProperty("修改人(50)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 修改人名称
    */
    @ApiModelProperty("修改人名称(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 修改时间
    */
    @ApiModelProperty("修改时间(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("修改时间开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("修改时间结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 组织代码
    */
    @ApiModelProperty("组织代码(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 组织名称
    */
    @ApiModelProperty("组织名称(200)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 公司代码
    */
    @ApiModelProperty("公司代码(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 公司名称
    */
    @ApiModelProperty("公司名称(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 集团代码
    */
    @ApiModelProperty("集团代码(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 集团名称
    */
    @ApiModelProperty("集团名称(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public FzgjTaskLimitationTimeEntity() {
        this.setSubClazz(FzgjTaskLimitationTimeEntity.class);
    }

    public FzgjTaskLimitationTimeEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public FzgjTaskLimitationTimeEntity setLimitationGuid(String limitationGuid) {
        this.limitationGuid = limitationGuid;
        this.nodifySetFiled("limitationGuid", limitationGuid);
        return this;
    }

    public String getLimitationGuid() {
        this.nodifyGetFiled("limitationGuid");
        return limitationGuid;
    }

    public FzgjTaskLimitationTimeEntity setValidTimeCode(String validTimeCode) {
        this.validTimeCode = validTimeCode;
        this.nodifySetFiled("validTimeCode", validTimeCode);
        return this;
    }

    public String getValidTimeCode() {
        this.nodifyGetFiled("validTimeCode");
        return validTimeCode;
    }

    public FzgjTaskLimitationTimeEntity setReachStanddard(BigDecimal reachStanddard) {
        this.reachStanddard = reachStanddard;
        this.nodifySetFiled("reachStanddard", reachStanddard);
        return this;
    }

    public BigDecimal getReachStanddard() {
        this.nodifyGetFiled("reachStanddard");
        return reachStanddard;
    }

    public FzgjTaskLimitationTimeEntity setTimeUnit(String timeUnit) {
        this.timeUnit = timeUnit;
        this.nodifySetFiled("timeUnit", timeUnit);
        return this;
    }

    public String getTimeUnit() {
        this.nodifyGetFiled("timeUnit");
        return timeUnit;
    }

    public FzgjTaskLimitationTimeEntity setFixed(String fixed) {
        this.fixed = fixed;
        this.nodifySetFiled("fixed", fixed);
        return this;
    }

    public String getFixed() {
        this.nodifyGetFiled("fixed");
        return fixed;
    }

    public FzgjTaskLimitationTimeEntity setFixedCode(String fixedCode) {
        this.fixedCode = fixedCode;
        this.nodifySetFiled("fixedCode", fixedCode);
        return this;
    }

    public String getFixedCode() {
        this.nodifyGetFiled("fixedCode");
        return fixedCode;
    }

    public FzgjTaskLimitationTimeEntity setSeq(Integer seq) {
        this.seq = seq;
        this.nodifySetFiled("seq", seq);
        return this;
    }

    public Integer getSeq() {
        this.nodifyGetFiled("seq");
        return seq;
    }

    public FzgjTaskLimitationTimeEntity setMemo(String memo) {
        this.memo = memo;
        this.nodifySetFiled("memo", memo);
        return this;
    }

    public String getMemo() {
        this.nodifyGetFiled("memo");
        return memo;
    }

    public FzgjTaskLimitationTimeEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public FzgjTaskLimitationTimeEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public FzgjTaskLimitationTimeEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public FzgjTaskLimitationTimeEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public FzgjTaskLimitationTimeEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public FzgjTaskLimitationTimeEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public FzgjTaskLimitationTimeEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public FzgjTaskLimitationTimeEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public FzgjTaskLimitationTimeEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public FzgjTaskLimitationTimeEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public FzgjTaskLimitationTimeEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public FzgjTaskLimitationTimeEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public FzgjTaskLimitationTimeEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public FzgjTaskLimitationTimeEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public FzgjTaskLimitationTimeEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public FzgjTaskLimitationTimeEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

}
