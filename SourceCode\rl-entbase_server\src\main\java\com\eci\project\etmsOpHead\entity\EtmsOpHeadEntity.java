package com.eci.project.etmsOpHead.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 平台业务主表对象 ETMS_OP_HEAD
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-06-13
*/
@ApiModel("平台业务主表")
@TableName("ETMS_OP_HEAD")
@FieldNameConstants
public class EtmsOpHeadEntity extends EciBaseEntity{
    /**
    * 主键
    */
    @ApiModelProperty("主键(50)")
    @TableId("GUID")
    private String guid;

    /**
    * 业务编号
    */
    @ApiModelProperty("业务编号(20)")
    @TableField("OP_NO")
    private String opNo;

    /**
    * 货主
    */
    @ApiModelProperty("货主(1,000)")
    @TableField("CUSTOMER_CODE")
    private String customerCode;

    /**
    * 收货方
    */
    @ApiModelProperty("收货方(1,000)")
    @TableField("CONSIGNEE_CODE")
    private String consigneeCode;

    /**
    * 发票号
    */
    @ApiModelProperty("发票号(1,000)")
    @TableField("INV_NO")
    private String invNo;

    /**
    * 主单号
    */
    @ApiModelProperty("主单号(1,000)")
    @TableField("MBL")
    private String mbl;

    /**
    * 分单号
    */
    @ApiModelProperty("分单号(1,000)")
    @TableField("HBL")
    private String hbl;

    /**
    * 转关单号
    */
    @ApiModelProperty("转关单号(1,000)")
    @TableField("ZG_NO")
    private String zgNo;

    /**
    * 核放单号
    */
    @ApiModelProperty("核放单号(1,000)")
    @TableField("HF_NO")
    private String hfNo;

    /**
    * 创建企业
    */
    @ApiModelProperty("创建企业(20)")
    @TableField("CREATE_COMPANY")
    private String createCompany;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 编辑人
    */
    @ApiModelProperty("编辑人(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 编辑日期
    */
    @ApiModelProperty("编辑日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("编辑日期开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("编辑日期结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 状态
    */
    @ApiModelProperty("状态(10)")
    @TableField("STATUS")
    private String status;

    /**
    * 调度人员
    */
    @ApiModelProperty("调度人员(20)")
    @TableField("MANAGE_USER")
    private String manageUser;

    /**
    * 所属部门ID
    */
    @ApiModelProperty("所属部门ID(50)")
    @TableField("ORG_DEP_ID")
    private String orgDepId;

    /**
    * 所属部门CODE
    */
    @ApiModelProperty("所属部门CODE(50)")
    @TableField("ORG_DEP_CODE")
    private String orgDepCode;

    /**
    * 所属部门名称
    */
    @ApiModelProperty("所属部门名称(50)")
    @TableField("ORG_DEP_NAME")
    private String orgDepName;

    /**
    * 报关单号 
    */
    @ApiModelProperty("报关单号 (200)")
    @TableField("BGDH")
    private String bgdh;

    /**
    * 外贸启动港 
    */
    @ApiModelProperty("外贸启动港 (200)")
    @TableField("WM_QDG")
    private String wmQdg;

    /**
    * 外贸目的港 
    */
    @ApiModelProperty("外贸目的港 (200)")
    @TableField("WM_MDG")
    private String wmMdg;

    /**
    * 船名/航次
    */
    @ApiModelProperty("船名/航次(200)")
    @TableField("SHIP_NAME")
    private String shipName;

    /**
    * 物流线路 
    */
    @ApiModelProperty("物流线路 (200)")
    @TableField("WL_LINE")
    private String wlLine;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 发送批次号
    */
    @ApiModelProperty("发送批次号(20)")
    @TableField("BATCH_NUMBER")
    private String batchNumber;

    /**
    * OMS订单号
    */
    @ApiModelProperty("OMS订单号(20)")
    @TableField("OMS_ORDER_NO")
    private String omsOrderNo;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public EtmsOpHeadEntity() {
        this.setSubClazz(EtmsOpHeadEntity.class);
    }

    public EtmsOpHeadEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public EtmsOpHeadEntity setOpNo(String opNo) {
        this.opNo = opNo;
        this.nodifySetFiled("opNo", opNo);
        return this;
    }

    public String getOpNo() {
        this.nodifyGetFiled("opNo");
        return opNo;
    }

    public EtmsOpHeadEntity setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
        this.nodifySetFiled("customerCode", customerCode);
        return this;
    }

    public String getCustomerCode() {
        this.nodifyGetFiled("customerCode");
        return customerCode;
    }

    public EtmsOpHeadEntity setConsigneeCode(String consigneeCode) {
        this.consigneeCode = consigneeCode;
        this.nodifySetFiled("consigneeCode", consigneeCode);
        return this;
    }

    public String getConsigneeCode() {
        this.nodifyGetFiled("consigneeCode");
        return consigneeCode;
    }

    public EtmsOpHeadEntity setInvNo(String invNo) {
        this.invNo = invNo;
        this.nodifySetFiled("invNo", invNo);
        return this;
    }

    public String getInvNo() {
        this.nodifyGetFiled("invNo");
        return invNo;
    }

    public EtmsOpHeadEntity setMbl(String mbl) {
        this.mbl = mbl;
        this.nodifySetFiled("mbl", mbl);
        return this;
    }

    public String getMbl() {
        this.nodifyGetFiled("mbl");
        return mbl;
    }

    public EtmsOpHeadEntity setHbl(String hbl) {
        this.hbl = hbl;
        this.nodifySetFiled("hbl", hbl);
        return this;
    }

    public String getHbl() {
        this.nodifyGetFiled("hbl");
        return hbl;
    }

    public EtmsOpHeadEntity setZgNo(String zgNo) {
        this.zgNo = zgNo;
        this.nodifySetFiled("zgNo", zgNo);
        return this;
    }

    public String getZgNo() {
        this.nodifyGetFiled("zgNo");
        return zgNo;
    }

    public EtmsOpHeadEntity setHfNo(String hfNo) {
        this.hfNo = hfNo;
        this.nodifySetFiled("hfNo", hfNo);
        return this;
    }

    public String getHfNo() {
        this.nodifyGetFiled("hfNo");
        return hfNo;
    }

    public EtmsOpHeadEntity setCreateCompany(String createCompany) {
        this.createCompany = createCompany;
        this.nodifySetFiled("createCompany", createCompany);
        return this;
    }

    public String getCreateCompany() {
        this.nodifyGetFiled("createCompany");
        return createCompany;
    }

    public EtmsOpHeadEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public EtmsOpHeadEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public EtmsOpHeadEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public EtmsOpHeadEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public EtmsOpHeadEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public EtmsOpHeadEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public EtmsOpHeadEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public EtmsOpHeadEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public EtmsOpHeadEntity setStatus(String status) {
        this.status = status;
        this.nodifySetFiled("status", status);
        return this;
    }

    public String getStatus() {
        this.nodifyGetFiled("status");
        return status;
    }

    public EtmsOpHeadEntity setManageUser(String manageUser) {
        this.manageUser = manageUser;
        this.nodifySetFiled("manageUser", manageUser);
        return this;
    }

    public String getManageUser() {
        this.nodifyGetFiled("manageUser");
        return manageUser;
    }

    public EtmsOpHeadEntity setOrgDepId(String orgDepId) {
        this.orgDepId = orgDepId;
        this.nodifySetFiled("orgDepId", orgDepId);
        return this;
    }

    public String getOrgDepId() {
        this.nodifyGetFiled("orgDepId");
        return orgDepId;
    }

    public EtmsOpHeadEntity setOrgDepCode(String orgDepCode) {
        this.orgDepCode = orgDepCode;
        this.nodifySetFiled("orgDepCode", orgDepCode);
        return this;
    }

    public String getOrgDepCode() {
        this.nodifyGetFiled("orgDepCode");
        return orgDepCode;
    }

    public EtmsOpHeadEntity setOrgDepName(String orgDepName) {
        this.orgDepName = orgDepName;
        this.nodifySetFiled("orgDepName", orgDepName);
        return this;
    }

    public String getOrgDepName() {
        this.nodifyGetFiled("orgDepName");
        return orgDepName;
    }

    public EtmsOpHeadEntity setBgdh(String bgdh) {
        this.bgdh = bgdh;
        this.nodifySetFiled("bgdh", bgdh);
        return this;
    }

    public String getBgdh() {
        this.nodifyGetFiled("bgdh");
        return bgdh;
    }

    public EtmsOpHeadEntity setWmQdg(String wmQdg) {
        this.wmQdg = wmQdg;
        this.nodifySetFiled("wmQdg", wmQdg);
        return this;
    }

    public String getWmQdg() {
        this.nodifyGetFiled("wmQdg");
        return wmQdg;
    }

    public EtmsOpHeadEntity setWmMdg(String wmMdg) {
        this.wmMdg = wmMdg;
        this.nodifySetFiled("wmMdg", wmMdg);
        return this;
    }

    public String getWmMdg() {
        this.nodifyGetFiled("wmMdg");
        return wmMdg;
    }

    public EtmsOpHeadEntity setShipName(String shipName) {
        this.shipName = shipName;
        this.nodifySetFiled("shipName", shipName);
        return this;
    }

    public String getShipName() {
        this.nodifyGetFiled("shipName");
        return shipName;
    }

    public EtmsOpHeadEntity setWlLine(String wlLine) {
        this.wlLine = wlLine;
        this.nodifySetFiled("wlLine", wlLine);
        return this;
    }

    public String getWlLine() {
        this.nodifyGetFiled("wlLine");
        return wlLine;
    }

    public EtmsOpHeadEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public EtmsOpHeadEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public EtmsOpHeadEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public EtmsOpHeadEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public EtmsOpHeadEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public EtmsOpHeadEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public EtmsOpHeadEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public EtmsOpHeadEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public EtmsOpHeadEntity setBatchNumber(String batchNumber) {
        this.batchNumber = batchNumber;
        this.nodifySetFiled("batchNumber", batchNumber);
        return this;
    }

    public String getBatchNumber() {
        this.nodifyGetFiled("batchNumber");
        return batchNumber;
    }

    public EtmsOpHeadEntity setOmsOrderNo(String omsOrderNo) {
        this.omsOrderNo = omsOrderNo;
        this.nodifySetFiled("omsOrderNo", omsOrderNo);
        return this;
    }

    public String getOmsOrderNo() {
        this.nodifyGetFiled("omsOrderNo");
        return omsOrderNo;
    }

}
