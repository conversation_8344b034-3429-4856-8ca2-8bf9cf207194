package com.eci.project.etmsBdDriver.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.codeConvert.EciCode;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 司机基础信息对象 ETMS_BD_DRIVER
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-04-14
*/
@ApiModel("司机基础信息")
@TableName("ETMS_BD_DRIVER")
@FieldNameConstants
public class EtmsBdDriverEntity extends EciBaseEntity{
    /**
    * GUID
    */
    @ApiModelProperty("GUID(50)")
    @TableId("GUID")
    private String guid;

    /**
    * 姓名
    */
    @ApiModelProperty("姓名(20)")
    @TableField("NAME")
    private String name;

    @ApiModelProperty("管理状态(3)")
    @TableField("MANAGE_STATUS")
    @EciCode("Driver_Manage_Status")
    private String manageStatus;

    /**
    * 手机号
    */
    @ApiModelProperty("手机号(30)")
    @TableField("PHONE")
    private String phone;

    /**
    * 是否挂靠司机
    */
    @ApiModelProperty("是否挂靠司机(1)")
    @TableField("IS_GK")
    private String isGk;

    /**
    * 权限平台用户ID
    */
    @ApiModelProperty("权限平台用户ID(20)")
    @TableField("USER_ID")
    private String userId;

    /**
    * 创建时间
    */
    @ApiModelProperty("创建时间(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建时间开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建时间结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 创建人企业
    */
    @ApiModelProperty("创建人企业(50)")
    @TableField("CREATE_COMPANY")
    private String createCompany;

    /**
    * 修改时间
    */
    @ApiModelProperty("修改时间(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("修改时间开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("修改时间结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 修改人
    */
    @ApiModelProperty("修改人(50)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 备注
    */
    @ApiModelProperty("备注(200)")
    @TableField("MEMO")
    private String memo;

    /**
    * 车牌号
    */
    @ApiModelProperty("车牌号(20)")
    @TableField("TRUCK_NO")
    private String truckNo;
    @ApiModelProperty("合作伙伴名称")
    @TableField(exist = false)
    private String partnerName;
    @TableField(exist = false)
    private String quasiCarTypeName;
    /**
    * 是否启用
    */
    @ApiModelProperty("是否启用(1)")
    @TableField("STATUS")
    @EciCode("YNKey")
    private String status;

    /**
    * 驾驶证号
    */
    @ApiModelProperty("驾驶证号(20)")
    @TableField("DRIVER_NO")
    private String driverNo;

    /**
    * 驾驶证换证日期
    */
    @ApiModelProperty("驾驶证换证日期(7)")
    @TableField("DRIVER_DATE")
    private Date driverDate;

    @ApiModelProperty("驾驶证换证日期开始")
    @TableField(exist=false)
    private Date driverDateStart;

    @ApiModelProperty("驾驶证换证日期结束")
    @TableField(exist=false)
    private Date driverDateEnd;

    /**
    * 是否驾驶员
    */
    @ApiModelProperty("是否驾驶员(20)")
    @TableField("IS_DRIVER")
    private String isDriver;

    /**
    * 身份证号
    */
    @ApiModelProperty("身份证号(50)")
    @TableField("ID_NUMBER")
    private String idNumber;

    /**
    * 准驾车型
    */
    @ApiModelProperty("准驾车型(20)")
    @TableField("QUASI_CAR_TYPE")
    private String quasiCarType;

    /**
    * 押运员
    */
    @ApiModelProperty("押运员(20)")
    @TableField("SUPERCARGO")
    private String supercargo;

    /**
    * 出生日期
    */
    @ApiModelProperty("出生日期(7)")
    @TableField("BIRTH_DATE")
    private Date birthDate;

    @ApiModelProperty("出生日期开始")
    @TableField(exist=false)
    private Date birthDateStart;

    @ApiModelProperty("出生日期结束")
    @TableField(exist=false)
    private Date birthDateEnd;

    /**
    * 性别
    */
    @ApiModelProperty("性别(20)")
    @TableField("SEX")
    private String sex;

    /**
    * 住址
    */
    @ApiModelProperty("住址(100)")
    @TableField("ADDRESS")
    private String address;

    /**
    * 合作伙伴 GUID
    */
    @ApiModelProperty("合作伙伴 GUID(100)")
    @TableField("PARTNER_GUID")
    private String partnerGuid;

    /**
    * 登陆账号
    */
    @ApiModelProperty("登陆账号(30)")
    @TableField("LOGIN_NO")
    private String loginNo;

    /**
    * 是否使用APP
    */
    @ApiModelProperty("是否使用APP(20)")
    @TableField("IS_USE_APP")
    private String isUseApp;

    /**
    * 公司GUID
    */
    @ApiModelProperty("公司GUID(50)")
    @TableField("SSO_COMPANY_GUID")
    private String ssoCompanyGuid;

    /**
    * 押运员号码
    */
    @ApiModelProperty("押运员号码(30)")
    @TableField("SUPERCARGO_PHONE")
    private String supercargoPhone;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 极光-是否注册(0成功)
    */
    @ApiModelProperty("极光-是否注册(0成功)(50)")
    @TableField("JG_ISZC")
    private String jgIszc;

    /**
    * 司机在途
    */
    @ApiModelProperty("司机在途(50)")
    @TableField("SJZT_ISZC")
    private String sjztIszc;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public EtmsBdDriverEntity() {
        this.setSubClazz(EtmsBdDriverEntity.class);
    }

    public EtmsBdDriverEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public EtmsBdDriverEntity setName(String name) {
        this.name = name;
        this.nodifySetFiled("name", name);
        return this;
    }

    public String getName() {
        this.nodifyGetFiled("name");
        return name;
    }

    public EtmsBdDriverEntity setManageStatus(String manageStatus) {
        this.manageStatus = manageStatus;
        this.nodifySetFiled("manageStatus", manageStatus);
        return this;
    }
    public String getManageStatus() {
        this.nodifyGetFiled("manageStatus");
        return manageStatus;
    }

    public EtmsBdDriverEntity setPhone(String phone) {
        this.phone = phone;
        this.nodifySetFiled("phone", phone);
        return this;
    }

    public String getPhone() {
        this.nodifyGetFiled("phone");
        return phone;
    }

    public EtmsBdDriverEntity setIsGk(String isGk) {
        this.isGk = isGk;
        this.nodifySetFiled("isGk", isGk);
        return this;
    }

    public String getIsGk() {
        this.nodifyGetFiled("isGk");
        return isGk;
    }

    public EtmsBdDriverEntity setUserId(String userId) {
        this.userId = userId;
        this.nodifySetFiled("userId", userId);
        return this;
    }

    public String getUserId() {
        this.nodifyGetFiled("userId");
        return userId;
    }

    public EtmsBdDriverEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public EtmsBdDriverEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public EtmsBdDriverEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public EtmsBdDriverEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public EtmsBdDriverEntity setCreateCompany(String createCompany) {
        this.createCompany = createCompany;
        this.nodifySetFiled("createCompany", createCompany);
        return this;
    }

    public String getCreateCompany() {
        this.nodifyGetFiled("createCompany");
        return createCompany;
    }

    public EtmsBdDriverEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public EtmsBdDriverEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public EtmsBdDriverEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public EtmsBdDriverEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public EtmsBdDriverEntity setMemo(String memo) {
        this.memo = memo;
        this.nodifySetFiled("memo", memo);
        return this;
    }

    public String getMemo() {
        this.nodifyGetFiled("memo");
        return memo;
    }

    public EtmsBdDriverEntity setTruckNo(String truckNo) {
        this.truckNo = truckNo;
        this.nodifySetFiled("truckNo", truckNo);
        return this;
    }

    public String getTruckNo() {
        this.nodifyGetFiled("truckNo");
        return truckNo;
    }

    public EtmsBdDriverEntity setStatus(String status) {
        this.status = status;
        this.nodifySetFiled("status", status);
        return this;
    }

    public String getStatus() {
        this.nodifyGetFiled("status");
        return status;
    }

    public EtmsBdDriverEntity setDriverNo(String driverNo) {
        this.driverNo = driverNo;
        this.nodifySetFiled("driverNo", driverNo);
        return this;
    }

    public String getDriverNo() {
        this.nodifyGetFiled("driverNo");
        return driverNo;
    }

    public EtmsBdDriverEntity setDriverDate(Date driverDate) {
        this.driverDate = driverDate;
        this.nodifySetFiled("driverDate", driverDate);
        return this;
    }

    public Date getDriverDate() {
        this.nodifyGetFiled("driverDate");
        return driverDate;
    }

    public EtmsBdDriverEntity setDriverDateStart(Date driverDateStart) {
        this.driverDateStart = driverDateStart;
        this.nodifySetFiled("driverDateStart", driverDateStart);
        return this;
    }

    public Date getDriverDateStart() {
        this.nodifyGetFiled("driverDateStart");
        return driverDateStart;
    }

    public EtmsBdDriverEntity setDriverDateEnd(Date driverDateEnd) {
        this.driverDateEnd = driverDateEnd;
        this.nodifySetFiled("driverDateEnd", driverDateEnd);
        return this;
    }

    public Date getDriverDateEnd() {
        this.nodifyGetFiled("driverDateEnd");
        return driverDateEnd;
    }
    public EtmsBdDriverEntity setIsDriver(String isDriver) {
        this.isDriver = isDriver;
        this.nodifySetFiled("isDriver", isDriver);
        return this;
    }

    public String getIsDriver() {
        this.nodifyGetFiled("isDriver");
        return isDriver;
    }

    public EtmsBdDriverEntity setIdNumber(String idNumber) {
        this.idNumber = idNumber;
        this.nodifySetFiled("idNumber", idNumber);
        return this;
    }

    public String getIdNumber() {
        this.nodifyGetFiled("idNumber");
        return idNumber;
    }

    public EtmsBdDriverEntity setQuasiCarType(String quasiCarType) {
        this.quasiCarType = quasiCarType;
        this.nodifySetFiled("quasiCarType", quasiCarType);
        return this;
    }

    public String getQuasiCarType() {
        this.nodifyGetFiled("quasiCarType");
        return quasiCarType;
    }

    public EtmsBdDriverEntity setSupercargo(String supercargo) {
        this.supercargo = supercargo;
        this.nodifySetFiled("supercargo", supercargo);
        return this;
    }

    public String getSupercargo() {
        this.nodifyGetFiled("supercargo");
        return supercargo;
    }

    public EtmsBdDriverEntity setBirthDate(Date birthDate) {
        this.birthDate = birthDate;
        this.nodifySetFiled("birthDate", birthDate);
        return this;
    }

    public Date getBirthDate() {
        this.nodifyGetFiled("birthDate");
        return birthDate;
    }

    public EtmsBdDriverEntity setBirthDateStart(Date birthDateStart) {
        this.birthDateStart = birthDateStart;
        this.nodifySetFiled("birthDateStart", birthDateStart);
        return this;
    }

    public Date getBirthDateStart() {
        this.nodifyGetFiled("birthDateStart");
        return birthDateStart;
    }

    public EtmsBdDriverEntity setBirthDateEnd(Date birthDateEnd) {
        this.birthDateEnd = birthDateEnd;
        this.nodifySetFiled("birthDateEnd", birthDateEnd);
        return this;
    }

    public Date getBirthDateEnd() {
        this.nodifyGetFiled("birthDateEnd");
        return birthDateEnd;
    }
    public EtmsBdDriverEntity setSex(String sex) {
        this.sex = sex;
        this.nodifySetFiled("sex", sex);
        return this;
    }

    public String getSex() {
        this.nodifyGetFiled("sex");
        return sex;
    }

    public EtmsBdDriverEntity setAddress(String address) {
        this.address = address;
        this.nodifySetFiled("address", address);
        return this;
    }

    public String getAddress() {
        this.nodifyGetFiled("address");
        return address;
    }

    public EtmsBdDriverEntity setPartnerGuid(String partnerGuid) {
        this.partnerGuid = partnerGuid;
        this.nodifySetFiled("partnerGuid", partnerGuid);
        return this;
    }

    public String getPartnerGuid() {
        this.nodifyGetFiled("partnerGuid");
        return partnerGuid;
    }

    public EtmsBdDriverEntity setLoginNo(String loginNo) {
        this.loginNo = loginNo;
        this.nodifySetFiled("loginNo", loginNo);
        return this;
    }

    public String getLoginNo() {
        this.nodifyGetFiled("loginNo");
        return loginNo;
    }

    public EtmsBdDriverEntity setIsUseApp(String isUseApp) {
        this.isUseApp = isUseApp;
        this.nodifySetFiled("isUseApp", isUseApp);
        return this;
    }

    public String getIsUseApp() {
        this.nodifyGetFiled("isUseApp");
        return isUseApp;
    }

    public EtmsBdDriverEntity setSsoCompanyGuid(String ssoCompanyGuid) {
        this.ssoCompanyGuid = ssoCompanyGuid;
        this.nodifySetFiled("ssoCompanyGuid", ssoCompanyGuid);
        return this;
    }

    public String getSsoCompanyGuid() {
        this.nodifyGetFiled("ssoCompanyGuid");
        return ssoCompanyGuid;
    }

    public EtmsBdDriverEntity setSupercargoPhone(String supercargoPhone) {
        this.supercargoPhone = supercargoPhone;
        this.nodifySetFiled("supercargoPhone", supercargoPhone);
        return this;
    }

    public String getSupercargoPhone() {
        this.nodifyGetFiled("supercargoPhone");
        return supercargoPhone;
    }

    public EtmsBdDriverEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public EtmsBdDriverEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public EtmsBdDriverEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public EtmsBdDriverEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public EtmsBdDriverEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public EtmsBdDriverEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public EtmsBdDriverEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public EtmsBdDriverEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public EtmsBdDriverEntity setJgIszc(String jgIszc) {
        this.jgIszc = jgIszc;
        this.nodifySetFiled("jgIszc", jgIszc);
        return this;
    }

    public String getJgIszc() {
        this.nodifyGetFiled("jgIszc");
        return jgIszc;
    }

    public EtmsBdDriverEntity setSjztIszc(String sjztIszc) {
        this.sjztIszc = sjztIszc;
        this.nodifySetFiled("sjztIszc", sjztIszc);
        return this;
    }

    public String getSjztIszc() {
        this.nodifyGetFiled("sjztIszc");
        return sjztIszc;
    }

    public EtmsBdDriverEntity setPartnerName(String partnerName) {
        this.partnerName = partnerName;
        this.nodifySetFiled("partnerName", partnerName);
        return this;
    }

    public String getPartnerName() {
        this.nodifyGetFiled("partnerName");
        return partnerName;
    }

    public EtmsBdDriverEntity setQuasiCarTypeName(String quasiCarTypeName) {
        this.quasiCarTypeName = quasiCarTypeName;
        this.nodifySetFiled("quasiCarTypeName", quasiCarTypeName);
        return this;
    }

    public String getQuasiCarTypeName() {
        this.nodifyGetFiled("quasiCarTypeName");
        return quasiCarTypeName;
    }

}
