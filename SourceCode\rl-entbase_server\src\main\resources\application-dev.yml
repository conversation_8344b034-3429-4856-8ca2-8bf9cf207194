﻿spring:
  datasource:
    driver-class-name: org.postgresql.Driver
    url: ***************************************************  #***************************************************
    username: cqdm_basic
    password: cqdm_basic_1qaz
    type: com.alibaba.druid.pool.DruidDataSource
#    driver-class-name: oracle.jdbc.driver.OracleDriver
#    url: *******************************************
#    username: PT1_ECI_CQDM
#    password: ecidh.com2024
#    type: com.alibaba.druid.pool.DruidDataSource
  redis:
    host: ***************
    port: 6379
    database: 13
    password: Redis_1qaz
    timeout: 5000
    lettuce:
      pool:
        max-active: 8
        max-wait: -1
        max-idle: 8
        min-idle: 0
  cache:
    redis:
      cache-null-values: false
  mvc:
    static-path-pattern: /Attachment/**
  web:
    resources:
      static-locations:
        - file:D:/Uploads/Attachment/
logging:
  level:
    # 开发使用debug, 生产改成info
    com.eci: debug
    org.springframework: warn
    com.baomidou: error
  config: classpath:logback-spring.xml
  file:
    path: .\logs
tgExcel:
  serverUrl: http://localhost:9527
  openCors: true
  excel-dir-path: C:\TgExcel
  excel-read-limit: 10000
# 缓存配置
cache:
  # 缓存策略: auto(自动检测), redis(强制Redis), local(强制本地)
  strategy: auto
  # 本地缓存配置
  local:
    max-size: 1000
    expire-minutes: 60
  # Redis 缓存配置
  redis:
    connection-test-timeout: 3000
    # 降级策略配置
    fallback:
      enabled: true
      max-errors: 5
      error-window-seconds: 60

project:
  # 使用权限平台配置 权限平台:web界面登录地址:https://framework.ecidh.com/ssotg;账号:dev,密码:123456
  dpssorole:
    publicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCa4KHNwDX44gGmmIAtRu4gjVYtGWZzcm4t+1wjUD4dn7fMLPvuK7ai4UrfDeEJE1RPwudJw+lJ6crql8wSIg7/DbTlG3ihsCT6dT9H5B9OoeR7K9VWUesaW/iyVL6HXiYOANabW14pvJATDmdq91Tfgp6PSQyvdfiRdV4r07crpQIDAQAB
    host: https://framework.ecidh.com/ssoapitg
    apiKey: ZDQxZDhjZDk4ZjAwYjIwNGU5ODAwOTk4ZWNmODQyN2U=
    menuID: MAIN-56b7f1f4f287415f6a563bca0899b680
    filepath: /mnt/cqdm/Attachment
    file-store:
      path: /
  cors:
    open: true
  token:
    exclude: /fzgjBdServiceItem/getCheckedNodes,/etmsOpFileQz/upload,/etmsOpFile/upload,/Attachment/**,/crmFileInfo/downLoadAttr,/crmFileInfo/downLoadAttrs,/fanKui/**,/omsFile/previewAttr,/omsFile/downLoadAttr,/etmsOpFileQz/previewAttr,/fzgjBaseInformation/previewAttr
# 与灵境对接的接口地址配置
api:
  order:
    save-url: http://**************/ljapi-glwl/b2bOmsWtrw/save