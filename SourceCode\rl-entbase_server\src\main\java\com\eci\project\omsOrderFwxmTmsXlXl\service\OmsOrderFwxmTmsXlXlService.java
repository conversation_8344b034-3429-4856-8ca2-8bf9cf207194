package com.eci.project.omsOrderFwxmTmsXlXl.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eci.common.db.DBHelper;
import com.eci.common.util.StringUtils;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjBaseDataDetail.entity.FzgjBaseDataDetailEntity;
import com.eci.project.omsOrderFwxmTmsXlXl.dao.OmsOrderFwxmTmsXlXlDao;
import com.eci.project.omsOrderFwxmTmsXlXl.entity.OmsOrderFwxmTmsXlXlDTOEntity;
import com.eci.project.omsOrderFwxmTmsXlXl.entity.OmsOrderFwxmTmsXlXlEntity;
import com.eci.project.omsOrderFwxmTmsXlXl.validate.OmsOrderFwxmTmsXlXlVal;

import com.eci.project.omsOrderFwxmTmsXlXlLyXl.dao.OmsOrderFwxmTmsXlXlLyXlDao;
import com.eci.project.omsOrderFwxmTmsXlXlLyXl.entity.OmsOrderFwxmTmsXlXlLyXlDTOEntity;
import com.eci.project.omsOrderFwxmTmsXlXlLyXl.entity.OmsOrderFwxmTmsXlXlLyXlEntity;
import com.eci.wu.core.DataTable;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.cms.PasswordRecipientId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 委托内容-程运序列Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-05-28
 */
@Service
@Slf4j
public class OmsOrderFwxmTmsXlXlService implements EciBaseService<OmsOrderFwxmTmsXlXlEntity> {

    @Autowired
    private OmsOrderFwxmTmsXlXlDao omsOrderFwxmTmsXlXlDao;
    @Autowired
    private OmsOrderFwxmTmsXlXlLyXlDao omsOrderFwxmTmsXlXlLyXlDao;

    @Autowired
    private OmsOrderFwxmTmsXlXlVal omsOrderFwxmTmsXlXlVal;


    @Override
    public TgPageInfo queryPageList(OmsOrderFwxmTmsXlXlEntity entity) {
        EciQuery<OmsOrderFwxmTmsXlXlEntity> eciQuery = EciQuery.buildQuery(entity);
        List<OmsOrderFwxmTmsXlXlEntity> entities = omsOrderFwxmTmsXlXlDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderFwxmTmsXlXlEntity save(OmsOrderFwxmTmsXlXlEntity entity) {
        // 返回实体对象
        OmsOrderFwxmTmsXlXlEntity omsOrderFwxmTmsXlXlEntity = null;
        omsOrderFwxmTmsXlXlVal.saveValidate(entity, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            omsOrderFwxmTmsXlXlEntity = omsOrderFwxmTmsXlXlDao.insertOne(entity);

        } else {

            omsOrderFwxmTmsXlXlEntity = omsOrderFwxmTmsXlXlDao.updateByEntityId(entity);

        }
        return omsOrderFwxmTmsXlXlEntity;
    }

    @Override
    public List<OmsOrderFwxmTmsXlXlEntity> selectList(OmsOrderFwxmTmsXlXlEntity entity) {
        return omsOrderFwxmTmsXlXlDao.selectList(entity);
    }

    @Override
    public OmsOrderFwxmTmsXlXlEntity selectOneById(Serializable id) {
        return omsOrderFwxmTmsXlXlDao.selectById(id);
    }


    @Override
    public void insertBatch(List<OmsOrderFwxmTmsXlXlEntity> list) {
        omsOrderFwxmTmsXlXlDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return omsOrderFwxmTmsXlXlDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return omsOrderFwxmTmsXlXlDao.deleteById(id);
    }


    /**
     * 查询路线行
     */
    public List<OmsOrderFwxmTmsXlXlLyXlDTOEntity> getGetXlRow(OmsOrderFwxmTmsXlXlLyXlEntity entity) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(" SELECT A.GUID,A.GROUP_CODE,A.CODE,A.NAME,A.STATUS\n" +
                "                           ,A.MEMO,A.SEQ,A.BELONG_GROUP\n" +
                "                           FROM FZGJ_BASE_DATA_DETAIL A LEFT JOIN FZGJ_BASE_DATA B ON A.GROUP_CODE=B.GROUP_CODE\n" +
                "                           where A.GROUP_CODE='" + entity.getGroupCode() + "' order by A.SEQ asc /*'OMS_AREA_TYPE'*/");
        List<FzgjBaseDataDetailEntity> dt = DBHelper.selectList(stringBuilder.toString(), FzgjBaseDataDetailEntity.class);

        String sql = "select DISTINCT A.*,B.CITY_NAME from OMS_ORDER_FWXM_TMS_XL_XL_LY_XL A LEFT JOIN V_FZGJ_BD_AREA B ON A.CITY=B.CITY_CODE AND A.GROUP_CODE = B.GROUP_CODE\n" +
                "                               where 1=1 ";
        sql += " AND (A.ORDER_NO='" + entity.getOrderNo() + "' OR A.PRE_NO='" + entity.getPreNo() + "')";
        sql += " AND A.LY_NO='" + entity.getLyNo() + "'";
        sql += " order by A.XL_SEQ asc";
        List<OmsOrderFwxmTmsXlXlLyXlDTOEntity> listLyXl = DBHelper.selectList(sql, OmsOrderFwxmTmsXlXlLyXlDTOEntity.class);
        List<OmsOrderFwxmTmsXlXlLyXlDTOEntity> dtXlXl = new ArrayList<>();
        if (listLyXl.size() > 0) {
            dtXlXl = listLyXl;
            dtXlXl.forEach(lyxl -> {
                List<FzgjBaseDataDetailEntity> listBase = dt.stream().filter(a -> a.getCode().equals(lyxl.getXlType())).collect(Collectors.toList());
                if (listBase.size() > 0) {
                    lyxl.setMemo(listBase.get(0).getName());
                }
            });
        } else {
            for (FzgjBaseDataDetailEntity fzgjBaseDataDetailEntity : dt) {
                if (fzgjBaseDataDetailEntity.getCode().equals("QS") || fzgjBaseDataDetailEntity.getCode().equals("ZD") || fzgjBaseDataDetailEntity.getCode().equals("TJ")) {
                    OmsOrderFwxmTmsXlXlLyXlDTOEntity lyxl = new OmsOrderFwxmTmsXlXlLyXlDTOEntity();
                    lyxl.setXlType(fzgjBaseDataDetailEntity.getCode());
                    lyxl.setMemo(fzgjBaseDataDetailEntity.getName());
                    dtXlXl.add(lyxl);
                }
            }
        }
        return dtXlXl;
    }
}