package com.eci.project.crmCustomerHzfwGys.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eci.common.db.DBHelper;
import com.eci.common.utils.CommonLib;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.crmCustomerHzfwGys.dao.CrmCustomerHzfwGysDao;
import com.eci.project.crmCustomerHzfwGys.entity.CrmCustomerHzfwGysEntity;
import com.eci.project.crmCustomerHzfwGys.validate.CrmCustomerHzfwGysVal;

import com.eci.project.fzgjBdServiceItemPt.entity.TreeModel;
import com.eci.sso.role.entity.UserContext;
import com.eci.wu.core.DataTable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.xml.datatype.DatatypeConfigurationException;
import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;


/**
* 供应商合作服务Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-05-13
*/
@Service
@Slf4j
public class CrmCustomerHzfwGysService implements EciBaseService<CrmCustomerHzfwGysEntity> {

    @Autowired
    private CrmCustomerHzfwGysDao crmCustomerHzfwGysDao;

    @Autowired
    private CrmCustomerHzfwGysVal crmCustomerHzfwGysVal;

    CommonLib cmn = CommonLib.getInstance();

    @Override
    public TgPageInfo queryPageList(CrmCustomerHzfwGysEntity entity) {
        EciQuery<CrmCustomerHzfwGysEntity> eciQuery = EciQuery.buildQuery(entity);
        List<CrmCustomerHzfwGysEntity> entities = crmCustomerHzfwGysDao.selectlist(eciQuery);
        return EciQuery.getPageInfo(entities);
    }

    public List<CrmCustomerHzfwGysEntity> selectTreeWhitGYS(String groupCode, String customerCode, String serviceGuid) {
        QueryWrapper query = new QueryWrapper();
        query.eq("A.GROUP_CODE", groupCode);
        if (!serviceGuid.isEmpty())
            query.eq("A.Owned_Service", serviceGuid);
        return crmCustomerHzfwGysDao.selectTreeWhitGYS(query, customerCode);
    }

    public String SelectServiceId(String customerCode) {
        String sql = "select B.Owned_Service from CRM_CUSTOMER_HZFW_GYS A " +
                "inner join FZGJ_BD_SERVICE_ITEM B on A.FWXM_CODE=B.CODE\n" +
                "WHERE A.customer_code=%s AND A.GROUP_CODE='%s' AND B.GROUP_CODE='%s' group by B.Owned_Service";
        sql = String.format(sql, cmn.SQLQ(customerCode), UserContext.getUserInfo().getCompanyCode(), UserContext.getUserInfo().getCompanyCode());
        DataTable dt = DBHelper.getDataTable(sql);
        if (dt.rows.size() > 0)
            return dt.rows.get(0).getString("OWNED_SERVICE");
        return "";
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public CrmCustomerHzfwGysEntity save(CrmCustomerHzfwGysEntity entity) {
        // 返回实体对象
        CrmCustomerHzfwGysEntity crmCustomerHzfwGysEntity = null;
        crmCustomerHzfwGysVal.saveValidate(entity, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            crmCustomerHzfwGysEntity = crmCustomerHzfwGysDao.insertOne(entity);

        } else {

            crmCustomerHzfwGysEntity = crmCustomerHzfwGysDao.updateByEntityId(entity);

        }
        return crmCustomerHzfwGysEntity;
    }

    @Override
    public List<CrmCustomerHzfwGysEntity> selectList(CrmCustomerHzfwGysEntity entity) {
        return crmCustomerHzfwGysDao.selectList(entity);
    }

    @Override
    public CrmCustomerHzfwGysEntity selectOneById(Serializable id) {
        return crmCustomerHzfwGysDao.selectById(id);
    }


    @Override
    public void insertBatch(List<CrmCustomerHzfwGysEntity> list) {
        crmCustomerHzfwGysDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return crmCustomerHzfwGysDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return crmCustomerHzfwGysDao.deleteById(id);
    }

    public CrmCustomerHzfwGysEntity Build(CrmCustomerHzfwGysEntity model,List<CrmCustomerHzfwGysEntity> services) {
        BuildTreeData(model, services);
        return model;
    }


    public void BuildTreeData(CrmCustomerHzfwGysEntity model, List<CrmCustomerHzfwGysEntity> services) {
        model.setChildren(services.stream()
                .filter(p -> p.getParentId().equals(model.getItemGuid()))
                .collect(Collectors.toList()));
        if (model.getChildren() != null && model.getChildren().size() > 0) {
            model.getChildren().forEach(p -> {
                BuildTreeData(p, services);
            });
        }
    }

    public void deleteByCustomerCodeAndFwlxCode(String cusomerCode,String fwlxCode){
        QueryWrapper query=new QueryWrapper();
        query.eq("CUSTOMER_CODE",cusomerCode);
        query.eq("FWLX_CODE",fwlxCode);
        query.eq("GROUP_CODE",UserContext.getUserInfo().getCompanyCode());
        crmCustomerHzfwGysDao.delete(query);
    }
}