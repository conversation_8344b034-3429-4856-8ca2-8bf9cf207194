package com.eci.project.fzgjTaskLimitation.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjTaskLimitation.entity.FzgjTaskLimitationEntity;

import org.springframework.stereotype.Service;


/**
* 作业环节及标准时效Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-03-28
*/
@Service
public class FzgjTaskLimitationVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjTaskLimitationEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjTaskLimitationEntity entity, BusinessType businessType) {

    }

}
