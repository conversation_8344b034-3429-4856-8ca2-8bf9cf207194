package com.eci.project.omsOrder.validate;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eci.common.DBHelperX;
import com.eci.common.DataExtend;
import com.eci.common.enums.OrderEnum;
import com.eci.common.util.StringUtils;
import com.eci.common.utils.CommonLib;
import com.eci.exception.BaseException;
import com.eci.log.enums.BusinessType;
import com.eci.project.omsOrder.dao.OmsOrderDao;
import com.eci.project.omsOrder.entity.OmsOrderEntity;
import com.eci.sso.role.entity.UserContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 订单表Val接口
 * 验证接口
 *
 * @<NAME_EMAIL>
 * @date 2025-04-14
 */
@Service
public class OmsOrderVal {

    CommonLib cmn = CommonLib.getInstance();

    @Autowired
    private OmsOrderDao omsOrderDao;

    /**
     * 删除验证
     *
     * @param entity
     */
    public void deleteValidate(OmsOrderEntity entity) {

    }

    /**
     * 保存验证
     *
     * @param entity
     */
    public void saveValidate(OmsOrderEntity entity, BusinessType businessType) {

    }


    /// <summary>
    /// 保存订单  验证
    /// </summary>
    /// <param name="context"></param>
    /// <param name="eciDataGroup"></param>
    /// <param name="isAdd"></param>
    public void SaveValidate(OmsOrderEntity order, boolean isAdd, String saveType) {
        String groupCode = UserContext.getUserInfo().getCompanyCode();
        if (StringUtils.isNotEmpty(order.getOrderNo())) {
            // 订单编辑保存
            String checkSql = "SELECT ORDER_NO FROM OMS_ORDER WHERE CONSIGNEE_CODE = " + cmn.SQLQ(DataExtend.toCode(order.getConsigneeCode())) + " AND CUSTOMER_ORDER_NO =" + cmn.SQLQ(order.getCustomerOrderNo()) + " AND GROUP_CODE = " + cmn.SQLQ(groupCode) + " ";
            checkSql += " AND BIZ_REG_ID != (SELECT A.BIZ_REG_ID FROM OMS_ORDER A WHERE A.ORDER_NO = " + cmn.SQLQ(order.getOrderNo()) + ")";

            DBHelperX.checkRepeat(checkSql, "ORDER_NO", order.getOrderNo(), "委托方【" + order.getConsigneeCode() + "】已存在客户单据号【" + order.getCustomerOrderNo() + "】的订单！");
        } else if (StringUtils.isNotEmpty(order.getBizRegId())) {
            //协同订单审核
            String checkSql = "SELECT BIZ_REG_ID FROM OMS_ORDER WHERE CONSIGNEE_CODE = " + cmn.SQLQ(DataExtend.toCode(order.getConsigneeCode())) + " AND CUSTOMER_ORDER_NO =" + cmn.SQLQ(order.getCustomerOrderNo()) + " AND GROUP_CODE = " + cmn.SQLQ(groupCode) + " ";
            checkSql += " AND BIZ_REG_ID != " + cmn.SQLQ(order.getBizRegId());
            DBHelperX.checkRepeat(checkSql, "BIZ_REG_ID", order.getBizRegId(), "委托方【" + order.getConsigneeCode() + "】已存在客户单据号【" + order.getCustomerOrderNo() + "】的订单！");
        } else {
            //OMS新增订单
            String checkSql = "SELECT ORDER_NO FROM OMS_ORDER WHERE CONSIGNEE_CODE = " + cmn.SQLQ(DataExtend.toCode(order.getConsigneeCode())) + " AND CUSTOMER_ORDER_NO =" + cmn.SQLQ(order.getCustomerOrderNo()) + " AND GROUP_CODE = " + cmn.SQLQ(groupCode) + " ";
            DBHelperX.checkRepeat(checkSql, "ORDER_NO", order.getOrderNo(), "委托方【" + order.getConsigneeCode() + "】已存在客户单据号【" + order.getCustomerOrderNo() + "】的订单！");
        }

        if (!isAdd) {
            QueryWrapper query = new QueryWrapper();
            query.eq("ORDER_NO", order.getOrderNo());
            List<OmsOrderEntity> orderCheck = omsOrderDao.selectList(query);
            if (orderCheck == null) {
                throw new BaseException("订单信息不存在或者已删除!");
            }

            if (!saveType.equals(OrderEnum.OrderSaveType.SAVETEMPLATE) && !saveType.equals(OrderEnum.OrderSaveType.YUWEI)) {
                if (!orderCheck.get(0).getStatus().equals(OrderEnum.OrderStatus.ZC)) {
                    throw new BaseException("订单非【暂存】状态，不允许修改！");
                }
            }
        }
    }
}
