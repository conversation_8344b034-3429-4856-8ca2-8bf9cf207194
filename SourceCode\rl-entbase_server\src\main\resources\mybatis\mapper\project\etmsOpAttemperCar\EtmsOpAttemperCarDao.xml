<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.etmsOpAttemperCar.dao.EtmsOpAttemperCarDao">
    <resultMap type="EtmsOpAttemperCarEntity" id="EtmsOpAttemperCarResult">
        <result property="guid" column="GUID"/>
        <result property="attGuid" column="ATT_GUID"/>
        <result property="opNo" column="OP_NO"/>
        <result property="deliveryType" column="DELIVERY_TYPE"/>
        <result property="zcType" column="ZC_TYPE"/>
        <result property="zcModel" column="ZC_MODEL"/>
        <result property="zcNum" column="ZC_NUM"/>
        <result property="ldBalance" column="LD_BALANCE"/>
        <result property="isJzx" column="IS_JZX"/>
        <result property="status" column="STATUS"/>
        <result property="createCompany" column="CREATE_COMPANY"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="opcarNo" column="OPCAR_NO"/>
        <result property="farathestEnd" column="FARATHEST_END"/>
        <result property="orgDepId" column="ORG_DEP_ID"/>
        <result property="orgDepCode" column="ORG_DEP_CODE"/>
        <result property="orgDepName" column="ORG_DEP_NAME"/>
        <result property="finishedDate" column="FINISHED_DATE"/>
        <result property="attNo" column="ATT_NO"/>
        <result property="pieces" column="PIECES"/>
        <result property="weight" column="WEIGHT"/>
        <result property="volume" column="VOLUME"/>
        <result property="cargo" column="CARGO"/>
        <result property="truckSpec" column="TRUCK_SPEC"/>
        <result property="senderCode" column="SENDER_CODE"/>
        <result property="consigneeCode" column="CONSIGNEE_CODE"/>
        <result property="startRequestDate" column="START_REQUEST_DATE"/>
        <result property="endRequestDate" column="END_REQUEST_DATE"/>
        <result property="startOpArea" column="START_OP_AREA"/>
        <result property="endOpArea" column="END_OP_AREA"/>
        <result property="carLine" column="CAR_LINE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="attributeCode" column="ATTRIBUTE_CODE"/>
        <result property="pcType" column="PC_TYPE"/>
        <result property="pcNo" column="PC_NO"/>
        <result property="pcCarGuid" column="PC_CAR_GUID"/>
    </resultMap>

    <sql id="selectEtmsOpAttemperCarEntityVo">
        select
            GUID,
            ATT_GUID,
            OP_NO,
            DELIVERY_TYPE,
            ZC_TYPE,
            ZC_MODEL,
            ZC_NUM,
            LD_BALANCE,
            IS_JZX,
            STATUS,
            CREATE_COMPANY,
            CREATE_USER,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_DATE,
            OPCAR_NO,
            FARATHEST_END,
            ORG_DEP_ID,
            ORG_DEP_CODE,
            ORG_DEP_NAME,
            FINISHED_DATE,
            ATT_NO,
            PIECES,
            WEIGHT,
            VOLUME,
            CARGO,
            TRUCK_SPEC,
            SENDER_CODE,
            CONSIGNEE_CODE,
            START_REQUEST_DATE,
            END_REQUEST_DATE,
            START_OP_AREA,
            END_OP_AREA,
            CAR_LINE,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            CREATE_USER_NAME,
            UPDATE_USER_NAME,
            ATTRIBUTE_CODE,
            PC_TYPE,
            PC_NO,
            PC_CAR_GUID
        from ETMS_OP_ATTEMPER_CAR
    </sql>
</mapper>