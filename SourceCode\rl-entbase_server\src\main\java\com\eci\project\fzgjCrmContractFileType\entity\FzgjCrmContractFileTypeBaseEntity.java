package com.eci.project.fzgjCrmContractFileType.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.eci.project.fzgjCrmContractTypeWarn.entity.FzgjCrmContractTypeWarnEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;

import java.util.Date;


/**
* 合同附件类型对象 FZGJ_CRM_CONTRACT_FILE_TYPE
*
* @<NAME_EMAIL>
* @date 2025-03-19
*/
@FieldNameConstants
public class FzgjCrmContractFileTypeBaseEntity extends EciBaseEntity
{
	/**
	* GUID
	*/
	@ApiModelProperty("GUID(50)")
	@TableId("GUID")
	private String guid;

	/**
	* 编码
	*/
	@ApiModelProperty("编码(200)")
	@TableField("CODE")
	private String code;

	/**
	* 名称
	*/
	@ApiModelProperty("名称(200)")
	@TableField("NAME")
	private String name;

	/**
	* 目标类型（附件类型）
	*/
	@ApiModelProperty("目标类型（附件类型）(50)")
	@TableField("TARGET_TYPE_CODE")
	private String targetTypeCode;

	/**
	* 状态
	*/
	@ApiModelProperty("状态(1)")
	@TableField("STATUS")
	private String status;

	/**
	* 顺序
	*/
	@ApiModelProperty("顺序(22)")
	@TableField("SEQ")
	private Integer seq;

	/**
	* 备注
	*/
	@ApiModelProperty("备注(50)")
	@TableField("MEMO")
	private String memo;

	/**
	* 创建人
	*/
	@ApiModelProperty("创建人(50)")
	@TableField("CREATE_USER")
	private String createUser;

	/**
	* 创建人名称
	*/
	@ApiModelProperty("创建人名称(50)")
	@TableField("CREATE_USER_NAME")
	private String createUserName;

	/**
	* 创建时间
	*/
	@ApiModelProperty("创建时间(7)")
	@TableField("CREATE_DATE")
	private Date createDate;

	@ApiModelProperty("创建时间开始")
	@TableField(exist=false)
	private Date createDateStart;

	@ApiModelProperty("创建时间结束")
	@TableField(exist=false)
	private Date createDateEnd;

	/**
	* 修改人
	*/
	@ApiModelProperty("修改人(50)")
	@TableField("UPDATE_USER")
	private String updateUser;

	/**
	* 修改人名称
	*/
	@ApiModelProperty("修改人名称(50)")
	@TableField("UPDATE_USER_NAME")
	private String updateUserName;

	/**
	* 修改时间
	*/
	@ApiModelProperty("修改时间(7)")
	@TableField("UPDATE_DATE")
	private Date updateDate;

	@ApiModelProperty("修改时间开始")
	@TableField(exist=false)
	private Date updateDateStart;

	@ApiModelProperty("修改时间结束")
	@TableField(exist=false)
	private Date updateDateEnd;

	/**
	* 组织代码
	*/
	@ApiModelProperty("组织代码(50)")
	@TableField("NODE_CODE")
	private String nodeCode;

	/**
	* 组织名称
	*/
	@ApiModelProperty("组织名称(200)")
	@TableField("NODE_NAME")
	private String nodeName;

	/**
	* 公司代码
	*/
	@ApiModelProperty("公司代码(50)")
	@TableField("COMPANY_CODE")
	private String companyCode;

	/**
	* 公司名称
	*/
	@ApiModelProperty("公司名称(200)")
	@TableField("COMPANY_NAME")
	private String companyName;

	/**
	* 集团代码
	*/
	@ApiModelProperty("集团代码(50)")
	@TableField("GROUP_CODE")
	private String groupCode;

	/**
	* 集团名称
	*/
	@ApiModelProperty("集团名称(200)")
	@TableField("GROUP_NAME")
	private String groupName;

	@JsonIgnore
	@TableField(exist = false)
	private Class<?> clazz = this.getClass();

	public Class<?> getClazz() {
		return clazz;
	}

	public void setClazz(Class<?> clazz) {
		this.clazz = clazz;
	}

	public FzgjCrmContractFileTypeBaseEntity setGuid(String guid) {
		this.guid = guid;
		return this;
	}

	public String getGuid() {
		return guid;
	}

	public FzgjCrmContractFileTypeBaseEntity setCode(String code) {
		this.code = code;
		return this;
	}

	public String getCode() {
		return code;
	}

	public FzgjCrmContractFileTypeBaseEntity setName(String name) {
		this.name = name;
		return this;
	}

	public String getName() {
		return name;
	}

	public FzgjCrmContractFileTypeBaseEntity setTargetTypeCode(String targetTypeCode) {
		this.targetTypeCode = targetTypeCode;
		return this;
	}

	public String getTargetTypeCode() {
		return targetTypeCode;
	}

	public FzgjCrmContractFileTypeBaseEntity setStatus(String status) {
		this.status = status;
		return this;
	}

	public String getStatus() {
		return status;
	}

	public FzgjCrmContractFileTypeBaseEntity setSeq(Integer seq) {
		this.seq = seq;
		return this;
	}

	public Integer getSeq() {
		return seq;
	}

	public FzgjCrmContractFileTypeBaseEntity setMemo(String memo) {
		this.memo = memo;
		return this;
	}

	public String getMemo() {
		return memo;
	}

	public FzgjCrmContractFileTypeBaseEntity setCreateUser(String createUser) {
		this.createUser = createUser;
		return this;
	}

	public String getCreateUser() {
		return createUser;
	}

	public FzgjCrmContractFileTypeBaseEntity setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
		return this;
	}

	public String getCreateUserName() {
		return createUserName;
	}

	public FzgjCrmContractFileTypeBaseEntity setCreateDate(Date createDate) {
		this.createDate = createDate;
		return this;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public FzgjCrmContractFileTypeBaseEntity setCreateDateStart(Date createDateStart) {
		this.createDateStart = createDateStart;
		return this;
	}

	public Date getCreateDateStart() {
		return createDateStart;
	}

	public FzgjCrmContractFileTypeBaseEntity setCreateDateEnd(Date createDateEnd) {
		this.createDateEnd = createDateEnd;
		return this;
	}

	public Date getCreateDateEnd() {
		return createDateEnd;
	}
	public FzgjCrmContractFileTypeBaseEntity setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
		return this;
	}

	public String getUpdateUser() {
		return updateUser;
	}

	public FzgjCrmContractFileTypeBaseEntity setUpdateUserName(String updateUserName) {
		this.updateUserName = updateUserName;
		return this;
	}

	public String getUpdateUserName() {
		return updateUserName;
	}

	public FzgjCrmContractFileTypeBaseEntity setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
		return this;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public FzgjCrmContractFileTypeBaseEntity setUpdateDateStart(Date updateDateStart) {
		this.updateDateStart = updateDateStart;
		return this;
	}

	public Date getUpdateDateStart() {
		return updateDateStart;
	}

	public FzgjCrmContractFileTypeBaseEntity setUpdateDateEnd(Date updateDateEnd) {
		this.updateDateEnd = updateDateEnd;
		return this;
	}

	public Date getUpdateDateEnd() {
		return updateDateEnd;
	}
	public FzgjCrmContractFileTypeBaseEntity setNodeCode(String nodeCode) {
		this.nodeCode = nodeCode;
		return this;
	}

	public String getNodeCode() {
		return nodeCode;
	}

	public FzgjCrmContractFileTypeBaseEntity setNodeName(String nodeName) {
		this.nodeName = nodeName;
		return this;
	}

	public String getNodeName() {
		return nodeName;
	}

	public FzgjCrmContractFileTypeBaseEntity setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
		return this;
	}

	public String getCompanyCode() {
		return companyCode;
	}

	public FzgjCrmContractFileTypeBaseEntity setCompanyName(String companyName) {
		this.companyName = companyName;
		return this;
	}

	public String getCompanyName() {
		return companyName;
	}

	public FzgjCrmContractFileTypeBaseEntity setGroupCode(String groupCode) {
		this.groupCode = groupCode;
		return this;
	}

	public String getGroupCode() {
		return groupCode;
	}

	public FzgjCrmContractFileTypeBaseEntity setGroupName(String groupName) {
		this.groupName = groupName;
		return this;
	}

	public String getGroupName() {
		return groupName;
	}
}
