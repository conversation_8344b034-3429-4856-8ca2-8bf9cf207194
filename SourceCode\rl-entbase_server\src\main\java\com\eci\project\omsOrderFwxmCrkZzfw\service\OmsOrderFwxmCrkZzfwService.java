package com.eci.project.omsOrderFwxmCrkZzfw.service;

import com.eci.common.db.DBHelper;
import com.eci.common.utils.CommonLib;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.omsOrderFwxmCrkZzfw.dao.OmsOrderFwxmCrkZzfwDao;
import com.eci.project.omsOrderFwxmCrkZzfw.entity.OmsOrderFwxmCrkZzfwEntity;
import com.eci.project.omsOrderFwxmCrkZzfw.validate.OmsOrderFwxmCrkZzfwVal;
import com.eci.project.omsOrderFwxmWorkFkZzfw.entity.OmsOrderFwxmWorkFkZzfwEntity;
import com.eci.wu.core.EntityBase;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.List;


/**
 * 委托内容-仓储-增值服务明细Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-05-21
 */
@Service
@Slf4j
public class OmsOrderFwxmCrkZzfwService implements EciBaseService<OmsOrderFwxmCrkZzfwEntity> {

    CommonLib cmn = CommonLib.getInstance();

    @Autowired
    private OmsOrderFwxmCrkZzfwDao omsOrderFwxmCrkZzfwDao;

    @Autowired
    private OmsOrderFwxmCrkZzfwVal omsOrderFwxmCrkZzfwVal;


    @Override
    public TgPageInfo queryPageList(OmsOrderFwxmCrkZzfwEntity entity) {
        EciQuery<OmsOrderFwxmCrkZzfwEntity> eciQuery = EciQuery.buildQuery(entity);
        List<OmsOrderFwxmCrkZzfwEntity> entities = omsOrderFwxmCrkZzfwDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderFwxmCrkZzfwEntity save(OmsOrderFwxmCrkZzfwEntity entity) {
        // 返回实体对象
        OmsOrderFwxmCrkZzfwEntity omsOrderFwxmCrkZzfwEntity = null;
        omsOrderFwxmCrkZzfwVal.saveValidate(entity, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            omsOrderFwxmCrkZzfwEntity = omsOrderFwxmCrkZzfwDao.insertOne(entity);

        } else {

            omsOrderFwxmCrkZzfwEntity = omsOrderFwxmCrkZzfwDao.updateByEntityId(entity);

        }
        return omsOrderFwxmCrkZzfwEntity;
    }

    @Override
    public List<OmsOrderFwxmCrkZzfwEntity> selectList(OmsOrderFwxmCrkZzfwEntity entity) {
        return omsOrderFwxmCrkZzfwDao.selectList(entity);
    }

    @Override
    public OmsOrderFwxmCrkZzfwEntity selectOneById(Serializable id) {
        return omsOrderFwxmCrkZzfwDao.selectById(id);
    }


    @Override
    public void insertBatch(List<OmsOrderFwxmCrkZzfwEntity> list) {
        omsOrderFwxmCrkZzfwDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return omsOrderFwxmCrkZzfwDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return omsOrderFwxmCrkZzfwDao.deleteById(id);
    }

    /**
     * 其他作业服务
     */
    public List<OmsOrderFwxmWorkFkZzfwEntity> loadQtKcZzfw(String bizRegId) {
        String sql = "SELECT\n" +
                "     OP_ITEM,\n" +
                "     OP_PROPERTY ,\n" +
                "     UNIT,\n" +
                "     OP_QTY_REAL,\n" +
                "     MEMO,\n" +
                "     A.GUID,\n" +
                "     A.LINE_NUM\n" +
                " FROM OMS_ORDER_FWXM_WORK_FK_ZZFW A\n" +
                "WHERE  BIZ_REG_ID ="+cmn.SQLQ(bizRegId)+"";

        return DBHelper.selectList(sql, OmsOrderFwxmWorkFkZzfwEntity.class);
    }
}