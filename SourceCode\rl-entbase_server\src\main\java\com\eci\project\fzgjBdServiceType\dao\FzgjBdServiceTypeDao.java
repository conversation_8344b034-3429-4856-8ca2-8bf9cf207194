package com.eci.project.fzgjBdServiceType.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.eci.project.fzgjBdServiceItemPt.entity.TreeModel;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.fzgjBdServiceType.entity.FzgjBdServiceTypeEntity;

import java.util.List;


/**
* 服务类型Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-03-13
*/
public interface FzgjBdServiceTypeDao extends EciBaseDao<FzgjBdServiceTypeEntity> {
    List<TreeModel> selectTree(@Param(Constants.WRAPPER) Wrapper queryWrapper);
}