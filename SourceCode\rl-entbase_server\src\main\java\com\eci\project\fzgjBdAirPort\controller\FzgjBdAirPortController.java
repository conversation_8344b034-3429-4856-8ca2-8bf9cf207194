package com.eci.project.fzgjBdAirPort.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjBdAirPort.service.FzgjBdAirPortService;
import com.eci.project.fzgjBdAirPort.entity.FzgjBdAirPortEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 空运港口Controller
*
* @<NAME_EMAIL>
* @date 2025-03-14
*/
@Api(tags = "空运港口")
@RestController
@RequestMapping("/fzgjBdAirPort")
public class FzgjBdAirPortController extends EciBaseController {

    @Autowired
    private FzgjBdAirPortService fzgjBdAirPortService;


    @ApiOperation("空运港口:保存")
    @EciLog(title = "空运港口:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody FzgjBdAirPortEntity entity){
        FzgjBdAirPortEntity fzgjBdAirPortEntity =fzgjBdAirPortService.save(entity);
        return ResponseMsgUtil.success(10001,fzgjBdAirPortEntity);
    }


    @ApiOperation("空运港口:查询列表")
    @EciLog(title = "空运港口:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjBdAirPortEntity entity){
        List<FzgjBdAirPortEntity> fzgjBdAirPortEntities = fzgjBdAirPortService.selectList(entity);
        return ResponseMsgUtil.success(10001,fzgjBdAirPortEntities);
    }


    @ApiOperation("空运港口:分页查询列表")
    @EciLog(title = "空运港口:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjBdAirPortEntity entity){
        TgPageInfo tgPageInfo = fzgjBdAirPortService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("空运港口:根据ID查一条")
    @EciLog(title = "空运港口:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjBdAirPortEntity entity){
        FzgjBdAirPortEntity  fzgjBdAirPortEntity = fzgjBdAirPortService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,fzgjBdAirPortEntity);
    }


    @ApiOperation("空运港口:根据ID删除一条")
    @EciLog(title = "空运港口:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjBdAirPortEntity entity){
        int count = fzgjBdAirPortService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("空运港口:根据ID字符串删除多条")
    @EciLog(title = "空运港口:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjBdAirPortEntity entity) {
        int count = fzgjBdAirPortService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}