package com.eci.project.etmsBdTruckSpec.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.etmsBdTruckSpec.entity.EtmsBdTruckSpecEntity;


/**
* 车辆规则Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-03-21
*/
public interface EtmsBdTruckSpecDao extends EciBaseDao<EtmsBdTruckSpecEntity> {

}