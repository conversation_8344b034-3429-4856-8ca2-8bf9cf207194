package com.eci.project.fzgjBdOpType.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjBdOpType.entity.FzgjBdOpTypeEntity;

import org.springframework.stereotype.Service;


/**
* 业务类型Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-03-27
*/
@Service
public class FzgjBdOpTypeVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjBdOpTypeEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjBdOpTypeEntity entity, BusinessType businessType) {

    }

}
