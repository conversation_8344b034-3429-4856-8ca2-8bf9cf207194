package com.eci.project.crmCustomerHzTrade.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.crmCustomerHzTrade.dao.CrmCustomerHzTradeDao;
import com.eci.project.crmCustomerHzTrade.entity.CrmCustomerHzTradeEntity;
import com.eci.project.crmCustomerHzTrade.validate.CrmCustomerHzTradeVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 业务伙伴货主贸易关系Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-05-20
*/
@Service
@Slf4j
public class CrmCustomerHzTradeService implements EciBaseService<CrmCustomerHzTradeEntity> {

    @Autowired
    private CrmCustomerHzTradeDao crmCustomerHzTradeDao;

    @Autowired
    private CrmCustomerHzTradeVal crmCustomerHzTradeVal;


    @Override
    public TgPageInfo queryPageList(CrmCustomerHzTradeEntity entity) {
        EciQuery<CrmCustomerHzTradeEntity> eciQuery = EciQuery.buildQuery(entity);
        List<CrmCustomerHzTradeEntity> entities = crmCustomerHzTradeDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public CrmCustomerHzTradeEntity save(CrmCustomerHzTradeEntity entity) {
        // 返回实体对象
        CrmCustomerHzTradeEntity crmCustomerHzTradeEntity = null;
        crmCustomerHzTradeVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            crmCustomerHzTradeEntity = crmCustomerHzTradeDao.insertOne(entity);

        }else{

            crmCustomerHzTradeEntity = crmCustomerHzTradeDao.updateByEntityId(entity);

        }
        return crmCustomerHzTradeEntity;
    }

    @Override
    public List<CrmCustomerHzTradeEntity> selectList(CrmCustomerHzTradeEntity entity) {
        return crmCustomerHzTradeDao.selectList(entity);
    }

    @Override
    public CrmCustomerHzTradeEntity selectOneById(Serializable id) {
        return crmCustomerHzTradeDao.selectById(id);
    }


    @Override
    public void insertBatch(List<CrmCustomerHzTradeEntity> list) {
        crmCustomerHzTradeDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return crmCustomerHzTradeDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return crmCustomerHzTradeDao.deleteById(id);
    }

}