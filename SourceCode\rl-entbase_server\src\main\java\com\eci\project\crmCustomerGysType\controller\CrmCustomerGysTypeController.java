package com.eci.project.crmCustomerGysType.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.crmCustomerGysType.service.CrmCustomerGysTypeService;
import com.eci.project.crmCustomerGysType.entity.CrmCustomerGysTypeEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 供应商类型Controller
*
* @<NAME_EMAIL>
* @date 2025-05-14
*/
@Api(tags = "供应商类型")
@RestController
@RequestMapping("/crmCustomerGysType")
public class CrmCustomerGysTypeController extends EciBaseController {

    @Autowired
    private CrmCustomerGysTypeService crmCustomerGysTypeService;


    @ApiOperation("供应商类型:保存")
    @EciLog(title = "供应商类型:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody CrmCustomerGysTypeEntity entity){
        CrmCustomerGysTypeEntity crmCustomerGysTypeEntity =crmCustomerGysTypeService.save(entity);
        return ResponseMsgUtil.success(10001,crmCustomerGysTypeEntity);
    }


    @ApiOperation("供应商类型:查询列表")
    @EciLog(title = "供应商类型:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody CrmCustomerGysTypeEntity entity){
        List<CrmCustomerGysTypeEntity> crmCustomerGysTypeEntities = crmCustomerGysTypeService.selectList(entity);
        return ResponseMsgUtil.success(10001,crmCustomerGysTypeEntities);
    }


    @ApiOperation("供应商类型:分页查询列表")
    @EciLog(title = "供应商类型:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody CrmCustomerGysTypeEntity entity){
        TgPageInfo tgPageInfo = crmCustomerGysTypeService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("供应商类型:根据ID查一条")
    @EciLog(title = "供应商类型:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody CrmCustomerGysTypeEntity entity){
        CrmCustomerGysTypeEntity  crmCustomerGysTypeEntity = crmCustomerGysTypeService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,crmCustomerGysTypeEntity);
    }


    @ApiOperation("供应商类型:根据ID删除一条")
    @EciLog(title = "供应商类型:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody CrmCustomerGysTypeEntity entity){
        int count = crmCustomerGysTypeService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("供应商类型:根据ID字符串删除多条")
    @EciLog(title = "供应商类型:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody CrmCustomerGysTypeEntity entity) {
        int count = crmCustomerGysTypeService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}