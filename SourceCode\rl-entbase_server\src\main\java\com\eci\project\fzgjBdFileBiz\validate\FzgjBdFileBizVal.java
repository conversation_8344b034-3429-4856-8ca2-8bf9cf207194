package com.eci.project.fzgjBdFileBiz.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjBdFileBiz.entity.FzgjBdFileBizEntity;

import org.springframework.stereotype.Service;


/**
* 业务附件类型及授权Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-11
*/
@Service
public class FzgjBdFileBizVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjBdFileBizEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjBdFileBizEntity entity, BusinessType businessType) {

    }

}
