package com.eci.project.etmsBdTruckQz.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.etmsBdTruckQz.entity.EtmsBdTruckQzEntity;

import org.springframework.stereotype.Service;


/**
* 车辆信息Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-09
*/
@Service
public class EtmsBdTruckQzVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(EtmsBdTruckQzEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(EtmsBdTruckQzEntity entity, BusinessType businessType) {

    }

}
