package com.eci.project.fzgjTaskLimitationFixed.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjTaskLimitationFixed.service.FzgjTaskLimitationFixedService;
import com.eci.project.fzgjTaskLimitationFixed.entity.FzgjTaskLimitationFixedEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 作业环节及时效标准条件Controller
*
* @<NAME_EMAIL>
* @date 2025-05-22
*/
@Api(tags = "作业环节及时效标准条件")
@RestController
@RequestMapping("/fzgjTaskLimitationFixed")
public class FzgjTaskLimitationFixedController extends EciBaseController {

    @Autowired
    private FzgjTaskLimitationFixedService fzgjTaskLimitationFixedService;


    @ApiOperation("作业环节及时效标准条件:保存")
    @EciLog(title = "作业环节及时效标准条件:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody FzgjTaskLimitationFixedEntity entity){
        FzgjTaskLimitationFixedEntity fzgjTaskLimitationFixedEntity =fzgjTaskLimitationFixedService.save(entity);
        return ResponseMsgUtil.success(10001,fzgjTaskLimitationFixedEntity);
    }


    @ApiOperation("作业环节及时效标准条件:查询列表")
    @EciLog(title = "作业环节及时效标准条件:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjTaskLimitationFixedEntity entity){
        List<FzgjTaskLimitationFixedEntity> fzgjTaskLimitationFixedEntities = fzgjTaskLimitationFixedService.selectList(entity);
        return ResponseMsgUtil.success(10001,fzgjTaskLimitationFixedEntities);
    }


    @ApiOperation("作业环节及时效标准条件:分页查询列表")
    @EciLog(title = "作业环节及时效标准条件:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjTaskLimitationFixedEntity entity){
        TgPageInfo tgPageInfo = fzgjTaskLimitationFixedService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("作业环节及时效标准条件:根据ID查一条")
    @EciLog(title = "作业环节及时效标准条件:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjTaskLimitationFixedEntity entity){
        FzgjTaskLimitationFixedEntity  fzgjTaskLimitationFixedEntity = fzgjTaskLimitationFixedService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,fzgjTaskLimitationFixedEntity);
    }


    @ApiOperation("作业环节及时效标准条件:根据ID删除一条")
    @EciLog(title = "作业环节及时效标准条件:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjTaskLimitationFixedEntity entity){
        int count = fzgjTaskLimitationFixedService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("作业环节及时效标准条件:根据ID字符串删除多条")
    @EciLog(title = "作业环节及时效标准条件:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjTaskLimitationFixedEntity entity) {
        int count = fzgjTaskLimitationFixedService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}