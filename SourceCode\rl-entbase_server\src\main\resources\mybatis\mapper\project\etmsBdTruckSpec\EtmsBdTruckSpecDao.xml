<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.etmsBdTruckSpec.dao.EtmsBdTruckSpecDao">
    <resultMap type="EtmsBdTruckSpecEntity" id="EtmsBdTruckSpecResult">
        <result property="guid" column="GUID"/>
        <result property="truckType" column="TRUCK_TYPE"/>
        <result property="code" column="CODE"/>
        <result property="xweight" column="XWEIGHT"/>
        <result property="xlength" column="XLENGTH"/>
        <result property="xwidth" column="XWIDTH"/>
        <result property="xheight" column="XHEIGHT"/>
        <result property="xvolume" column="XVOLUME"/>
        <result property="xxWd" column="XX_WD"/>
        <result property="sxWd" column="SX_WD"/>
        <result property="createCompany" column="CREATE_COMPANY"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
    </resultMap>

    <sql id="selectEtmsBdTruckSpecEntityVo">
        select
            GUID,
            TRUCK_TYPE,
            CODE,
            XWEIGHT,
            XLENGTH,
            XWIDTH,
            XHEIGHT,
            XVOLUME,
            XX_WD,
            SX_WD,
            CREATE_COMPANY,
            UPDATE_DATE,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            CREATE_USER_NAME,
            UPDATE_USER_NAME
        from ETMS_BD_TRUCK_SPEC
    </sql>
</mapper>