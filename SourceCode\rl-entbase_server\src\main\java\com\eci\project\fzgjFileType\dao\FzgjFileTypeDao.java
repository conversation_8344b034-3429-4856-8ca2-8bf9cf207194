package com.eci.project.fzgjFileType.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.fzgjFileType.entity.FzgjFileTypeEntity;


/**
* 附件类型Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-04-11
*/
public interface FzgjFileTypeDao extends EciBaseDao<FzgjFileTypeEntity> {

}