package com.eci.project.etmsBdTruckGcQz.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.etmsBdTruckGcQz.entity.EtmsBdTruckGcQzEntity;


/**
* 挂车号Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-04-16
*/
public interface EtmsBdTruckGcQzDao extends EciBaseDao<EtmsBdTruckGcQzEntity> {

}