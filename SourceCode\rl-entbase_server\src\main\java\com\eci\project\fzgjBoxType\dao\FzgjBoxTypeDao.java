package com.eci.project.fzgjBoxType.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.fzgjBoxType.entity.FzgjBoxTypeEntity;


/**
* 集装箱类型Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-04-17
*/
public interface FzgjBoxTypeDao extends EciBaseDao<FzgjBoxTypeEntity> {

}