package com.eci.project.fzgjFile.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjFile.service.FzgjFileService;
import com.eci.project.fzgjFile.entity.FzgjFileEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 附件Controller
*
* @<NAME_EMAIL>
* @date 2025-06-04
*/
@Api(tags = "附件")
@RestController
@RequestMapping("/fzgjFile")
public class FzgjFileController extends EciBaseController {

    @Autowired
    private FzgjFileService fzgjFileService;


    @ApiOperation("附件:保存")
    @EciLog(title = "附件:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody FzgjFileEntity entity){
        FzgjFileEntity fzgjFileEntity =fzgjFileService.save(entity);
        return ResponseMsgUtil.success(10001,fzgjFileEntity);
    }


    @ApiOperation("附件:查询列表")
    @EciLog(title = "附件:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjFileEntity entity){
        List<FzgjFileEntity> fzgjFileEntities = fzgjFileService.selectList(entity);
        return ResponseMsgUtil.success(10001,fzgjFileEntities);
    }


    @ApiOperation("附件:分页查询列表")
    @EciLog(title = "附件:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjFileEntity entity){
        TgPageInfo tgPageInfo = fzgjFileService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("附件:根据ID查一条")
    @EciLog(title = "附件:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjFileEntity entity){
        FzgjFileEntity  fzgjFileEntity = fzgjFileService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,fzgjFileEntity);
    }


    @ApiOperation("附件:根据ID删除一条")
    @EciLog(title = "附件:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjFileEntity entity){
        int count = fzgjFileService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("附件:根据ID字符串删除多条")
    @EciLog(title = "附件:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjFileEntity entity) {
        int count = fzgjFileService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}