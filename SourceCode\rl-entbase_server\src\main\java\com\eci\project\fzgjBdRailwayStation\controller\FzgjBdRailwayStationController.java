package com.eci.project.fzgjBdRailwayStation.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjBdRailwayStation.entity.FzgjBdRailwayStationPageEntity;
import com.eci.project.fzgjBdRailwayStation.service.FzgjBdRailwayStationService;
import com.eci.project.fzgjBdRailwayStation.entity.FzgjBdRailwayStationEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 铁路站点Controller
*
* @<NAME_EMAIL>
* @date 2025-03-25
*/
@Api(tags = "铁路站点")
@RestController
@RequestMapping("/fzgjBdRailwayStation")
public class FzgjBdRailwayStationController extends EciBaseController {

    @Autowired
    private FzgjBdRailwayStationService fzgjBdRailwayStationService;


    @ApiOperation("铁路站点:保存")
    @EciLog(title = "铁路站点:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody FzgjBdRailwayStationEntity entity){
        FzgjBdRailwayStationEntity fzgjBdRailwayStationEntity =fzgjBdRailwayStationService.save(entity);
        return ResponseMsgUtil.success(10001,fzgjBdRailwayStationEntity);
    }


    @ApiOperation("铁路站点:查询列表")
    @EciLog(title = "铁路站点:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjBdRailwayStationEntity entity){
        List<FzgjBdRailwayStationEntity> fzgjBdRailwayStationEntities = fzgjBdRailwayStationService.selectList(entity);
        return ResponseMsgUtil.success(10001,fzgjBdRailwayStationEntities);
    }


    @ApiOperation("铁路站点:分页查询列表")
    @EciLog(title = "铁路站点:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjBdRailwayStationEntity entity){
        TgPageInfo tgPageInfo = fzgjBdRailwayStationService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("铁路站点:根据ID查一条")
    @EciLog(title = "铁路站点:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjBdRailwayStationEntity entity){
        FzgjBdRailwayStationPageEntity fzgjBdRailwayStationPageEntity = fzgjBdRailwayStationService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,fzgjBdRailwayStationPageEntity);
    }


    @ApiOperation("铁路站点:根据ID删除一条")
    @EciLog(title = "铁路站点:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjBdRailwayStationEntity entity){
        int count = fzgjBdRailwayStationService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("铁路站点:根据ID字符串删除多条")
    @EciLog(title = "铁路站点:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjBdRailwayStationEntity entity) {
        int count = fzgjBdRailwayStationService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}