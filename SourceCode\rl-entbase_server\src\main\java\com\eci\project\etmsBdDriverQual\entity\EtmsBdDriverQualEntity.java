package com.eci.project.etmsBdDriverQual.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.codeConvert.EciCode;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 司机从业资格证对象 ETMS_BD_DRIVER_QUAL
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-04-17
*/
@ApiModel("司机从业资格证")
@TableName("ETMS_BD_DRIVER_QUAL")
@FieldNameConstants
public class EtmsBdDriverQualEntity extends EciBaseEntity{
    /**
    * GUID
    */
    @ApiModelProperty("GUID(50)")
    @TableId("GUID")
    private String guid;

    @ApiModelProperty("(50)")
    @TableField("DRIVER_GUID")
    private String driverGuid;
    @TableField(exist = false)
    private String personaltype;

    /**
    * 从业资格证起始日期
    */
    @ApiModelProperty("从业资格证起始日期(7)")
    @TableField("START_DATE")
    private Date startDate;

    @ApiModelProperty("从业资格证起始日期开始")
    @TableField(exist=false)
    private Date startDateStart;

    @ApiModelProperty("从业资格证起始日期结束")
    @TableField(exist=false)
    private Date startDateEnd;

    /**
    * 从业资格证结束日期
    */
    @ApiModelProperty("从业资格证结束日期(7)")
    @TableField("END_DATE")
    private Date endDate;

    @ApiModelProperty("从业资格证结束日期开始")
    @TableField(exist=false)
    private Date endDateStart;

    @ApiModelProperty("从业资格证结束日期结束")
    @TableField(exist=false)
    private Date endDateEnd;

    /**
    * 从业资格证类别
    */
    @ApiModelProperty("从业资格证类别(20)")
    @TableField("CERTIFICATE_TYPE")
    @EciCode("ETMS_BD_CERTIFICATE_TYPE_CHOOSE")
    private String certificateType;

    /**
    * 初次领证日期
    */
    @ApiModelProperty("初次领证日期(7)")
    @TableField("FIRST_ISSUE_DATE")
    private Date firstIssueDate;

    @ApiModelProperty("初次领证日期开始")
    @TableField(exist=false)
    private Date firstIssueDateStart;

    @ApiModelProperty("初次领证日期结束")
    @TableField(exist=false)
    private Date firstIssueDateEnd;

    @ApiModelProperty("(50)")
    @TableField("CARD_NO")
    private String cardNo;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 创建时间
    */
    @ApiModelProperty("创建时间(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建时间开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建时间结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 修改人
    */
    @ApiModelProperty("修改人(50)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 修改时间
    */
    @ApiModelProperty("修改时间(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("修改时间开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("修改时间结束")
    @TableField(exist=false)
    private Date updateDateEnd;
    @TableField(exist=false)
    private String cardStatus;
    @TableField(exist=false)
    private String statusName;

    @ApiModelProperty("(50)")
    @TableField("CREATE_COMPANY")
    private String createCompany;

    /**
    * 状态
    */
    @ApiModelProperty("状态(20)")
    @TableField("STATUS")
    private String status;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public EtmsBdDriverQualEntity() {
        this.setSubClazz(EtmsBdDriverQualEntity.class);
    }

    public EtmsBdDriverQualEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public EtmsBdDriverQualEntity setDriverGuid(String driverGuid) {
        this.driverGuid = driverGuid;
        this.nodifySetFiled("driverGuid", driverGuid);
        return this;
    }

    public EtmsBdDriverQualEntity setPersonalType(String personaltype) {
        this.personaltype = personaltype;
        this.nodifySetFiled("personaltype", personaltype);
        return this;
    }
    public String getPersonalType() {
        this.nodifyGetFiled("personaltype");
        return personaltype;
    }

    public EtmsBdDriverQualEntity setCardStatus(String cardStatus) {
        this.cardStatus = cardStatus;
        this.nodifySetFiled("cardStatus", cardStatus);
        return this;
    }
    public String getCardStatus() {
        this.nodifyGetFiled("cardStatus");
        return cardStatus;
    }

    public EtmsBdDriverQualEntity setStatusName(String statusName) {
        this.statusName = statusName;
        this.nodifySetFiled("statusName", statusName);
        return this;
    }
    public String getStatusName() {
        this.nodifyGetFiled("statusName");
        return statusName;
    }

    public String getDriverGuid() {
        this.nodifyGetFiled("driverGuid");
        return driverGuid;
    }

    public EtmsBdDriverQualEntity setStartDate(Date startDate) {
        this.startDate = startDate;
        this.nodifySetFiled("startDate", startDate);
        return this;
    }

    public Date getStartDate() {
        this.nodifyGetFiled("startDate");
        return startDate;
    }

    public EtmsBdDriverQualEntity setStartDateStart(Date startDateStart) {
        this.startDateStart = startDateStart;
        this.nodifySetFiled("startDateStart", startDateStart);
        return this;
    }

    public Date getStartDateStart() {
        this.nodifyGetFiled("startDateStart");
        return startDateStart;
    }

    public EtmsBdDriverQualEntity setStartDateEnd(Date startDateEnd) {
        this.startDateEnd = startDateEnd;
        this.nodifySetFiled("startDateEnd", startDateEnd);
        return this;
    }

    public Date getStartDateEnd() {
        this.nodifyGetFiled("startDateEnd");
        return startDateEnd;
    }
    public EtmsBdDriverQualEntity setEndDate(Date endDate) {
        this.endDate = endDate;
        this.nodifySetFiled("endDate", endDate);
        return this;
    }

    public Date getEndDate() {
        this.nodifyGetFiled("endDate");
        return endDate;
    }

    public EtmsBdDriverQualEntity setEndDateStart(Date endDateStart) {
        this.endDateStart = endDateStart;
        this.nodifySetFiled("endDateStart", endDateStart);
        return this;
    }

    public Date getEndDateStart() {
        this.nodifyGetFiled("endDateStart");
        return endDateStart;
    }

    public EtmsBdDriverQualEntity setEndDateEnd(Date endDateEnd) {
        this.endDateEnd = endDateEnd;
        this.nodifySetFiled("endDateEnd", endDateEnd);
        return this;
    }

    public Date getEndDateEnd() {
        this.nodifyGetFiled("endDateEnd");
        return endDateEnd;
    }
    public EtmsBdDriverQualEntity setCertificateType(String certificateType) {
        this.certificateType = certificateType;
        this.nodifySetFiled("certificateType", certificateType);
        return this;
    }

    public String getCertificateType() {
        this.nodifyGetFiled("certificateType");
        return certificateType;
    }

    public EtmsBdDriverQualEntity setFirstIssueDate(Date firstIssueDate) {
        this.firstIssueDate = firstIssueDate;
        this.nodifySetFiled("firstIssueDate", firstIssueDate);
        return this;
    }

    public Date getFirstIssueDate() {
        this.nodifyGetFiled("firstIssueDate");
        return firstIssueDate;
    }

    public EtmsBdDriverQualEntity setFirstIssueDateStart(Date firstIssueDateStart) {
        this.firstIssueDateStart = firstIssueDateStart;
        this.nodifySetFiled("firstIssueDateStart", firstIssueDateStart);
        return this;
    }

    public Date getFirstIssueDateStart() {
        this.nodifyGetFiled("firstIssueDateStart");
        return firstIssueDateStart;
    }

    public EtmsBdDriverQualEntity setFirstIssueDateEnd(Date firstIssueDateEnd) {
        this.firstIssueDateEnd = firstIssueDateEnd;
        this.nodifySetFiled("firstIssueDateEnd", firstIssueDateEnd);
        return this;
    }

    public Date getFirstIssueDateEnd() {
        this.nodifyGetFiled("firstIssueDateEnd");
        return firstIssueDateEnd;
    }
    public EtmsBdDriverQualEntity setCardNo(String cardNo) {
        this.cardNo = cardNo;
        this.nodifySetFiled("cardNo", cardNo);
        return this;
    }

    public String getCardNo() {
        this.nodifyGetFiled("cardNo");
        return cardNo;
    }

    public EtmsBdDriverQualEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public EtmsBdDriverQualEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public EtmsBdDriverQualEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public EtmsBdDriverQualEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public EtmsBdDriverQualEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public EtmsBdDriverQualEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public EtmsBdDriverQualEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public EtmsBdDriverQualEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public EtmsBdDriverQualEntity setCreateCompany(String createCompany) {
        this.createCompany = createCompany;
        this.nodifySetFiled("createCompany", createCompany);
        return this;
    }

    public String getCreateCompany() {
        this.nodifyGetFiled("createCompany");
        return createCompany;
    }

    public EtmsBdDriverQualEntity setStatus(String status) {
        this.status = status;
        this.nodifySetFiled("status", status);
        return this;
    }

    public String getStatus() {
        this.nodifyGetFiled("status");
        return status;
    }

    public EtmsBdDriverQualEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public EtmsBdDriverQualEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public EtmsBdDriverQualEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public EtmsBdDriverQualEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public EtmsBdDriverQualEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public EtmsBdDriverQualEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public EtmsBdDriverQualEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public EtmsBdDriverQualEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

}
