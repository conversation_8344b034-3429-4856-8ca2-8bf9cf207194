package com.eci.project.fzgjGoodsAttr.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjGoodsAttr.service.FzgjGoodsAttrService;
import com.eci.project.fzgjGoodsAttr.entity.FzgjGoodsAttrEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 货物属性Controller
*
* @<NAME_EMAIL>
* @date 2025-04-01
*/
@Api(tags = "货物属性")
@RestController
@RequestMapping("/fzgjGoodsAttr")
public class FzgjGoodsAttrController extends EciBaseController {

    @Autowired
    private FzgjGoodsAttrService fzgjGoodsAttrService;


    @ApiOperation("货物属性:保存")
    @EciLog(title = "货物属性:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody FzgjGoodsAttrEntity entity){
        FzgjGoodsAttrEntity fzgjGoodsAttrEntity =fzgjGoodsAttrService.save(entity);
        return ResponseMsgUtil.success(10001,fzgjGoodsAttrEntity);
    }


    @ApiOperation("货物属性:查询列表")
    @EciLog(title = "货物属性:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjGoodsAttrEntity entity){
        List<FzgjGoodsAttrEntity> fzgjGoodsAttrEntities = fzgjGoodsAttrService.selectList(entity);
        return ResponseMsgUtil.success(10001,fzgjGoodsAttrEntities);
    }


    @ApiOperation("货物属性:分页查询列表")
    @EciLog(title = "货物属性:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjGoodsAttrEntity entity){
        TgPageInfo tgPageInfo = fzgjGoodsAttrService.queryPageList(entity);
        return ResponseMsgUtil.successPlus(10001,tgPageInfo);
    }


    @ApiOperation("货物属性:根据ID查一条")
    @EciLog(title = "货物属性:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjGoodsAttrEntity entity){
        FzgjGoodsAttrEntity  fzgjGoodsAttrEntity = fzgjGoodsAttrService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,fzgjGoodsAttrEntity);
    }


    @ApiOperation("货物属性:根据ID删除一条")
    @EciLog(title = "货物属性:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjGoodsAttrEntity entity){
        int count = fzgjGoodsAttrService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("货物属性:根据ID字符串删除多条")
    @EciLog(title = "货物属性:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjGoodsAttrEntity entity) {
        int count = fzgjGoodsAttrService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}