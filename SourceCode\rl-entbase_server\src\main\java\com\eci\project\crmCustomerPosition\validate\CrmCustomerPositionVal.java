package com.eci.project.crmCustomerPosition.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.crmCustomerPosition.entity.CrmCustomerPositionEntity;

import com.eci.sso.role.entity.UserContext;
import org.springframework.stereotype.Service;

import java.util.Date;


/**
* 合作伙伴职务Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-05-26
*/
@Service
public class CrmCustomerPositionVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(CrmCustomerPositionEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(CrmCustomerPositionEntity entity, BusinessType businessType) {
        entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        entity.setUpdateDate(new Date());
        entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
        if(businessType==BusinessType.INSERT){
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateDate(new Date());
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
            entity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            entity.setCompanyName(UserContext.getUserInfo().getCompanyName());
        }
    }

}
