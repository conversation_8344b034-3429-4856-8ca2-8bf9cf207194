package com.eci.project.fzgjBdProductFwxm.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjBdProductFwxm.dao.FzgjBdProductFwxmDao;
import com.eci.project.fzgjBdProductFwxm.entity.FzgjBdProductFwxmEntity;
import com.eci.project.fzgjBdProductFwxm.validate.FzgjBdProductFwxmVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 产品服务项目Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-01
*/
@Service
@Slf4j
public class FzgjBdProductFwxmService implements EciBaseService<FzgjBdProductFwxmEntity> {

    @Autowired
    private FzgjBdProductFwxmDao fzgjBdProductFwxmDao;

    @Autowired
    private FzgjBdProductFwxmVal fzgjBdProductFwxmVal;


    @Override
    public TgPageInfo queryPageList(FzgjBdProductFwxmEntity entity) {
        EciQuery<FzgjBdProductFwxmEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjBdProductFwxmEntity> entities = fzgjBdProductFwxmDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjBdProductFwxmEntity save(FzgjBdProductFwxmEntity entity) {
        // 返回实体对象
        FzgjBdProductFwxmEntity fzgjBdProductFwxmEntity = null;
        fzgjBdProductFwxmVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjBdProductFwxmEntity = fzgjBdProductFwxmDao.insertOne(entity);

        }else{

            fzgjBdProductFwxmEntity = fzgjBdProductFwxmDao.updateByEntityId(entity);

        }
        return fzgjBdProductFwxmEntity;
    }

    @Override
    public List<FzgjBdProductFwxmEntity> selectList(FzgjBdProductFwxmEntity entity) {
        return fzgjBdProductFwxmDao.selectList(entity);
    }

    @Override
    public FzgjBdProductFwxmEntity selectOneById(Serializable id) {
        return fzgjBdProductFwxmDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjBdProductFwxmEntity> list) {
        fzgjBdProductFwxmDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjBdProductFwxmDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjBdProductFwxmDao.deleteById(id);
    }

}