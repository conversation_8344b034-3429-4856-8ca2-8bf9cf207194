package com.eci.project.fzgjExceptionDescribe.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.fzgjExceptionDescribe.entity.FzgjExceptionDescribeEntity;


/**
* 异常描述Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-04-01
*/
public interface FzgjExceptionDescribeDao extends EciBaseDao<FzgjExceptionDescribeEntity> {

}