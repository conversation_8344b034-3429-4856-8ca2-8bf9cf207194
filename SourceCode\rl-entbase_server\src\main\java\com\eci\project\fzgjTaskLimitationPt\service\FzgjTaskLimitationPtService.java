package com.eci.project.fzgjTaskLimitationPt.service;

import com.eci.common.Zsr;
import com.eci.common.ZsrJson;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.exception.BaseException;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjTaskLimitationPt.dao.FzgjTaskLimitationPtDao;
import com.eci.project.fzgjTaskLimitationPt.entity.FzgjTaskLimitationPtEntity;
import com.eci.project.fzgjTaskLimitationPt.validate.FzgjTaskLimitationPtVal;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 平台级作业环节及参考时效Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-04-08
 */
@Service
@Slf4j
public class FzgjTaskLimitationPtService implements EciBaseService<FzgjTaskLimitationPtEntity> {

    @Autowired
    private FzgjTaskLimitationPtDao fzgjTaskLimitationPtDao;

    @Autowired
    private FzgjTaskLimitationPtVal fzgjTaskLimitationPtVal;

    /**
     * 根据targetCode【服务项目的code】查询作业环节数据
     *
     * @param jsonString json字符串
     * @return
     */
    @SneakyThrows
    public List<FzgjTaskLimitationPtEntity> selectListByTargetCode(String jsonString) {

        // 根据json字符串获取targetCode
        ZsrJson zsrJson = ZsrJson.parse(jsonString);
        // 查询具体的某一条数据
        String guid = zsrJson.getString("guid");
        String status = zsrJson.getStringOrDefault("status", "Y");

        if (Zsr.String.IsNullOrWhiteSpace(guid)) {
            // 检查targetCode并取值，根据这个查询数据库
            String targetCode = zsrJson.check("targetCode").getString("targetCode");
            List<FzgjTaskLimitationPtEntity> list = fzgjTaskLimitationPtDao.select().eq(FzgjTaskLimitationPtEntity::getTargetCode, targetCode)
                    .eq(FzgjTaskLimitationPtEntity::getStatus, status)
                    .orderBy(true,FzgjTaskLimitationPtEntity::getSeq)
                    .list();
            return list;
        } else {
            return fzgjTaskLimitationPtDao.select().eq(FzgjTaskLimitationPtEntity::getGuid, guid)
                    .list();
        }
    }

    /**
     * 上移或者下移
     * <remark>更新数据的顺序</remark>
     * @param jsonString
     */
    public FzgjTaskLimitationPtEntity updateSeq(String jsonString) {
        // 根据json字符串获取targetCode
        ZsrJson zsrJson = ZsrJson.parse(jsonString);
        String guid = zsrJson.check("guid").getString("guid");
        String seq = zsrJson.getString("seq");
        boolean isDown = zsrJson.getStringOrDefault("isDown", "Y").equals("Y");

        FzgjTaskLimitationPtEntity entity = fzgjTaskLimitationPtDao.select().eq(FzgjTaskLimitationPtEntity::getGuid, guid).one();
        if (Zsr.String.IsNullOrWhiteSpace(seq)) {

            List<FzgjTaskLimitationPtEntity> data = fzgjTaskLimitationPtDao.select()
                    .eq(FzgjTaskLimitationPtEntity::getTargetCode, entity.getTargetCode())
                    .list();

            List<FzgjTaskLimitationPtEntity> dataOld = data.stream().filter(x -> x.getSeq().equals(isDown ? entity.getSeq() - 1 : entity.getSeq() + 1)).collect(Collectors.toList());
            dataOld.forEach(item -> {
                item.setSeq(entity.getSeq());
                fzgjTaskLimitationPtDao.updateByEntityId(entity);
            });

            entity.setSeq(isDown ? entity.getSeq() - 1 : entity.getSeq() + 1);

            if (entity.getSeq() > data.size()) {
                throw new BaseException("已无法下移！");
            } else if (entity.getSeq() <= 0) {
                throw new BaseException("已无法上移");
            }
            fzgjTaskLimitationPtDao.updateByEntityId(entity);

        } else {
            entity.setSeq(Integer.valueOf(seq));
            fzgjTaskLimitationPtDao.updateByEntityId(entity);
        }

        return entity;
    }

    @Override
    public TgPageInfo queryPageList(FzgjTaskLimitationPtEntity entity) {
        EciQuery<FzgjTaskLimitationPtEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjTaskLimitationPtEntity> entities = fzgjTaskLimitationPtDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjTaskLimitationPtEntity save(FzgjTaskLimitationPtEntity entity) {
        // 返回实体对象
        FzgjTaskLimitationPtEntity fzgjTaskLimitationPtEntity = null;
        fzgjTaskLimitationPtVal.saveValidate(entity, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjTaskLimitationPtEntity = fzgjTaskLimitationPtDao.insertOne(entity);

        } else {

            fzgjTaskLimitationPtEntity = fzgjTaskLimitationPtDao.updateByEntityId(entity);

        }
        return fzgjTaskLimitationPtEntity;
    }

    @Override
    public List<FzgjTaskLimitationPtEntity> selectList(FzgjTaskLimitationPtEntity entity) {
        return fzgjTaskLimitationPtDao.selectList(entity);
    }

    @Override
    public FzgjTaskLimitationPtEntity selectOneById(Serializable id) {
        return fzgjTaskLimitationPtDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjTaskLimitationPtEntity> list) {
        fzgjTaskLimitationPtDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjTaskLimitationPtDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjTaskLimitationPtDao.deleteById(id);
    }

}