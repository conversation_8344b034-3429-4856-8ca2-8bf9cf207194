package com.eci.project.omsOrderFwxmCrkZzfw.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.omsOrderFwxmCrkZzfw.entity.OmsOrderFwxmCrkZzfwEntity;


/**
* 委托内容-仓储-增值服务明细Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-05-21
*/
public interface OmsOrderFwxmCrkZzfwDao extends EciBaseDao<OmsOrderFwxmCrkZzfwEntity> {

}