package com.eci.project.etmsBdDriver.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.eci.project.etmsBdDriver.entity.DriverInfo;
import com.eci.project.etmsBdTruck.entity.EtmsBdTruckDTO;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.etmsBdDriver.entity.EtmsBdDriverEntity;

import java.util.List;


/**
* 司机基础信息Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-04-14
*/
public interface EtmsBdDriverDao extends EciBaseDao<EtmsBdDriverEntity> {
    List<DriverInfo> queryPages(@Param(Constants.WRAPPER) Wrapper queryWrapper);
}