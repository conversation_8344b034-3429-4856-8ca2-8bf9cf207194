package com.eci.project.etmsOpAttemperCargo.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.etmsOpAttemperCargo.entity.EtmsOpAttemperCargoEntity;

import org.springframework.stereotype.Service;


/**
* 托运货物信息Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-06-13
*/
@Service
public class EtmsOpAttemperCargoVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(EtmsOpAttemperCargoEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(EtmsOpAttemperCargoEntity entity, BusinessType businessType) {

    }

}
