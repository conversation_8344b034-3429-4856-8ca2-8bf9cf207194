<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.omsOrderFwxmZhys.dao.OmsOrderFwxmZhysDao">
    <resultMap type="OmsOrderFwxmZhysEntity" id="OmsOrderFwxmZhysResult">
        <result property="zhysNo" column="ZHYS_NO"/>
        <result property="orderNo" column="ORDER_NO"/>
        <result property="fwxmCode" column="FWXM_CODE"/>
        <result property="isMydl" column="IS_MYDL"/>
        <result property="isZjdb" column="IS_ZJDB"/>
        <result property="isBxfw" column="IS_BXFW"/>
        <result property="qhc" column="QHC"/>
        <result property="crossLine" column="CROSS_LINE"/>
        <result property="crossItem" column="CROSS_ITEM"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
    </resultMap>

    <sql id="selectOmsOrderFwxmZhysEntityVo">
        select
            ZHYS_NO,
            ORDER_NO,
            FWXM_CODE,
            IS_MYDL,
            IS_ZJDB,
            IS_BXFW,
            QHC,
            CROSS_LINE,
            CROSS_ITEM,
            CREATE_USER,
            CREATE_USER_NAME,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_USER_NAME,
            UPDATE_DATE,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME
        from OMS_ORDER_FWXM_ZHYS
    </sql>
</mapper>