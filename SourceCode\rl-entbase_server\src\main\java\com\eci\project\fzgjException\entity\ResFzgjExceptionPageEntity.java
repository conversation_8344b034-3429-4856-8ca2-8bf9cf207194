package com.eci.project.fzgjException.entity;

/**
 * @ClassName: ResFzgjExceptionPageEntity
 * @Author: guangyan.mei
 * @Date: 2025/4/25 16:04
 * @Description: TODO
 */
public class ResFzgjExceptionPageEntity extends FzgjExceptionEntity {

    public String exceptionTypeName;
    public String fwlxCodeName;
    public String billCodeName;
    public String zrfName;
    public String statusName;
    public String linkCodeName;
    public String consigneeCodeName;

    public String getExceptionTypeName() {
        return exceptionTypeName;
    }

    public void setExceptionTypeName(String exceptionTypeName) {
        this.exceptionTypeName = exceptionTypeName;
    }

    public String getFwlxCodeName() {
        return fwlxCodeName;
    }

    public void setFwlxCodeName(String fwlxCodeName) {
        this.fwlxCodeName = fwlxCodeName;
    }

    public String getBillCodeName() {
        return billCodeName;
    }

    public void setBillCodeName(String billCodeName) {
        this.billCodeName = billCodeName;
    }

    public String getZrfName() {
        return zrfName;
    }

    public void setZrfName(String zrfName) {
        this.zrfName = zrfName;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getLinkCodeName() {
        return linkCodeName;
    }

    public void setLinkCodeName(String linkCodeName) {
        this.linkCodeName = linkCodeName;
    }

    public String getConsigneeCodeName() {
        return consigneeCodeName;
    }

    public void setConsigneeCodeName(String consigneeCodeName) {
        this.consigneeCodeName = consigneeCodeName;
    }
}
