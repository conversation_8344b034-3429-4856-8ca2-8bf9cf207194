package com.eci.project.vFzgjBdArea.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.vFzgjBdArea.entity.VFzgjBdAreaEntity;


/**
* Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-05-20
*/
public interface VFzgjBdAreaDao extends EciBaseDao<VFzgjBdAreaEntity> {

}