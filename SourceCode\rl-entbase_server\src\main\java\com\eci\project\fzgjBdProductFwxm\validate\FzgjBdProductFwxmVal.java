package com.eci.project.fzgjBdProductFwxm.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjBdProductFwxm.entity.FzgjBdProductFwxmEntity;

import org.springframework.stereotype.Service;


/**
* 产品服务项目Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-01
*/
@Service
public class FzgjBdProductFwxmVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjBdProductFwxmEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjBdProductFwxmEntity entity, BusinessType businessType) {

    }

}
