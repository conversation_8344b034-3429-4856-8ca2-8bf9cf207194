<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.fzgjBdServiceItemPagesPt.dao.FzgjBdServiceItemPagesPtDao">
    <resultMap type="FzgjBdServiceItemPagesPtEntity" id="FzgjBdServiceItemPagesPtResult">
        <result property="guid" column="GUID"/>
        <result property="serviceItemCode" column="SERVICE_ITEM_CODE"/>
        <result property="code" column="CODE"/>
        <result property="name" column="NAME"/>
        <result property="seq" column="SEQ"/>
        <result property="status" column="STATUS"/>
        <result property="pageUrl" column="PAGE_URL"/>
        <result property="memo" column="MEMO"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
    </resultMap>

    <sql id="selectFzgjBdServiceItemPagesPtEntityVo">
        select
            GUID,
            SERVICE_ITEM_CODE,
            CODE,
            NAME,
            SEQ,
            STATUS,
            PAGE_URL,
            MEMO,
            CREATE_USER,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_DATE,
            CREATE_USER_NAME,
            UPDATE_USER_NAME
        from FZGJ_BD_SERVICE_ITEM_PAGES_PT
    </sql>
</mapper>