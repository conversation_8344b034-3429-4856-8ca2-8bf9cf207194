package com.eci.project.omsOrderGoodsPack.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.omsOrderGoodsPack.entity.OmsOrderGoodsPackEntity;


/**
* 货物包装表Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-06-10
*/
public interface OmsOrderGoodsPackDao extends EciBaseDao<OmsOrderGoodsPackEntity> {

}