package com.eci.project.fzgjScoreCar.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.fzgjScoreCar.entity.FzgjScoreCarEntity;


/**
* 企业评分Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-06-16
*/
public interface FzgjScoreCarDao extends EciBaseDao<FzgjScoreCarEntity> {

}