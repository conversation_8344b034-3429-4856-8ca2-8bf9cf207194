package com.eci.project.fzgjBdCountry.service.impl;

import com.eci.common.web.BllContext;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjBdCountry.dao.FzgjBdCountryDao;
import com.eci.project.fzgjBdCountry.entity.FzgjBdCountryEntity;
import com.eci.project.fzgjBdCountry.service.IFzgjBdCountryService;
import com.eci.project.fzgjBdCountry.validate.FzgjBdCountryVal;
import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 国家Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-03-14
 */
@Service
@Slf4j
public class FzgjBdCountryServiceImpl implements IFzgjBdCountryService {
    @Autowired
    private FzgjBdCountryDao fzgjBdCountryDao;

    @Autowired
    private FzgjBdCountryVal fzgjBdCountryVal;


    @Override
    public TgPageInfo queryPageList(FzgjBdCountryEntity entity) {
        EciQuery<FzgjBdCountryEntity> eciQuery = EciQuery.doQuery(entity);
        List<FzgjBdCountryEntity> entities = fzgjBdCountryDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjBdCountryEntity save(FzgjBdCountryEntity entity) {
        // 返回实体对象
        FzgjBdCountryEntity fzgjBdCountryEntity = null;
        fzgjBdCountryVal.saveValidate(entity, BllContext.getBusinessType());

        // 赋值guid
        entity.setGuid(entity.getCode());

        // 获取当前时间
        Date nowDate = new Date();

        // 设置基础数据
        entity.setUpdateUser(UserContext.getUserId());
        entity.setUpdateDate(nowDate);
        entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            // 设置基础数据
            entity.setCreateUser(UserContext.getUserId());
            entity.setCreateDate(nowDate);
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());

            fzgjBdCountryEntity = fzgjBdCountryDao.insertOne(entity);
        } else {
            fzgjBdCountryEntity = fzgjBdCountryDao.updateByEntityId(entity);

        }
        return fzgjBdCountryEntity;
    }

    @Override
    public List<FzgjBdCountryEntity> selectList(FzgjBdCountryEntity entity) {
        return fzgjBdCountryDao.selectList(entity);
    }

    @Override
    public FzgjBdCountryEntity selectOneById(Serializable id) {
        return fzgjBdCountryDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjBdCountryEntity> list) {
        fzgjBdCountryDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjBdCountryDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjBdCountryDao.deleteById(id);
    }

}