package com.eci.project.fzgjTaskLimitation.controller;

import com.alibaba.fastjson.JSONArray;
import com.eci.common.ZsrJson;
import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.project.fzgjTaskLimitation.service.IFzgjTaskLimitationService;
import com.eci.project.fzgjTaskLimitation.entity.FzgjTaskLimitationEntity;
import com.eci.sso.role.entity.UserContext;
import com.google.gson.JsonObject;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.EciLog;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.ArrayList;
import java.util.List;


/**
* 作业环节及标准时效Controller
*
* @<NAME_EMAIL>
* @date 2025-03-28
*/
@Api(tags = "作业环节及标准时效")
@RestController
@RequestMapping("/fzgjTaskLimitation")
public class FzgjTaskLimitationController extends EciBaseController {

    @Autowired
    private IFzgjTaskLimitationService fzgjTaskLimitationService;

    /**
    * 根据实体插入数据库
    * @param entity
    * @return
    */
    @ApiOperation("作业环节及标准时效:保存")
    @EciLog(title = "作业环节及标准时效:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public ResponseMsg save(@RequestBody FzgjTaskLimitationEntity entity){
        return ResponseMsgUtil.success(10001,fzgjTaskLimitationService.save(entity));
    }

    /**
    * 根据条件查询一个集合
    * @param entity
    * @return
    */
    @ApiOperation("作业环节及标准时效:查询列表")
    @EciLog(title = "作业环节及标准时效:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    public ResponseMsg selectList(@RequestBody FzgjTaskLimitationEntity entity){
        return ResponseMsgUtil.success(10001,fzgjTaskLimitationService.selectList(entity));
    }

    /**
    * 分页查询
    * @param entity
    * @return
    */
    @ApiOperation("作业环节及标准时效:分页查询列表")
    @EciLog(title = "作业环节及标准时效:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    public ResponseMsg selectPageList(@RequestBody FzgjTaskLimitationEntity entity){
        return ResponseMsgUtil.success(10001,fzgjTaskLimitationService.queryPageList(entity));
    }

    /**
    * 根据ID查一条记录
    * @param entity
    * @return
    */
    @ApiOperation("作业环节及标准时效:根据ID查一条")
    @EciLog(title = "作业环节及标准时效:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    public ResponseMsg selectOneById(@RequestBody FzgjTaskLimitationEntity entity){
        return ResponseMsgUtil.success(10001,fzgjTaskLimitationService.selectOneById(entity.getGuid()));
    }


    /**
    * 根据ID删除
    * @param entity
    * @return
    */
    @ApiOperation("作业环节及标准时效:根据ID删除一条")
    @EciLog(title = "作业环节及标准时效:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    public ResponseMsg deleteById(@RequestBody FzgjTaskLimitationEntity entity){
        return ResponseMsgUtil.success(10001,fzgjTaskLimitationService.deleteById(entity.getGuid()));
    }

    /**
    * 根据IDS删除
    *
    * @param entity
    * @return
    */
    @ApiOperation("作业环节及标准时效:根据ID字符串删除多条")
    @EciLog(title = "作业环节及标准时效:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    public ResponseMsg deleteByIds(@RequestBody FzgjTaskLimitationEntity entity) {
        return ResponseMsgUtil.success(10001, fzgjTaskLimitationService.deleteByIds(entity.getIds()));
    }

    /**
     * 环节一键初始化
     *
     * @param entity
     * @return
     */
    @ApiOperation("作业环节及标准时效:环节一键初始化")
    @EciLog(title = "作业环节及标准时效:环节一键初始化", businessType = BusinessType.DELETE)
    @PostMapping("/taskLimiteInit")
    public ResponseMsg taskLimitationInit(@RequestBody FzgjTaskLimitationEntity entity) {
        return ResponseMsgUtil.success(10001, fzgjTaskLimitationService.taskLimitationInit());
    }

    /**
     * 保存作业环节勾选
     *
     * @param entity
     * @return
     */
    @ApiOperation("作业环节及标准时效:保存作业环节勾选")
    @EciLog(title = "作业环节及标准时效:保存作业环节勾选", businessType = BusinessType.INSERT)
    @PostMapping("/taskSaveSelect")
    public ResponseMsg taskLimitationSaveSelect(@RequestBody FzgjTaskLimitationEntity entity) {
        List<String> listkey = JSONArray.parseArray(entity.getGuid(), String.class);
        return ResponseMsgUtil.success(10001, fzgjTaskLimitationService.taskLimitationSaveSelect(listkey));
    }
    /**
     * 上移、下移
     *
     * @param entity
     * @return
     */
    @ApiOperation("作业环节及标准时效:上移/下移")
    @EciLog(title = "作业环节及标准时效:上移/下移", businessType = BusinessType.INSERT)
    @PostMapping("/taskSeqUpdate")
    public ResponseMsg taskLimitationSeqUpdate(@RequestBody FzgjTaskLimitationEntity entity) {
        return ResponseMsgUtil.success(10001, fzgjTaskLimitationService.taskLimitationSeqUpdate(entity));
    }

    /**
     * 加载已选节点
     *
     * @param entity
     * @return
     */
    @ApiOperation("作业环节及标准时效:加载已选节点")
    @EciLog(title = "作业环节及标准时效:加载已选节点", businessType = BusinessType.SELECT)
    @PostMapping("/getCheckedTask")
    public ResponseMsg getCheckedTask(@RequestBody FzgjTaskLimitationEntity entity) {
        return ResponseMsgUtil.success(10001, fzgjTaskLimitationService.getCheckedTask(entity));
    }
}