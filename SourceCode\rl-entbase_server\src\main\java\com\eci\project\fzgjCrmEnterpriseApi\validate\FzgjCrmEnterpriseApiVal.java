package com.eci.project.fzgjCrmEnterpriseApi.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjCrmEnterpriseApi.entity.FzgjCrmEnterpriseApiEntity;

import org.springframework.stereotype.Service;


/**
* 注册企业发送接口Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-03-13
*/
@Service
public class FzgjCrmEnterpriseApiVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjCrmEnterpriseApiEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjCrmEnterpriseApiEntity entity, BusinessType businessType) {

    }

}
