package com.eci.project.fzgjGoodsAttr.service;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjGoodsAttr.dao.FzgjGoodsAttrDao;
import com.eci.project.fzgjGoodsAttr.entity.FzgjGoodsAttrEntity;
import com.eci.project.fzgjGoodsAttr.validate.FzgjGoodsAttrVal;

import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 货物属性Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-01
*/
@Service
@Slf4j
public class FzgjGoodsAttrService implements EciBaseService<FzgjGoodsAttrEntity> {

    @Autowired
    private FzgjGoodsAttrDao fzgjGoodsAttrDao;

    @Autowired
    private FzgjGoodsAttrVal fzgjGoodsAttrVal;


    @Override
    public TgPageInfo queryPageList(FzgjGoodsAttrEntity entity) {
        entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
        EciQuery<FzgjGoodsAttrEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjGoodsAttrEntity> entities = fzgjGoodsAttrDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjGoodsAttrEntity save(FzgjGoodsAttrEntity entity) {
        // 返回实体对象
        FzgjGoodsAttrEntity fzgjGoodsAttrEntity = null;
        fzgjGoodsAttrVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setGuid(IdWorker.get32UUID());
            entity.setCreateDate(new java.util.Date());
            entity.setUpdateDate(new java.util.Date());
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
            entity.setNodeCode(UserContext.getUserInfo().getDeptCode());
            entity.setNodeName(UserContext.getUserInfo().getDeptName());
            entity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            entity.setCompanyName(UserContext.getUserInfo().getCompanyName());
            fzgjGoodsAttrEntity = fzgjGoodsAttrDao.insertOne(entity);

        }else{
            entity.setUpdateDate(new java.util.Date());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            fzgjGoodsAttrEntity = fzgjGoodsAttrDao.updateByEntityId(entity);

        }
        return fzgjGoodsAttrEntity;
    }

    @Override
    public List<FzgjGoodsAttrEntity> selectList(FzgjGoodsAttrEntity entity) {
        return fzgjGoodsAttrDao.selectList(entity);
    }

    @Override
    public FzgjGoodsAttrEntity selectOneById(Serializable id) {
        return fzgjGoodsAttrDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjGoodsAttrEntity> list) {
        fzgjGoodsAttrDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjGoodsAttrDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjGoodsAttrDao.deleteById(id);
    }

}