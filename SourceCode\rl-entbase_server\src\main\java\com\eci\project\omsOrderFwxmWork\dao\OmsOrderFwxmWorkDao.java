package com.eci.project.omsOrderFwxmWork.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.eci.project.omsOrderFwxmWork.entity.OmsOrderFwxmWorkVO;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.omsOrderFwxmWork.entity.OmsOrderFwxmWorkEntity;
import org.apache.ibatis.annotations.Select;

import java.util.List;


/**
 * 供方协作任务Dao层
 * 接口层, 直接查询数据库使用
 *
 * @<NAME_EMAIL>
 * @date 2025-04-21
 */
public interface OmsOrderFwxmWorkDao extends EciBaseDao<OmsOrderFwxmWorkEntity> {

    /**
     * 协作方案
     *
     * @param queryWrapper
     * @return
     */
    @Select("select * from (SELECT \n" +
            " A.WORK_NO,\n" +
            " A.ORDER_NO,\n" +
            " A.PRE_NO,\n" +
            " A.FWXM_CODE,\n" +
            " A.TASK_SEQ,\n" +
            " A.TASK_PRE,\n" +
            " A.IS_WB,\n" +
            " A.GYS_CODE,\n" +
            " A.NODE_CODE_NB,\n" +
            " A.QUOTE_CODE,\n" +
            " A.STATUS,\n" +
            " A.STAGE,\n" +
            " A.SYS_CODE,\n" +
            " A.BILL_CODE,\n" +
            " A.DOC_NO,\n" +
            " A.RESPONSE_CODE,\n" +
            " A.SEND_DATE,\n" +
            " A.CREATE_USER,\n" +
            " A.CREATE_USER_NAME,\n" +
            " A.CREATE_DATE,\n" +
            " A.UPDATE_USER,\n" +
            " A.UPDATE_USER_NAME,\n" +
            " A.UPDATE_DATE,\n" +
            " A.COMPANY_CODE,\n" +
            " A.COMPANY_NAME,\n" +
            " A.NODE_CODE,\n" +
            " A.NODE_NAME,\n" +
            " A.GROUP_CODE,\n" +
            " A.GROUP_NAME,\n" +
            " A.GUID,\n" +
            " CASE\n" +
            "   WHEN A.OP_COMPLETE_OK = 'Y' THEN\n" +
            "    '√'\n" +
            "   ELSE\n" +
            "    NULL\n" +
            " END AS OP_COMPLETE_OK, --作业完成\n" +
            " CASE\n" +
            "   WHEN A.DATA_OK = 'Y' THEN\n" +
            "    '√'\n" +
            "   ELSE\n" +
            "    NULL\n" +
            " END AS DATA_OK, --作业数据齐全\n" +
            " CASE\n" +
            "   WHEN A.ARAP_OK = 'Y' THEN\n" +
            "    '√'\n" +
            "   ELSE\n" +
            "    NULL\n" +
            " END AS ARAP_OK, --结算完成\n" +
            " CASE\n" +
            "   WHEN A.STATUS = 'TH' THEN\n" +
            "    'Y'\n" +
            "   ELSE\n" +
            "    'N'\n" +
            " END SHOWSTATUS,\n" +
            " A.PRE_WORK_NO,\n" +
            " A.UDF2,\n" +
            " CASE A.IS_AP\n" +
            "   WHEN 'Y' THEN\n" +
            "    '是'\n" +
            "   WHEN 'N' THEN\n" +
            "    '否'\n" +
            "   ELSE\n" +
            "    NULL\n" +
            " END AS IS_AP,\n" +
            " CASE (SELECT 1\n" +
            "     FROM FZGJ_BD_PRODUCT_FWXM_FF F\n" +
            "    WHERE F.FWXM_CODE = A.FWXM_CODE\n" +
            "      AND F.GROUP_CODE = A.GROUP_CODE\n" +
            "      AND F.PRODUCT_CODE = O.PRODUCT_CODE)\n" +
            "   WHEN 1 THEN\n" +
            "    'N'\n" +
            "   ELSE\n" +
            "    'Y'\n" +
            " END AS FBPF,\n" +
            " (SELECT MAX(P.AUDIT_DATE)\n" +
            "    FROM OMS_ORDER_PRE P, OMS_ORDER OS\n" +
            "   WHERE OS.PRE_ORDER_NO = O.ORDER_NO\n" +
            "     AND P.PRE_NO = OS.PRE_NO) AS AUDIT_DATE \n" +
            "  FROM OMS_ORDER_FWXM_WORK A\n" +
            "  LEFT JOIN OMS_ORDER O\n" +
            "    ON A.ORDER_NO = O.ORDER_NO\n" +
            " )A ${ew.customSqlSegment}")
    List<OmsOrderFwxmWorkVO> selectOmsOrderFwxmWorkList(@Param(Constants.WRAPPER) Wrapper queryWrapper);
}