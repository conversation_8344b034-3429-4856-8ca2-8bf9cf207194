package com.eci.project.omsOrderFwxmWork.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.project.omsOrderFwxmWork.entity.ReqOrderFwxmWorkEntity;
import com.eci.project.omsOrderFwxmWork.service.OmsOrderFwxmWorkService;
import com.eci.project.omsOrderFwxmWork.entity.OmsOrderFwxmWorkEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.EciLog;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


/**
* 供方协作任务Controller
*
* @<NAME_EMAIL>
* @date 2025-04-21
*/
@Api(tags = "供方协作任务")
@RestController
@RequestMapping("/omsOrderFwxmWork")
public class OmsOrderFwxmWorkController extends EciBaseController {

    @Autowired
    private OmsOrderFwxmWorkService omsOrderFwxmWorkService;


    @ApiOperation("供方协作任务:保存")
    @EciLog(title = "供方协作任务:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    //@EciAction() //框架core,sso,component 需升级到2.2.5
    public ResponseMsg save(@RequestBody OmsOrderFwxmWorkEntity entity){
        return ResponseMsgUtil.success(10001,omsOrderFwxmWorkService.save(entity));
    }


    @ApiOperation("供方协作任务:查询列表")
    @EciLog(title = "供方协作任务:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    //@EciAction() //框架core,sso,component 需升级到2.2.5
    public ResponseMsg selectList(@RequestBody OmsOrderFwxmWorkEntity entity){
        return ResponseMsgUtil.success(10001,omsOrderFwxmWorkService.selectList(entity));
    }


    @ApiOperation("供方协作任务:分页查询列表")
    @EciLog(title = "供方协作任务:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    //@EciAction() //框架core,sso,component 需升级到2.2.5
    public ResponseMsg selectPageList(@RequestBody OmsOrderFwxmWorkEntity entity){
        return ResponseMsgUtil.success(10001,omsOrderFwxmWorkService.queryPageList(entity));
    }


    @ApiOperation("供方协作任务:根据ID查一条")
    @EciLog(title = "供方协作任务:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    //@EciAction() //框架core,sso,component 需升级到2.2.5
    public ResponseMsg selectOneById(@RequestBody OmsOrderFwxmWorkEntity entity){
        return ResponseMsgUtil.success(10001,omsOrderFwxmWorkService.selectOneById(entity.getGuid()));
    }


    @ApiOperation("供方协作任务:根据ID删除一条")
    @EciLog(title = "供方协作任务:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    //@EciAction() //框架core,sso,component 需升级到2.2.5
    public ResponseMsg deleteById(@RequestBody OmsOrderFwxmWorkEntity entity){
        return ResponseMsgUtil.success(10001,omsOrderFwxmWorkService.deleteById(entity.getGuid()));
    }


    @ApiOperation("供方协作任务:根据ID字符串删除多条")
    @EciLog(title = "供方协作任务:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    //@EciAction() //框架core,sso,component 需升级到2.2.5
    public ResponseMsg deleteByIds(@RequestBody OmsOrderFwxmWorkEntity entity) {
        return ResponseMsgUtil.success(10001, omsOrderFwxmWorkService.deleteByIds(entity.getIds()));
    }

    @ApiOperation("供方协作任务:查询列表")
    @EciLog(title = "供方协作任务:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectFwxmWorkByOrderNoList")
    public ResponseMsg selectFwxmWorkByOrderNoList(@RequestBody OmsOrderFwxmWorkEntity entity){
        return ResponseMsgUtil.success(10001,omsOrderFwxmWorkService.searchByEntity(entity));
    }

    @ApiOperation("批量作业完成:保存")
    @EciLog(title = "供方协作任务:新增", businessType = BusinessType.INSERT)
    @PostMapping("/batchTaskSave")
    //@EciAction() //框架core,sso,component 需升级到2.2.5
    public ResponseMsg batchTaskSave(@RequestBody ReqOrderFwxmWorkEntity entity){
        return ResponseMsgUtil.success(10001,omsOrderFwxmWorkService.batchTaskSave(entity));
    }


    @ApiOperation("供方协作任务:判断是否作业齐全|作业完成标识")
    @EciLog(title = "供方协作任务:判断是否作业齐全|作业完成标识", businessType = BusinessType.SELECT)
    @PostMapping("/selectOrderWork")
    public ResponseMsg selectOrderWork(@RequestBody OmsOrderFwxmWorkEntity entity){
        return ResponseMsgUtil.success(10001,omsOrderFwxmWorkService.selectOrderWork(entity));
    }

}