<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.fzgjException.dao.FzgjExceptionDao">
    <resultMap type="FzgjExceptionEntity" id="FzgjExceptionResult">
        <result property="guid" column="GUID"/>
        <result property="goodsGuid" column="GOODS_GUID"/>
        <result property="targetSystemCode" column="TARGET_SYSTEM_CODE"/>
        <result property="billCode" column="BILL_CODE"/>
        <result property="docNo" column="DOC_NO"/>
        <result property="fwlxCode" column="FWLX_CODE"/>
        <result property="linkCode" column="LINK_CODE"/>
        <result property="exceptionType" column="EXCEPTION_TYPE"/>
        <result property="exceptionMemo" column="EXCEPTION_MEMO"/>
        <result property="zrf" column="ZRF"/>
        <result property="dealFa" column="DEAL_FA"/>
        <result property="dealMemo" column="DEAL_MEMO"/>
        <result property="memo" column="MEMO"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="status" column="STATUS"/>
        <result property="exceptionNo" column="EXCEPTION_NO"/>
        <result property="exceptionDate" column="EXCEPTION_DATE"/>
        <result property="orderNo" column="ORDER_NO"/>
        <result property="consigneeCode" column="CONSIGNEE_CODE"/>
        <result property="opType" column="OP_TYPE"/>
        <result property="productCode" column="PRODUCT_CODE"/>
        <result property="fileUrl" column="FILE_URL"/>
        <result property="fwxmCode" column="FWXM_CODE"/>
        <result property="xzwtNo" column="XZWT_NO"/>
        <result property="workNo" column="WORK_NO"/>
        <result property="bizRegId" column="BIZ_REG_ID"/>
        <result property="exceptionMemoComm" column="EXCEPTION_MEMO_COMM"/>
        <result property="docNoWork" column="DOC_NO_WORK"/>
        <result property="billCodeWork" column="BILL_CODE_WORK"/>
        <result property="docNoJd" column="DOC_NO_JD"/>
        <result property="billCodeJd" column="BILL_CODE_JD"/>
    </resultMap>

    <sql id="selectFzgjExceptionEntityVo">
        select
            GUID,
            GOODS_GUID,
            TARGET_SYSTEM_CODE,
            BILL_CODE,
            DOC_NO,
            FWLX_CODE,
            LINK_CODE,
            EXCEPTION_TYPE,
            EXCEPTION_MEMO,
            ZRF,
            DEAL_FA,
            DEAL_MEMO,
            MEMO,
            CREATE_USER,
            CREATE_USER_NAME,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_USER_NAME,
            UPDATE_DATE,
            NODE_CODE,
            NODE_NAME,
            COMPANY_CODE,
            COMPANY_NAME,
            GROUP_CODE,
            GROUP_NAME,
            STATUS,
            EXCEPTION_NO,
            EXCEPTION_DATE,
            ORDER_NO,
            CONSIGNEE_CODE,
            OP_TYPE,
            PRODUCT_CODE,
            FILE_URL,
            FWXM_CODE,
            XZWT_NO,
            WORK_NO,
            BIZ_REG_ID,
            EXCEPTION_MEMO_COMM,
            DOC_NO_WORK,
            BILL_CODE_WORK,
            DOC_NO_JD,
            BILL_CODE_JD
        from FZGJ_EXCEPTION
    </sql>
</mapper>