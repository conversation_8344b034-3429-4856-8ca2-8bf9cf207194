package com.eci.project.crmCustomerHzfwGys.controller;

import com.eci.common.ZsrJson;
import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.crmCustomerHzfwGys.service.CrmCustomerHzfwGysService;
import com.eci.project.crmCustomerHzfwGys.entity.CrmCustomerHzfwGysEntity;
import com.eci.project.fzgjBdServiceItem.entity.FzgjBdServiceItemEntity;
import com.eci.sso.role.entity.UserContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
* 供应商合作服务Controller
*
* @<NAME_EMAIL>
* @date 2025-05-13
*/
@Api(tags = "供应商合作服务")
@RestController
@RequestMapping("/crmCustomerHzfwGys")
public class CrmCustomerHzfwGysController extends EciBaseController {

    @Autowired
    private CrmCustomerHzfwGysService crmCustomerHzfwGysService;


    @ApiOperation("供应商合作服务:保存")
    @EciLog(title = "供应商合作服务:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody  String jsonstring){
        ZsrJson zsrJson= ZsrJson.parse(jsonstring);
        List<CrmCustomerHzfwGysEntity> entities=new ArrayList<>();
        if(zsrJson.exists("saveData")) {
            entities=zsrJson.getList("saveData", CrmCustomerHzfwGysEntity.class);
            entities.forEach(p->{
                p.setGroupCode(UserContext.getUserInfo().getCompanyCode());
                p.setGroupName(UserContext.getUserInfo().getCompanyName());
                p.setCreateDate(new Date());
                p.setCreateUser(UserContext.getUserInfo().getTrueName());
                p.setUpdateDate(new Date());
                p.setUpdateUser(UserContext.getUserInfo().getTrueName());
            });
        }
        //删除该服务类型下的数据
        crmCustomerHzfwGysService.deleteByCustomerCodeAndFwlxCode(entities.get(0).getCustomerCode()
                ,entities.get(0).getFwlxCode());
        crmCustomerHzfwGysService.insertBatch(entities);
        return ResponseMsgUtil.success(10001,entities);
    }


    @ApiOperation("供应商合作服务:查询列表")
    @EciLog(title = "供应商合作服务:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody CrmCustomerHzfwGysEntity entity){
        List<CrmCustomerHzfwGysEntity> crmCustomerHzfwGysEntities = crmCustomerHzfwGysService.selectList(entity);
        return ResponseMsgUtil.success(10001,crmCustomerHzfwGysEntities);
    }

    @ApiOperation("供应商合作服务:查询列表")
    @EciLog(title = "供应商合作服务:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectTreeList")
    @EciAction()
    public ResponseMsg selectTreeList(@RequestBody CrmCustomerHzfwGysEntity entity){
        String serivceGuid="",comGuid="",comName="";
        if(entity.getRequestParams()!=null&&entity.getRequestParams().containsKey("serviceGuid")) {
            serivceGuid=entity.getRequestParams().get("serviceGuid").toString();
        }
        if(entity.getRequestParams()!=null&&entity.getRequestParams().containsKey("comGuid")) {
            comGuid=entity.getRequestParams().get("comGuid").toString();
        }
        if(entity.getRequestParams()!=null&&entity.getRequestParams().containsKey("comName")) {
            comName=entity.getRequestParams().get("comName").toString();
        }
        List<CrmCustomerHzfwGysEntity> crmCustomerHzfwGysEntities = crmCustomerHzfwGysService.selectTreeWhitGYS(UserContext.getUserInfo().getCompanyCode()
                , entity.getCustomerCode(), serivceGuid);
        CrmCustomerHzfwGysEntity m=new CrmCustomerHzfwGysEntity();
        m.setItemGuid(comGuid);
        m.setName(comName);
        crmCustomerHzfwGysService.Build(m,crmCustomerHzfwGysEntities);
        List<CrmCustomerHzfwGysEntity> result=new ArrayList<>();
        result.add(m);
        return ResponseMsgUtil.success(10001,result );
    }

    @ApiOperation("供应商合作服务:分页查询列表")
    @EciLog(title = "供应商合作服务:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody CrmCustomerHzfwGysEntity entity){
        TgPageInfo tgPageInfo = crmCustomerHzfwGysService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("供应商合作服务:根据ID查一条")
    @EciLog(title = "供应商合作服务:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody CrmCustomerHzfwGysEntity entity){
        CrmCustomerHzfwGysEntity  crmCustomerHzfwGysEntity = crmCustomerHzfwGysService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,crmCustomerHzfwGysEntity);
    }


    @ApiOperation("供应商合作服务:根据ID删除一条")
    @EciLog(title = "供应商合作服务:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody CrmCustomerHzfwGysEntity entity){
        int count = crmCustomerHzfwGysService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("供应商合作服务:根据ID字符串删除多条")
    @EciLog(title = "供应商合作服务:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody CrmCustomerHzfwGysEntity entity) {
        int count = crmCustomerHzfwGysService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}