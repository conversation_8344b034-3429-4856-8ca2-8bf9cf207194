package com.eci.project.fzgjBdTruckSpecCom.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;

import java.math.BigDecimal;
import java.util.Date;


/**
* 计费车辆尺寸对象 FZGJ_BD_TRUCK_SPEC_COM
*
* @<NAME_EMAIL>
* @date 2025-03-25
*/
@FieldNameConstants
public class FzgjBdTruckSpecComBaseEntity extends EciBaseEntity
{
	/**
	* GUID
	*/
	@ApiModelProperty("GUID(50)")
	@TableId("GUID")
	private String guid;

	/**
	* 代码
	*/
	@ApiModelProperty("代码(50)")
	@TableField("CODE")
	private String code;

	/**
	* 名称
	*/
	@ApiModelProperty("名称(50)")
	@TableField("NAME")
	private String name;

	/**
	* 关联计费车辆类型
	*/
	@ApiModelProperty("关联计费车辆类型(50)")
	@TableField("TRUNC_TYPE")
	private String truncType;

	/**
	* 备注
	*/
	@ApiModelProperty("备注(50)")
	@TableField("MEMO")
	private String memo;

	/**
	* 状态
	*/
	@ApiModelProperty("状态(1)")
	@TableField("STATUS")
	private String status;

	/**
	* CREATE_USER
	*/
	@ApiModelProperty("CREATE_USER(200)")
	@TableField("CREATE_USER_NAME")
	private String createUserName;

	/**
	* 创建人
	*/
	@ApiModelProperty("创建人(50)")
	@TableField("CREATE_USER")
	private String createUser;

	/**
	* 创建日期
	*/
	@ApiModelProperty("创建日期(7)")
	@TableField("CREATE_DATE")
	private Date createDate;

	@ApiModelProperty("创建日期开始")
	@TableField(exist=false)
	private Date createDateStart;

	@ApiModelProperty("创建日期结束")
	@TableField(exist=false)
	private Date createDateEnd;

	/**
	* 修改人
	*/
	@ApiModelProperty("修改人(200)")
	@TableField("UPDATE_USER_NAME")
	private String updateUserName;

	/**
	* 修改人
	*/
	@ApiModelProperty("修改人(50)")
	@TableField("UPDATE_USER")
	private String updateUser;

	/**
	* 修改日期
	*/
	@ApiModelProperty("修改日期(7)")
	@TableField("UPDATE_DATE")
	private Date updateDate;

	@ApiModelProperty("修改日期开始")
	@TableField(exist=false)
	private Date updateDateStart;

	@ApiModelProperty("修改日期结束")
	@TableField(exist=false)
	private Date updateDateEnd;

	/**
	* 归属公司
	*/
	@ApiModelProperty("归属公司(50)")
	@TableField("COMPANY_CODE")
	private String companyCode;

	/**
	* 归属公司
	*/
	@ApiModelProperty("归属公司(200)")
	@TableField("COMPANY_NAME")
	private String companyName;

	/**
	* 归属组织
	*/
	@ApiModelProperty("归属组织(50)")
	@TableField("NODE_CODE")
	private String nodeCode;

	/**
	* 归属组织
	*/
	@ApiModelProperty("归属组织(200)")
	@TableField("NODE_NAME")
	private String nodeName;

	/**
	* 归属集团
	*/
	@ApiModelProperty("归属集团(50)")
	@TableField("GROUP_CODE")
	private String groupCode;

	/**
	* 归属集团
	*/
	@ApiModelProperty("归属集团(200)")
	@TableField("GROUP_NAME")
	private String groupName;

	/**
	* 限重(T)
	*/
	@ApiModelProperty("限重(T)(22)")
	@TableField("XZT")
	private Integer xzt;

	/**
	* 标箱
	*/
	@ApiModelProperty("标箱(100)")
	@TableField("BX")
	private String bx;

	/**
	* 尺寸
	*/
	@ApiModelProperty("尺寸(22)")
	@TableField("CAR_LONG")
	private BigDecimal carLong;

	/**
	* 尺寸类型
	*/
	@ApiModelProperty("尺寸类型(10)")
	@TableField("CAR_LONG_TYPE")
	private String carLongType;

	@JsonIgnore
	@TableField(exist = false)
	private Class<?> clazz = this.getClass();

	public Class<?> getClazz() {
		return clazz;
	}

	public void setClazz(Class<?> clazz) {
		this.clazz = clazz;
	}

	public FzgjBdTruckSpecComBaseEntity setGuid(String guid) {
		this.guid = guid;
		return this;
	}

	public String getGuid() {
		return guid;
	}

	public FzgjBdTruckSpecComBaseEntity setCode(String code) {
		this.code = code;
		return this;
	}

	public String getCode() {
		return code;
	}

	public FzgjBdTruckSpecComBaseEntity setName(String name) {
		this.name = name;
		return this;
	}

	public String getName() {
		return name;
	}

	public FzgjBdTruckSpecComBaseEntity setTruncType(String truncType) {
		this.truncType = truncType;
		return this;
	}

	public String getTruncType() {
		return truncType;
	}

	public FzgjBdTruckSpecComBaseEntity setMemo(String memo) {
		this.memo = memo;
		return this;
	}

	public String getMemo() {
		return memo;
	}

	public FzgjBdTruckSpecComBaseEntity setStatus(String status) {
		this.status = status;
		return this;
	}

	public String getStatus() {
		return status;
	}

	public FzgjBdTruckSpecComBaseEntity setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
		return this;
	}

	public String getCreateUserName() {
		return createUserName;
	}

	public FzgjBdTruckSpecComBaseEntity setCreateUser(String createUser) {
		this.createUser = createUser;
		return this;
	}

	public String getCreateUser() {
		return createUser;
	}

	public FzgjBdTruckSpecComBaseEntity setCreateDate(Date createDate) {
		this.createDate = createDate;
		return this;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public FzgjBdTruckSpecComBaseEntity setCreateDateStart(Date createDateStart) {
		this.createDateStart = createDateStart;
		return this;
	}

	public Date getCreateDateStart() {
		return createDateStart;
	}

	public FzgjBdTruckSpecComBaseEntity setCreateDateEnd(Date createDateEnd) {
		this.createDateEnd = createDateEnd;
		return this;
	}

	public Date getCreateDateEnd() {
		return createDateEnd;
	}
	public FzgjBdTruckSpecComBaseEntity setUpdateUserName(String updateUserName) {
		this.updateUserName = updateUserName;
		return this;
	}

	public String getUpdateUserName() {
		return updateUserName;
	}

	public FzgjBdTruckSpecComBaseEntity setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
		return this;
	}

	public String getUpdateUser() {
		return updateUser;
	}

	public FzgjBdTruckSpecComBaseEntity setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
		return this;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public FzgjBdTruckSpecComBaseEntity setUpdateDateStart(Date updateDateStart) {
		this.updateDateStart = updateDateStart;
		return this;
	}

	public Date getUpdateDateStart() {
		return updateDateStart;
	}

	public FzgjBdTruckSpecComBaseEntity setUpdateDateEnd(Date updateDateEnd) {
		this.updateDateEnd = updateDateEnd;
		return this;
	}

	public Date getUpdateDateEnd() {
		return updateDateEnd;
	}
	public FzgjBdTruckSpecComBaseEntity setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
		return this;
	}

	public String getCompanyCode() {
		return companyCode;
	}

	public FzgjBdTruckSpecComBaseEntity setCompanyName(String companyName) {
		this.companyName = companyName;
		return this;
	}

	public String getCompanyName() {
		return companyName;
	}

	public FzgjBdTruckSpecComBaseEntity setNodeCode(String nodeCode) {
		this.nodeCode = nodeCode;
		return this;
	}

	public String getNodeCode() {
		return nodeCode;
	}

	public FzgjBdTruckSpecComBaseEntity setNodeName(String nodeName) {
		this.nodeName = nodeName;
		return this;
	}

	public String getNodeName() {
		return nodeName;
	}

	public FzgjBdTruckSpecComBaseEntity setGroupCode(String groupCode) {
		this.groupCode = groupCode;
		return this;
	}

	public String getGroupCode() {
		return groupCode;
	}

	public FzgjBdTruckSpecComBaseEntity setGroupName(String groupName) {
		this.groupName = groupName;
		return this;
	}

	public String getGroupName() {
		return groupName;
	}

	public FzgjBdTruckSpecComBaseEntity setXzt(Integer xzt) {
		this.xzt = xzt;
		return this;
	}

	public Integer getXzt() {
		return xzt;
	}

	public FzgjBdTruckSpecComBaseEntity setBx(String bx) {
		this.bx = bx;
		return this;
	}

	public String getBx() {
		return bx;
	}

	public FzgjBdTruckSpecComBaseEntity setCarLong(BigDecimal carLong) {
		this.carLong = carLong;
		return this;
	}

	public BigDecimal getCarLong() {
		return carLong;
	}

	public FzgjBdTruckSpecComBaseEntity setCarLongType(String carLongType) {
		this.carLongType = carLongType;
		return this;
	}

	public String getCarLongType() {
		return carLongType;
	}

}
