package com.eci.project.fzgjScoreCar.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjScoreCar.dao.FzgjScoreCarDao;
import com.eci.project.fzgjScoreCar.entity.FzgjScoreCarEntity;
import com.eci.project.fzgjScoreCar.validate.FzgjScoreCarVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 企业评分Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-06-16
*/
@Service
@Slf4j
public class FzgjScoreCarService implements EciBaseService<FzgjScoreCarEntity> {

    @Autowired
    private FzgjScoreCarDao fzgjScoreCarDao;

    @Autowired
    private FzgjScoreCarVal fzgjScoreCarVal;


    @Override
    public TgPageInfo queryPageList(FzgjScoreCarEntity entity) {
        EciQuery<FzgjScoreCarEntity> eciQuery = EciQuery.buildQuery(entity);
        if(entity.getScore()!=null)
            eciQuery.apply(" A.score>={0}",entity.getScore());
        if(entity.getScore1()!=null)
            eciQuery.apply(" A.score<={0}",entity.getScore1());
        List<FzgjScoreCarEntity> entities = fzgjScoreCarDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjScoreCarEntity save(FzgjScoreCarEntity entity) {
        // 返回实体对象
        FzgjScoreCarEntity fzgjScoreCarEntity = null;
        fzgjScoreCarVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjScoreCarEntity = fzgjScoreCarDao.insertOne(entity);

        }else{

            fzgjScoreCarEntity = fzgjScoreCarDao.updateByEntityId(entity);

        }
        return fzgjScoreCarEntity;
    }

    @Override
    public List<FzgjScoreCarEntity> selectList(FzgjScoreCarEntity entity) {
        return fzgjScoreCarDao.selectList(entity);
    }

    @Override
    public FzgjScoreCarEntity selectOneById(Serializable id) {
        return fzgjScoreCarDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjScoreCarEntity> list) {
        fzgjScoreCarDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjScoreCarDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjScoreCarDao.deleteById(id);
    }

}