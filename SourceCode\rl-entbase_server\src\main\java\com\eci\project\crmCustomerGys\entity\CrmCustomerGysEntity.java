package com.eci.project.crmCustomerGys.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 业务伙伴-供应商对象 CRM_CUSTOMER_GYS
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-04-29
*/
@ApiModel("业务伙伴-供应商")
@TableName("CRM_CUSTOMER_GYS")
@FieldNameConstants
public class CrmCustomerGysEntity extends EciBaseEntity{
    /**
    * GUID
    */
    @ApiModelProperty("GUID(36)")
    @TableId("GUID")
    private String guid;

    /**
    * 业务伙伴代码
    */
    @ApiModelProperty("业务伙伴代码(36)")
    @TableField("CUSTOMER_CODE")
    private String customerCode;

    /**
    * 付款冻结标志(Y/N)
    */
    @ApiModelProperty("付款冻结标志(Y/N)(1)")
    @TableField("PAY_FREEZE")
    private String payFreeze;

    /**
    * 付款期限(天)
    */
    @ApiModelProperty("付款期限(天)(22)")
    @TableField("PAY_DATELINE")
    private Integer payDateline;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 最后修改日期
    */
    @ApiModelProperty("最后修改日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("最后修改日期开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("最后修改日期结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(200)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 供应商类型，铁路TL，航空HK，船C，其他QT
    */
    @ApiModelProperty("供应商类型，铁路TL，航空HK，船C，其他QT(50)")
    @TableField("GYS_TYPE")
    private String gysType;

    /**
    * 供应商类型，铁路TL，航空HK，船C，其他QT
    */
    @ApiModelProperty("供应商类型，铁路TL，航空HK，船C，其他QT(200)")
    @TableField("GYS_TYPE_NAME")
    private String gysTypeName;

    /**
    * 合作开始日期
    */
    @ApiModelProperty("合作开始日期(7)")
    @TableField("BEGIN_DATE")
    private Date beginDate;

    @ApiModelProperty("合作开始日期开始")
    @TableField(exist=false)
    private Date beginDateStart;

    @ApiModelProperty("合作开始日期结束")
    @TableField(exist=false)
    private Date beginDateEnd;

    /**
    * 合作结束日期
    */
    @ApiModelProperty("合作结束日期(7)")
    @TableField("END_DATE")
    private Date endDate;

    @ApiModelProperty("合作结束日期开始")
    @TableField(exist=false)
    private Date endDateStart;

    @ApiModelProperty("合作结束日期结束")
    @TableField(exist=false)
    private Date endDateEnd;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public CrmCustomerGysEntity() {
        this.setSubClazz(CrmCustomerGysEntity.class);
    }

    public CrmCustomerGysEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public CrmCustomerGysEntity setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
        this.nodifySetFiled("customerCode", customerCode);
        return this;
    }

    public String getCustomerCode() {
        this.nodifyGetFiled("customerCode");
        return customerCode;
    }

    public CrmCustomerGysEntity setPayFreeze(String payFreeze) {
        this.payFreeze = payFreeze;
        this.nodifySetFiled("payFreeze", payFreeze);
        return this;
    }

    public String getPayFreeze() {
        this.nodifyGetFiled("payFreeze");
        return payFreeze;
    }

    public CrmCustomerGysEntity setPayDateline(Integer payDateline) {
        this.payDateline = payDateline;
        this.nodifySetFiled("payDateline", payDateline);
        return this;
    }

    public Integer getPayDateline() {
        this.nodifyGetFiled("payDateline");
        return payDateline;
    }

    public CrmCustomerGysEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public CrmCustomerGysEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public CrmCustomerGysEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public CrmCustomerGysEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public CrmCustomerGysEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public CrmCustomerGysEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public CrmCustomerGysEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public CrmCustomerGysEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public CrmCustomerGysEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public CrmCustomerGysEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public CrmCustomerGysEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public CrmCustomerGysEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public CrmCustomerGysEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public CrmCustomerGysEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public CrmCustomerGysEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public CrmCustomerGysEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public CrmCustomerGysEntity setGysType(String gysType) {
        this.gysType = gysType;
        this.nodifySetFiled("gysType", gysType);
        return this;
    }

    public String getGysType() {
        this.nodifyGetFiled("gysType");
        return gysType;
    }

    public CrmCustomerGysEntity setGysTypeName(String gysTypeName) {
        this.gysTypeName = gysTypeName;
        this.nodifySetFiled("gysTypeName", gysTypeName);
        return this;
    }

    public String getGysTypeName() {
        this.nodifyGetFiled("gysTypeName");
        return gysTypeName;
    }

    public CrmCustomerGysEntity setBeginDate(Date beginDate) {
        this.beginDate = beginDate;
        this.nodifySetFiled("beginDate", beginDate);
        return this;
    }

    public Date getBeginDate() {
        this.nodifyGetFiled("beginDate");
        return beginDate;
    }

    public CrmCustomerGysEntity setBeginDateStart(Date beginDateStart) {
        this.beginDateStart = beginDateStart;
        this.nodifySetFiled("beginDateStart", beginDateStart);
        return this;
    }

    public Date getBeginDateStart() {
        this.nodifyGetFiled("beginDateStart");
        return beginDateStart;
    }

    public CrmCustomerGysEntity setBeginDateEnd(Date beginDateEnd) {
        this.beginDateEnd = beginDateEnd;
        this.nodifySetFiled("beginDateEnd", beginDateEnd);
        return this;
    }

    public Date getBeginDateEnd() {
        this.nodifyGetFiled("beginDateEnd");
        return beginDateEnd;
    }
    public CrmCustomerGysEntity setEndDate(Date endDate) {
        this.endDate = endDate;
        this.nodifySetFiled("endDate", endDate);
        return this;
    }

    public Date getEndDate() {
        this.nodifyGetFiled("endDate");
        return endDate;
    }

    public CrmCustomerGysEntity setEndDateStart(Date endDateStart) {
        this.endDateStart = endDateStart;
        this.nodifySetFiled("endDateStart", endDateStart);
        return this;
    }

    public Date getEndDateStart() {
        this.nodifyGetFiled("endDateStart");
        return endDateStart;
    }

    public CrmCustomerGysEntity setEndDateEnd(Date endDateEnd) {
        this.endDateEnd = endDateEnd;
        this.nodifySetFiled("endDateEnd", endDateEnd);
        return this;
    }

    public Date getEndDateEnd() {
        this.nodifyGetFiled("endDateEnd");
        return endDateEnd;
    }
}
