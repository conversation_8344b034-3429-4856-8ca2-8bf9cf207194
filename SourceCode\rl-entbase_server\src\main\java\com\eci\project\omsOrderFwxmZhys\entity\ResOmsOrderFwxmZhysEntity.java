package com.eci.project.omsOrderFwxmZhys.entity;

import com.eci.common.ZsrBaseEntity;
import com.eci.project.omsOrderFwxmZhysXl.entity.OmsOrderFwxmZhysXlEntity;

import java.util.List;

/**
 * @ClassName: ResOmsOrderFwxmZhysEntity
 * @Author: guangyan.mei
 * @Date: 2025/6/13 17:06
 * @Description: TODO
 */
public class ResOmsOrderFwxmZhysEntity extends ZsrBaseEntity {

    public OmsOrderFwxmZhysEntity entity;
    public List<OmsOrderFwxmZhysXlEntity> list;

    public OmsOrderFwxmZhysEntity getEntity() {
        return entity;
    }

    public void setEntity(OmsOrderFwxmZhysEntity entity) {
        this.entity = entity;
    }

    public List<OmsOrderFwxmZhysXlEntity> getList() {
        return list;
    }

    public void setList(List<OmsOrderFwxmZhysXlEntity> list) {
        this.list = list;
    }
}
