package com.eci.project.etmsCrmEnterprise.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.etmsCrmEnterprise.entity.EtmsCrmEnterpriseEntity;

import org.springframework.stereotype.Service;


/**
* Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-06-13
*/
@Service
public class EtmsCrmEnterpriseVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(EtmsCrmEnterpriseEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(EtmsCrmEnterpriseEntity entity, BusinessType businessType) {

    }

}
