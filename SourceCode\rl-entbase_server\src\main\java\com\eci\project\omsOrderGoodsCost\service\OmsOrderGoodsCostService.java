package com.eci.project.omsOrderGoodsCost.service;

import com.eci.common.db.DBHelper;
import com.eci.common.util.StringUtils;
import com.eci.common.utils.CommonLib;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.omsOrderGoodsCost.dao.OmsOrderGoodsCostDao;
import com.eci.project.omsOrderGoodsCost.entity.OmsOrderGoodsCostEntity;
import com.eci.project.omsOrderGoodsCost.entity.ResOmsOrderGoodsCostEntity;
import com.eci.project.omsOrderGoodsCost.validate.OmsOrderGoodsCostVal;
import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * 货值币制表Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-06-10
 */
@Service
@Slf4j
public class OmsOrderGoodsCostService implements EciBaseService<OmsOrderGoodsCostEntity> {

    CommonLib cmn = CommonLib.getInstance();

    @Autowired
    private OmsOrderGoodsCostDao omsOrderGoodsCostDao;

    @Autowired
    private OmsOrderGoodsCostVal omsOrderGoodsCostVal;


    @Override
    public TgPageInfo queryPageList(OmsOrderGoodsCostEntity entity) {
        EciQuery<OmsOrderGoodsCostEntity> eciQuery = EciQuery.buildQuery(entity);
        List<OmsOrderGoodsCostEntity> entities = omsOrderGoodsCostDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderGoodsCostEntity save(OmsOrderGoodsCostEntity entity) {
        // 返回实体对象
        OmsOrderGoodsCostEntity omsOrderGoodsCostEntity = null;
        omsOrderGoodsCostVal.saveValidate(entity, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            omsOrderGoodsCostEntity = omsOrderGoodsCostDao.insertOne(entity);

        } else {

            omsOrderGoodsCostEntity = omsOrderGoodsCostDao.updateByEntityId(entity);

        }
        return omsOrderGoodsCostEntity;
    }

    @Override
    public List<OmsOrderGoodsCostEntity> selectList(OmsOrderGoodsCostEntity entity) {
        return omsOrderGoodsCostDao.selectList(entity);
    }

    @Override
    public OmsOrderGoodsCostEntity selectOneById(Serializable id) {
        return omsOrderGoodsCostDao.selectById(id);
    }


    @Override
    public void insertBatch(List<OmsOrderGoodsCostEntity> list) {
        omsOrderGoodsCostDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return omsOrderGoodsCostDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return omsOrderGoodsCostDao.deleteById(id);
    }


    /**
     * 货值币制表加载
     **/
    public List<ResOmsOrderGoodsCostEntity> loadOrderGoodsCost(OmsOrderGoodsCostEntity entity) {

        String sql = "SELECT A.GUID,A.GOODS_GUID,A.ORDER_NO,A.PRE_NO,A.CURRENCY,\n" +
                "       A.COST,A.CREATE_USER,A.CREATE_USER_NAME,A.CREATE_DATE,A.UPDATE_USER\n" +
                "       ,A.UPDATE_USER_NAME,A.UPDATE_DATE,A.COMPANY_CODE,A.COMPANY_NAME,A.NODE_CODE\n" +
                "       ,A.NODE_NAME,A.GROUP_CODE,A.GROUP_NAME\n" +
                ",(SELECT CURRENCY.NAME FROM FZGJ_BD_CURRENCY CURRENCY WHERE CURRENCY.STATUS = 'Y'  AND CURRENCY.CODE = A.CURRENCY) CURRENCY_NAME\n" +
                "FROM OMS_ORDER_GOODS_COST A";
        sql += "   WHERE A.GROUP_CODE =" + cmn.SQLQ(UserContext.getUserInfo().getCompanyCode());

        if (StringUtils.hasValue(entity.getOrderNo())) {
            sql += " AND  A.ORDER_NO=" + cmn.SQLQ(entity.getOrderNo());
        }

        if (StringUtils.hasValue(entity.getPreNo())) {
            sql += " AND  A.PRE_NO=" + cmn.SQLQ(entity.getPreNo());
        }

        return DBHelper.selectList(sql, ResOmsOrderGoodsCostEntity.class);

    }
}