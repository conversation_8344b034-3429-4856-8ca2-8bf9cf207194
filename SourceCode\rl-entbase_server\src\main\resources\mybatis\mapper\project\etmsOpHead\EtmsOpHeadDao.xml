<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.etmsOpHead.dao.EtmsOpHeadDao">
    <resultMap type="EtmsOpHeadEntity" id="EtmsOpHeadResult">
        <result property="guid" column="GUID"/>
        <result property="opNo" column="OP_NO"/>
        <result property="customerCode" column="CUSTOMER_CODE"/>
        <result property="consigneeCode" column="CONSIGNEE_CODE"/>
        <result property="invNo" column="INV_NO"/>
        <result property="mbl" column="MBL"/>
        <result property="hbl" column="HBL"/>
        <result property="zgNo" column="ZG_NO"/>
        <result property="hfNo" column="HF_NO"/>
        <result property="createCompany" column="CREATE_COMPANY"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="status" column="STATUS"/>
        <result property="manageUser" column="MANAGE_USER"/>
        <result property="orgDepId" column="ORG_DEP_ID"/>
        <result property="orgDepCode" column="ORG_DEP_CODE"/>
        <result property="orgDepName" column="ORG_DEP_NAME"/>
        <result property="bgdh" column="BGDH"/>
        <result property="wmQdg" column="WM_QDG"/>
        <result property="wmMdg" column="WM_MDG"/>
        <result property="shipName" column="SHIP_NAME"/>
        <result property="wlLine" column="WL_LINE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="batchNumber" column="BATCH_NUMBER"/>
        <result property="omsOrderNo" column="OMS_ORDER_NO"/>
    </resultMap>

    <sql id="selectEtmsOpHeadEntityVo">
        select
            GUID,
            OP_NO,
            CUSTOMER_CODE,
            CONSIGNEE_CODE,
            INV_NO,
            MBL,
            HBL,
            ZG_NO,
            HF_NO,
            CREATE_COMPANY,
            CREATE_USER,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_DATE,
            STATUS,
            MANAGE_USER,
            ORG_DEP_ID,
            ORG_DEP_CODE,
            ORG_DEP_NAME,
            BGDH,
            WM_QDG,
            WM_MDG,
            SHIP_NAME,
            WL_LINE,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            CREATE_USER_NAME,
            UPDATE_USER_NAME,
            BATCH_NUMBER,
            OMS_ORDER_NO
        from ETMS_OP_HEAD
    </sql>
</mapper>