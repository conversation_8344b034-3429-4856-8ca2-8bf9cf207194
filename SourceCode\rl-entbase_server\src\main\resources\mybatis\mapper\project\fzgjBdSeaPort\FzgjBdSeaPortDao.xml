<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.fzgjBdSeaPort.dao.FzgjBdSeaPortDao">
    <resultMap type="FzgjBdSeaPortEntity" id="FzgjBdSeaPortResult">
        <result property="guid" column="GUID"/>
        <result property="code" column="CODE"/>
        <result property="name" column="NAME"/>
        <result property="portCode" column="PORT_CODE"/>
        <result property="status" column="STATUS"/>
        <result property="seq" column="SEQ"/>
        <result property="memo" column="MEMO"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="countryCode" column="COUNTRY_CODE"/>
        <result property="countryName" column="COUNTRY_NAME"/>
        <result property="enName" column="EN_NAME"/>
    </resultMap>

    <sql id="selectFzgjBdSeaPortEntityVo">
        select
            GUID,
            CODE,
            NAME,
            PORT_CODE,
            STATUS,
            SEQ,
            MEMO,
            CREATE_USER,
            CREATE_USER_NAME,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_USER_NAME,
            UPDATE_DATE,
            NODE_CODE,
            NODE_NAME,
            COMPANY_CODE,
            COMPANY_NAME,
            GROUP_CODE,
            GROUP_NAME,
            COUNTRY_CODE,
            COUNTRY_NAME,
            EN_NAME
        from FZGJ_BD_SEA_PORT
    </sql>
</mapper>