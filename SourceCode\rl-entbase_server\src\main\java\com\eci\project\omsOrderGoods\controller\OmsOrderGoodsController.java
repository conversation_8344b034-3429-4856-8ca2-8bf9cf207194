package com.eci.project.omsOrderGoods.controller;

import com.eci.common.ResponseMsgUtilX;
import com.eci.common.util.ResponseMsg;
import com.eci.common.util.ResponseMsgUtil;
import com.eci.crud.controller.EciBaseController;
import com.eci.log.annotation.EciAction;
import com.eci.log.annotation.EciLog;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.omsOrderGoods.entity.OmsOrderGoodsEntity;
import com.eci.project.omsOrderGoods.entity.ResOmsOrderGoodsEntity;
import com.eci.project.omsOrderGoods.service.OmsOrderGoodsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 货物信息表Controller
 *
 * @<NAME_EMAIL>
 * @date 2025-06-10
 */
@Api(tags = "货物信息表")
@RestController
@RequestMapping("/omsOrderGoods")
public class OmsOrderGoodsController extends EciBaseController {

    @Autowired
    private OmsOrderGoodsService omsOrderGoodsService;


    @ApiOperation("货物信息表:保存")
    @EciLog(title = "货物信息表:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody OmsOrderGoodsEntity entity) {
        OmsOrderGoodsEntity omsOrderGoodsEntity = omsOrderGoodsService.save(entity);
        return ResponseMsgUtil.success(10001, omsOrderGoodsEntity);
    }


    @ApiOperation("货物信息表:查询列表")
    @EciLog(title = "货物信息表:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody OmsOrderGoodsEntity entity) {
        List<OmsOrderGoodsEntity> omsOrderGoodsEntities = omsOrderGoodsService.selectList(entity);
        return ResponseMsgUtil.success(10001, omsOrderGoodsEntities);
    }


    @ApiOperation("货物信息表:分页查询列表")
    @EciLog(title = "货物信息表:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody OmsOrderGoodsEntity entity) {
        TgPageInfo tgPageInfo = omsOrderGoodsService.queryPageList(entity);
        return ResponseMsgUtil.success(10001, tgPageInfo);
    }


    @ApiOperation("货物信息表:根据ID查一条")
    @EciLog(title = "货物信息表:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody OmsOrderGoodsEntity entity) {
        OmsOrderGoodsEntity omsOrderGoodsEntity = omsOrderGoodsService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001, omsOrderGoodsEntity);
    }


    @ApiOperation("货物信息表:根据ID删除一条")
    @EciLog(title = "货物信息表:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody OmsOrderGoodsEntity entity) {
        int count = omsOrderGoodsService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001, count);
    }


    @ApiOperation("货物信息表:根据ID字符串删除多条")
    @EciLog(title = "货物信息表:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody OmsOrderGoodsEntity entity) {
        int count = omsOrderGoodsService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001, count);
    }

    @ApiOperation("货物信息表:货物信息加载")
    @EciLog(title = "货物信息表:货物信息加载", businessType = BusinessType.SELECT)
    @PostMapping("/loadOrderGoods")
    @EciAction()
    public ResponseMsg loadOrderGoods(@RequestBody OmsOrderGoodsEntity entity) {
        return ResponseMsgUtilX.success(10001, omsOrderGoodsService.loadOrderGoods(entity).size() > 0 ? omsOrderGoodsService.loadOrderGoods(entity).get(0) : new OmsOrderGoodsEntity());
    }

    @ApiOperation("货物信息表:保存|编辑")
    @EciLog(title = "货物信息表:保存|编辑", businessType = BusinessType.SELECT)
    @PostMapping("/saveOrderGoods")
    @EciAction()
    public ResponseMsg saveOrderGoods(@RequestBody String jsonString) {
        boolean flag = omsOrderGoodsService.saveOrderGoods(jsonString);
        return ResponseMsgUtil.success(10001, flag ? "保存成功" : "保存失败");
    }

}