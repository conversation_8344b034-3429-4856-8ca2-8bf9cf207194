package com.eci.project.fzgjFile.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjFile.dao.FzgjFileDao;
import com.eci.project.fzgjFile.entity.FzgjFileEntity;
import com.eci.project.fzgjFile.validate.FzgjFileVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 附件Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-06-04
*/
@Service
@Slf4j
public class FzgjFileService implements EciBaseService<FzgjFileEntity> {

    @Autowired
    private FzgjFileDao fzgjFileDao;

    @Autowired
    private FzgjFileVal fzgjFileVal;


    @Override
    public TgPageInfo queryPageList(FzgjFileEntity entity) {
        EciQuery<FzgjFileEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjFileEntity> entities = fzgjFileDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjFileEntity save(FzgjFileEntity entity) {
        // 返回实体对象
        FzgjFileEntity fzgjFileEntity = null;
        fzgjFileVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjFileEntity = fzgjFileDao.insertOne(entity);

        }else{

            fzgjFileEntity = fzgjFileDao.updateByEntityId(entity);

        }
        return fzgjFileEntity;
    }

    @Override
    public List<FzgjFileEntity> selectList(FzgjFileEntity entity) {
        return fzgjFileDao.selectList(entity);
    }

    @Override
    public FzgjFileEntity selectOneById(Serializable id) {
        return fzgjFileDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjFileEntity> list) {
        fzgjFileDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjFileDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjFileDao.deleteById(id);
    }

}