package com.eci.project.omsOrderGoodsPack.service;

import com.eci.common.db.DBHelper;
import com.eci.common.util.StringUtils;
import com.eci.common.utils.CommonLib;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.omsOrderGoodsPack.dao.OmsOrderGoodsPackDao;
import com.eci.project.omsOrderGoodsPack.entity.OmsOrderGoodsPackEntity;
import com.eci.project.omsOrderGoodsPack.entity.ResOmsOrderGoodsPackEntity;
import com.eci.project.omsOrderGoodsPack.validate.OmsOrderGoodsPackVal;
import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * 货物包装表Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-06-10
 */
@Service
@Slf4j
public class OmsOrderGoodsPackService implements EciBaseService<OmsOrderGoodsPackEntity> {


    CommonLib cmn = CommonLib.getInstance();

    @Autowired
    private OmsOrderGoodsPackDao omsOrderGoodsPackDao;

    @Autowired
    private OmsOrderGoodsPackVal omsOrderGoodsPackVal;


    @Override
    public TgPageInfo queryPageList(OmsOrderGoodsPackEntity entity) {
        EciQuery<OmsOrderGoodsPackEntity> eciQuery = EciQuery.buildQuery(entity);
        List<OmsOrderGoodsPackEntity> entities = omsOrderGoodsPackDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderGoodsPackEntity save(OmsOrderGoodsPackEntity entity) {
        // 返回实体对象
        OmsOrderGoodsPackEntity omsOrderGoodsPackEntity = null;
        omsOrderGoodsPackVal.saveValidate(entity, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            omsOrderGoodsPackEntity = omsOrderGoodsPackDao.insertOne(entity);

        } else {

            omsOrderGoodsPackEntity = omsOrderGoodsPackDao.updateByEntityId(entity);

        }
        return omsOrderGoodsPackEntity;
    }

    @Override
    public List<OmsOrderGoodsPackEntity> selectList(OmsOrderGoodsPackEntity entity) {
        return omsOrderGoodsPackDao.selectList(entity);
    }

    @Override
    public OmsOrderGoodsPackEntity selectOneById(Serializable id) {
        return omsOrderGoodsPackDao.selectById(id);
    }


    @Override
    public void insertBatch(List<OmsOrderGoodsPackEntity> list) {
        omsOrderGoodsPackDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return omsOrderGoodsPackDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return omsOrderGoodsPackDao.deleteById(id);
    }


    /**
     * 货物明细
     */
    public List<ResOmsOrderGoodsPackEntity> loadOrderGoodsPack(OmsOrderGoodsPackEntity entity) {

        String sql = "SELECT A.GUID,\n" +
                "       A.GOODS_GUID,\n" +
                "       A.ORDER_NO,\n" +
                "       A.PRE_NO,\n" +
                "       A.PACK_TYPE,\n" +
                "       A.QTY_PACK,\n" +
                "       A.LONGS,\n" +
                "       A.WIDTHS,\n" +
                "       A.HEIGHTS,\n" +
                "       A.WEIGHT_PIECE,\n" +
                "       A.KDFCS,\n" +
                "       A.PACK_MEMO,\n" +
                "       A.CREATE_USER,\n" +
                "       A.CREATE_USER_NAME,\n" +
                "       A.CREATE_DATE,\n" +
                "       A.UPDATE_USER,\n" +
                "       A.UPDATE_USER_NAME,\n" +
                "       A.UPDATE_DATE,\n" +
                "       A.COMPANY_CODE,\n" +
                "       UNIT,\n" +
                "       QTY_GOODS,\n" +
                "       (SELECT GOODSUNIT.NAME  FROM FZGJ_GOODS_UNIT GOODSUNIT WHERE  GOODSUNIT.STATUS='Y' AND GOODSUNIT.GROUP_CODE=A.GROUP_CODE AND GOODSUNIT.CODE = A.UNIT AND ROWNUM=1)  UNIT_NAME,\n" +
                "       A.COMPANY_NAME,\n" +
                "       A.NODE_CODE,\n" +
                "       A.NODE_NAME,\n" +
                "       A.GROUP_CODE,\n" +
                "       A.GROUP_NAME,\n" +
                "       A.IS_MZBZ,\n" +
                "       (SELECT PACKTYPE.NAME  FROM FZGJ_GOODS_PACK_TYPE PACKTYPE WHERE PACKTYPE.STATUS = 'Y' AND PACKTYPE.GROUP_CODE=A.GROUP_CODE AND PACKTYPE.CODE = A.PACK_TYPE AND ROWNUM=1) PACK_TYPE_NAME\n" +
                "FROM OMS_ORDER_GOODS_PACK A";
        sql += "   WHERE A.GROUP_CODE =" + cmn.SQLQ(UserContext.getUserInfo().getCompanyCode());

        if (StringUtils.hasValue(entity.getOrderNo())) {
            sql += " AND  A.ORDER_NO=" + cmn.SQLQ(entity.getOrderNo());
            return DBHelper.selectList(sql, ResOmsOrderGoodsPackEntity.class);
        }

        if (StringUtils.hasValue(entity.getPreNo())) {
            sql += " AND  A.PRE_NO=" + cmn.SQLQ(entity.getPreNo());
            return DBHelper.selectList(sql, ResOmsOrderGoodsPackEntity.class);
        }

        return new ArrayList<>();

    }

}