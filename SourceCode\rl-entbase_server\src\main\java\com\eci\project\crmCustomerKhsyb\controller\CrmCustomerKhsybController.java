package com.eci.project.crmCustomerKhsyb.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.crmCustomer.entity.CrmCustomerEntity;
import com.eci.project.crmCustomerKhsyb.service.CrmCustomerKhsybService;
import com.eci.project.crmCustomerKhsyb.entity.CrmCustomerKhsybEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 客户事业部Controller
*
* @<NAME_EMAIL>
* @date 2025-05-20
*/
@Api(tags = "客户事业部")
@RestController
@RequestMapping("/crmCustomerKhsyb")
public class CrmCustomerKhsybController extends EciBaseController {

    @Autowired
    private CrmCustomerKhsybService crmCustomerKhsybService;


    @ApiOperation("客户事业部:保存")
    @EciLog(title = "客户事业部:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody CrmCustomerKhsybEntity entity){
        CrmCustomerKhsybEntity crmCustomerKhsybEntity =crmCustomerKhsybService.save(entity);
        return ResponseMsgUtil.success(10001,crmCustomerKhsybEntity);
    }


    @ApiOperation("客户事业部:查询列表")
    @EciLog(title = "客户事业部:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody CrmCustomerKhsybEntity entity){
        List<CrmCustomerKhsybEntity> crmCustomerKhsybEntities = crmCustomerKhsybService.selectList(entity);
        return ResponseMsgUtil.success(10001,crmCustomerKhsybEntities);
    }


    @ApiOperation("客户事业部:分页查询列表")
    @EciLog(title = "客户事业部:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody CrmCustomerKhsybEntity entity){
        TgPageInfo tgPageInfo = crmCustomerKhsybService.queryPageList(entity);
        return ResponseMsgUtil.successPlus(10001,tgPageInfo);
    }


    @ApiOperation("客户事业部:根据ID查一条")
    @EciLog(title = "客户事业部:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody CrmCustomerKhsybEntity entity){
        CrmCustomerKhsybEntity  crmCustomerKhsybEntity = crmCustomerKhsybService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,crmCustomerKhsybEntity);
    }


    @ApiOperation("客户事业部:根据ID删除一条")
    @EciLog(title = "客户事业部:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody CrmCustomerKhsybEntity entity){
        int count = crmCustomerKhsybService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("客户事业部:根据ID字符串删除多条")
    @EciLog(title = "客户事业部:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody CrmCustomerKhsybEntity entity) {
        int count = crmCustomerKhsybService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }

    @ApiOperation("客户事业部:验证是否存在")
    @EciLog(title = "客户事业部:验证是否存在", businessType = BusinessType.INSERT)
    @PostMapping("/Exist")
    @EciAction()
    public ResponseMsg Exist(@RequestBody CrmCustomerKhsybEntity entity){
        String value="";
        Boolean isCode=Boolean.valueOf(entity.getRequestParams().get("isCode").toString());
        value=entity.getParam("value").toString();

        boolean exist=crmCustomerKhsybService.Exist(value
                ,entity.getGuid()
                ,isCode);
        return ResponseMsgUtil.success(10001,exist);
    }

}