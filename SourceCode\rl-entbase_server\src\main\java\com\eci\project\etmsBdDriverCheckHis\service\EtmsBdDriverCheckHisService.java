package com.eci.project.etmsBdDriverCheckHis.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.etmsBdDriverCheckHis.dao.EtmsBdDriverCheckHisDao;
import com.eci.project.etmsBdDriverCheckHis.entity.EtmsBdDriverCheckHisEntity;
import com.eci.project.etmsBdDriverCheckHis.validate.EtmsBdDriverCheckHisVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 司机体检历史Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-22
*/
@Service
@Slf4j
public class EtmsBdDriverCheckHisService implements EciBaseService<EtmsBdDriverCheckHisEntity> {

    @Autowired
    private EtmsBdDriverCheckHisDao etmsBdDriverCheckHisDao;

    @Autowired
    private EtmsBdDriverCheckHisVal etmsBdDriverCheckHisVal;


    @Override
    public TgPageInfo queryPageList(EtmsBdDriverCheckHisEntity entity) {
        EciQuery<EtmsBdDriverCheckHisEntity> eciQuery = EciQuery.buildQuery(entity);
        List<EtmsBdDriverCheckHisEntity> entities = etmsBdDriverCheckHisDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public EtmsBdDriverCheckHisEntity save(EtmsBdDriverCheckHisEntity entity) {
        // 返回实体对象
        EtmsBdDriverCheckHisEntity etmsBdDriverCheckHisEntity = null;
        etmsBdDriverCheckHisVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            etmsBdDriverCheckHisEntity = etmsBdDriverCheckHisDao.insertOne(entity);

        }else{

            etmsBdDriverCheckHisEntity = etmsBdDriverCheckHisDao.updateByEntityId(entity);

        }
        return etmsBdDriverCheckHisEntity;
    }

    @Override
    public List<EtmsBdDriverCheckHisEntity> selectList(EtmsBdDriverCheckHisEntity entity) {
        return etmsBdDriverCheckHisDao.selectList(entity);
    }

    @Override
    public EtmsBdDriverCheckHisEntity selectOneById(Serializable id) {
        return etmsBdDriverCheckHisDao.selectById(id);
    }


    @Override
    public void insertBatch(List<EtmsBdDriverCheckHisEntity> list) {
        etmsBdDriverCheckHisDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return etmsBdDriverCheckHisDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return etmsBdDriverCheckHisDao.deleteById(id);
    }

}