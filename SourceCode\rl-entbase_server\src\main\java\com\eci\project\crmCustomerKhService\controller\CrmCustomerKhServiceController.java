package com.eci.project.crmCustomerKhService.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.crmCustomerKhService.service.CrmCustomerKhServiceService;
import com.eci.project.crmCustomerKhService.entity.CrmCustomerKhServiceEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* Controller
*
* @<NAME_EMAIL>
* @date 2025-05-13
*/
@Api(tags = "")
@RestController
@RequestMapping("/crmCustomerKhService")
public class CrmCustomerKhServiceController extends EciBaseController {

    @Autowired
    private CrmCustomerKhServiceService crmCustomerKhServiceService;


    @ApiOperation(":保存")
    @EciLog(title = ":新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody CrmCustomerKhServiceEntity entity){
        CrmCustomerKhServiceEntity crmCustomerKhServiceEntity =crmCustomerKhServiceService.save(entity);
        return ResponseMsgUtil.success(10001,crmCustomerKhServiceEntity);
    }


    @ApiOperation(":查询列表")
    @EciLog(title = ":查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody CrmCustomerKhServiceEntity entity){
        List<CrmCustomerKhServiceEntity> crmCustomerKhServiceEntities = crmCustomerKhServiceService.selectList(entity);
        return ResponseMsgUtil.success(10001,crmCustomerKhServiceEntities);
    }


    @ApiOperation(":分页查询列表")
    @EciLog(title = ":分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody CrmCustomerKhServiceEntity entity){
        TgPageInfo tgPageInfo = crmCustomerKhServiceService.queryPageList(entity);
        return ResponseMsgUtil.successPlus(10001,tgPageInfo);
    }


    @ApiOperation(":根据ID查一条")
    @EciLog(title = ":根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody CrmCustomerKhServiceEntity entity){
        CrmCustomerKhServiceEntity  crmCustomerKhServiceEntity = crmCustomerKhServiceService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,crmCustomerKhServiceEntity);
    }


    @ApiOperation(":根据ID删除一条")
    @EciLog(title = ":根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody CrmCustomerKhServiceEntity entity){
        int count = crmCustomerKhServiceService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation(":根据ID字符串删除多条")
    @EciLog(title = ":根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody CrmCustomerKhServiceEntity entity) {
        int count = crmCustomerKhServiceService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}