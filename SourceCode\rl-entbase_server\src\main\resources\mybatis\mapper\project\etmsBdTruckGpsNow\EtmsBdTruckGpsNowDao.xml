<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.etmsBdTruckGpsNow.dao.EtmsBdTruckGpsNowDao">
    <resultMap type="EtmsBdTruckGpsNowEntity" id="EtmsBdTruckGpsNowResult">
        <result property="guid" column="GUID"/>
        <result property="truckNo" column="TRUCK_NO"/>
        <result property="gpsMode" column="GPS_MODE"/>
        <result property="gpsNo" column="GPS_NO"/>
        <result property="driverName" column="DRIVER_NAME"/>
        <result property="driverPhone" column="DRIVER_PHONE"/>
        <result property="longitude" column="LONGITUDE"/>
        <result property="latitude" column="LATITUDE"/>
        <result property="longitudebd" column="LONGITUDEBD"/>
        <result property="latitudebd" column="LATITUDEBD"/>
        <result property="address" column="ADDRESS"/>
        <result property="addTime" column="ADD_TIME"/>
        <result property="status" column="STATUS"/>
        <result property="orderCarGuid" column="ORDER_CAR_GUID"/>
        <result property="createCompany" column="CREATE_COMPANY"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
    </resultMap>

    <sql id="selectEtmsBdTruckGpsNowEntityVo">
        select
            GUID,
            TRUCK_NO,
            GPS_MODE,
            GPS_NO,
            DRIVER_NAME,
            DRIVER_PHONE,
            LONGITUDE,
            LATITUDE,
            LONGITUDEBD,
            LATITUDEBD,
            ADDRESS,
            ADD_TIME,
            STATUS,
            ORDER_CAR_GUID,
            CREATE_COMPANY,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            CREATE_USER_NAME,
            UPDATE_USER_NAME
        from ETMS_BD_TRUCK_GPS_NOW
    </sql>
</mapper>