package com.eci.project.fzgjCrmContractTypeWarn.service;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.DictFieldUtils;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjCrmContractTypeWarn.dao.FzgjCrmContractTypeWarnDao;
import com.eci.project.fzgjCrmContractTypeWarn.entity.FzgjCrmContractTypeWarnEntity;
import com.eci.project.fzgjCrmContractTypeWarn.validate.FzgjCrmContractTypeWarnVal;

import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.List;


/**
 * 合同种类及预警时效Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-04-10
 */
@Service
@Slf4j
public class FzgjCrmContractTypeWarnService implements EciBaseService<FzgjCrmContractTypeWarnEntity> {

    @Autowired
    private FzgjCrmContractTypeWarnDao fzgjCrmContractTypeWarnDao;

    @Autowired
    private FzgjCrmContractTypeWarnVal fzgjCrmContractTypeWarnVal;


    @Override
    public TgPageInfo queryPageList(FzgjCrmContractTypeWarnEntity entity) {
        entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
        EciQuery<FzgjCrmContractTypeWarnEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjCrmContractTypeWarnEntity> entities = fzgjCrmContractTypeWarnDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjCrmContractTypeWarnEntity save(FzgjCrmContractTypeWarnEntity entity) {
        // 返回实体对象
        FzgjCrmContractTypeWarnEntity fzgjCrmContractTypeWarnEntity = null;
        fzgjCrmContractTypeWarnVal.saveValidate(entity, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setGuid(IdWorker.get32UUID());
            entity.setCreateDate(new java.util.Date());
            entity.setUpdateDate(new java.util.Date());
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
            entity.setNodeCode(UserContext.getUserInfo().getDeptCode());
            entity.setNodeName(UserContext.getUserInfo().getDeptName());
            entity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            entity.setCompanyName(UserContext.getUserInfo().getCompanyName());
            fzgjCrmContractTypeWarnEntity = fzgjCrmContractTypeWarnDao.insertOne(entity);

        } else {
            entity.setUpdateDate(new java.util.Date());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            fzgjCrmContractTypeWarnEntity = fzgjCrmContractTypeWarnDao.updateByEntityId(entity);

        }
        return fzgjCrmContractTypeWarnEntity;
    }

    @Override
    public List<FzgjCrmContractTypeWarnEntity> selectList(FzgjCrmContractTypeWarnEntity entity) {
        return fzgjCrmContractTypeWarnDao.selectList(entity);
    }

    @Override
    public FzgjCrmContractTypeWarnEntity selectOneById(Serializable id) {
        return fzgjCrmContractTypeWarnDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjCrmContractTypeWarnEntity> list) {
        fzgjCrmContractTypeWarnDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjCrmContractTypeWarnDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjCrmContractTypeWarnDao.deleteById(id);
    }

}