package com.eci.project.omsOrderPre.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.omsOrderPre.entity.OmsOrderPreEntity;


/**
* 客户委托单Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-04-15
*/
public interface OmsOrderPreDao extends EciBaseDao<OmsOrderPreEntity> {

}