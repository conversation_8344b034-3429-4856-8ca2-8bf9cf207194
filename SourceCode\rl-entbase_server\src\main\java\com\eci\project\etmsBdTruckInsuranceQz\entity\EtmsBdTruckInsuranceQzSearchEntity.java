package com.eci.project.etmsBdTruckInsuranceQz.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.eci.common.Zsr;
import com.eci.common.utils.ExportUtils.Excel;
import com.eci.crud.entity.EciBaseEntity;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;

public class EtmsBdTruckInsuranceQzSearchEntity extends EciBaseEntity {

    private String guid;
    @Excel(value = "车牌号",order = 1)
    private String truckNo;
    @Excel(value = "企业名称",order = 2)
    private String createCompany;
    @Excel(value = "保险类型",order = 3)
    private String insuranceType;
    @Excel(value = "保单编号",order = 4)
    private String policyNo;
    @Excel(value = "保险公司",order = 6)
    private String insurer;
    @Excel(value = "投保人/公司名称",order = 5)
    private String insuredName;
    @Excel(value = "保险金额",order = 7)
    private BigDecimal insuranceMoney;
    @Excel(value = "保险开始日期",order = 8)
    private String startDate;
    private Date startDateStart;
    private Date startDateEnd;
    @Excel(value = "保险结束日期",order = 9)
    private String endDate;
    private Date endDateStart;
    private Date endDateEnd;
    @Excel(value = "投保期限",order = 10)
    private String insurancePeriod;
    @Excel(value = "是否送审",order = 11)
    private String modMark;
    @Excel(value = "审核通过",order = 12)
    private String checkMark;
    @Excel(value = "保险状态",order = 13)
    private String insuranceStatus;
    @Excel(value = "创建企业",order = 14)
    private String companyName;
    @Excel(value = "创建时间",order = 15)
    private Date createDate;
    private Date createDateStart;
    private Date createDateEnd;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getTruckNo() {
        return truckNo;
    }

    public void setTruckNo(String truckNo) {
        this.truckNo = truckNo;
    }

    public String getCreateCompany() {
        return createCompany;
    }

    public void setCreateCompany(String createCompany) {
        this.createCompany = createCompany;
    }

    public String getInsuranceType() {
        return insuranceType;
    }

    public void setInsuranceType(String insuranceType) {
        this.insuranceType = insuranceType;
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public String getInsurer() {
        return insurer;
    }

    public void setInsurer(String insurer) {
        this.insurer = insurer;
    }

    public String getInsuredName() {
        return insuredName;
    }

    public void setInsuredName(String insuredName) {
        this.insuredName = insuredName;
    }

    public BigDecimal getInsuranceMoney() {
        return insuranceMoney;
    }

    public void setInsuranceMoney(BigDecimal insuranceMoney) {
        this.insuranceMoney = insuranceMoney;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public Date getStartDateStart() {
        return startDateStart;
    }

    public void setStartDateStart(Date startDateStart) {
        this.startDateStart = startDateStart;
    }

    public Date getStartDateEnd() {
        return startDateEnd;
    }

    public void setStartDateEnd(Date startDateEnd) {
        this.startDateEnd = startDateEnd;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public Date getEndDateStart() {
        return endDateStart;
    }

    public void setEndDateStart(Date endDateStart) {
        this.endDateStart = endDateStart;
    }

    public Date getEndDateEnd() {
        return endDateEnd;
    }

    public void setEndDateEnd(Date endDateEnd) {
        this.endDateEnd = endDateEnd;
    }

    public String getInsurancePeriod() {
        return insurancePeriod;
    }

    public void setInsurancePeriod(String insurancePeriod) {
        this.insurancePeriod = insurancePeriod;
    }

    public String getModMark() {
        return modMark;
    }

    public void setModMark(String modMark) {
        this.modMark = modMark;
    }

    public String getCheckMark() {
        return checkMark;
    }

    public void setCheckMark(String checkMark) {
        this.checkMark = checkMark;
    }

    public String getInsuranceStatus() {
        return insuranceStatus;
    }

    public void setInsuranceStatus(String insuranceStatus) {
        this.insuranceStatus = insuranceStatus;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getCreateDateStart() {
        return createDateStart;
    }

    public void setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
    }

    public Date getCreateDateEnd() {
        return createDateEnd;
    }

    public void setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
    }
}
