package com.eci.project.fzgjBdOpTypeUser.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.fzgjBdOpTypeUser.entity.FzgjBdOpTypeUserEntity;


/**
* 用户-业务伙伴授权Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-03-27
*/
public interface FzgjBdOpTypeUserDao extends EciBaseDao<FzgjBdOpTypeUserEntity> {

}