package com.eci.project.fzgjBdHx.validate;

import com.eci.common.web.BllContext;
import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjBdHx.entity.FzgjBdHxEntity;

import com.eci.sso.role.entity.UserContext;
import org.springframework.stereotype.Service;

import java.util.Date;


/**
* 航线信息Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-03-13
*/
@Service
public class FzgjBdHxVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjBdHxEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjBdHxEntity entity, BusinessType businessType) {
        entity.setUpdateUser(UserContext.getUserInfo().getUserId());
        entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        entity.setUpdateDate(new Date());
        if(businessType==BusinessType.INSERT){
            entity.setCreateUser(UserContext.getUserInfo().getUserId());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setCreateDate(new Date());
        }
    }

}
