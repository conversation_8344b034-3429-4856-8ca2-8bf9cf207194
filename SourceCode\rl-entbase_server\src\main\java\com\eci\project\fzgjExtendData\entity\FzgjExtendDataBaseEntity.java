package com.eci.project.fzgjExtendData.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;

import java.util.Date;


/**
* 扩展基础资料对象 FZGJ_EXTEND_DATA
*
* @<NAME_EMAIL>
* @date 2025-03-19
*/
@FieldNameConstants
public class FzgjExtendDataBaseEntity extends EciBaseEntity
{
	/**
	* GUID
	*/
	@ApiModelProperty("GUID(50)")
	@TableId("GUID")
	private String guid;

	/**
	* 数据类型
	*/
	@ApiModelProperty("数据类型(50)")
	@TableField("DATA_TYPE")
	private String dataType;

	/**
	* 代码
	*/
	@ApiModelProperty("代码(20)")
	@TableField("CODE")
	private String code;

	/**
	* 名称
	*/
	@ApiModelProperty("名称(200)")
	@TableField("NAME")
	private String name;

	/**
	* 状态
	*/
	@ApiModelProperty("状态(1)")
	@TableField("STATUS")
	private String status;

	/**
	* 备注
	*/
	@ApiModelProperty("备注(200)")
	@TableField("MEMO")
	private String memo;

	/**
	* 顺序
	*/
	@ApiModelProperty("顺序(22)")
	@TableField("SEQ")
	private Integer seq;

	/**
	* 创建时间
	*/
	@ApiModelProperty("创建时间(7)")
	@TableField("CREATE_DATE")
	private Date createDate;

	@ApiModelProperty("创建时间开始")
	@TableField(exist=false)
	private Date createDateStart;

	@ApiModelProperty("创建时间结束")
	@TableField(exist=false)
	private Date createDateEnd;

	/**
	* 创建人
	*/
	@ApiModelProperty("创建人(50)")
	@TableField("CREATE_USER")
	private String createUser;

	/**
	* 创建人名称
	*/
	@ApiModelProperty("创建人名称(50)")
	@TableField("CREATE_USER_NAME")
	private String createUserName;

	/**
	* 修改时间
	*/
	@ApiModelProperty("修改时间(7)")
	@TableField("UPDATE_DATE")
	private Date updateDate;

	@ApiModelProperty("修改时间开始")
	@TableField(exist=false)
	private Date updateDateStart;

	@ApiModelProperty("修改时间结束")
	@TableField(exist=false)
	private Date updateDateEnd;

	/**
	* 修改人
	*/
	@ApiModelProperty("修改人(50)")
	@TableField("UPDATE_USER")
	private String updateUser;

	/**
	* 修改人名称
	*/
	@ApiModelProperty("修改人名称(50)")
	@TableField("UPDATE_USER_NAME")
	private String updateUserName;

	/**
	* 组织代码
	*/
	@ApiModelProperty("组织代码(50)")
	@TableField("NODE_CODE")
	private String nodeCode;

	/**
	* 组织名称
	*/
	@ApiModelProperty("组织名称(200)")
	@TableField("NODE_NAME")
	private String nodeName;

	/**
	* 公司代码
	*/
	@ApiModelProperty("公司代码(50)")
	@TableField("COMPANY_CODE")
	private String companyCode;

	/**
	* 公司名称
	*/
	@ApiModelProperty("公司名称(200)")
	@TableField("COMPANY_NAME")
	private String companyName;

	/**
	* 集团代码
	*/
	@ApiModelProperty("集团代码(50)")
	@TableField("GROUP_CODE")
	private String groupCode;

	/**
	* 集团名称
	*/
	@ApiModelProperty("集团名称(200)")
	@TableField("GROUP_NAME")
	private String groupName;

	/**
	* 跨境班车线路里的关联国家代码
	*/
	@ApiModelProperty("跨境班车线路里的关联国家代码(4,000)")
	@TableField("REL_COUNTRY_CODE")
	private String relCountryCode;

	/**
	* 跨境班车线路里的关联国家名称
	*/
	@ApiModelProperty("跨境班车线路里的关联国家名称(4,000)")
	@TableField("REL_COUNTRY_NAME")
	private String relCountryName;

	@JsonIgnore
	@TableField(exist = false)
	private Class<?> clazz = this.getClass();

	public Class<?> getClazz() {
		return clazz;
	}

	public void setClazz(Class<?> clazz) {
		this.clazz = clazz;
	}

	public FzgjExtendDataBaseEntity setGuid(String guid) {
		this.guid = guid;
		return this;
	}

	public String getGuid() {
		return guid;
	}

	public FzgjExtendDataBaseEntity setDataType(String dataType) {
		this.dataType = dataType;
		return this;
	}

	public String getDataType() {
		return dataType;
	}

	public FzgjExtendDataBaseEntity setCode(String code) {
		this.code = code;
		return this;
	}

	public String getCode() {
		return code;
	}

	public FzgjExtendDataBaseEntity setName(String name) {
		this.name = name;
		return this;
	}

	public String getName() {
		return name;
	}

	public FzgjExtendDataBaseEntity setStatus(String status) {
		this.status = status;
		return this;
	}

	public String getStatus() {
		return status;
	}

	public FzgjExtendDataBaseEntity setMemo(String memo) {
		this.memo = memo;
		return this;
	}

	public String getMemo() {
		return memo;
	}

	public FzgjExtendDataBaseEntity setSeq(Integer seq) {
		this.seq = seq;
		return this;
	}

	public Integer getSeq() {
		return seq;
	}

	public FzgjExtendDataBaseEntity setCreateDate(Date createDate) {
		this.createDate = createDate;
		return this;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public FzgjExtendDataBaseEntity setCreateDateStart(Date createDateStart) {
		this.createDateStart = createDateStart;
		return this;
	}

	public Date getCreateDateStart() {
		return createDateStart;
	}

	public FzgjExtendDataBaseEntity setCreateDateEnd(Date createDateEnd) {
		this.createDateEnd = createDateEnd;
		return this;
	}

	public Date getCreateDateEnd() {
		return createDateEnd;
	}
	public FzgjExtendDataBaseEntity setCreateUser(String createUser) {
		this.createUser = createUser;
		return this;
	}

	public String getCreateUser() {
		return createUser;
	}

	public FzgjExtendDataBaseEntity setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
		return this;
	}

	public String getCreateUserName() {
		return createUserName;
	}

	public FzgjExtendDataBaseEntity setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
		return this;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public FzgjExtendDataBaseEntity setUpdateDateStart(Date updateDateStart) {
		this.updateDateStart = updateDateStart;
		return this;
	}

	public Date getUpdateDateStart() {
		return updateDateStart;
	}

	public FzgjExtendDataBaseEntity setUpdateDateEnd(Date updateDateEnd) {
		this.updateDateEnd = updateDateEnd;
		return this;
	}

	public Date getUpdateDateEnd() {
		return updateDateEnd;
	}
	public FzgjExtendDataBaseEntity setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
		return this;
	}

	public String getUpdateUser() {
		return updateUser;
	}

	public FzgjExtendDataBaseEntity setUpdateUserName(String updateUserName) {
		this.updateUserName = updateUserName;
		return this;
	}

	public String getUpdateUserName() {
		return updateUserName;
	}

	public FzgjExtendDataBaseEntity setNodeCode(String nodeCode) {
		this.nodeCode = nodeCode;
		return this;
	}

	public String getNodeCode() {
		return nodeCode;
	}

	public FzgjExtendDataBaseEntity setNodeName(String nodeName) {
		this.nodeName = nodeName;
		return this;
	}

	public String getNodeName() {
		return nodeName;
	}

	public FzgjExtendDataBaseEntity setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
		return this;
	}

	public String getCompanyCode() {
		return companyCode;
	}

	public FzgjExtendDataBaseEntity setCompanyName(String companyName) {
		this.companyName = companyName;
		return this;
	}

	public String getCompanyName() {
		return companyName;
	}

	public FzgjExtendDataBaseEntity setGroupCode(String groupCode) {
		this.groupCode = groupCode;
		return this;
	}

	public String getGroupCode() {
		return groupCode;
	}

	public FzgjExtendDataBaseEntity setGroupName(String groupName) {
		this.groupName = groupName;
		return this;
	}

	public String getGroupName() {
		return groupName;
	}

	public FzgjExtendDataBaseEntity setRelCountryCode(String relCountryCode) {
		this.relCountryCode = relCountryCode;
		return this;
	}

	public String getRelCountryCode() {
		return relCountryCode;
	}

	public FzgjExtendDataBaseEntity setRelCountryName(String relCountryName) {
		this.relCountryName = relCountryName;
		return this;
	}

	public String getRelCountryName() {
		return relCountryName;
	}

}
