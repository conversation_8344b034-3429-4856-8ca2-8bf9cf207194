<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.omsOrderFwxmWorkFkHzqd.dao.OmsOrderFwxmWorkFkHzqdDao">
    <resultMap type="OmsOrderFwxmWorkFkHzqdEntity" id="OmsOrderFwxmWorkFkHzqdResult">
        <result property="guid" column="GUID"/>
        <result property="orderNo" column="ORDER_NO"/>
        <result property="workNo" column="WORK_NO"/>
        <result property="fwxmCode" column="FWXM_CODE"/>
        <result property="checkbillNo" column="CHECKBILL_NO"/>
        <result property="checkbillTableNum" column="CHECKBILL_TABLE_NUM"/>
        <result property="jyjyNo" column="JYJY_NO"/>
        <result property="decNo" column="DEC_NO"/>
        <result property="decTableNum" column="DEC_TABLE_NUM"/>
        <result property="decNoXt" column="DEC_NO_XT"/>
        <result property="decTradeMode" column="DEC_TRADE_MODE"/>
        <result property="piece" column="PIECE"/>
        <result property="unit" column="UNIT"/>
        <result property="weight" column="WEIGHT"/>
        <result property="xzwtNo" column="XZWT_NO"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="amount" column="AMOUNT"/>
        <result property="currency" column="CURRENCY"/>
        <result property="dDate" column="D_DATE"/>
        <result property="isSd" column="IS_SD"/>
        <result property="isGd" column="IS_GD"/>
        <result property="decNoRe" column="DEC_NO_RE"/>
        <result property="gdNum" column="GD_NUM"/>
        <result property="sgdMemo" column="SGD_MEMO"/>
        <result property="bizRegId" column="BIZ_REG_ID"/>
        <result property="hkDate" column="HK_DATE"/>
        <result property="qty" column="QTY"/>
        <result property="productCode" column="PRODUCT_CODE"/>
        <result property="bjType" column="BJ_TYPE"/>
        <result property="ydh" column="YDH"/>
        <result property="fdh" column="FDH"/>
        <result property="xdh" column="XDH"/>
        <result property="bgdZs" column="BGD_ZS"/>
        <result property="zgdh" column="ZGDH"/>
        <result property="crkdh" column="CRKDH"/>
        <result property="bgd1" column="BGD1"/>
        <result property="bgd1Type" column="BGD1_TYPE"/>
        <result property="bgd2" column="BGD2"/>
        <result property="bgd2Type" column="BGD2_TYPE"/>
        <result property="remark" column="REMARK"/>
        <result property="sdNo" column="SD_NO"/>
        <result property="gdNo" column="GD_NO"/>
        <result property="commentPre" column="COMMENT_PRE"/>
        <result property="commentNow" column="COMMENT_NOW"/>
        <result property="contractNo" column="CONTRACT_NO"/>
        <result property="originArrivalCountry" column="ORIGIN_ARRIVAL_COUNTRY"/>
        <result property="containerNo" column="CONTAINER_NO"/>
        <result property="supervisionMode" column="SUPERVISION_MODE"/>
        <result property="isCheck" column="IS_CHECK"/>
        <result property="hzqdDate" column="HZQD_DATE"/>
        <result property="cylx" column="CYLX"/>
        <result property="iEType" column="I_E_TYPE"/>
        <result property="iEPort" column="I_E_PORT"/>
        <result property="customCode" column="CUSTOM_CODE"/>
        <result property="tradeCode" column="TRADE_CODE"/>
        <result property="ysfs" column="YSFS"/>
        <result property="netWeight" column="NET_WEIGHT"/>
        <result property="jnsfhr" column="JNSFHR"/>
        <result property="isJyjy" column="IS_JYJY"/>
        <result property="packType" column="PACK_TYPE"/>
        <result property="lds" column="LDS"/>
        <result property="qsMan" column="QS_MAN"/>
        <result property="qsDate" column="QS_DATE"/>
        <result property="billMan" column="BILL_MAN"/>
        <result property="tradeCountry" column="TRADE_COUNTRY"/>
        <result property="bgdMemo" column="BGD_MEMO"/>
        <result property="amountCurrency" column="AMOUNT_CURRENCY"/>
        <result property="isBj" column="IS_BJ"/>
        <result property="hsCode" column="HS_CODE"/>
        <result property="ysType" column="YS_TYPE"/>
        <result property="bgdBgType" column="BGD_BG_TYPE"/>
        <result property="hdcs" column="HDCS"/>
        <result property="billDate" column="BILL_DATE"/>
        <result property="jdDate" column="JD_DATE"/>
        <result property="bgFlag" column="BG_FLAG"/>
        <result property="hzqdBgType" column="HZQD_BG_TYPE"/>
        <result property="tradeCodeOut" column="TRADE_CODE_OUT"/>
        <result property="amountCny" column="AMOUNT_CNY"/>
        <result property="purporgcode" column="PURPORGCODE"/>
        <result property="decNoGw" column="DEC_NO_GW"/>
        <result property="bgd1Result" column="BGD1_RESULT"/>
        <result property="bgd2Result" column="BGD2_RESULT"/>
        <result property="bgd1Filename" column="BGD1_FILENAME"/>
        <result property="bgd1Filecontent" column="BGD1_FILECONTENT"/>
        <result property="bgd2Filename" column="BGD2_FILENAME"/>
        <result property="bgd2Filecontent" column="BGD2_FILECONTENT"/>
        <result property="bgd1Business" column="BGD1_BUSINESS"/>
        <result property="bgd1Ieflag" column="BGD1_IEFLAG"/>
        <result property="bgd2Business" column="BGD2_BUSINESS"/>
        <result property="bgd2Ieflag" column="BGD2_IEFLAG"/>
        <result property="ysamount" column="YSAMOUNT"/>
    </resultMap>

    <sql id="selectOmsOrderFwxmWorkFkHzqdEntityVo">
        select
            GUID,
            ORDER_NO,
            WORK_NO,
            FWXM_CODE,
            CHECKBILL_NO,
            CHECKBILL_TABLE_NUM,
            JYJY_NO,
            DEC_NO,
            DEC_TABLE_NUM,
            DEC_NO_XT,
            DEC_TRADE_MODE,
            PIECE,
            UNIT,
            WEIGHT,
            XZWT_NO,
            CREATE_USER,
            CREATE_USER_NAME,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_USER_NAME,
            UPDATE_DATE,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            AMOUNT,
            CURRENCY,
            D_DATE,
            IS_SD,
            IS_GD,
            DEC_NO_RE,
            GD_NUM,
            SGD_MEMO,
            BIZ_REG_ID,
            HK_DATE,
            QTY,
            PRODUCT_CODE,
            BJ_TYPE,
            YDH,
            FDH,
            XDH,
            BGD_ZS,
            ZGDH,
            CRKDH,
            BGD1,
            BGD1_TYPE,
            BGD2,
            BGD2_TYPE,
            REMARK,
            SD_NO,
            GD_NO,
            COMMENT_PRE,
            COMMENT_NOW,
            CONTRACT_NO,
            ORIGIN_ARRIVAL_COUNTRY,
            CONTAINER_NO,
            SUPERVISION_MODE,
            IS_CHECK,
            HZQD_DATE,
            CYLX,
            I_E_TYPE,
            I_E_PORT,
            CUSTOM_CODE,
            TRADE_CODE,
            YSFS,
            NET_WEIGHT,
            JNSFHR,
            IS_JYJY,
            PACK_TYPE,
            LDS,
            QS_MAN,
            QS_DATE,
            BILL_MAN,
            TRADE_COUNTRY,
            BGD_MEMO,
            AMOUNT_CURRENCY,
            IS_BJ,
            HS_CODE,
            YS_TYPE,
            BGD_BG_TYPE,
            HDCS,
            BILL_DATE,
            JD_DATE,
            BG_FLAG,
            HZQD_BG_TYPE,
            TRADE_CODE_OUT,
            AMOUNT_CNY,
            PURPORGCODE,
            DEC_NO_GW,
            BGD1_RESULT,
            BGD2_RESULT,
            BGD1_FILENAME,
            BGD1_FILECONTENT,
            BGD2_FILENAME,
            BGD2_FILECONTENT,
            BGD1_BUSINESS,
            BGD1_IEFLAG,
            BGD2_BUSINESS,
            BGD2_IEFLAG,
            YSAMOUNT
        from OMS_ORDER_FWXM_WORK_FK_HZQD
    </sql>
</mapper>