package com.eci.project.fzgjScoreDriver.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjScoreDriver.dao.FzgjScoreDriverDao;
import com.eci.project.fzgjScoreDriver.entity.FzgjScoreDriverEntity;
import com.eci.project.fzgjScoreDriver.validate.FzgjScoreDriverVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 企业评分Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-06-16
*/
@Service
@Slf4j
public class FzgjScoreDriverService implements EciBaseService<FzgjScoreDriverEntity> {

    @Autowired
    private FzgjScoreDriverDao fzgjScoreDriverDao;

    @Autowired
    private FzgjScoreDriverVal fzgjScoreDriverVal;


    @Override
    public TgPageInfo queryPageList(FzgjScoreDriverEntity entity) {
        EciQuery<FzgjScoreDriverEntity> eciQuery = EciQuery.buildQuery(entity);
        if(entity.getScore()!=null)
            eciQuery.apply(" A.score>={0}",entity.getScore());
        if(entity.getScore1()!=null)
            eciQuery.apply(" A.score<={0}",entity.getScore1());
        List<FzgjScoreDriverEntity> entities = fzgjScoreDriverDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjScoreDriverEntity save(FzgjScoreDriverEntity entity) {
        // 返回实体对象
        FzgjScoreDriverEntity fzgjScoreDriverEntity = null;
        fzgjScoreDriverVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjScoreDriverEntity = fzgjScoreDriverDao.insertOne(entity);

        }else{

            fzgjScoreDriverEntity = fzgjScoreDriverDao.updateByEntityId(entity);

        }
        return fzgjScoreDriverEntity;
    }

    @Override
    public List<FzgjScoreDriverEntity> selectList(FzgjScoreDriverEntity entity) {
        return fzgjScoreDriverDao.selectList(entity);
    }

    @Override
    public FzgjScoreDriverEntity selectOneById(Serializable id) {
        return fzgjScoreDriverDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjScoreDriverEntity> list) {
        fzgjScoreDriverDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjScoreDriverDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjScoreDriverDao.deleteById(id);
    }

}