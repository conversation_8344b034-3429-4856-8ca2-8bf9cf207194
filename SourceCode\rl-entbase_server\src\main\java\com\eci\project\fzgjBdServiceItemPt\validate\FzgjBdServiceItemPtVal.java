package com.eci.project.fzgjBdServiceItemPt.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjBdServiceItemPt.entity.FzgjBdServiceItemPtEntity;

import com.eci.sso.role.entity.UserContext;
import org.springframework.stereotype.Service;

import java.util.Date;


/**
* 平台级服务项目Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-03-25
*/
@Service
public class FzgjBdServiceItemPtVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjBdServiceItemPtEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjBdServiceItemPtEntity entity, BusinessType businessType) {
        entity.setUpdateUser(UserContext.getUserInfo().getUserId());
        entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        entity.setUpdateDate(new Date());

        entity.setCreateUser(UserContext.getUserInfo().getUserId());
        entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
        entity.setCreateDate(new Date());
    }

}
