package com.eci.project.omsFile.controller;

import com.eci.common.BaseProperties;
import com.eci.common.DictFieldUtils;
import com.eci.common.ResponseMsgUtilX;
import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.omsFile.service.OmsFileService;
import com.eci.project.omsFile.entity.OmsFileEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.net.URLConnection;
import java.util.List;

/**
* OMS对接数据其他业务系统附件Controller
*
* @<NAME_EMAIL>
* @date 2025-06-04
*/
@Api(tags = "OMS对接数据其他业务系统附件")
@RestController
@RequestMapping("/omsFile")
public class OmsFileController extends EciBaseController {

    @Autowired
    private OmsFileService omsFileService;


    @ApiOperation("OMS对接数据其他业务系统附件:保存")
    @EciLog(title = "OMS对接数据其他业务系统附件:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody OmsFileEntity entity){
        OmsFileEntity omsFileEntity =omsFileService.save(entity);
        return ResponseMsgUtil.success(10001,omsFileEntity);
    }


    @ApiOperation("OMS对接数据其他业务系统附件:查询列表")
    @EciLog(title = "OMS对接数据其他业务系统附件:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody OmsFileEntity entity){
        List<OmsFileEntity> omsFileEntities = omsFileService.selectList(entity);
        return ResponseMsgUtilX.success(10001,omsFileEntities);
    }


    @ApiOperation("OMS系统附件:分页查询列表")
    @EciLog(title = "OMS系统附件:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody OmsFileEntity entity){
        TgPageInfo tgPageInfo = omsFileService.queryPageList(entity);
        return ResponseMsgUtilX.success(10001,tgPageInfo);
    }


    @ApiOperation("OMS对接数据其他业务系统附件:分页查询列表")
    @EciLog(title = "OMS对接数据其他业务系统附件:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageListForOther")
    @EciAction()
    public ResponseMsg selectPageListForOther(@RequestBody OmsFileEntity entity){
        TgPageInfo tgPageInfo = omsFileService.selectPageListForOther(entity);
        return ResponseMsgUtilX.success(10001,tgPageInfo);
    }


    @ApiOperation("OMS对接数据其他业务系统附件:根据ID查一条")
    @EciLog(title = "OMS对接数据其他业务系统附件:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody OmsFileEntity entity){
        OmsFileEntity  omsFileEntity = omsFileService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,omsFileEntity);
    }


    @ApiOperation("OMS对接数据其他业务系统附件:根据ID删除一条")
    @EciLog(title = "OMS对接数据其他业务系统附件:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody OmsFileEntity entity){
        int count = omsFileService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("OMS对接数据其他业务系统附件:根据ID字符串删除多条")
    @EciLog(title = "OMS对接数据其他业务系统附件:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody OmsFileEntity entity) {
        int count = omsFileService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }

    @ApiOperation("附件:附件下载")
    @EciLog(title = "附件:附件下载", businessType = BusinessType.DELETE)
    @GetMapping("/downLoadAttr")
    @EciAction()
    public void downLoadAttr(String fid) throws Exception {
        OmsFileEntity entity= omsFileService.selectOneById(fid);
        if(entity==null) throw new Exception("文件不存在");
        String basePath = BaseProperties.getFilepath()+entity.getUrl();
        File file=new File(basePath);
        if(!file.exists())
            throw new Exception("文件不存在");
        try {
            HttpServletResponse response = ServletUtils.getResponse();
            ServletOutputStream outputStream = null;
            outputStream = response.getOutputStream();
            response.setHeader("Content-Disposition", "attachment;filename=" + file.getName());

            FileInputStream inStream = new FileInputStream(file);
            byte[] buf = new byte[4096];
            int readLength;
            while((readLength = inStream.read(buf))!= -1){
                outputStream.write(buf, 0, readLength);
            }
            inStream.close();
            outputStream.flush();
            outputStream.close();
        }catch (Exception ex){

        }
    }

    private static final Logger logger = LoggerFactory.getLogger(OmsFileController.class);
    @ApiOperation("附件:附件预览")
    @EciLog(title = "附件:附件预览", businessType = BusinessType.OTHER)
    @GetMapping("/previewAttr")
    @EciAction()
    public void previewAttr(String fid) throws Exception {
        OmsFileEntity entity = omsFileService.selectOneById(fid);
        if (entity == null) throw new Exception("未查询到对应文件");

        String basePath = BaseProperties.getFilepath() + entity.getUrl();
        File file = new File(basePath);
        logger.info("previewAttr basePath {}",basePath);
        if (!file.exists()) {
            throw new Exception("文件不存在");
        }

        HttpServletResponse response = ServletUtils.getResponse();

        // 设置 MIME 类型（可选，但推荐）
        String contentType = URLConnection.guessContentTypeFromName(file.getName());
        if (contentType == null) {
            contentType = "application/octet-stream";
        }
        response.setContentType(contentType);

        // 关键修改：使用 inline 表示内联预览
        response.setHeader("Content-Disposition", "inline;filename=" + file.getName());

        try (FileInputStream inStream = new FileInputStream(file);
             ServletOutputStream outputStream = response.getOutputStream()) {

            byte[] buf = new byte[4096];
            int readLength;
            while ((readLength = inStream.read(buf)) != -1) {
                outputStream.write(buf, 0, readLength);
            }

            outputStream.flush();
        } catch (Exception ex) {
            // 可记录日志或处理异常
            ex.printStackTrace();
        }
    }
}