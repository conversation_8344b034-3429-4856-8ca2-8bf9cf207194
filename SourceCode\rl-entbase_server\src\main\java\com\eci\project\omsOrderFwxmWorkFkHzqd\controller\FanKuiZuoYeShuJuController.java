package com.eci.project.omsOrderFwxmWorkFkHzqd.controller;

import com.eci.common.util.ResponseMsg;
import com.eci.common.util.ResponseMsgUtil;
import com.eci.crud.controller.EciBaseController;
import com.eci.log.annotation.EciAction;
import com.eci.log.annotation.EciLog;
import com.eci.log.enums.BusinessType;
import com.eci.project.omsOrderFwxmWork.entity.OmsOrderFwxmWorkEntity;
import com.eci.project.omsOrderFwxmWorkFkHzqd.entity.OmsOrderFwxmWorkFkHzqdEntity;
import com.eci.project.omsOrderFwxmWorkFkHzqd.service.FeedbackOmsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 与外部数据对接-反馈作业数据-Controller
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@Api(tags = "反馈内容-作业数据/接单状态/作业节点/作业完成")
@RestController
@RequestMapping("/fanKui")
public class FanKuiZuoYeShuJuController extends EciBaseController {

    @Autowired
    private FeedbackOmsService feedbackOmsService;

    @ApiOperation("反馈内容-作业数据:保存")
    @EciLog(title = "反馈内容-作业数据:保存", businessType = BusinessType.INSERT)
    @PostMapping("/zuoYeData")
    @EciAction()
    public ResponseMsg zuoYeData(@RequestBody String jsonString) {
        OmsOrderFwxmWorkFkHzqdEntity omsOrderFwxmWorkFkHzqdEntity = feedbackOmsService.zuoYeData(jsonString);
        return ResponseMsgUtil.success(10001, null);
    }

    @ApiOperation("反馈内容-反馈接单状态:保存")
    @EciLog(title = "反馈内容-反馈接单状态:保存", businessType = BusinessType.INSERT)
    @PostMapping("/jieDanStatus")
    @EciAction()
    public ResponseMsg jieDanStatus(@RequestBody String jsonString) {
        feedbackOmsService.jieDanStatus(jsonString);
        return ResponseMsgUtil.success(10001, null);
    }

    @ApiOperation("反馈内容-作业节点:保存")
    @EciLog(title = "反馈内容-作业节点:保存", businessType = BusinessType.INSERT)
    @PostMapping("/zuoYeJieDian")
    @EciAction()
    public ResponseMsg zuoYeJieDian(@RequestBody String jsonString) {
        feedbackOmsService.zuoYeJieDian(jsonString);
        return ResponseMsgUtil.success(10001, null);
    }

    @ApiOperation("反馈内容-作业完成:保存")
    @EciLog(title = "反馈内容-作业完成:保存", businessType = BusinessType.INSERT)
    @PostMapping("/zuoYeWanCheng")
    @EciAction()
    public ResponseMsg zuoYeWanCheng(@RequestBody String jsonString) {
        feedbackOmsService.zuoYeWanCheng(jsonString);
        return ResponseMsgUtil.success(10001, null);
    }


}