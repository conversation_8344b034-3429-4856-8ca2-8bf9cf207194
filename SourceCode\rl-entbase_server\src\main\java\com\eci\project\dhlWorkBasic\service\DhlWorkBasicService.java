package com.eci.project.dhlWorkBasic.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.dhlWorkBasic.dao.DhlWorkBasicDao;
import com.eci.project.dhlWorkBasic.entity.DhlWorkBasicEntity;
import com.eci.project.dhlWorkBasic.validate.DhlWorkBasicVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 运单信息Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-22
*/
@Service
@Slf4j
public class DhlWorkBasicService implements EciBaseService<DhlWorkBasicEntity> {

    @Autowired
    private DhlWorkBasicDao dhlWorkBasicDao;

    @Autowired
    private DhlWorkBasicVal dhlWorkBasicVal;


    @Override
    public TgPageInfo queryPageList(DhlWorkBasicEntity entity) {
        EciQuery<DhlWorkBasicEntity> eciQuery = EciQuery.buildQuery(entity);
        List<DhlWorkBasicEntity> entities = dhlWorkBasicDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public DhlWorkBasicEntity save(DhlWorkBasicEntity entity) {
        // 返回实体对象
        DhlWorkBasicEntity dhlWorkBasicEntity = null;
        dhlWorkBasicVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            dhlWorkBasicEntity = dhlWorkBasicDao.insertOne(entity);

        }else{

            dhlWorkBasicEntity = dhlWorkBasicDao.updateByEntityId(entity);

        }
        return dhlWorkBasicEntity;
    }

    @Override
    public List<DhlWorkBasicEntity> selectList(DhlWorkBasicEntity entity) {
        return dhlWorkBasicDao.selectList(entity);
    }

    @Override
    public DhlWorkBasicEntity selectOneById(Serializable id) {
        return dhlWorkBasicDao.selectById(id);
    }


    @Override
    public void insertBatch(List<DhlWorkBasicEntity> list) {
        dhlWorkBasicDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return dhlWorkBasicDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return dhlWorkBasicDao.deleteById(id);
    }

}