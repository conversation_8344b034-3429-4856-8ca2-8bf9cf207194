package com.eci.project.omsOrderLog.service;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.Zsr;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.exception.BaseException;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.omsOrderLog.dao.OmsOrderLogDao;
import com.eci.project.omsOrderLog.entity.OmsOrderLogEntity;
import com.eci.project.omsOrderLog.validate.OmsOrderLogVal;

import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 订单操作日志信息Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-06-19
 */
@Service
@Slf4j
public class OmsOrderLogService implements EciBaseService<OmsOrderLogEntity> {

    @Autowired
    private OmsOrderLogDao omsOrderLogDao;

    @Autowired
    private OmsOrderLogVal omsOrderLogVal;

    /**
     * 带必填参数orderno的查询分页
     *
     * @param entity
     * @return
     */
    @Override
    public TgPageInfo queryPageList(OmsOrderLogEntity entity) {
        if (Zsr.String.IsNullOrWhiteSpace(entity.getOrderNo())) {
            return new TgPageInfo();
        }
        EciQuery<OmsOrderLogEntity> eciQuery = EciQuery.buildQuery(entity);
        eciQuery.eq(OmsOrderLogEntity::getOrderNo, entity.getOrderNo());
        eciQuery.orderBy(false, OmsOrderLogEntity::getCreateDate);
        List<OmsOrderLogEntity> entities = omsOrderLogDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }

    @Transactional(rollbackFor = Exception.class)
    public OmsOrderLogEntity writeLog(OmsOrderLogEntity entity) {
        // 返回实体对象
        if (entity == null){
            throw new BaseException("参数错误");
        }
        entity.setCreateDate(new Date());
        entity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
        entity.setCompanyName(UserContext.getUserInfo().getCompanyName());
        entity.setGuid(IdWorker.getIdStr());
        entity.setUserName(UserContext.getUserInfo().getLoginName());
        entity.setTrueName(UserContext.getUserInfo().getTrueName());

        OmsOrderLogEntity   omsOrderLogEntity = omsOrderLogDao.insertOne(entity);
        return omsOrderLogEntity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderLogEntity save(OmsOrderLogEntity entity) {
        // 返回实体对象
        OmsOrderLogEntity omsOrderLogEntity = null;
        omsOrderLogVal.saveValidate(entity, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            omsOrderLogEntity = omsOrderLogDao.insertOne(entity);

        } else {

            omsOrderLogEntity = omsOrderLogDao.updateByEntityId(entity);

        }
        return omsOrderLogEntity;
    }

    @Override
    public List<OmsOrderLogEntity> selectList(OmsOrderLogEntity entity) {
        return omsOrderLogDao.selectList(entity);
    }

    @Override
    public OmsOrderLogEntity selectOneById(Serializable id) {
        return omsOrderLogDao.selectById(id);
    }


    @Override
    public void insertBatch(List<OmsOrderLogEntity> list) {
        omsOrderLogDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return omsOrderLogDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return omsOrderLogDao.deleteById(id);
    }

}