package com.eci.project.etmsBdTruckLimitedQz.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.etmsBdTruckLimitedQz.service.EtmsBdTruckLimitedQzService;
import com.eci.project.etmsBdTruckLimitedQz.entity.EtmsBdTruckLimitedQzEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 车辆年审历史Controller
*
* @<NAME_EMAIL>
* @date 2025-04-16
*/
@Api(tags = "车辆年审历史")
@RestController
@RequestMapping("/etmsBdTruckLimitedQz")
public class EtmsBdTruckLimitedQzController extends EciBaseController {

    @Autowired
    private EtmsBdTruckLimitedQzService etmsBdTruckLimitedQzService;


    @ApiOperation("车辆年审历史:保存")
    @EciLog(title = "车辆年审历史:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody EtmsBdTruckLimitedQzEntity entity){
        EtmsBdTruckLimitedQzEntity etmsBdTruckLimitedQzEntity =etmsBdTruckLimitedQzService.save(entity);
        return ResponseMsgUtil.success(10001,etmsBdTruckLimitedQzEntity);
    }


    @ApiOperation("车辆年审历史:查询列表")
    @EciLog(title = "车辆年审历史:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody EtmsBdTruckLimitedQzEntity entity){
        List<EtmsBdTruckLimitedQzEntity> etmsBdTruckLimitedQzEntities = etmsBdTruckLimitedQzService.selectList(entity);
        return ResponseMsgUtil.success(10001,etmsBdTruckLimitedQzEntities);
    }


    @ApiOperation("车辆年审历史:分页查询列表")
    @EciLog(title = "车辆年审历史:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody EtmsBdTruckLimitedQzEntity entity){
        TgPageInfo tgPageInfo = etmsBdTruckLimitedQzService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("车辆年审历史:根据ID查一条")
    @EciLog(title = "车辆年审历史:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody EtmsBdTruckLimitedQzEntity entity){
        EtmsBdTruckLimitedQzEntity  etmsBdTruckLimitedQzEntity = etmsBdTruckLimitedQzService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,etmsBdTruckLimitedQzEntity);
    }


    @ApiOperation("车辆年审历史:根据ID删除一条")
    @EciLog(title = "车辆年审历史:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody EtmsBdTruckLimitedQzEntity entity){
        int count = etmsBdTruckLimitedQzService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("车辆年审历史:根据ID字符串删除多条")
    @EciLog(title = "车辆年审历史:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody EtmsBdTruckLimitedQzEntity entity) {
        int count = etmsBdTruckLimitedQzService.deleteByList(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}