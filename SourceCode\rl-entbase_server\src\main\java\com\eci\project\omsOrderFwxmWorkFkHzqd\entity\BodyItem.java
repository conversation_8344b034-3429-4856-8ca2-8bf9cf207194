package com.eci.project.omsOrderFwxmWorkFkHzqd.entity;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class BodyItem {
    private String PRODUCT_CODE; // 品名
    private String HS_CODE; // HS编码
    private BigDecimal NET_WEIGHT; // 净重
    private BigDecimal PIECE; // 件数
    private BigDecimal WEIGHT; // 毛重
    private BigDecimal QTY; // 数量
    private BigDecimal AMOUNT; // 金额
    private String CURRENCY; // 币制
    private String AMOUNT_CURRENCY; // 金额币制
}