package com.eci.project.fzgjGoodsUnit.service;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjGoodsUnit.dao.FzgjGoodsUnitDao;
import com.eci.project.fzgjGoodsUnit.entity.FzgjGoodsUnitEntity;
import com.eci.project.fzgjGoodsUnit.validate.FzgjGoodsUnitVal;
import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.List;


/**
 * 仓储货品单位Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-04-01
 */
@Service
@Slf4j
public class FzgjGoodsUnitService implements EciBaseService<FzgjGoodsUnitEntity> {

    @Autowired
    private FzgjGoodsUnitDao fzgjGoodsUnitDao;

    @Autowired
    private FzgjGoodsUnitVal fzgjGoodsUnitVal;


    @Override
    public TgPageInfo queryPageList(FzgjGoodsUnitEntity entity) {
        entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
        EciQuery<FzgjGoodsUnitEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjGoodsUnitEntity> entities = fzgjGoodsUnitDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjGoodsUnitEntity save(FzgjGoodsUnitEntity entity) {
        // 返回实体对象
        FzgjGoodsUnitEntity fzgjGoodsUnitEntity = null;
        fzgjGoodsUnitVal.saveValidate(entity, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setGuid(IdWorker.get32UUID());
            entity.setCreateDate(new java.util.Date());
            entity.setUpdateDate(new java.util.Date());
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
            entity.setNodeCode(UserContext.getUserInfo().getDeptCode());
            entity.setNodeName(UserContext.getUserInfo().getDeptName());
            entity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            entity.setCompanyName(UserContext.getUserInfo().getCompanyName());
            fzgjGoodsUnitEntity = fzgjGoodsUnitDao.insertOne(entity);

        } else {
            entity.setUpdateDate(new java.util.Date());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            fzgjGoodsUnitEntity = fzgjGoodsUnitDao.updateByEntityId(entity);

        }
        return fzgjGoodsUnitEntity;
    }

    @Override
    public List<FzgjGoodsUnitEntity> selectList(FzgjGoodsUnitEntity entity) {
        return fzgjGoodsUnitDao.selectList(entity);
    }

    @Override
    public FzgjGoodsUnitEntity selectOneById(Serializable id) {
        return fzgjGoodsUnitDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjGoodsUnitEntity> list) {
        fzgjGoodsUnitDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjGoodsUnitDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjGoodsUnitDao.deleteById(id);
    }

}