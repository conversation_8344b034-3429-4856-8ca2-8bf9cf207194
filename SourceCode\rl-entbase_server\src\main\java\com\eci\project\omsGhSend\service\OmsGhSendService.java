package com.eci.project.omsGhSend.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.omsGhSend.dao.OmsGhSendDao;
import com.eci.project.omsGhSend.entity.OmsGhSendEntity;
import com.eci.project.omsGhSend.validate.OmsGhSendVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* OMS固化路由表Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-05-16
*/
@Service
@Slf4j
public class OmsGhSendService implements EciBaseService<OmsGhSendEntity> {

    @Autowired
    private OmsGhSendDao omsGhSendDao;

    @Autowired
    private OmsGhSendVal omsGhSendVal;


    @Override
    public TgPageInfo queryPageList(OmsGhSendEntity entity) {
        EciQuery<OmsGhSendEntity> eciQuery = EciQuery.buildQuery(entity);
        List<OmsGhSendEntity> entities = omsGhSendDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public OmsGhSendEntity save(OmsGhSendEntity entity) {
        // 返回实体对象
        OmsGhSendEntity omsGhSendEntity = null;
        omsGhSendVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            omsGhSendEntity = omsGhSendDao.insertOne(entity);

        }else{

            omsGhSendEntity = omsGhSendDao.updateByEntityId(entity);

        }
        return omsGhSendEntity;
    }

    @Override
    public List<OmsGhSendEntity> selectList(OmsGhSendEntity entity) {
        return omsGhSendDao.selectList(entity);
    }

    @Override
    public OmsGhSendEntity selectOneById(Serializable id) {
        return omsGhSendDao.selectById(id);
    }


    @Override
    public void insertBatch(List<OmsGhSendEntity> list) {
        omsGhSendDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return omsGhSendDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return omsGhSendDao.deleteById(id);
    }

}