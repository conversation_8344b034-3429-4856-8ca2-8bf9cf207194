package com.eci.project.omsOrderFwxmWorkFkHzqd.controller;

import com.eci.common.ResponseMsgUtilX;
import com.eci.common.util.ResponseMsg;
import com.eci.common.util.ResponseMsgUtil;
import com.eci.crud.controller.EciBaseController;
import com.eci.log.annotation.EciAction;
import com.eci.log.annotation.EciLog;
import com.eci.log.enums.BusinessType;
import com.eci.project.omsOrderFwxmWorkFkHzqd.entity.ReqomsOrderFwxmWorkFkBgdQueryEntity;
import com.eci.project.omsOrderFwxmWorkFkHzqd.service.OmsOrderFwxmWorkFkBgdService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "报关单反馈")
@RestController
@RequestMapping("/omsOrderFwxmWorkFkBgd")
public class OmsOrderFwxmWorkFkBgdController extends EciBaseController {

    @Autowired
    private OmsOrderFwxmWorkFkBgdService omsOrderFwxmWorkFkBgdService;

    @ApiOperation("反馈内容-报关单:加载")
    @EciLog(title = "反馈内容-报关单:加载", businessType = BusinessType.SELECT)
    @PostMapping("/loadFeedBackBgd")
    @EciAction()
    public ResponseMsg loadFeedBackBgd(@RequestBody ReqomsOrderFwxmWorkFkBgdQueryEntity entity){
        return ResponseMsgUtilX.success(10001,omsOrderFwxmWorkFkBgdService.feedBackBgdLoad(entity));
    }

    @ApiOperation("反馈内容-报关单:保存")
    @EciLog(title = "反馈内容-报关单:保存", businessType = BusinessType.SELECT)
    @PostMapping("/saveOrderFwxmWorkFkBgd")
    @EciAction()
    public ResponseMsg saveOrderFwxmWorkFkBgd(@RequestBody  String jsonString) {
        boolean flag = omsOrderFwxmWorkFkBgdService.feedBackBgdSave(jsonString);
        return ResponseMsgUtil.success(10001, flag ? "保存成功" : "保存失败");
    }

}
