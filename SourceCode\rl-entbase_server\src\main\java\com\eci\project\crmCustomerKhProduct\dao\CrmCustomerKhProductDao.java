package com.eci.project.crmCustomerKhProduct.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.crmCustomerKhProduct.entity.CrmCustomerKhProductEntity;


/**
* 客户信息-关联业务产品Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-05-12
*/
public interface CrmCustomerKhProductDao extends EciBaseDao<CrmCustomerKhProductEntity> {

}