package com.eci.project.fzgjBoxSize.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjBoxSize.service.FzgjBoxSizeService;
import com.eci.project.fzgjBoxSize.entity.FzgjBoxSizeEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 集装箱尺寸Controller
*
* @<NAME_EMAIL>
* @date 2025-04-17
*/
@Api(tags = "集装箱尺寸")
@RestController
@RequestMapping("/fzgjBoxSize")
public class FzgjBoxSizeController extends EciBaseController {

    @Autowired
    private FzgjBoxSizeService fzgjBoxSizeService;


    @ApiOperation("集装箱尺寸:保存")
    @EciLog(title = "集装箱尺寸:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody FzgjBoxSizeEntity entity){
        FzgjBoxSizeEntity fzgjBoxSizeEntity =fzgjBoxSizeService.save(entity);
        return ResponseMsgUtil.success(10001,fzgjBoxSizeEntity);
    }


    @ApiOperation("集装箱尺寸:查询列表")
    @EciLog(title = "集装箱尺寸:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjBoxSizeEntity entity){
        List<FzgjBoxSizeEntity> fzgjBoxSizeEntities = fzgjBoxSizeService.selectList(entity);
        return ResponseMsgUtil.success(10001,fzgjBoxSizeEntities);
    }


    @ApiOperation("集装箱尺寸:分页查询列表")
    @EciLog(title = "集装箱尺寸:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjBoxSizeEntity entity){
        TgPageInfo tgPageInfo = fzgjBoxSizeService.queryPageList(entity);
        return ResponseMsgUtil.successPlus(10001,tgPageInfo);
    }


    @ApiOperation("集装箱尺寸:根据ID查一条")
    @EciLog(title = "集装箱尺寸:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjBoxSizeEntity entity){
        FzgjBoxSizeEntity  fzgjBoxSizeEntity = fzgjBoxSizeService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,fzgjBoxSizeEntity);
    }


    @ApiOperation("集装箱尺寸:根据ID删除一条")
    @EciLog(title = "集装箱尺寸:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjBoxSizeEntity entity){
        int count = fzgjBoxSizeService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("集装箱尺寸:根据ID字符串删除多条")
    @EciLog(title = "集装箱尺寸:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjBoxSizeEntity entity) {
        int count = fzgjBoxSizeService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}