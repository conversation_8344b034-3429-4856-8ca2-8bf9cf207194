package com.eci.project.etmsOpFile.service;

import com.eci.common.enums.Enums;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.etmsOpFile.dao.EtmsOpFileDao;
import com.eci.project.etmsOpFile.entity.EtmsOpFileEntity;
import com.eci.project.etmsOpFile.validate.EtmsOpFileVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 业务附件Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-22
*/
@Service
@Slf4j
public class EtmsOpFileService implements EciBaseService<EtmsOpFileEntity> {

    @Autowired
    private EtmsOpFileDao etmsOpFileDao;

    @Autowired
    private EtmsOpFileVal etmsOpFileVal;


    @Override
    public TgPageInfo queryPageList(EtmsOpFileEntity entity) {
        EciQuery<EtmsOpFileEntity> eciQuery = EciQuery.buildQuery(entity);
        List<EtmsOpFileEntity> entities = etmsOpFileDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public EtmsOpFileEntity save(EtmsOpFileEntity entity) {
        // 返回实体对象
        EtmsOpFileEntity etmsOpFileEntity = null;
        etmsOpFileVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setModMark(Enums.ModMark.XZ.getValue());
            String suffix = entity.getFileUrl().substring(entity.getFileUrl().lastIndexOf("."));
            entity.setFileType(suffix);
            etmsOpFileEntity = etmsOpFileDao.insertOne(entity);

        }else{
            entity.setModMark(Enums.ModMark.XG.getValue());
            etmsOpFileEntity = etmsOpFileDao.updateByEntityId(entity);

        }
        return etmsOpFileEntity;
    }

    @Override
    public List<EtmsOpFileEntity> selectList(EtmsOpFileEntity entity) {
        return etmsOpFileDao.selectList(entity);
    }

    @Override
    public EtmsOpFileEntity selectOneById(Serializable id) {
        return etmsOpFileDao.selectById(id);
    }


    @Override
    public void insertBatch(List<EtmsOpFileEntity> list) {
        etmsOpFileDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return etmsOpFileDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return etmsOpFileDao.deleteById(id);
    }

}