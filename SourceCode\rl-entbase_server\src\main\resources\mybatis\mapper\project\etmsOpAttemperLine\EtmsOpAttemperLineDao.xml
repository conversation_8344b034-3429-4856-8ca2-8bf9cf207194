<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.etmsOpAttemperLine.dao.EtmsOpAttemperLineDao">
    <resultMap type="EtmsOpAttemperLineEntity" id="EtmsOpAttemperLineResult">
        <result property="guid" column="GUID"/>
        <result property="opNo" column="OP_NO"/>
        <result property="attGuid" column="ATT_GUID"/>
        <result property="carGuid" column="CAR_GUID"/>
        <result property="stationSeq" column="STATION_SEQ"/>
        <result property="stationType" column="STATION_TYPE"/>
        <result property="requestDate" column="REQUEST_DATE"/>
        <result property="opArea" column="OP_AREA"/>
        <result property="opAddress" column="OP_ADDRESS"/>
        <result property="opWh" column="OP_WH"/>
        <result property="opLink" column="OP_LINK"/>
        <result property="opTel" column="OP_TEL"/>
        <result property="opRequest" column="OP_REQUEST"/>
        <result property="createCompany" column="CREATE_COMPANY"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="attNo" column="ATT_NO"/>
        <result property="opAbbreviation" column="OP_ABBREVIATION"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="opLong" column="OP_LONG"/>
        <result property="opLat" column="OP_LAT"/>
        <result property="loadLong" column="LOAD_LONG"/>
        <result property="loadLat" column="LOAD_LAT"/>
    </resultMap>

    <sql id="selectEtmsOpAttemperLineEntityVo">
        select
            GUID,
            OP_NO,
            ATT_GUID,
            CAR_GUID,
            STATION_SEQ,
            STATION_TYPE,
            REQUEST_DATE,
            OP_AREA,
            OP_ADDRESS,
            OP_WH,
            OP_LINK,
            OP_TEL,
            OP_REQUEST,
            CREATE_COMPANY,
            CREATE_USER,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_DATE,
            ATT_NO,
            OP_ABBREVIATION,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            CREATE_USER_NAME,
            UPDATE_USER_NAME,
            OP_LONG,
            OP_LAT,
            LOAD_LONG,
            LOAD_LAT
        from ETMS_OP_ATTEMPER_LINE
    </sql>
</mapper>