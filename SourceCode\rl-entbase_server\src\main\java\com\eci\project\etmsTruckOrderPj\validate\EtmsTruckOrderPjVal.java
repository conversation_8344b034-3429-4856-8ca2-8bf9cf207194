package com.eci.project.etmsTruckOrderPj.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.etmsTruckOrderPj.entity.EtmsTruckOrderPjEntity;

import org.springframework.stereotype.Service;


/**
* Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-22
*/
@Service
public class EtmsTruckOrderPjVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(EtmsTruckOrderPjEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(EtmsTruckOrderPjEntity entity, BusinessType businessType) {

    }

}
