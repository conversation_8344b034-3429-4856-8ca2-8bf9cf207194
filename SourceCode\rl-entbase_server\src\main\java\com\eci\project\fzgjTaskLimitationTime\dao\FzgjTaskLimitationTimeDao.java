package com.eci.project.fzgjTaskLimitationTime.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.fzgjTaskLimitationTime.entity.FzgjTaskLimitationTimeEntity;


/**
* 作业环节基准时效Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-05-22
*/
public interface FzgjTaskLimitationTimeDao extends EciBaseDao<FzgjTaskLimitationTimeEntity> {

}