package com.eci.project.etmsOpFileQz.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 业务附件对象 ETMS_OP_FILE_QZ
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-04-17
*/
@ApiModel("业务附件")
@TableName("ETMS_OP_FILE_QZ")
@FieldNameConstants
public class EtmsOpFileQzEntity extends EciBaseEntity{
    /**
    * 主键
    */
    @ApiModelProperty("主键(50)")
    @TableId("GUID")
    private String guid;

    /**
    * 业务编号
    */
    @ApiModelProperty("业务编号(100)")
    @TableField("OP_NO")
    private String opNo;

    /**
    * 业务类型
    */
    @ApiModelProperty("业务类型(30)")
    @TableField("OP_TYPE")
    private String opType;

    /**
    * 文件类型
    */
    @ApiModelProperty("文件类型(20)")
    @TableField("FILE_TYPE")
    private String fileType;

    /**
    * 文件编号
    */
    @ApiModelProperty("文件编号(200)")
    @TableField("FILE_NO")
    private String fileNo;

    /**
    * 文件名称
    */
    @ApiModelProperty("文件名称(200)")
    @TableField("FILE_NAME")
    private String fileName;

    /**
    * 文件地址
    */
    @ApiModelProperty("文件地址(300)")
    @TableField("FILE_URL")
    private String fileUrl;

    /**
    * 文件说明
    */
    @ApiModelProperty("文件说明(200)")
    @TableField("MEMO")
    private String memo;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 编辑人
    */
    @ApiModelProperty("编辑人(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 编辑日期
    */
    @ApiModelProperty("编辑日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("编辑日期开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("编辑日期结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 创建企业
    */
    @ApiModelProperty("创建企业(20)")
    @TableField("CREATE_COMPANY")
    private String createCompany;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 是否删除
    */
    @ApiModelProperty("是否删除(1)")
    @TableField("IS_DELETE")
    private String isDelete;

    @ApiModelProperty("(100)")
    @TableField("DELETE_USER")
    private String deleteUser;

    @ApiModelProperty("(100)")
    @TableField("DELETE_USER_NAME")
    private String deleteUserName;

    @ApiModelProperty("(7)")
    @TableField("DELETE_DATE")
    private Date deleteDate;

    @ApiModelProperty("开始")
    @TableField(exist=false)
    private Date deleteDateStart;

    @ApiModelProperty("结束")
    @TableField(exist=false)
    private Date deleteDateEnd;

    /**
    * 节点类型
    */
    @ApiModelProperty("节点类型(50)")
    @TableField("STATION_TYPE")
    private String stationType;

    /**
    * 修改标志（0：不变，1：修改，2：删除，3：新增）
    */
    @ApiModelProperty("修改标志（0：不变，1：修改，2：删除，3：新增）(50)")
    @TableField("MOD_MARK")
    private String modMark;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public EtmsOpFileQzEntity() {
        this.setSubClazz(EtmsOpFileQzEntity.class);
    }

    public EtmsOpFileQzEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public EtmsOpFileQzEntity setOpNo(String opNo) {
        this.opNo = opNo;
        this.nodifySetFiled("opNo", opNo);
        return this;
    }

    public String getOpNo() {
        this.nodifyGetFiled("opNo");
        return opNo;
    }

    public EtmsOpFileQzEntity setOpType(String opType) {
        this.opType = opType;
        this.nodifySetFiled("opType", opType);
        return this;
    }

    public String getOpType() {
        this.nodifyGetFiled("opType");
        return opType;
    }

    public EtmsOpFileQzEntity setFileType(String fileType) {
        this.fileType = fileType;
        this.nodifySetFiled("fileType", fileType);
        return this;
    }

    public String getFileType() {
        this.nodifyGetFiled("fileType");
        return fileType;
    }

    public EtmsOpFileQzEntity setFileNo(String fileNo) {
        this.fileNo = fileNo;
        this.nodifySetFiled("fileNo", fileNo);
        return this;
    }

    public String getFileNo() {
        this.nodifyGetFiled("fileNo");
        return fileNo;
    }

    public EtmsOpFileQzEntity setFileName(String fileName) {
        this.fileName = fileName;
        this.nodifySetFiled("fileName", fileName);
        return this;
    }

    public String getFileName() {
        this.nodifyGetFiled("fileName");
        return fileName;
    }

    public EtmsOpFileQzEntity setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
        this.nodifySetFiled("fileUrl", fileUrl);
        return this;
    }

    public String getFileUrl() {
        this.nodifyGetFiled("fileUrl");
        return fileUrl;
    }

    public EtmsOpFileQzEntity setMemo(String memo) {
        this.memo = memo;
        this.nodifySetFiled("memo", memo);
        return this;
    }

    public String getMemo() {
        this.nodifyGetFiled("memo");
        return memo;
    }

    public EtmsOpFileQzEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public EtmsOpFileQzEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public EtmsOpFileQzEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public EtmsOpFileQzEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public EtmsOpFileQzEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public EtmsOpFileQzEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public EtmsOpFileQzEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public EtmsOpFileQzEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public EtmsOpFileQzEntity setCreateCompany(String createCompany) {
        this.createCompany = createCompany;
        this.nodifySetFiled("createCompany", createCompany);
        return this;
    }

    public String getCreateCompany() {
        this.nodifyGetFiled("createCompany");
        return createCompany;
    }

    public EtmsOpFileQzEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public EtmsOpFileQzEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public EtmsOpFileQzEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public EtmsOpFileQzEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public EtmsOpFileQzEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public EtmsOpFileQzEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public EtmsOpFileQzEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public EtmsOpFileQzEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public EtmsOpFileQzEntity setIsDelete(String isDelete) {
        this.isDelete = isDelete;
        this.nodifySetFiled("isDelete", isDelete);
        return this;
    }

    public String getIsDelete() {
        this.nodifyGetFiled("isDelete");
        return isDelete;
    }

    public EtmsOpFileQzEntity setDeleteUser(String deleteUser) {
        this.deleteUser = deleteUser;
        this.nodifySetFiled("deleteUser", deleteUser);
        return this;
    }

    public String getDeleteUser() {
        this.nodifyGetFiled("deleteUser");
        return deleteUser;
    }

    public EtmsOpFileQzEntity setDeleteUserName(String deleteUserName) {
        this.deleteUserName = deleteUserName;
        this.nodifySetFiled("deleteUserName", deleteUserName);
        return this;
    }

    public String getDeleteUserName() {
        this.nodifyGetFiled("deleteUserName");
        return deleteUserName;
    }

    public EtmsOpFileQzEntity setDeleteDate(Date deleteDate) {
        this.deleteDate = deleteDate;
        this.nodifySetFiled("deleteDate", deleteDate);
        return this;
    }

    public Date getDeleteDate() {
        this.nodifyGetFiled("deleteDate");
        return deleteDate;
    }

    public EtmsOpFileQzEntity setDeleteDateStart(Date deleteDateStart) {
        this.deleteDateStart = deleteDateStart;
        this.nodifySetFiled("deleteDateStart", deleteDateStart);
        return this;
    }

    public Date getDeleteDateStart() {
        this.nodifyGetFiled("deleteDateStart");
        return deleteDateStart;
    }

    public EtmsOpFileQzEntity setDeleteDateEnd(Date deleteDateEnd) {
        this.deleteDateEnd = deleteDateEnd;
        this.nodifySetFiled("deleteDateEnd", deleteDateEnd);
        return this;
    }

    public Date getDeleteDateEnd() {
        this.nodifyGetFiled("deleteDateEnd");
        return deleteDateEnd;
    }
    public EtmsOpFileQzEntity setStationType(String stationType) {
        this.stationType = stationType;
        this.nodifySetFiled("stationType", stationType);
        return this;
    }

    public String getStationType() {
        this.nodifyGetFiled("stationType");
        return stationType;
    }

    public EtmsOpFileQzEntity setModMark(String modMark) {
        this.modMark = modMark;
        this.nodifySetFiled("modMark", modMark);
        return this;
    }

    public String getModMark() {
        this.nodifyGetFiled("modMark");
        return modMark;
    }

}
