package com.eci.project.crmCustomerRole.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.crmCustomerRole.service.CrmCustomerRoleService;
import com.eci.project.crmCustomerRole.entity.CrmCustomerRoleEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 业务伙伴角色Controller
*
* @<NAME_EMAIL>
* @date 2025-04-29
*/
@Api(tags = "业务伙伴角色")
@RestController
@RequestMapping("/crmCustomerRole")
public class CrmCustomerRoleController extends EciBaseController {

    @Autowired
    private CrmCustomerRoleService crmCustomerRoleService;


    @ApiOperation("业务伙伴角色:保存")
    @EciLog(title = "业务伙伴角色:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody CrmCustomerRoleEntity entity){
        CrmCustomerRoleEntity crmCustomerRoleEntity =crmCustomerRoleService.save(entity);
        return ResponseMsgUtil.success(10001,crmCustomerRoleEntity);
    }


    @ApiOperation("业务伙伴角色:查询列表")
    @EciLog(title = "业务伙伴角色:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody CrmCustomerRoleEntity entity){
        List<CrmCustomerRoleEntity> crmCustomerRoleEntities = crmCustomerRoleService.selectList(entity);
        return ResponseMsgUtil.success(10001,crmCustomerRoleEntities);
    }


    @ApiOperation("业务伙伴角色:分页查询列表")
    @EciLog(title = "业务伙伴角色:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody CrmCustomerRoleEntity entity){
        TgPageInfo tgPageInfo = crmCustomerRoleService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("业务伙伴角色:根据ID查一条")
    @EciLog(title = "业务伙伴角色:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody CrmCustomerRoleEntity entity){
        CrmCustomerRoleEntity  crmCustomerRoleEntity = crmCustomerRoleService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,crmCustomerRoleEntity);
    }


    @ApiOperation("业务伙伴角色:根据ID删除一条")
    @EciLog(title = "业务伙伴角色:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody CrmCustomerRoleEntity entity){
        int count = crmCustomerRoleService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("业务伙伴角色:根据ID字符串删除多条")
    @EciLog(title = "业务伙伴角色:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody CrmCustomerRoleEntity entity) {
        int count = crmCustomerRoleService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}