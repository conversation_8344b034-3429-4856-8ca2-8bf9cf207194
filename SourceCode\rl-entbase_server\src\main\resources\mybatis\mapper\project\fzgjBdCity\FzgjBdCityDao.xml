<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.fzgjBdCity.dao.FzgjBdCityDao">
    <resultMap type="FzgjBdCityEntity" id="FzgjBdCityResult">
        <result property="guid" column="GUID"/>
        <result property="name" column="NAME"/>
        <result property="provinceId" column="PROVINCE_ID"/>
        <result property="zipCode" column="ZIP_CODE"/>
        <result property="memo" column="MEMO"/>
        <result property="status" column="STATUS"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="seq" column="SEQ"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="enName" column="EN_NAME"/>
    </resultMap>

    <sql id="selectFzgjBdCityEntityVo">
        select
            GUID,
            NAME,
            PROVINCE_ID,
            ZIP_CODE,
            MEMO,
            STATUS,
            CREATE_USER,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_DATE,
            SEQ,
            CREATE_USER_NAME,
            UPDATE_USER_NAME,
            EN_NAME
        from FZGJ_BD_CITY
    </sql>
</mapper>