<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.omsISlResult.dao.OmsISlResultDao">
    <resultMap type="OmsISlResultEntity" id="OmsISlResultResult">
        <result property="guid" column="GUID"/>
        <result property="workNo" column="WORK_NO"/>
        <result property="orderNo" column="ORDER_NO"/>
        <result property="result" column="RESULT"/>
        <result property="sysCode" column="SYS_CODE"/>
        <result property="docType" column="DOC_TYPE"/>
        <result property="docNo" column="DOC_NO"/>
        <result property="trnDate" column="TRN_DATE"/>
        <result property="opFlag" column="OP_FLAG"/>
        <result property="opDate" column="OP_DATE"/>
        <result property="opResult" column="OP_RESULT"/>
        <result property="xzwtNo" column="XZWT_NO"/>
        <result property="bizRegId" column="BIZ_REG_ID"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
    </resultMap>

    <sql id="selectOmsISlResultEntityVo">
        select
            GUID,
            WORK_NO,
            ORDER_NO,
            RESULT,
            SYS_CODE,
            DOC_TYPE,
            DOC_NO,
            TRN_DATE,
            OP_FLAG,
            OP_DATE,
            OP_RESULT,
            XZWT_NO,
            BIZ_REG_ID,
            COMPANY_CODE,
            COMPANY_NAME
        from OMS_I_SL_RESULT
    </sql>
</mapper>