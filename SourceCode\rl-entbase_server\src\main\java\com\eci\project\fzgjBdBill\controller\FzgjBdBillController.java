package com.eci.project.fzgjBdBill.controller;

import com.eci.common.ResponseMsgUtilX;
import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjBdBill.entity.YeWuChanPingQueryDto;
import com.eci.project.fzgjBdBill.service.FzgjBdBillService;
import com.eci.project.fzgjBdBill.entity.FzgjBdBillEntity;
import com.eci.project.fzgjBdOpType.entity.FzgjBdOpTypeEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 * 单据类型Controller
 *
 * @<NAME_EMAIL>
 * @date 2025-03-19
 */
@Api(tags = "单据类型")
@RestController
@RequestMapping("/fzgjBdBill")
public class FzgjBdBillController extends EciBaseController {

    @Autowired
    private FzgjBdBillService fzgjBdBillService;


    @ApiOperation("单据类型:保存")
    @EciLog(title = "单据类型:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody FzgjBdBillEntity entity) {
        FzgjBdBillEntity fzgjBdBillEntity = fzgjBdBillService.save(entity);
        return ResponseMsgUtil.success(10001, fzgjBdBillEntity);
    }


    @ApiOperation("单据类型:查询列表")
    @EciLog(title = "单据类型:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjBdBillEntity entity) {
        List<FzgjBdBillEntity> fzgjBdBillEntities = fzgjBdBillService.selectList(entity);
        return ResponseMsgUtil.success(10001, fzgjBdBillEntities);
    }


    @ApiOperation("单据类型:查询列表")
    @EciLog(title = "单据类型:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/getTreeRoot")
    @EciAction()
    public ResponseMsg getTreeRoot(@RequestBody FzgjBdBillEntity entity) {
        return ResponseMsgUtil.success(10001, fzgjBdBillService.getTreeRoot());
    }

    @ApiOperation("单据类型:查询列表")
    @EciLog(title = "单据类型:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/getTreeDataByRootId")
    @EciAction()
    public ResponseMsg getTreeDataByRootId(@RequestBody String jsonString) {
        return ResponseMsgUtil.success(10001, fzgjBdBillService.getTreeDataByRootId(jsonString));
    }

    @ApiOperation("单据类型:查询列表")
    @EciLog(title = "单据类型:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/getNodeTree2ItemInfoById")
    @EciAction()
    public ResponseMsg getNodeTree2ItemInfoById(@RequestBody String jsonString) {
        return ResponseMsgUtil.success(10001, fzgjBdBillService.getNodeTree2ItemInfoById(jsonString));
    }


    @ApiOperation("单据类型-业务产品:分页查询列表")
    @EciLog(title = "单据类型-业务产品:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/queryYeWuChanPingTablePageList")
    @EciAction()
    public ResponseMsg queryYeWuChanPingTablePageList(@RequestBody YeWuChanPingQueryDto entity) {

        TgPageInfo tgPageInfo = fzgjBdBillService.queryYeWuChanPingTablePageList(entity);
        return ResponseMsgUtilX.success(10001, tgPageInfo);
    }


    @ApiOperation("单据类型:根据ID查一条")
    @EciLog(title = "单据类型:查询表单", businessType = BusinessType.SELECT)
    @PostMapping("/getFormDeatil")
    @EciAction()
    public ResponseMsg getFormDeatil(@RequestBody String jsonString) {
        FzgjBdOpTypeEntity entity = fzgjBdBillService.getFormDeatil(jsonString);
        return ResponseMsgUtil.success(10001, entity);
    }


    @ApiOperation("单据类型:根据ID查一条")
    @EciLog(title = "单据类型:查询表单", businessType = BusinessType.SELECT)
    @PostMapping("/queryYeWuChanPingTablePageListServiceType")
    @EciAction()
    public ResponseMsg queryYeWuChanPingTablePageListServiceType(@RequestBody String jsonString) {
        TgPageInfo tgPageInfo = fzgjBdBillService.queryYeWuChanPingTablePageListServiceType(jsonString);
        return ResponseMsgUtilX.success(10001, tgPageInfo);
    }


    @ApiOperation("单据类型:分页查询列表")
    @EciLog(title = "单据类型:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjBdBillEntity entity) {

        TgPageInfo tgPageInfo = fzgjBdBillService.queryPageList(entity);
        return ResponseMsgUtilX.success(10001, tgPageInfo);
    }


    @ApiOperation("单据类型:根据ID查一条")
    @EciLog(title = "单据类型:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjBdBillEntity entity) {
        FzgjBdBillEntity fzgjBdBillEntity = fzgjBdBillService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001, fzgjBdBillEntity);
    }


    @ApiOperation("单据类型:根据ID删除一条")
    @EciLog(title = "单据类型:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjBdBillEntity entity) {
        int count = fzgjBdBillService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001, count);
    }


    @ApiOperation("单据类型:根据ID字符串删除多条")
    @EciLog(title = "单据类型:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjBdBillEntity entity) {
        int count = fzgjBdBillService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001, count);
    }


}