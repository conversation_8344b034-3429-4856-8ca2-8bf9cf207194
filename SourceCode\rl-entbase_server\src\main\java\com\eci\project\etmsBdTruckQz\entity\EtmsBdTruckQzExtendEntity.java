package com.eci.project.etmsBdTruckQz.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.eci.common.utils.ExportUtils.Excel;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;

public class EtmsBdTruckQzExtendEntity extends EtmsBdTruckQzEntity {
    @Override
    protected void addConvertMap() {
        convertMap.put(Fields.isGk, () -> "YNKey");
        convertMap.put(Fields.gpsMode, () -> "GPS_TYPE");
        convertMap.put(Fields.status, () -> "CAR_STATUS");
        convertMap.put(Fields.isUser, () -> "IS_USE");
        convertMap.put(Fields.checkStatus, () -> "TRUCK_CHECK_STATUS");
        convertMap.put(Fields.carLongType, () -> "CAR_LONG_TYPE");
        convertMap.put(Fields.createCompany, () -> "EP");
    }
    private String driverAtt;
    public String getDriverAtt() {
        return driverAtt;
    }
    public void setDriverAtt(String driverAtt) {
        this.driverAtt = driverAtt;
    }

    private String newCllx;
    public String getNewCllx() {
        return newCllx;
    }
    public void setNewCllx(String newCllx) {
        this.newCllx = newCllx;
    }

    private String newClcc;
    public String getNewClcc() {
        return newClcc;
    }
    public void setNewClcc(String newClcc) {
        this.newClcc = newClcc;
    }

    private String driverName;
    public String getDriverName() {
        return driverName;
    }
    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    public String  createCompanyName;
    public String getCreateCompanyName() {
        return createCompanyName;
    }
    public void setCreateCompanyName(String createCompanyName) {
        this.createCompanyName = createCompanyName;
    }

    private String partnerName;
    public String getPartnerName() {
        return partnerName;
    }
    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }
}
