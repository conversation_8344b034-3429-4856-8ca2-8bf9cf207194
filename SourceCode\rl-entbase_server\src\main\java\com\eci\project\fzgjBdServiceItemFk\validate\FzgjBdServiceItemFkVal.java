package com.eci.project.fzgjBdServiceItemFk.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjBdServiceItemFk.entity.FzgjBdServiceItemFkEntity;

import org.springframework.stereotype.Service;


/**
* 企业服务项目对应反馈页面Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-14
*/
@Service
public class FzgjBdServiceItemFkVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjBdServiceItemFkEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjBdServiceItemFkEntity entity, BusinessType businessType) {

    }

}
