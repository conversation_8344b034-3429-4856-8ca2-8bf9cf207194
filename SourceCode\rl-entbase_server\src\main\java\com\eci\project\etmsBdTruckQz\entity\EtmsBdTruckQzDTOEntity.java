package com.eci.project.etmsBdTruckQz.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.eci.common.utils.ExportUtils.Excel;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;

public class EtmsBdTruckQzDTOEntity extends EciBaseEntity {
    @Excel(value = "车牌号",order = 1)
    private String truckNo;
    @Excel(value = "车辆类型",order = 2)
    private String newCllx;
    @Excel(value = "车辆规格",order = 3)
    private String newClcc;

    public String getIsGk() {
        return isGk;
    }

    public void setIsGk(String isGk) {
        this.isGk = isGk;
    }

    private String isGk;
    @Excel(value = "自有",order = 4)
    private String isGkName;

    @Excel(value = "车主业务伙伴",order = 5)
    private String partnerGuid;

    public String getGpsMode() {
        return gpsMode;
    }

    public void setGpsMode(String gpsMode) {
        this.gpsMode = gpsMode;
    }

    private String gpsMode;
    @Excel(value = "定位方式",order = 6)
    private String gpsModeName;

    @Excel(value = "定位设备编号",order = 7)
    private String gpsNo;

    @Excel(value = "默认驾驶人",order = 8)
    private String driverName;

    public String getCheckStatus() {
        return checkStatus;
    }

    public void setCheckStatus(String checkStatus) {
        this.checkStatus = checkStatus;
    }

    private String checkStatus;
    @Excel(value = "审批状态",order = 9)
    private String checkStatusName;

    @Excel(value = "退回原因",order = 10)
    private String checkRmk;
    @Excel(value = "油耗系数",order = 11)
    private BigDecimal llOil;
    @Excel(value = "行驶证附件",order = 12)
    private String driverAtt;

    public String getIsUser() {
        return isUser;
    }

    public void setIsUser(String isUser) {
        this.isUser = isUser;
    }

    private String isUser;
    @Excel(value = "启用",order = 13)
    private String isUserName;
    @Excel(value = "创建时间",order = 14)
    private Date createDate;
    @Excel(value = "创建人",order = 15)
    private String createUserName;
    @Excel(value = "创建人部门",order = 16)
    public String  createCompanyName;
    @Excel(value = "最后修改时间",order = 17)
    private Date updateDate;
    @Excel(value = "修改人",order = 18)
    private String updateUserName;
    @Excel(value = "最后修改人部门",order = 19)
    private String orgDepName;

    public String getTruckNo() {
        return truckNo;
    }

    public void setTruckNo(String truckNo) {
        this.truckNo = truckNo;
    }

    public String getNewCllx() {
        return newCllx;
    }

    public void setNewCllx(String newCllx) {
        this.newCllx = newCllx;
    }

    public String getNewClcc() {
        return newClcc;
    }

    public void setNewClcc(String newClcc) {
        this.newClcc = newClcc;
    }

    public String getIsGkName() {
        return isGkName;
    }

    public void setIsGkName(String isGkName) {
        this.isGkName = isGkName;
    }

    public String getPartnerGuid() {
        return partnerGuid;
    }

    public void setPartnerGuid(String partnerGuid) {
        this.partnerGuid = partnerGuid;
    }

    public String getGpsModeName() {
        return gpsModeName;
    }

    public void setGpsModeName(String gpsModeName) {
        this.gpsModeName = gpsModeName;
    }

    public String getGpsNo() {
        return gpsNo;
    }

    public void setGpsNo(String gpsNo) {
        this.gpsNo = gpsNo;
    }

    public String getDriverName() {
        return driverName;
    }

    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    public String getCheckStatusName() {
        return checkStatusName;
    }

    public void setCheckStatusName(String checkStatusName) {
        this.checkStatusName = checkStatusName;
    }

    public String getCheckRmk() {
        return checkRmk;
    }

    public void setCheckRmk(String checkRmk) {
        this.checkRmk = checkRmk;
    }

    public BigDecimal getLlOil() {
        return llOil;
    }

    public void setLlOil(BigDecimal llOil) {
        this.llOil = llOil;
    }

    public String getDriverAtt() {
        return driverAtt;
    }

    public void setDriverAtt(String driverAtt) {
        this.driverAtt = driverAtt;
    }

    public String getIsUserName() {
        return isUserName;
    }

    public void setIsUserName(String isUserName) {
        this.isUserName = isUserName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getCreateCompanyName() {
        return createCompanyName;
    }

    public void setCreateCompanyName(String createCompanyName) {
        this.createCompanyName = createCompanyName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }

    public String getOrgDepName() {
        return orgDepName;
    }

    public void setOrgDepName(String orgDepName) {
        this.orgDepName = orgDepName;
    }
}
