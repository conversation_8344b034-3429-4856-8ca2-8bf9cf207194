<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.fzgjBaseInformation.dao.FzgjBaseInformationDao">
    <resultMap type="FzgjBaseInformationEntity" id="FzgjBaseInformationResult">
        <result property="guid" column="GUID"/>
        <result property="title" column="TITLE"/>
        <result property="status" column="STATUS"/>
        <result property="infoType" column="INFO_TYPE"/>
        <result property="isRecommend" column="IS_RECOMMEND"/>
        <result property="author" column="AUTHOR"/>
        <result property="publishdate" column="PUBLISHDATE"/>
        <result property="content" column="CONTENT" />
        <result property="sort" column="SORT"/>
        <result property="attachmentUrl" column="ATTACHMENT_URL"/>
        <result property="remark" column="REMARK"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>

    <sql id="selectFzgjBaseInformationEntityVo">
        select
            GUID,
            TITLE,
            STATUS,
            INFO_TYPE,
            IS_RECOMMEND,
            AUTHOR,
            PUBLISHDATE,
            CONTENT,
            SORT,
            ATTACHMENT_URL,
            REMARK,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            CREATE_USER_NAME,
            UPDATE_USER_NAME,
            CREATE_DATE,
            UPDATE_DATE
        from FZGJ_BASE_INFORMATION
    </sql>
</mapper>