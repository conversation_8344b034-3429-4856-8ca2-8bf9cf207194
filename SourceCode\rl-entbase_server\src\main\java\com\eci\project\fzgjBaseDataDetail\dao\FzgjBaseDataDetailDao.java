package com.eci.project.fzgjBaseDataDetail.dao;

import com.eci.crud.dao.EciBaseDao;
import com.eci.project.fzgjBaseDataDetail.entity.FzgjBaseDataDetailEntity;
import com.eci.project.fzgjBdCity.entity.FzgjBdCityEntity;

import java.util.List;


/**
* 扩展基础资料明细Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-03-18
*/
public interface FzgjBaseDataDetailDao extends EciBaseDao<FzgjBaseDataDetailEntity> {
    List<FzgjBaseDataDetailEntity> selectListInfo(FzgjBaseDataDetailEntity entity);
}
