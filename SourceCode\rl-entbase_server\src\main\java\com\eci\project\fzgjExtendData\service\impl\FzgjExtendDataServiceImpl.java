package com.eci.project.fzgjExtendData.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.util.StringUtils;
import com.eci.common.web.BllContext;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjExtendData.dao.FzgjExtendDataDao;
import com.eci.project.fzgjExtendData.entity.FzgjExtendDataEntity;
import com.eci.project.fzgjExtendData.entity.FzgjExtendDataPageEntity;
import com.eci.project.fzgjExtendData.service.IFzgjExtendDataService;
import com.eci.project.fzgjExtendData.validate.FzgjExtendDataVal;
import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 扩展基础资料Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-03-19
 */
@Service
@Slf4j
public class FzgjExtendDataServiceImpl implements IFzgjExtendDataService {
    @Autowired
    private FzgjExtendDataDao fzgjExtendDataDao;

    @Autowired
    private FzgjExtendDataVal fzgjExtendDataVal;


    @Override
    public TgPageInfo queryPageList(FzgjExtendDataEntity entity) {
        entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
        startPage();
        List<FzgjExtendDataPageEntity> entities = fzgjExtendDataDao.selectListInfo(entity);
        return EciQuery.getPageInfo(entities);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjExtendDataEntity save(FzgjExtendDataEntity entity) {
        // 返回实体对象
        FzgjExtendDataEntity fzgjExtendDataEntity = null;
        fzgjExtendDataVal.saveValidate(entity, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setGuid(IdWorker.get32UUID());
            entity.setCreateDate(new java.util.Date());
            entity.setUpdateDate(new java.util.Date());
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
            entity.setNodeCode(UserContext.getUserInfo().getDeptCode());
            entity.setNodeName(UserContext.getUserInfo().getDeptName());
            entity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            entity.setCompanyName(UserContext.getUserInfo().getCompanyName());
            fzgjExtendDataEntity = fzgjExtendDataDao.insertOne(entity);

        } else {
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            entity.setUpdateDate(new java.util.Date());
            fzgjExtendDataEntity = fzgjExtendDataDao.updateByEntityId(entity);
        }
        return fzgjExtendDataEntity;
    }

    @Override
    public List<FzgjExtendDataEntity> selectList(FzgjExtendDataEntity entity) {
        return fzgjExtendDataDao.selectList(entity);
    }

    @Override
    public FzgjExtendDataEntity selectOneById(Serializable id) {
        return fzgjExtendDataDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjExtendDataEntity> list) {
        fzgjExtendDataDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjExtendDataDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjExtendDataDao.deleteById(id);
    }

}