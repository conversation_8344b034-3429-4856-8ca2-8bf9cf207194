package com.eci.project.etmsOpFileQz.controller;

import com.eci.common.BaseProperties;
import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.etmsOpFileQz.service.EtmsOpFileQzService;
import com.eci.project.etmsOpFileQz.entity.EtmsOpFileQzEntity;
import com.eci.project.fzgjBaseInformation.entity.FzgjBaseInformationEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.URLConnection;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.UUID;

/**
* 业务附件Controller
*
* @<NAME_EMAIL>
* @date 2025-04-17
*/
@Api(tags = "业务附件")
@RestController
@RequestMapping("/etmsOpFileQz")
public class EtmsOpFileQzController extends EciBaseController {

    @Autowired
    private EtmsOpFileQzService etmsOpFileQzService;


    @ApiOperation("业务附件:保存")
    @EciLog(title = "业务附件:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody EtmsOpFileQzEntity entity){
        EtmsOpFileQzEntity etmsOpFileQzEntity =etmsOpFileQzService.save(entity);
        return ResponseMsgUtil.success(10001,etmsOpFileQzEntity);
    }


    @ApiOperation("业务附件:查询列表")
    @EciLog(title = "业务附件:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody EtmsOpFileQzEntity entity){
        List<EtmsOpFileQzEntity> etmsOpFileQzEntities = etmsOpFileQzService.selectList(entity);
        return ResponseMsgUtil.success(10001,etmsOpFileQzEntities);
    }


    @ApiOperation("业务附件:分页查询列表")
    @EciLog(title = "业务附件:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody EtmsOpFileQzEntity entity){
        TgPageInfo tgPageInfo = etmsOpFileQzService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("业务附件:根据ID查一条")
    @EciLog(title = "业务附件:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody EtmsOpFileQzEntity entity){
        EtmsOpFileQzEntity  etmsOpFileQzEntity = etmsOpFileQzService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,etmsOpFileQzEntity);
    }


    @ApiOperation("业务附件:根据ID删除一条")
    @EciLog(title = "业务附件:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody EtmsOpFileQzEntity entity){
        int count = etmsOpFileQzService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("业务附件:根据ID字符串删除多条")
    @EciLog(title = "业务附件:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody EtmsOpFileQzEntity entity) {
        int count = etmsOpFileQzService.deleteByList(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }

    @ApiOperation("业务附件:上传附件")
    @EciLog(title = "业务附件:上传附件", businessType = BusinessType.SELECT)
    @PostMapping("/upload")
    @EciAction()
    public ResponseMsg upload(@RequestParam MultipartFile file, @RequestParam String type){
        String originalFilename = file.getOriginalFilename();
        String suffix = originalFilename.substring(originalFilename.lastIndexOf("."));
        String fileName = UUID.randomUUID()+suffix;

        Path currentDir = Paths.get(System.getProperty("user.dir"));
        String filepath="/OpFilesQz/"+type+"/"+DateUtils.getDate()+"/";
        String basePath = BaseProperties.getFilepath()+filepath;
        File dir = new File(basePath);
        if(!dir.exists()){
            dir.mkdirs();
        }
        //Path outputPath = currentDir.resolve(basePath);
        try {
            file.transferTo(new File(basePath+fileName));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return ResponseMsgUtil.success(10001,filepath+fileName);
    }

    @ApiOperation("业务附件:删除文件")
    @EciLog(title = "业务附件:删除文件", businessType = BusinessType.DELETE)
    @PostMapping("/deleteFile")
    @EciAction()
    public ResponseMsg deleteFile(@RequestBody EtmsOpFileQzEntity entity) {
        File file = new File(entity.getFileUrl());
        boolean isdelete=file.delete();
        return ResponseMsgUtil.success(10001,isdelete);
    }

    @ApiOperation("附件:附件预览")
    @EciLog(title = "附件:附件预览", businessType = BusinessType.OTHER)
    @GetMapping("/previewAttr")
    @EciAction()
    public void previewAttr(String fid) throws Exception {
        EtmsOpFileQzEntity entity = etmsOpFileQzService.selectOneById(fid);
        if (entity == null) {throw new Exception("文件不存在");}

        String basePath = BaseProperties.getFilepath() + entity.getFileUrl();
        File file = new File(basePath);

        if (!file.exists()) {
            throw new Exception("文件不存在");
        }

        HttpServletResponse response = ServletUtils.getResponse();

        // 设置 MIME 类型（可选，但推荐）
        String contentType = URLConnection.guessContentTypeFromName(file.getName());
        if (contentType == null) {
            contentType = "application/octet-stream";
        }
        response.setContentType(contentType);

        // 关键修改：使用 inline 表示内联预览
        response.setHeader("Content-Disposition", "inline;filename=" + file.getName());

        try (FileInputStream inStream = new FileInputStream(file);
             ServletOutputStream outputStream = response.getOutputStream()) {

            byte[] buf = new byte[4096];
            int readLength;
            while ((readLength = inStream.read(buf)) != -1) {
                outputStream.write(buf, 0, readLength);
            }

            outputStream.flush();
        } catch (Exception ex) {
            // 可记录日志或处理异常
            ex.printStackTrace();
        }
    }
}