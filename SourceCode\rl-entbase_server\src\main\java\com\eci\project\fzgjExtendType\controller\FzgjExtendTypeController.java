package com.eci.project.fzgjExtendType.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.project.fzgjExtendType.service.IFzgjExtendTypeService;
import com.eci.project.fzgjExtendType.entity.FzgjExtendTypeEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.EciLog;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


/**
* 扩展基础资料类型Controller
*
* @<NAME_EMAIL>
* @date 2025-03-19
*/
@Api(tags = "扩展基础资料类型")
@RestController
@RequestMapping("/fzgjExtendType")
public class FzgjExtendTypeController extends EciBaseController {

    @Autowired
    private IFzgjExtendTypeService fzgjExtendTypeService;

    /**
    * 根据实体插入数据库
    * @param entity
    * @return
    */
    @ApiOperation("扩展基础资料类型:保存")
    @EciLog(title = "扩展基础资料类型:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public ResponseMsg save(@RequestBody FzgjExtendTypeEntity entity){
        return ResponseMsgUtil.success(10001,fzgjExtendTypeService.save(entity));
    }

    /**
    * 根据条件查询一个集合
    * @param entity
    * @return
    */
    @ApiOperation("扩展基础资料类型:查询列表")
    @EciLog(title = "扩展基础资料类型:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    public ResponseMsg selectList(@RequestBody FzgjExtendTypeEntity entity){
        return ResponseMsgUtil.success(10001,fzgjExtendTypeService.selectList(entity));
    }

    /**
    * 分页查询
    * @param entity
    * @return
    */
    @ApiOperation("扩展基础资料类型:分页查询列表")
    @EciLog(title = "扩展基础资料类型:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    public ResponseMsg selectPageList(@RequestBody FzgjExtendTypeEntity entity){
        return ResponseMsgUtil.success(10001,fzgjExtendTypeService.queryPageList(entity));
    }

    /**
    * 根据ID查一条记录
    * @param entity
    * @return
    */
    @ApiOperation("扩展基础资料类型:根据ID查一条")
    @EciLog(title = "扩展基础资料类型:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    public ResponseMsg selectOneById(@RequestBody FzgjExtendTypeEntity entity){
        return ResponseMsgUtil.success(10001,fzgjExtendTypeService.selectOneById(entity.getGuid()));
    }


    /**
    * 根据ID删除
    * @param entity
    * @return
    */
    @ApiOperation("扩展基础资料类型:根据ID删除一条")
    @EciLog(title = "扩展基础资料类型:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    public ResponseMsg deleteById(@RequestBody FzgjExtendTypeEntity entity){
        return ResponseMsgUtil.success(10001,fzgjExtendTypeService.deleteById(entity.getGuid()));
    }

    /**
    * 根据IDS删除
    *
    * @param entity
    * @return
    */
    @ApiOperation("扩展基础资料类型:根据ID字符串删除多条")
    @EciLog(title = "扩展基础资料类型:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    public ResponseMsg deleteByIds(@RequestBody FzgjExtendTypeEntity entity) {
        return ResponseMsgUtil.success(10001, fzgjExtendTypeService.deleteByIds(entity.getIds()));
    }


}