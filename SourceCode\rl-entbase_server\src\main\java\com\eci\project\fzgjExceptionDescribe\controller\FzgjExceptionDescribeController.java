package com.eci.project.fzgjExceptionDescribe.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjExceptionDescribe.service.FzgjExceptionDescribeService;
import com.eci.project.fzgjExceptionDescribe.entity.FzgjExceptionDescribeEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 异常描述Controller
*
* @<NAME_EMAIL>
* @date 2025-04-01
*/
@Api(tags = "异常描述")
@RestController
@RequestMapping("/fzgjExceptionDescribe")
public class FzgjExceptionDescribeController extends EciBaseController {

    @Autowired
    private FzgjExceptionDescribeService fzgjExceptionDescribeService;


    @ApiOperation("异常描述:保存")
    @EciLog(title = "异常描述:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody FzgjExceptionDescribeEntity entity){
        FzgjExceptionDescribeEntity fzgjExceptionDescribeEntity =fzgjExceptionDescribeService.save(entity);
        return ResponseMsgUtil.success(10001,fzgjExceptionDescribeEntity);
    }


    @ApiOperation("异常描述:查询列表")
    @EciLog(title = "异常描述:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjExceptionDescribeEntity entity){
        List<FzgjExceptionDescribeEntity> fzgjExceptionDescribeEntities = fzgjExceptionDescribeService.selectList(entity);
        return ResponseMsgUtil.success(10001,fzgjExceptionDescribeEntities);
    }


    @ApiOperation("异常描述:分页查询列表")
    @EciLog(title = "异常描述:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjExceptionDescribeEntity entity){
        TgPageInfo tgPageInfo = fzgjExceptionDescribeService.queryPageList(entity);
        return ResponseMsgUtil.successPlus(10001,tgPageInfo);
    }


    @ApiOperation("异常描述:根据ID查一条")
    @EciLog(title = "异常描述:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjExceptionDescribeEntity entity){
        FzgjExceptionDescribeEntity  fzgjExceptionDescribeEntity = fzgjExceptionDescribeService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,fzgjExceptionDescribeEntity);
    }


    @ApiOperation("异常描述:根据ID删除一条")
    @EciLog(title = "异常描述:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjExceptionDescribeEntity entity){
        int count = fzgjExceptionDescribeService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("异常描述:根据ID字符串删除多条")
    @EciLog(title = "异常描述:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjExceptionDescribeEntity entity) {
        int count = fzgjExceptionDescribeService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}