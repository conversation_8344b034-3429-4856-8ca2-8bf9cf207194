package com.eci.project.fzgjExceptionType.validate;

import com.eci.common.util.StringUtils;
import com.eci.exception.BaseException;
import com.eci.log.enums.BusinessType;
import com.eci.project.fzgjExceptionType.entity.FzgjExceptionTypeEntity;
import org.springframework.stereotype.Service;


/**
 * 异常类型Val接口
 * 验证接口
 *
 * @<NAME_EMAIL>
 * @date 2025-04-02
 */
@Service
public class FzgjExceptionTypeVal {

    /**
     * 删除验证
     *
     * @param entity
     */
    public void deleteValidate(FzgjExceptionTypeEntity entity) {

    }

    /**
     * 保存验证
     *
     * @param entity
     */
    public void saveValidate(FzgjExceptionTypeEntity entity, BusinessType businessType) {

        // 异常类型代码
        if (StringUtils.isEmpty(entity.getCode())) {
            throw new BaseException("异常类型代码为空");
        }

        // 异常类型名称
        if (StringUtils.isEmpty(entity.getName())) {
            throw new BaseException("异常类型名称为空");
        }

        // 启用
        if (StringUtils.isEmpty(entity.getStatus())) {
            throw new BaseException("启用为空");
        }
    }

}
