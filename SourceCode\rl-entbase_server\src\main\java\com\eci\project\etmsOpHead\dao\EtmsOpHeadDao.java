package com.eci.project.etmsOpHead.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.etmsOpHead.entity.EtmsOpHeadEntity;


/**
* 平台业务主表Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-06-13
*/
public interface EtmsOpHeadDao extends EciBaseDao<EtmsOpHeadEntity> {

}