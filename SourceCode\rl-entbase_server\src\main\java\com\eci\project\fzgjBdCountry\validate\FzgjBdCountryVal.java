package com.eci.project.fzgjBdCountry.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjBdCountry.entity.FzgjBdCountryEntity;

import org.springframework.stereotype.Service;


/**
* 国家Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-03-14
*/
@Service
public class FzgjBdCountryVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjBdCountryEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjBdCountryEntity entity, BusinessType businessType) {

    }

}
