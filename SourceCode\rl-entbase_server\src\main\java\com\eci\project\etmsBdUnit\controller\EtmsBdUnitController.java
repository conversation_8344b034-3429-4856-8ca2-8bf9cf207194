package com.eci.project.etmsBdUnit.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.etmsBdUnit.service.EtmsBdUnitService;
import com.eci.project.etmsBdUnit.entity.EtmsBdUnitEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 计量单位Controller
*
* @<NAME_EMAIL>
* @date 2025-03-21
*/
@Api(tags = "计量单位")
@RestController
@RequestMapping("/etmsBdUnit")
public class EtmsBdUnitController extends EciBaseController {

    @Autowired
    private EtmsBdUnitService etmsBdUnitService;


    @ApiOperation("计量单位:保存")
    @EciLog(title = "计量单位:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody EtmsBdUnitEntity entity){
        EtmsBdUnitEntity etmsBdUnitEntity =etmsBdUnitService.save(entity);
        return ResponseMsgUtil.success(10001,etmsBdUnitEntity);
    }


    @ApiOperation("计量单位:查询列表")
    @EciLog(title = "计量单位:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody EtmsBdUnitEntity entity){
        List<EtmsBdUnitEntity> etmsBdUnitEntities = etmsBdUnitService.selectList(entity);
        return ResponseMsgUtil.success(10001,etmsBdUnitEntities);
    }


    @ApiOperation("计量单位:分页查询列表")
    @EciLog(title = "计量单位:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody EtmsBdUnitEntity entity){
        TgPageInfo tgPageInfo = etmsBdUnitService.queryPageList(entity);
        return ResponseMsgUtil.successPlus(10001,tgPageInfo);
    }


    @ApiOperation("计量单位:根据ID查一条")
    @EciLog(title = "计量单位:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody EtmsBdUnitEntity entity){
        EtmsBdUnitEntity  etmsBdUnitEntity = etmsBdUnitService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,etmsBdUnitEntity);
    }


    @ApiOperation("计量单位:根据ID删除一条")
    @EciLog(title = "计量单位:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody EtmsBdUnitEntity entity){
        int count = etmsBdUnitService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("计量单位:根据ID字符串删除多条")
    @EciLog(title = "计量单位:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody EtmsBdUnitEntity entity) {
        int count = etmsBdUnitService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}