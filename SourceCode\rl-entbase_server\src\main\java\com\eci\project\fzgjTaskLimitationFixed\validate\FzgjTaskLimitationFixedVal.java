package com.eci.project.fzgjTaskLimitationFixed.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjTaskLimitationFixed.entity.FzgjTaskLimitationFixedEntity;

import org.springframework.stereotype.Service;


/**
* 作业环节及时效标准条件Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-05-22
*/
@Service
public class FzgjTaskLimitationFixedVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjTaskLimitationFixedEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjTaskLimitationFixedEntity entity, BusinessType businessType) {

    }

}
