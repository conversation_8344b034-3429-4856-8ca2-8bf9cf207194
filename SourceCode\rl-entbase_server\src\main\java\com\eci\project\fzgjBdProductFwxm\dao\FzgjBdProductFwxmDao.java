package com.eci.project.fzgjBdProductFwxm.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.fzgjBdProductFwxm.entity.FzgjBdProductFwxmEntity;


/**
* 产品服务项目Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-04-01
*/
public interface FzgjBdProductFwxmDao extends EciBaseDao<FzgjBdProductFwxmEntity> {

}