package com.eci.project.fzgjBdRailwayStation.service;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjBdRailwayStation.dao.FzgjBdRailwayStationDao;
import com.eci.project.fzgjBdRailwayStation.entity.FzgjBdRailwayStationEntity;
import com.eci.project.fzgjBdRailwayStation.entity.FzgjBdRailwayStationPageEntity;
import com.eci.project.fzgjBdRailwayStation.validate.FzgjBdRailwayStationVal;

import com.eci.sso.login.entity.UserInfo;
import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 铁路站点Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-03-25
*/
@Service
@Slf4j
public class FzgjBdRailwayStationService implements EciBaseService<FzgjBdRailwayStationEntity> {

    @Autowired
    private FzgjBdRailwayStationDao fzgjBdRailwayStationDao;

    @Autowired
    private FzgjBdRailwayStationVal fzgjBdRailwayStationVal;


    @Override
    public TgPageInfo queryPageList(FzgjBdRailwayStationEntity entity) {
        startPage();
//        entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
        List<FzgjBdRailwayStationPageEntity> entities = fzgjBdRailwayStationDao.selectStationPageList(entity);
        return EciQuery.getPageInfo(entities);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjBdRailwayStationEntity save(FzgjBdRailwayStationEntity entity) {
        // 返回实体对象
        FzgjBdRailwayStationEntity fzgjBdRailwayStationEntity = null;
        fzgjBdRailwayStationVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setGuid(IdWorker.get32UUID());
            entity.setCreateDate(new java.util.Date());
            entity.setUpdateDate(new java.util.Date());
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
            entity.setNodeCode(UserContext.getUserInfo().getDeptCode());
            entity.setNodeName(UserContext.getUserInfo().getDeptName());
            entity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            entity.setCompanyName(UserContext.getUserInfo().getCompanyName());
            fzgjBdRailwayStationEntity = fzgjBdRailwayStationDao.insertOne(entity);
        } else {
            entity.setUpdateDate(new java.util.Date());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            fzgjBdRailwayStationEntity = fzgjBdRailwayStationDao.updateByEntityId(entity);
        }

        return fzgjBdRailwayStationEntity;
    }

    @Override
    public List<FzgjBdRailwayStationEntity> selectList(FzgjBdRailwayStationEntity entity) {
        return fzgjBdRailwayStationDao.selectList(entity);
    }

    @Override
    public FzgjBdRailwayStationPageEntity selectOneById(Serializable id) {
        return fzgjBdRailwayStationDao.selectOneByID(String.valueOf(id));
    }


    @Override
    public void insertBatch(List<FzgjBdRailwayStationEntity> list) {
        fzgjBdRailwayStationDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjBdRailwayStationDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjBdRailwayStationDao.deleteById(id);
    }

}