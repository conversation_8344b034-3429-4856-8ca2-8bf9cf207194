package com.eci.project.omsOrderFwxmWorkFkHzqd.entity;

import com.eci.project.omsOrderFwxmWorkFk.entity.OmsOrderFwxmWorkFkEntity;
import com.eci.project.omsOrderFwxmWorkFkHfd.entity.OmsOrderFwxmWorkFkHfdEntity;

import java.util.List;

/**
 * @ClassName: ReqOmsOrderFwxmWorkFkHzqdEntity
 * @Author: guangyan.mei
 * @Date: 2025/6/3 14:09
 * @Description: TODO
 */
public class ReqOmsOrderFwxmWorkFkHzqdEntity {

    public String getFiledName() {
        return filedName;
    }

    public void setFiledName(String filedName) {
        this.filedName = filedName;
    }

    // 报关类型
    public String filedName;
    public  OmsOrderFwxmWorkFkEntity entity;

    // 核注清单作业
    public List<OmsOrderFwxmWorkFkHzqdEntity> dtDateHZQD;

    // 金二核放单
    public List<OmsOrderFwxmWorkFkHfdEntity> dtDateJEHFD;


    public OmsOrderFwxmWorkFkEntity getEntity() {
        return entity;
    }

    public void setEntity(OmsOrderFwxmWorkFkEntity entity) {
        this.entity = entity;
    }

    public List<OmsOrderFwxmWorkFkHzqdEntity> getDtDateHZQD() {
        return dtDateHZQD;
    }

    public void setDtDateHZQD(List<OmsOrderFwxmWorkFkHzqdEntity> dtDateHZQD) {
        this.dtDateHZQD = dtDateHZQD;
    }

    public List<OmsOrderFwxmWorkFkHfdEntity> getDtDateJEHFD() {
        return dtDateJEHFD;
    }

    public void setDtDateJEHFD(List<OmsOrderFwxmWorkFkHfdEntity> dtDateJEHFD) {
        this.dtDateJEHFD = dtDateJEHFD;
    }


}
