package com.eci.project.fzgjTaskLimitation.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;

import java.util.Date;


/**
* 作业环节及标准时效对象 FZGJ_TASK_LIMITATION
*
* @<NAME_EMAIL>
* @date 2025-03-28
*/
@FieldNameConstants
public class FzgjTaskLimitationBaseEntity extends EciBaseEntity
{
	/**
	* GUID
	*/
	@ApiModelProperty("GUID(50)")
	@TableId("GUID")
	private String guid;

	/**
	* 编码
	*/
	@ApiModelProperty("编码(200)")
	@TableField("CODE")
	private String code;

	/**
	* 名称
	*/
	@ApiModelProperty("名称(200)")
	@TableField("NAME")
	private String name;

	/**
	* 时效基准参数编码
	*/
	@ApiModelProperty("时效基准参数编码(200)")
	@TableField("VALID_TIME_CODE")
	private String validTimeCode;

	/**
	* 目标编码（所属服务项目）
	*/
	@ApiModelProperty("目标编码（所属服务项目）(200)")
	@TableField("TARGET_CODE")
	private String targetCode;

	/**
	* 目标系统
	*/
	@ApiModelProperty("目标系统(50)")
	@TableField("TARGET_SYSTEM_CODE")
	private String targetSystemCode;

	/**
	* 达标时效
	*/
	@ApiModelProperty("达标时效(200)")
	@TableField("REACH_STANDDARD")
	private String reachStanddard;

	/**
	* 时效单位
	*/
	@ApiModelProperty("时效单位(200)")
	@TableField("TIME_UNIT")
	private String timeUnit;

	/**
	* 状态
	*/
	@ApiModelProperty("状态(1)")
	@TableField("STATUS")
	private String status;

	/**
	* 顺序
	*/
	@ApiModelProperty("顺序(22)")
	@TableField("SEQ")
	private Integer seq;

	/**
	* 备注
	*/
	@ApiModelProperty("备注(50)")
	@TableField("MEMO")
	private String memo;

	/**
	* 创建人
	*/
	@ApiModelProperty("创建人(50)")
	@TableField("CREATE_USER")
	private String createUser;

	/**
	* 创建人名称
	*/
	@ApiModelProperty("创建人名称(50)")
	@TableField("CREATE_USER_NAME")
	private String createUserName;

	/**
	* 创建时间
	*/
	@ApiModelProperty("创建时间(7)")
	@TableField("CREATE_DATE")
	private Date createDate;

	@ApiModelProperty("创建时间开始")
	@TableField(exist=false)
	private Date createDateStart;

	@ApiModelProperty("创建时间结束")
	@TableField(exist=false)
	private Date createDateEnd;

	/**
	* 修改人
	*/
	@ApiModelProperty("修改人(50)")
	@TableField("UPDATE_USER")
	private String updateUser;

	/**
	* 修改人名称
	*/
	@ApiModelProperty("修改人名称(50)")
	@TableField("UPDATE_USER_NAME")
	private String updateUserName;

	/**
	* 修改时间
	*/
	@ApiModelProperty("修改时间(7)")
	@TableField("UPDATE_DATE")
	private Date updateDate;

	@ApiModelProperty("修改时间开始")
	@TableField(exist=false)
	private Date updateDateStart;

	@ApiModelProperty("修改时间结束")
	@TableField(exist=false)
	private Date updateDateEnd;

	/**
	* 组织代码
	*/
	@ApiModelProperty("组织代码(50)")
	@TableField("NODE_CODE")
	private String nodeCode;

	/**
	* 组织名称
	*/
	@ApiModelProperty("组织名称(200)")
	@TableField("NODE_NAME")
	private String nodeName;

	/**
	* 公司代码
	*/
	@ApiModelProperty("公司代码(50)")
	@TableField("COMPANY_CODE")
	private String companyCode;

	/**
	* 公司名称
	*/
	@ApiModelProperty("公司名称(200)")
	@TableField("COMPANY_NAME")
	private String companyName;

	/**
	* 集团代码
	*/
	@ApiModelProperty("集团代码(50)")
	@TableField("GROUP_CODE")
	private String groupCode;

	/**
	* 集团名称
	*/
	@ApiModelProperty("集团名称(200)")
	@TableField("GROUP_NAME")
	private String groupName;

	/**
	* 目标编码（所属服务项目）
	*/
	@ApiModelProperty("目标编码（所属服务项目）(200)")
	@TableField("TARGET_NAME")
	private String targetName;

	/**
	* 英文名称，仅做存储
	*/
	@ApiModelProperty("英文名称，仅做存储(200)")
	@TableField("EN_NAME")
	private String enName;

	/**
	* 上游作业完成时间取值方式
	*/
	@ApiModelProperty("上游作业完成时间取值方式(10)")
	@TableField("SYZYWCQZFS")
	private String syzywcqzfs;

	@JsonIgnore
	@TableField(exist = false)
	private Class<?> clazz = this.getClass();

	public Class<?> getClazz() {
		return clazz;
	}

	public void setClazz(Class<?> clazz) {
		this.clazz = clazz;
	}

	public FzgjTaskLimitationBaseEntity setGuid(String guid) {
		this.guid = guid;
		return this;
	}

	public String getGuid() {
		return guid;
	}

	public FzgjTaskLimitationBaseEntity setCode(String code) {
		this.code = code;
		return this;
	}

	public String getCode() {
		return code;
	}

	public FzgjTaskLimitationBaseEntity setName(String name) {
		this.name = name;
		return this;
	}

	public String getName() {
		return name;
	}

	public FzgjTaskLimitationBaseEntity setValidTimeCode(String validTimeCode) {
		this.validTimeCode = validTimeCode;
		return this;
	}

	public String getValidTimeCode() {
		return validTimeCode;
	}

	public FzgjTaskLimitationBaseEntity setTargetCode(String targetCode) {
		this.targetCode = targetCode;
		return this;
	}

	public String getTargetCode() {
		return targetCode;
	}

	public FzgjTaskLimitationBaseEntity setTargetSystemCode(String targetSystemCode) {
		this.targetSystemCode = targetSystemCode;
		return this;
	}

	public String getTargetSystemCode() {
		return targetSystemCode;
	}

	public FzgjTaskLimitationBaseEntity setReachStanddard(String reachStanddard) {
		this.reachStanddard = reachStanddard;
		return this;
	}

	public String getReachStanddard() {
		return reachStanddard;
	}

	public FzgjTaskLimitationBaseEntity setTimeUnit(String timeUnit) {
		this.timeUnit = timeUnit;
		return this;
	}

	public String getTimeUnit() {
		return timeUnit;
	}

	public FzgjTaskLimitationBaseEntity setStatus(String status) {
		this.status = status;
		return this;
	}

	public String getStatus() {
		return status;
	}

	public FzgjTaskLimitationBaseEntity setSeq(Integer seq) {
		this.seq = seq;
		return this;
	}

	public Integer getSeq() {
		return seq;
	}

	public FzgjTaskLimitationBaseEntity setMemo(String memo) {
		this.memo = memo;
		return this;
	}

	public String getMemo() {
		return memo;
	}

	public FzgjTaskLimitationBaseEntity setCreateUser(String createUser) {
		this.createUser = createUser;
		return this;
	}

	public String getCreateUser() {
		return createUser;
	}

	public FzgjTaskLimitationBaseEntity setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
		return this;
	}

	public String getCreateUserName() {
		return createUserName;
	}

	public FzgjTaskLimitationBaseEntity setCreateDate(Date createDate) {
		this.createDate = createDate;
		return this;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public FzgjTaskLimitationBaseEntity setCreateDateStart(Date createDateStart) {
		this.createDateStart = createDateStart;
		return this;
	}

	public Date getCreateDateStart() {
		return createDateStart;
	}

	public FzgjTaskLimitationBaseEntity setCreateDateEnd(Date createDateEnd) {
		this.createDateEnd = createDateEnd;
		return this;
	}

	public Date getCreateDateEnd() {
		return createDateEnd;
	}
	public FzgjTaskLimitationBaseEntity setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
		return this;
	}

	public String getUpdateUser() {
		return updateUser;
	}

	public FzgjTaskLimitationBaseEntity setUpdateUserName(String updateUserName) {
		this.updateUserName = updateUserName;
		return this;
	}

	public String getUpdateUserName() {
		return updateUserName;
	}

	public FzgjTaskLimitationBaseEntity setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
		return this;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public FzgjTaskLimitationBaseEntity setUpdateDateStart(Date updateDateStart) {
		this.updateDateStart = updateDateStart;
		return this;
	}

	public Date getUpdateDateStart() {
		return updateDateStart;
	}

	public FzgjTaskLimitationBaseEntity setUpdateDateEnd(Date updateDateEnd) {
		this.updateDateEnd = updateDateEnd;
		return this;
	}

	public Date getUpdateDateEnd() {
		return updateDateEnd;
	}
	public FzgjTaskLimitationBaseEntity setNodeCode(String nodeCode) {
		this.nodeCode = nodeCode;
		return this;
	}

	public String getNodeCode() {
		return nodeCode;
	}

	public FzgjTaskLimitationBaseEntity setNodeName(String nodeName) {
		this.nodeName = nodeName;
		return this;
	}

	public String getNodeName() {
		return nodeName;
	}

	public FzgjTaskLimitationBaseEntity setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
		return this;
	}

	public String getCompanyCode() {
		return companyCode;
	}

	public FzgjTaskLimitationBaseEntity setCompanyName(String companyName) {
		this.companyName = companyName;
		return this;
	}

	public String getCompanyName() {
		return companyName;
	}

	public FzgjTaskLimitationBaseEntity setGroupCode(String groupCode) {
		this.groupCode = groupCode;
		return this;
	}

	public String getGroupCode() {
		return groupCode;
	}

	public FzgjTaskLimitationBaseEntity setGroupName(String groupName) {
		this.groupName = groupName;
		return this;
	}

	public String getGroupName() {
		return groupName;
	}

	public FzgjTaskLimitationBaseEntity setTargetName(String targetName) {
		this.targetName = targetName;
		return this;
	}

	public String getTargetName() {
		return targetName;
	}

	public FzgjTaskLimitationBaseEntity setEnName(String enName) {
		this.enName = enName;
		return this;
	}

	public String getEnName() {
		return enName;
	}

	public FzgjTaskLimitationBaseEntity setSyzywcqzfs(String syzywcqzfs) {
		this.syzywcqzfs = syzywcqzfs;
		return this;
	}

	public String getSyzywcqzfs() {
		return syzywcqzfs;
	}

}
