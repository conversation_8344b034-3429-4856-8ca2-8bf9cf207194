package com.eci.project.etmsBdTruck.dao;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.etmsBdTruck.entity.EtmsBdTruckDTO;
import com.eci.project.etmsBdTruck.entity.EtmsBdTruckEntity;
import com.eci.project.fzgjBdServiceItemPt.entity.TreeModel;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
* 车辆信息Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-04-11
*/
public interface EtmsBdTruckDao extends EciBaseDao<EtmsBdTruckEntity> {
    List<EtmsBdTruckDTO> queryPages(@Param(Constants.WRAPPER) Wrapper queryWrapper);
}
