package com.eci.common;

import lombok.Data;

import java.util.Objects;

@Data
public class CodeNameEntity extends  ZsrBaseEntity {
    private String code;
    private String name;


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        CodeNameEntity that = (CodeNameEntity) o;
        return Objects.equals(code, that.code) && Objects.equals(name, that.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(code, name);
    }

}
