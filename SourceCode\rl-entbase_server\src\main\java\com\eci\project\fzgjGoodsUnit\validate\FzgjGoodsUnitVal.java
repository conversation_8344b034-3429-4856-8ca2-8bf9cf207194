package com.eci.project.fzgjGoodsUnit.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjGoodsUnit.entity.FzgjGoodsUnitEntity;

import org.springframework.stereotype.Service;


/**
* 仓储货品单位Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-01
*/
@Service
public class FzgjGoodsUnitVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjGoodsUnitEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjGoodsUnitEntity entity, BusinessType businessType) {

    }

}
