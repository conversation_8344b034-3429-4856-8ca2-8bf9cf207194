package com.eci.project.fzgjCrmEnterpriseCom.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjCrmEnterpriseCom.entity.FzgjCrmEnterpriseComEntity;

import org.springframework.stereotype.Service;


/**
* 平台受理企业Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-03-13
*/
@Service
public class FzgjCrmEnterpriseComVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjCrmEnterpriseComEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjCrmEnterpriseComEntity entity, BusinessType businessType) {

    }

}
