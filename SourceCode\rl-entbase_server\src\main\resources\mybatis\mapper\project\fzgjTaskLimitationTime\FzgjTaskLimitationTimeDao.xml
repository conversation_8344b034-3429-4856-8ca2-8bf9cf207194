<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.fzgjTaskLimitationTime.dao.FzgjTaskLimitationTimeDao">
    <resultMap type="FzgjTaskLimitationTimeEntity" id="FzgjTaskLimitationTimeResult">
        <result property="guid" column="GUID"/>
        <result property="limitationGuid" column="LIMITATION_GUID"/>
        <result property="validTimeCode" column="VALID_TIME_CODE"/>
        <result property="reachStanddard" column="REACH_STANDDARD"/>
        <result property="timeUnit" column="TIME_UNIT"/>
        <result property="fixed" column="FIXED"/>
        <result property="fixedCode" column="FIXED_CODE"/>
        <result property="seq" column="SEQ"/>
        <result property="memo" column="MEMO"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
    </resultMap>

    <sql id="selectFzgjTaskLimitationTimeEntityVo">
        select
            GUID,
            LIMITATION_GUID,
            VALID_TIME_CODE,
            REACH_STANDDARD,
            TIME_UNIT,
            FIXED,
            FIXED_CODE,
            SEQ,
            MEMO,
            CREATE_USER,
            CREATE_USER_NAME,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_USER_NAME,
            UPDATE_DATE,
            NODE_CODE,
            NODE_NAME,
            COMPANY_CODE,
            COMPANY_NAME,
            GROUP_CODE,
            GROUP_NAME
        from FZGJ_TASK_LIMITATION_TIME
    </sql>
</mapper>