package com.eci.project.fzgjExtendData.controller;

import com.eci.common.util.ResponseMsg;
import com.eci.common.util.ResponseMsgUtil;
import com.eci.crud.controller.EciBaseController;
import com.eci.log.annotation.EciLog;
import com.eci.log.enums.BusinessType;
import com.eci.project.fzgjExtendData.entity.FzgjExtendDataEntity;
import com.eci.project.fzgjExtendData.service.IFzgjExtendDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 扩展基础资料Controller
 *
 * @<NAME_EMAIL>
 * @date 2025-03-19
 */
@Api(tags = "扩展基础资料")
@RestController
@RequestMapping("/fzgjExtendData")
public class FzgjExtendDataController extends EciBaseController {

    @Autowired
    private IFzgjExtendDataService fzgjExtendDataService;

    /**
     * 根据实体插入数据库
     *
     * @param entity
     * @return
     */
    @ApiOperation("扩展基础资料:保存")
    @EciLog(title = "扩展基础资料:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public ResponseMsg save(@RequestBody FzgjExtendDataEntity entity) {
        return ResponseMsgUtil.success(10001, fzgjExtendDataService.save(entity));
    }

    /**
     * 根据条件查询一个集合
     *
     * @param entity
     * @return
     */
    @ApiOperation("扩展基础资料:查询列表")
    @EciLog(title = "扩展基础资料:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    public ResponseMsg selectList(@RequestBody FzgjExtendDataEntity entity) {
        return ResponseMsgUtil.success(10001, fzgjExtendDataService.selectList(entity));
    }

    /**
     * 分页查询
     *
     * @param entity
     * @return
     */
    @ApiOperation("扩展基础资料:分页查询列表")
    @EciLog(title = "扩展基础资料:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    public ResponseMsg selectPageList(@RequestBody FzgjExtendDataEntity entity) {
        return ResponseMsgUtil.success(10001, fzgjExtendDataService.queryPageList(entity));
    }

    /**
     * 根据ID查一条记录
     *
     * @param entity
     * @return
     */
    @ApiOperation("扩展基础资料:根据ID查一条")
    @EciLog(title = "扩展基础资料:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    public ResponseMsg selectOneById(@RequestBody FzgjExtendDataEntity entity) {
        return ResponseMsgUtil.successPlus(10001, fzgjExtendDataService.selectOneById(entity.getGuid()));
    }


    /**
     * 根据ID删除
     *
     * @param entity
     * @return
     */
    @ApiOperation("扩展基础资料:根据ID删除一条")
    @EciLog(title = "扩展基础资料:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    public ResponseMsg deleteById(@RequestBody FzgjExtendDataEntity entity) {
        return ResponseMsgUtil.success(10001, fzgjExtendDataService.deleteById(entity.getGuid()));
    }

    /**
     * 根据IDS删除
     *
     * @param entity
     * @return
     */
    @ApiOperation("扩展基础资料:根据ID字符串删除多条")
    @EciLog(title = "扩展基础资料:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    public ResponseMsg deleteByIds(@RequestBody FzgjExtendDataEntity entity) {
        return ResponseMsgUtil.success(10001, fzgjExtendDataService.deleteByIds(entity.getIds()));
    }


}