package com.eci.project.etmsBdTruck.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eci.common.utils.CommonLib;
import com.eci.common.web.BllContext;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.etmsBdTruck.dao.EtmsBdTruckDao;
import com.eci.project.etmsBdTruck.entity.EtmsBdTruckDTO;
import com.eci.project.etmsBdTruck.entity.EtmsBdTruckEntity;
import com.eci.project.etmsBdTruck.entity.EtmsBdTruckQuery;
import com.eci.project.etmsBdTruck.service.IEtmsBdTruckService;
import com.eci.project.etmsBdTruck.validate.EtmsBdTruckVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 车辆信息Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-11
*/
@Service
@Slf4j
public class EtmsBdTruckServiceImpl implements IEtmsBdTruckService
{
    @Autowired
    private EtmsBdTruckDao etmsBdTruckDao;

    @Autowired
    private EtmsBdTruckVal etmsBdTruckVal;

    CommonLib cmn = CommonLib.getInstance();

    public TgPageInfo queryPageList1(EtmsBdTruckQuery entity) {
        EciQuery<EtmsBdTruckQuery> eciQuery = EciQuery.doQuery(entity);
        eciQuery.apply("1=1").apply("AND A.IS_GK='Y'");
        if(!entity.DRIVER_ATT.isEmpty()){
            if(entity.DRIVER_ATT.equals("Y"))
                eciQuery.apply(" AND EXISTS(SELECT 1 FROM ETMS_OP_FILE T WHERE T.OP_NO=A.GUID)");
            else
                eciQuery.apply(" AND NOT EXISTS(SELECT 1 FROM ETMS_OP_FILE T WHERE T.OP_NO=A.GUID)");
        }
        if(!entity.DRIVER_GUID.isEmpty()){
            eciQuery.apply(" AND A.DRIVER_GUID=(SELECT GUID FROM ETMS_BD_DRIVER X WHERE USER_ID="+cmn.SQLQ(entity.DRIVER_GUID));
        }
        List<EtmsBdTruckDTO> entities = etmsBdTruckDao.queryPages(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public EtmsBdTruckEntity save(EtmsBdTruckEntity entity) {
        // 返回实体对象
        EtmsBdTruckEntity etmsBdTruckEntity = null;
        etmsBdTruckVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            etmsBdTruckEntity = etmsBdTruckDao.insertOne(entity);

        }else{

            etmsBdTruckEntity = etmsBdTruckDao.updateByEntityId(entity);

        }
        return etmsBdTruckEntity;
    }

    @Override
    public List<EtmsBdTruckEntity> selectList(EtmsBdTruckEntity entity) {
        return etmsBdTruckDao.selectList(entity);
    }

    @Override
    public EtmsBdTruckEntity selectOneById(Serializable id) {
        return etmsBdTruckDao.selectById(id);
    }


    @Override
    public void insertBatch(List<EtmsBdTruckEntity> list) {
        etmsBdTruckDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return etmsBdTruckDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return etmsBdTruckDao.deleteById(id);
    }

}