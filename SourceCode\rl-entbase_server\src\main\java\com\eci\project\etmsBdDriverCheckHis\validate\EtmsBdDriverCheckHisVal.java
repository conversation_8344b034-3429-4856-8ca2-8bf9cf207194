package com.eci.project.etmsBdDriverCheckHis.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.etmsBdDriverCheckHis.entity.EtmsBdDriverCheckHisEntity;

import org.springframework.stereotype.Service;


/**
* 司机体检历史Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-22
*/
@Service
public class EtmsBdDriverCheckHisVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(EtmsBdDriverCheckHisEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(EtmsBdDriverCheckHisEntity entity, BusinessType businessType) {

    }

}
