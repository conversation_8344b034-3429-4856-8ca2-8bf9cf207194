package com.eci.project.crmCustomerKh.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.crmCustomerKh.service.CrmCustomerKhService;
import com.eci.project.crmCustomerKh.entity.CrmCustomerKhEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 业务伙伴-客户信息Controller
*
* @<NAME_EMAIL>
* @date 2025-04-29
*/
@Api(tags = "业务伙伴-客户信息")
@RestController
@RequestMapping("/crmCustomerKh")
public class CrmCustomerKhController extends EciBaseController {

    @Autowired
    private CrmCustomerKhService crmCustomerKhService;


    @ApiOperation("业务伙伴-客户信息:保存")
    @EciLog(title = "业务伙伴-客户信息:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody CrmCustomerKhEntity entity){
        CrmCustomerKhEntity crmCustomerKhEntity =crmCustomerKhService.save(entity);
        return ResponseMsgUtil.success(10001,crmCustomerKhEntity);
    }


    @ApiOperation("业务伙伴-客户信息:查询列表")
    @EciLog(title = "业务伙伴-客户信息:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody CrmCustomerKhEntity entity){
        List<CrmCustomerKhEntity> crmCustomerKhEntities = crmCustomerKhService.selectList(entity);
        return ResponseMsgUtil.success(10001,crmCustomerKhEntities);
    }


    @ApiOperation("业务伙伴-客户信息:分页查询列表")
    @EciLog(title = "业务伙伴-客户信息:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody CrmCustomerKhEntity entity){
        TgPageInfo tgPageInfo = crmCustomerKhService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }

    @ApiOperation("业务伙伴-客户信息:查询发票类型及税率")
    @EciLog(title = "业务伙伴-客户信息:查询发票类型及税率", businessType = BusinessType.SELECT)
    @PostMapping("/searchTax")
    @EciAction()
    public ResponseMsg searchTax(@RequestBody CrmCustomerKhEntity entity){
        String code="",name="";
        if(entity.getRequestParams()!=null){
            code=entity.getRequestParams().get("code")==null?"":entity.getRequestParams().get("code").toString();
            name=entity.getRequestParams().get("name")==null?"":entity.getRequestParams().get("name").toString();
        }
        TgPageInfo tgPageInfo = crmCustomerKhService.searchTax(code
                ,name);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("业务伙伴-客户信息:根据ID查一条")
    @EciLog(title = "业务伙伴-客户信息:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody CrmCustomerKhEntity entity){
        CrmCustomerKhEntity  crmCustomerKhEntity = crmCustomerKhService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,crmCustomerKhEntity);
    }

    @ApiOperation("业务伙伴-客户信息:根据ID查一条")
    @EciLog(title = "业务伙伴-客户信息:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/load")
    @EciAction()
    public ResponseMsg load(@RequestBody CrmCustomerKhEntity entity){
        CrmCustomerKhEntity  crmCustomerKhEntity = crmCustomerKhService.load(entity);
        return ResponseMsgUtil.successPlus(10001,crmCustomerKhEntity==null?new CrmCustomerKhEntity():crmCustomerKhEntity);
    }


    @ApiOperation("业务伙伴-客户信息:根据ID删除一条")
    @EciLog(title = "业务伙伴-客户信息:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody CrmCustomerKhEntity entity){
        int count = crmCustomerKhService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("业务伙伴-客户信息:根据ID字符串删除多条")
    @EciLog(title = "业务伙伴-客户信息:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody CrmCustomerKhEntity entity) {
        int count = crmCustomerKhService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}