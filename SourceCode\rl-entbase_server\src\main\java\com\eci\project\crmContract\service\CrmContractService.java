package com.eci.project.crmContract.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eci.cache.config.TgCacheHelper;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.crmContract.dao.CrmContractDao;
import com.eci.project.crmContract.entity.CrmContractEntity;
import com.eci.project.crmContract.validate.CrmContractVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 合同Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-05-21
*/
@Service
@Slf4j
public class CrmContractService implements EciBaseService<CrmContractEntity> {

    @Autowired
    private CrmContractDao crmContractDao;

    @Autowired
    private CrmContractVal crmContractVal;


    @Override
    public TgPageInfo queryPageList(CrmContractEntity entity) {
        EciQuery<CrmContractEntity> eciQuery = EciQuery.buildQuery(entity);
        eciQuery.select("(select Name from FZGJ_CRM_CONTRACT_TYPE_WARN where code=A.CONTRACT_TYPE and group_code=A.GROUP_CODE) as contractTypeName",
                "(SELECT COUNT(*) FROM CRM_FILE_INFO F WHERE F.FILE_NO=A.GUID) AS fileCount",
                "A.*");

        List<CrmContractEntity> entities = crmContractDao.queryPageList(eciQuery);
        entities.forEach(p->{
            if(p.getHtzt().equals("Y")){
                p.setHtzt("失效");
            }else if(p.getHtzt().equals("N")){
                p.setHtzt("生效");
            }else if(p.getHtzt().equals("D")){
                p.setHtzt("待生效");
            }
        });
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public CrmContractEntity save(CrmContractEntity entity) {
        // 返回实体对象
        CrmContractEntity crmContractEntity = null;
        crmContractVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setGuid(AutoBuildCode());
            crmContractEntity = crmContractDao.insertOne(entity);

        }else{

            crmContractEntity = crmContractDao.updateByEntityId(entity);

        }
        return crmContractEntity;
    }

    @Override
    public List<CrmContractEntity> selectList(CrmContractEntity entity) {
        return crmContractDao.selectList(entity);
    }

    @Override
    public CrmContractEntity selectOneById(Serializable id) {
        return crmContractDao.selectById(id);
    }
    /**
     * <AUTHOR>
     * @Description 通过guid查找list
     * @Date  2025/5/23 15:36
     * @Param [ids]
     * @return java.util.List<com.eci.project.crmContract.entity.CrmContractEntity>
     **/
    public List<CrmContractEntity> selectList(String ids){
        QueryWrapper query=new QueryWrapper();
        query.in("guid",ids.split(","));
        return crmContractDao.selectList(query);
    }

    @Override
    public void insertBatch(List<CrmContractEntity> list) {
        crmContractDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return crmContractDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return crmContractDao.deleteById(id);
    }

    public String AutoBuildCode(){
        String template="HT%s";
        Long no= TgCacheHelper.incr("HTBuildCode",1);//redis自增1
        String code = String.format(template,String.format("%08d",no));//补位
        if(codeExist(code)) //信用代码存在则重新生成
            return AutoBuildCode();
        return code;
    }

    public boolean codeExist(String code){
        QueryWrapper queryWrapper=new QueryWrapper();
        queryWrapper.eq("GUID",code);
        return crmContractDao.exists(queryWrapper);
    }

    public void Update(CrmContractEntity entity){
        crmContractDao.updateByEntityId(entity,"htzt","renewalExpirationDate","renewalDate","status","terminationDate");
    }
}