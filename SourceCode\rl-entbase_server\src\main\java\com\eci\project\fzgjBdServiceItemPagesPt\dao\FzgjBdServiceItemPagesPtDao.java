package com.eci.project.fzgjBdServiceItemPagesPt.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.fzgjBdServiceItemPagesPt.entity.FzgjBdServiceItemPagesPtEntity;


/**
* 企业服务项目对应页面编辑区Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-04-01
*/
public interface FzgjBdServiceItemPagesPtDao extends EciBaseDao<FzgjBdServiceItemPagesPtEntity> {

}