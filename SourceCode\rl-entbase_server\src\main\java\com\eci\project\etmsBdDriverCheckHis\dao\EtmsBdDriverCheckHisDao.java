package com.eci.project.etmsBdDriverCheckHis.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.etmsBdDriverCheckHis.entity.EtmsBdDriverCheckHisEntity;


/**
* 司机体检历史Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-04-22
*/
public interface EtmsBdDriverCheckHisDao extends EciBaseDao<EtmsBdDriverCheckHisEntity> {

}