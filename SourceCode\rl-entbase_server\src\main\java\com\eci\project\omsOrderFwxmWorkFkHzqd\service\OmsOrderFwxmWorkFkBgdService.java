package com.eci.project.omsOrderFwxmWorkFkHzqd.service;

import com.eci.common.DictFieldUtils;
import com.eci.common.Extensions;
import com.eci.common.ZsrBaseEntity;
import com.eci.common.ZsrJson;
import com.eci.common.db.DBHelper;
import com.eci.common.utils.CommonLib;
import com.eci.exception.BaseException;
import com.eci.project.omsOrderFwxmWorkFk.entity.OmsOrderFwxmWorkFkEntity;
import com.eci.project.omsOrderFwxmWorkFk.validate.OmsOrderFwxmWorkFkVal;
import com.eci.project.omsOrderFwxmWorkFkHfd.entity.OmsOrderFwxmWorkFkHfdEntity;
import com.eci.project.omsOrderFwxmWorkFkHzqd.entity.OmsOrderFwxmWorkFkHzqdEntity;
import com.eci.project.omsOrderFwxmWorkFkHzqd.entity.ReqomsOrderFwxmWorkFkBgdQueryEntity;
import com.eci.project.omsOrderFwxmWorkFkHzqd.entity.ResomsOrderFwxmWorkFkBgdEntity;
import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ClassName: OmsOrderFwxmWorkFkBgdService
 * @Author: guangyan.mei
 * @Date: 2025/6/3 19:57
 * @Description: TODO
 */
@Service
@Slf4j
public class OmsOrderFwxmWorkFkBgdService {

    CommonLib cmn = CommonLib.getInstance();

    @Autowired
    private OmsOrderFwxmWorkFkVal omsOrderFwxmWorkFkVal;

    @Autowired
    private OmsOrderFwxmWorkFkHzqdService omsOrderFwxmWorkFkHzqdService;

    /**
     * 扩展类
     **/
    @Autowired
    private Extensions extensions;

    /**
     * 加载报关单详情数据
     */
    public ZsrBaseEntity feedBackBgdLoad(ReqomsOrderFwxmWorkFkBgdQueryEntity entity) {

        ZsrBaseEntity responseEntity = new ZsrBaseEntity();

        String bizRegId = entity.getBizRegId();

        // 核注清单作业
        List<OmsOrderFwxmWorkFkHzqdEntity> hzqdList = feedBackBgdInfoLoad(bizRegId);
        DictFieldUtils.handleDictFields(hzqdList);
        responseEntity.push("dtDateBGD", hzqdList);

        return responseEntity;
    }


    /**
     * 报关单反馈-保存
     **/
    public boolean feedBackBgdSave(String jsonString) {

        boolean resFlag = true;

        ZsrJson jsonStr = ZsrJson.parse(jsonString);
        String fieldName = jsonStr.check("decFiledName").getString("decFiledName");
        OmsOrderFwxmWorkFkEntity saveEntity = jsonStr.check("entity").getObject("entity", OmsOrderFwxmWorkFkEntity.class);
        List<OmsOrderFwxmWorkFkHzqdEntity> dtDateBgd = jsonStr.check("dtDateBGD").getList("dtDateBGD", OmsOrderFwxmWorkFkHzqdEntity.class);
        if (saveEntity == null) {
            throw new BaseException("entity 解析失败");
        }

        try {
            String workNo = saveEntity.getWorkNo();
            String xzwtNo = saveEntity.getXzwtNo();
            String orderNo = saveEntity.getOrderNo();
            String bizRegId = saveEntity.getBizRegId();
            String fwxmCode = saveEntity.getFwxmCode();

            // 验证
            omsOrderFwxmWorkFkVal.feedBackValidate(workNo, "");

            // 反馈
            omsOrderFwxmWorkFkHzqdService.saveOrderWorkFK(saveEntity);

            // 核注清单
            omsOrderFwxmWorkFkHzqdService.saveOrderWorkInfoHZQD(workNo, orderNo, fwxmCode, bizRegId, fieldName, dtDateBgd);

            // 调用存储过程-固化
            extensions.addOmsGh(orderNo);

        } catch (Exception ex) {
            resFlag = false;
        }

        return resFlag;
    }

    /**
     * 核注清单
     **/
    public List<OmsOrderFwxmWorkFkHzqdEntity> feedBackBgdInfoLoad(String bizRegId) {

        String groupCode = UserContext.getUserInfo().getCompanyCode();

        String sql = "SELECT A.GUID\n" +
                "     ,A.DEC_NO\n" +
                "     ,A.DEC_TABLE_NUM\n" +
                "     ,TO_CHAR(A.D_DATE,'yyyy-mm-dd hh24:mi:ss') AS D_DATE\n" +
                "     ,A.CHECKBILL_NO,A.JYJY_NO,A.YDH,A.FDH,A.XDH,A.BGD_ZS,A.ZGDH,A.DEC_NO_XT,PIECE\n" +
                "     ,A.WEIGHT,A.QTY,A.PRODUCT_CODE,A.CONTRACT_NO,A.CONTAINER_NO,A.IS_CHECK\n" +
                "     ,TO_CHAR(A.HZQD_DATE,'yyyy-mm-dd hh24:mi:ss') AS HZQD_DATE\n" +
                "     ,TO_CHAR(A.JD_DATE,'yyyy-mm-dd') AS JD_DATE\n" +
                "     ,TO_CHAR(A.BILL_DATE,'yyyy-mm-dd') AS BILL_DATE\n" +
                "     ,(I_E_TYPE||'|'||DECODE(A.I_E_TYPE,'I','进口','E','出口','')) AS I_E_TYPE\n" +
                "     ,(I_E_PORT || '|' ||(SELECT NAME  FROM FZGJ_BD_CUSTOMS CUSTOMS WHERE 1 = 1  AND CUSTOMS.CODE = A.I_E_PORT )) AS I_E_PORT\n" +
                "      ,(CUSTOM_CODE|| '|'||(SELECT NAME  FROM FZGJ_BD_CUSTOMS CUSTOMS WHERE 1 = 1  AND CUSTOMS.CODE = A.CUSTOM_CODE )) AS CUSTOM_CODE\n" +
                "      ,(PURPORGCODE||'|'||( SELECT NAME  FROM FZGJ_BD_CUSTOMS CUSTOMS WHERE 1 = 1  AND CUSTOMS.CODE = A.PURPORGCODE) )AS PURPORGCODE\n" +
                "      ,TRADE_CODE||'|'||(SELECT MAX(CUSTOMER.NAME) FROM CRM_CUSTOMER CUSTOMER   WHERE CUSTOMER.STATUS='Y' AND CUSTOMER.CODE = A.TRADE_CODE AND CUSTOMER.GROUP_CODE = " + cmn.SQLQ(groupCode) + ") AS TRADE_CODE\n" +
                "      ,(YSFS||'|'||( SELECT BDYSFS.NAME FROM FZGJ_BD_YSFS BDYSFS WHERE BDYSFS.CODE = A.YSFS) )AS YSFS\n" +
                "      ,JNSFHR||'|'|| ((SELECT MAX(CUSTOMER.NAME) FROM CRM_CUSTOMER CUSTOMER   WHERE CUSTOMER.STATUS='Y' AND CUSTOMER.CODE = A.JNSFHR AND CUSTOMER.GROUP_CODE = " + cmn.SQLQ(groupCode) + ") ) AS JNSFHR\n" +
                "      ,AMOUNT\n" +
                "      ,OMS_CODE_NAME(A.DEC_TRADE_MODE,'OMS_BD_TRADE') DEC_TRADE_MODE\n" +
                "      ,RTRIM(CYLX)||CASE WHEN (SELECT MAX(DATA.NAME) FROM FZGJ_EXTEND_DATA DATA WHERE DATA.STATUS = 'Y' AND DATA.CODE = A.CYLX AND DATA.GROUP_CODE = " + cmn.SQLQ(groupCode) + ") IS NULL THEN NULL ELSE '|'||((SELECT NAME FROM FZGJ_EXTEND_DATA DATA WHERE DATA.STATUS = 'Y' AND DATA.CODE = A.CYLX AND DATA.GROUP_CODE = " + cmn.SQLQ(groupCode) + ")) END CYLX\n" +
                "      ,BJ_TYPE||'|'|| (SELECT MAX(DATA.NAME) FROM FZGJ_EXTEND_DATA DATA WHERE DATA.STATUS = 'Y' AND DATA.CODE = A.BJ_TYPE AND DATA.GROUP_CODE = " + cmn.SQLQ(groupCode) + ") AS BJ_TYPE\n" +
                "      ,IS_SD\n" +
                "      ,IS_GD\n" +
                "      ,IS_JYJY\n" +
                "      ,PACK_TYPE||'|'|| (SELECT MAX(PACKTYPE.NAME)  FROM FZGJ_GOODS_PACK_TYPE PACKTYPE WHERE PACKTYPE.STATUS = 'Y' AND PACKTYPE.GROUP_CODE=" + cmn.SQLQ(groupCode) + " AND PACKTYPE.CODE = A.PACK_TYPE) AS PACK_TYPE\n" +
                "      ,NET_WEIGHT\n" +
                "      ,LDS\n" +
                "      ,QS_MAN\n" +
                "      ,QS_DATE\n" +
                "      ,IS_BJ\n" +
                "      ,BILL_MAN\n" +
                "      ,OMS_CODE_NAME(TRADE_COUNTRY,'OMS_BD_COUNTRY_TG') TRADE_COUNTRY\n" +
                "      ,BGD_MEMO\n" +
                "      ,AMOUNT_CURRENCY\n" +
                "      ,OMS_CODE_NAME(A.CURRENCY, 'OMS_BD_CURRENCY') CURRENCY\n" +
                "      ,OMS_CODE_NAME(A.ORIGIN_ARRIVAL_COUNTRY,'OMS_BD_COUNTRY_TG') ORIGIN_ARRIVAL_COUNTRY\n" +
                "      ,OMS_CODE_NAME(A.SUPERVISION_MODE,'OMS_BD_TRADE') SUPERVISION_MODE\n" +
                "      ,HS_CODE\n" +
                "      ,DEC_NO_GW\n" +
                " FROM OMS_ORDER_FWXM_WORK_FK_HZQD A";

        sql += " WHERE A.BIZ_REG_ID=" + cmn.SQLQ(bizRegId);

        return DBHelper.selectList(sql, OmsOrderFwxmWorkFkHzqdEntity.class);
    }

    /**
     * 金二核放单-加载
     */
    public List<OmsOrderFwxmWorkFkHfdEntity> feedBackLyhfdInfoLoad(String bizRegId) {

        String sql = "SELECT A.GUID,A.HF_NO,A.HF_NO_LY,A.FX_DATE,A.REMARK,CAR_NO,GJ_NO ";

        sql += " ,CASE IE_MARK WHEN '1' THEN '进区' WHEN '2' THEN '出区' ELSE  IE_MARK END AS IE_MARK ";
        sql += " ,CASE I_E_FLAG WHEN 'I' THEN '进' WHEN 'E' THEN '出' ELSE I_E_FLAG END AS I_E_FLAG ";
        sql += " FROM OMS_ORDER_FWXM_WORK_FK_HFD A ";
        sql += " WHERE A.BIZ_REG_ID=" + cmn.SQLQ(bizRegId);

        return DBHelper.selectList(sql, OmsOrderFwxmWorkFkHfdEntity.class);

    }

}
