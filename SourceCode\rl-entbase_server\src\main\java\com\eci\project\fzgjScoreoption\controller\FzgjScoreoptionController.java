package com.eci.project.fzgjScoreoption.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjScoreoption.service.FzgjScoreoptionService;
import com.eci.project.fzgjScoreoption.entity.FzgjScoreoptionEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 评分标准设置Controller
*
* @<NAME_EMAIL>
* @date 2025-06-16
*/
@Api(tags = "评分标准设置")
@RestController
@RequestMapping("/fzgjScoreoption")
public class FzgjScoreoptionController extends EciBaseController {

    @Autowired
    private FzgjScoreoptionService fzgjScoreoptionService;


    @ApiOperation("评分标准设置:保存")
    @EciLog(title = "评分标准设置:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody FzgjScoreoptionEntity entity){
        FzgjScoreoptionEntity fzgjScoreoptionEntity =fzgjScoreoptionService.save(entity);
        return ResponseMsgUtil.success(10001,fzgjScoreoptionEntity);
    }


    @ApiOperation("评分标准设置:查询列表")
    @EciLog(title = "评分标准设置:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjScoreoptionEntity entity){
        List<FzgjScoreoptionEntity> fzgjScoreoptionEntities = fzgjScoreoptionService.selectList(entity);
        return ResponseMsgUtil.success(10001,fzgjScoreoptionEntities);
    }


    @ApiOperation("评分标准设置:分页查询列表")
    @EciLog(title = "评分标准设置:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjScoreoptionEntity entity){
        TgPageInfo tgPageInfo = fzgjScoreoptionService.queryPageList(entity);
        return ResponseMsgUtil.successPlus(10001,tgPageInfo);
    }


    @ApiOperation("评分标准设置:根据ID查一条")
    @EciLog(title = "评分标准设置:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjScoreoptionEntity entity){
        FzgjScoreoptionEntity  fzgjScoreoptionEntity = fzgjScoreoptionService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,fzgjScoreoptionEntity);
    }


    @ApiOperation("评分标准设置:根据ID删除一条")
    @EciLog(title = "评分标准设置:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjScoreoptionEntity entity){
        int count = fzgjScoreoptionService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("评分标准设置:根据ID字符串删除多条")
    @EciLog(title = "评分标准设置:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjScoreoptionEntity entity) {
        int count = fzgjScoreoptionService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}