package com.eci.project.etmsBdTruckInsuranceQz.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.db.DBHelper;
import com.eci.common.enums.Enums;
import com.eci.common.util.DateUtils;
import com.eci.common.util.StringUtils;
import com.eci.common.utils.ExportUtils.ExcelProcess;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.ActionType;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.etmsBdDriverCertificate.entity.EtmsBdDriverCertificateSearchEntity;
import com.eci.project.etmsBdTruckInsuranceQz.dao.EtmsBdTruckInsuranceQzDao;
import com.eci.project.etmsBdTruckInsuranceQz.entity.EtmsBdTruckInsuranceQzDTOEntity;
import com.eci.project.etmsBdTruckInsuranceQz.entity.EtmsBdTruckInsuranceQzEntity;
import com.eci.project.etmsBdTruckInsuranceQz.entity.EtmsBdTruckInsuranceQzSearchEntity;
import com.eci.project.etmsBdTruckInsuranceQz.validate.EtmsBdTruckInsuranceQzVal;

import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.Arrays;
import java.util.Date;
import java.util.List;


/**
* 车辆保险历史Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-15
*/
@Service
@Slf4j
public class EtmsBdTruckInsuranceQzService implements EciBaseService<EtmsBdTruckInsuranceQzEntity> {

    @Autowired
    private EtmsBdTruckInsuranceQzDao etmsBdTruckInsuranceQzDao;

    @Autowired
    private EtmsBdTruckInsuranceQzVal etmsBdTruckInsuranceQzVal;


    @Override
    public TgPageInfo queryPageList(EtmsBdTruckInsuranceQzEntity entity) {
        startPage();
        // 使用参数化查询
        String stSql = "SELECT BD.CHECK_STATUS, B.GUID, B.POLICY_NO, B.INSURER, B.INSURANCE_TYPE, " +
                "B.START_DATE, B.END_DATE, B.MOD_MARK,B.INSURED_NAME,B.INSURANCE_STATUS,B.INSURANCE_MONEY,B.CREATE_DATE,B.UPDATE_DATE,B.CREATE_USER_NAME,B.UPDATE_USER_NAME,B.INSURANCE_PERIOD " +
                "FROM ETMS_BD_TRUCK_QZ BD " +
                "RIGHT JOIN ETMS_BD_TRUCK_INSURANCE_QZ B ON B.TRUCK_GUID = BD.GUID " +
                "WHERE BD.GUID = ?";

        // 使用参数化查询，避免 SQL 注入
        List<EtmsBdTruckInsuranceQzDTOEntity> pageInfo = DBHelper.selectList(stSql, EtmsBdTruckInsuranceQzDTOEntity.class, entity.getTruckGuid());
        return EciQuery.getPageInfo(pageInfo);
    }

    public TgPageInfo queryInsurancePageList(EtmsBdTruckInsuranceQzSearchEntity entity){
        StringBuilder stringBuilder=new StringBuilder();
        stringBuilder.append("SELECT  A.TRUCK_NO,A.COMPANY_NAME AS CREATE_COMPANY,B.INSURER,B.INSURANCE_TYPE,B.POLICY_NO,B.INSURED_NAME,B.INSURANCE_MONEY,B.START_DATE,B.END_DATE,B.INSURANCE_PERIOD,B.INSURANCE_STATUS,B.COMPANY_NAME,B.CREATE_DATE,B.MOD_MARK\n" +
                "FROM ETMS_BD_TRUCK_QZ A INNER JOIN ETMS_BD_TRUCK_INSURANCE_QZ B ON A.GUID=B.TRUCK_GUID where 1=1 ");
        if(StringUtils.hasValue(entity.getTruckNo())){
            stringBuilder.append(" AND A.TRUCK_NO like '%"+entity.getTruckNo()+"%'");
        }
        if(StringUtils.hasValue(entity.getCreateCompany())){
            stringBuilder.append(" AND A.COMPANY_CODE like '%"+entity.getCreateCompany()+"%'");
        }
        if(StringUtils.hasValue(entity.getCompanyName())){
            stringBuilder.append(" AND B.COMPANY_CODE like '%"+entity.getCompanyName()+"%'");
        }
        if(StringUtils.hasValue(entity.getInsuredName())){
            stringBuilder.append(" AND B.INSURED_NAME like '%"+entity.getInsuredName()+"%'");
        }
        if(StringUtils.hasValue(entity.getInsuranceType())){
            stringBuilder.append(" AND B.INSURANCE_TYPE like '%"+entity.getInsuranceType()+"%'");
        }
        if(StringUtils.hasValue(entity.getInsurer())){
            stringBuilder.append(" AND B.INSURER like '%"+entity.getInsurer()+"%'");
        }
        if(StringUtils.hasValue(entity.getPolicyNo())){
            stringBuilder.append(" AND B.POLICY_NO like '%"+entity.getPolicyNo()+"%'");
        }
        if (entity.getStartDateStart()!=null) {
            stringBuilder.append(" AND B.START_DATE >= TO_DATE('" + DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss",entity.getStartDateStart()) + "', 'yyyy-mm-dd hh24:mi:ss')");
        }
        if (entity.getStartDateEnd()!=null) {
            stringBuilder.append(" AND B.START_DATE <= TO_DATE('" + DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss",entity.getStartDateEnd()) + "', 'yyyy-mm-dd hh24:mi:ss')");
        }
        if (entity.getEndDateStart()!=null) {
            stringBuilder.append(" AND B.END_DATE >= TO_DATE('" + DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss",entity.getEndDateStart()) + "', 'yyyy-mm-dd hh24:mi:ss')");
        }
        if (entity.getEndDateEnd()!=null) {
            stringBuilder.append(" AND B.END_DATE <= TO_DATE('" + DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss",entity.getEndDateEnd()) + "', 'yyyy-mm-dd hh24:mi:ss')");
        }
        if (entity.getCreateDateStart()!=null) {
            stringBuilder.append(" AND B.CREATE_DATE >= TO_DATE('" + DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss",entity.getCreateDateStart()) + "', 'yyyy-mm-dd hh24:mi:ss')");
        }
        if (entity.getCreateDateEnd()!=null) {
            stringBuilder.append(" AND B.CREATE_DATE <= TO_DATE('" + DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss",entity.getCreateDateEnd()) + "', 'yyyy-mm-dd hh24:mi:ss')");
        }
        if (BllContext.getBusinessType() == BusinessType.EXPORT) {
            if (BllContext.getRequestEntity().getActionType() == ActionType.ASYNC) {
                etmsBdTruckInsuranceQzDao.asyncExportDefaultExcel(()->{
                    List<EtmsBdTruckInsuranceQzSearchEntity> entities = DBHelper.selectList(stringBuilder.toString(), EtmsBdTruckInsuranceQzSearchEntity.class);
                    entities.forEach(qzSearchEntity -> {
                        if (StringUtils.hasValue(qzSearchEntity.getEndDate()) && DateUtils.parseDate(qzSearchEntity.getEndDate().substring(0,19)).compareTo(DateUtils.parseDate(DateUtils.getDate())) >= 0) {
                            qzSearchEntity.setInsuranceStatus("有效");
                        } else {
                            qzSearchEntity.setInsuranceStatus("无效");
                        }
                        qzSearchEntity.setStartDate(qzSearchEntity.getStartDate().substring(0, 10));
                        qzSearchEntity.setEndDate(qzSearchEntity.getEndDate().substring(0, 10));
                        qzSearchEntity.setModMark(qzSearchEntity.getModMark().equals("4") ? "是" : "否");
                    });
                    return entities;
                }, ExcelProcess.BuildConfig("车辆保险管理", EtmsBdTruckInsuranceQzSearchEntity.class));
            } else {
                etmsBdTruckInsuranceQzDao.exportDefaultExcel(() -> {
                    List<EtmsBdTruckInsuranceQzSearchEntity> entities = DBHelper.selectList(stringBuilder.toString(), EtmsBdTruckInsuranceQzSearchEntity.class);
                    entities.forEach(qzSearchEntity -> {
                        if (StringUtils.hasValue(qzSearchEntity.getEndDate()) && DateUtils.parseDate(qzSearchEntity.getEndDate().substring(0,19)).compareTo(DateUtils.parseDate(DateUtils.getDate())) >= 0) {
                            qzSearchEntity.setInsuranceStatus("有效");
                        } else {
                            qzSearchEntity.setInsuranceStatus("无效");
                        }
                        qzSearchEntity.setStartDate(qzSearchEntity.getStartDate().substring(0, 10));
                        qzSearchEntity.setEndDate(qzSearchEntity.getEndDate().substring(0, 10));
                        qzSearchEntity.setModMark(qzSearchEntity.getModMark().equals("4") ? "是" : "否");
                    });
                    return entities;
                }, ExcelProcess.BuildConfig("司机证件管理", EtmsBdTruckInsuranceQzSearchEntity.class));
            }
            return new TgPageInfo<>();
        }else{
            startPage();
            List<EtmsBdTruckInsuranceQzSearchEntity> entities = DBHelper.selectList(stringBuilder.toString(), EtmsBdTruckInsuranceQzSearchEntity.class);
            entities.forEach(qzSearchEntity -> {
                Date now=DateUtils.parseDate(DateUtils.getDate());
                if (StringUtils.hasValue(qzSearchEntity.getEndDate()) && DateUtils.parseDate(qzSearchEntity.getEndDate().substring(0, 19)).compareTo(now) >= 0) {
                    qzSearchEntity.setInsuranceStatus("有效");
                } else {
                    qzSearchEntity.setInsuranceStatus("无效");
                }
                qzSearchEntity.setModMark(qzSearchEntity.getModMark().equals("4") ? "是" : "否");
            });
            return EciQuery.getPageInfo(entities);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public EtmsBdTruckInsuranceQzEntity save(EtmsBdTruckInsuranceQzEntity entity) {
        // 返回实体对象
        EtmsBdTruckInsuranceQzEntity etmsBdTruckInsuranceQzEntity = null;
        etmsBdTruckInsuranceQzVal.saveValidate(entity,BllContext.getBusinessType());
        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setGuid(IdWorker.get32UUID());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setCreateDate(DateUtils.getNowDate());
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
            entity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            entity.setCompanyName(UserContext.getUserInfo().getCompanyName());
            entity.setNodeCode(UserContext.getUserInfo().getDeptCode());
            entity.setNodeName(UserContext.getUserInfo().getDeptName());
            entity.setModMark(Enums.ModMark.XZ.getValue());
            etmsBdTruckInsuranceQzEntity = etmsBdTruckInsuranceQzDao.insertOne(entity);
        }else{
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            entity.setUpdateDate(DateUtils.getNowDate());
            entity.setModMark(Enums.ModMark.XG.getValue());
            etmsBdTruckInsuranceQzEntity = etmsBdTruckInsuranceQzDao.updateByEntityId(entity);
        }
        return etmsBdTruckInsuranceQzEntity;
    }

    @Override
    public List<EtmsBdTruckInsuranceQzEntity> selectList(EtmsBdTruckInsuranceQzEntity entity) {
        return etmsBdTruckInsuranceQzDao.selectList(entity);
    }

    @Override
    public EtmsBdTruckInsuranceQzEntity selectOneById(Serializable id) {
        return etmsBdTruckInsuranceQzDao.selectById(id);
    }


    @Override
    public void insertBatch(List<EtmsBdTruckInsuranceQzEntity> list) {
        etmsBdTruckInsuranceQzDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return etmsBdTruckInsuranceQzDao.deleteByIds(ids);
    }

    @Transactional(rollbackFor = Exception.class)
    public int deleteByList(String ids) {
        List<String> list=Arrays.asList(ids.split(","));
        int count=0;
        for(String str:list){
            QueryWrapper<EtmsBdTruckInsuranceQzEntity> queryWrapper=new QueryWrapper<>();
            queryWrapper.eq("GUID",str);
            count+=etmsBdTruckInsuranceQzDao.delete(queryWrapper);
        }
        return count;
    }

    @Override
    public int deleteById(Serializable id) {
        return etmsBdTruckInsuranceQzDao.deleteById(id);
    }

}