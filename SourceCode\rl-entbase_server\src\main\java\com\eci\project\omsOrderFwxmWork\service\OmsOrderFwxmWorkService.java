package com.eci.project.omsOrderFwxmWork.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.Extension;
import com.eci.common.NoManager;
import com.eci.common.OmsHelper;
import com.eci.common.db.DBHelper;
import com.eci.common.enums.Enums;
import com.eci.common.enums.OrderEnum;
import com.eci.common.util.StringUtils;
import com.eci.common.utils.CommonLib;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.exception.BaseException;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.omsIBillStatus.dao.OmsIBillStatusDao;
import com.eci.project.omsIBillStatus.entity.OmsIBillStatusEntity;
import com.eci.project.omsOrderFwxmWork.dao.OmsOrderFwxmWorkDao;
import com.eci.project.omsOrderFwxmWork.entity.OmsOrderFwxmWorkEntity;
import com.eci.project.omsOrderFwxmWork.entity.OrderFwxmWorkResponse;
import com.eci.project.omsOrderFwxmWork.entity.ReqOrderFwxmWorkEntity;
import com.eci.project.omsOrderFwxmWork.validate.OmsOrderFwxmWorkVal;
import com.eci.project.omsOrderPre.dao.OmsOrderPreDao;
import com.eci.project.omsOrderPre.entity.OmsOrderPreEntity;
import com.eci.sso.role.entity.UserContext;
import com.eci.wu.core.EntityBase;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;


/**
 * 供方协作任务Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-04-21
 */
@Service
@Slf4j
public class OmsOrderFwxmWorkService implements EciBaseService<OmsOrderFwxmWorkEntity> {

    CommonLib cmn = CommonLib.getInstance();

    @Autowired
    private OmsOrderFwxmWorkDao omsOrderFwxmWorkDao;
    @Autowired
    private OmsOrderFwxmWorkVal omsOrderFwxmWorkVal;
    @Autowired
    private OmsOrderPreDao omsOrderPreDao;

    @Autowired
    private OmsIBillStatusDao omsIBillStatusDao;

    @Override
    public TgPageInfo queryPageList(OmsOrderFwxmWorkEntity entity) {
        EciQuery<OmsOrderFwxmWorkEntity> eciQuery = EciQuery.buildQuery(entity);
        List<OmsOrderFwxmWorkEntity> entities = omsOrderFwxmWorkDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderFwxmWorkEntity save(OmsOrderFwxmWorkEntity entity) {
        // 返回实体对象
        OmsOrderFwxmWorkEntity omsOrderFwxmWorkEntity = null;
        omsOrderFwxmWorkVal.saveValidate(entity, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            omsOrderFwxmWorkEntity = omsOrderFwxmWorkDao.insertOne(entity);

        } else {

            omsOrderFwxmWorkEntity = omsOrderFwxmWorkDao.updateByEntityId(entity);

        }
        return omsOrderFwxmWorkEntity;
    }

    @Override
    public List<OmsOrderFwxmWorkEntity> selectList(OmsOrderFwxmWorkEntity entity) {
        return omsOrderFwxmWorkDao.selectList(entity);
    }

    @Override
    public OmsOrderFwxmWorkEntity selectOneById(Serializable id) {
        return omsOrderFwxmWorkDao.selectById(id);
    }

    @Override
    public void insertBatch(List<OmsOrderFwxmWorkEntity> list) {
        omsOrderFwxmWorkDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return omsOrderFwxmWorkDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return omsOrderFwxmWorkDao.deleteById(id);
    }

    /// <summary>
    /// 协同订单 审核 重新生成WorkNo
    /// </summary>
    /// <param name="preNo"></param>
    /// <param name="orderNo"></param>
    @Transactional(rollbackFor = Exception.class)
    public void resetWorkNo(String preNo, String orderNo) {
        String groupCode = UserContext.getUserInfo().getCompanyCode();

        List<OmsOrderFwxmWorkEntity> listWork = omsOrderFwxmWorkDao.select()
                .eq(OmsOrderFwxmWorkEntity::getPreNo,preNo)
                .eq(OmsOrderFwxmWorkEntity::getGroupCode,groupCode)
                .list();

      if (listWork.size() > 0) {

            List<String> listUpdateWorkNoSql = new ArrayList<>();

            // SQL 模板定义
            String updateWorkNoTemplate = " UPDATE %1$s SET WORK_NO =%2$s WHERE WORK_NO = %3$s AND PRE_NO =%4$s AND GROUP_CODE=%5$s ";
            String updateWorkNoTemplateUDF = " UPDATE %1$s SET UDF1 =REPLACE(UDF1, %3$s, %2$s) WHERE WORK_NO = %3$s AND PRE_NO =%4$s AND GROUP_CODE=%5$s ";
            String updateUDF1 = " UPDATE %1$s SET UDF1=%2$s,UDF2=%6$s WHERE WORK_NO = %3$s AND PRE_NO =%4$s AND GROUP_CODE=%5$s ";
            String updateUDF2 = " UPDATE %1$s SET UDF2=%2$s WHERE WORK_NO = %3$s AND PRE_NO =%4$s AND GROUP_CODE=%5$s ";

            for (OmsOrderFwxmWorkEntity item : listWork) {
                OrderFwxmWorkResponse work = new OrderFwxmWorkResponse();
                BeanUtils.copyProperties(item, work);
                // 赋值
                work.setP_FWXM_CODE(Extension.getFwxmCodeByWorkInfo(work));

                // 生成新的 WorkNo
                String newWorkNo = NoManager.createWorkNo(orderNo, work.getP_FWXM_CODE());

                // 构建并添加 UDF1 更新语句
                String updateWorkNoTemplateUDFSql = String.format(
                        updateWorkNoTemplateUDF,
                        "OMS_ORDER_FWXM_WORK",
                        cmn.SQLQ(newWorkNo),
                        cmn.SQLQ(work.getWorkNo()),
                        cmn.SQLQ(work.getPreNo()),
                        cmn.SQLQ(work.getGroupCode())
                );
                listUpdateWorkNoSql.add(updateWorkNoTemplateUDFSql);
                DBHelper.execute(updateWorkNoTemplateUDFSql);

                // 查询来源为 CSC 的订单
                QueryWrapper queryWrapper = new QueryWrapper();
                queryWrapper.eq("PRE_NO", work.getPreNo());
                queryWrapper.eq("SYS_CODE", "CSC");
                List<OmsOrderPreEntity> preEntt = omsOrderPreDao.selectList(queryWrapper);
                if (preEntt == null && preEntt.size() > 0) {
                    // 如果不存在，则只更新 UDF2 为 'Y'
                    String updateUDF2Sql = String.format(
                            updateUDF2,
                            "OMS_ORDER_FWXM_WORK",
                            cmn.SQLQ("Y"),
                            cmn.SQLQ(work.getWorkNo()),
                            cmn.SQLQ(work.getPreNo()),
                            cmn.SQLQ(work.getGroupCode())
                    );
                    listUpdateWorkNoSql.add(updateUDF2Sql);
                    DBHelper.execute(updateUDF2Sql);
                } else {
                    // 存在时，更新 UDF1 为新 WorkNo，UDF2 为 'N'
                    String updateUDF1Sql = String.format(
                            updateUDF1,
                            "OMS_ORDER_FWXM_WORK",
                            cmn.SQLQ(newWorkNo),
                            cmn.SQLQ("N"),
                            cmn.SQLQ(work.getWorkNo()),
                            cmn.SQLQ(work.getPreNo()),
                            cmn.SQLQ(work.getGroupCode())
                    );
                    listUpdateWorkNoSql.add(updateUDF1Sql);
                    DBHelper.execute(updateUDF1Sql);
                }

                // 遍历其他需要更新的表并添加语句
                List<String> listUpdateTable = getUpdateWorkNoTables(work, newWorkNo);
                for (String tableName : listUpdateTable) {
                    String updateWorkNoTemplateSql = String.format(
                            updateWorkNoTemplate,
                            tableName,
                            cmn.SQLQ(newWorkNo),
                            cmn.SQLQ(work.getWorkNo()),
                            cmn.SQLQ(work.getPreNo()),
                            cmn.SQLQ(work.getGroupCode())
                    );
                    listUpdateWorkNoSql.add(updateWorkNoTemplateSql);
                    DBHelper.execute(updateWorkNoTemplateSql);
                }
            }

            if (listUpdateWorkNoSql != null && listUpdateWorkNoSql.size() > 0) {
                listUpdateWorkNoSql.clear();

                    String sql3 = "UPDATE PC \n" +
                            "SET PC.ZP_SEQ_NO = (SELECT XL.WORK_NO\n" +
                            "        FROM OMS_ORDER_FWXM_TMS_XL_XL XL\n" +
                            "         INNER JOIN OMS_ORDER_FWXM_TMS_XL_XL_LY_PC ZPC ON ZPC.SEQ_NO = XL.SEQ_NO\n" +
                            "         INNER JOIN OMS_ORDER_FWXM_TMS_XL_XL_LY_PC BPC ON ZPC.BP_SEQ_NO =  BPC.ZP_SEQ_NO \n" +
                            "        WHERE PC.SEQ_NO = BPC.SEQ_NO) \n" +
                            "FROM OMS_ORDER_FWXM_TMS_XL_XL_LY_PC PC WHERE  PC.ZP_SEQ_NO IN (SELECT LPC.ZP_SEQ_NO FROM OMS_ORDER_FWXM_TMS_XL_XL_LY_PC LPC WHERE LPC.PRE_NO = " + cmn.SQLQ(preNo) + " AND LPC.GROUP_CODE =" + cmn.SQLQ(groupCode) + "  AND LPC.ZP_SEQ_NO = LPC.BP_SEQ_NO) ";
                    DBHelper.execute(sql3);

                    // 修改LY_PC表 ZP_SEQ_NO BP_SEQ_NO（非Oracle语法）
                    String sql4 = " UPDATE  PC SET PC.BP_SEQ_NO = (SELECT XL.WORK_NO FROM OMS_ORDER_FWXM_TMS_XL_XL XL WHERE PC.SEQ_NO =XL.SEQ_NO) \n" +
                            "FROM OMS_ORDER_FWXM_TMS_XL_XL_LY_PC PC WHERE PC.PRE_NO =" + cmn.SQLQ(preNo) + " AND PC.GROUP_CODE = " + cmn.SQLQ(groupCode) + " ";
                    DBHelper.execute(sql4);

            }
        }
    }

    /// <summary>
    /// 获取每个服务项目应该要更新的表
    /// </summary>
    /// <param name="work"></param>
    /// <param name="newWorkNo"></param>
    /// <returns></returns>
    private List<String> getUpdateWorkNoTables(OmsOrderFwxmWorkEntity work, String newWorkNo) {
        List<String> listUpdeateTable = new ArrayList<>();

        //String code = work.getFwxmCode();
//        OrderEnum.ServiceItem fwxmCode = OrderEnum.ServiceItem.fromCode(code);
//
//        switch (fwxmCode) {
//            default:
//                if (OmsHelper.isBgdFwxm(work.getFwxmCode())) {
//                    listUpdeateTable.add("OMS_ORDER_FWXM_BGBJ");
//                } else if (OmsHelper.isHzqdFwxm(work.getFwxmCode())) {
//                    listUpdeateTable.add("OMS_ORDER_FWXM_BGBJ");
//                } else if (OmsHelper.isBgbjCrkdFwxm(work.getFwxmCode())) {
//                    listUpdeateTable.add("OMS_ORDER_FWXM_BGBJ");
//                } else if (OmsHelper.isBgdHzqdFwxm(work.getFwxmCode())) {
//                    listUpdeateTable.add("OMS_ORDER_FWXM_BGBJ");
//                } else if (OmsHelper.isTmsHyFwxm(work.getFwxmCode())) {
//                    throw new BaseException("未实现的统一获取每个服务项目应该要更新的表");
//                } else if (OmsHelper.isTmsLyFwxm(work.getFwxmCode())) {
//                    throw new BaseException("未实现的统一获取每个服务项目应该要更新的表");
//                } else if (OmsHelper.isTmsKyFwxm(work.getFwxmCode())) {
//                    throw new BaseException("未实现的统一获取每个服务项目应该要更新的表");
//                } else if (OmsHelper.isTmsTlFwxm(work.getFwxmCode())) {
//                    throw new BaseException("未实现的统一获取每个服务项目应该要更新的表");
//                } else {
//                    throw new BaseException("服务项目错误！");
//                }
//                break;
//        }

        if (OmsHelper.isBgdFwxm(work.getFwxmCode())) {
            listUpdeateTable.add("OMS_ORDER_FWXM_BGBJ");
        } else if (OmsHelper.isHzqdFwxm(work.getFwxmCode())) {
            listUpdeateTable.add("OMS_ORDER_FWXM_BGBJ");
        } else if (OmsHelper.isBgbjCrkdFwxm(work.getFwxmCode())) {
            listUpdeateTable.add("OMS_ORDER_FWXM_BGBJ");
        } else if (OmsHelper.isBgdHzqdFwxm(work.getFwxmCode())) {
            listUpdeateTable.add("OMS_ORDER_FWXM_BGBJ");
        } else if (OmsHelper.isTmsHyFwxm(work.getFwxmCode())) {
            throw new BaseException("未实现的统一获取每个服务项目应该要更新的表");
        } else if (OmsHelper.isTmsLyFwxm(work.getFwxmCode())) {
            throw new BaseException("未实现的统一获取每个服务项目应该要更新的表");
        } else if (OmsHelper.isTmsKyFwxm(work.getFwxmCode())) {
            throw new BaseException("未实现的统一获取每个服务项目应该要更新的表");
        } else if (OmsHelper.isTmsTlFwxm(work.getFwxmCode())) {
            throw new BaseException("未实现的统一获取每个服务项目应该要更新的表");
        } else {
            throw new BaseException("服务项目错误！");
        }

        listUpdeateTable.add("OMS_ORDER_FWXM_WORK");

        return listUpdeateTable;
    }


    public List<EntityBase> searchByEntity(OmsOrderFwxmWorkEntity entity) {
        String sql = "SELECT * ";
        sql += " FROM OMS_ORDER_FWXM_WORK A ";
        sql += " WHERE A.GROUP_CODE=" + cmn.SQLQ(UserContext.getUserInfo().getCompanyCode());

        if (StringUtils.hasValue(entity.getOrderNo())) {
            sql += " AND A.ORDER_NO=" + cmn.SQLQ(entity.getOrderNo());
        }

        if (StringUtils.hasValue(entity.getResponseCode())) {
            sql += " AND A.RESPONSE_CODE=" + cmn.SQLQ(entity.getResponseCode());
        }

        return DBHelper.selectList(sql, EntityBase.class);
    }

    /**
     * 批量作业完成
     */
    public boolean batchTaskSave(ReqOrderFwxmWorkEntity entity) {
        String poStatus = OmsHelper.getFeedbackDocStatus(entity.getType());

        List<OmsOrderFwxmWorkEntity> workList = omsOrderFwxmWorkDao.select()
                .eq(OmsOrderFwxmWorkEntity::getOrderNo, entity.getOrderNo())
                .eq(OmsOrderFwxmWorkEntity::getGroupCode, UserContext.getUserInfo().getCompanyCode())
                .eq(OmsOrderFwxmWorkEntity::getResponseCode, OrderEnum.OrderWorkResponse.NO.getCode())
                .list();

        if (workList.size() == 0) {
            throw new BaseException("不存在反馈作业数据方式=无作业数据反馈的协作任务，无法进行批量操作！");
        }

        boolean xzjdFlag = workList.stream().anyMatch(item -> !item.getStatus().equals(OrderEnum.WorkStatus.XZJD));
        boolean ynFlag = workList.stream().anyMatch(item -> item.getOpCompleteOk().equals(Enums.YNStatus.N));

        if (OrderEnum.DocStatus.OP_COMPLETE_OK.equals(poStatus) && xzjdFlag) {
            throw new BaseException("协作任务非【协作接单】状态，不允许此操作");
        }

        if (OrderEnum.DocStatus.DATA_OK.equals(poStatus) && ynFlag) {
            throw new BaseException("协作任务未作业完成，不允许此操作！");
        }

        for (OmsOrderFwxmWorkEntity item : workList) {
            OmsIBillStatusEntity billStatus = new OmsIBillStatusEntity();
            billStatus.setBizRegId(item.getBizRegId());
            billStatus.setDocStatus(poStatus);
            billStatus.setGuid(IdWorker.get32UUID());
            billStatus.setOkDate(new java.util.Date());
            billStatus.setOpDate(new java.util.Date());
            billStatus.setOpFlag("0");
            billStatus.setOrderNo(item.getOrderNo());
            billStatus.setSysCode(StringUtils.hasValue(item.getSysCode()) ? item.getSysCode() : "OMS");
            billStatus.setTrnDate(new java.util.Date());
            billStatus.setWorkNo(item.getWorkNo());
            billStatus.setXzwtNo(item.getXzwtNo());
            omsIBillStatusDao.insert(billStatus);
        }

        return true;
    }


    /**
     * 判断是否作业齐全|作业完成标识
     * **/
    public List<OmsOrderFwxmWorkEntity> selectOrderWork(OmsOrderFwxmWorkEntity queryEntity) {
        String sql = "SELECT *  FROM  OMS_ORDER_FWXM_WORK  WHERE WORK_NO="+cmn.SQLQ(queryEntity.getWorkNo())+" AND  GROUP_CODE= " + cmn.SQLQ(UserContext.getUserInfo().getCompanyCode());

        return DBHelper.selectList(sql, OmsOrderFwxmWorkEntity.class);
    }
}