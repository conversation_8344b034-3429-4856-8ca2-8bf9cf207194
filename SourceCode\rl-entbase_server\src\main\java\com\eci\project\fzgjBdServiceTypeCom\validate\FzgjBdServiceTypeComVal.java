package com.eci.project.fzgjBdServiceTypeCom.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjBdServiceTypeCom.entity.FzgjBdServiceTypeComEntity;

import org.springframework.stereotype.Service;


/**
* 企业级服务类型Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-03-13
*/
@Service
public class FzgjBdServiceTypeComVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjBdServiceTypeComEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjBdServiceTypeComEntity entity, BusinessType businessType) {

    }

}
