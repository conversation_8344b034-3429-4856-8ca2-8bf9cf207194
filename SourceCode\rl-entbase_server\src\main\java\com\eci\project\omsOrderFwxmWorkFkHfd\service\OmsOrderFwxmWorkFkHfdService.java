package com.eci.project.omsOrderFwxmWorkFkHfd.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.omsOrderFwxmWorkFkHfd.dao.OmsOrderFwxmWorkFkHfdDao;
import com.eci.project.omsOrderFwxmWorkFkHfd.entity.OmsOrderFwxmWorkFkHfdEntity;
import com.eci.project.omsOrderFwxmWorkFkHfd.validate.OmsOrderFwxmWorkFkHfdVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 反馈内容-核放单Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-06-03
*/
@Service
@Slf4j
public class OmsOrderFwxmWorkFkHfdService implements EciBaseService<OmsOrderFwxmWorkFkHfdEntity> {

    @Autowired
    private OmsOrderFwxmWorkFkHfdDao omsOrderFwxmWorkFkHfdDao;

    @Autowired
    private OmsOrderFwxmWorkFkHfdVal omsOrderFwxmWorkFkHfdVal;


    @Override
    public TgPageInfo queryPageList(OmsOrderFwxmWorkFkHfdEntity entity) {
        EciQuery<OmsOrderFwxmWorkFkHfdEntity> eciQuery = EciQuery.buildQuery(entity);
        List<OmsOrderFwxmWorkFkHfdEntity> entities = omsOrderFwxmWorkFkHfdDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderFwxmWorkFkHfdEntity save(OmsOrderFwxmWorkFkHfdEntity entity) {
        // 返回实体对象
        OmsOrderFwxmWorkFkHfdEntity omsOrderFwxmWorkFkHfdEntity = null;
        omsOrderFwxmWorkFkHfdVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            omsOrderFwxmWorkFkHfdEntity = omsOrderFwxmWorkFkHfdDao.insertOne(entity);

        }else{

            omsOrderFwxmWorkFkHfdEntity = omsOrderFwxmWorkFkHfdDao.updateByEntityId(entity);

        }
        return omsOrderFwxmWorkFkHfdEntity;
    }

    @Override
    public List<OmsOrderFwxmWorkFkHfdEntity> selectList(OmsOrderFwxmWorkFkHfdEntity entity) {
        return omsOrderFwxmWorkFkHfdDao.selectList(entity);
    }

    @Override
    public OmsOrderFwxmWorkFkHfdEntity selectOneById(Serializable id) {
        return omsOrderFwxmWorkFkHfdDao.selectById(id);
    }


    @Override
    public void insertBatch(List<OmsOrderFwxmWorkFkHfdEntity> list) {
        omsOrderFwxmWorkFkHfdDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return omsOrderFwxmWorkFkHfdDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return omsOrderFwxmWorkFkHfdDao.deleteById(id);
    }

}