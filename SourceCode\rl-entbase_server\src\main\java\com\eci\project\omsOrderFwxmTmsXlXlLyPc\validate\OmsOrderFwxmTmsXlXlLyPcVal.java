package com.eci.project.omsOrderFwxmTmsXlXlLyPc.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.omsOrderFwxmTmsXlXlLyPc.entity.OmsOrderFwxmTmsXlXlLyPcEntity;

import org.springframework.stereotype.Service;


/**
* 委托内容-程运序列-陆运-拼车Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-06-05
*/
@Service
public class OmsOrderFwxmTmsXlXlLyPcVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(OmsOrderFwxmTmsXlXlLyPcEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(OmsOrderFwxmTmsXlXlLyPcEntity entity, BusinessType businessType) {

    }

}
