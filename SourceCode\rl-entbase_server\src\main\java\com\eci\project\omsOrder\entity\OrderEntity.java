package com.eci.project.omsOrder.entity;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data; // 引入 Lombok 的 Data 注解
import java.time.LocalDateTime; // 引入 LocalDateTime

@Data // 自动生成 Getter, Setter, equals, hashCode, toString
public class OrderEntity {

    /**
     * OMS订单号
     */
    @JSONField(name = "ORDER_NO")
    private String ORDER_NO;

    /**
     * 协作任务编号
     */
    @JSONField(name = "WORK_NO")
    private String WORK_NO;

    /**
     * 服务项目名称
     */
    @JSONField(name = "FWXM_CODE")
    private String FWXM_CODE;

    /**
     * 客户单据编号（企业内控编号）
     */
    @JSONField(name = "CUSTOMER_ORDER_NO")
    private String CUSTOMER_ORDER_NO;

    /**
     * 境内收发货人
     */
    @JSONField(name = "JNSFHR")
    private String JNSFHR;

    /**
     * 进出标志
     */
    @JSONField(name = "I_E_TYPE")
    private String I_E_TYPE;

    /**
     * 货代/厂商
     */
    @JSONField(name = "HDCS")
    private String HDCS;

    /**
     * 报关类型
     */
    @JSONField(name = "BH_TYPE")
    private String BH_TYPE;

    /**
     * 运输方式
     */
    @JSONField(name = "YSFS")
    private String YSFS;

    /**
     * 申报地海关
     */
    @JSONField(name = "BGD_CUSTOM_CODE")
    private String BGD_CUSTOM_CODE;

    /**
     * 内部作业组织（录入企业）
     */
    @JSONField(name = "JD_NODE")
    private String JD_NODE;

    /**
     * 接单人
     */
    @JSONField(name = "JD_USER")
    private String JD_USER;

    /**
     * 接单确认时间
     */
    @JSONField(name = "CONFIRM_DATE")
    private String CONFIRM_DATE;

    /**
     * 箱单号
     */
    @JSONField(name = "XDH")
    private String XDH;

    /**
     * 其它参考号
     */
    @JSONField(name = "QTCKH")
    private String QTCKH;

    /**
     * 报检类型
     */
    @JSONField(name = "BJ_TYPE")
    private String BJ_TYPE;

    /**
     * 进出境关别
     */
    @JSONField(name = "I_E_PORT")
    private String I_E_PORT;

    /**
     * 其他特殊要求（ORDER_FWXM_TMS_XL_XL）
     */
    @JSONField(name = "OTHER_MEMO")
    private String OTHER_MEMO;

    /**
     * 企业海关代码
     */
    @JSONField(name = "CUSTOM_CODE")
    private String CUSTOM_CODE;

    /**
     * 接单时间
     */
    @JSONField(name = "OP_DATE")
    private String OP_DATE;
}