package com.eci.project.etmsCrmEnterprise.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 对象 ETMS_CRM_ENTERPRISE
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-06-13
*/
@ApiModel("")
@TableName("ETMS_CRM_ENTERPRISE")
@FieldNameConstants
public class EtmsCrmEnterpriseEntity extends EciBaseEntity{
    @ApiModelProperty("(32)")
    @TableField("GUID")
    private String guid;

    @ApiModelProperty("(20)")
    @TableField("CODE")
    private String code;

    @ApiModelProperty("(100)")
    @TableField("NAME")
    private String name;

    @ApiModelProperty("(50)")
    @TableField("SHORT_NAME")
    private String shortName;

    @ApiModelProperty("(100)")
    @TableField("EN_NAME")
    private String enName;

    @ApiModelProperty("(50)")
    @TableField("OTHER_NAME")
    private String otherName;

    @ApiModelProperty("(10)")
    @TableField("CUSTOM_NO")
    private String customNo;

    @ApiModelProperty("(80)")
    @TableField("ADDRESS")
    private String address;

    @ApiModelProperty("(80)")
    @TableField("ADDRESS_EN")
    private String addressEn;

    @ApiModelProperty("(10)")
    @TableField("PERSON")
    private String person;

    @ApiModelProperty("(15)")
    @TableField("TEL1")
    private String tel1;

    @ApiModelProperty("(15)")
    @TableField("TEL2")
    private String tel2;

    @ApiModelProperty("(15)")
    @TableField("FAX")
    private String fax;

    @ApiModelProperty("(40)")
    @TableField("EMAIL")
    private String email;

    @ApiModelProperty("(8)")
    @TableField("ZIP")
    private String zip;

    @ApiModelProperty("(10)")
    @TableField("COUNTRY")
    private String country;

    @ApiModelProperty("(10)")
    @TableField("STATE")
    private String state;

    @ApiModelProperty("(10)")
    @TableField("CITY")
    private String city;

    @ApiModelProperty("(0)")
    @TableField("CONTRACT")
    private String contract;

    @ApiModelProperty("(0)")
    @TableField("CONTRACT_FROM_DATE")
    private String contractFromDate;

    @ApiModelProperty("(0)")
    @TableField("CONTRACT_TO_DATE")
    private String contractToDate;

    @ApiModelProperty("(0)")
    @TableField("ACCOUNT")
    private String account;

    @ApiModelProperty("(0)")
    @TableField("BANK")
    private String bank;

    @ApiModelProperty("(30)")
    @TableField("TAX")
    private String tax;

    @ApiModelProperty("(0)")
    @TableField("CREDIT_RATING")
    private String creditRating;

    @ApiModelProperty("(0)")
    @TableField("CREDIT_LIMIT")
    private String creditLimit;

    @ApiModelProperty("(0)")
    @TableField("CHARGE_CYCLE")
    private String chargeCycle;

    @ApiModelProperty("(0)")
    @TableField("TREATY")
    private String treaty;

    @ApiModelProperty("(0)")
    @TableField("NOTES")
    private String notes;

    @ApiModelProperty("(1)")
    @TableField("STATUS")
    private String status;

    @ApiModelProperty("(200)")
    @TableField("MEMO")
    private String memo;

    @ApiModelProperty("(20)")
    @TableField("CREATE_USER")
    private String createUser;

    @ApiModelProperty("(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("结束")
    @TableField(exist=false)
    private Date createDateEnd;

    @ApiModelProperty("(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    @ApiModelProperty("(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    @ApiModelProperty("(200)")
    @TableField("COMPANY_TYPE")
    private String companyType;

    @ApiModelProperty("(300)")
    @TableField("COMPANY_TYPE_NAME")
    private String companyTypeName;

    @ApiModelProperty("(0)")
    @TableField("FLAG")
    private String flag;

    @ApiModelProperty("(40)")
    @TableField("CUSTOMER_B2B")
    private String customerB2b;

    @ApiModelProperty("(200)")
    @TableField("LOGO")
    private String logo;

    @ApiModelProperty("(200)")
    @TableField("APPNAME")
    private String appname;

    @ApiModelProperty("(7)")
    @TableField("END_USER_DATE")
    private Date endUserDate;

    @ApiModelProperty("开始")
    @TableField(exist=false)
    private Date endUserDateStart;

    @ApiModelProperty("结束")
    @TableField(exist=false)
    private Date endUserDateEnd;

    @ApiModelProperty("(1)")
    @TableField("AUTO_CREATE_ARAP")
    private String autoCreateArap;

    @ApiModelProperty("(50)")
    @TableField("START_NO")
    private String startNo;

    @ApiModelProperty("(1)")
    @TableField("IS_USE_TRADE")
    private String isUseTrade;

    @ApiModelProperty("(0)")
    @TableField("SMTP_EMAIL")
    private String smtpEmail;

    @ApiModelProperty("(0)")
    @TableField("SMTP_EMAIL_USER")
    private String smtpEmailUser;

    @ApiModelProperty("(0)")
    @TableField("SMTP_EMAIL_PWD")
    private String smtpEmailPwd;

    @ApiModelProperty("(0)")
    @TableField("SMTP_EMAIL_HOST")
    private String smtpEmailHost;

    @ApiModelProperty("(0)")
    @TableField("FCOMPANY_CODE")
    private String fcompanyCode;

    @ApiModelProperty("(0)")
    @TableField("BMS_SYS")
    private String bmsSys;

    @ApiModelProperty("(200)")
    @TableField("SYS_URL")
    private String sysUrl;

    @ApiModelProperty("(50)")
    @TableField("CLIENT_COMPANY_CODE")
    private String clientCompanyCode;

    @ApiModelProperty("(50)")
    @TableField("SSO_COMPANY_GUID")
    private String ssoCompanyGuid;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public EtmsCrmEnterpriseEntity() {
        this.setSubClazz(EtmsCrmEnterpriseEntity.class);
    }

    public EtmsCrmEnterpriseEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public EtmsCrmEnterpriseEntity setCode(String code) {
        this.code = code;
        this.nodifySetFiled("code", code);
        return this;
    }

    public String getCode() {
        this.nodifyGetFiled("code");
        return code;
    }

    public EtmsCrmEnterpriseEntity setName(String name) {
        this.name = name;
        this.nodifySetFiled("name", name);
        return this;
    }

    public String getName() {
        this.nodifyGetFiled("name");
        return name;
    }

    public EtmsCrmEnterpriseEntity setShortName(String shortName) {
        this.shortName = shortName;
        this.nodifySetFiled("shortName", shortName);
        return this;
    }

    public String getShortName() {
        this.nodifyGetFiled("shortName");
        return shortName;
    }

    public EtmsCrmEnterpriseEntity setEnName(String enName) {
        this.enName = enName;
        this.nodifySetFiled("enName", enName);
        return this;
    }

    public String getEnName() {
        this.nodifyGetFiled("enName");
        return enName;
    }

    public EtmsCrmEnterpriseEntity setOtherName(String otherName) {
        this.otherName = otherName;
        this.nodifySetFiled("otherName", otherName);
        return this;
    }

    public String getOtherName() {
        this.nodifyGetFiled("otherName");
        return otherName;
    }

    public EtmsCrmEnterpriseEntity setCustomNo(String customNo) {
        this.customNo = customNo;
        this.nodifySetFiled("customNo", customNo);
        return this;
    }

    public String getCustomNo() {
        this.nodifyGetFiled("customNo");
        return customNo;
    }

    public EtmsCrmEnterpriseEntity setAddress(String address) {
        this.address = address;
        this.nodifySetFiled("address", address);
        return this;
    }

    public String getAddress() {
        this.nodifyGetFiled("address");
        return address;
    }

    public EtmsCrmEnterpriseEntity setAddressEn(String addressEn) {
        this.addressEn = addressEn;
        this.nodifySetFiled("addressEn", addressEn);
        return this;
    }

    public String getAddressEn() {
        this.nodifyGetFiled("addressEn");
        return addressEn;
    }

    public EtmsCrmEnterpriseEntity setPerson(String person) {
        this.person = person;
        this.nodifySetFiled("person", person);
        return this;
    }

    public String getPerson() {
        this.nodifyGetFiled("person");
        return person;
    }

    public EtmsCrmEnterpriseEntity setTel1(String tel1) {
        this.tel1 = tel1;
        this.nodifySetFiled("tel1", tel1);
        return this;
    }

    public String getTel1() {
        this.nodifyGetFiled("tel1");
        return tel1;
    }

    public EtmsCrmEnterpriseEntity setTel2(String tel2) {
        this.tel2 = tel2;
        this.nodifySetFiled("tel2", tel2);
        return this;
    }

    public String getTel2() {
        this.nodifyGetFiled("tel2");
        return tel2;
    }

    public EtmsCrmEnterpriseEntity setFax(String fax) {
        this.fax = fax;
        this.nodifySetFiled("fax", fax);
        return this;
    }

    public String getFax() {
        this.nodifyGetFiled("fax");
        return fax;
    }

    public EtmsCrmEnterpriseEntity setEmail(String email) {
        this.email = email;
        this.nodifySetFiled("email", email);
        return this;
    }

    public String getEmail() {
        this.nodifyGetFiled("email");
        return email;
    }

    public EtmsCrmEnterpriseEntity setZip(String zip) {
        this.zip = zip;
        this.nodifySetFiled("zip", zip);
        return this;
    }

    public String getZip() {
        this.nodifyGetFiled("zip");
        return zip;
    }

    public EtmsCrmEnterpriseEntity setCountry(String country) {
        this.country = country;
        this.nodifySetFiled("country", country);
        return this;
    }

    public String getCountry() {
        this.nodifyGetFiled("country");
        return country;
    }

    public EtmsCrmEnterpriseEntity setState(String state) {
        this.state = state;
        this.nodifySetFiled("state", state);
        return this;
    }

    public String getState() {
        this.nodifyGetFiled("state");
        return state;
    }

    public EtmsCrmEnterpriseEntity setCity(String city) {
        this.city = city;
        this.nodifySetFiled("city", city);
        return this;
    }

    public String getCity() {
        this.nodifyGetFiled("city");
        return city;
    }

    public EtmsCrmEnterpriseEntity setContract(String contract) {
        this.contract = contract;
        this.nodifySetFiled("contract", contract);
        return this;
    }

    public String getContract() {
        this.nodifyGetFiled("contract");
        return contract;
    }

    public EtmsCrmEnterpriseEntity setContractFromDate(String contractFromDate) {
        this.contractFromDate = contractFromDate;
        this.nodifySetFiled("contractFromDate", contractFromDate);
        return this;
    }

    public String getContractFromDate() {
        this.nodifyGetFiled("contractFromDate");
        return contractFromDate;
    }

    public EtmsCrmEnterpriseEntity setContractToDate(String contractToDate) {
        this.contractToDate = contractToDate;
        this.nodifySetFiled("contractToDate", contractToDate);
        return this;
    }

    public String getContractToDate() {
        this.nodifyGetFiled("contractToDate");
        return contractToDate;
    }

    public EtmsCrmEnterpriseEntity setAccount(String account) {
        this.account = account;
        this.nodifySetFiled("account", account);
        return this;
    }

    public String getAccount() {
        this.nodifyGetFiled("account");
        return account;
    }

    public EtmsCrmEnterpriseEntity setBank(String bank) {
        this.bank = bank;
        this.nodifySetFiled("bank", bank);
        return this;
    }

    public String getBank() {
        this.nodifyGetFiled("bank");
        return bank;
    }

    public EtmsCrmEnterpriseEntity setTax(String tax) {
        this.tax = tax;
        this.nodifySetFiled("tax", tax);
        return this;
    }

    public String getTax() {
        this.nodifyGetFiled("tax");
        return tax;
    }

    public EtmsCrmEnterpriseEntity setCreditRating(String creditRating) {
        this.creditRating = creditRating;
        this.nodifySetFiled("creditRating", creditRating);
        return this;
    }

    public String getCreditRating() {
        this.nodifyGetFiled("creditRating");
        return creditRating;
    }

    public EtmsCrmEnterpriseEntity setCreditLimit(String creditLimit) {
        this.creditLimit = creditLimit;
        this.nodifySetFiled("creditLimit", creditLimit);
        return this;
    }

    public String getCreditLimit() {
        this.nodifyGetFiled("creditLimit");
        return creditLimit;
    }

    public EtmsCrmEnterpriseEntity setChargeCycle(String chargeCycle) {
        this.chargeCycle = chargeCycle;
        this.nodifySetFiled("chargeCycle", chargeCycle);
        return this;
    }

    public String getChargeCycle() {
        this.nodifyGetFiled("chargeCycle");
        return chargeCycle;
    }

    public EtmsCrmEnterpriseEntity setTreaty(String treaty) {
        this.treaty = treaty;
        this.nodifySetFiled("treaty", treaty);
        return this;
    }

    public String getTreaty() {
        this.nodifyGetFiled("treaty");
        return treaty;
    }

    public EtmsCrmEnterpriseEntity setNotes(String notes) {
        this.notes = notes;
        this.nodifySetFiled("notes", notes);
        return this;
    }

    public String getNotes() {
        this.nodifyGetFiled("notes");
        return notes;
    }

    public EtmsCrmEnterpriseEntity setStatus(String status) {
        this.status = status;
        this.nodifySetFiled("status", status);
        return this;
    }

    public String getStatus() {
        this.nodifyGetFiled("status");
        return status;
    }

    public EtmsCrmEnterpriseEntity setMemo(String memo) {
        this.memo = memo;
        this.nodifySetFiled("memo", memo);
        return this;
    }

    public String getMemo() {
        this.nodifyGetFiled("memo");
        return memo;
    }

    public EtmsCrmEnterpriseEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public EtmsCrmEnterpriseEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public EtmsCrmEnterpriseEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public EtmsCrmEnterpriseEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public EtmsCrmEnterpriseEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public EtmsCrmEnterpriseEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public EtmsCrmEnterpriseEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public EtmsCrmEnterpriseEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public EtmsCrmEnterpriseEntity setCompanyType(String companyType) {
        this.companyType = companyType;
        this.nodifySetFiled("companyType", companyType);
        return this;
    }

    public String getCompanyType() {
        this.nodifyGetFiled("companyType");
        return companyType;
    }

    public EtmsCrmEnterpriseEntity setCompanyTypeName(String companyTypeName) {
        this.companyTypeName = companyTypeName;
        this.nodifySetFiled("companyTypeName", companyTypeName);
        return this;
    }

    public String getCompanyTypeName() {
        this.nodifyGetFiled("companyTypeName");
        return companyTypeName;
    }

    public EtmsCrmEnterpriseEntity setFlag(String flag) {
        this.flag = flag;
        this.nodifySetFiled("flag", flag);
        return this;
    }

    public String getFlag() {
        this.nodifyGetFiled("flag");
        return flag;
    }

    public EtmsCrmEnterpriseEntity setCustomerB2b(String customerB2b) {
        this.customerB2b = customerB2b;
        this.nodifySetFiled("customerB2b", customerB2b);
        return this;
    }

    public String getCustomerB2b() {
        this.nodifyGetFiled("customerB2b");
        return customerB2b;
    }

    public EtmsCrmEnterpriseEntity setLogo(String logo) {
        this.logo = logo;
        this.nodifySetFiled("logo", logo);
        return this;
    }

    public String getLogo() {
        this.nodifyGetFiled("logo");
        return logo;
    }

    public EtmsCrmEnterpriseEntity setAppname(String appname) {
        this.appname = appname;
        this.nodifySetFiled("appname", appname);
        return this;
    }

    public String getAppname() {
        this.nodifyGetFiled("appname");
        return appname;
    }

    public EtmsCrmEnterpriseEntity setEndUserDate(Date endUserDate) {
        this.endUserDate = endUserDate;
        this.nodifySetFiled("endUserDate", endUserDate);
        return this;
    }

    public Date getEndUserDate() {
        this.nodifyGetFiled("endUserDate");
        return endUserDate;
    }

    public EtmsCrmEnterpriseEntity setEndUserDateStart(Date endUserDateStart) {
        this.endUserDateStart = endUserDateStart;
        this.nodifySetFiled("endUserDateStart", endUserDateStart);
        return this;
    }

    public Date getEndUserDateStart() {
        this.nodifyGetFiled("endUserDateStart");
        return endUserDateStart;
    }

    public EtmsCrmEnterpriseEntity setEndUserDateEnd(Date endUserDateEnd) {
        this.endUserDateEnd = endUserDateEnd;
        this.nodifySetFiled("endUserDateEnd", endUserDateEnd);
        return this;
    }

    public Date getEndUserDateEnd() {
        this.nodifyGetFiled("endUserDateEnd");
        return endUserDateEnd;
    }
    public EtmsCrmEnterpriseEntity setAutoCreateArap(String autoCreateArap) {
        this.autoCreateArap = autoCreateArap;
        this.nodifySetFiled("autoCreateArap", autoCreateArap);
        return this;
    }

    public String getAutoCreateArap() {
        this.nodifyGetFiled("autoCreateArap");
        return autoCreateArap;
    }

    public EtmsCrmEnterpriseEntity setStartNo(String startNo) {
        this.startNo = startNo;
        this.nodifySetFiled("startNo", startNo);
        return this;
    }

    public String getStartNo() {
        this.nodifyGetFiled("startNo");
        return startNo;
    }

    public EtmsCrmEnterpriseEntity setIsUseTrade(String isUseTrade) {
        this.isUseTrade = isUseTrade;
        this.nodifySetFiled("isUseTrade", isUseTrade);
        return this;
    }

    public String getIsUseTrade() {
        this.nodifyGetFiled("isUseTrade");
        return isUseTrade;
    }

    public EtmsCrmEnterpriseEntity setSmtpEmail(String smtpEmail) {
        this.smtpEmail = smtpEmail;
        this.nodifySetFiled("smtpEmail", smtpEmail);
        return this;
    }

    public String getSmtpEmail() {
        this.nodifyGetFiled("smtpEmail");
        return smtpEmail;
    }

    public EtmsCrmEnterpriseEntity setSmtpEmailUser(String smtpEmailUser) {
        this.smtpEmailUser = smtpEmailUser;
        this.nodifySetFiled("smtpEmailUser", smtpEmailUser);
        return this;
    }

    public String getSmtpEmailUser() {
        this.nodifyGetFiled("smtpEmailUser");
        return smtpEmailUser;
    }

    public EtmsCrmEnterpriseEntity setSmtpEmailPwd(String smtpEmailPwd) {
        this.smtpEmailPwd = smtpEmailPwd;
        this.nodifySetFiled("smtpEmailPwd", smtpEmailPwd);
        return this;
    }

    public String getSmtpEmailPwd() {
        this.nodifyGetFiled("smtpEmailPwd");
        return smtpEmailPwd;
    }

    public EtmsCrmEnterpriseEntity setSmtpEmailHost(String smtpEmailHost) {
        this.smtpEmailHost = smtpEmailHost;
        this.nodifySetFiled("smtpEmailHost", smtpEmailHost);
        return this;
    }

    public String getSmtpEmailHost() {
        this.nodifyGetFiled("smtpEmailHost");
        return smtpEmailHost;
    }

    public EtmsCrmEnterpriseEntity setFcompanyCode(String fcompanyCode) {
        this.fcompanyCode = fcompanyCode;
        this.nodifySetFiled("fcompanyCode", fcompanyCode);
        return this;
    }

    public String getFcompanyCode() {
        this.nodifyGetFiled("fcompanyCode");
        return fcompanyCode;
    }

    public EtmsCrmEnterpriseEntity setBmsSys(String bmsSys) {
        this.bmsSys = bmsSys;
        this.nodifySetFiled("bmsSys", bmsSys);
        return this;
    }

    public String getBmsSys() {
        this.nodifyGetFiled("bmsSys");
        return bmsSys;
    }

    public EtmsCrmEnterpriseEntity setSysUrl(String sysUrl) {
        this.sysUrl = sysUrl;
        this.nodifySetFiled("sysUrl", sysUrl);
        return this;
    }

    public String getSysUrl() {
        this.nodifyGetFiled("sysUrl");
        return sysUrl;
    }

    public EtmsCrmEnterpriseEntity setClientCompanyCode(String clientCompanyCode) {
        this.clientCompanyCode = clientCompanyCode;
        this.nodifySetFiled("clientCompanyCode", clientCompanyCode);
        return this;
    }

    public String getClientCompanyCode() {
        this.nodifyGetFiled("clientCompanyCode");
        return clientCompanyCode;
    }

    public EtmsCrmEnterpriseEntity setSsoCompanyGuid(String ssoCompanyGuid) {
        this.ssoCompanyGuid = ssoCompanyGuid;
        this.nodifySetFiled("ssoCompanyGuid", ssoCompanyGuid);
        return this;
    }

    public String getSsoCompanyGuid() {
        this.nodifyGetFiled("ssoCompanyGuid");
        return ssoCompanyGuid;
    }

}
