package com.eci.project.omsOrderFwxmWorkXzwt.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.omsOrderFwxmWorkXzwt.entity.OmsOrderFwxmWorkXzwtEntity;

import org.springframework.stereotype.Service;


/**
* 协作委托表Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-14
*/
@Service
public class OmsOrderFwxmWorkXzwtVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(OmsOrderFwxmWorkXzwtEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(OmsOrderFwxmWorkXzwtEntity entity, BusinessType businessType) {

    }

}
