package com.eci.project.omsOrderGoodsCost.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.omsOrderGoodsCost.entity.ResOmsOrderGoodsCostEntity;
import com.eci.project.omsOrderGoodsCost.service.OmsOrderGoodsCostService;
import com.eci.project.omsOrderGoodsCost.entity.OmsOrderGoodsCostEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 货值币制表Controller
*
* @<NAME_EMAIL>
* @date 2025-06-10
*/
@Api(tags = "货值币制表")
@RestController
@RequestMapping("/omsOrderGoodsCost")
public class OmsOrderGoodsCostController extends EciBaseController {

    @Autowired
    private OmsOrderGoodsCostService omsOrderGoodsCostService;


    @ApiOperation("货值币制表:保存")
    @EciLog(title = "货值币制表:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody OmsOrderGoodsCostEntity entity){
        OmsOrderGoodsCostEntity omsOrderGoodsCostEntity =omsOrderGoodsCostService.save(entity);
        return ResponseMsgUtil.success(10001,omsOrderGoodsCostEntity);
    }


    @ApiOperation("货值币制表:查询列表")
    @EciLog(title = "货值币制表:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody OmsOrderGoodsCostEntity entity){
        List<OmsOrderGoodsCostEntity> omsOrderGoodsCostEntities = omsOrderGoodsCostService.selectList(entity);
        return ResponseMsgUtil.success(10001,omsOrderGoodsCostEntities);
    }


    @ApiOperation("货值币制表:分页查询列表")
    @EciLog(title = "货值币制表:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody OmsOrderGoodsCostEntity entity){
        TgPageInfo tgPageInfo = omsOrderGoodsCostService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("货值币制表:根据ID查一条")
    @EciLog(title = "货值币制表:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody OmsOrderGoodsCostEntity entity){
        OmsOrderGoodsCostEntity  omsOrderGoodsCostEntity = omsOrderGoodsCostService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,omsOrderGoodsCostEntity);
    }


    @ApiOperation("货值币制表:根据ID删除一条")
    @EciLog(title = "货值币制表:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody OmsOrderGoodsCostEntity entity){
        int count = omsOrderGoodsCostService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("货值币制表:根据ID字符串删除多条")
    @EciLog(title = "货值币制表:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody OmsOrderGoodsCostEntity entity) {
        int count = omsOrderGoodsCostService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }

    @ApiOperation("货值币制表:加载货值币制")
    @EciLog(title = "货值币制表:加载货值币制", businessType = BusinessType.SELECT)
    @PostMapping("/loadOrderGoodsCost")
    @EciAction()
    public ResponseMsg loadOrderGoodsCost(@RequestBody OmsOrderGoodsCostEntity entity){
        List<ResOmsOrderGoodsCostEntity> omsOrderGoodsCostEntities = omsOrderGoodsCostService.loadOrderGoodsCost(entity);
        return ResponseMsgUtil.success(10001,omsOrderGoodsCostEntities);
    }
}