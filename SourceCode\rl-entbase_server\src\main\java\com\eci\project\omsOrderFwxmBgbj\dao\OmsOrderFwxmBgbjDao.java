package com.eci.project.omsOrderFwxmBgbj.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.omsOrderFwxmBgbj.entity.OmsOrderFwxmBgbjEntity;


/**
* 委托内容-报关报检Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-05-14
*/
public interface OmsOrderFwxmBgbjDao extends EciBaseDao<OmsOrderFwxmBgbjEntity> {

}