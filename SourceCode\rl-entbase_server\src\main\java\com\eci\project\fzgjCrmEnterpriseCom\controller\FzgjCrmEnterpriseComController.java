package com.eci.project.fzgjCrmEnterpriseCom.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjCrmEnterpriseCom.service.FzgjCrmEnterpriseComService;
import com.eci.project.fzgjCrmEnterpriseCom.entity.FzgjCrmEnterpriseComEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 平台受理企业Controller
*
* @<NAME_EMAIL>
* @date 2025-03-13
*/
@Api(tags = "平台受理企业")
@RestController
@RequestMapping("/fzgjCrmEnterpriseCom")
public class FzgjCrmEnterpriseComController extends EciBaseController {

    @Autowired
    private FzgjCrmEnterpriseComService fzgjCrmEnterpriseComService;


    @ApiOperation("平台受理企业:保存")
    @EciLog(title = "平台受理企业:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody FzgjCrmEnterpriseComEntity entity){
        FzgjCrmEnterpriseComEntity fzgjCrmEnterpriseComEntity =fzgjCrmEnterpriseComService.save(entity);
        return ResponseMsgUtil.success(10001,fzgjCrmEnterpriseComEntity);
    }


    @ApiOperation("平台受理企业:查询列表")
    @EciLog(title = "平台受理企业:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjCrmEnterpriseComEntity entity){
        List<FzgjCrmEnterpriseComEntity> fzgjCrmEnterpriseComEntities = fzgjCrmEnterpriseComService.selectList(entity);
        return ResponseMsgUtil.success(10001,fzgjCrmEnterpriseComEntities);
    }


    @ApiOperation("平台受理企业:分页查询列表")
    @EciLog(title = "平台受理企业:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjCrmEnterpriseComEntity entity){
        TgPageInfo tgPageInfo = fzgjCrmEnterpriseComService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("平台受理企业:根据ID查一条")
    @EciLog(title = "平台受理企业:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjCrmEnterpriseComEntity entity){
        FzgjCrmEnterpriseComEntity  fzgjCrmEnterpriseComEntity = fzgjCrmEnterpriseComService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,fzgjCrmEnterpriseComEntity);
    }


    @ApiOperation("平台受理企业:根据ID删除一条")
    @EciLog(title = "平台受理企业:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjCrmEnterpriseComEntity entity){
        int count = fzgjCrmEnterpriseComService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("平台受理企业:根据ID字符串删除多条")
    @EciLog(title = "平台受理企业:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjCrmEnterpriseComEntity entity) {
        int count = fzgjCrmEnterpriseComService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}