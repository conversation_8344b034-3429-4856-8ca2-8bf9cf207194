package com.eci.project.fzgjCrmEnterpriseSys.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjCrmEnterpriseSys.dao.FzgjCrmEnterpriseSysDao;
import com.eci.project.fzgjCrmEnterpriseSys.entity.FzgjCrmEnterpriseSysEntity;
import com.eci.project.fzgjCrmEnterpriseSys.validate.FzgjCrmEnterpriseSysVal;

import com.eci.project.fzgjFileType.entity.FzgjFileTypeEntity;
import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.List;


/**
 * 作业系统-数据归属集团Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-03-13
 */
@Service
@Slf4j
public class FzgjCrmEnterpriseSysService implements EciBaseService<FzgjCrmEnterpriseSysEntity> {

    @Autowired
    private FzgjCrmEnterpriseSysDao fzgjCrmEnterpriseSysDao;

    @Autowired
    private FzgjCrmEnterpriseSysVal fzgjCrmEnterpriseSysVal;


    @Override
    public TgPageInfo queryPageList(FzgjCrmEnterpriseSysEntity entity) {
        EciQuery<FzgjCrmEnterpriseSysEntity> eciQuery = EciQuery.buildQuery(entity);
        eciQuery.eq(FzgjCrmEnterpriseSysEntity::getGroupCode, UserContext.getUserInfo().getCompanyCode());
        List<FzgjCrmEnterpriseSysEntity> entities = fzgjCrmEnterpriseSysDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjCrmEnterpriseSysEntity save(FzgjCrmEnterpriseSysEntity entity) {
        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setCreateDate(new java.util.Date());
            entity.setUpdateDate(new java.util.Date());
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        } else {
            entity.setUpdateDate(new java.util.Date());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        }
        // 返回实体对象
        FzgjCrmEnterpriseSysEntity fzgjCrmEnterpriseSysEntity = null;
        fzgjCrmEnterpriseSysVal.saveValidate(entity, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjCrmEnterpriseSysEntity = fzgjCrmEnterpriseSysDao.insertOne(entity);

        } else {

            fzgjCrmEnterpriseSysEntity = fzgjCrmEnterpriseSysDao.updateByEntityId(entity);

        }
        return fzgjCrmEnterpriseSysEntity;
    }

    @Transactional(rollbackFor = Exception.class)
    public FzgjCrmEnterpriseSysEntity save(String jsonString) {

        FzgjCrmEnterpriseSysEntity entity = null;
        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setCreateDate(new java.util.Date());
            entity.setUpdateDate(new java.util.Date());
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        } else {
            entity.setUpdateDate(new java.util.Date());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        }
        // 返回实体对象
        FzgjCrmEnterpriseSysEntity fzgjCrmEnterpriseSysEntity = null;
        fzgjCrmEnterpriseSysVal.saveValidate(entity, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjCrmEnterpriseSysEntity = fzgjCrmEnterpriseSysDao.insertOne(entity);

        } else {

            fzgjCrmEnterpriseSysEntity = fzgjCrmEnterpriseSysDao.updateByEntityId(entity);

        }
        return fzgjCrmEnterpriseSysEntity;
    }

    @Override
    public List<FzgjCrmEnterpriseSysEntity> selectList(FzgjCrmEnterpriseSysEntity entity) {
        if (entity == null) {
            entity = new FzgjCrmEnterpriseSysEntity();
        }
        entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
        return fzgjCrmEnterpriseSysDao.selectList(entity);
    }

    @Override
    public FzgjCrmEnterpriseSysEntity selectOneById(Serializable id) {
        return fzgjCrmEnterpriseSysDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjCrmEnterpriseSysEntity> list) {
        fzgjCrmEnterpriseSysDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjCrmEnterpriseSysDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjCrmEnterpriseSysDao.deleteById(id);
    }

}