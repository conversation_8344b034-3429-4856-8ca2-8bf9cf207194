package com.eci.project.crmCustomerHzTrade.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.crmCustomerHzTrade.entity.CrmCustomerHzTradeEntity;

import org.springframework.stereotype.Service;


/**
* 业务伙伴货主贸易关系Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-05-20
*/
@Service
public class CrmCustomerHzTradeVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(CrmCustomerHzTradeEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(CrmCustomerHzTradeEntity entity, BusinessType businessType) {

    }

}
