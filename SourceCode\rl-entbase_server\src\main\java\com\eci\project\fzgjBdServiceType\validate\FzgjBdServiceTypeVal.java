package com.eci.project.fzgjBdServiceType.validate;

import com.eci.common.validations.ZsrValidationUtil;
import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjBdServiceType.entity.FzgjBdServiceTypeEntity;

import org.springframework.stereotype.Service;


/**
* 服务类型Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-03-13
*/
@Service
public class FzgjBdServiceTypeVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjBdServiceTypeEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjBdServiceTypeEntity entity, BusinessType businessType) throws IllegalAccessException {
        ZsrValidationUtil.validation(entity);
    }

}
