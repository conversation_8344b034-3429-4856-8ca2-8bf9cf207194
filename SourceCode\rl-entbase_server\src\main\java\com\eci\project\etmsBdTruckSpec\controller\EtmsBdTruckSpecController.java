package com.eci.project.etmsBdTruckSpec.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.etmsBdTruckSpec.service.EtmsBdTruckSpecService;
import com.eci.project.etmsBdTruckSpec.entity.EtmsBdTruckSpecEntity;
import com.eci.project.fzgjBdHx.entity.FzgjBdHxEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 车辆规则Controller
*
* @<NAME_EMAIL>
* @date 2025-03-21
*/
@Api(tags = "车辆规则")
@RestController
@RequestMapping("/etmsBdTruckSpec")
public class EtmsBdTruckSpecController extends EciBaseController {

    @Autowired
    private EtmsBdTruckSpecService etmsBdTruckSpecService;


    @ApiOperation("车辆规则:保存")
    @EciLog(title = "车辆规则:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody EtmsBdTruckSpecEntity entity){
        EtmsBdTruckSpecEntity etmsBdTruckSpecEntity =etmsBdTruckSpecService.save(entity);
        return ResponseMsgUtil.success(10001,etmsBdTruckSpecEntity);
    }
    @ApiOperation("车辆规格:导出")
    @EciLog(title = "车辆规格:导出", businessType = BusinessType.SELECT)
    @PostMapping("/ExportExcel")
    @EciAction()
    public ResponseMsg ExportExcel(@RequestBody EtmsBdTruckSpecEntity entity){
        etmsBdTruckSpecService.Export(entity);
        return ResponseMsgUtil.success(10001,null);
    }

    @ApiOperation("车辆规则:查询列表")
    @EciLog(title = "车辆规则:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody EtmsBdTruckSpecEntity entity){
        List<EtmsBdTruckSpecEntity> etmsBdTruckSpecEntities = etmsBdTruckSpecService.selectList(entity);
        return ResponseMsgUtil.success(10001,etmsBdTruckSpecEntities);
    }


    @ApiOperation("车辆规则:分页查询列表")
    @EciLog(title = "车辆规则:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody EtmsBdTruckSpecEntity entity){
        TgPageInfo tgPageInfo = etmsBdTruckSpecService.queryPageList(entity);
        return ResponseMsgUtil.successPlus(10001,tgPageInfo);
    }


    @ApiOperation("车辆规则:根据ID查一条")
    @EciLog(title = "车辆规则:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody EtmsBdTruckSpecEntity entity){
        EtmsBdTruckSpecEntity  etmsBdTruckSpecEntity = etmsBdTruckSpecService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.successPlus(10001,etmsBdTruckSpecEntity);
    }


    @ApiOperation("车辆规则:根据ID删除一条")
    @EciLog(title = "车辆规则:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody EtmsBdTruckSpecEntity entity){
        int count = etmsBdTruckSpecService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("车辆规则:根据ID字符串删除多条")
    @EciLog(title = "车辆规则:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody EtmsBdTruckSpecEntity entity) {
        int count = etmsBdTruckSpecService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}