package com.eci.project.etmsBdTruckEjwhQz.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.db.DBHelper;
import com.eci.common.enums.Enums;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.etmsBdTruckEjwhQz.dao.EtmsBdTruckEjwhQzDao;
import com.eci.project.etmsBdTruckEjwhQz.entity.EtmsBdTruckEjwhQzDTOEntity;
import com.eci.project.etmsBdTruckEjwhQz.entity.EtmsBdTruckEjwhQzEntity;
import com.eci.project.etmsBdTruckEjwhQz.validate.EtmsBdTruckEjwhQzVal;

import com.eci.project.etmsBdTruckInsuranceQz.entity.EtmsBdTruckInsuranceQzDTOEntity;
import com.eci.project.etmsBdTruckInsuranceQz.entity.EtmsBdTruckInsuranceQzEntity;
import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.Arrays;
import java.util.List;


/**
* 车辆二级维护历史Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-16
*/
@Service
@Slf4j
public class EtmsBdTruckEjwhQzService implements EciBaseService<EtmsBdTruckEjwhQzEntity> {

    @Autowired
    private EtmsBdTruckEjwhQzDao etmsBdTruckEjwhQzDao;

    @Autowired
    private EtmsBdTruckEjwhQzVal etmsBdTruckEjwhQzVal;


    @Override
    public TgPageInfo queryPageList(EtmsBdTruckEjwhQzEntity entity) {
        startPage();
        String sql="select BD.CHECK_STATUS,B.GUID,B.WH_DATE,B.NEXT_DATE,B.MOD_MARK from ETMS_BD_TRUCK_QZ BD\n" +
                "            right join ETMS_BD_TRUCK_EJWH_QZ B on B.TRUCK_GUID = BD.guid where B.TRUCK_GUID=?";
        List<EtmsBdTruckEjwhQzDTOEntity> pageInfo = DBHelper.selectList(sql, EtmsBdTruckEjwhQzDTOEntity.class, entity.getTruckGuid());
        return EciQuery.getPageInfo(pageInfo);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public EtmsBdTruckEjwhQzEntity save(EtmsBdTruckEjwhQzEntity entity) {
        // 返回实体对象
        EtmsBdTruckEjwhQzEntity etmsBdTruckEjwhQzEntity = null;
        etmsBdTruckEjwhQzVal.saveValidate(entity,BllContext.getBusinessType());
        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setGuid(IdWorker.get32UUID());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
            entity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            entity.setCompanyName(UserContext.getUserInfo().getCompanyName());
            entity.setNodeCode(UserContext.getUserInfo().getDeptCode());
            entity.setNodeName(UserContext.getUserInfo().getDeptName());
            entity.setModMark(Enums.ModMark.XZ.getValue());
            etmsBdTruckEjwhQzEntity = etmsBdTruckEjwhQzDao.insertOne(entity);
        }else{
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            entity.setModMark(Enums.ModMark.XG.getValue());
            etmsBdTruckEjwhQzEntity = etmsBdTruckEjwhQzDao.updateByEntityId(entity);

        }
        return etmsBdTruckEjwhQzEntity;
    }

    @Override
    public List<EtmsBdTruckEjwhQzEntity> selectList(EtmsBdTruckEjwhQzEntity entity) {
        return etmsBdTruckEjwhQzDao.selectList(entity);
    }

    @Override
    public EtmsBdTruckEjwhQzEntity selectOneById(Serializable id) {
        return etmsBdTruckEjwhQzDao.selectById(id);
    }


    @Override
    public void insertBatch(List<EtmsBdTruckEjwhQzEntity> list) {
        etmsBdTruckEjwhQzDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return etmsBdTruckEjwhQzDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return etmsBdTruckEjwhQzDao.deleteById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public int deleteByList(String ids) {
        List<String> list= Arrays.asList(ids.split(","));
        int count=0;
        for(String str:list){
            QueryWrapper<EtmsBdTruckEjwhQzEntity> queryWrapper=new QueryWrapper<>();
            queryWrapper.eq("GUID",str);
            count+=etmsBdTruckEjwhQzDao.delete(queryWrapper);
        }
        return count;
    }
}