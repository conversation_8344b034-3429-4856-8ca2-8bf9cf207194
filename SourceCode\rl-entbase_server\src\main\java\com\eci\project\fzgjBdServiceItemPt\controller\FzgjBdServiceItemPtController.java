package com.eci.project.fzgjBdServiceItemPt.controller;

import com.alibaba.fastjson.JSON;
import com.eci.common.ResponseMsgUtilX;
import com.eci.common.util.*;
import com.eci.common.web.BllContext;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjBdOmsPages.entity.FzgjBdOmsPagesEntity;
import com.eci.project.fzgjBdOmsPages.service.IFzgjBdOmsPagesService;
import com.eci.project.fzgjBdServiceItemPagesPt.entity.FzgjBdServiceItemPagesPtEntity;
import com.eci.project.fzgjBdServiceItemPagesPt.service.FzgjBdServiceItemPagesPtService;
import com.eci.project.fzgjBdServiceItemPt.entity.ServiceItemSaveDTO;
import com.eci.project.fzgjBdServiceItemPt.entity.TreeModel;
import com.eci.project.fzgjBdServiceItemPt.service.FzgjBdServiceItemPtService;
import com.eci.project.fzgjBdServiceItemPt.entity.FzgjBdServiceItemPtEntity;
import com.eci.project.fzgjBdServiceType.entity.FzgjBdServiceTypeEntity;
import com.eci.project.fzgjBdServiceType.service.FzgjBdServiceTypeService;
import com.eci.wu.core.DataTable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.stream.Collectors;

/**
* 平台级服务项目Controller
*
* @<NAME_EMAIL>
* @date 2025-03-25
*/
@Api(tags = "平台级服务项目")
@RestController
@RequestMapping("/fzgjBdServiceItemPt")
public class FzgjBdServiceItemPtController extends EciBaseController {

    @Autowired
    private FzgjBdServiceItemPtService fzgjBdServiceItemPtService;
    @Autowired
    private FzgjBdServiceTypeService fzgjBdServiceTypeService;
    @Autowired
    private FzgjBdServiceItemPagesPtService fzgjBdServiceItemPagesPtService;
    @Autowired
    private IFzgjBdOmsPagesService fzgjBdOmsPagesService;
    @ApiOperation("平台级服务项目:保存")
    @EciLog(title = "平台级服务项目:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody String json) throws Exception {
        //JSON反序列化时，将OWNED_SERVICENAME 给了OWNED_SERVICE
        //现修改为前端将OWNED_SERVICE给临时字段tempField 然后通过JsonField特性去转
        ServiceItemSaveDTO entity= JSON.parseObject(json,ServiceItemSaveDTO.class);
        if(fzgjBdServiceItemPtService.existCode(entity.getEntity().getCode(),entity.getEntity().getGuid())){
            throw new Exception("服务代码重复，请重新录入服务代码");
        }
        FzgjBdServiceItemPtEntity fzgjBdServiceItemPtEntity =fzgjBdServiceItemPtService.save(entity.getEntity());

      //  fzgjBdServiceItemPagesPtService.insertBatch(entity.getAddList());
        List<FzgjBdServiceItemPagesPtEntity> addList=entity.getAddList();
        if(addList.size()>0){
            addList.forEach(p->{
                p.setServiceItemCode(fzgjBdServiceItemPtEntity.getGuid());
                fzgjBdServiceItemPagesPtService.save(p);
                //fzgjBdServiceItemPagesPtService.insertOne(p);
            });
        }
        String ids= String.join(",",entity.getDelList().stream().map(p-> p.getGuid()).collect(Collectors.toList()));
        if(!ids.isEmpty())
            fzgjBdServiceItemPagesPtService.deleteByIds(ids);
        return ResponseMsgUtil.success(10001,fzgjBdServiceItemPtEntity);
    }


    @ApiOperation("平台级服务项目:查询列表")
    @EciLog(title = "平台级服务项目:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjBdServiceItemPtEntity entity){
        List<FzgjBdServiceItemPtEntity> fzgjBdServiceItemPtEntities = fzgjBdServiceItemPtService.selectList(entity);
        return ResponseMsgUtil.success(10001,fzgjBdServiceItemPtEntities);
    }

    @ApiOperation("平台级服务项目:查询列表")
    @EciLog(title = "平台级服务项目:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectTree")
    @EciAction()
    public ResponseMsg selectTree(@RequestBody FzgjBdServiceItemPtEntity entity){
        List<TreeModel> fzgjBdServiceItemPtEntities=null;
        if(entity.getParentid()==null||entity.getParentid().isEmpty()){
            FzgjBdServiceTypeEntity typeEntity=new FzgjBdServiceTypeEntity();
            typeEntity.setParentid("0");
            typeEntity.setStatus("Y");
            fzgjBdServiceItemPtEntities=fzgjBdServiceTypeService.selectTree(typeEntity);
            return ResponseMsgUtil.success(10001,fzgjBdServiceItemPtEntities);
        }else {
            fzgjBdServiceItemPtEntities = fzgjBdServiceItemPtService.selectTree(entity);
            return ResponseMsgUtil.success(10001,fzgjBdServiceItemPtEntities);
        }

    }


    @ApiOperation("平台级服务项目:分页查询列表")
    @EciLog(title = "平台级服务项目:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjBdServiceItemPtEntity entity){
        TgPageInfo tgPageInfo = fzgjBdServiceItemPtService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("平台级服务项目:根据ID查一条")
    @EciLog(title = "平台级服务项目:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjBdServiceItemPtEntity entity){
        FzgjBdServiceItemPtEntity  fzgjBdServiceItemPtEntity = fzgjBdServiceItemPtService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,fzgjBdServiceItemPtEntity);
    }

    @ApiOperation("平台级服务项目:根据ID查一条")
    @EciLog(title = "平台级服务项目:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectwithparent")
    @EciAction()
    public ResponseMsg selectwithparent(@RequestBody FzgjBdServiceItemPtEntity entity){
        FzgjBdServiceItemPtEntity  fzgjBdServiceItemPtEntity = fzgjBdServiceItemPtService.selectwithparent(entity);
        return ResponseMsgUtilX.success(10001,fzgjBdServiceItemPtEntity);
    }

    @ApiOperation("平台级服务项目:获取订单编辑区")
    @EciLog(title = "平台级服务项目:获取订单编辑区", businessType = BusinessType.SELECT)
    @PostMapping("/getCheckEditItem")
    @EciAction()
    public ResponseMsg getCheckEditItem(@RequestBody FzgjBdServiceItemPtEntity entity){
        DataTable dt= fzgjBdServiceItemPtService.getCheckEditItem(entity.getGuid());
        return ResponseMsgUtil.success(10001,dt);
    }


    @ApiOperation("平台级服务项目:根据ID删除一条")
    @EciLog(title = "平台级服务项目:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjBdServiceItemPtEntity entity){
        int count = fzgjBdServiceItemPtService.deleteById(entity.getGuid());
        fzgjBdServiceItemPagesPtService.deleteByCode(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("平台级服务项目:根据ID字符串删除多条")
    @EciLog(title = "平台级服务项目:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjBdServiceItemPtEntity entity) {
        int count = fzgjBdServiceItemPtService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}