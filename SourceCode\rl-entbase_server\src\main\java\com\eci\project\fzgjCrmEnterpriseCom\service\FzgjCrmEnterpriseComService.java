package com.eci.project.fzgjCrmEnterpriseCom.service;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjCrmEnterpriseCom.dao.FzgjCrmEnterpriseComDao;
import com.eci.project.fzgjCrmEnterpriseCom.entity.FzgjCrmEnterpriseComEntity;
import com.eci.project.fzgjCrmEnterpriseCom.validate.FzgjCrmEnterpriseComVal;

import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 平台受理企业Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-03-13
*/
@Service
@Slf4j
public class FzgjCrmEnterpriseComService implements EciBaseService<FzgjCrmEnterpriseComEntity> {

    @Autowired
    private FzgjCrmEnterpriseComDao fzgjCrmEnterpriseComDao;

    @Autowired
    private FzgjCrmEnterpriseComVal fzgjCrmEnterpriseComVal;


    @Override
    public TgPageInfo queryPageList(FzgjCrmEnterpriseComEntity entity) {
        EciQuery<FzgjCrmEnterpriseComEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjCrmEnterpriseComEntity> entities = fzgjCrmEnterpriseComDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjCrmEnterpriseComEntity save(FzgjCrmEnterpriseComEntity entity) {
        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setGuid(IdWorker.getIdStr());
            entity.setCreateDate(new java.util.Date());
            entity.setUpdateDate(new java.util.Date());
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        } else {
            entity.setUpdateDate(new java.util.Date());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        }
        // 返回实体对象
        FzgjCrmEnterpriseComEntity fzgjCrmEnterpriseComEntity = null;
        fzgjCrmEnterpriseComVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjCrmEnterpriseComEntity = fzgjCrmEnterpriseComDao.insertOne(entity);

        }else{

            fzgjCrmEnterpriseComEntity = fzgjCrmEnterpriseComDao.updateByEntityId(entity);

        }
        return fzgjCrmEnterpriseComEntity;
    }

    @Override
    public List<FzgjCrmEnterpriseComEntity> selectList(FzgjCrmEnterpriseComEntity entity) {
        return fzgjCrmEnterpriseComDao.selectList(entity);
    }

    @Override
    public FzgjCrmEnterpriseComEntity selectOneById(Serializable id) {
        return fzgjCrmEnterpriseComDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjCrmEnterpriseComEntity> list) {
        fzgjCrmEnterpriseComDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjCrmEnterpriseComDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjCrmEnterpriseComDao.deleteById(id);
    }

}