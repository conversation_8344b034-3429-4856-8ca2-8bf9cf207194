package com.eci.project.omsOrderFwxmWorkFkZzfw.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.omsOrderFwxmWorkFkZzfw.service.OmsOrderFwxmWorkFkZzfwService;
import com.eci.project.omsOrderFwxmWorkFkZzfw.entity.OmsOrderFwxmWorkFkZzfwEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 反馈内容-作业信息:其他增值服务信息Controller
*
* @<NAME_EMAIL>
* @date 2025-05-22
*/
@Api(tags = "反馈内容-作业信息:其他增值服务信息")
@RestController
@RequestMapping("/omsOrderFwxmWorkFkZzfw")
public class OmsOrderFwxmWorkFkZzfwController extends EciBaseController {

    @Autowired
    private OmsOrderFwxmWorkFkZzfwService omsOrderFwxmWorkFkZzfwService;


    @ApiOperation("反馈内容-作业信息:其他增值服务信息:保存")
    @EciLog(title = "反馈内容-作业信息:其他增值服务信息:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody OmsOrderFwxmWorkFkZzfwEntity entity){
        OmsOrderFwxmWorkFkZzfwEntity omsOrderFwxmWorkFkZzfwEntity =omsOrderFwxmWorkFkZzfwService.save(entity);
        return ResponseMsgUtil.success(10001,omsOrderFwxmWorkFkZzfwEntity);
    }


    @ApiOperation("反馈内容-作业信息:其他增值服务信息:查询列表")
    @EciLog(title = "反馈内容-作业信息:其他增值服务信息:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody OmsOrderFwxmWorkFkZzfwEntity entity){
        List<OmsOrderFwxmWorkFkZzfwEntity> omsOrderFwxmWorkFkZzfwEntities = omsOrderFwxmWorkFkZzfwService.selectList(entity);
        return ResponseMsgUtil.success(10001,omsOrderFwxmWorkFkZzfwEntities);
    }


    @ApiOperation("反馈内容-作业信息:其他增值服务信息:分页查询列表")
    @EciLog(title = "反馈内容-作业信息:其他增值服务信息:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody OmsOrderFwxmWorkFkZzfwEntity entity){
        TgPageInfo tgPageInfo = omsOrderFwxmWorkFkZzfwService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("反馈内容-作业信息:其他增值服务信息:根据ID查一条")
    @EciLog(title = "反馈内容-作业信息:其他增值服务信息:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody OmsOrderFwxmWorkFkZzfwEntity entity){
        OmsOrderFwxmWorkFkZzfwEntity  omsOrderFwxmWorkFkZzfwEntity = omsOrderFwxmWorkFkZzfwService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,omsOrderFwxmWorkFkZzfwEntity);
    }


    @ApiOperation("反馈内容-作业信息:其他增值服务信息:根据ID删除一条")
    @EciLog(title = "反馈内容-作业信息:其他增值服务信息:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody OmsOrderFwxmWorkFkZzfwEntity entity){
        int count = omsOrderFwxmWorkFkZzfwService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("反馈内容-作业信息:其他增值服务信息:根据ID字符串删除多条")
    @EciLog(title = "反馈内容-作业信息:其他增值服务信息:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody OmsOrderFwxmWorkFkZzfwEntity entity) {
        int count = omsOrderFwxmWorkFkZzfwService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}