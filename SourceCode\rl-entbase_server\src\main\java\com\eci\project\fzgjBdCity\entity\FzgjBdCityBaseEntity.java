package com.eci.project.fzgjBdCity.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.eci.common.DictField;
import com.eci.common.ZsrBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;

import java.util.Date;


/**
 * 市对象 FZGJ_BD_CITY
 *
 * @<NAME_EMAIL>
 * @date 2025-03-17
 */
@FieldNameConstants
public class FzgjBdCityBaseEntity extends ZsrBaseEntity {
    /**
     * 城市代码
     */
    @ApiModelProperty("城市代码(50)")
    @TableId("GUID")
    private String guid;

    /**
     * 城市名称
     */
    @ApiModelProperty("城市名称(50)")
    @TableField("NAME")
    private String name;

    /**
     * 所属省代码
     */
    @ApiModelProperty("所属省代码(50)")
    @TableField("PROVINCE_ID")
    @DictField(queryKey = "BASE_PROVINCE")
    private String provinceId;

    /**
     * 邮编
     */
    @ApiModelProperty("邮编(20)")
    @TableField("ZIP_CODE")
    private String zipCode;

    /**
     * 备注
     */
    @ApiModelProperty("备注(50)")
    @TableField("MEMO")
    private String memo;

    /**
     * 状态
     */
    @ApiModelProperty("状态(1)")
    @TableField("STATUS")
    private String status;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
     * 创建日期
     */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist = false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist = false)
    private Date createDateEnd;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人(50)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
     * 修改日期
     */
    @ApiModelProperty("修改日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("修改日期开始")
    @TableField(exist = false)
    private Date updateDateStart;

    @ApiModelProperty("修改日期结束")
    @TableField(exist = false)
    private Date updateDateEnd;

    /**
     * 排序
     */
    @ApiModelProperty("排序(22)")
    @TableField("SEQ")
    private Integer seq;

    /**
     * CREATE_USER
     */
    @ApiModelProperty("CREATE_USER(200)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
     * UPDATE_USER
     */
    @ApiModelProperty("UPDATE_USER(200)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
     * 英文名称，仅做存储
     */
    @ApiModelProperty("英文名称，仅做存储(200)")
    @TableField("EN_NAME")
    private String enName;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public FzgjBdCityBaseEntity setGuid(String guid) {
        this.guid = guid;
        return this;
    }

    public String getGuid() {
        return guid;
    }

    public FzgjBdCityBaseEntity setName(String name) {
        this.name = name;
        return this;
    }

    public String getName() {
        return name;
    }

    public FzgjBdCityBaseEntity setProvinceId(String provinceId) {
        this.provinceId = provinceId;
        return this;
    }

    public String getProvinceId() {
        return provinceId;
    }

    public FzgjBdCityBaseEntity setZipCode(String zipCode) {
        this.zipCode = zipCode;
        return this;
    }

    public String getZipCode() {
        return zipCode;
    }

    public FzgjBdCityBaseEntity setMemo(String memo) {
        this.memo = memo;
        return this;
    }

    public String getMemo() {
        return memo;
    }

    public FzgjBdCityBaseEntity setStatus(String status) {
        this.status = status;
        return this;
    }

    public String getStatus() {
        return status;
    }

    public FzgjBdCityBaseEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        return this;
    }

    public String getCreateUser() {
        return createUser;
    }

    public FzgjBdCityBaseEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        return this;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public FzgjBdCityBaseEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        return this;
    }

    public Date getCreateDateStart() {
        return createDateStart;
    }

    public FzgjBdCityBaseEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        return this;
    }

    public Date getCreateDateEnd() {
        return createDateEnd;
    }

    public FzgjBdCityBaseEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        return this;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public FzgjBdCityBaseEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        return this;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public FzgjBdCityBaseEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        return this;
    }

    public Date getUpdateDateStart() {
        return updateDateStart;
    }

    public FzgjBdCityBaseEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        return this;
    }

    public Date getUpdateDateEnd() {
        return updateDateEnd;
    }

    public FzgjBdCityBaseEntity setSeq(Integer seq) {
        this.seq = seq;
        return this;
    }

    public Integer getSeq() {
        return seq;
    }

    public FzgjBdCityBaseEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        return this;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public FzgjBdCityBaseEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        return this;
    }

    public String getUpdateUserName() {
        return updateUserName;
    }

    public FzgjBdCityBaseEntity setEnName(String enName) {
        this.enName = enName;
        return this;
    }

    public String getEnName() {
        return enName;
    }

}
