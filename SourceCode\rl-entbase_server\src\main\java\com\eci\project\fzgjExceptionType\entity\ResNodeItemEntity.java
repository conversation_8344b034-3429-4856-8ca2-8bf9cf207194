package com.eci.project.fzgjExceptionType.entity;

import com.eci.project.fzgjBdBill.common.TreeNodeDTO;

import java.util.List;

/**
 * @ClassName: ResNodeItemEntity - 作业系统返回实体
 * @Author: guangyan.mei
 * @Date: 2025/4/3 9:23
 * @Description: TODO
 */
public class ResNodeItemEntity {

    public String id;

    public String label;

    public String state;

    private List<ResNodeItemEntity> children;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public List<ResNodeItemEntity> getChildren() {
        return children;
    }

    public void setChildren(List<ResNodeItemEntity> children) {
        this.children = children;
    }


}
