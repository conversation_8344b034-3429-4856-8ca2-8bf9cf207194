package com.eci.project.etmsBdTruckGcQz.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.db.DBHelper;
import com.eci.common.enums.Enums;
import com.eci.common.util.DateUtils;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.etmsBdTruckEjwhQz.entity.EtmsBdTruckEjwhQzDTOEntity;
import com.eci.project.etmsBdTruckEjwhQz.entity.EtmsBdTruckEjwhQzEntity;
import com.eci.project.etmsBdTruckGcQz.dao.EtmsBdTruckGcQzDao;
import com.eci.project.etmsBdTruckGcQz.entity.EtmsBdTruckGcQzDTOEntity;
import com.eci.project.etmsBdTruckGcQz.entity.EtmsBdTruckGcQzEntity;
import com.eci.project.etmsBdTruckGcQz.validate.EtmsBdTruckGcQzVal;

import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.Arrays;
import java.util.List;


/**
* 挂车号Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-16
*/
@Service
@Slf4j
public class EtmsBdTruckGcQzService implements EciBaseService<EtmsBdTruckGcQzEntity> {

    @Autowired
    private EtmsBdTruckGcQzDao etmsBdTruckGcQzDao;

    @Autowired
    private EtmsBdTruckGcQzVal etmsBdTruckGcQzVal;


    @Override
    public TgPageInfo queryPageList(EtmsBdTruckGcQzEntity entity) {
        startPage();
        String sql="select BD.CHECK_STATUS,B.GUID,B.GC_NO,B.CREATE_USER_NAME,B.CREATE_DATE,B.MOD_MARK from ETMS_BD_TRUCK_QZ BD\n" +
                "            RIGHT join ETMS_BD_TRUCK_GC_QZ B on B.TRUCK_GUID = BD.guid where B.TRUCK_GUID=?";
        List<EtmsBdTruckGcQzDTOEntity> pageInfo = DBHelper.selectList(sql, EtmsBdTruckGcQzDTOEntity.class, entity.getTruckGuid());
        return EciQuery.getPageInfo(pageInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public EtmsBdTruckGcQzEntity save(EtmsBdTruckGcQzEntity entity) {
        // 返回实体对象
        EtmsBdTruckGcQzEntity etmsBdTruckGcQzEntity = null;
        etmsBdTruckGcQzVal.saveValidate(entity,BllContext.getBusinessType());
        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setGuid(IdWorker.get32UUID());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
            entity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            entity.setCompanyName(UserContext.getUserInfo().getCompanyName());
            entity.setNodeCode(UserContext.getUserInfo().getDeptCode());
            entity.setNodeName(UserContext.getUserInfo().getDeptName());
            entity.setModMark(Enums.ModMark.XZ.getValue());
            entity.setCreateDate(DateUtils.getNowDate());
            etmsBdTruckGcQzEntity = etmsBdTruckGcQzDao.insertOne(entity);

        }else{
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            entity.setModMark(Enums.ModMark.XG.getValue());
            etmsBdTruckGcQzEntity = etmsBdTruckGcQzDao.updateByEntityId(entity);

        }
        return etmsBdTruckGcQzEntity;
    }

    @Override
    public List<EtmsBdTruckGcQzEntity> selectList(EtmsBdTruckGcQzEntity entity) {
        return etmsBdTruckGcQzDao.selectList(entity);
    }

    @Override
    public EtmsBdTruckGcQzEntity selectOneById(Serializable id) {
        return etmsBdTruckGcQzDao.selectById(id);
    }


    @Override
    public void insertBatch(List<EtmsBdTruckGcQzEntity> list) {
        etmsBdTruckGcQzDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return etmsBdTruckGcQzDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return etmsBdTruckGcQzDao.deleteById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public int deleteByList(String ids) {
        List<String> list= Arrays.asList(ids.split(","));
        int count=0;
        for(String str:list){
            QueryWrapper<EtmsBdTruckGcQzEntity> queryWrapper=new QueryWrapper<>();
            queryWrapper.eq("GUID",str);
            count+=etmsBdTruckGcQzDao.delete(queryWrapper);
        }
        return count;
    }
}