package com.eci.project.crmCustomerQual.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.crmCustomerQual.service.CrmCustomerQualService;
import com.eci.project.crmCustomerQual.entity.CrmCustomerQualEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 资质管理Controller
*
* @<NAME_EMAIL>
* @date 2025-05-26
*/
@Api(tags = "资质管理")
@RestController
@RequestMapping("/crmCustomerQual")
public class CrmCustomerQualController extends EciBaseController {

    @Autowired
    private CrmCustomerQualService crmCustomerQualService;


    @ApiOperation("资质管理:保存")
    @EciLog(title = "资质管理:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody CrmCustomerQualEntity entity){
        CrmCustomerQualEntity crmCustomerQualEntity =crmCustomerQualService.save(entity);
        return ResponseMsgUtil.success(10001,crmCustomerQualEntity);
    }


    @ApiOperation("资质管理:查询列表")
    @EciLog(title = "资质管理:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody CrmCustomerQualEntity entity){
        List<CrmCustomerQualEntity> crmCustomerQualEntities = crmCustomerQualService.selectList(entity);
        return ResponseMsgUtil.success(10001,crmCustomerQualEntities);
    }


    @ApiOperation("资质管理:分页查询列表")
    @EciLog(title = "资质管理:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody CrmCustomerQualEntity entity){
        TgPageInfo tgPageInfo = crmCustomerQualService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("资质管理:根据ID查一条")
    @EciLog(title = "资质管理:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody CrmCustomerQualEntity entity){
        CrmCustomerQualEntity  crmCustomerQualEntity = crmCustomerQualService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,crmCustomerQualEntity);
    }


    @ApiOperation("资质管理:根据ID删除一条")
    @EciLog(title = "资质管理:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody CrmCustomerQualEntity entity){
        int count = crmCustomerQualService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("资质管理:根据ID字符串删除多条")
    @EciLog(title = "资质管理:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody CrmCustomerQualEntity entity) {
        int count = crmCustomerQualService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}