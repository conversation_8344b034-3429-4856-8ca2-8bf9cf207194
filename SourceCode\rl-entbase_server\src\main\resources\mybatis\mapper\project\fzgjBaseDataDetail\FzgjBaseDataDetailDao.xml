<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.fzgjBaseDataDetail.dao.FzgjBaseDataDetailDao">
    <resultMap type="FzgjBaseDataDetailEntity" id="FzgjBaseDataDetailResult">
        <result property="guid" column="GUID"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="code" column="CODE"/>
        <result property="name" column="NAME"/>
        <result property="status" column="STATUS"/>
        <result property="memo" column="MEMO"/>
        <result property="seq" column="SEQ"/>
        <result property="belongGroup" column="BELONG_GROUP"/>
        <result property="paramCode" column="PARAM_CODE"/>
    </resultMap>

    <sql id="selectFzgjBaseDataDetailEntityVo">
        select GUID,
               GROUP_CODE,
               CODE,
               NAME,
               STATUS,
               MEMO,
               SEQ,
               BELONG_GROUP,
               PARAM_CODE
        from FZGJ_BASE_DATA_DETAIL
    </sql>


    <select id="selectListInfo" parameterType="FzgjBaseDataDetailEntity" resultMap="FzgjBaseDataDetailResult">
        SELECT A.GUID,
        A.GROUP_CODE,
        A.CODE,
        A.NAME,
        A.STATUS,
        A.MEMO,
        A.SEQ,
        A.BELONG_GROUP
        FROM FZGJ_BASE_DATA_DETAIL A
        LEFT JOIN FZGJ_BASE_DATA B ON A.GROUP_CODE = B.GROUP_CODE
        WHERE B.STATUS = 'Y' AND A.GROUP_CODE = #{groupCode}
        <if test="code != null and code != ''">
            AND A.CODE like '%' || #{code} || '%'
        </if>
        <if test="name != null and name != ''">
            AND A.NAME like '%' || #{name} || '%'
        </if>
        <if test="status != null">
            AND A.STATUS = #{status}
        </if>
    </select>
</mapper>