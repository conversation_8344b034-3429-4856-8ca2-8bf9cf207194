package com.eci.project.omsOrderFwxmWorkFkHfd.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.omsOrderFwxmWorkFkHfd.entity.OmsOrderFwxmWorkFkHfdEntity;

import org.springframework.stereotype.Service;


/**
* 反馈内容-核放单Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-06-03
*/
@Service
public class OmsOrderFwxmWorkFkHfdVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(OmsOrderFwxmWorkFkHfdEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(OmsOrderFwxmWorkFkHfdEntity entity, BusinessType businessType) {

    }

}
