package com.eci.project.omsOrderFwxmTmsXlXlLyXl.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.omsOrderFwxmTmsXlXlLyXl.service.OmsOrderFwxmTmsXlXlLyXlService;
import com.eci.project.omsOrderFwxmTmsXlXlLyXl.entity.OmsOrderFwxmTmsXlXlLyXlEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 委托内容-程运序列-陆运-线路Controller
*
* @<NAME_EMAIL>
* @date 2025-06-04
*/
@Api(tags = "委托内容-程运序列-陆运-线路")
@RestController
@RequestMapping("/omsOrderFwxmTmsXlXlLyXl")
public class OmsOrderFwxmTmsXlXlLyXlController extends EciBaseController {

    @Autowired
    private OmsOrderFwxmTmsXlXlLyXlService omsOrderFwxmTmsXlXlLyXlService;


    @ApiOperation("委托内容-程运序列-陆运-线路:保存")
    @EciLog(title = "委托内容-程运序列-陆运-线路:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody OmsOrderFwxmTmsXlXlLyXlEntity entity){
        OmsOrderFwxmTmsXlXlLyXlEntity omsOrderFwxmTmsXlXlLyXlEntity =omsOrderFwxmTmsXlXlLyXlService.save(entity);
        return ResponseMsgUtil.success(10001,omsOrderFwxmTmsXlXlLyXlEntity);
    }


    @ApiOperation("委托内容-程运序列-陆运-线路:查询列表")
    @EciLog(title = "委托内容-程运序列-陆运-线路:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody OmsOrderFwxmTmsXlXlLyXlEntity entity){
        List<OmsOrderFwxmTmsXlXlLyXlEntity> omsOrderFwxmTmsXlXlLyXlEntities = omsOrderFwxmTmsXlXlLyXlService.selectList(entity);
        return ResponseMsgUtil.success(10001,omsOrderFwxmTmsXlXlLyXlEntities);
    }


    @ApiOperation("委托内容-程运序列-陆运-线路:分页查询列表")
    @EciLog(title = "委托内容-程运序列-陆运-线路:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody OmsOrderFwxmTmsXlXlLyXlEntity entity){
        TgPageInfo tgPageInfo = omsOrderFwxmTmsXlXlLyXlService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("委托内容-程运序列-陆运-线路:根据ID查一条")
    @EciLog(title = "委托内容-程运序列-陆运-线路:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody OmsOrderFwxmTmsXlXlLyXlEntity entity){
        OmsOrderFwxmTmsXlXlLyXlEntity  omsOrderFwxmTmsXlXlLyXlEntity = omsOrderFwxmTmsXlXlLyXlService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,omsOrderFwxmTmsXlXlLyXlEntity);
    }


    @ApiOperation("委托内容-程运序列-陆运-线路:根据ID删除一条")
    @EciLog(title = "委托内容-程运序列-陆运-线路:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody OmsOrderFwxmTmsXlXlLyXlEntity entity){
        int count = omsOrderFwxmTmsXlXlLyXlService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("委托内容-程运序列-陆运-线路:根据ID字符串删除多条")
    @EciLog(title = "委托内容-程运序列-陆运-线路:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody OmsOrderFwxmTmsXlXlLyXlEntity entity) {
        int count = omsOrderFwxmTmsXlXlLyXlService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}