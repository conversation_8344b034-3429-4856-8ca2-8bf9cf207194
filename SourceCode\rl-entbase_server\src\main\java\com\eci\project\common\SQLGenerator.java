package com.eci.project.common;

import com.eci.common.Zsr;
import com.eci.sso.role.entity.UserContext;

import java.util.List;
import java.util.stream.Collectors;

public class SQLGenerator {

    /**
     * 根据类型获取名称 OMS_NAMECOM 类似于：FNameManage 写法
     *
     * @param pType 类型，如：manage.Add("A.CONSIGNEE_CODE", FNameComList.CRM_CUSTOMER_SFHF, DbFunctionList.OMS_NAMECOM, true); 这个 CRM_CUSTOMER_SFHF 就是类型
     * @param pCode 主 SQL 返回 pageInfo 的时候的 code
     * @return 对应的 SQL
     */
    public static String OMS_NAMECOM(String pType, List<String> pCode) {
        String sql = "";
        String pOrg = UserContext.getUserInfo().getCompanyCode();
        String codePlaceholders = "";

        if (pCode != null && !pCode.isEmpty()) {
            codePlaceholders = pCode.stream()
                    .filter(s -> s != null && !s.isEmpty())
                    .map(code -> "'" + code + "'")
                    .collect(Collectors.joining(","));
        }

        switch (pType) {
            case "CRM_CUSTOMER":
            case "CRM_CUSTOMER_SFHF":
            case "CRM_CUSTOMER_GYS":
            case "CRM_CUSTOMER_HZ":
            case "CRM_CUSTOMER_KH":
            case "CRM_CUSTOMER_GWDL":
            case "CRM_CUSTOMER_HGBM":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.CODE AS CODE, (A.NAME) AS NAME FROM CRM_CUSTOMER A WHERE A.STATUS='Y' AND A.CODE IN (%s) AND A.GROUP_CODE = '%s'";
                    return String.format(sql, codePlaceholders, pOrg);
                }
                break;
            case "CRM_CUSTOMER_KHSYB":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.CODE AS CODE, (A.NAME) AS NAME FROM CRM_CUSTOMER_KHSYB A WHERE A.STATUS = 'Y' AND A.GROUP_CODE = '%s' AND A.CODE IN (%s)";
                    return String.format(sql, pOrg, codePlaceholders);
                }
                break;

            case "BMC_MMS_FKFA":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.FKFA_NO AS CODE, A.FKFA_NAME AS NAME FROM BMC_MMS_FKFA A WHERE A.FKFA_NO IN (%s) AND A.GROUP_CODE = '%s'";
                    return String.format(sql, codePlaceholders, pOrg);
                }
                break;

            case "OMS_AIR_PORT":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT CODE AS CODE, NAME AS NAME FROM FZGJ_BD_AIR_PORT WHERE STATUS='Y' AND CODE IN (%s)";
                    return String.format(sql, codePlaceholders);
                }
                break;

            case "OMS_SEA_PORT":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT CODE AS CODE, NAME AS NAME FROM FZGJ_BD_SEA_PORT WHERE STATUS='Y' AND CODE IN (%s)";
                    return String.format(sql, codePlaceholders);
                }
                break;

            case "OMS_ATTACH_TYPE":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.CODE AS CODE, A.NAME AS NAME FROM FZGJ_FILE_TYPE A WHERE A.STATUS='Y' AND A.GROUP_CODE = '%s' AND A.CODE IN (%s)";
                    return String.format(sql, pOrg, codePlaceholders);
                }
                break;

            case "OMS_BD_AREA":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.CODE AS CODE, A.NAME AS NAME FROM FZGJ_BD_AREA A WHERE A.STATUS='Y' AND A.GROUP_CODE='%s' AND A.CODE IN (%s)";
                    return String.format(sql, pOrg, codePlaceholders);
                }
                break;

            case "OMS_BD_OP_TYPE":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.CODE AS CODE, (A.NAME) AS NAME FROM FZGJ_BD_OP_TYPE A WHERE A.STATUS='Y' AND A.GROUP_CODE='%s' " +
                            "AND A.CODE IN (%s) AND A.SYS_CODE='OMS_ORDER'";
                    return String.format(sql, pOrg, codePlaceholders);
                }
                break;

            case "OMS_BD_PRODUCT":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.CODE AS CODE, (A.NAME) AS NAME FROM FZGJ_BD_PRODUCT A WHERE A.STATUS='Y' AND A.GROUP_CODE='%s' AND A.CODE IN (%s)";
                    return String.format(sql, pOrg, codePlaceholders);
                }
                break;

            case "OMS_BD_PRODUCT_GUID":
                // This case uses GUID, not CODE
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.GUID AS CODE, A.NAME AS NAME FROM FZGJ_BD_PRODUCT A WHERE A.GROUP_CODE='%s' AND A.GUID IN (%s)";
                    return String.format(sql, pOrg, codePlaceholders);
                }
                break;

            case "OMS_BD_TRUCK_TYPE":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT CODE AS CODE, NAME AS NAME FROM FZGJ_BD_TRUCK_TYPE WHERE GROUP_CODE = '%s' AND STATUS='Y' AND CODE IN (%s)";
                    return String.format(sql, pOrg, codePlaceholders);
                }
                break;

            case "OMS_BOX_TYPE":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.CODE AS CODE, A.NAME AS NAME FROM FZGJ_BOX_TYPE A WHERE A.STATUS='Y' AND A.GROUP_CODE = '%s' AND A.CODE IN (%s)";
                    return String.format(sql, pOrg, codePlaceholders);
                }
                break;

            case "OMS_BOX_SIZE":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.BOX_SIZE AS CODE, (A.BOX_SIZE) AS NAME FROM FZGJ_BOX_SIZE A WHERE A.BOX_SIZE IN (%s) AND A.GROUP_CODE = '%s'";
                    // Note: BOX_SIZE is compared directly, ensure pCode contains the box sizes
                    return String.format(sql, codePlaceholders, pOrg);
                }
                break;

            case "OMS_BOX_YARD":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT T.YARD_CODE AS CODE, T.YARD_NAME AS NAME FROM FZGJ_BOX_YARD T WHERE T.IS_USE='Y' AND GROUP_CODE='%s' AND T.YARD_CODE IN (%s)";
                    return String.format(sql, pOrg, codePlaceholders);
                }
                break;

            case "OMS_CRM_CONTRACT_FILE_TYPE":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT CODE AS CODE, NAME AS NAME FROM FZGJ_CRM_CONTRACT_FILE_TYPE WHERE GROUP_CODE = '%s' AND STATUS='Y' AND CODE IN (%s)";
                    return String.format(sql, pOrg, codePlaceholders);
                }
                break;

            case "OMS_CRM_CONTRACT_TYPE_WARN":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.CODE AS CODE, A.NAME AS NAME FROM FZGJ_CRM_CONTRACT_TYPE_WARN A WHERE A.STATUS='Y' AND A.GROUP_CODE = '%s' AND A.CODE IN (%s)";
                    return String.format(sql, pOrg, codePlaceholders);
                }
                break;

            case "OMS_CRM_FILE_TYPE":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT CODE AS CODE, NAME AS NAME FROM FZGJ_CRM_FILE_TYPE WHERE GROUP_CODE = '%s' AND STATUS='Y' AND CODE IN (%s)";
                    return String.format(sql, pOrg, codePlaceholders);
                }
                break;

            case "OMS_EXCEPTION_TYPE":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT CODE AS CODE, NAME AS NAME FROM FZGJ_EXCEPTION_TYPE WHERE GROUP_CODE='%s' AND STATUS='Y' AND CODE IN (%s)";
                    return String.format(sql, pOrg, codePlaceholders);
                }
                break;

            case "OMS_GOODS_ATTR":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.CODE AS CODE, A.NAME AS NAME FROM FZGJ_GOODS_ATTR A WHERE A.STATUS='Y' AND A.GROUP_CODE='%s' AND A.CODE IN (%s)";
                    return String.format(sql, pOrg, codePlaceholders);
                }
                break;

            case "OMS_GOODS_PACK_TYPE":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.CODE AS CODE, A.NAME AS NAME FROM FZGJ_GOODS_PACK_TYPE A WHERE A.STATUS = 'Y' AND A.GROUP_CODE='%s' AND A.CODE IN (%s)";
                    return String.format(sql, pOrg, codePlaceholders);
                }
                break;

            case "OMS_GOODS_UNIT":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.CODE AS CODE, A.NAME AS NAME FROM FZGJ_GOODS_UNIT A WHERE A.STATUS='Y' AND A.GROUP_CODE='%s' AND A.CODE IN (%s)";
                    return String.format(sql, pOrg, codePlaceholders);
                }
                break;

            case "OMS_LEASED_AREA_MODEL":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.CODE AS CODE, A.NAME AS NAME FROM FZGJ_LEASED_AREA_MODEL A WHERE A.STATUS='Y' AND A.GROUP_CODE = '%s' AND A.CODE IN (%s)";
                    return String.format(sql, pOrg, codePlaceholders);
                }
                break;

            case "OMS_LEASED_AREA_TYPE":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.CODE AS CODE, NAME AS NAME FROM FZGJ_LEASED_AREATYPE A WHERE A.CODE IN (%s) AND A.GROUP_CODE = '%s'";
                    return String.format(sql, codePlaceholders, pOrg);
                }
                break;

            case "OMS_LEASED_EQUIPMENT_MODEL":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.CODE AS CODE, A.NAME AS NAME FROM FZGJ_LEASED_EQUIPMENT_MODEL A WHERE A.GROUP_CODE = '%s' AND A.CODE IN (%s)";
                    return String.format(sql, pOrg, codePlaceholders);
                }
                break;

            case "OMS_LEASED_EQUIPMENT_TYPE":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.CODE AS CODE, NAME AS NAME FROM FZGJ_LEASED_EQUIPMENT_TYPE A WHERE A.CODE IN (%s) AND A.GROUP_CODE = '%s'";
                    return String.format(sql, codePlaceholders, pOrg);
                }
                break;

            case "OMS_LEASED_USER_TYPE":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.CODE AS CODE, A.NAME AS NAME FROM FZGJ_LEASED_USER_TYPE A WHERE A.STATUS='Y' AND A.GROUP_CODE = '%s' AND A.CODE IN (%s)";
                    return String.format(sql, pOrg, codePlaceholders);
                }
                break;

            case "OMS_SSO_USER":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.USERNAME AS CODE, (A.TRUENAME) AS NAME FROM FZGJ_SSO_USER A WHERE A.USERNAME IN (%s)";
                    return String.format(sql, codePlaceholders);
                }
                break;

            case "OMS_TASK_ATTR":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.CODE AS CODE, A.NAME AS NAME FROM FZGJ_TASK_ATTR A WHERE A.STATUS='Y' AND A.GROUP_CODE = '%s' AND A.CODE IN (%s)";
                    return String.format(sql, pOrg, codePlaceholders);
                }
                break;

            case "OMS_TASK_MATTER":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.CODE AS CODE, NAME AS NAME FROM FZGJ_TASK_MATTER A WHERE A.STATUS='Y' AND A.CODE IN (%s) AND A.GROUP_CODE = '%s'";
                    return String.format(sql, codePlaceholders, pOrg);
                }
                break;

            case "OMS_USER_TASK_CONFIG":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT FTC.TASK_CODE AS CODE, FTC.TASK_ITEM AS NAME FROM FZGJ_TASK_CONFIG FTC WHERE FTC.STATUS = 'Y' AND FTC.TASK_CODE IN (%s)";
                    return String.format(sql, codePlaceholders);
                }
                break;

            case "OMS_XZFA":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.XZFA_NO AS CODE, A.XZFA_NAME AS NAME FROM OMS_XZFA A WHERE A.STATUS = 'Y' AND A.GROUP_CODE = '%s' AND A.XZFA_NO IN (%s)";
                    return String.format(sql, pOrg, codePlaceholders);
                }
                break;

            case "OMS_BD_TLZD":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.CODE AS CODE, A.NAME AS NAME FROM V_FZGJ_BD_RAILWAY_STATION A WHERE A.CODE IN (%s) AND A.GROUP_CODE = '%s'";
                    return String.format(sql, codePlaceholders, pOrg);
                }
                break;

            case "OMS_BD_KDZD":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.CODE AS CODE, A.NAME AS NAME FROM V_FZGJ_BD_EXPRESS_SITE A WHERE A.CODE IN (%s) AND A.GROUP_CODE = '%s'";
                    return String.format(sql, codePlaceholders, pOrg);
                }
                break;

            case "OMS_WMS_WAREHOUSE":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.WH_CODE AS CODE, (A.WH_NAME) AS NAME FROM WMS_WAREHOUSE A WHERE A.WH_CODE IN (%s) AND A.GROUP_CODE = '%s'";
                    return String.format(sql, codePlaceholders, pOrg);
                }
                break;

            case "OMS_LEASED_ZOOM":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT CODE AS CODE, NAME AS NAME FROM FZGJ_LEASED_ZOOM A WHERE A.CODE IN (%s) AND A.GROUP_CODE = '%s'";
                    return String.format(sql, codePlaceholders, pOrg);
                }
                break;

            case "OMS_LEASED_FLOOR":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT CODE AS CODE, NAME AS NAME FROM FZGJ_LEASED_FLOOR A WHERE A.CODE IN (%s) AND A.GROUP_CODE = '%s'";
                    return String.format(sql, codePlaceholders, pOrg);
                }
                break;

            case "OMS_BD_CLCC":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT CODE AS CODE, NAME AS NAME FROM FZGJ_BD_TRUCK_SPEC_COM A WHERE A.CODE IN (%s) AND A.GROUP_CODE = '%s'";
                    return String.format(sql, codePlaceholders, pOrg);
                }
                break;

            case "OMS_BD_OP_TYPE_BY_BILL":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT CODE AS CODE, (NAME) AS NAME FROM FZGJ_BD_OP_TYPE A WHERE A.CODE IN (%s) AND A.GROUP_CODE = '%s'";
                    return String.format(sql, codePlaceholders, pOrg);
                }
                break;

            case "OMS_GJHD_FHR":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT (A.CODE) AS CODE, (A.CONTENT) AS NAME FROM FZGJ_GJHD_SHR_KH A WHERE A.TYPE='SH' AND A.CONTENT IN (%s) AND A.GROUP_CODE = '%s'";
                    return String.format(sql, codePlaceholders, pOrg);
                }
                break;

            case "OMS_GJHD_SHR":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT (A.CODE) AS CODE, (A.CONTENT) AS NAME FROM FZGJ_GJHD_SHR_KH A WHERE A.TYPE='CN' AND A.CONTENT IN (%s) AND A.GROUP_CODE = '%s'";
                    return String.format(sql, codePlaceholders, pOrg);
                }
                break;

            case "OMS_GJHD_TZR":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT (A.CODE) AS CODE, (A.CONTENT) AS NAME FROM FZGJ_GJHD_SHR_KH A WHERE A.TYPE='NOTICE' AND A.CONTENT IN (%s) AND A.GROUP_CODE = '%s'";
                    return String.format(sql, codePlaceholders, pOrg);
                }
                break;

            case "BMC_SSO_USER":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.USERNAME AS CODE, (A.TRUENAME) AS NAME FROM FZGJ_SSO_USER A WHERE A.USERNAME IN (%s) AND A.COMPANY_CODE = '%s'";
                    return String.format(sql, codePlaceholders, pOrg);
                }
                break;

            case "FZGJ_GOODS_ATTR":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.CODE AS CODE, (A.NAME) AS NAME FROM FZGJ_GOODS_ATTR A WHERE A.CODE IN (%s) AND A.GROUP_CODE = '%s'";
                    return String.format(sql, codePlaceholders, pOrg);
                }
                break;

            case "CREATE_USER":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.USERNAME AS CODE, (A.TRUENAME) AS NAME FROM FZGJ_SSO_USER A WHERE A.USERNAME IN (%s)";
                    return String.format(sql, codePlaceholders);
                }
                break;

            case "GROUP_CODE":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.CODE AS CODE, (A.NAME) AS NAME FROM FZGJ_CRM_ENTERPRISE A WHERE A.CODE IN (%s)";
                    return String.format(sql, codePlaceholders);
                }
                break;

            case "OMS_ORDER_STAGE":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.CODE AS CODE, (NAME) AS NAME FROM FZGJ_BASE_DATA_DETAIL A WHERE A.STATUS='Y' AND A.GROUP_CODE = 'OMS_ORDER_STAGE' AND A.CODE IN (%s)";
                    return String.format(sql, codePlaceholders);
                }
                break;

            default: {
                // 处理 OMS_BD_开头的通用情况
                if (pType.startsWith("OMS_BD_")) {
                    String dataType = pType.replace("OMS_BD_", "");
                    if (!codePlaceholders.isEmpty()) {
                        sql = "SELECT A.CODE AS CODE, A.NAME AS NAME FROM FZGJ_EXTEND_DATA A WHERE A.STATUS = 'Y' " +
                                "AND A.DATA_TYPE = '%s' AND A.CODE IN (%s) AND A.GROUP_CODE = '%s'";
                        return String.format(sql, dataType, codePlaceholders, pOrg);
                    }
                }
                // 默认返回空值（根据需求可抛异常）
                return "";
            }
        }
        return ""; // Return empty string if no match or pCode is empty for cases expecting it
    }

    /*
     * 根据类型获取名称 OMS_NAME 类似于：FNameManage 写法
     *
     * @param pType 类型，如：manage.Add("A.CONSIGNEE_CODE", FNameComList.CRM_CUSTOMER_SFHF, DbFunctionList.OMS_NAME, true);
     * @param pCode 主 SQL 返回 pageInfo 的时候的 code
     * @return 对应的 SQL

    public static String OMS_NAME(String pType, List<String> pCode) {
        String sql = "";
        String pOrg = UserContext.getUserInfo().getCompanyCode();
        String codePlaceholders = "";

        if (pCode != null && !pCode.isEmpty()) {
            codePlaceholders = pCode.stream()
                    .filter(s -> s != null && !s.isEmpty())
                    .map(code -> "'" + code + "'")
                    .collect(Collectors.joining(","));
        }

        switch (pType) {
            case "OMS_ISUSE":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT CASE WHEN A.CODE = 'Y' OR A.CODE = '1' THEN '是' ELSE '否' END AS NAME FROM DUAL A WHERE A.CODE IN (%s)";
                    return String.format(sql, codePlaceholders);
                }
                break;
            case "YNFLAG_NOT":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT CASE WHEN A.CODE = 'Y' THEN '是' WHEN A.CODE = 'N' THEN '否' ELSE '' END AS NAME FROM DUAL A WHERE A.CODE IN (%s)";
                    return String.format(sql, codePlaceholders);
                }
                break;
            case "OMS_BD_COUNTRY":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.CH_NAME AS NAME FROM FZGJ_BD_COUNTRY A WHERE A.CODE IN (%s)";
                    return String.format(sql, codePlaceholders);
                }
                break;
            case "OMS_BD_COUNTRY_TG":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.NAME AS NAME FROM FZGJ_BD_COUNTRY_TG A WHERE A.CODE IN (%s)";
                    return String.format(sql, codePlaceholders);
                }
                break;
            case "OMS_BD_COUNTRY_EN":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.EN_NAME AS NAME FROM FZGJ_BD_COUNTRY A WHERE A.CODE IN (%s)";
                    return String.format(sql, codePlaceholders);
                }
                break;
            case "OMS_BD_PROVINCE":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.NAME AS NAME FROM FZGJ_BD_PROVINCE A WHERE A.GUID IN (%s)";
                    return String.format(sql, codePlaceholders);
                }
                break;
            case "OMS_BD_CITY":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.NAME AS NAME FROM FZGJ_BD_CITY A WHERE A.GUID IN (%s)";
                    return String.format(sql, codePlaceholders);
                }
                break;
            case "OMS_BD_DISTRICT":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.NAME AS NAME FROM FZGJ_BD_DISTRICT A WHERE A.GUID IN (%s)";
                    return String.format(sql, codePlaceholders);
                }
                break;
            case "OMS_BOX_YARD":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.YARD_NAME AS NAME FROM FZGJ_BOX_YARD A WHERE A.YARD_CODE IN (%s)";
                    return String.format(sql, codePlaceholders);
                }
                break;
            case "OMS_BD_SERVICE_TYPE":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT CASE WHEN INSTR(A.NAME,'|')>0 THEN SUBSTR(A.NAME,0, INSTR(A.NAME,'|')-1 ) ELSE A.NAME END AS NAME FROM FZGJ_BD_SERVICE_TYPE A WHERE A.CODE IN (%s)";
                    return String.format(sql, codePlaceholders);
                }
                break;
            case "OMS_PRINT_UI":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.UI_NAME AS NAME FROM FZGJ_PRINT_UI A WHERE A.UI_CODE IN (%s)";
                    return String.format(sql, codePlaceholders);
                }
                break;
            case "OMS_PRINT_DATA":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.DATA_NAME AS NAME FROM FZGJ_PRINT_DATA A WHERE A.DATA_CODE IN (%s)";
                    return String.format(sql, codePlaceholders);
                }
                break;
            case "OMS_BD_BILL":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.NAME AS NAME FROM FZGJ_BD_BILL A WHERE A.STATUS = 'Y' AND A.CODE IN (%s)";
                    return String.format(sql, codePlaceholders);
                }
                break;
            case "OMS_AIR_PORT":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.NAME AS NAME FROM FZGJ_BD_AIR_PORT A WHERE A.STATUS = 'Y' AND A.CODE IN (%s)";
                    return String.format(sql, codePlaceholders);
                }
                break;
            case "OMS_SEA_PORT":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.NAME AS NAME FROM FZGJ_BD_SEA_PORT A WHERE A.STATUS = 'Y' AND A.CODE IN (%s)";
                    return String.format(sql, codePlaceholders);
                }
                break;
            case "OMS_BD_CHARGE_UNIT":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.NAME AS NAME FROM FZGJ_BD_CHARGE_UNIT A WHERE A.STATUS = 'Y' AND A.CODE IN (%s)";
                    return String.format(sql, codePlaceholders);
                }
                break;
            case "OMS_BD_CURRENCY":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.NAME AS NAME FROM FZGJ_BD_CURRENCY A WHERE A.STATUS = 'Y' AND A.CODE IN (%s)";
                    return String.format(sql, codePlaceholders);
                }
                break;
            case "OMS_BD_CUSTOMS":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.NAME AS NAME FROM FZGJ_BD_CUSTOMS A WHERE A.CODE IN (%s)";
                    return String.format(sql, codePlaceholders);
                }
                break;
            case "OMS_BD_DEAL":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.NAME AS NAME FROM FZGJ_BD_DEAL A WHERE A.CODE IN (%s)";
                    return String.format(sql, codePlaceholders);
                }
                break;
            case "OMS_BD_DOMESTICPORT":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.NAME AS NAME FROM FZGJ_BD_DOMESTICPORT A WHERE A.CODE IN (%s)";
                    return String.format(sql, codePlaceholders);
                }
                break;
            case "OMS_BD_INVOICE_TYPE":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.NAME AS NAME FROM FZGJ_BD_INVOICE_TYPE A WHERE A.STATUS = 'Y' AND A.CODE IN (%s)";
                    return String.format(sql, codePlaceholders);
                }
                break;
            case "OMS_BD_TRADE":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.NAME AS NAME FROM FZGJ_BD_TRADE A WHERE A.CODE IN (%s)";
                    return String.format(sql, codePlaceholders);
                }
                break;
            case "OMS_CRM_CONTRACT_TYPE_WARN":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.NAME AS NAME FROM FZGJ_CRM_CONTRACT_TYPE_WARN A WHERE A.CODE IN (%s)";
                    return String.format(sql, codePlaceholders);
                }
                break;
            case "OMS_SSO_NODE":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.FULLNAME AS NAME FROM FZGJ_SSO_NODE A WHERE A.CODE IN (%s)";
                    return String.format(sql, codePlaceholders);
                }
                break;
            case "OMS_BD_SERVICE_ITEM":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT MAX(CASE WHEN INSTR(A.NAME,'|')>0 THEN SUBSTR(A.NAME,0, INSTR(A.NAME,'|')-1 ) ELSE A.NAME END) AS NAME FROM FZGJ_BD_SERVICE_ITEM_PT A WHERE A.STATUS = 'Y' AND A.CODE IN (%s)";
                    return String.format(sql, codePlaceholders);
                }
                break;
            case "OMS_TASK_LIMITATION":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT MAX(A.NAME) AS NAME FROM FZGJ_TASK_LIMITATION_PT A WHERE A.STATUS = 'Y' AND A.CODE IN (%s)";
                    return String.format(sql, codePlaceholders);
                }
                break;
            case "OMS_BD_YSFS":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.NAME AS NAME FROM FZGJ_BD_YSFS A WHERE A.CODE IN (%s)";
                    return String.format(sql, codePlaceholders);
                }
                break;
            case "OMS_BD_LYXHD":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.NAME AS NAME FROM FZGJ_BD_LYXHD A WHERE A.CODE IN (%s)";
                    return String.format(sql, codePlaceholders);
                }
                break;
            case "OMS_BD_CUSTOM_UNIT":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.NAME AS NAME FROM FZGJ_BD_CUSTOM_UNIT A WHERE A.CODE IN (%s)";
                    return String.format(sql, codePlaceholders);
                }
                break;
            case "OMS_SERVICE_TYPE_ITEM":
                if (!codePlaceholders.isEmpty()) {
                    sql = "SELECT A.NAME AS NAME FROM FZGJ_BD_SERVICE_TYPE A WHERE A.CODE IN (%s)";
                    return String.format(sql, codePlaceholders);
                }
                break;
            default:
                if (pType.startsWith("OMS_BD_")) {
                    String dataType = pType.replace("OMS_BD_", "");
                    if (!codePlaceholders.isEmpty()) {
                        sql = "SELECT A.NAME AS NAME FROM FZGJ_BASE_DATA_DETAIL A WHERE A.STATUS = 'Y' AND (A.GROUP_CODE = '%s' OR A.GROUP_CODE = '%s') AND A.CODE IN (%s)";
                        return String.format(sql, dataType, pType, codePlaceholders);
                    }
                }
                return "";
        }
        return "";
    }
   */


    /**
     * 根据类型获取名称 OMS_NAME 类似于：FNameManage 写法
     * 修改：select 列统一为 code, name，where 条件统一使用 code
     *
     * @param pType 类型，如：manage.Add("A.CONSIGNEE_CODE", FNameComList.CRM_CUSTOMER_SFHF, DbFunctionList.OMS_NAME, true);
     * @param pCode 主 SQL 返回 pageInfo 的时候的 code 列表
     * @return 对应的 SQL
     */
    public static String OMS_NAME(String pType, List<String> pCode) {
        String sql = ""; // 不再提前声明 sql
        String pOrg = UserContext.getUserInfo().getCompanyCode(); // 组织机构代码，当前逻辑未用到

        if (pCode == null || pCode.isEmpty()) {
            // 如果代码列表为空，则返回空 SQL
            return "";
        }

        String codePlaceholders = pCode.stream()
                .filter(s -> s != null && !s.isEmpty())
                .map(code -> "'" + code + "'")
                .collect(Collectors.joining(","));

        // 如果过滤后没有有效的代码占位符，也返回空 SQL
        if (codePlaceholders.isEmpty()) {
            return "";
        }

        String table;
        String nameColumn; // 原始查询中作为 NAME 的列或表达式
        String additionalWhere = ""; // 额外的 WHERE 条件，如 " AND A.STATUS = 'Y'"

        switch (pType) {
            case "OMS_ISUSE":
                table = "DUAL A";
                nameColumn = "CASE WHEN A.CODE = 'Y' OR A.CODE = '1' THEN '是' ELSE '否' END";
                // 原始查询条件已是 A.CODE IN (%s)
                break;
            case "YNFLAG_NOT":
                table = "DUAL A";
                nameColumn = "CASE WHEN A.CODE = 'Y' THEN '是' WHEN A.CODE = 'N' THEN '否' ELSE '' END";
                // 原始查询条件已是 A.CODE IN (%s)
                break;
            case "OMS_BD_COUNTRY":
                table = "FZGJ_BD_COUNTRY A";
                nameColumn = "A.CH_NAME";
                // 原始查询条件已是 A.CODE IN (%s)
                break;
            case "OMS_BD_COUNTRY_TG":
                table = "FZGJ_BD_COUNTRY_TG A";
                nameColumn = "A.NAME";
                // 原始查询条件已是 A.CODE IN (%s)
                break;
            case "OMS_BD_COUNTRY_EN":
                table = "FZGJ_BD_COUNTRY A";
                nameColumn = "A.EN_NAME";
                // 原始查询条件已是 A.CODE IN (%s)
                break;
            case "OMS_BD_PROVINCE":
                table = "FZGJ_BD_PROVINCE A";
                nameColumn = "A.NAME";
                // 原始查询条件是 A.GUID IN (%s)，现改为 A.CODE IN (%s)
                break;
            case "OMS_BD_CITY":
                table = "FZGJ_BD_CITY A";
                nameColumn = "A.NAME";
                // 原始查询条件是 A.GUID IN (%s)，现改为 A.CODE IN (%s)
                break;
            case "OMS_BD_DISTRICT":
                table = "FZGJ_BD_DISTRICT A";
                nameColumn = "A.NAME";
                // 原始查询条件是 A.GUID IN (%s)，现改为 A.CODE IN (%s)
                break;
            case "OMS_BOX_YARD":
                table = "FZGJ_BOX_YARD A";
                nameColumn = "A.YARD_NAME";
                // 原始查询条件是 A.YARD_CODE IN (%s)，现改为 A.CODE IN (%s)
                break;
            case "OMS_BD_SERVICE_TYPE":
                table = "FZGJ_BD_SERVICE_TYPE A";
                nameColumn = "CASE WHEN INSTR(A.NAME,'|')>0 THEN SUBSTR(A.NAME,0, INSTR(A.NAME,'|')-1 ) ELSE A.NAME END";
                // 原始查询条件已是 A.CODE IN (%s)
                break;
            case "OMS_PRINT_UI":
                table = "FZGJ_PRINT_UI A";
                nameColumn = "A.UI_NAME";
                // 原始查询条件是 A.UI_CODE IN (%s)，现改为 A.CODE IN (%s)
                break;
            case "OMS_PRINT_DATA":
                table = "FZGJ_PRINT_DATA A";
                nameColumn = "A.DATA_NAME";
                // 原始查询条件是 A.DATA_CODE IN (%s)，现改为 A.CODE IN (%s)
                break;
            case "OMS_BD_BILL":
                table = "FZGJ_BD_BILL A";
                nameColumn = "A.NAME";
                additionalWhere = " AND A.STATUS = 'Y'";
                // 原始查询条件已是 A.CODE IN (%s)
                break;
            case "OMS_AIR_PORT":
                table = "FZGJ_BD_AIR_PORT A";
                nameColumn = "A.NAME";
                additionalWhere = " AND A.STATUS = 'Y'";
                // 原始查询条件已是 A.CODE IN (%s)
                break;
            case "OMS_SEA_PORT":
                table = "FZGJ_BD_SEA_PORT A";
                nameColumn = "A.NAME";
                additionalWhere = " AND A.STATUS = 'Y'";
                // 原始查询条件已是 A.CODE IN (%s)
                break;
            case "OMS_BD_CHARGE_UNIT":
                table = "FZGJ_BD_CHARGE_UNIT A";
                nameColumn = "A.NAME";
                additionalWhere = " AND A.STATUS = 'Y'";
                // 原始查询条件已是 A.CODE IN (%s)
                break;
            case "OMS_BD_CURRENCY":
                table = "FZGJ_BD_CURRENCY A";
                nameColumn = "A.NAME";
                additionalWhere = " AND A.STATUS = 'Y'";
                // 原始查询条件已是 A.CODE IN (%s)
                break;
            case "OMS_BD_CUSTOMS":
                table = "FZGJ_BD_CUSTOMS A";
                nameColumn = "A.NAME";
                // 原始查询条件已是 A.CODE IN (%s)
                break;
            case "OMS_BD_DEAL":
                table = "FZGJ_BD_DEAL A";
                nameColumn = "A.NAME";
                // 原始查询条件已是 A.CODE IN (%s)
                break;
            case "OMS_BD_DOMESTICPORT":
                table = "FZGJ_BD_DOMESTICPORT A";
                nameColumn = "A.NAME";
                // 原始查询条件已是 A.CODE IN (%s)
                break;
            case "OMS_BD_INVOICE_TYPE":
                table = "FZGJ_BD_INVOICE_TYPE A";
                nameColumn = "A.NAME";
                additionalWhere = " AND A.STATUS = 'Y'";
                // 原始查询条件已是 A.CODE IN (%s)
                break;
            case "OMS_BD_TRADE":
                table = "FZGJ_BD_TRADE A";
                nameColumn = "A.NAME";
                // 原始查询条件已是 A.CODE IN (%s)
                break;
            case "OMS_CRM_CONTRACT_TYPE_WARN":
                table = "FZGJ_CRM_CONTRACT_TYPE_WARN A";
                nameColumn = "A.NAME";
                // 原始查询条件已是 A.CODE IN (%s)
                break;
            case "OMS_SSO_NODE":
                table = "FZGJ_SSO_NODE A";
                nameColumn = "A.FULLNAME";
                // 原始查询条件已是 A.CODE IN (%s)
                break;
            case "OMS_BD_SERVICE_ITEM":
                table = "FZGJ_BD_SERVICE_ITEM_PT A";
                nameColumn = "MAX(CASE WHEN INSTR(A.NAME,'|')>0 THEN SUBSTR(A.NAME,0, INSTR(A.NAME,'|')-1 ) ELSE A.NAME END)";
                additionalWhere = " AND A.STATUS = 'Y'";
                // 原始查询条件已是 A.CODE IN (%s)
                break;
            case "OMS_TASK_LIMITATION":
                table = "FZGJ_TASK_LIMITATION_PT A";
                nameColumn = "MAX(A.NAME)";
                additionalWhere = " AND A.STATUS = 'Y'";
                // 原始查询条件已是 A.CODE IN (%s)
                break;
            case "OMS_BD_YSFS":
                table = "FZGJ_BD_YSFS A";
                nameColumn = "A.NAME";
                // 原始查询条件已是 A.CODE IN (%s)
                break;
            case "OMS_BD_LYXHD":
                table = "FZGJ_BD_LYXHD A";
                nameColumn = "A.NAME";
                // 原始查询条件已是 A.CODE IN (%s)
                break;
            case "OMS_BD_CUSTOM_UNIT":
                table = "FZGJ_BD_CUSTOM_UNIT A";
                nameColumn = "A.NAME";
                // 原始查询条件已是 A.CODE IN (%s)
                break;
            case "OMS_SERVICE_TYPE_ITEM":
                table = "FZGJ_BD_SERVICE_TYPE A";
                nameColumn = "A.NAME";
                // 原始查询条件已是 A.CODE IN (%s)
                break;
            default:
                // 处理 OMS_BD_ 开头的通用基础数据类型
                if (pType.startsWith("OMS_BD_")) {
                    String dataType = pType.replace("OMS_BD_", "");
                    table = "FZGJ_BASE_DATA_DETAIL A";
                    nameColumn = "A.NAME";
                    // 原始查询条件包含 A.GROUP_CODE = '%s' OR A.GROUP_CODE = '%s' AND A.CODE IN (%s)
                    // 补充 GROUP_CODE 条件
                    additionalWhere = String.format(" AND (A.GROUP_CODE = '%s' OR A.GROUP_CODE = '%s')", dataType, pType);
                    // 注意：原始 default case 没有 A.STATUS = 'Y'，此处保留原样
                } else {
                    // 未知类型，返回空 SQL
                    return "";
                }
                break; // default case 结束后也需要 break
        }

        // 统一构建 SQL 语句
        // SELECT A.CODE AS CODE, [原始的名称列] AS NAME FROM [表] WHERE A.CODE IN (%s) [附加条件]
        // 注意：这里的 WHERE A.CODE IN (%s) 强制使用 A.CODE 进行过滤
        sql = String.format("SELECT A.CODE AS CODE, %s AS NAME FROM %s WHERE A.CODE IN (%s)%s",
                nameColumn, table, codePlaceholders, additionalWhere);

        return sql;
    }
}