package com.eci.project.omsOrderJdmb.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.omsOrderJdmb.entity.OmsOrderJdmbEntity;


/**
* 接单模板Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-04-24
*/
public interface OmsOrderJdmbDao extends EciBaseDao<OmsOrderJdmbEntity> {

}