package com.eci.project.etmsBdTruckCertificateQz.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.etmsBdTruckCertificateQz.entity.EtmsBdTruckCertificateQzEntity;

import org.springframework.stereotype.Service;


/**
* 证件管理Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-27
*/
@Service
public class EtmsBdTruckCertificateQzVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(EtmsBdTruckCertificateQzEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(EtmsBdTruckCertificateQzEntity entity, BusinessType businessType) {

    }

}
