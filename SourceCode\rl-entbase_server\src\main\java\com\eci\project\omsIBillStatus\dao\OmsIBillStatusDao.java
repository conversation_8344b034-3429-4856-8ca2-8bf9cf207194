package com.eci.project.omsIBillStatus.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.omsIBillStatus.entity.OmsIBillStatusEntity;


/**
* 单据状态Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-05-16
*/
public interface OmsIBillStatusDao extends EciBaseDao<OmsIBillStatusEntity> {

}