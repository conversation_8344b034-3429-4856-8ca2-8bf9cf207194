package com.eci.project.fzgjBdServiceItemPages.controller;

import com.eci.common.ZsrJson;
import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjBdServiceItem.entity.FzgjBdServiceItemEntity;
import com.eci.project.fzgjBdServiceItemPages.service.FzgjBdServiceItemPagesService;
import com.eci.project.fzgjBdServiceItemPages.entity.FzgjBdServiceItemPagesEntity;
import com.eci.project.fzgjBdServiceItemPagesPt.entity.FzgjBdServiceItemPagesPtEntity;
import com.eci.project.fzgjBdServiceItemPagesPt.service.FzgjBdServiceItemPagesPtService;
import com.eci.project.fzgjBdServiceItemPt.entity.FzgjBdServiceItemPtEntity;
import com.eci.sso.login.entity.UserInfo;
import com.eci.sso.role.entity.UserContext;
import com.eci.wu.core.DataTable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
* 平台服务项目对应页面编辑区Controller
*
* @<NAME_EMAIL>
* @date 2025-04-07
*/
@Api(tags = "平台服务项目对应页面编辑区")
@RestController
@RequestMapping("/fzgjBdServiceItemPages")
public class FzgjBdServiceItemPagesController extends EciBaseController {

    @Autowired
    private FzgjBdServiceItemPagesService fzgjBdServiceItemPagesService;
    @Autowired
    private FzgjBdServiceItemPagesPtService fzgjBdServiceItemPagesPtService;




    @ApiOperation("平台服务项目对应页面编辑区:查询列表")
    @EciLog(title = "平台服务项目对应页面编辑区:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjBdServiceItemPagesEntity entity){
        List<FzgjBdServiceItemPagesEntity> fzgjBdServiceItemPagesEntities = fzgjBdServiceItemPagesService.selectList(entity);
        return ResponseMsgUtil.success(10001,fzgjBdServiceItemPagesEntities);
    }


    @ApiOperation("平台服务项目对应页面编辑区:分页查询列表")
    @EciLog(title = "平台服务项目对应页面编辑区:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjBdServiceItemPagesEntity entity){
        TgPageInfo tgPageInfo = fzgjBdServiceItemPagesService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("平台服务项目对应页面编辑区:根据ID查一条")
    @EciLog(title = "平台服务项目对应页面编辑区:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjBdServiceItemPagesEntity entity){
        FzgjBdServiceItemPagesEntity  fzgjBdServiceItemPagesEntity = fzgjBdServiceItemPagesService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,fzgjBdServiceItemPagesEntity);
    }


    @ApiOperation("平台服务项目对应页面编辑区:根据ID删除一条")
    @EciLog(title = "平台服务项目对应页面编辑区:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjBdServiceItemPagesEntity entity){
        int count = fzgjBdServiceItemPagesService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("平台服务项目对应页面编辑区:根据ID字符串删除多条")
    @EciLog(title = "平台服务项目对应页面编辑区:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody String jsonstring) {
        ZsrJson zsrJson= ZsrJson.parse(jsonstring);
        if(!zsrJson.exists("listkey")) return ResponseMsgUtil.error(10000,"请选择需要移除的业务产品/项目");
        List<String> listKey=zsrJson.getList("listkey",String.class);

        int count = fzgjBdServiceItemPagesService.deleteByIds(String.join(",",listKey));
        return ResponseMsgUtil.success(10001,count);
    }
    @ApiOperation("平台级服务项目:获取订单编辑区")
    @EciLog(title = "平台级服务项目:获取订单编辑区", businessType = BusinessType.SELECT)
    @PostMapping("/getCheckEditItem")
    @EciAction()
    public ResponseMsg getCheckEditItem(@RequestBody FzgjBdServiceItemPagesEntity entity){
        DataTable dt= fzgjBdServiceItemPagesService.getCheckEditItem(entity.getServiceItemCode());
        return ResponseMsgUtil.success(10001,dt);
    }
    @ApiOperation("平台服务项目对应页面编辑区:保存")
    @EciLog(title = "平台服务项目对应页面编辑区:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody String jsonstring){
        ZsrJson zsrJson= ZsrJson.parse(jsonstring);
        if(!zsrJson.exists("listkey")) return ResponseMsgUtil.error(10000,"请选择需要添加的业务产品/项目");
        List<String> listKey=zsrJson.getList("listkey",String.class);
        UserInfo user=UserContext.getUserInfo();
        List<FzgjBdServiceItemPagesEntity> list=new ArrayList<>();
        listKey.forEach(p->{
            FzgjBdServiceItemPagesPtEntity entity= fzgjBdServiceItemPagesPtService.selectOneById(p);
            FzgjBdServiceItemPagesEntity saveModel=new FzgjBdServiceItemPagesEntity();
            saveModel.setCode(entity.getCode());
            saveModel.setServiceItemCode(entity.getServiceItemCode());
            saveModel.setMemo(entity.getMemo());
            saveModel.setName(entity.getName());
            saveModel.setSeq(entity.getSeq());
            saveModel.setStatus(entity.getStatus());
            saveModel.setPageUrl(entity.getPageUrl());
            saveModel.setCompanyCode(user.getCompanyCode());
            saveModel.setCompanyName(user.getCompanyName());
            saveModel.setGroupCode(user.getCompanyCode());
            saveModel.setGroupName(user.getCompanyName());
            saveModel.setCreateDate(new Date());
            saveModel.setUpdateDate(new Date());
            saveModel.setCreateUser(user.getUserLoginNo());
            saveModel.setCreateUserName(user.getTrueName());
            saveModel.setUpdateUser(user.getUserLoginNo());
            saveModel.setUpdateUserName(user.getTrueName());
            list.add(saveModel);
        });
        fzgjBdServiceItemPagesService.insertBatch(list);
        return ResponseMsgUtil.success(10001);
    }
}