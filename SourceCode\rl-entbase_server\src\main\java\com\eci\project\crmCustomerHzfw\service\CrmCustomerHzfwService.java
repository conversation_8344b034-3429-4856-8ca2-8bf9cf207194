package com.eci.project.crmCustomerHzfw.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.crmCustomerHzfw.dao.CrmCustomerHzfwDao;
import com.eci.project.crmCustomerHzfw.entity.CrmCustomerHzfwEntity;
import com.eci.project.crmCustomerHzfw.validate.CrmCustomerHzfwVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 客户合作服务Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-29
*/
@Service
@Slf4j
public class CrmCustomerHzfwService implements EciBaseService<CrmCustomerHzfwEntity> {

    @Autowired
    private CrmCustomerHzfwDao crmCustomerHzfwDao;

    @Autowired
    private CrmCustomerHzfwVal crmCustomerHzfwVal;


    @Override
    public TgPageInfo queryPageList(CrmCustomerHzfwEntity entity) {
        EciQuery<CrmCustomerHzfwEntity> eciQuery = EciQuery.buildQuery(entity);
        List<CrmCustomerHzfwEntity> entities = crmCustomerHzfwDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public CrmCustomerHzfwEntity save(CrmCustomerHzfwEntity entity) {
        // 返回实体对象
        CrmCustomerHzfwEntity crmCustomerHzfwEntity = null;
        crmCustomerHzfwVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            crmCustomerHzfwEntity = crmCustomerHzfwDao.insertOne(entity);

        }else{

            crmCustomerHzfwEntity = crmCustomerHzfwDao.updateByEntityId(entity);

        }
        return crmCustomerHzfwEntity;
    }
    public CrmCustomerHzfwEntity buildData(String customerCode){
        CrmCustomerHzfwEntity data=new CrmCustomerHzfwEntity();
        data.setCustomerCode(customerCode);
        data.setCode("200");
        data.setName("国内运输");
        data.setStatus("Y");
        data.setHzfwType("HZ");
        return data;
    }

    @Override
    public List<CrmCustomerHzfwEntity> selectList(CrmCustomerHzfwEntity entity) {
        return crmCustomerHzfwDao.selectList(entity);
    }

    @Override
    public CrmCustomerHzfwEntity selectOneById(Serializable id) {
        return crmCustomerHzfwDao.selectById(id);
    }


    @Override
    public void insertBatch(List<CrmCustomerHzfwEntity> list) {
        crmCustomerHzfwDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return crmCustomerHzfwDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return crmCustomerHzfwDao.deleteById(id);
    }

}