package com.eci.project.fzgjBdServiceItemPt.entity;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <Description>TODO <br>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/25$
 */
@Data
public class TreeModel {
    public String label;
    public String id;
    public boolean isLeaf;
    public String parentid;
    public String parentcode;
    public String code;
    public String ptguid;
    public Integer checked;
    public Integer orgChecked;
    public String sysCode;
    public String seq;
    public String ownedService;
    public List<TreeModel> children=new ArrayList<>();
}
