package com.eci.project.etmsBdTruckInsuranceQz.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.etmsBdTruckInsuranceQz.entity.EtmsBdTruckInsuranceQzEntity;


/**
* 车辆保险历史Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-04-15
*/
public interface EtmsBdTruckInsuranceQzDao extends EciBaseDao<EtmsBdTruckInsuranceQzEntity> {

}