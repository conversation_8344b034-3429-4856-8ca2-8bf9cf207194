package com.eci.project.omsOrderFwxmTmsXlXlLyCl.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.omsOrderFwxmTmsXlXlLyCl.entity.OmsOrderFwxmTmsXlXlLyClEntity;

import org.springframework.stereotype.Service;


/**
* 委托内容-程运序列-陆运-车辆信息Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-06-04
*/
@Service
public class OmsOrderFwxmTmsXlXlLyClVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(OmsOrderFwxmTmsXlXlLyClEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(OmsOrderFwxmTmsXlXlLyClEntity entity, BusinessType businessType) {

    }

}
