<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.omsOrderFwxmTmsXlXlLy.dao.OmsOrderFwxmTmsXlXlLyDao">
    <resultMap type="OmsOrderFwxmTmsXlXlLyEntity" id="OmsOrderFwxmTmsXlXlLyResult">
        <result property="preNo" column="PRE_NO"/>
        <result property="orderNo" column="ORDER_NO"/>
        <result property="tmsNo" column="TMS_NO"/>
        <result property="lineNo" column="LINE_NO"/>
        <result property="seqNo" column="SEQ_NO"/>
        <result property="lyNo" column="LY_NO"/>
        <result property="cyfs" column="CYFS"/>
        <result property="isZp" column="IS_ZP"/>
        <result property="wbysjds" column="WBYSJDS"/>
        <result property="isLhwt" column="IS_LHWT"/>
        <result property="isFzzh" column="IS_FZZH"/>
        <result property="isFzxh" column="IS_FZXH"/>
        <result property="isYcy" column="IS_YCY"/>
        <result property="isDyyc" column="IS_DYYC"/>
        <result property="isCtfl" column="IS_CTFL"/>
        <result property="isGps" column="IS_GPS"/>
        <result property="isExpressway" column="IS_EXPRESSWAY"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="ldjfyj" column="LDJFYJ"/>
        <result property="jssdDate" column="JSSD_DATE"/>
        <result property="fwxmCode" column="FWXM_CODE"/>
        <result property="otherMemo" column="OTHER_MEMO"/>
        <result property="workNo" column="WORK_NO"/>
    </resultMap>

    <sql id="selectOmsOrderFwxmTmsXlXlLyEntityVo">
        select
            PRE_NO,
            ORDER_NO,
            TMS_NO,
            LINE_NO,
            SEQ_NO,
            LY_NO,
            CYFS,
            IS_ZP,
            WBYSJDS,
            IS_LHWT,
            IS_FZZH,
            IS_FZXH,
            IS_YCY,
            IS_DYYC,
            IS_CTFL,
            IS_GPS,
            IS_EXPRESSWAY,
            CREATE_USER,
            CREATE_USER_NAME,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_USER_NAME,
            UPDATE_DATE,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            LDJFYJ,
            JSSD_DATE,
            FWXM_CODE,
            OTHER_MEMO,
            WORK_NO
        from OMS_ORDER_FWXM_TMS_XL_XL_LY
    </sql>
</mapper>