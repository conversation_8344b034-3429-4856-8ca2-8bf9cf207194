package com.eci.project.fzgjScoreCompany.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.eci.project.fzgjScoreCar.entity.FzgjScoreCarEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 企业评分对象 FZGJ_SCORE_COMPANY
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-06-16
*/
@ApiModel("企业评分")
@TableName("FZGJ_SCORE_COMPANY")
@FieldNameConstants
public class FzgjScoreCompanyEntity extends EciBaseEntity{
    /**
    * GUID
    */
    @ApiModelProperty("GUID(50)")
    @TableId("GUID")
    private String guid;

    /**
    * 评分类目
    */
    @ApiModelProperty("评分类目(10)")
    @TableField("CATEGORY")
    private String category;

    /**
    * 分值
    */
    @ApiModelProperty("分值(22)")
    @TableField("SCORE")
    private Integer score;

    @ApiModelProperty("分值(22)")
    @TableField(exist = false)
    private Integer score1;

    @ApiModelProperty("(50)")
    @TableField("CREATE_USER")
    private String createUser;

    @ApiModelProperty("(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("结束")
    @TableField(exist=false)
    private Date createDateEnd;

    @ApiModelProperty("(50)")
    @TableField("UPDATE_USER")
    private String updateUser;

    @ApiModelProperty("(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    @ApiModelProperty("(200)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    @ApiModelProperty("(200)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 所属企业code
    */
    @ApiModelProperty("所属企业code(200)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 所属企业名称
    */
    @ApiModelProperty("所属企业名称(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 所属集团code
    */
    @ApiModelProperty("所属集团code(200)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 所属集团名称
    */
    @ApiModelProperty("所属集团名称(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public FzgjScoreCompanyEntity() {
        this.setSubClazz(FzgjScoreCompanyEntity.class);
    }

    public FzgjScoreCompanyEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public FzgjScoreCompanyEntity setCategory(String category) {
        this.category = category;
        this.nodifySetFiled("category", category);
        return this;
    }

    public String getCategory() {
        this.nodifyGetFiled("category");
        return category;
    }

    public FzgjScoreCompanyEntity setScore(Integer score) {
        this.score = score;
        this.nodifySetFiled("score", score);
        return this;
    }

    public Integer getScore() {
        this.nodifyGetFiled("score");
        return score;
    }

    public FzgjScoreCompanyEntity setScore1(Integer score1) {
        this.score1 = score1;
        this.nodifySetFiled("score1", score1);
        return this;
    }

    public Integer getScore1() {
        this.nodifyGetFiled("score1");
        return score1;
    }

    public FzgjScoreCompanyEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public FzgjScoreCompanyEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public FzgjScoreCompanyEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public FzgjScoreCompanyEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public FzgjScoreCompanyEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public FzgjScoreCompanyEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public FzgjScoreCompanyEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public FzgjScoreCompanyEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public FzgjScoreCompanyEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public FzgjScoreCompanyEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public FzgjScoreCompanyEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public FzgjScoreCompanyEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public FzgjScoreCompanyEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public FzgjScoreCompanyEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

}
