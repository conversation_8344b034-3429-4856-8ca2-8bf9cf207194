package com.eci.project.omsOrderLog.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.omsOrderLog.service.OmsOrderLogService;
import com.eci.project.omsOrderLog.entity.OmsOrderLogEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 订单操作日志信息Controller
*
* @<NAME_EMAIL>
* @date 2025-06-19
*/
@Api(tags = "订单操作日志信息")
@RestController
@RequestMapping("/omsOrderLog")
public class OmsOrderLogController extends EciBaseController {

    @Autowired
    private OmsOrderLogService omsOrderLogService;


    @ApiOperation("订单操作日志信息:保存")
    @EciLog(title = "订单操作日志信息:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody OmsOrderLogEntity entity){
        OmsOrderLogEntity omsOrderLogEntity =omsOrderLogService.save(entity);
        return ResponseMsgUtil.success(10001,omsOrderLogEntity);
    }


    @ApiOperation("订单操作日志信息:查询列表")
    @EciLog(title = "订单操作日志信息:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody OmsOrderLogEntity entity){
        List<OmsOrderLogEntity> omsOrderLogEntities = omsOrderLogService.selectList(entity);
        return ResponseMsgUtil.success(10001,omsOrderLogEntities);
    }


    @ApiOperation("订单操作日志信息:分页查询列表")
    @EciLog(title = "订单操作日志信息:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody OmsOrderLogEntity entity){
        TgPageInfo tgPageInfo = omsOrderLogService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("订单操作日志信息:根据ID查一条")
    @EciLog(title = "订单操作日志信息:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody OmsOrderLogEntity entity){
        OmsOrderLogEntity  omsOrderLogEntity = omsOrderLogService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,omsOrderLogEntity);
    }


    @ApiOperation("订单操作日志信息:根据ID删除一条")
    @EciLog(title = "订单操作日志信息:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody OmsOrderLogEntity entity){
        int count = omsOrderLogService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("订单操作日志信息:根据ID字符串删除多条")
    @EciLog(title = "订单操作日志信息:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody OmsOrderLogEntity entity) {
        int count = omsOrderLogService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}