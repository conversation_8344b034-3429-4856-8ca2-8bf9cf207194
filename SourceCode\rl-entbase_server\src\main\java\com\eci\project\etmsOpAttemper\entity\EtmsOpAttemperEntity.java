package com.eci.project.etmsOpAttemper.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.math.BigDecimal;
import java.util.Date;


/**
* 运输委托信息对象 ETMS_OP_ATTEMPER
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-06-13
*/
@ApiModel("运输委托信息")
@TableName("ETMS_OP_ATTEMPER")
@FieldNameConstants
public class EtmsOpAttemperEntity extends EciBaseEntity{
    /**
    * 主键
    */
    @ApiModelProperty("主键(50)")
    @TableId("GUID")
    private String guid;

    /**
    * 业务编号
    */
    @ApiModelProperty("业务编号(20)")
    @TableField("OP_NO")
    private String opNo;

    /**
    * 上级委托GUID
    */
    @ApiModelProperty("上级委托GUID(2,000)")
    @TableField("PARENT_GUID")
    private String parentGuid;

    /**
    * 委托编号
    */
    @ApiModelProperty("委托编号(2,000)")
    @TableField("ATT_NO")
    private String attNo;

    /**
    * 客户单证编号
    */
    @ApiModelProperty("客户单证编号(2,000)")
    @TableField("CUSTOMER_JOB_NO")
    private String customerJobNo;

    /**
    * 托运方业务伙伴代码
    */
    @ApiModelProperty("托运方业务伙伴代码(2,000)")
    @TableField("SHIPPER_CODE")
    private String shipperCode;

    /**
    * 承运方业务伙伴代码
    */
    @ApiModelProperty("承运方业务伙伴代码(2,000)")
    @TableField("DELIVERY_CODE")
    private String deliveryCode;

    /**
    * 结算方业务伙伴代码
    */
    @ApiModelProperty("结算方业务伙伴代码(2,000)")
    @TableField("ACCOUNT_CODE")
    private String accountCode;

    /**
    * 业务类型
    */
    @ApiModelProperty("业务类型(2,000)")
    @TableField("BUSINESS_TYPE")
    private String businessType;

    /**
    * 委托备注
    */
    @ApiModelProperty("委托备注(2,000)")
    @TableField("ATT_MEMO")
    private String attMemo;

    /**
    * 结算备注
    */
    @ApiModelProperty("结算备注(500)")
    @TableField("BALANCE_MEMO")
    private String balanceMemo;

    /**
    * 承运方式(空,承运,外包,部分外包)
    */
    @ApiModelProperty("承运方式(空,承运,外包,部分外包)(20)")
    @TableField("DELIVERY_MODE")
    private String deliveryMode;

    /**
    * 受理时间
    */
    @ApiModelProperty("受理时间(7)")
    @TableField("ACCEPT_DATE")
    private Date acceptDate;

    @ApiModelProperty("受理时间开始")
    @TableField(exist=false)
    private Date acceptDateStart;

    @ApiModelProperty("受理时间结束")
    @TableField(exist=false)
    private Date acceptDateEnd;

    /**
    * 受理人
    */
    @ApiModelProperty("受理人(20)")
    @TableField("ACCEPT_USER")
    private String acceptUser;

    /**
    * 状态
    */
    @ApiModelProperty("状态(10)")
    @TableField("STATUS")
    private String status;

    /**
    * 委托来源
    */
    @ApiModelProperty("委托来源(20)")
    @TableField("ATT_FROM")
    private String attFrom;

    /**
    * 理论总应付
    */
    @ApiModelProperty("理论总应付(22)")
    @TableField("AMOUNT_AP")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal amountAp;

    /**
    * 理论总应收
    */
    @ApiModelProperty("理论总应收(22)")
    @TableField("AMOUNT_AR")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal amountAr;

    /**
    * 创建企业
    */
    @ApiModelProperty("创建企业(20)")
    @TableField("CREATE_COMPANY")
    private String createCompany;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 编辑人
    */
    @ApiModelProperty("编辑人(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 编辑日期
    */
    @ApiModelProperty("编辑日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("编辑日期开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("编辑日期结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 作废原因
    */
    @ApiModelProperty("作废原因(100)")
    @TableField("ZF_REASON")
    private String zfReason;

    /**
    * 退单原因
    */
    @ApiModelProperty("退单原因(100)")
    @TableField("TD_REASON")
    private String tdReason;

    /**
    * 分发日期
    */
    @ApiModelProperty("分发日期(7)")
    @TableField("DISTRIBUTE_DATE")
    private Date distributeDate;

    @ApiModelProperty("分发日期开始")
    @TableField(exist=false)
    private Date distributeDateStart;

    @ApiModelProperty("分发日期结束")
    @TableField(exist=false)
    private Date distributeDateEnd;

    /**
    * 执行状态
    */
    @ApiModelProperty("执行状态(10)")
    @TableField("EXE_STATUS")
    private String exeStatus;

    /**
    * 发货方业务伙伴
    */
    @ApiModelProperty("发货方业务伙伴(400)")
    @TableField("SENDER_CODE")
    private String senderCode;

    /**
    * 收货方业务伙伴
    */
    @ApiModelProperty("收货方业务伙伴(400)")
    @TableField("CONSIGNEE_CODE")
    private String consigneeCode;

    /**
    * 起始要求作业时间
    */
    @ApiModelProperty("起始要求作业时间(7)")
    @TableField("START_REQUEST_DATE")
    private Date startRequestDate;

    @ApiModelProperty("起始要求作业时间开始")
    @TableField(exist=false)
    private Date startRequestDateStart;

    @ApiModelProperty("起始要求作业时间结束")
    @TableField(exist=false)
    private Date startRequestDateEnd;

    /**
    * 终到要求作业时间
    */
    @ApiModelProperty("终到要求作业时间(7)")
    @TableField("END_REQUEST_DATE")
    private Date endRequestDate;

    @ApiModelProperty("终到要求作业时间开始")
    @TableField(exist=false)
    private Date endRequestDateStart;

    @ApiModelProperty("终到要求作业时间结束")
    @TableField(exist=false)
    private Date endRequestDateEnd;

    /**
    * 起始地
    */
    @ApiModelProperty("起始地(400)")
    @TableField("START_OP_AREA")
    private String startOpArea;

    /**
    * 终到地
    */
    @ApiModelProperty("终到地(400)")
    @TableField("END_OP_AREA")
    private String endOpArea;

    /**
    * 最远目的地
    */
    @ApiModelProperty("最远目的地(400)")
    @TableField("FARATHEST_END")
    private String farathestEnd;

    /**
    * 件数
    */
    @ApiModelProperty("件数(200)")
    @TableField("PIECES")
    private String pieces;

    /**
    * 重量
    */
    @ApiModelProperty("重量(200)")
    @TableField("WEIGHT")
    private String weight;

    /**
    * 体积
    */
    @ApiModelProperty("体积(200)")
    @TableField("VOLUME")
    private String volume;

    /**
    * 销售员
    */
    @ApiModelProperty("销售员(50)")
    @TableField("SALES")
    private String sales;

    /**
    * 客户员
    */
    @ApiModelProperty("客户员(50)")
    @TableField("OPER")
    private String oper;

    /**
    * 确认接单人
    */
    @ApiModelProperty("确认接单人(50)")
    @TableField("CONFIRM_USER")
    private String confirmUser;

    /**
    * 确认接单时间
    */
    @ApiModelProperty("确认接单时间(7)")
    @TableField("CONFIRM_DATE")
    private Date confirmDate;

    @ApiModelProperty("确认接单时间开始")
    @TableField(exist=false)
    private Date confirmDateStart;

    @ApiModelProperty("确认接单时间结束")
    @TableField(exist=false)
    private Date confirmDateEnd;

    /**
    * 客户订单系统
    */
    @ApiModelProperty("客户订单系统(50)")
    @TableField("CUSTOMER_SYS")
    private String customerSys;

    /**
    * 企业订单流水号
    */
    @ApiModelProperty("企业订单流水号(50)")
    @TableField("CUSTOMER_SYS_NO")
    private String customerSysNo;

    /**
    * 结算方式
    */
    @ApiModelProperty("结算方式(20)")
    @TableField("ACCOUNT_MODE")
    private String accountMode;

    /**
    * 付款方式
    */
    @ApiModelProperty("付款方式(20)")
    @TableField("PAY_MODE")
    private String payMode;

    /**
    * 品名
    */
    @ApiModelProperty("品名(500)")
    @TableField("CARGO")
    private String cargo;

    /**
    * 车牌号
    */
    @ApiModelProperty("车牌号(200)")
    @TableField("CAR_NO")
    private String carNo;

    /**
    * 创建人所属部门ID
    */
    @ApiModelProperty("创建人所属部门ID(50)")
    @TableField("ORG_DEP_ID")
    private String orgDepId;

    /**
    * 创建人所属部门CODE
    */
    @ApiModelProperty("创建人所属部门CODE(50)")
    @TableField("ORG_DEP_CODE")
    private String orgDepCode;

    /**
    * 创建人所属部门名称
    */
    @ApiModelProperty("创建人所属部门名称(50)")
    @TableField("ORG_DEP_NAME")
    private String orgDepName;

    /**
    * 是否急货
    */
    @ApiModelProperty("是否急货(20)")
    @TableField("IS_URGENT")
    private String isUrgent;

    /**
    * 确认接单人所属机构代码
    */
    @ApiModelProperty("确认接单人所属机构代码(50)")
    @TableField("CONFIRM_USER_ORG_DEP_ID")
    private String confirmUserOrgDepId;

    /**
    * 确认接单人所属部门代码
    */
    @ApiModelProperty("确认接单人所属部门代码(50)")
    @TableField("CONFIRM_USER_ORG_DEP_CODE")
    private String confirmUserOrgDepCode;

    /**
    * 确认接单人所属机构名称
    */
    @ApiModelProperty("确认接单人所属机构名称(50)")
    @TableField("CONFIRM_USER_ORG_DEP_NAME")
    private String confirmUserOrgDepName;

    /**
    * 运输完成时间
    */
    @ApiModelProperty("运输完成时间(7)")
    @TableField("FINISHED_DATE")
    private Date finishedDate;

    @ApiModelProperty("运输完成时间开始")
    @TableField(exist=false)
    private Date finishedDateStart;

    @ApiModelProperty("运输完成时间结束")
    @TableField(exist=false)
    private Date finishedDateEnd;

    /**
    * 计费车辆规格*车数
    */
    @ApiModelProperty("计费车辆规格*车数(200)")
    @TableField("TRUCK_SPEC")
    private String truckSpec;

    /**
    * 导入批次号
    */
    @ApiModelProperty("导入批次号(20)")
    @TableField("BATCH_NO")
    private String batchNo;

    /**
    * 发送BMS
    */
    @ApiModelProperty("发送BMS(1)")
    @TableField("SEND_BMS")
    private String sendBms;

    /**
    * 最后发送BMS时间
    */
    @ApiModelProperty("最后发送BMS时间(7)")
    @TableField("SEND_BMS_DATE")
    private Date sendBmsDate;

    @ApiModelProperty("最后发送BMS时间开始")
    @TableField(exist=false)
    private Date sendBmsDateStart;

    @ApiModelProperty("最后发送BMS时间结束")
    @TableField(exist=false)
    private Date sendBmsDateEnd;

    /**
    * 发送BM失败原因
    */
    @ApiModelProperty("发送BM失败原因(2,000)")
    @TableField("SEND_BMS_ERROR")
    private String sendBmsError;

    /**
    * ETMS业务类型
    */
    @ApiModelProperty("ETMS业务类型(20)")
    @TableField("FMS_BUSINESS_TYPE")
    private String fmsBusinessType;

    /**
    * ETMS订单号
    */
    @ApiModelProperty("ETMS订单号(20)")
    @TableField("FMS_OP_NO")
    private String fmsOpNo;

    /**
    * ETMS发票号
    */
    @ApiModelProperty("ETMS发票号(40)")
    @TableField("FMS_INV_NO")
    private String fmsInvNo;

    /**
    * ETMS合同协议号
    */
    @ApiModelProperty("ETMS合同协议号(40)")
    @TableField("FMS_CONTRACT_NO")
    private String fmsContractNo;

    /**
    * 下级委托GUID 
    */
    @ApiModelProperty("下级委托GUID (52)")
    @TableField("SUBORDIN_GUID")
    private String subordinGuid;

    /**
    * 下级委托编号
    */
    @ApiModelProperty("下级委托编号(51)")
    @TableField("SUBORDIN_NO")
    private String subordinNo;

    /**
    * 是否外包委托单 
    */
    @ApiModelProperty("是否外包委托单 (50)")
    @TableField("IS_WB")
    private String isWb;

    /**
    * 物流费
    */
    @ApiModelProperty("物流费(22)")
    @TableField("PAY_WLF")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal payWlf;

    /**
    * 内部作业组织
    */
    @ApiModelProperty("内部作业组织(50)")
    @TableField("NBZY_NODE")
    private String nbzyNode;

    /**
    * 协作委托单号
    */
    @ApiModelProperty("协作委托单号(50)")
    @TableField("XZWT_NO")
    private String xzwtNo;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 是否客户自助
    */
    @ApiModelProperty("是否客户自助(50)")
    @TableField("IS_KHZZ")
    private String isKhzz;

    /**
    * 业务数据唯一注册编号
    */
    @ApiModelProperty("业务数据唯一注册编号(50)")
    @TableField("BIZ_REG_ID")
    private String bizRegId;

    /**
    * 业务日期
    */
    @ApiModelProperty("业务日期(7)")
    @TableField("OP_DATE")
    private Date opDate;

    @ApiModelProperty("业务日期开始")
    @TableField(exist=false)
    private Date opDateStart;

    @ApiModelProperty("业务日期结束")
    @TableField(exist=false)
    private Date opDateEnd;

    @ApiModelProperty("(1)")
    @TableField("KHZZ_XD")
    private String khzzXd;

    @ApiModelProperty("(100)")
    @TableField("UDF1")
    private String udf1;

    @ApiModelProperty("(100)")
    @TableField("UDF2")
    private String udf2;

    @ApiModelProperty("(100)")
    @TableField("UDF3")
    private String udf3;

    @ApiModelProperty("(100)")
    @TableField("UDF4")
    private String udf4;

    @ApiModelProperty("(100)")
    @TableField("UDF5")
    private String udf5;

    @ApiModelProperty("(100)")
    @TableField("UDF6")
    private String udf6;

    @ApiModelProperty("(100)")
    @TableField("UDF7")
    private String udf7;

    @ApiModelProperty("(100)")
    @TableField("UDF8")
    private String udf8;

    @ApiModelProperty("(400)")
    @TableField("UDF9")
    private String udf9;

    @ApiModelProperty("(400)")
    @TableField("UDF10")
    private String udf10;

    /**
    * WMS是否出库装车
    */
    @ApiModelProperty("WMS是否出库装车(10)")
    @TableField("IS_WMS_OUT_LOAD")
    private String isWmsOutLoad;

    /**
    * 作业数据齐全标志（源数据齐全）
    */
    @ApiModelProperty("作业数据齐全标志（源数据齐全）(1)")
    @TableField("DATA_OK")
    private String dataOk;

    /**
    * 作业数据齐全时间
    */
    @ApiModelProperty("作业数据齐全时间(7)")
    @TableField("DATA_OK_DATE")
    private Date dataOkDate;

    @ApiModelProperty("作业数据齐全时间开始")
    @TableField(exist=false)
    private Date dataOkDateStart;

    @ApiModelProperty("作业数据齐全时间结束")
    @TableField(exist=false)
    private Date dataOkDateEnd;

    /**
    * 作业数据齐全操作人
    */
    @ApiModelProperty("作业数据齐全操作人(40)")
    @TableField("DATA_OK_USER")
    private String dataOkUser;

    /**
    * 发送批次号
    */
    @ApiModelProperty("发送批次号(20)")
    @TableField("BATCH_NUMBER")
    private String batchNumber;

    /**
    * OMS订单号
    */
    @ApiModelProperty("OMS订单号(20)")
    @TableField("OMS_ORDER_NO")
    private String omsOrderNo;

    /**
    * OMS预订单号
    */
    @ApiModelProperty("OMS预订单号(36)")
    @TableField("OMS_PRE_NO")
    private String omsPreNo;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public EtmsOpAttemperEntity() {
        this.setSubClazz(EtmsOpAttemperEntity.class);
    }

    public EtmsOpAttemperEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public EtmsOpAttemperEntity setOpNo(String opNo) {
        this.opNo = opNo;
        this.nodifySetFiled("opNo", opNo);
        return this;
    }

    public String getOpNo() {
        this.nodifyGetFiled("opNo");
        return opNo;
    }

    public EtmsOpAttemperEntity setParentGuid(String parentGuid) {
        this.parentGuid = parentGuid;
        this.nodifySetFiled("parentGuid", parentGuid);
        return this;
    }

    public String getParentGuid() {
        this.nodifyGetFiled("parentGuid");
        return parentGuid;
    }

    public EtmsOpAttemperEntity setAttNo(String attNo) {
        this.attNo = attNo;
        this.nodifySetFiled("attNo", attNo);
        return this;
    }

    public String getAttNo() {
        this.nodifyGetFiled("attNo");
        return attNo;
    }

    public EtmsOpAttemperEntity setCustomerJobNo(String customerJobNo) {
        this.customerJobNo = customerJobNo;
        this.nodifySetFiled("customerJobNo", customerJobNo);
        return this;
    }

    public String getCustomerJobNo() {
        this.nodifyGetFiled("customerJobNo");
        return customerJobNo;
    }

    public EtmsOpAttemperEntity setShipperCode(String shipperCode) {
        this.shipperCode = shipperCode;
        this.nodifySetFiled("shipperCode", shipperCode);
        return this;
    }

    public String getShipperCode() {
        this.nodifyGetFiled("shipperCode");
        return shipperCode;
    }

    public EtmsOpAttemperEntity setDeliveryCode(String deliveryCode) {
        this.deliveryCode = deliveryCode;
        this.nodifySetFiled("deliveryCode", deliveryCode);
        return this;
    }

    public String getDeliveryCode() {
        this.nodifyGetFiled("deliveryCode");
        return deliveryCode;
    }

    public EtmsOpAttemperEntity setAccountCode(String accountCode) {
        this.accountCode = accountCode;
        this.nodifySetFiled("accountCode", accountCode);
        return this;
    }

    public String getAccountCode() {
        this.nodifyGetFiled("accountCode");
        return accountCode;
    }

    public EtmsOpAttemperEntity setBusinessType(String businessType) {
        this.businessType = businessType;
        this.nodifySetFiled("businessType", businessType);
        return this;
    }

    public String getBusinessType() {
        this.nodifyGetFiled("businessType");
        return businessType;
    }

    public EtmsOpAttemperEntity setAttMemo(String attMemo) {
        this.attMemo = attMemo;
        this.nodifySetFiled("attMemo", attMemo);
        return this;
    }

    public String getAttMemo() {
        this.nodifyGetFiled("attMemo");
        return attMemo;
    }

    public EtmsOpAttemperEntity setBalanceMemo(String balanceMemo) {
        this.balanceMemo = balanceMemo;
        this.nodifySetFiled("balanceMemo", balanceMemo);
        return this;
    }

    public String getBalanceMemo() {
        this.nodifyGetFiled("balanceMemo");
        return balanceMemo;
    }

    public EtmsOpAttemperEntity setDeliveryMode(String deliveryMode) {
        this.deliveryMode = deliveryMode;
        this.nodifySetFiled("deliveryMode", deliveryMode);
        return this;
    }

    public String getDeliveryMode() {
        this.nodifyGetFiled("deliveryMode");
        return deliveryMode;
    }

    public EtmsOpAttemperEntity setAcceptDate(Date acceptDate) {
        this.acceptDate = acceptDate;
        this.nodifySetFiled("acceptDate", acceptDate);
        return this;
    }

    public Date getAcceptDate() {
        this.nodifyGetFiled("acceptDate");
        return acceptDate;
    }

    public EtmsOpAttemperEntity setAcceptDateStart(Date acceptDateStart) {
        this.acceptDateStart = acceptDateStart;
        this.nodifySetFiled("acceptDateStart", acceptDateStart);
        return this;
    }

    public Date getAcceptDateStart() {
        this.nodifyGetFiled("acceptDateStart");
        return acceptDateStart;
    }

    public EtmsOpAttemperEntity setAcceptDateEnd(Date acceptDateEnd) {
        this.acceptDateEnd = acceptDateEnd;
        this.nodifySetFiled("acceptDateEnd", acceptDateEnd);
        return this;
    }

    public Date getAcceptDateEnd() {
        this.nodifyGetFiled("acceptDateEnd");
        return acceptDateEnd;
    }
    public EtmsOpAttemperEntity setAcceptUser(String acceptUser) {
        this.acceptUser = acceptUser;
        this.nodifySetFiled("acceptUser", acceptUser);
        return this;
    }

    public String getAcceptUser() {
        this.nodifyGetFiled("acceptUser");
        return acceptUser;
    }

    public EtmsOpAttemperEntity setStatus(String status) {
        this.status = status;
        this.nodifySetFiled("status", status);
        return this;
    }

    public String getStatus() {
        this.nodifyGetFiled("status");
        return status;
    }

    public EtmsOpAttemperEntity setAttFrom(String attFrom) {
        this.attFrom = attFrom;
        this.nodifySetFiled("attFrom", attFrom);
        return this;
    }

    public String getAttFrom() {
        this.nodifyGetFiled("attFrom");
        return attFrom;
    }

    public EtmsOpAttemperEntity setAmountAp(BigDecimal amountAp) {
        this.amountAp = amountAp;
        this.nodifySetFiled("amountAp", amountAp);
        return this;
    }

    public BigDecimal getAmountAp() {
        this.nodifyGetFiled("amountAp");
        return amountAp;
    }

    public EtmsOpAttemperEntity setAmountAr(BigDecimal amountAr) {
        this.amountAr = amountAr;
        this.nodifySetFiled("amountAr", amountAr);
        return this;
    }

    public BigDecimal getAmountAr() {
        this.nodifyGetFiled("amountAr");
        return amountAr;
    }

    public EtmsOpAttemperEntity setCreateCompany(String createCompany) {
        this.createCompany = createCompany;
        this.nodifySetFiled("createCompany", createCompany);
        return this;
    }

    public String getCreateCompany() {
        this.nodifyGetFiled("createCompany");
        return createCompany;
    }

    public EtmsOpAttemperEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public EtmsOpAttemperEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public EtmsOpAttemperEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public EtmsOpAttemperEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public EtmsOpAttemperEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public EtmsOpAttemperEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public EtmsOpAttemperEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public EtmsOpAttemperEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public EtmsOpAttemperEntity setZfReason(String zfReason) {
        this.zfReason = zfReason;
        this.nodifySetFiled("zfReason", zfReason);
        return this;
    }

    public String getZfReason() {
        this.nodifyGetFiled("zfReason");
        return zfReason;
    }

    public EtmsOpAttemperEntity setTdReason(String tdReason) {
        this.tdReason = tdReason;
        this.nodifySetFiled("tdReason", tdReason);
        return this;
    }

    public String getTdReason() {
        this.nodifyGetFiled("tdReason");
        return tdReason;
    }

    public EtmsOpAttemperEntity setDistributeDate(Date distributeDate) {
        this.distributeDate = distributeDate;
        this.nodifySetFiled("distributeDate", distributeDate);
        return this;
    }

    public Date getDistributeDate() {
        this.nodifyGetFiled("distributeDate");
        return distributeDate;
    }

    public EtmsOpAttemperEntity setDistributeDateStart(Date distributeDateStart) {
        this.distributeDateStart = distributeDateStart;
        this.nodifySetFiled("distributeDateStart", distributeDateStart);
        return this;
    }

    public Date getDistributeDateStart() {
        this.nodifyGetFiled("distributeDateStart");
        return distributeDateStart;
    }

    public EtmsOpAttemperEntity setDistributeDateEnd(Date distributeDateEnd) {
        this.distributeDateEnd = distributeDateEnd;
        this.nodifySetFiled("distributeDateEnd", distributeDateEnd);
        return this;
    }

    public Date getDistributeDateEnd() {
        this.nodifyGetFiled("distributeDateEnd");
        return distributeDateEnd;
    }
    public EtmsOpAttemperEntity setExeStatus(String exeStatus) {
        this.exeStatus = exeStatus;
        this.nodifySetFiled("exeStatus", exeStatus);
        return this;
    }

    public String getExeStatus() {
        this.nodifyGetFiled("exeStatus");
        return exeStatus;
    }

    public EtmsOpAttemperEntity setSenderCode(String senderCode) {
        this.senderCode = senderCode;
        this.nodifySetFiled("senderCode", senderCode);
        return this;
    }

    public String getSenderCode() {
        this.nodifyGetFiled("senderCode");
        return senderCode;
    }

    public EtmsOpAttemperEntity setConsigneeCode(String consigneeCode) {
        this.consigneeCode = consigneeCode;
        this.nodifySetFiled("consigneeCode", consigneeCode);
        return this;
    }

    public String getConsigneeCode() {
        this.nodifyGetFiled("consigneeCode");
        return consigneeCode;
    }

    public EtmsOpAttemperEntity setStartRequestDate(Date startRequestDate) {
        this.startRequestDate = startRequestDate;
        this.nodifySetFiled("startRequestDate", startRequestDate);
        return this;
    }

    public Date getStartRequestDate() {
        this.nodifyGetFiled("startRequestDate");
        return startRequestDate;
    }

    public EtmsOpAttemperEntity setStartRequestDateStart(Date startRequestDateStart) {
        this.startRequestDateStart = startRequestDateStart;
        this.nodifySetFiled("startRequestDateStart", startRequestDateStart);
        return this;
    }

    public Date getStartRequestDateStart() {
        this.nodifyGetFiled("startRequestDateStart");
        return startRequestDateStart;
    }

    public EtmsOpAttemperEntity setStartRequestDateEnd(Date startRequestDateEnd) {
        this.startRequestDateEnd = startRequestDateEnd;
        this.nodifySetFiled("startRequestDateEnd", startRequestDateEnd);
        return this;
    }

    public Date getStartRequestDateEnd() {
        this.nodifyGetFiled("startRequestDateEnd");
        return startRequestDateEnd;
    }
    public EtmsOpAttemperEntity setEndRequestDate(Date endRequestDate) {
        this.endRequestDate = endRequestDate;
        this.nodifySetFiled("endRequestDate", endRequestDate);
        return this;
    }

    public Date getEndRequestDate() {
        this.nodifyGetFiled("endRequestDate");
        return endRequestDate;
    }

    public EtmsOpAttemperEntity setEndRequestDateStart(Date endRequestDateStart) {
        this.endRequestDateStart = endRequestDateStart;
        this.nodifySetFiled("endRequestDateStart", endRequestDateStart);
        return this;
    }

    public Date getEndRequestDateStart() {
        this.nodifyGetFiled("endRequestDateStart");
        return endRequestDateStart;
    }

    public EtmsOpAttemperEntity setEndRequestDateEnd(Date endRequestDateEnd) {
        this.endRequestDateEnd = endRequestDateEnd;
        this.nodifySetFiled("endRequestDateEnd", endRequestDateEnd);
        return this;
    }

    public Date getEndRequestDateEnd() {
        this.nodifyGetFiled("endRequestDateEnd");
        return endRequestDateEnd;
    }
    public EtmsOpAttemperEntity setStartOpArea(String startOpArea) {
        this.startOpArea = startOpArea;
        this.nodifySetFiled("startOpArea", startOpArea);
        return this;
    }

    public String getStartOpArea() {
        this.nodifyGetFiled("startOpArea");
        return startOpArea;
    }

    public EtmsOpAttemperEntity setEndOpArea(String endOpArea) {
        this.endOpArea = endOpArea;
        this.nodifySetFiled("endOpArea", endOpArea);
        return this;
    }

    public String getEndOpArea() {
        this.nodifyGetFiled("endOpArea");
        return endOpArea;
    }

    public EtmsOpAttemperEntity setFarathestEnd(String farathestEnd) {
        this.farathestEnd = farathestEnd;
        this.nodifySetFiled("farathestEnd", farathestEnd);
        return this;
    }

    public String getFarathestEnd() {
        this.nodifyGetFiled("farathestEnd");
        return farathestEnd;
    }

    public EtmsOpAttemperEntity setPieces(String pieces) {
        this.pieces = pieces;
        this.nodifySetFiled("pieces", pieces);
        return this;
    }

    public String getPieces() {
        this.nodifyGetFiled("pieces");
        return pieces;
    }

    public EtmsOpAttemperEntity setWeight(String weight) {
        this.weight = weight;
        this.nodifySetFiled("weight", weight);
        return this;
    }

    public String getWeight() {
        this.nodifyGetFiled("weight");
        return weight;
    }

    public EtmsOpAttemperEntity setVolume(String volume) {
        this.volume = volume;
        this.nodifySetFiled("volume", volume);
        return this;
    }

    public String getVolume() {
        this.nodifyGetFiled("volume");
        return volume;
    }

    public EtmsOpAttemperEntity setSales(String sales) {
        this.sales = sales;
        this.nodifySetFiled("sales", sales);
        return this;
    }

    public String getSales() {
        this.nodifyGetFiled("sales");
        return sales;
    }

    public EtmsOpAttemperEntity setOper(String oper) {
        this.oper = oper;
        this.nodifySetFiled("oper", oper);
        return this;
    }

    public String getOper() {
        this.nodifyGetFiled("oper");
        return oper;
    }

    public EtmsOpAttemperEntity setConfirmUser(String confirmUser) {
        this.confirmUser = confirmUser;
        this.nodifySetFiled("confirmUser", confirmUser);
        return this;
    }

    public String getConfirmUser() {
        this.nodifyGetFiled("confirmUser");
        return confirmUser;
    }

    public EtmsOpAttemperEntity setConfirmDate(Date confirmDate) {
        this.confirmDate = confirmDate;
        this.nodifySetFiled("confirmDate", confirmDate);
        return this;
    }

    public Date getConfirmDate() {
        this.nodifyGetFiled("confirmDate");
        return confirmDate;
    }

    public EtmsOpAttemperEntity setConfirmDateStart(Date confirmDateStart) {
        this.confirmDateStart = confirmDateStart;
        this.nodifySetFiled("confirmDateStart", confirmDateStart);
        return this;
    }

    public Date getConfirmDateStart() {
        this.nodifyGetFiled("confirmDateStart");
        return confirmDateStart;
    }

    public EtmsOpAttemperEntity setConfirmDateEnd(Date confirmDateEnd) {
        this.confirmDateEnd = confirmDateEnd;
        this.nodifySetFiled("confirmDateEnd", confirmDateEnd);
        return this;
    }

    public Date getConfirmDateEnd() {
        this.nodifyGetFiled("confirmDateEnd");
        return confirmDateEnd;
    }
    public EtmsOpAttemperEntity setCustomerSys(String customerSys) {
        this.customerSys = customerSys;
        this.nodifySetFiled("customerSys", customerSys);
        return this;
    }

    public String getCustomerSys() {
        this.nodifyGetFiled("customerSys");
        return customerSys;
    }

    public EtmsOpAttemperEntity setCustomerSysNo(String customerSysNo) {
        this.customerSysNo = customerSysNo;
        this.nodifySetFiled("customerSysNo", customerSysNo);
        return this;
    }

    public String getCustomerSysNo() {
        this.nodifyGetFiled("customerSysNo");
        return customerSysNo;
    }

    public EtmsOpAttemperEntity setAccountMode(String accountMode) {
        this.accountMode = accountMode;
        this.nodifySetFiled("accountMode", accountMode);
        return this;
    }

    public String getAccountMode() {
        this.nodifyGetFiled("accountMode");
        return accountMode;
    }

    public EtmsOpAttemperEntity setPayMode(String payMode) {
        this.payMode = payMode;
        this.nodifySetFiled("payMode", payMode);
        return this;
    }

    public String getPayMode() {
        this.nodifyGetFiled("payMode");
        return payMode;
    }

    public EtmsOpAttemperEntity setCargo(String cargo) {
        this.cargo = cargo;
        this.nodifySetFiled("cargo", cargo);
        return this;
    }

    public String getCargo() {
        this.nodifyGetFiled("cargo");
        return cargo;
    }

    public EtmsOpAttemperEntity setCarNo(String carNo) {
        this.carNo = carNo;
        this.nodifySetFiled("carNo", carNo);
        return this;
    }

    public String getCarNo() {
        this.nodifyGetFiled("carNo");
        return carNo;
    }

    public EtmsOpAttemperEntity setOrgDepId(String orgDepId) {
        this.orgDepId = orgDepId;
        this.nodifySetFiled("orgDepId", orgDepId);
        return this;
    }

    public String getOrgDepId() {
        this.nodifyGetFiled("orgDepId");
        return orgDepId;
    }

    public EtmsOpAttemperEntity setOrgDepCode(String orgDepCode) {
        this.orgDepCode = orgDepCode;
        this.nodifySetFiled("orgDepCode", orgDepCode);
        return this;
    }

    public String getOrgDepCode() {
        this.nodifyGetFiled("orgDepCode");
        return orgDepCode;
    }

    public EtmsOpAttemperEntity setOrgDepName(String orgDepName) {
        this.orgDepName = orgDepName;
        this.nodifySetFiled("orgDepName", orgDepName);
        return this;
    }

    public String getOrgDepName() {
        this.nodifyGetFiled("orgDepName");
        return orgDepName;
    }

    public EtmsOpAttemperEntity setIsUrgent(String isUrgent) {
        this.isUrgent = isUrgent;
        this.nodifySetFiled("isUrgent", isUrgent);
        return this;
    }

    public String getIsUrgent() {
        this.nodifyGetFiled("isUrgent");
        return isUrgent;
    }

    public EtmsOpAttemperEntity setConfirmUserOrgDepId(String confirmUserOrgDepId) {
        this.confirmUserOrgDepId = confirmUserOrgDepId;
        this.nodifySetFiled("confirmUserOrgDepId", confirmUserOrgDepId);
        return this;
    }

    public String getConfirmUserOrgDepId() {
        this.nodifyGetFiled("confirmUserOrgDepId");
        return confirmUserOrgDepId;
    }

    public EtmsOpAttemperEntity setConfirmUserOrgDepCode(String confirmUserOrgDepCode) {
        this.confirmUserOrgDepCode = confirmUserOrgDepCode;
        this.nodifySetFiled("confirmUserOrgDepCode", confirmUserOrgDepCode);
        return this;
    }

    public String getConfirmUserOrgDepCode() {
        this.nodifyGetFiled("confirmUserOrgDepCode");
        return confirmUserOrgDepCode;
    }

    public EtmsOpAttemperEntity setConfirmUserOrgDepName(String confirmUserOrgDepName) {
        this.confirmUserOrgDepName = confirmUserOrgDepName;
        this.nodifySetFiled("confirmUserOrgDepName", confirmUserOrgDepName);
        return this;
    }

    public String getConfirmUserOrgDepName() {
        this.nodifyGetFiled("confirmUserOrgDepName");
        return confirmUserOrgDepName;
    }

    public EtmsOpAttemperEntity setFinishedDate(Date finishedDate) {
        this.finishedDate = finishedDate;
        this.nodifySetFiled("finishedDate", finishedDate);
        return this;
    }

    public Date getFinishedDate() {
        this.nodifyGetFiled("finishedDate");
        return finishedDate;
    }

    public EtmsOpAttemperEntity setFinishedDateStart(Date finishedDateStart) {
        this.finishedDateStart = finishedDateStart;
        this.nodifySetFiled("finishedDateStart", finishedDateStart);
        return this;
    }

    public Date getFinishedDateStart() {
        this.nodifyGetFiled("finishedDateStart");
        return finishedDateStart;
    }

    public EtmsOpAttemperEntity setFinishedDateEnd(Date finishedDateEnd) {
        this.finishedDateEnd = finishedDateEnd;
        this.nodifySetFiled("finishedDateEnd", finishedDateEnd);
        return this;
    }

    public Date getFinishedDateEnd() {
        this.nodifyGetFiled("finishedDateEnd");
        return finishedDateEnd;
    }
    public EtmsOpAttemperEntity setTruckSpec(String truckSpec) {
        this.truckSpec = truckSpec;
        this.nodifySetFiled("truckSpec", truckSpec);
        return this;
    }

    public String getTruckSpec() {
        this.nodifyGetFiled("truckSpec");
        return truckSpec;
    }

    public EtmsOpAttemperEntity setBatchNo(String batchNo) {
        this.batchNo = batchNo;
        this.nodifySetFiled("batchNo", batchNo);
        return this;
    }

    public String getBatchNo() {
        this.nodifyGetFiled("batchNo");
        return batchNo;
    }

    public EtmsOpAttemperEntity setSendBms(String sendBms) {
        this.sendBms = sendBms;
        this.nodifySetFiled("sendBms", sendBms);
        return this;
    }

    public String getSendBms() {
        this.nodifyGetFiled("sendBms");
        return sendBms;
    }

    public EtmsOpAttemperEntity setSendBmsDate(Date sendBmsDate) {
        this.sendBmsDate = sendBmsDate;
        this.nodifySetFiled("sendBmsDate", sendBmsDate);
        return this;
    }

    public Date getSendBmsDate() {
        this.nodifyGetFiled("sendBmsDate");
        return sendBmsDate;
    }

    public EtmsOpAttemperEntity setSendBmsDateStart(Date sendBmsDateStart) {
        this.sendBmsDateStart = sendBmsDateStart;
        this.nodifySetFiled("sendBmsDateStart", sendBmsDateStart);
        return this;
    }

    public Date getSendBmsDateStart() {
        this.nodifyGetFiled("sendBmsDateStart");
        return sendBmsDateStart;
    }

    public EtmsOpAttemperEntity setSendBmsDateEnd(Date sendBmsDateEnd) {
        this.sendBmsDateEnd = sendBmsDateEnd;
        this.nodifySetFiled("sendBmsDateEnd", sendBmsDateEnd);
        return this;
    }

    public Date getSendBmsDateEnd() {
        this.nodifyGetFiled("sendBmsDateEnd");
        return sendBmsDateEnd;
    }
    public EtmsOpAttemperEntity setSendBmsError(String sendBmsError) {
        this.sendBmsError = sendBmsError;
        this.nodifySetFiled("sendBmsError", sendBmsError);
        return this;
    }

    public String getSendBmsError() {
        this.nodifyGetFiled("sendBmsError");
        return sendBmsError;
    }

    public EtmsOpAttemperEntity setFmsBusinessType(String fmsBusinessType) {
        this.fmsBusinessType = fmsBusinessType;
        this.nodifySetFiled("fmsBusinessType", fmsBusinessType);
        return this;
    }

    public String getFmsBusinessType() {
        this.nodifyGetFiled("fmsBusinessType");
        return fmsBusinessType;
    }

    public EtmsOpAttemperEntity setFmsOpNo(String fmsOpNo) {
        this.fmsOpNo = fmsOpNo;
        this.nodifySetFiled("fmsOpNo", fmsOpNo);
        return this;
    }

    public String getFmsOpNo() {
        this.nodifyGetFiled("fmsOpNo");
        return fmsOpNo;
    }

    public EtmsOpAttemperEntity setFmsInvNo(String fmsInvNo) {
        this.fmsInvNo = fmsInvNo;
        this.nodifySetFiled("fmsInvNo", fmsInvNo);
        return this;
    }

    public String getFmsInvNo() {
        this.nodifyGetFiled("fmsInvNo");
        return fmsInvNo;
    }

    public EtmsOpAttemperEntity setFmsContractNo(String fmsContractNo) {
        this.fmsContractNo = fmsContractNo;
        this.nodifySetFiled("fmsContractNo", fmsContractNo);
        return this;
    }

    public String getFmsContractNo() {
        this.nodifyGetFiled("fmsContractNo");
        return fmsContractNo;
    }

    public EtmsOpAttemperEntity setSubordinGuid(String subordinGuid) {
        this.subordinGuid = subordinGuid;
        this.nodifySetFiled("subordinGuid", subordinGuid);
        return this;
    }

    public String getSubordinGuid() {
        this.nodifyGetFiled("subordinGuid");
        return subordinGuid;
    }

    public EtmsOpAttemperEntity setSubordinNo(String subordinNo) {
        this.subordinNo = subordinNo;
        this.nodifySetFiled("subordinNo", subordinNo);
        return this;
    }

    public String getSubordinNo() {
        this.nodifyGetFiled("subordinNo");
        return subordinNo;
    }

    public EtmsOpAttemperEntity setIsWb(String isWb) {
        this.isWb = isWb;
        this.nodifySetFiled("isWb", isWb);
        return this;
    }

    public String getIsWb() {
        this.nodifyGetFiled("isWb");
        return isWb;
    }

    public EtmsOpAttemperEntity setPayWlf(BigDecimal payWlf) {
        this.payWlf = payWlf;
        this.nodifySetFiled("payWlf", payWlf);
        return this;
    }

    public BigDecimal getPayWlf() {
        this.nodifyGetFiled("payWlf");
        return payWlf;
    }

    public EtmsOpAttemperEntity setNbzyNode(String nbzyNode) {
        this.nbzyNode = nbzyNode;
        this.nodifySetFiled("nbzyNode", nbzyNode);
        return this;
    }

    public String getNbzyNode() {
        this.nodifyGetFiled("nbzyNode");
        return nbzyNode;
    }

    public EtmsOpAttemperEntity setXzwtNo(String xzwtNo) {
        this.xzwtNo = xzwtNo;
        this.nodifySetFiled("xzwtNo", xzwtNo);
        return this;
    }

    public String getXzwtNo() {
        this.nodifyGetFiled("xzwtNo");
        return xzwtNo;
    }

    public EtmsOpAttemperEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public EtmsOpAttemperEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public EtmsOpAttemperEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public EtmsOpAttemperEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public EtmsOpAttemperEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public EtmsOpAttemperEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public EtmsOpAttemperEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public EtmsOpAttemperEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public EtmsOpAttemperEntity setIsKhzz(String isKhzz) {
        this.isKhzz = isKhzz;
        this.nodifySetFiled("isKhzz", isKhzz);
        return this;
    }

    public String getIsKhzz() {
        this.nodifyGetFiled("isKhzz");
        return isKhzz;
    }

    public EtmsOpAttemperEntity setBizRegId(String bizRegId) {
        this.bizRegId = bizRegId;
        this.nodifySetFiled("bizRegId", bizRegId);
        return this;
    }

    public String getBizRegId() {
        this.nodifyGetFiled("bizRegId");
        return bizRegId;
    }

    public EtmsOpAttemperEntity setOpDate(Date opDate) {
        this.opDate = opDate;
        this.nodifySetFiled("opDate", opDate);
        return this;
    }

    public Date getOpDate() {
        this.nodifyGetFiled("opDate");
        return opDate;
    }

    public EtmsOpAttemperEntity setOpDateStart(Date opDateStart) {
        this.opDateStart = opDateStart;
        this.nodifySetFiled("opDateStart", opDateStart);
        return this;
    }

    public Date getOpDateStart() {
        this.nodifyGetFiled("opDateStart");
        return opDateStart;
    }

    public EtmsOpAttemperEntity setOpDateEnd(Date opDateEnd) {
        this.opDateEnd = opDateEnd;
        this.nodifySetFiled("opDateEnd", opDateEnd);
        return this;
    }

    public Date getOpDateEnd() {
        this.nodifyGetFiled("opDateEnd");
        return opDateEnd;
    }
    public EtmsOpAttemperEntity setKhzzXd(String khzzXd) {
        this.khzzXd = khzzXd;
        this.nodifySetFiled("khzzXd", khzzXd);
        return this;
    }

    public String getKhzzXd() {
        this.nodifyGetFiled("khzzXd");
        return khzzXd;
    }

    public EtmsOpAttemperEntity setUdf1(String udf1) {
        this.udf1 = udf1;
        this.nodifySetFiled("udf1", udf1);
        return this;
    }

    public String getUdf1() {
        this.nodifyGetFiled("udf1");
        return udf1;
    }

    public EtmsOpAttemperEntity setUdf2(String udf2) {
        this.udf2 = udf2;
        this.nodifySetFiled("udf2", udf2);
        return this;
    }

    public String getUdf2() {
        this.nodifyGetFiled("udf2");
        return udf2;
    }

    public EtmsOpAttemperEntity setUdf3(String udf3) {
        this.udf3 = udf3;
        this.nodifySetFiled("udf3", udf3);
        return this;
    }

    public String getUdf3() {
        this.nodifyGetFiled("udf3");
        return udf3;
    }

    public EtmsOpAttemperEntity setUdf4(String udf4) {
        this.udf4 = udf4;
        this.nodifySetFiled("udf4", udf4);
        return this;
    }

    public String getUdf4() {
        this.nodifyGetFiled("udf4");
        return udf4;
    }

    public EtmsOpAttemperEntity setUdf5(String udf5) {
        this.udf5 = udf5;
        this.nodifySetFiled("udf5", udf5);
        return this;
    }

    public String getUdf5() {
        this.nodifyGetFiled("udf5");
        return udf5;
    }

    public EtmsOpAttemperEntity setUdf6(String udf6) {
        this.udf6 = udf6;
        this.nodifySetFiled("udf6", udf6);
        return this;
    }

    public String getUdf6() {
        this.nodifyGetFiled("udf6");
        return udf6;
    }

    public EtmsOpAttemperEntity setUdf7(String udf7) {
        this.udf7 = udf7;
        this.nodifySetFiled("udf7", udf7);
        return this;
    }

    public String getUdf7() {
        this.nodifyGetFiled("udf7");
        return udf7;
    }

    public EtmsOpAttemperEntity setUdf8(String udf8) {
        this.udf8 = udf8;
        this.nodifySetFiled("udf8", udf8);
        return this;
    }

    public String getUdf8() {
        this.nodifyGetFiled("udf8");
        return udf8;
    }

    public EtmsOpAttemperEntity setUdf9(String udf9) {
        this.udf9 = udf9;
        this.nodifySetFiled("udf9", udf9);
        return this;
    }

    public String getUdf9() {
        this.nodifyGetFiled("udf9");
        return udf9;
    }

    public EtmsOpAttemperEntity setUdf10(String udf10) {
        this.udf10 = udf10;
        this.nodifySetFiled("udf10", udf10);
        return this;
    }

    public String getUdf10() {
        this.nodifyGetFiled("udf10");
        return udf10;
    }

    public EtmsOpAttemperEntity setIsWmsOutLoad(String isWmsOutLoad) {
        this.isWmsOutLoad = isWmsOutLoad;
        this.nodifySetFiled("isWmsOutLoad", isWmsOutLoad);
        return this;
    }

    public String getIsWmsOutLoad() {
        this.nodifyGetFiled("isWmsOutLoad");
        return isWmsOutLoad;
    }

    public EtmsOpAttemperEntity setDataOk(String dataOk) {
        this.dataOk = dataOk;
        this.nodifySetFiled("dataOk", dataOk);
        return this;
    }

    public String getDataOk() {
        this.nodifyGetFiled("dataOk");
        return dataOk;
    }

    public EtmsOpAttemperEntity setDataOkDate(Date dataOkDate) {
        this.dataOkDate = dataOkDate;
        this.nodifySetFiled("dataOkDate", dataOkDate);
        return this;
    }

    public Date getDataOkDate() {
        this.nodifyGetFiled("dataOkDate");
        return dataOkDate;
    }

    public EtmsOpAttemperEntity setDataOkDateStart(Date dataOkDateStart) {
        this.dataOkDateStart = dataOkDateStart;
        this.nodifySetFiled("dataOkDateStart", dataOkDateStart);
        return this;
    }

    public Date getDataOkDateStart() {
        this.nodifyGetFiled("dataOkDateStart");
        return dataOkDateStart;
    }

    public EtmsOpAttemperEntity setDataOkDateEnd(Date dataOkDateEnd) {
        this.dataOkDateEnd = dataOkDateEnd;
        this.nodifySetFiled("dataOkDateEnd", dataOkDateEnd);
        return this;
    }

    public Date getDataOkDateEnd() {
        this.nodifyGetFiled("dataOkDateEnd");
        return dataOkDateEnd;
    }
    public EtmsOpAttemperEntity setDataOkUser(String dataOkUser) {
        this.dataOkUser = dataOkUser;
        this.nodifySetFiled("dataOkUser", dataOkUser);
        return this;
    }

    public String getDataOkUser() {
        this.nodifyGetFiled("dataOkUser");
        return dataOkUser;
    }

    public EtmsOpAttemperEntity setBatchNumber(String batchNumber) {
        this.batchNumber = batchNumber;
        this.nodifySetFiled("batchNumber", batchNumber);
        return this;
    }

    public String getBatchNumber() {
        this.nodifyGetFiled("batchNumber");
        return batchNumber;
    }

    public EtmsOpAttemperEntity setOmsOrderNo(String omsOrderNo) {
        this.omsOrderNo = omsOrderNo;
        this.nodifySetFiled("omsOrderNo", omsOrderNo);
        return this;
    }

    public String getOmsOrderNo() {
        this.nodifyGetFiled("omsOrderNo");
        return omsOrderNo;
    }

    public EtmsOpAttemperEntity setOmsPreNo(String omsPreNo) {
        this.omsPreNo = omsPreNo;
        this.nodifySetFiled("omsPreNo", omsPreNo);
        return this;
    }

    public String getOmsPreNo() {
        this.nodifyGetFiled("omsPreNo");
        return omsPreNo;
    }

}
