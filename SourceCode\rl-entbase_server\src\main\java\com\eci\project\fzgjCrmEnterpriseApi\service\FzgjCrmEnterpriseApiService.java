package com.eci.project.fzgjCrmEnterpriseApi.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjCrmEnterpriseApi.dao.FzgjCrmEnterpriseApiDao;
import com.eci.project.fzgjCrmEnterpriseApi.entity.FzgjCrmEnterpriseApiEntity;
import com.eci.project.fzgjCrmEnterpriseApi.validate.FzgjCrmEnterpriseApiVal;

import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 注册企业发送接口Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-03-13
*/
@Service
@Slf4j
public class FzgjCrmEnterpriseApiService implements EciBaseService<FzgjCrmEnterpriseApiEntity> {

    @Autowired
    private FzgjCrmEnterpriseApiDao fzgjCrmEnterpriseApiDao;

    @Autowired
    private FzgjCrmEnterpriseApiVal fzgjCrmEnterpriseApiVal;


    @Override
    public TgPageInfo queryPageList(FzgjCrmEnterpriseApiEntity entity) {
        EciQuery<FzgjCrmEnterpriseApiEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjCrmEnterpriseApiEntity> entities = fzgjCrmEnterpriseApiDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjCrmEnterpriseApiEntity save(FzgjCrmEnterpriseApiEntity entity) {
        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setCreateDate(new java.util.Date());
            entity.setUpdateDate(new java.util.Date());
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        } else {
            entity.setUpdateDate(new java.util.Date());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        }
        // 返回实体对象
        FzgjCrmEnterpriseApiEntity fzgjCrmEnterpriseApiEntity = null;
        fzgjCrmEnterpriseApiVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjCrmEnterpriseApiEntity = fzgjCrmEnterpriseApiDao.insertOne(entity);

        }else{

            fzgjCrmEnterpriseApiEntity = fzgjCrmEnterpriseApiDao.updateByEntityId(entity);

        }
        return fzgjCrmEnterpriseApiEntity;
    }

    @Override
    public List<FzgjCrmEnterpriseApiEntity> selectList(FzgjCrmEnterpriseApiEntity entity) {
        return fzgjCrmEnterpriseApiDao.selectList(entity);
    }

    @Override
    public FzgjCrmEnterpriseApiEntity selectOneById(Serializable id) {
        return fzgjCrmEnterpriseApiDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjCrmEnterpriseApiEntity> list) {
        fzgjCrmEnterpriseApiDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjCrmEnterpriseApiDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjCrmEnterpriseApiDao.deleteById(id);
    }

}