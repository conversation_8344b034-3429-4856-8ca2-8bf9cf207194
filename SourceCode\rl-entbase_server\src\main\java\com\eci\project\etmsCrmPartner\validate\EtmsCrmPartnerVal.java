package com.eci.project.etmsCrmPartner.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.etmsCrmPartner.entity.EtmsCrmPartnerEntity;

import org.springframework.stereotype.Service;


/**
* Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-16
*/
@Service
public class EtmsCrmPartnerVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(EtmsCrmPartnerEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(EtmsCrmPartnerEntity entity, BusinessType businessType) {

    }

}
