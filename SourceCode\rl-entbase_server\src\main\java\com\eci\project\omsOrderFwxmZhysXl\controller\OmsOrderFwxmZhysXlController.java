package com.eci.project.omsOrderFwxmZhysXl.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.omsOrderFwxmTmsXlXl.entity.OmsOrderFwxmTmsXlXlDTOEntity;
import com.eci.project.omsOrderFwxmZhysXl.entity.OmsOrderFwxmZhysXlDTOEntity;
import com.eci.project.omsOrderFwxmZhysXl.service.OmsOrderFwxmZhysXlService;
import com.eci.project.omsOrderFwxmZhysXl.entity.OmsOrderFwxmZhysXlEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 综合运输-线路Controller
*
* @<NAME_EMAIL>
* @date 2025-06-13
*/
@Api(tags = "综合运输-线路")
@RestController
@RequestMapping("/omsOrderFwxmZhysXl")
public class OmsOrderFwxmZhysXlController extends EciBaseController {

    @Autowired
    private OmsOrderFwxmZhysXlService omsOrderFwxmZhysXlService;


    @ApiOperation("综合运输-线路:保存")
    @EciLog(title = "综合运输-线路:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody OmsOrderFwxmZhysXlEntity entity){
        OmsOrderFwxmZhysXlEntity omsOrderFwxmZhysXlEntity =omsOrderFwxmZhysXlService.save(entity);
        return ResponseMsgUtil.success(10001,omsOrderFwxmZhysXlEntity);
    }


    @ApiOperation("综合运输-线路:查询列表")
    @EciLog(title = "综合运输-线路:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody OmsOrderFwxmZhysXlEntity entity){
        List<OmsOrderFwxmZhysXlEntity> omsOrderFwxmZhysXlEntities = omsOrderFwxmZhysXlService.selectList(entity);
        return ResponseMsgUtil.success(10001,omsOrderFwxmZhysXlEntities);
    }


    @ApiOperation("综合运输-线路:分页查询列表")
    @EciLog(title = "综合运输-线路:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody OmsOrderFwxmZhysXlEntity entity){
        TgPageInfo tgPageInfo = omsOrderFwxmZhysXlService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("综合运输-线路:根据ID查一条")
    @EciLog(title = "综合运输-线路:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody OmsOrderFwxmZhysXlEntity entity){
        OmsOrderFwxmZhysXlEntity  omsOrderFwxmZhysXlEntity = omsOrderFwxmZhysXlService.selectOneById(entity.getLineNo());
        return ResponseMsgUtil.success(10001,omsOrderFwxmZhysXlEntity);
    }


    @ApiOperation("综合运输-线路:根据ID删除一条")
    @EciLog(title = "综合运输-线路:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody OmsOrderFwxmZhysXlEntity entity){
        int count = omsOrderFwxmZhysXlService.deleteById(entity.getLineNo());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("综合运输-线路:根据ID字符串删除多条")
    @EciLog(title = "综合运输-线路:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody OmsOrderFwxmZhysXlEntity entity) {
        int count = omsOrderFwxmZhysXlService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }

    @ApiOperation("委托内容-程运序列:查询tab")
    @EciLog(title = "委托内容-程运序列:查询tab", businessType = BusinessType.SELECT)
    @PostMapping("/selectXlList")
    @EciAction()
    public ResponseMsg selectXlList(@RequestBody OmsOrderFwxmZhysXlDTOEntity entity){
        List<OmsOrderFwxmZhysXlDTOEntity> list = omsOrderFwxmZhysXlService.getOrderFwxmTmsXlSearch(entity);
        return ResponseMsgUtil.success(10001,list);
    }

}