package com.eci.project.omsOrderFw.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.omsOrderFw.service.OmsOrderFwService;
import com.eci.project.omsOrderFw.entity.OmsOrderFwEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 订单服务类型Controller
*
* @<NAME_EMAIL>
* @date 2025-04-24
*/
@Api(tags = "订单服务类型")
@RestController
@RequestMapping("/omsOrderFw")
public class OmsOrderFwController extends EciBaseController {

    @Autowired
    private OmsOrderFwService omsOrderFwService;


    @ApiOperation("订单服务类型:保存")
    @EciLog(title = "订单服务类型:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody OmsOrderFwEntity entity){
        OmsOrderFwEntity omsOrderFwEntity =omsOrderFwService.save(entity);
        return ResponseMsgUtil.success(10001,omsOrderFwEntity);
    }


    @ApiOperation("订单服务类型:查询列表")
    @EciLog(title = "订单服务类型:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody OmsOrderFwEntity entity){
        List<OmsOrderFwEntity> omsOrderFwEntities = omsOrderFwService.selectList(entity);
        return ResponseMsgUtil.success(10001,omsOrderFwEntities);
    }


    @ApiOperation("订单服务类型:分页查询列表")
    @EciLog(title = "订单服务类型:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody OmsOrderFwEntity entity){
        TgPageInfo tgPageInfo = omsOrderFwService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("订单服务类型:根据ID查一条")
    @EciLog(title = "订单服务类型:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody OmsOrderFwEntity entity){
        OmsOrderFwEntity  omsOrderFwEntity = omsOrderFwService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,omsOrderFwEntity);
    }


    @ApiOperation("订单服务类型:根据ID删除一条")
    @EciLog(title = "订单服务类型:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody OmsOrderFwEntity entity){
        int count = omsOrderFwService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("订单服务类型:根据ID字符串删除多条")
    @EciLog(title = "订单服务类型:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody OmsOrderFwEntity entity) {
        int count = omsOrderFwService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}