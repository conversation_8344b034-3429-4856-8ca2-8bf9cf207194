package com.eci.project.fzgjBdDistrict.controller;

import com.eci.common.ResponseMsgUtilX;
import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.project.fzgjBdDistrict.entity.FzgjBdDistrictRealtionEntity;
import com.eci.project.fzgjBdDistrict.service.IFzgjBdDistrictService;
import com.eci.project.fzgjBdDistrict.entity.FzgjBdDistrictEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.EciLog;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


/**
* 区县Controller
*
* @<NAME_EMAIL>
* @date 2025-03-17
*/
@Api(tags = "区县")
@RestController
@RequestMapping("/fzgjBdDistrict")
public class FzgjBdDistrictController extends EciBaseController {

    @Autowired
    private IFzgjBdDistrictService fzgjBdDistrictService;

    /**
    * 根据实体插入数据库
    * @param entity
    * @return
    */
    @ApiOperation("区县:保存")
    @EciLog(title = "区县:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public ResponseMsg save(@RequestBody FzgjBdDistrictEntity entity){
        return ResponseMsgUtil.success(10001,fzgjBdDistrictService.save(entity));
    }

    /**
    * 根据条件查询一个集合
    * @param entity
    * @return
    */
    @ApiOperation("区县:查询列表")
    @EciLog(title = "区县:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    public ResponseMsg selectList(@RequestBody FzgjBdDistrictEntity entity){
        return ResponseMsgUtil.success(10001,fzgjBdDistrictService.selectList(entity));
    }

    /**
    * 分页查询
    * @param entity
    * @return
    */
    @ApiOperation("区县:分页查询列表")
    @EciLog(title = "区县:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    public ResponseMsg selectPageList(@RequestBody FzgjBdDistrictEntity entity){
        return ResponseMsgUtilX.success(10001,fzgjBdDistrictService.queryPageList(entity));
    }

    /**
    * 根据ID查一条记录
    * @param entity
    * @return
    */
    @ApiOperation("区县:根据ID查一条")
    @EciLog(title = "区县:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    public ResponseMsg selectOneById(@RequestBody FzgjBdDistrictEntity entity){
        return ResponseMsgUtilX.success(10001,fzgjBdDistrictService.selectOneById(entity.getGuid()));
    }


    /**
    * 根据ID删除
    * @param entity
    * @return
    */
    @ApiOperation("区县:根据ID删除一条")
    @EciLog(title = "区县:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    public ResponseMsg deleteById(@RequestBody FzgjBdDistrictEntity entity){
        return ResponseMsgUtil.success(10001,fzgjBdDistrictService.deleteById(entity.getGuid()));
    }

    /**
    * 根据IDS删除
    *
    * @param entity
    * @return
    */
    @ApiOperation("区县:根据ID字符串删除多条")
    @EciLog(title = "区县:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    public ResponseMsg deleteByIds(@RequestBody FzgjBdDistrictEntity entity) {
        return ResponseMsgUtil.success(10001, fzgjBdDistrictService.deleteByIds(entity.getIds()));
    }


    /**
     * 区县所属关系分页查询
     * @param entity
     * @return
     */
    @ApiOperation("区县所属关系:分页查询列表")
    @EciLog(title = "区县所属关系:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectDistrictRealtionPageList")
    public ResponseMsg selectDistrictRealtionPageList(@RequestBody FzgjBdDistrictRealtionEntity entity){
        return ResponseMsgUtilX.success(10001,fzgjBdDistrictService.selectDistrictRealtionPageList(entity));
    }

}