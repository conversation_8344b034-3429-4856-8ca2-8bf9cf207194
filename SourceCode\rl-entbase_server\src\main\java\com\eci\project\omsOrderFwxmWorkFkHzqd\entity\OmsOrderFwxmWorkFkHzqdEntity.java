package com.eci.project.omsOrderFwxmWorkFkHzqd.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.common.DictField;
import com.eci.common.ZsrBaseEntity;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.math.BigDecimal;
import java.util.Date;


/**
* 反馈内容-核注清单对象 OMS_ORDER_FWXM_WORK_FK_HZQD
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-06-03
*/
@ApiModel("反馈内容-核注清单")
@TableName("OMS_ORDER_FWXM_WORK_FK_HZQD")
@FieldNameConstants
public class OmsOrderFwxmWorkFkHzqdEntity extends ZsrBaseEntity{
    /**
    * GUID
    */
    @ApiModelProperty("GUID(36)")
    @TableId("GUID")
    private String guid;

    /**
    * 订单号
    */
    @ApiModelProperty("订单号(20)")
    @TableField("ORDER_NO")
    private String orderNo;

    /**
    * 任务协助编号
    */
    @ApiModelProperty("任务协助编号(50)")
    @TableField("WORK_NO")
    private String workNo;

    /**
    * 服务项目
    */
    @ApiModelProperty("服务项目(20)")
    @TableField("FWXM_CODE")
    private String fwxmCode;

    /**
    * 核注清单编号
    */
    @ApiModelProperty("核注清单编号(20)")
    @TableField("CHECKBILL_NO")
    private String checkbillNo;

    /**
    * 核注清单表体行数
    */
    @ApiModelProperty("核注清单表体行数(22)")
    @TableField("CHECKBILL_TABLE_NUM")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal checkbillTableNum;

    /**
    * 检验检疫编号，报检号
    */
    @ApiModelProperty("检验检疫编号，报检号(20)")
    @TableField("JYJY_NO")
    private String jyjyNo;

    /**
    * 报关单号
    */
    @ApiModelProperty("报关单号(36)")
    @TableField("DEC_NO")
    private String decNo;

    /**
    * 报关单表体行数
    */
    @ApiModelProperty("报关单表体行数(22)")
    @TableField("DEC_TABLE_NUM")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal decTableNum;

    /**
    * 关联/协同报关单号
    */
    @ApiModelProperty("关联/协同报关单号(20)")
    @TableField("DEC_NO_XT")
    private String decNoXt;

    /**
    * 申报贸易方式
    */
    @ApiModelProperty("申报贸易方式(20)")
    @TableField("DEC_TRADE_MODE")
    @DictField(queryKey = "OMS_BD_TRADE")
    private String decTradeMode;

    /**
    * 件数
    */
    @ApiModelProperty("件数(22)")
    @TableField("PIECE")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal piece;

    /**
    * 单位
    */
    @ApiModelProperty("单位(10)")
    @TableField("UNIT")
    private String unit;

    /**
    * 重量/毛重
    */
    @ApiModelProperty("重量/毛重(22)")
    @TableField("WEIGHT")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal weight;

    /**
    * 协作委托单编号
    */
    @ApiModelProperty("协作委托单编号(36)")
    @TableField("XZWT_NO")
    private String xzwtNo;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 最后修改日期
    */
    @ApiModelProperty("最后修改日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("最后修改日期开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("最后修改日期结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(200)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 金额
    */
    @ApiModelProperty("金额(22)")
    @TableField("AMOUNT")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal amount;

    /**
    * 币制
    */
    @ApiModelProperty("币制(10)")
    @TableField("CURRENCY")
    @DictField(queryKey = "OMS_BD_CURRENCY")
    private String currency;

    /**
    * 申报日期
    */
    @ApiModelProperty("申报日期(7)")
    @TableField("D_DATE")
    private Date dDate;

    @ApiModelProperty("申报日期开始")
    @TableField(exist=false)
    private Date dDateStart;

    @ApiModelProperty("申报日期结束")
    @TableField(exist=false)
    private Date dDateEnd;

    /**
    * 删单标志YN
    */
    @ApiModelProperty("删单标志YN(1)")
    @TableField("IS_SD")
    private String isSd;

    /**
    * 改单标志YN
    */
    @ApiModelProperty("改单标志YN(1)")
    @TableField("IS_GD")
    private String isGd;

    /**
    * 重报报关单号
    */
    @ApiModelProperty("重报报关单号(20)")
    @TableField("DEC_NO_RE")
    private String decNoRe;

    /**
    * 改单次数
    */
    @ApiModelProperty("改单次数(22)")
    @TableField("GD_NUM")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal gdNum;

    /**
    * 删改单说明
    */
    @ApiModelProperty("删改单说明(2,000)")
    @TableField("SGD_MEMO")
    private String sgdMemo;

    /**
    * 业务数据唯一注册编号
    */
    @ApiModelProperty("业务数据唯一注册编号(50)")
    @TableField("BIZ_REG_ID")
    private String bizRegId;

    /**
    * 核扣日期
    */
    @ApiModelProperty("核扣日期(7)")
    @TableField("HK_DATE")
    private Date hkDate;

    @ApiModelProperty("核扣日期开始")
    @TableField(exist=false)
    private Date hkDateStart;

    @ApiModelProperty("核扣日期结束")
    @TableField(exist=false)
    private Date hkDateEnd;

    /**
    * 数量
    */
    @ApiModelProperty("数量(22)")
    @TableField("QTY")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal qty;

    /**
    * 品名
    */
    @ApiModelProperty("品名(500)")
    @TableField("PRODUCT_CODE")
    private String productCode;

    /**
    * 报检类别
    */
    @ApiModelProperty("报检类别(20)")
    @TableField("BJ_TYPE")
    private String bjType;

    /**
    * 运单号
    */
    @ApiModelProperty("运单号(100)")
    @TableField("YDH")
    private String ydh;

    /**
    * 分单号
    */
    @ApiModelProperty("分单号(100)")
    @TableField("FDH")
    private String fdh;

    /**
    * 箱单号
    */
    @ApiModelProperty("箱单号(100)")
    @TableField("XDH")
    private String xdh;

    /**
    * 报关单张数
    */
    @ApiModelProperty("报关单张数(22)")
    @TableField("BGD_ZS")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal bgdZs;

    /**
    * 转关单号
    */
    @ApiModelProperty("转关单号(100)")
    @TableField("ZGDH")
    private String zgdh;

    /**
    * 出入库单号
    */
    @ApiModelProperty("出入库单号(100)")
    @TableField("CRKDH")
    private String crkdh;

    /**
    * 报关单1
    */
    @ApiModelProperty("报关单1(20)")
    @TableField("BGD1")
    private String bgd1;

    /**
    * 报关单1类型
    */
    @ApiModelProperty("报关单1类型(10)")
    @TableField("BGD1_TYPE")
    private String bgd1Type;

    /**
    * 报关单2
    */
    @ApiModelProperty("报关单2(20)")
    @TableField("BGD2")
    private String bgd2;

    /**
    * 报关单2类型
    */
    @ApiModelProperty("报关单2类型(10)")
    @TableField("BGD2_TYPE")
    private String bgd2Type;

    /**
    * 备注
    */
    @ApiModelProperty("备注(2,000)")
    @TableField("REMARK")
    private String remark;

    /**
    * 删除单号
    */
    @ApiModelProperty("删除单号(50)")
    @TableField("SD_NO")
    private String sdNo;

    /**
    * 改单单号
    */
    @ApiModelProperty("改单单号(50)")
    @TableField("GD_NO")
    private String gdNo;

    /**
    * 修改前内容
    */
    @ApiModelProperty("修改前内容(2,000)")
    @TableField("COMMENT_PRE")
    private String commentPre;

    /**
    * 修改后内容
    */
    @ApiModelProperty("修改后内容(2,000)")
    @TableField("COMMENT_NOW")
    private String commentNow;

    /**
    * 合同号
    */
    @ApiModelProperty("合同号(100)")
    @TableField("CONTRACT_NO")
    private String contractNo;

    /**
    * 起运国/运抵国
    */
    @ApiModelProperty("起运国/运抵国(100)")
    @TableField("ORIGIN_ARRIVAL_COUNTRY")
    @DictField(queryKey = "OMS_BD_COUNTRY_TG")
    private String originArrivalCountry;

    /**
    * 集装箱号
    */
    @ApiModelProperty("集装箱号(100)")
    @TableField("CONTAINER_NO")
    private String containerNo;

    /**
    * 监管方式
    */
    @ApiModelProperty("监管方式(100)")
    @TableField("SUPERVISION_MODE")
    @DictField(queryKey = "OMS_BD_TRADE")
    private String supervisionMode;

    /**
    * 是否查验
    */
    @ApiModelProperty("是否查验(2)")
    @TableField("IS_CHECK")
    private String isCheck;

    /**
    * 核注清单申报日期
    */
    @ApiModelProperty("核注清单申报日期(7)")
    @TableField("HZQD_DATE")
    private Date hzqdDate;

    @ApiModelProperty("核注清单申报日期开始")
    @TableField(exist=false)
    private Date hzqdDateStart;

    @ApiModelProperty("核注清单申报日期结束")
    @TableField(exist=false)
    private Date hzqdDateEnd;

    /**
    * 查验类型
    */
    @ApiModelProperty("查验类型(20)")
    @TableField("CYLX")
    private String cylx;

    /**
    * 进出标志：I进E出
    */
    @ApiModelProperty("进出标志：I进E出(10)")
    @TableField("I_E_TYPE")
    private String iEType;

    /**
    * 进/出境关别
    */
    @ApiModelProperty("进/出境关别(10)")
    @TableField("I_E_PORT")
    @DictField(queryKey = "OMS_BD_CUSTOMS")
    private String iEPort;

    /**
    * 申报地海关/主管关区
    */
    @ApiModelProperty("申报地海关/主管关区(10)")
    @TableField("CUSTOM_CODE")
    @DictField(queryKey = "OMS_BD_CUSTOMS")
    private String customCode;

    /**
    * 经营单位
    */
    @ApiModelProperty("经营单位(20)")
    @TableField("TRADE_CODE")
    @DictField(queryKey = "CRM_CUSTOMER_SFHF")
    private String tradeCode;

    /**
    * 运输方式
    */
    @ApiModelProperty("运输方式(10)")
    @TableField("YSFS")
    @DictField(queryKey = "OMS_BD_YSFS")
    private String ysfs;

    /**
    * 净重
    */
    @ApiModelProperty("净重(22)")
    @TableField("NET_WEIGHT")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal netWeight;

    /**
    * 境内收发货人、区外企业
    */
    @ApiModelProperty("境内收发货人、区外企业(100)")
    @TableField("JNSFHR")
    @DictField(queryKey = "CRM_CUSTOMER_SFHF")
    private String jnsfhr;

    /**
    * 是否检验检疫
    */
    @ApiModelProperty("是否检验检疫(2)")
    @TableField("IS_JYJY")
    private String isJyjy;

    /**
    * 包装种类
    */
    @ApiModelProperty("包装种类(10)")
    @TableField("PACK_TYPE")
    @DictField(queryKey = "OMS_GOODS_PACK_TYPE")
    private String packType;

    /**
    * 联单数
    */
    @ApiModelProperty("联单数(22)")
    @TableField("LDS")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal lds;

    /**
    * 签收人
    */
    @ApiModelProperty("签收人(20)")
    @TableField("QS_MAN")
    private String qsMan;

    /**
    * 签收时间
    */
    @ApiModelProperty("签收时间(7)")
    @TableField("QS_DATE")
    private Date qsDate;

    @ApiModelProperty("签收时间开始")
    @TableField(exist=false)
    private Date qsDateStart;

    @ApiModelProperty("签收时间结束")
    @TableField(exist=false)
    private Date qsDateEnd;

    /**
    * 制单人
    */
    @ApiModelProperty("制单人(20)")
    @TableField("BILL_MAN")
    @DictField(queryKey = "OMS_SSO_USER")
    private String billMan;

    /**
    * 贸易国
    */
    @ApiModelProperty("贸易国(50)")
    @TableField("TRADE_COUNTRY")
    @DictField(queryKey = "OMS_BD_COUNTRY_TG")
    private String tradeCountry;

    /**
    * 制单备注
    */
    @ApiModelProperty("制单备注(255)")
    @TableField("BGD_MEMO")
    private String bgdMemo;

    /**
    * 金额币制
    */
    @ApiModelProperty("金额币制(200)")
    @TableField("AMOUNT_CURRENCY")
    private String amountCurrency;

    /**
    * 是否报检
    */
    @ApiModelProperty("是否报检(1)")
    @TableField("IS_BJ")
    private String isBj;

    /**
    * HS编码
    */
    @ApiModelProperty("HS编码(700)")
    @TableField("HS_CODE")
    private String hsCode;

    /**
    * 运输模式、报关标志（报关/非报关）
    */
    @ApiModelProperty("运输模式、报关标志（报关/非报关）(20)")
    @TableField("YS_TYPE")
    private String ysType;

    /**
    * 企业报关类型
    */
    @ApiModelProperty("企业报关类型(20)")
    @TableField("BGD_BG_TYPE")
    private String bgdBgType;

    /**
    * 货代/厂商
    */
    @ApiModelProperty("货代/厂商(100)")
    @TableField("HDCS")
    private String hdcs;

    /**
    * 制单日期
    */
    @ApiModelProperty("制单日期(7)")
    @TableField("BILL_DATE")
    private Date billDate;

    @ApiModelProperty("制单日期开始")
    @TableField(exist=false)
    private Date billDateStart;

    @ApiModelProperty("制单日期结束")
    @TableField(exist=false)
    private Date billDateEnd;

    /**
    * 接单日期
    */
    @ApiModelProperty("接单日期(7)")
    @TableField("JD_DATE")
    private Date jdDate;

    @ApiModelProperty("接单日期开始")
    @TableField(exist=false)
    private Date jdDateStart;

    @ApiModelProperty("接单日期结束")
    @TableField(exist=false)
    private Date jdDateEnd;

    /**
    * 报关标志
    */
    @ApiModelProperty("报关标志(20)")
    @TableField("BG_FLAG")
    private String bgFlag;

    /**
    * (核注清单)报关类型
    */
    @ApiModelProperty("(核注清单)报关类型(20)")
    @TableField("HZQD_BG_TYPE")
    private String hzqdBgType;

    /**
    * 区外企业
    */
    @ApiModelProperty("区外企业(20)")
    @TableField("TRADE_CODE_OUT")
    private String tradeCodeOut;

    /**
    * 金额(人民币)
    */
    @ApiModelProperty("金额(人民币)(22)")
    @TableField("AMOUNT_CNY")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal amountCny;

    /**
    * 目的地检验检疫机关
    */
    @ApiModelProperty("目的地检验检疫机关(10)")
    @TableField("PURPORGCODE")
    private String purporgcode;

    /**
    * 国外报关单号
    */
    @ApiModelProperty("国外报关单号(50)")
    @TableField("DEC_NO_GW")
    private String decNoGw;

    /**
    * 报关单1核验结果
    */
    @ApiModelProperty("报关单1核验结果(2)")
    @TableField("BGD1_RESULT")
    private String bgd1Result;

    /**
    * 报关单2核验结果
    */
    @ApiModelProperty("报关单2核验结果(2)")
    @TableField("BGD2_RESULT")
    private String bgd2Result;

    @ApiModelProperty("(1,000)")
    @TableField("BGD1_FILENAME")
    private String bgd1Filename;

    @ApiModelProperty("(4,000)")
    @TableField("BGD1_FILECONTENT")
    private String bgd1Filecontent;

    @ApiModelProperty("(1,000)")
    @TableField("BGD2_FILENAME")
    private String bgd2Filename;

    @ApiModelProperty("(4,000)")
    @TableField("BGD2_FILECONTENT")
    private String bgd2Filecontent;

    @ApiModelProperty("(10)")
    @TableField("BGD1_BUSINESS")
    private String bgd1Business;

    @ApiModelProperty("(10)")
    @TableField("BGD1_IEFLAG")
    private String bgd1Ieflag;

    @ApiModelProperty("(10)")
    @TableField("BGD2_BUSINESS")
    private String bgd2Business;

    @ApiModelProperty("(10)")
    @TableField("BGD2_IEFLAG")
    private String bgd2Ieflag;

    @ApiModelProperty("(1,000)")
    @TableField("YSAMOUNT")
    private String ysamount;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public OmsOrderFwxmWorkFkHzqdEntity() {
        this.setSubClazz(OmsOrderFwxmWorkFkHzqdEntity.class);
    }

    public OmsOrderFwxmWorkFkHzqdEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        this.nodifySetFiled("orderNo", orderNo);
        return this;
    }

    public String getOrderNo() {
        this.nodifyGetFiled("orderNo");
        return orderNo;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setWorkNo(String workNo) {
        this.workNo = workNo;
        this.nodifySetFiled("workNo", workNo);
        return this;
    }

    public String getWorkNo() {
        this.nodifyGetFiled("workNo");
        return workNo;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setFwxmCode(String fwxmCode) {
        this.fwxmCode = fwxmCode;
        this.nodifySetFiled("fwxmCode", fwxmCode);
        return this;
    }

    public String getFwxmCode() {
        this.nodifyGetFiled("fwxmCode");
        return fwxmCode;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setCheckbillNo(String checkbillNo) {
        this.checkbillNo = checkbillNo;
        this.nodifySetFiled("checkbillNo", checkbillNo);
        return this;
    }

    public String getCheckbillNo() {
        this.nodifyGetFiled("checkbillNo");
        return checkbillNo;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setCheckbillTableNum(BigDecimal checkbillTableNum) {
        this.checkbillTableNum = checkbillTableNum;
        this.nodifySetFiled("checkbillTableNum", checkbillTableNum);
        return this;
    }

    public BigDecimal getCheckbillTableNum() {
        this.nodifyGetFiled("checkbillTableNum");
        return checkbillTableNum;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setJyjyNo(String jyjyNo) {
        this.jyjyNo = jyjyNo;
        this.nodifySetFiled("jyjyNo", jyjyNo);
        return this;
    }

    public String getJyjyNo() {
        this.nodifyGetFiled("jyjyNo");
        return jyjyNo;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setDecNo(String decNo) {
        this.decNo = decNo;
        this.nodifySetFiled("decNo", decNo);
        return this;
    }

    public String getDecNo() {
        this.nodifyGetFiled("decNo");
        return decNo;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setDecTableNum(BigDecimal decTableNum) {
        this.decTableNum = decTableNum;
        this.nodifySetFiled("decTableNum", decTableNum);
        return this;
    }

    public BigDecimal getDecTableNum() {
        this.nodifyGetFiled("decTableNum");
        return decTableNum;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setDecNoXt(String decNoXt) {
        this.decNoXt = decNoXt;
        this.nodifySetFiled("decNoXt", decNoXt);
        return this;
    }

    public String getDecNoXt() {
        this.nodifyGetFiled("decNoXt");
        return decNoXt;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setDecTradeMode(String decTradeMode) {
        this.decTradeMode = decTradeMode;
        this.nodifySetFiled("decTradeMode", decTradeMode);
        return this;
    }

    public String getDecTradeMode() {
        this.nodifyGetFiled("decTradeMode");
        return decTradeMode;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setPiece(BigDecimal piece) {
        this.piece = piece;
        this.nodifySetFiled("piece", piece);
        return this;
    }

    public BigDecimal getPiece() {
        this.nodifyGetFiled("piece");
        return piece;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setUnit(String unit) {
        this.unit = unit;
        this.nodifySetFiled("unit", unit);
        return this;
    }

    public String getUnit() {
        this.nodifyGetFiled("unit");
        return unit;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setWeight(BigDecimal weight) {
        this.weight = weight;
        this.nodifySetFiled("weight", weight);
        return this;
    }

    public BigDecimal getWeight() {
        this.nodifyGetFiled("weight");
        return weight;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setXzwtNo(String xzwtNo) {
        this.xzwtNo = xzwtNo;
        this.nodifySetFiled("xzwtNo", xzwtNo);
        return this;
    }

    public String getXzwtNo() {
        this.nodifyGetFiled("xzwtNo");
        return xzwtNo;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public OmsOrderFwxmWorkFkHzqdEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public OmsOrderFwxmWorkFkHzqdEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setAmount(BigDecimal amount) {
        this.amount = amount;
        this.nodifySetFiled("amount", amount);
        return this;
    }

    public BigDecimal getAmount() {
        this.nodifyGetFiled("amount");
        return amount;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setCurrency(String currency) {
        this.currency = currency;
        this.nodifySetFiled("currency", currency);
        return this;
    }

    public String getCurrency() {
        this.nodifyGetFiled("currency");
        return currency;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setdDate(Date dDate) {
        this.dDate = dDate;
        this.nodifySetFiled("dDate", dDate);
        return this;
    }

    public Date getdDate() {
        this.nodifyGetFiled("dDate");
        return dDate;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setdDateStart(Date dDateStart) {
        this.dDateStart = dDateStart;
        this.nodifySetFiled("dDateStart", dDateStart);
        return this;
    }

    public Date getdDateStart() {
        this.nodifyGetFiled("dDateStart");
        return dDateStart;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setdDateEnd(Date dDateEnd) {
        this.dDateEnd = dDateEnd;
        this.nodifySetFiled("dDateEnd", dDateEnd);
        return this;
    }

    public Date getdDateEnd() {
        this.nodifyGetFiled("dDateEnd");
        return dDateEnd;
    }
    public OmsOrderFwxmWorkFkHzqdEntity setIsSd(String isSd) {
        this.isSd = isSd;
        this.nodifySetFiled("isSd", isSd);
        return this;
    }

    public String getIsSd() {
        this.nodifyGetFiled("isSd");
        return isSd;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setIsGd(String isGd) {
        this.isGd = isGd;
        this.nodifySetFiled("isGd", isGd);
        return this;
    }

    public String getIsGd() {
        this.nodifyGetFiled("isGd");
        return isGd;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setDecNoRe(String decNoRe) {
        this.decNoRe = decNoRe;
        this.nodifySetFiled("decNoRe", decNoRe);
        return this;
    }

    public String getDecNoRe() {
        this.nodifyGetFiled("decNoRe");
        return decNoRe;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setGdNum(BigDecimal gdNum) {
        this.gdNum = gdNum;
        this.nodifySetFiled("gdNum", gdNum);
        return this;
    }

    public BigDecimal getGdNum() {
        this.nodifyGetFiled("gdNum");
        return gdNum;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setSgdMemo(String sgdMemo) {
        this.sgdMemo = sgdMemo;
        this.nodifySetFiled("sgdMemo", sgdMemo);
        return this;
    }

    public String getSgdMemo() {
        this.nodifyGetFiled("sgdMemo");
        return sgdMemo;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setBizRegId(String bizRegId) {
        this.bizRegId = bizRegId;
        this.nodifySetFiled("bizRegId", bizRegId);
        return this;
    }

    public String getBizRegId() {
        this.nodifyGetFiled("bizRegId");
        return bizRegId;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setHkDate(Date hkDate) {
        this.hkDate = hkDate;
        this.nodifySetFiled("hkDate", hkDate);
        return this;
    }

    public Date getHkDate() {
        this.nodifyGetFiled("hkDate");
        return hkDate;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setHkDateStart(Date hkDateStart) {
        this.hkDateStart = hkDateStart;
        this.nodifySetFiled("hkDateStart", hkDateStart);
        return this;
    }

    public Date getHkDateStart() {
        this.nodifyGetFiled("hkDateStart");
        return hkDateStart;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setHkDateEnd(Date hkDateEnd) {
        this.hkDateEnd = hkDateEnd;
        this.nodifySetFiled("hkDateEnd", hkDateEnd);
        return this;
    }

    public Date getHkDateEnd() {
        this.nodifyGetFiled("hkDateEnd");
        return hkDateEnd;
    }
    public OmsOrderFwxmWorkFkHzqdEntity setQty(BigDecimal qty) {
        this.qty = qty;
        this.nodifySetFiled("qty", qty);
        return this;
    }

    public BigDecimal getQty() {
        this.nodifyGetFiled("qty");
        return qty;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setProductCode(String productCode) {
        this.productCode = productCode;
        this.nodifySetFiled("productCode", productCode);
        return this;
    }

    public String getProductCode() {
        this.nodifyGetFiled("productCode");
        return productCode;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setBjType(String bjType) {
        this.bjType = bjType;
        this.nodifySetFiled("bjType", bjType);
        return this;
    }

    public String getBjType() {
        this.nodifyGetFiled("bjType");
        return bjType;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setYdh(String ydh) {
        this.ydh = ydh;
        this.nodifySetFiled("ydh", ydh);
        return this;
    }

    public String getYdh() {
        this.nodifyGetFiled("ydh");
        return ydh;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setFdh(String fdh) {
        this.fdh = fdh;
        this.nodifySetFiled("fdh", fdh);
        return this;
    }

    public String getFdh() {
        this.nodifyGetFiled("fdh");
        return fdh;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setXdh(String xdh) {
        this.xdh = xdh;
        this.nodifySetFiled("xdh", xdh);
        return this;
    }

    public String getXdh() {
        this.nodifyGetFiled("xdh");
        return xdh;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setBgdZs(BigDecimal bgdZs) {
        this.bgdZs = bgdZs;
        this.nodifySetFiled("bgdZs", bgdZs);
        return this;
    }

    public BigDecimal getBgdZs() {
        this.nodifyGetFiled("bgdZs");
        return bgdZs;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setZgdh(String zgdh) {
        this.zgdh = zgdh;
        this.nodifySetFiled("zgdh", zgdh);
        return this;
    }

    public String getZgdh() {
        this.nodifyGetFiled("zgdh");
        return zgdh;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setCrkdh(String crkdh) {
        this.crkdh = crkdh;
        this.nodifySetFiled("crkdh", crkdh);
        return this;
    }

    public String getCrkdh() {
        this.nodifyGetFiled("crkdh");
        return crkdh;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setBgd1(String bgd1) {
        this.bgd1 = bgd1;
        this.nodifySetFiled("bgd1", bgd1);
        return this;
    }

    public String getBgd1() {
        this.nodifyGetFiled("bgd1");
        return bgd1;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setBgd1Type(String bgd1Type) {
        this.bgd1Type = bgd1Type;
        this.nodifySetFiled("bgd1Type", bgd1Type);
        return this;
    }

    public String getBgd1Type() {
        this.nodifyGetFiled("bgd1Type");
        return bgd1Type;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setBgd2(String bgd2) {
        this.bgd2 = bgd2;
        this.nodifySetFiled("bgd2", bgd2);
        return this;
    }

    public String getBgd2() {
        this.nodifyGetFiled("bgd2");
        return bgd2;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setBgd2Type(String bgd2Type) {
        this.bgd2Type = bgd2Type;
        this.nodifySetFiled("bgd2Type", bgd2Type);
        return this;
    }

    public String getBgd2Type() {
        this.nodifyGetFiled("bgd2Type");
        return bgd2Type;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setRemark(String remark) {
        this.remark = remark;
        this.nodifySetFiled("remark", remark);
        return this;
    }

    public String getRemark() {
        this.nodifyGetFiled("remark");
        return remark;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setSdNo(String sdNo) {
        this.sdNo = sdNo;
        this.nodifySetFiled("sdNo", sdNo);
        return this;
    }

    public String getSdNo() {
        this.nodifyGetFiled("sdNo");
        return sdNo;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setGdNo(String gdNo) {
        this.gdNo = gdNo;
        this.nodifySetFiled("gdNo", gdNo);
        return this;
    }

    public String getGdNo() {
        this.nodifyGetFiled("gdNo");
        return gdNo;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setCommentPre(String commentPre) {
        this.commentPre = commentPre;
        this.nodifySetFiled("commentPre", commentPre);
        return this;
    }

    public String getCommentPre() {
        this.nodifyGetFiled("commentPre");
        return commentPre;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setCommentNow(String commentNow) {
        this.commentNow = commentNow;
        this.nodifySetFiled("commentNow", commentNow);
        return this;
    }

    public String getCommentNow() {
        this.nodifyGetFiled("commentNow");
        return commentNow;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setContractNo(String contractNo) {
        this.contractNo = contractNo;
        this.nodifySetFiled("contractNo", contractNo);
        return this;
    }

    public String getContractNo() {
        this.nodifyGetFiled("contractNo");
        return contractNo;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setOriginArrivalCountry(String originArrivalCountry) {
        this.originArrivalCountry = originArrivalCountry;
        this.nodifySetFiled("originArrivalCountry", originArrivalCountry);
        return this;
    }

    public String getOriginArrivalCountry() {
        this.nodifyGetFiled("originArrivalCountry");
        return originArrivalCountry;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setContainerNo(String containerNo) {
        this.containerNo = containerNo;
        this.nodifySetFiled("containerNo", containerNo);
        return this;
    }

    public String getContainerNo() {
        this.nodifyGetFiled("containerNo");
        return containerNo;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setSupervisionMode(String supervisionMode) {
        this.supervisionMode = supervisionMode;
        this.nodifySetFiled("supervisionMode", supervisionMode);
        return this;
    }

    public String getSupervisionMode() {
        this.nodifyGetFiled("supervisionMode");
        return supervisionMode;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setIsCheck(String isCheck) {
        this.isCheck = isCheck;
        this.nodifySetFiled("isCheck", isCheck);
        return this;
    }

    public String getIsCheck() {
        this.nodifyGetFiled("isCheck");
        return isCheck;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setHzqdDate(Date hzqdDate) {
        this.hzqdDate = hzqdDate;
        this.nodifySetFiled("hzqdDate", hzqdDate);
        return this;
    }

    public Date getHzqdDate() {
        this.nodifyGetFiled("hzqdDate");
        return hzqdDate;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setHzqdDateStart(Date hzqdDateStart) {
        this.hzqdDateStart = hzqdDateStart;
        this.nodifySetFiled("hzqdDateStart", hzqdDateStart);
        return this;
    }

    public Date getHzqdDateStart() {
        this.nodifyGetFiled("hzqdDateStart");
        return hzqdDateStart;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setHzqdDateEnd(Date hzqdDateEnd) {
        this.hzqdDateEnd = hzqdDateEnd;
        this.nodifySetFiled("hzqdDateEnd", hzqdDateEnd);
        return this;
    }

    public Date getHzqdDateEnd() {
        this.nodifyGetFiled("hzqdDateEnd");
        return hzqdDateEnd;
    }
    public OmsOrderFwxmWorkFkHzqdEntity setCylx(String cylx) {
        this.cylx = cylx;
        this.nodifySetFiled("cylx", cylx);
        return this;
    }

    public String getCylx() {
        this.nodifyGetFiled("cylx");
        return cylx;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setiEType(String iEType) {
        this.iEType = iEType;
        this.nodifySetFiled("iEType", iEType);
        return this;
    }

    public String getiEType() {
        this.nodifyGetFiled("iEType");
        return iEType;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setiEPort(String iEPort) {
        this.iEPort = iEPort;
        this.nodifySetFiled("iEPort", iEPort);
        return this;
    }

    public String getiEPort() {
        this.nodifyGetFiled("iEPort");
        return iEPort;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setCustomCode(String customCode) {
        this.customCode = customCode;
        this.nodifySetFiled("customCode", customCode);
        return this;
    }

    public String getCustomCode() {
        this.nodifyGetFiled("customCode");
        return customCode;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setTradeCode(String tradeCode) {
        this.tradeCode = tradeCode;
        this.nodifySetFiled("tradeCode", tradeCode);
        return this;
    }

    public String getTradeCode() {
        this.nodifyGetFiled("tradeCode");
        return tradeCode;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setYsfs(String ysfs) {
        this.ysfs = ysfs;
        this.nodifySetFiled("ysfs", ysfs);
        return this;
    }

    public String getYsfs() {
        this.nodifyGetFiled("ysfs");
        return ysfs;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setNetWeight(BigDecimal netWeight) {
        this.netWeight = netWeight;
        this.nodifySetFiled("netWeight", netWeight);
        return this;
    }

    public BigDecimal getNetWeight() {
        this.nodifyGetFiled("netWeight");
        return netWeight;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setJnsfhr(String jnsfhr) {
        this.jnsfhr = jnsfhr;
        this.nodifySetFiled("jnsfhr", jnsfhr);
        return this;
    }

    public String getJnsfhr() {
        this.nodifyGetFiled("jnsfhr");
        return jnsfhr;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setIsJyjy(String isJyjy) {
        this.isJyjy = isJyjy;
        this.nodifySetFiled("isJyjy", isJyjy);
        return this;
    }

    public String getIsJyjy() {
        this.nodifyGetFiled("isJyjy");
        return isJyjy;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setPackType(String packType) {
        this.packType = packType;
        this.nodifySetFiled("packType", packType);
        return this;
    }

    public String getPackType() {
        this.nodifyGetFiled("packType");
        return packType;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setLds(BigDecimal lds) {
        this.lds = lds;
        this.nodifySetFiled("lds", lds);
        return this;
    }

    public BigDecimal getLds() {
        this.nodifyGetFiled("lds");
        return lds;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setQsMan(String qsMan) {
        this.qsMan = qsMan;
        this.nodifySetFiled("qsMan", qsMan);
        return this;
    }

    public String getQsMan() {
        this.nodifyGetFiled("qsMan");
        return qsMan;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setQsDate(Date qsDate) {
        this.qsDate = qsDate;
        this.nodifySetFiled("qsDate", qsDate);
        return this;
    }

    public Date getQsDate() {
        this.nodifyGetFiled("qsDate");
        return qsDate;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setQsDateStart(Date qsDateStart) {
        this.qsDateStart = qsDateStart;
        this.nodifySetFiled("qsDateStart", qsDateStart);
        return this;
    }

    public Date getQsDateStart() {
        this.nodifyGetFiled("qsDateStart");
        return qsDateStart;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setQsDateEnd(Date qsDateEnd) {
        this.qsDateEnd = qsDateEnd;
        this.nodifySetFiled("qsDateEnd", qsDateEnd);
        return this;
    }

    public Date getQsDateEnd() {
        this.nodifyGetFiled("qsDateEnd");
        return qsDateEnd;
    }
    public OmsOrderFwxmWorkFkHzqdEntity setBillMan(String billMan) {
        this.billMan = billMan;
        this.nodifySetFiled("billMan", billMan);
        return this;
    }

    public String getBillMan() {
        this.nodifyGetFiled("billMan");
        return billMan;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setTradeCountry(String tradeCountry) {
        this.tradeCountry = tradeCountry;
        this.nodifySetFiled("tradeCountry", tradeCountry);
        return this;
    }

    public String getTradeCountry() {
        this.nodifyGetFiled("tradeCountry");
        return tradeCountry;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setBgdMemo(String bgdMemo) {
        this.bgdMemo = bgdMemo;
        this.nodifySetFiled("bgdMemo", bgdMemo);
        return this;
    }

    public String getBgdMemo() {
        this.nodifyGetFiled("bgdMemo");
        return bgdMemo;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setAmountCurrency(String amountCurrency) {
        this.amountCurrency = amountCurrency;
        this.nodifySetFiled("amountCurrency", amountCurrency);
        return this;
    }

    public String getAmountCurrency() {
        this.nodifyGetFiled("amountCurrency");
        return amountCurrency;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setIsBj(String isBj) {
        this.isBj = isBj;
        this.nodifySetFiled("isBj", isBj);
        return this;
    }

    public String getIsBj() {
        this.nodifyGetFiled("isBj");
        return isBj;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setHsCode(String hsCode) {
        this.hsCode = hsCode;
        this.nodifySetFiled("hsCode", hsCode);
        return this;
    }

    public String getHsCode() {
        this.nodifyGetFiled("hsCode");
        return hsCode;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setYsType(String ysType) {
        this.ysType = ysType;
        this.nodifySetFiled("ysType", ysType);
        return this;
    }

    public String getYsType() {
        this.nodifyGetFiled("ysType");
        return ysType;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setBgdBgType(String bgdBgType) {
        this.bgdBgType = bgdBgType;
        this.nodifySetFiled("bgdBgType", bgdBgType);
        return this;
    }

    public String getBgdBgType() {
        this.nodifyGetFiled("bgdBgType");
        return bgdBgType;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setHdcs(String hdcs) {
        this.hdcs = hdcs;
        this.nodifySetFiled("hdcs", hdcs);
        return this;
    }

    public String getHdcs() {
        this.nodifyGetFiled("hdcs");
        return hdcs;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setBillDate(Date billDate) {
        this.billDate = billDate;
        this.nodifySetFiled("billDate", billDate);
        return this;
    }

    public Date getBillDate() {
        this.nodifyGetFiled("billDate");
        return billDate;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setBillDateStart(Date billDateStart) {
        this.billDateStart = billDateStart;
        this.nodifySetFiled("billDateStart", billDateStart);
        return this;
    }

    public Date getBillDateStart() {
        this.nodifyGetFiled("billDateStart");
        return billDateStart;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setBillDateEnd(Date billDateEnd) {
        this.billDateEnd = billDateEnd;
        this.nodifySetFiled("billDateEnd", billDateEnd);
        return this;
    }

    public Date getBillDateEnd() {
        this.nodifyGetFiled("billDateEnd");
        return billDateEnd;
    }
    public OmsOrderFwxmWorkFkHzqdEntity setJdDate(Date jdDate) {
        this.jdDate = jdDate;
        this.nodifySetFiled("jdDate", jdDate);
        return this;
    }

    public Date getJdDate() {
        this.nodifyGetFiled("jdDate");
        return jdDate;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setJdDateStart(Date jdDateStart) {
        this.jdDateStart = jdDateStart;
        this.nodifySetFiled("jdDateStart", jdDateStart);
        return this;
    }

    public Date getJdDateStart() {
        this.nodifyGetFiled("jdDateStart");
        return jdDateStart;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setJdDateEnd(Date jdDateEnd) {
        this.jdDateEnd = jdDateEnd;
        this.nodifySetFiled("jdDateEnd", jdDateEnd);
        return this;
    }

    public Date getJdDateEnd() {
        this.nodifyGetFiled("jdDateEnd");
        return jdDateEnd;
    }
    public OmsOrderFwxmWorkFkHzqdEntity setBgFlag(String bgFlag) {
        this.bgFlag = bgFlag;
        this.nodifySetFiled("bgFlag", bgFlag);
        return this;
    }

    public String getBgFlag() {
        this.nodifyGetFiled("bgFlag");
        return bgFlag;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setHzqdBgType(String hzqdBgType) {
        this.hzqdBgType = hzqdBgType;
        this.nodifySetFiled("hzqdBgType", hzqdBgType);
        return this;
    }

    public String getHzqdBgType() {
        this.nodifyGetFiled("hzqdBgType");
        return hzqdBgType;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setTradeCodeOut(String tradeCodeOut) {
        this.tradeCodeOut = tradeCodeOut;
        this.nodifySetFiled("tradeCodeOut", tradeCodeOut);
        return this;
    }

    public String getTradeCodeOut() {
        this.nodifyGetFiled("tradeCodeOut");
        return tradeCodeOut;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setAmountCny(BigDecimal amountCny) {
        this.amountCny = amountCny;
        this.nodifySetFiled("amountCny", amountCny);
        return this;
    }

    public BigDecimal getAmountCny() {
        this.nodifyGetFiled("amountCny");
        return amountCny;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setPurporgcode(String purporgcode) {
        this.purporgcode = purporgcode;
        this.nodifySetFiled("purporgcode", purporgcode);
        return this;
    }

    public String getPurporgcode() {
        this.nodifyGetFiled("purporgcode");
        return purporgcode;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setDecNoGw(String decNoGw) {
        this.decNoGw = decNoGw;
        this.nodifySetFiled("decNoGw", decNoGw);
        return this;
    }

    public String getDecNoGw() {
        this.nodifyGetFiled("decNoGw");
        return decNoGw;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setBgd1Result(String bgd1Result) {
        this.bgd1Result = bgd1Result;
        this.nodifySetFiled("bgd1Result", bgd1Result);
        return this;
    }

    public String getBgd1Result() {
        this.nodifyGetFiled("bgd1Result");
        return bgd1Result;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setBgd2Result(String bgd2Result) {
        this.bgd2Result = bgd2Result;
        this.nodifySetFiled("bgd2Result", bgd2Result);
        return this;
    }

    public String getBgd2Result() {
        this.nodifyGetFiled("bgd2Result");
        return bgd2Result;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setBgd1Filename(String bgd1Filename) {
        this.bgd1Filename = bgd1Filename;
        this.nodifySetFiled("bgd1Filename", bgd1Filename);
        return this;
    }

    public String getBgd1Filename() {
        this.nodifyGetFiled("bgd1Filename");
        return bgd1Filename;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setBgd1Filecontent(String bgd1Filecontent) {
        this.bgd1Filecontent = bgd1Filecontent;
        this.nodifySetFiled("bgd1Filecontent", bgd1Filecontent);
        return this;
    }

    public String getBgd1Filecontent() {
        this.nodifyGetFiled("bgd1Filecontent");
        return bgd1Filecontent;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setBgd2Filename(String bgd2Filename) {
        this.bgd2Filename = bgd2Filename;
        this.nodifySetFiled("bgd2Filename", bgd2Filename);
        return this;
    }

    public String getBgd2Filename() {
        this.nodifyGetFiled("bgd2Filename");
        return bgd2Filename;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setBgd2Filecontent(String bgd2Filecontent) {
        this.bgd2Filecontent = bgd2Filecontent;
        this.nodifySetFiled("bgd2Filecontent", bgd2Filecontent);
        return this;
    }

    public String getBgd2Filecontent() {
        this.nodifyGetFiled("bgd2Filecontent");
        return bgd2Filecontent;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setBgd1Business(String bgd1Business) {
        this.bgd1Business = bgd1Business;
        this.nodifySetFiled("bgd1Business", bgd1Business);
        return this;
    }

    public String getBgd1Business() {
        this.nodifyGetFiled("bgd1Business");
        return bgd1Business;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setBgd1Ieflag(String bgd1Ieflag) {
        this.bgd1Ieflag = bgd1Ieflag;
        this.nodifySetFiled("bgd1Ieflag", bgd1Ieflag);
        return this;
    }

    public String getBgd1Ieflag() {
        this.nodifyGetFiled("bgd1Ieflag");
        return bgd1Ieflag;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setBgd2Business(String bgd2Business) {
        this.bgd2Business = bgd2Business;
        this.nodifySetFiled("bgd2Business", bgd2Business);
        return this;
    }

    public String getBgd2Business() {
        this.nodifyGetFiled("bgd2Business");
        return bgd2Business;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setBgd2Ieflag(String bgd2Ieflag) {
        this.bgd2Ieflag = bgd2Ieflag;
        this.nodifySetFiled("bgd2Ieflag", bgd2Ieflag);
        return this;
    }

    public String getBgd2Ieflag() {
        this.nodifyGetFiled("bgd2Ieflag");
        return bgd2Ieflag;
    }

    public OmsOrderFwxmWorkFkHzqdEntity setYsamount(String ysamount) {
        this.ysamount = ysamount;
        this.nodifySetFiled("ysamount", ysamount);
        return this;
    }

    public String getYsamount() {
        this.nodifyGetFiled("ysamount");
        return ysamount;
    }

}
