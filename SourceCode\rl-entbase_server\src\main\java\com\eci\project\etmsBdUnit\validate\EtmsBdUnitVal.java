package com.eci.project.etmsBdUnit.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.etmsBdUnit.entity.EtmsBdUnitEntity;

import com.eci.sso.role.entity.UserContext;
import org.springframework.stereotype.Service;

import java.util.Date;


/**
* 计量单位Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-03-21
*/
@Service
public class EtmsBdUnitVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(EtmsBdUnitEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(EtmsBdUnitEntity entity, BusinessType businessType) {
        entity.setUpdateUser(UserContext.getUserInfo().getUserId());
        entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        entity.setUpdateDate(new Date());
        if(businessType==BusinessType.INSERT){
            entity.setCreateUser(UserContext.getUserInfo().getUserId());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setCreateDate(new Date());
        }
    }

}
