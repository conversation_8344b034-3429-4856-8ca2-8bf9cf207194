package com.eci.project.etmsBdTruckGpsNow.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.math.BigDecimal;
import java.util.Date;


/**
* 对象 ETMS_BD_TRUCK_GPS_NOW
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-04-14
*/
@ApiModel("")
@TableName("ETMS_BD_TRUCK_GPS_NOW")
@FieldNameConstants
public class EtmsBdTruckGpsNowEntity extends EciBaseEntity{
    /**
    * GUID
    */
    @ApiModelProperty("GUID(50)")
    @TableId("GUID")
    private String guid;

    /**
    * 车牌号
    */
    @ApiModelProperty("车牌号(20)")
    @TableField("TRUCK_NO")
    private String truckNo;

    /**
    * 定位方式
    */
    @ApiModelProperty("定位方式(20)")
    @TableField("GPS_MODE")
    private String gpsMode;

    /**
    * 定位设备编号
    */
    @ApiModelProperty("定位设备编号(40)")
    @TableField("GPS_NO")
    private String gpsNo;

    /**
    * 司机姓名
    */
    @ApiModelProperty("司机姓名(20)")
    @TableField("DRIVER_NAME")
    private String driverName;

    /**
    * 司机电话
    */
    @ApiModelProperty("司机电话(20)")
    @TableField("DRIVER_PHONE")
    private String driverPhone;

    /**
    * 经度
    */
    @ApiModelProperty("经度(22)")
    @TableField("LONGITUDE")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal longitude;

    /**
    * 纬度
    */
    @ApiModelProperty("纬度(22)")
    @TableField("LATITUDE")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal latitude;

    /**
    * 百度经度
    */
    @ApiModelProperty("百度经度(22)")
    @TableField("LONGITUDEBD")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal longitudebd;

    /**
    * 百度纬度
    */
    @ApiModelProperty("百度纬度(22)")
    @TableField("LATITUDEBD")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal latitudebd;

    /**
    * 地址
    */
    @ApiModelProperty("地址(400)")
    @TableField("ADDRESS")
    private String address;

    /**
    * 采集时间
    */
    @ApiModelProperty("采集时间(7)")
    @TableField("ADD_TIME")
    private Date addTime;

    @ApiModelProperty("采集时间开始")
    @TableField(exist=false)
    private Date addTimeStart;

    @ApiModelProperty("采集时间结束")
    @TableField(exist=false)
    private Date addTimeEnd;

    /**
    * 状态  默认空闲
    */
    @ApiModelProperty("状态  默认空闲(1)")
    @TableField("STATUS")
    private String status;

    /**
    * 当前派车单GUID
    */
    @ApiModelProperty("当前派车单GUID(40)")
    @TableField("ORDER_CAR_GUID")
    private String orderCarGuid;

    /**
    * 创建公司
    */
    @ApiModelProperty("创建公司(40)")
    @TableField("CREATE_COMPANY")
    private String createCompany;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public EtmsBdTruckGpsNowEntity() {
        this.setSubClazz(EtmsBdTruckGpsNowEntity.class);
    }

    public EtmsBdTruckGpsNowEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public EtmsBdTruckGpsNowEntity setTruckNo(String truckNo) {
        this.truckNo = truckNo;
        this.nodifySetFiled("truckNo", truckNo);
        return this;
    }

    public String getTruckNo() {
        this.nodifyGetFiled("truckNo");
        return truckNo;
    }

    public EtmsBdTruckGpsNowEntity setGpsMode(String gpsMode) {
        this.gpsMode = gpsMode;
        this.nodifySetFiled("gpsMode", gpsMode);
        return this;
    }

    public String getGpsMode() {
        this.nodifyGetFiled("gpsMode");
        return gpsMode;
    }

    public EtmsBdTruckGpsNowEntity setGpsNo(String gpsNo) {
        this.gpsNo = gpsNo;
        this.nodifySetFiled("gpsNo", gpsNo);
        return this;
    }

    public String getGpsNo() {
        this.nodifyGetFiled("gpsNo");
        return gpsNo;
    }

    public EtmsBdTruckGpsNowEntity setDriverName(String driverName) {
        this.driverName = driverName;
        this.nodifySetFiled("driverName", driverName);
        return this;
    }

    public String getDriverName() {
        this.nodifyGetFiled("driverName");
        return driverName;
    }

    public EtmsBdTruckGpsNowEntity setDriverPhone(String driverPhone) {
        this.driverPhone = driverPhone;
        this.nodifySetFiled("driverPhone", driverPhone);
        return this;
    }

    public String getDriverPhone() {
        this.nodifyGetFiled("driverPhone");
        return driverPhone;
    }

    public EtmsBdTruckGpsNowEntity setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
        this.nodifySetFiled("longitude", longitude);
        return this;
    }

    public BigDecimal getLongitude() {
        this.nodifyGetFiled("longitude");
        return longitude;
    }

    public EtmsBdTruckGpsNowEntity setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
        this.nodifySetFiled("latitude", latitude);
        return this;
    }

    public BigDecimal getLatitude() {
        this.nodifyGetFiled("latitude");
        return latitude;
    }

    public EtmsBdTruckGpsNowEntity setLongitudebd(BigDecimal longitudebd) {
        this.longitudebd = longitudebd;
        this.nodifySetFiled("longitudebd", longitudebd);
        return this;
    }

    public BigDecimal getLongitudebd() {
        this.nodifyGetFiled("longitudebd");
        return longitudebd;
    }

    public EtmsBdTruckGpsNowEntity setLatitudebd(BigDecimal latitudebd) {
        this.latitudebd = latitudebd;
        this.nodifySetFiled("latitudebd", latitudebd);
        return this;
    }

    public BigDecimal getLatitudebd() {
        this.nodifyGetFiled("latitudebd");
        return latitudebd;
    }

    public EtmsBdTruckGpsNowEntity setAddress(String address) {
        this.address = address;
        this.nodifySetFiled("address", address);
        return this;
    }

    public String getAddress() {
        this.nodifyGetFiled("address");
        return address;
    }

    public EtmsBdTruckGpsNowEntity setAddTime(Date addTime) {
        this.addTime = addTime;
        this.nodifySetFiled("addTime", addTime);
        return this;
    }

    public Date getAddTime() {
        this.nodifyGetFiled("addTime");
        return addTime;
    }

    public EtmsBdTruckGpsNowEntity setAddTimeStart(Date addTimeStart) {
        this.addTimeStart = addTimeStart;
        this.nodifySetFiled("addTimeStart", addTimeStart);
        return this;
    }

    public Date getAddTimeStart() {
        this.nodifyGetFiled("addTimeStart");
        return addTimeStart;
    }

    public EtmsBdTruckGpsNowEntity setAddTimeEnd(Date addTimeEnd) {
        this.addTimeEnd = addTimeEnd;
        this.nodifySetFiled("addTimeEnd", addTimeEnd);
        return this;
    }

    public Date getAddTimeEnd() {
        this.nodifyGetFiled("addTimeEnd");
        return addTimeEnd;
    }
    public EtmsBdTruckGpsNowEntity setStatus(String status) {
        this.status = status;
        this.nodifySetFiled("status", status);
        return this;
    }

    public String getStatus() {
        this.nodifyGetFiled("status");
        return status;
    }

    public EtmsBdTruckGpsNowEntity setOrderCarGuid(String orderCarGuid) {
        this.orderCarGuid = orderCarGuid;
        this.nodifySetFiled("orderCarGuid", orderCarGuid);
        return this;
    }

    public String getOrderCarGuid() {
        this.nodifyGetFiled("orderCarGuid");
        return orderCarGuid;
    }

    public EtmsBdTruckGpsNowEntity setCreateCompany(String createCompany) {
        this.createCompany = createCompany;
        this.nodifySetFiled("createCompany", createCompany);
        return this;
    }

    public String getCreateCompany() {
        this.nodifyGetFiled("createCompany");
        return createCompany;
    }

    public EtmsBdTruckGpsNowEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public EtmsBdTruckGpsNowEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public EtmsBdTruckGpsNowEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public EtmsBdTruckGpsNowEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public EtmsBdTruckGpsNowEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public EtmsBdTruckGpsNowEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public EtmsBdTruckGpsNowEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public EtmsBdTruckGpsNowEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

}
