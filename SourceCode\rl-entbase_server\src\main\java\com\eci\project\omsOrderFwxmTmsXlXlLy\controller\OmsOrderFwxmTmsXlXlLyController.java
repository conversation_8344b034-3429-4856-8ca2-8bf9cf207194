package com.eci.project.omsOrderFwxmTmsXlXlLy.controller;

import cn.hutool.json.JSONUtil;
import com.eci.common.ResponseMsgUtilX;
import com.eci.common.ZsrJson;
import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.omsOrderFwxmTmsXlXlLy.entity.OmsOrderFwxmTmsXlXlLyDTOEntity;
import com.eci.project.omsOrderFwxmTmsXlXlLy.service.OmsOrderFwxmTmsXlXlLyService;
import com.eci.project.omsOrderFwxmTmsXlXlLy.entity.OmsOrderFwxmTmsXlXlLyEntity;
import com.eci.project.omsOrderFwxmTmsXlXlLyCl.entity.OmsOrderFwxmTmsXlXlLyClEntity;
import com.eci.project.omsOrderFwxmTmsXlXlLyXl.entity.OmsOrderFwxmTmsXlXlLyXlEntity;
import dm.jdbc.filter.stat.util.JSONUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 委托内容-程运序列-陆运Controller
*
* @<NAME_EMAIL>
* @date 2025-06-04
*/
@Api(tags = "委托内容-程运序列-陆运")
@RestController
@RequestMapping("/omsOrderFwxmTmsXlXlLy")
public class OmsOrderFwxmTmsXlXlLyController extends EciBaseController {

    @Autowired
    private OmsOrderFwxmTmsXlXlLyService omsOrderFwxmTmsXlXlLyService;


    @ApiOperation("委托内容-程运序列-陆运:保存")
    @EciLog(title = "委托内容-程运序列-陆运:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody OmsOrderFwxmTmsXlXlLyDTOEntity entity){
        List<OmsOrderFwxmTmsXlXlLyXlEntity> xlList= JSONUtil.toList(entity.getXldata(), OmsOrderFwxmTmsXlXlLyXlEntity.class);
        List<OmsOrderFwxmTmsXlXlLyClEntity> clList=JSONUtil.toList(entity.getCldata(), OmsOrderFwxmTmsXlXlLyClEntity.class);
        OmsOrderFwxmTmsXlXlLyDTOEntity omsOrderFwxmTmsXlXlLyEntity =omsOrderFwxmTmsXlXlLyService.saveInfo(entity,xlList,clList);
        return ResponseMsgUtil.successPlus(10001,omsOrderFwxmTmsXlXlLyEntity);
    }


    @ApiOperation("委托内容-程运序列-陆运:查询列表")
    @EciLog(title = "委托内容-程运序列-陆运:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody OmsOrderFwxmTmsXlXlLyEntity entity){
        List<OmsOrderFwxmTmsXlXlLyEntity> omsOrderFwxmTmsXlXlLyEntities = omsOrderFwxmTmsXlXlLyService.selectList(entity);
        return ResponseMsgUtil.success(10001,omsOrderFwxmTmsXlXlLyEntities);
    }


    @ApiOperation("委托内容-程运序列-陆运:分页查询列表")
    @EciLog(title = "委托内容-程运序列-陆运:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody OmsOrderFwxmTmsXlXlLyEntity entity){
        TgPageInfo tgPageInfo = omsOrderFwxmTmsXlXlLyService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("委托内容-程运序列-陆运:根据ID查一条")
    @EciLog(title = "委托内容-程运序列-陆运:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody OmsOrderFwxmTmsXlXlLyEntity entity){
        OmsOrderFwxmTmsXlXlLyEntity  omsOrderFwxmTmsXlXlLyEntity = omsOrderFwxmTmsXlXlLyService.selectOneById(entity.getLyNo());
        return ResponseMsgUtil.success(10001,omsOrderFwxmTmsXlXlLyEntity);
    }

    @ApiOperation("委托内容-程运序列-陆运:根据ID查一条")
    @EciLog(title = "委托内容-程运序列-陆运:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneByEntity")
    @EciAction()
    public ResponseMsg selectOneByEntity(@RequestBody OmsOrderFwxmTmsXlXlLyEntity entity){
        OmsOrderFwxmTmsXlXlLyEntity  omsOrderFwxmTmsXlXlLyEntity = omsOrderFwxmTmsXlXlLyService.selectOneByEntity(entity);
        //return ResponseMsgUtil.success(10001,omsOrderFwxmTmsXlXlLyEntity);
        return ResponseMsgUtilX.success(10001, omsOrderFwxmTmsXlXlLyEntity);
    }


    @ApiOperation("委托内容-程运序列-陆运:根据ID删除一条")
    @EciLog(title = "委托内容-程运序列-陆运:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody OmsOrderFwxmTmsXlXlLyEntity entity){
        int count = omsOrderFwxmTmsXlXlLyService.deleteById(entity.getLyNo());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("委托内容-程运序列-陆运:根据ID字符串删除多条")
    @EciLog(title = "委托内容-程运序列-陆运:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody OmsOrderFwxmTmsXlXlLyEntity entity) {
        int count = omsOrderFwxmTmsXlXlLyService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }

}