package com.eci.project.etmsBdTruck.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.project.etmsBdTruck.service.IEtmsBdTruckService;
import com.eci.project.etmsBdTruck.entity.EtmsBdTruckEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.EciLog;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


/**
* 车辆信息Controller
*
* @<NAME_EMAIL>
* @date 2025-04-11
*/
@Api(tags = "车辆信息")
@RestController
@RequestMapping("/etmsBdTruck")
public class EtmsBdTruckController extends EciBaseController {

    @Autowired
    private IEtmsBdTruckService etmsBdTruckService;

    /**
    * 根据实体插入数据库
    * @param entity
    * @return
    */
    @ApiOperation("车辆信息:保存")
    @EciLog(title = "车辆信息:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public ResponseMsg save(@RequestBody EtmsBdTruckEntity entity){
        return ResponseMsgUtil.success(10001,etmsBdTruckService.save(entity));
    }

    /**
    * 根据条件查询一个集合
    * @param entity
    * @return
    */
    @ApiOperation("车辆信息:查询列表")
    @EciLog(title = "车辆信息:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    public ResponseMsg selectList(@RequestBody EtmsBdTruckEntity entity){
        return ResponseMsgUtil.success(10001,etmsBdTruckService.selectList(entity));
    }

    /**
    * 分页查询
    * @param entity
    * @return
    */
    @ApiOperation("车辆信息:分页查询列表")
    @EciLog(title = "车辆信息:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    public ResponseMsg selectPageList(@RequestBody EtmsBdTruckEntity entity){
        return ResponseMsgUtil.success(10001,etmsBdTruckService.queryPageList(entity));
    }

    /**
    * 根据ID查一条记录
    * @param entity
    * @return
    */
    @ApiOperation("车辆信息:根据ID查一条")
    @EciLog(title = "车辆信息:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    public ResponseMsg selectOneById(@RequestBody EtmsBdTruckEntity entity){
        return ResponseMsgUtil.success(10001,etmsBdTruckService.selectOneById(entity.getGuid()));
    }


    /**
    * 根据ID删除
    * @param entity
    * @return
    */
    @ApiOperation("车辆信息:根据ID删除一条")
    @EciLog(title = "车辆信息:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    public ResponseMsg deleteById(@RequestBody EtmsBdTruckEntity entity){
        return ResponseMsgUtil.success(10001,etmsBdTruckService.deleteById(entity.getGuid()));
    }

    /**
    * 根据IDS删除
    *
    * @param entity
    * @return
    */
    @ApiOperation("车辆信息:根据ID字符串删除多条")
    @EciLog(title = "车辆信息:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    public ResponseMsg deleteByIds(@RequestBody EtmsBdTruckEntity entity) {
        return ResponseMsgUtil.success(10001, etmsBdTruckService.deleteByIds(entity.getIds()));
    }


}