package com.eci.project.etmsBdCargoType.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.etmsBdCargoType.service.EtmsBdCargoTypeService;
import com.eci.project.etmsBdCargoType.entity.EtmsBdCargoTypeEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 货物形态Controller
*
* @<NAME_EMAIL>
* @date 2025-03-21
*/
@Api(tags = "货物形态")
@RestController
@RequestMapping("/etmsBdCargoType")
public class EtmsBdCargoTypeController extends EciBaseController {

    @Autowired
    private EtmsBdCargoTypeService etmsBdCargoTypeService;


    @ApiOperation("货物形态:保存")
    @EciLog(title = "货物形态:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody EtmsBdCargoTypeEntity entity){
        EtmsBdCargoTypeEntity etmsBdCargoTypeEntity =etmsBdCargoTypeService.save(entity);
        return ResponseMsgUtil.success(10001,etmsBdCargoTypeEntity);
    }


    @ApiOperation("货物形态:查询列表")
    @EciLog(title = "货物形态:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody EtmsBdCargoTypeEntity entity){
        List<EtmsBdCargoTypeEntity> etmsBdCargoTypeEntities = etmsBdCargoTypeService.selectList(entity);
        return ResponseMsgUtil.success(10001,etmsBdCargoTypeEntities);
    }


    @ApiOperation("货物形态:分页查询列表")
    @EciLog(title = "货物形态:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody EtmsBdCargoTypeEntity entity){
        TgPageInfo tgPageInfo = etmsBdCargoTypeService.queryPageList(entity);
        return ResponseMsgUtil.successPlus(10001,tgPageInfo);
    }


    @ApiOperation("货物形态:根据ID查一条")
    @EciLog(title = "货物形态:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody EtmsBdCargoTypeEntity entity){
        EtmsBdCargoTypeEntity  etmsBdCargoTypeEntity = etmsBdCargoTypeService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,etmsBdCargoTypeEntity);
    }


    @ApiOperation("货物形态:根据ID删除一条")
    @EciLog(title = "货物形态:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody EtmsBdCargoTypeEntity entity){
        int count = etmsBdCargoTypeService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("货物形态:根据ID字符串删除多条")
    @EciLog(title = "货物形态:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody EtmsBdCargoTypeEntity entity) {
        int count = etmsBdCargoTypeService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}