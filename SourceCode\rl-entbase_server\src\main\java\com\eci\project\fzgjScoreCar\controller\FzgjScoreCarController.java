package com.eci.project.fzgjScoreCar.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjScoreCar.service.FzgjScoreCarService;
import com.eci.project.fzgjScoreCar.entity.FzgjScoreCarEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 企业评分Controller
*
* @<NAME_EMAIL>
* @date 2025-06-16
*/
@Api(tags = "企业评分")
@RestController
@RequestMapping("/fzgjScoreCar")
public class FzgjScoreCarController extends EciBaseController {

    @Autowired
    private FzgjScoreCarService fzgjScoreCarService;


    @ApiOperation("企业评分:保存")
    @EciLog(title = "企业评分:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody FzgjScoreCarEntity entity){
        FzgjScoreCarEntity fzgjScoreCarEntity =fzgjScoreCarService.save(entity);
        return ResponseMsgUtil.success(10001,fzgjScoreCarEntity);
    }


    @ApiOperation("企业评分:查询列表")
    @EciLog(title = "企业评分:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjScoreCarEntity entity){
        List<FzgjScoreCarEntity> fzgjScoreCarEntities = fzgjScoreCarService.selectList(entity);
        return ResponseMsgUtil.success(10001,fzgjScoreCarEntities);
    }


    @ApiOperation("企业评分:分页查询列表")
    @EciLog(title = "企业评分:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjScoreCarEntity entity){
        TgPageInfo tgPageInfo = fzgjScoreCarService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("企业评分:根据ID查一条")
    @EciLog(title = "企业评分:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjScoreCarEntity entity){
        FzgjScoreCarEntity  fzgjScoreCarEntity = fzgjScoreCarService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,fzgjScoreCarEntity);
    }


    @ApiOperation("企业评分:根据ID删除一条")
    @EciLog(title = "企业评分:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjScoreCarEntity entity){
        int count = fzgjScoreCarService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("企业评分:根据ID字符串删除多条")
    @EciLog(title = "企业评分:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjScoreCarEntity entity) {
        int count = fzgjScoreCarService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}