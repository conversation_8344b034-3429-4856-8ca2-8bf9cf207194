package com.eci.project.fzgjExtendData.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjExtendData.entity.FzgjExtendDataEntity;

import org.springframework.stereotype.Service;


/**
* 扩展基础资料Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-03-19
*/
@Service
public class FzgjExtendDataVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjExtendDataEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjExtendDataEntity entity, BusinessType businessType) {

    }

}
