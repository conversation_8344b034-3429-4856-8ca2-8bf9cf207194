package com.eci.project.omsOrderFwxmWorkFkHfd.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 反馈内容-核放单对象 OMS_ORDER_FWXM_WORK_FK_HFD
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-06-03
*/
@ApiModel("反馈内容-核放单")
@TableName("OMS_ORDER_FWXM_WORK_FK_HFD")
@FieldNameConstants
public class OmsOrderFwxmWorkFkHfdEntity extends EciBaseEntity{
    /**
    * GUID
    */
    @ApiModelProperty("GUID(36)")
    @TableId("GUID")
    private String guid;

    /**
    * 订单号
    */
    @ApiModelProperty("订单号(20)")
    @TableField("ORDER_NO")
    private String orderNo;

    /**
    * 任务协助编号
    */
    @ApiModelProperty("任务协助编号(50)")
    @TableField("WORK_NO")
    private String workNo;

    /**
    * 服务项目
    */
    @ApiModelProperty("服务项目(20)")
    @TableField("FWXM_CODE")
    private String fwxmCode;

    /**
    * 金二核放单号
    */
    @ApiModelProperty("金二核放单号(50)")
    @TableField("HF_NO")
    private String hfNo;

    /**
    * 协作委托单编号
    */
    @ApiModelProperty("协作委托单编号(36)")
    @TableField("XZWT_NO")
    private String xzwtNo;

    /**
    * 核注清单编号
    */
    @ApiModelProperty("核注清单编号(20)")
    @TableField("CHECKBILL_NO")
    private String checkbillNo;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 最后修改日期
    */
    @ApiModelProperty("最后修改日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("最后修改日期开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("最后修改日期结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(200)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 业务数据唯一注册编号
    */
    @ApiModelProperty("业务数据唯一注册编号(50)")
    @TableField("BIZ_REG_ID")
    private String bizRegId;

    /**
    * 陆运核放单号
    */
    @ApiModelProperty("陆运核放单号(50)")
    @TableField("HF_NO_LY")
    private String hfNoLy;

    /**
    * 备注
    */
    @ApiModelProperty("备注(500)")
    @TableField("REMARK")
    private String remark;

    /**
    * 实物放行时间
    */
    @ApiModelProperty("实物放行时间(7)")
    @TableField("FX_DATE")
    private Date fxDate;

    @ApiModelProperty("实物放行时间开始")
    @TableField(exist=false)
    private Date fxDateStart;

    @ApiModelProperty("实物放行时间结束")
    @TableField(exist=false)
    private Date fxDateEnd;

    /**
    * 车牌号
    */
    @ApiModelProperty("车牌号(50)")
    @TableField("CAR_NO")
    private String carNo;

    /**
    * 进出口标志(I/E)
    */
    @ApiModelProperty("进出口标志(I/E)(1)")
    @TableField("I_E_FLAG")
    private String iEFlag;

    /**
    * 进出区标志
    */
    @ApiModelProperty("进出区标志(50)")
    @TableField("IE_MARK")
    private String ieMark;

    /**
    * 页面录入单证号
    */
    @ApiModelProperty("页面录入单证号(500)")
    @TableField("GJ_NO")
    private String gjNo;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public OmsOrderFwxmWorkFkHfdEntity() {
        this.setSubClazz(OmsOrderFwxmWorkFkHfdEntity.class);
    }

    public OmsOrderFwxmWorkFkHfdEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public OmsOrderFwxmWorkFkHfdEntity setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        this.nodifySetFiled("orderNo", orderNo);
        return this;
    }

    public String getOrderNo() {
        this.nodifyGetFiled("orderNo");
        return orderNo;
    }

    public OmsOrderFwxmWorkFkHfdEntity setWorkNo(String workNo) {
        this.workNo = workNo;
        this.nodifySetFiled("workNo", workNo);
        return this;
    }

    public String getWorkNo() {
        this.nodifyGetFiled("workNo");
        return workNo;
    }

    public OmsOrderFwxmWorkFkHfdEntity setFwxmCode(String fwxmCode) {
        this.fwxmCode = fwxmCode;
        this.nodifySetFiled("fwxmCode", fwxmCode);
        return this;
    }

    public String getFwxmCode() {
        this.nodifyGetFiled("fwxmCode");
        return fwxmCode;
    }

    public OmsOrderFwxmWorkFkHfdEntity setHfNo(String hfNo) {
        this.hfNo = hfNo;
        this.nodifySetFiled("hfNo", hfNo);
        return this;
    }

    public String getHfNo() {
        this.nodifyGetFiled("hfNo");
        return hfNo;
    }

    public OmsOrderFwxmWorkFkHfdEntity setXzwtNo(String xzwtNo) {
        this.xzwtNo = xzwtNo;
        this.nodifySetFiled("xzwtNo", xzwtNo);
        return this;
    }

    public String getXzwtNo() {
        this.nodifyGetFiled("xzwtNo");
        return xzwtNo;
    }

    public OmsOrderFwxmWorkFkHfdEntity setCheckbillNo(String checkbillNo) {
        this.checkbillNo = checkbillNo;
        this.nodifySetFiled("checkbillNo", checkbillNo);
        return this;
    }

    public String getCheckbillNo() {
        this.nodifyGetFiled("checkbillNo");
        return checkbillNo;
    }

    public OmsOrderFwxmWorkFkHfdEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public OmsOrderFwxmWorkFkHfdEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public OmsOrderFwxmWorkFkHfdEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public OmsOrderFwxmWorkFkHfdEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public OmsOrderFwxmWorkFkHfdEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public OmsOrderFwxmWorkFkHfdEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public OmsOrderFwxmWorkFkHfdEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public OmsOrderFwxmWorkFkHfdEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public OmsOrderFwxmWorkFkHfdEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public OmsOrderFwxmWorkFkHfdEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public OmsOrderFwxmWorkFkHfdEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public OmsOrderFwxmWorkFkHfdEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public OmsOrderFwxmWorkFkHfdEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public OmsOrderFwxmWorkFkHfdEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public OmsOrderFwxmWorkFkHfdEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public OmsOrderFwxmWorkFkHfdEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public OmsOrderFwxmWorkFkHfdEntity setBizRegId(String bizRegId) {
        this.bizRegId = bizRegId;
        this.nodifySetFiled("bizRegId", bizRegId);
        return this;
    }

    public String getBizRegId() {
        this.nodifyGetFiled("bizRegId");
        return bizRegId;
    }

    public OmsOrderFwxmWorkFkHfdEntity setHfNoLy(String hfNoLy) {
        this.hfNoLy = hfNoLy;
        this.nodifySetFiled("hfNoLy", hfNoLy);
        return this;
    }

    public String getHfNoLy() {
        this.nodifyGetFiled("hfNoLy");
        return hfNoLy;
    }

    public OmsOrderFwxmWorkFkHfdEntity setRemark(String remark) {
        this.remark = remark;
        this.nodifySetFiled("remark", remark);
        return this;
    }

    public String getRemark() {
        this.nodifyGetFiled("remark");
        return remark;
    }

    public OmsOrderFwxmWorkFkHfdEntity setFxDate(Date fxDate) {
        this.fxDate = fxDate;
        this.nodifySetFiled("fxDate", fxDate);
        return this;
    }

    public Date getFxDate() {
        this.nodifyGetFiled("fxDate");
        return fxDate;
    }

    public OmsOrderFwxmWorkFkHfdEntity setFxDateStart(Date fxDateStart) {
        this.fxDateStart = fxDateStart;
        this.nodifySetFiled("fxDateStart", fxDateStart);
        return this;
    }

    public Date getFxDateStart() {
        this.nodifyGetFiled("fxDateStart");
        return fxDateStart;
    }

    public OmsOrderFwxmWorkFkHfdEntity setFxDateEnd(Date fxDateEnd) {
        this.fxDateEnd = fxDateEnd;
        this.nodifySetFiled("fxDateEnd", fxDateEnd);
        return this;
    }

    public Date getFxDateEnd() {
        this.nodifyGetFiled("fxDateEnd");
        return fxDateEnd;
    }
    public OmsOrderFwxmWorkFkHfdEntity setCarNo(String carNo) {
        this.carNo = carNo;
        this.nodifySetFiled("carNo", carNo);
        return this;
    }

    public String getCarNo() {
        this.nodifyGetFiled("carNo");
        return carNo;
    }

    public OmsOrderFwxmWorkFkHfdEntity setiEFlag(String iEFlag) {
        this.iEFlag = iEFlag;
        this.nodifySetFiled("iEFlag", iEFlag);
        return this;
    }

    public String getiEFlag() {
        this.nodifyGetFiled("iEFlag");
        return iEFlag;
    }

    public OmsOrderFwxmWorkFkHfdEntity setIeMark(String ieMark) {
        this.ieMark = ieMark;
        this.nodifySetFiled("ieMark", ieMark);
        return this;
    }

    public String getIeMark() {
        this.nodifyGetFiled("ieMark");
        return ieMark;
    }

    public OmsOrderFwxmWorkFkHfdEntity setGjNo(String gjNo) {
        this.gjNo = gjNo;
        this.nodifySetFiled("gjNo", gjNo);
        return this;
    }

    public String getGjNo() {
        this.nodifyGetFiled("gjNo");
        return gjNo;
    }

}
