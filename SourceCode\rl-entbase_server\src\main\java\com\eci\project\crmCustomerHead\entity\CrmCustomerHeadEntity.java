package com.eci.project.crmCustomerHead.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 业务伙伴表头对象 CRM_CUSTOMER_HEAD
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-04-29
*/
@ApiModel("业务伙伴表头")
@TableName("CRM_CUSTOMER_HEAD")
@FieldNameConstants
public class CrmCustomerHeadEntity extends EciBaseEntity{
    /**
    * 编号
    */
    @ApiModelProperty("编号(36)")
    @TableId("GUID")
    private String guid;

    /**
    * 企业代码
    */
    @ApiModelProperty("企业代码(36)")
    @TableField("CODE")
    private String code;

    /**
    * 企业全称
    */
    @ApiModelProperty("企业全称(100)")
    @TableField("NAME")
    private String name;

    /**
    * 企业简称
    */
    @ApiModelProperty("企业简称(50)")
    @TableField("SHORT_NAME")
    private String shortName;

    /**
    * 企业英文名称
    */
    @ApiModelProperty("企业英文名称(100)")
    @TableField("EN_NAME")
    private String enName;

    /**
    * 所属国家
    */
    @ApiModelProperty("所属国家(50)")
    @TableField("COUNTRY")
    private String country;

    /**
    * 所属省份
    */
    @ApiModelProperty("所属省份(50)")
    @TableField("PROVINCE")
    private String province;

    /**
    * 所属城市
    */
    @ApiModelProperty("所属城市(50)")
    @TableField("CITY")
    private String city;

    /**
    * 所属区/县
    */
    @ApiModelProperty("所属区/县(50)")
    @TableField("DISTRICT")
    private String district;

    /**
    * 企业地址
    */
    @ApiModelProperty("企业地址(1,000)")
    @TableField("ADDRESS")
    private String address;

    /**
    * 企业英文地址
    */
    @ApiModelProperty("企业英文地址(510)")
    @TableField("ADDRESS_EN")
    private String addressEn;

    /**
    * 统一社会信用代码
    */
    @ApiModelProperty("统一社会信用代码(50)")
    @TableField("TY_CODE")
    private String tyCode;

    /**
    * 联系人
    */
    @ApiModelProperty("联系人(20)")
    @TableField("PERSON")
    private String person;

    /**
    * 成立日期
    */
    @ApiModelProperty("成立日期(7)")
    @TableField("CL_DATE")
    private Date clDate;

    @ApiModelProperty("成立日期开始")
    @TableField(exist=false)
    private Date clDateStart;

    @ApiModelProperty("成立日期结束")
    @TableField(exist=false)
    private Date clDateEnd;

    /**
    * 注册币制
    */
    @ApiModelProperty("注册币制(20)")
    @TableField("ZC_CURR")
    private String zcCurr;

    /**
    * 注册资本
    */
    @ApiModelProperty("注册资本(50)")
    @TableField("ZC_CAPITAL")
    private String zcCapital;

    /**
    * 股票代码
    */
    @ApiModelProperty("股票代码(50)")
    @TableField("STOCK_CODE")
    private String stockCode;

    /**
    * 企业海关代码
    */
    @ApiModelProperty("企业海关代码(20)")
    @TableField("CUSTOM_NO")
    private String customNo;

    /**
    * 是否上市公司
    */
    @ApiModelProperty("是否上市公司(1)")
    @TableField("IS_SSGS")
    private String isSsgs;

    /**
    * 法人代表
    */
    @ApiModelProperty("法人代表(50)")
    @TableField("FRDB")
    private String frdb;

    /**
    * 员工人数
    */
    @ApiModelProperty("员工人数(22)")
    @TableField("EMPLOYEE_NUM")
    private Integer employeeNum;

    /**
    * 是否内部公司
    */
    @ApiModelProperty("是否内部公司(1)")
    @TableField("IS_NB")
    private String isNb;

    /**
    * 是否启用
    */
    @ApiModelProperty("是否启用(1)")
    @TableField("STATUS")
    private String status;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 最后修改日期
    */
    @ApiModelProperty("最后修改日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("最后修改日期开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("最后修改日期结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(200)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 角色代码，多个逗号分隔
    */
    @ApiModelProperty("角色代码，多个逗号分隔(500)")
    @TableField("ROLE_CODE")
    private String roleCode;

    /**
    * 角色名称，多个逗号分隔
    */
    @ApiModelProperty("角色名称，多个逗号分隔(500)")
    @TableField("ROLE_NAME")
    private String roleName;

    /**
    * 公司性质
    */
    @ApiModelProperty("公司性质(20)")
    @TableField("COMPANY_TYPE")
    private String companyType;

    /**
    * 企业电话
    */
    @ApiModelProperty("企业电话(50)")
    @TableField("TEL")
    private String tel;

    /**
    * 用户授权控制
    */
    @ApiModelProperty("用户授权控制(1)")
    @TableField("IS_USER_CONTROL")
    private String isUserControl;

    /**
    * 企业B2B唯一码
    */
    @ApiModelProperty("企业B2B唯一码(50)")
    @TableField("CUSTOMER_B2B")
    private String customerB2b;

    /**
    * 备注
    */
    @ApiModelProperty("备注(500)")
    @TableField("MEMO")
    private String memo;

    /**
    * 企业区域性质
    */
    @ApiModelProperty("企业区域性质(20)")
    @TableField("IS_CUSTOM")
    private String isCustom;

    /**
    * 业务伙伴集团代码
    */
    @ApiModelProperty("业务伙伴集团代码(40)")
    @TableField("CUSTOMER_GROUP_CODE")
    private String customerGroupCode;

    /**
    * 业务伙伴集团名称
    */
    @ApiModelProperty("业务伙伴集团名称(200)")
    @TableField("CUSTOMER_GROUP_NAME")
    private String customerGroupName;

    /**
    * 企业类型，多个逗号分隔
    */
    @ApiModelProperty("企业类型，多个逗号分隔(4,000)")
    @TableField("COM_TYPE")
    private String comType;

    /**
    * 合作日期
    */
    @ApiModelProperty("合作日期(7)")
    @TableField("HZ_DATE")
    private Date hzDate;

    @ApiModelProperty("合作日期开始")
    @TableField(exist=false)
    private Date hzDateStart;

    @ApiModelProperty("合作日期结束")
    @TableField(exist=false)
    private Date hzDateEnd;

    /**
    * 主要货物品类
    */
    @ApiModelProperty("主要货物品类(255)")
    @TableField("GOODS_CATEGORY")
    private String goodsCategory;

    /**
    * 进出口类型，多个逗号分隔
    */
    @ApiModelProperty("进出口类型，多个逗号分隔(20)")
    @TableField("IE_TYPE")
    private String ieType;

    /**
    * 贸易国家
    */
    @ApiModelProperty("贸易国家(40)")
    @TableField("TRADE_COUNTRY")
    private String tradeCountry;

    /**
    * 货源地
    */
    @ApiModelProperty("货源地(40)")
    @TableField("HYD")
    private String hyd;

    /**
    * 企业类型名称，多个逗号分隔
    */
    @ApiModelProperty("企业类型名称，多个逗号分隔(500)")
    @TableField("COM_TYPE_NAME")
    private String comTypeName;

    /**
    * 进出口类型
    */
    @ApiModelProperty("进出口类型(500)")
    @TableField("IE_TYPE_NAME")
    private String ieTypeName;

    /**
    * 企业邮箱
    */
    @ApiModelProperty("企业邮箱(200)")
    @TableField("MAIL")
    private String mail;

    /**
    * 快速匹配码
    */
    @ApiModelProperty("快速匹配码(50)")
    @TableField("QUICK_MATCH_CODE")
    private String quickMatchCode;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public CrmCustomerHeadEntity() {
        this.setSubClazz(CrmCustomerHeadEntity.class);
    }

    public CrmCustomerHeadEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public CrmCustomerHeadEntity setCode(String code) {
        this.code = code;
        this.nodifySetFiled("code", code);
        return this;
    }

    public String getCode() {
        this.nodifyGetFiled("code");
        return code;
    }

    public CrmCustomerHeadEntity setName(String name) {
        this.name = name;
        this.nodifySetFiled("name", name);
        return this;
    }

    public String getName() {
        this.nodifyGetFiled("name");
        return name;
    }

    public CrmCustomerHeadEntity setShortName(String shortName) {
        this.shortName = shortName;
        this.nodifySetFiled("shortName", shortName);
        return this;
    }

    public String getShortName() {
        this.nodifyGetFiled("shortName");
        return shortName;
    }

    public CrmCustomerHeadEntity setEnName(String enName) {
        this.enName = enName;
        this.nodifySetFiled("enName", enName);
        return this;
    }

    public String getEnName() {
        this.nodifyGetFiled("enName");
        return enName;
    }

    public CrmCustomerHeadEntity setCountry(String country) {
        this.country = country;
        this.nodifySetFiled("country", country);
        return this;
    }

    public String getCountry() {
        this.nodifyGetFiled("country");
        return country;
    }

    public CrmCustomerHeadEntity setProvince(String province) {
        this.province = province;
        this.nodifySetFiled("province", province);
        return this;
    }

    public String getProvince() {
        this.nodifyGetFiled("province");
        return province;
    }

    public CrmCustomerHeadEntity setCity(String city) {
        this.city = city;
        this.nodifySetFiled("city", city);
        return this;
    }

    public String getCity() {
        this.nodifyGetFiled("city");
        return city;
    }

    public CrmCustomerHeadEntity setDistrict(String district) {
        this.district = district;
        this.nodifySetFiled("district", district);
        return this;
    }

    public String getDistrict() {
        this.nodifyGetFiled("district");
        return district;
    }

    public CrmCustomerHeadEntity setAddress(String address) {
        this.address = address;
        this.nodifySetFiled("address", address);
        return this;
    }

    public String getAddress() {
        this.nodifyGetFiled("address");
        return address;
    }

    public CrmCustomerHeadEntity setAddressEn(String addressEn) {
        this.addressEn = addressEn;
        this.nodifySetFiled("addressEn", addressEn);
        return this;
    }

    public String getAddressEn() {
        this.nodifyGetFiled("addressEn");
        return addressEn;
    }

    public CrmCustomerHeadEntity setTyCode(String tyCode) {
        this.tyCode = tyCode;
        this.nodifySetFiled("tyCode", tyCode);
        return this;
    }

    public String getTyCode() {
        this.nodifyGetFiled("tyCode");
        return tyCode;
    }

    public CrmCustomerHeadEntity setPerson(String person) {
        this.person = person;
        this.nodifySetFiled("person", person);
        return this;
    }

    public String getPerson() {
        this.nodifyGetFiled("person");
        return person;
    }

    public CrmCustomerHeadEntity setClDate(Date clDate) {
        this.clDate = clDate;
        this.nodifySetFiled("clDate", clDate);
        return this;
    }

    public Date getClDate() {
        this.nodifyGetFiled("clDate");
        return clDate;
    }

    public CrmCustomerHeadEntity setClDateStart(Date clDateStart) {
        this.clDateStart = clDateStart;
        this.nodifySetFiled("clDateStart", clDateStart);
        return this;
    }

    public Date getClDateStart() {
        this.nodifyGetFiled("clDateStart");
        return clDateStart;
    }

    public CrmCustomerHeadEntity setClDateEnd(Date clDateEnd) {
        this.clDateEnd = clDateEnd;
        this.nodifySetFiled("clDateEnd", clDateEnd);
        return this;
    }

    public Date getClDateEnd() {
        this.nodifyGetFiled("clDateEnd");
        return clDateEnd;
    }
    public CrmCustomerHeadEntity setZcCurr(String zcCurr) {
        this.zcCurr = zcCurr;
        this.nodifySetFiled("zcCurr", zcCurr);
        return this;
    }

    public String getZcCurr() {
        this.nodifyGetFiled("zcCurr");
        return zcCurr;
    }

    public CrmCustomerHeadEntity setZcCapital(String zcCapital) {
        this.zcCapital = zcCapital;
        this.nodifySetFiled("zcCapital", zcCapital);
        return this;
    }

    public String getZcCapital() {
        this.nodifyGetFiled("zcCapital");
        return zcCapital;
    }

    public CrmCustomerHeadEntity setStockCode(String stockCode) {
        this.stockCode = stockCode;
        this.nodifySetFiled("stockCode", stockCode);
        return this;
    }

    public String getStockCode() {
        this.nodifyGetFiled("stockCode");
        return stockCode;
    }

    public CrmCustomerHeadEntity setCustomNo(String customNo) {
        this.customNo = customNo;
        this.nodifySetFiled("customNo", customNo);
        return this;
    }

    public String getCustomNo() {
        this.nodifyGetFiled("customNo");
        return customNo;
    }

    public CrmCustomerHeadEntity setIsSsgs(String isSsgs) {
        this.isSsgs = isSsgs;
        this.nodifySetFiled("isSsgs", isSsgs);
        return this;
    }

    public String getIsSsgs() {
        this.nodifyGetFiled("isSsgs");
        return isSsgs;
    }

    public CrmCustomerHeadEntity setFrdb(String frdb) {
        this.frdb = frdb;
        this.nodifySetFiled("frdb", frdb);
        return this;
    }

    public String getFrdb() {
        this.nodifyGetFiled("frdb");
        return frdb;
    }

    public CrmCustomerHeadEntity setEmployeeNum(Integer employeeNum) {
        this.employeeNum = employeeNum;
        this.nodifySetFiled("employeeNum", employeeNum);
        return this;
    }

    public Integer getEmployeeNum() {
        this.nodifyGetFiled("employeeNum");
        return employeeNum;
    }

    public CrmCustomerHeadEntity setIsNb(String isNb) {
        this.isNb = isNb;
        this.nodifySetFiled("isNb", isNb);
        return this;
    }

    public String getIsNb() {
        this.nodifyGetFiled("isNb");
        return isNb;
    }

    public CrmCustomerHeadEntity setStatus(String status) {
        this.status = status;
        this.nodifySetFiled("status", status);
        return this;
    }

    public String getStatus() {
        this.nodifyGetFiled("status");
        return status;
    }

    public CrmCustomerHeadEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public CrmCustomerHeadEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public CrmCustomerHeadEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public CrmCustomerHeadEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public CrmCustomerHeadEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public CrmCustomerHeadEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public CrmCustomerHeadEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public CrmCustomerHeadEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public CrmCustomerHeadEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public CrmCustomerHeadEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public CrmCustomerHeadEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public CrmCustomerHeadEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public CrmCustomerHeadEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public CrmCustomerHeadEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public CrmCustomerHeadEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public CrmCustomerHeadEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public CrmCustomerHeadEntity setRoleCode(String roleCode) {
        this.roleCode = roleCode;
        this.nodifySetFiled("roleCode", roleCode);
        return this;
    }

    public String getRoleCode() {
        this.nodifyGetFiled("roleCode");
        return roleCode;
    }

    public CrmCustomerHeadEntity setRoleName(String roleName) {
        this.roleName = roleName;
        this.nodifySetFiled("roleName", roleName);
        return this;
    }

    public String getRoleName() {
        this.nodifyGetFiled("roleName");
        return roleName;
    }

    public CrmCustomerHeadEntity setCompanyType(String companyType) {
        this.companyType = companyType;
        this.nodifySetFiled("companyType", companyType);
        return this;
    }

    public String getCompanyType() {
        this.nodifyGetFiled("companyType");
        return companyType;
    }

    public CrmCustomerHeadEntity setTel(String tel) {
        this.tel = tel;
        this.nodifySetFiled("tel", tel);
        return this;
    }

    public String getTel() {
        this.nodifyGetFiled("tel");
        return tel;
    }

    public CrmCustomerHeadEntity setIsUserControl(String isUserControl) {
        this.isUserControl = isUserControl;
        this.nodifySetFiled("isUserControl", isUserControl);
        return this;
    }

    public String getIsUserControl() {
        this.nodifyGetFiled("isUserControl");
        return isUserControl;
    }

    public CrmCustomerHeadEntity setCustomerB2b(String customerB2b) {
        this.customerB2b = customerB2b;
        this.nodifySetFiled("customerB2b", customerB2b);
        return this;
    }

    public String getCustomerB2b() {
        this.nodifyGetFiled("customerB2b");
        return customerB2b;
    }

    public CrmCustomerHeadEntity setMemo(String memo) {
        this.memo = memo;
        this.nodifySetFiled("memo", memo);
        return this;
    }

    public String getMemo() {
        this.nodifyGetFiled("memo");
        return memo;
    }

    public CrmCustomerHeadEntity setIsCustom(String isCustom) {
        this.isCustom = isCustom;
        this.nodifySetFiled("isCustom", isCustom);
        return this;
    }

    public String getIsCustom() {
        this.nodifyGetFiled("isCustom");
        return isCustom;
    }

    public CrmCustomerHeadEntity setCustomerGroupCode(String customerGroupCode) {
        this.customerGroupCode = customerGroupCode;
        this.nodifySetFiled("customerGroupCode", customerGroupCode);
        return this;
    }

    public String getCustomerGroupCode() {
        this.nodifyGetFiled("customerGroupCode");
        return customerGroupCode;
    }

    public CrmCustomerHeadEntity setCustomerGroupName(String customerGroupName) {
        this.customerGroupName = customerGroupName;
        this.nodifySetFiled("customerGroupName", customerGroupName);
        return this;
    }

    public String getCustomerGroupName() {
        this.nodifyGetFiled("customerGroupName");
        return customerGroupName;
    }

    public CrmCustomerHeadEntity setComType(String comType) {
        this.comType = comType;
        this.nodifySetFiled("comType", comType);
        return this;
    }

    public String getComType() {
        this.nodifyGetFiled("comType");
        return comType;
    }

    public CrmCustomerHeadEntity setHzDate(Date hzDate) {
        this.hzDate = hzDate;
        this.nodifySetFiled("hzDate", hzDate);
        return this;
    }

    public Date getHzDate() {
        this.nodifyGetFiled("hzDate");
        return hzDate;
    }

    public CrmCustomerHeadEntity setHzDateStart(Date hzDateStart) {
        this.hzDateStart = hzDateStart;
        this.nodifySetFiled("hzDateStart", hzDateStart);
        return this;
    }

    public Date getHzDateStart() {
        this.nodifyGetFiled("hzDateStart");
        return hzDateStart;
    }

    public CrmCustomerHeadEntity setHzDateEnd(Date hzDateEnd) {
        this.hzDateEnd = hzDateEnd;
        this.nodifySetFiled("hzDateEnd", hzDateEnd);
        return this;
    }

    public Date getHzDateEnd() {
        this.nodifyGetFiled("hzDateEnd");
        return hzDateEnd;
    }
    public CrmCustomerHeadEntity setGoodsCategory(String goodsCategory) {
        this.goodsCategory = goodsCategory;
        this.nodifySetFiled("goodsCategory", goodsCategory);
        return this;
    }

    public String getGoodsCategory() {
        this.nodifyGetFiled("goodsCategory");
        return goodsCategory;
    }

    public CrmCustomerHeadEntity setIeType(String ieType) {
        this.ieType = ieType;
        this.nodifySetFiled("ieType", ieType);
        return this;
    }

    public String getIeType() {
        this.nodifyGetFiled("ieType");
        return ieType;
    }

    public CrmCustomerHeadEntity setTradeCountry(String tradeCountry) {
        this.tradeCountry = tradeCountry;
        this.nodifySetFiled("tradeCountry", tradeCountry);
        return this;
    }

    public String getTradeCountry() {
        this.nodifyGetFiled("tradeCountry");
        return tradeCountry;
    }

    public CrmCustomerHeadEntity setHyd(String hyd) {
        this.hyd = hyd;
        this.nodifySetFiled("hyd", hyd);
        return this;
    }

    public String getHyd() {
        this.nodifyGetFiled("hyd");
        return hyd;
    }

    public CrmCustomerHeadEntity setComTypeName(String comTypeName) {
        this.comTypeName = comTypeName;
        this.nodifySetFiled("comTypeName", comTypeName);
        return this;
    }

    public String getComTypeName() {
        this.nodifyGetFiled("comTypeName");
        return comTypeName;
    }

    public CrmCustomerHeadEntity setIeTypeName(String ieTypeName) {
        this.ieTypeName = ieTypeName;
        this.nodifySetFiled("ieTypeName", ieTypeName);
        return this;
    }

    public String getIeTypeName() {
        this.nodifyGetFiled("ieTypeName");
        return ieTypeName;
    }

    public CrmCustomerHeadEntity setMail(String mail) {
        this.mail = mail;
        this.nodifySetFiled("mail", mail);
        return this;
    }

    public String getMail() {
        this.nodifyGetFiled("mail");
        return mail;
    }

    public CrmCustomerHeadEntity setQuickMatchCode(String quickMatchCode) {
        this.quickMatchCode = quickMatchCode;
        this.nodifySetFiled("quickMatchCode", quickMatchCode);
        return this;
    }

    public String getQuickMatchCode() {
        this.nodifyGetFiled("quickMatchCode");
        return quickMatchCode;
    }

}
