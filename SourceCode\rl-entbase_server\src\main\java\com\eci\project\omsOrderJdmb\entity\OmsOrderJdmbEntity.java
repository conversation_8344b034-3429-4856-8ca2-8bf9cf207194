package com.eci.project.omsOrderJdmb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.common.DictField;
import com.eci.common.ZsrBaseEntity;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 接单模板对象 OMS_ORDER_JDMB
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-04-24
*/
@ApiModel("接单模板")
@TableName("OMS_ORDER_JDMB")
@FieldNameConstants
public class OmsOrderJdmbEntity extends ZsrBaseEntity {
    /**
    * 订单号，与模板编号一致
    */
    @ApiModelProperty("订单号，与模板编号一致(36)")
    @TableField("ORDER_NO")
    private String orderNo;

    /**
    * 模板编号
    */
    @ApiModelProperty("模板编号(36)")
    @TableId("JDMB_NO")
    private String jdmbNo;

    /**
    * 委托方
    */
    @ApiModelProperty("委托方(36)")
    @TableField("CONSIGNEE_CODE")
    private String consigneeCode;

    /**
    * 实际发货方
    */
    @ApiModelProperty("实际发货方(36)")
    @TableField("SHIPPER")
    private String shipper;

    /**
    * 实际收货方
    */
    @ApiModelProperty("实际收货方(36)")
    @TableField("RECEIVER")
    private String receiver;

    /**
    * 业务类型
    */
    @ApiModelProperty("业务类型(20)")
    @TableField("OP_TYPE")
    private String opType;

    /**
    * 公有/私有
    */
    @ApiModelProperty("公有/私有(10)")
    @TableField("MB_TYPE")
    @DictField(queryKey = "YNKey")
    private String mbType;

    /**
    * 是否启用
    */
    @ApiModelProperty("是否启用(1)")
    @TableField("IS_USE")
    @DictField(queryKey = "YNKey")
    private String isUse;

    /**
    * 业务产品/项目
    */
    @ApiModelProperty("业务产品/项目(20)")
    @TableField("PRODUCT_CODE")
    private String productCode;

    /**
    * 服务类型，多个逗号分割
    */
    @ApiModelProperty("服务类型，多个逗号分割(20)")
    @TableField("FWLX_CODE")
    private String fwlxCode;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 最后修改日期
    */
    @ApiModelProperty("最后修改日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("最后修改日期开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("最后修改日期结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(200)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 接单组织
    */
    @ApiModelProperty("接单组织(50)")
    @TableField("JD_NODE_CODE")
    private String jdNodeCode;

    /**
    * 接单组织
    */
    @ApiModelProperty("接单组织(50)")
    @TableField("JD_NODE_NAME")
    private String jdNodeName;

    /**
    * 模板名称
    */
    @ApiModelProperty("模板名称(200)")
    @TableField("MBMC")
    private String mbmc;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public OmsOrderJdmbEntity() {
        this.setSubClazz(OmsOrderJdmbEntity.class);
    }

    public OmsOrderJdmbEntity setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        this.nodifySetFiled("orderNo", orderNo);
        return this;
    }

    public String getOrderNo() {
        this.nodifyGetFiled("orderNo");
        return orderNo;
    }

    public OmsOrderJdmbEntity setJdmbNo(String jdmbNo) {
        this.jdmbNo = jdmbNo;
        this.nodifySetFiled("jdmbNo", jdmbNo);
        return this;
    }

    public String getJdmbNo() {
        this.nodifyGetFiled("jdmbNo");
        return jdmbNo;
    }

    public OmsOrderJdmbEntity setConsigneeCode(String consigneeCode) {
        this.consigneeCode = consigneeCode;
        this.nodifySetFiled("consigneeCode", consigneeCode);
        return this;
    }

    public String getConsigneeCode() {
        this.nodifyGetFiled("consigneeCode");
        return consigneeCode;
    }

    public OmsOrderJdmbEntity setShipper(String shipper) {
        this.shipper = shipper;
        this.nodifySetFiled("shipper", shipper);
        return this;
    }

    public String getShipper() {
        this.nodifyGetFiled("shipper");
        return shipper;
    }

    public OmsOrderJdmbEntity setReceiver(String receiver) {
        this.receiver = receiver;
        this.nodifySetFiled("receiver", receiver);
        return this;
    }

    public String getReceiver() {
        this.nodifyGetFiled("receiver");
        return receiver;
    }

    public OmsOrderJdmbEntity setOpType(String opType) {
        this.opType = opType;
        this.nodifySetFiled("opType", opType);
        return this;
    }

    public String getOpType() {
        this.nodifyGetFiled("opType");
        return opType;
    }

    public OmsOrderJdmbEntity setMbType(String mbType) {
        this.mbType = mbType;
        this.nodifySetFiled("mbType", mbType);
        return this;
    }

    public String getMbType() {
        this.nodifyGetFiled("mbType");
        return mbType;
    }

    public OmsOrderJdmbEntity setIsUse(String isUse) {
        this.isUse = isUse;
        this.nodifySetFiled("isUse", isUse);
        return this;
    }

    public String getIsUse() {
        this.nodifyGetFiled("isUse");
        return isUse;
    }

    public OmsOrderJdmbEntity setProductCode(String productCode) {
        this.productCode = productCode;
        this.nodifySetFiled("productCode", productCode);
        return this;
    }

    public String getProductCode() {
        this.nodifyGetFiled("productCode");
        return productCode;
    }

    public OmsOrderJdmbEntity setFwlxCode(String fwlxCode) {
        this.fwlxCode = fwlxCode;
        this.nodifySetFiled("fwlxCode", fwlxCode);
        return this;
    }

    public String getFwlxCode() {
        this.nodifyGetFiled("fwlxCode");
        return fwlxCode;
    }

    public OmsOrderJdmbEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public OmsOrderJdmbEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public OmsOrderJdmbEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public OmsOrderJdmbEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public OmsOrderJdmbEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public OmsOrderJdmbEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public OmsOrderJdmbEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public OmsOrderJdmbEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public OmsOrderJdmbEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public OmsOrderJdmbEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public OmsOrderJdmbEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public OmsOrderJdmbEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public OmsOrderJdmbEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public OmsOrderJdmbEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public OmsOrderJdmbEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public OmsOrderJdmbEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public OmsOrderJdmbEntity setJdNodeCode(String jdNodeCode) {
        this.jdNodeCode = jdNodeCode;
        this.nodifySetFiled("jdNodeCode", jdNodeCode);
        return this;
    }

    public String getJdNodeCode() {
        this.nodifyGetFiled("jdNodeCode");
        return jdNodeCode;
    }

    public OmsOrderJdmbEntity setJdNodeName(String jdNodeName) {
        this.jdNodeName = jdNodeName;
        this.nodifySetFiled("jdNodeName", jdNodeName);
        return this;
    }

    public String getJdNodeName() {
        this.nodifyGetFiled("jdNodeName");
        return jdNodeName;
    }

    public OmsOrderJdmbEntity setMbmc(String mbmc) {
        this.mbmc = mbmc;
        this.nodifySetFiled("mbmc", mbmc);
        return this;
    }

    public String getMbmc() {
        this.nodifyGetFiled("mbmc");
        return mbmc;
    }

}
