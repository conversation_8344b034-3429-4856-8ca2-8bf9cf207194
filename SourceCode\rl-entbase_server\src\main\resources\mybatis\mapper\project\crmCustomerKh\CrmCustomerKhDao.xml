<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.crmCustomerKh.dao.CrmCustomerKhDao">
    <resultMap type="CrmCustomerKhEntity" id="CrmCustomerKhResult">
        <result property="guid" column="GUID"/>
        <result property="customerCode" column="CUSTOMER_CODE"/>
        <result property="khly" column="KHLY"/>
        <result property="ywhbjb" column="YWHBJB"/>
        <result property="saleUser" column="SALE_USER"/>
        <result property="skDateline" column="SK_DATELINE"/>
        <result property="payDateline" column="PAY_DATELINE"/>
        <result property="creditLimit" column="CREDIT_LIMIT"/>
        <result property="accountMode" column="ACCOUNT_MODE"/>
        <result property="payMode" column="PAY_MODE"/>
        <result property="jdFreeze" column="JD_FREEZE"/>
        <result property="kpCycle" column="KP_CYCLE"/>
        <result property="kpDateline" column="KP_DATELINE"/>
        <result property="accountKh" column="ACCOUNT_KH"/>
        <result property="bankKh" column="BANK_KH"/>
        <result property="taxNoKh" column="TAX_NO_KH"/>
        <result property="taxValKh" column="TAX_VAL_KH"/>
        <result property="invTypeKh" column="INV_TYPE_KH"/>
        <result property="jkCycle" column="JK_CYCLE"/>
        <result property="payFreeze" column="PAY_FREEZE"/>
        <result property="tel" column="TEL"/>
        <result property="address" column="ADDRESS"/>
        <result property="hzfwCodeKh" column="HZFW_CODE_KH"/>
        <result property="hzfwNameKh" column="HZFW_NAME_KH"/>
        <result property="memoKh" column="MEMO_KH"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="accountMemo" column="ACCOUNT_MEMO"/>
        <result property="sprzz" column="SPRZZ"/>
        <result property="orderType" column="ORDER_TYPE"/>
        <result property="beginDate" column="BEGIN_DATE"/>
        <result property="endDate" column="END_DATE"/>
    </resultMap>

    <sql id="selectCrmCustomerKhEntityVo">
        select
            GUID,
            CUSTOMER_CODE,
            KHLY,
            YWHBJB,
            SALE_USER,
            SK_DATELINE,
            PAY_DATELINE,
            CREDIT_LIMIT,
            ACCOUNT_MODE,
            PAY_MODE,
            JD_FREEZE,
            KP_CYCLE,
            KP_DATELINE,
            ACCOUNT_KH,
            BANK_KH,
            TAX_NO_KH,
            TAX_VAL_KH,
            INV_TYPE_KH,
            JK_CYCLE,
            PAY_FREEZE,
            TEL,
            ADDRESS,
            HZFW_CODE_KH,
            HZFW_NAME_KH,
            MEMO_KH,
            CREATE_USER,
            CREATE_USER_NAME,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_USER_NAME,
            UPDATE_DATE,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            ACCOUNT_MEMO,
            SPRZZ,
            ORDER_TYPE,
            BEGIN_DATE,
            END_DATE
        from CRM_CUSTOMER_KH
    </sql>
</mapper>