package com.eci.project.fzgjBdServiceItem.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 服务项目对象 FZGJ_BD_SERVICE_ITEM
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-04-07
*/
@ApiModel("服务项目")
@TableName("FZGJ_BD_SERVICE_ITEM")
@FieldNameConstants
public class FzgjBdServiceItemEntity extends EciBaseEntity{
    /**
    * GUID
    */
    @ApiModelProperty("GUID(72)")
    @TableId("GUID")
    private String guid;

    /**
    * 服务项目代码
    */
    @ApiModelProperty("服务项目代码(40)")
    @TableField("CODE")
    private String code;

    /**
    * 服务项目名称
    */
    @ApiModelProperty("服务项目名称(200)")
    @TableField("NAME")
    private String name;

    /**
    * 状态
    */
    @ApiModelProperty("状态(1)")
    @TableField("STATUS")
    private String status;

    /**
    * 顺序
    */
    @ApiModelProperty("顺序(22)")
    @TableField("SEQ")
    private Float seq;

    /**
    * 备注
    */
    @ApiModelProperty("备注(1,000)")
    @TableField("MEMO")
    private String memo;

    /**
    * CREATE_DATE
    */
    @ApiModelProperty("CREATE_DATE(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("CREATE_DATE开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("CREATE_DATE结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * CREATE_USER
    */
    @ApiModelProperty("CREATE_USER(72)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * UPDATE_DATE
    */
    @ApiModelProperty("UPDATE_DATE(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("UPDATE_DATE开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("UPDATE_DATE结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * UPDATE_USER
    */
    @ApiModelProperty("UPDATE_USER(72)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 集团代码
    */
    @ApiModelProperty("集团代码(72)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 集团名称
    */
    @ApiModelProperty("集团名称(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 所属企业
    */
    @ApiModelProperty("所属企业(36)")
    @TableField("OWNED_COMPANY")
    private String ownedCompany;

    /**
    * 父级ID
    */
    @ApiModelProperty("父级ID(36)")
    @TableField("PARENTID")
    private String parentid;

    /**
    * 所属服务id
    */
    @ApiModelProperty("所属服务id(50)")
    @TableField("OWNED_SERVICE")
    private String ownedService;

    /**
    * CREATE_USER
    */
    @ApiModelProperty("CREATE_USER(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * UPDATE_USER
    */
    @ApiModelProperty("UPDATE_USER(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 用户自定义控件页面url
    */
    @ApiModelProperty("用户自定义控件页面url(200)")
    @TableField("PAGE_URL")
    private String pageUrl;

    /**
    * 选择类型：是否单选
    */
    @ApiModelProperty("选择类型：是否单选(1)")
    @TableField("SELECT_TYPE")
    private String selectType;

    /**
    * 运输方式
    */
    @ApiModelProperty("运输方式(10)")
    @TableField("YSFS")
    private String ysfs;

    /**
    * 平台作业系统代码
    */
    @ApiModelProperty("平台作业系统代码(10)")
    @TableField("SYS_CODE")
    private String sysCode;

    /**
    * 接单单据
    */
    @ApiModelProperty("接单单据(100)")
    @TableField("JD_BILL")
    private String jdBill;

    /**
    * 作业单据
    */
    @ApiModelProperty("作业单据(100)")
    @TableField("CZ_BILL")
    private String czBill;

    /**
    * 作业跟踪反馈url
    */
    @ApiModelProperty("作业跟踪反馈url(200)")
    @TableField("FEEDBACK_URL")
    private String feedbackUrl;

    /**
    * 要作业
    */
    @ApiModelProperty("要作业(1)")
    @TableField("IS_ZY")
    private String isZy;

    /**
    * 英文名称，仅用来存储
    */
    @ApiModelProperty("英文名称，仅用来存储(50)")
    @TableField("EN_NAME")
    private String enName;

    /**
    * 客户自助下单可选
    */
    @ApiModelProperty("客户自助下单可选(1)")
    @TableField("IS_CSC")
    private String isCsc;

    /**
    * 默认为外包
    */
    @ApiModelProperty("默认为外包(1)")
    @TableField("IS_WB")
    private String isWb;

    /**
    * 不接单只作业
    */
    @ApiModelProperty("不接单只作业(1)")
    @TableField("IS_ZY_NO_JD")
    private String isZyNoJd;

    /**
    * 作业数据齐全后是否允许修改
    */
    @ApiModelProperty("作业数据齐全后是否允许修改(1)")
    @TableField("IS_SAVE")
    private String isSave;

    /**
    * 接单服务项目
    */
    @ApiModelProperty("接单服务项目(50)")
    @TableField("JD_FWXM")
    private String jdFwxm;

    /**
    * 进出标志
    */
    @ApiModelProperty("进出标志(20)")
    @TableField("IE_TYPE")
    private String ieType;

    /**
    * 企业报关类型
    */
    @ApiModelProperty("企业报关类型(50)")
    @TableField("BG_TYPE")
    private String bgType;

    /**
    * 是否区内企业
    */
    @ApiModelProperty("是否区内企业(1)")
    @TableField("IS_QN")
    private String isQn;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public FzgjBdServiceItemEntity() {
        this.setSubClazz(FzgjBdServiceItemEntity.class);
    }

    public FzgjBdServiceItemEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public FzgjBdServiceItemEntity setCode(String code) {
        this.code = code;
        this.nodifySetFiled("code", code);
        return this;
    }

    public String getCode() {
        this.nodifyGetFiled("code");
        return code;
    }

    public FzgjBdServiceItemEntity setName(String name) {
        this.name = name;
        this.nodifySetFiled("name", name);
        return this;
    }

    public String getName() {
        this.nodifyGetFiled("name");
        return name;
    }

    public FzgjBdServiceItemEntity setStatus(String status) {
        this.status = status;
        this.nodifySetFiled("status", status);
        return this;
    }

    public String getStatus() {
        this.nodifyGetFiled("status");
        return status;
    }

    public FzgjBdServiceItemEntity setSeq(Float seq) {
        this.seq = seq;
        this.nodifySetFiled("seq", seq);
        return this;
    }

    public Float getSeq() {
        this.nodifyGetFiled("seq");
        return seq;
    }

    public FzgjBdServiceItemEntity setMemo(String memo) {
        this.memo = memo;
        this.nodifySetFiled("memo", memo);
        return this;
    }

    public String getMemo() {
        this.nodifyGetFiled("memo");
        return memo;
    }

    public FzgjBdServiceItemEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public FzgjBdServiceItemEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public FzgjBdServiceItemEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public FzgjBdServiceItemEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public FzgjBdServiceItemEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public FzgjBdServiceItemEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public FzgjBdServiceItemEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public FzgjBdServiceItemEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public FzgjBdServiceItemEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public FzgjBdServiceItemEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public FzgjBdServiceItemEntity setOwnedCompany(String ownedCompany) {
        this.ownedCompany = ownedCompany;
        this.nodifySetFiled("ownedCompany", ownedCompany);
        return this;
    }

    public String getOwnedCompany() {
        this.nodifyGetFiled("ownedCompany");
        return ownedCompany;
    }

    public FzgjBdServiceItemEntity setParentid(String parentid) {
        this.parentid = parentid;
        this.nodifySetFiled("parentid", parentid);
        return this;
    }

    public String getParentid() {
        this.nodifyGetFiled("parentid");
        return parentid;
    }

    public FzgjBdServiceItemEntity setOwnedService(String ownedService) {
        this.ownedService = ownedService;
        this.nodifySetFiled("ownedService", ownedService);
        return this;
    }

    public String getOwnedService() {
        this.nodifyGetFiled("ownedService");
        return ownedService;
    }

    public FzgjBdServiceItemEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public FzgjBdServiceItemEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public FzgjBdServiceItemEntity setPageUrl(String pageUrl) {
        this.pageUrl = pageUrl;
        this.nodifySetFiled("pageUrl", pageUrl);
        return this;
    }

    public String getPageUrl() {
        this.nodifyGetFiled("pageUrl");
        return pageUrl;
    }

    public FzgjBdServiceItemEntity setSelectType(String selectType) {
        this.selectType = selectType;
        this.nodifySetFiled("selectType", selectType);
        return this;
    }

    public String getSelectType() {
        this.nodifyGetFiled("selectType");
        return selectType;
    }

    public FzgjBdServiceItemEntity setYsfs(String ysfs) {
        this.ysfs = ysfs;
        this.nodifySetFiled("ysfs", ysfs);
        return this;
    }

    public String getYsfs() {
        this.nodifyGetFiled("ysfs");
        return ysfs;
    }

    public FzgjBdServiceItemEntity setSysCode(String sysCode) {
        this.sysCode = sysCode;
        this.nodifySetFiled("sysCode", sysCode);
        return this;
    }

    public String getSysCode() {
        this.nodifyGetFiled("sysCode");
        return sysCode;
    }

    public FzgjBdServiceItemEntity setJdBill(String jdBill) {
        this.jdBill = jdBill;
        this.nodifySetFiled("jdBill", jdBill);
        return this;
    }

    public String getJdBill() {
        this.nodifyGetFiled("jdBill");
        return jdBill;
    }

    public FzgjBdServiceItemEntity setCzBill(String czBill) {
        this.czBill = czBill;
        this.nodifySetFiled("czBill", czBill);
        return this;
    }

    public String getCzBill() {
        this.nodifyGetFiled("czBill");
        return czBill;
    }

    public FzgjBdServiceItemEntity setFeedbackUrl(String feedbackUrl) {
        this.feedbackUrl = feedbackUrl;
        this.nodifySetFiled("feedbackUrl", feedbackUrl);
        return this;
    }

    public String getFeedbackUrl() {
        this.nodifyGetFiled("feedbackUrl");
        return feedbackUrl;
    }

    public FzgjBdServiceItemEntity setIsZy(String isZy) {
        this.isZy = isZy;
        this.nodifySetFiled("isZy", isZy);
        return this;
    }

    public String getIsZy() {
        this.nodifyGetFiled("isZy");
        return isZy;
    }

    public FzgjBdServiceItemEntity setEnName(String enName) {
        this.enName = enName;
        this.nodifySetFiled("enName", enName);
        return this;
    }

    public String getEnName() {
        this.nodifyGetFiled("enName");
        return enName;
    }

    public FzgjBdServiceItemEntity setIsCsc(String isCsc) {
        this.isCsc = isCsc;
        this.nodifySetFiled("isCsc", isCsc);
        return this;
    }

    public String getIsCsc() {
        this.nodifyGetFiled("isCsc");
        return isCsc;
    }

    public FzgjBdServiceItemEntity setIsWb(String isWb) {
        this.isWb = isWb;
        this.nodifySetFiled("isWb", isWb);
        return this;
    }

    public String getIsWb() {
        this.nodifyGetFiled("isWb");
        return isWb;
    }

    public FzgjBdServiceItemEntity setIsZyNoJd(String isZyNoJd) {
        this.isZyNoJd = isZyNoJd;
        this.nodifySetFiled("isZyNoJd", isZyNoJd);
        return this;
    }

    public String getIsZyNoJd() {
        this.nodifyGetFiled("isZyNoJd");
        return isZyNoJd;
    }

    public FzgjBdServiceItemEntity setIsSave(String isSave) {
        this.isSave = isSave;
        this.nodifySetFiled("isSave", isSave);
        return this;
    }

    public String getIsSave() {
        this.nodifyGetFiled("isSave");
        return isSave;
    }

    public FzgjBdServiceItemEntity setJdFwxm(String jdFwxm) {
        this.jdFwxm = jdFwxm;
        this.nodifySetFiled("jdFwxm", jdFwxm);
        return this;
    }

    public String getJdFwxm() {
        this.nodifyGetFiled("jdFwxm");
        return jdFwxm;
    }

    public FzgjBdServiceItemEntity setIeType(String ieType) {
        this.ieType = ieType;
        this.nodifySetFiled("ieType", ieType);
        return this;
    }

    public String getIeType() {
        this.nodifyGetFiled("ieType");
        return ieType;
    }

    public FzgjBdServiceItemEntity setBgType(String bgType) {
        this.bgType = bgType;
        this.nodifySetFiled("bgType", bgType);
        return this;
    }

    public String getBgType() {
        this.nodifyGetFiled("bgType");
        return bgType;
    }

    public FzgjBdServiceItemEntity setIsQn(String isQn) {
        this.isQn = isQn;
        this.nodifySetFiled("isQn", isQn);
        return this;
    }

    public String getIsQn() {
        this.nodifyGetFiled("isQn");
        return isQn;
    }

}
