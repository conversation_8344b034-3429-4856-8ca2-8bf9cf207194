package com.eci.project.fzgjCrmContractFileType.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjCrmContractFileType.entity.FzgjCrmContractFileTypeEntity;

import org.springframework.stereotype.Service;


/**
* 合同附件类型Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-03-19
*/
@Service
public class FzgjCrmContractFileTypeVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjCrmContractFileTypeEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjCrmContractFileTypeEntity entity, BusinessType businessType) {

    }

}
