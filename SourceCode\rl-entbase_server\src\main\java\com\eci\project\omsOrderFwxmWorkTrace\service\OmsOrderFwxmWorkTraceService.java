package com.eci.project.omsOrderFwxmWorkTrace.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.Extensions;
import com.eci.common.db.DBHelper;
import com.eci.common.enums.Enums;
import com.eci.common.utils.CommonLib;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.exception.BaseException;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjTaskLimitation.dao.FzgjTaskLimitationDao;
import com.eci.project.fzgjTaskLimitation.entity.FzgjTaskLimitationEntity;
import com.eci.project.omsOrderFwxmWork.dao.OmsOrderFwxmWorkDao;
import com.eci.project.omsOrderFwxmWork.entity.OmsOrderFwxmWorkEntity;
import com.eci.project.omsOrderFwxmWorkTrace.dao.OmsOrderFwxmWorkTraceDao;
import com.eci.project.omsOrderFwxmWorkTrace.entity.LinkColorEntity;
import com.eci.project.omsOrderFwxmWorkTrace.entity.OmsOrderFwxmWorkInfoEntity;
import com.eci.project.omsOrderFwxmWorkTrace.entity.OmsOrderFwxmWorkTraceEntity;
import com.eci.project.omsOrderFwxmWorkTrace.entity.OrderTraceLinkEntity;
import com.eci.project.omsOrderFwxmWorkTrace.validate.OmsOrderFwxmWorkTraceVal;
import com.eci.sso.role.entity.UserContext;
import com.eci.wu.core.EntityBase;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;


/**
 * 作业跟踪Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-04-22
 */
@Service
@Slf4j
public class OmsOrderFwxmWorkTraceService implements EciBaseService<OmsOrderFwxmWorkTraceEntity> {

    CommonLib cmn = CommonLib.getInstance();

    @Autowired
    private OmsOrderFwxmWorkTraceDao omsOrderFwxmWorkTraceDao;

    @Autowired
    private OmsOrderFwxmWorkTraceVal omsOrderFwxmWorkTraceVal;

    @Autowired
    private OmsOrderFwxmWorkDao omsOrderFwxmWorkDao;

    @Autowired
    private FzgjTaskLimitationDao fzgjTaskLimitationDao;

    @Autowired
    private Extensions extensions;

    /**
     * 服务项目找上级服务
     **/
    public String parseServiceCode(String pCode) {

        int x = 3;
        StringBuilder returnName = new StringBuilder();
        String serviceName = "";

        while (true) {

            if (x > pCode.length()) {
                break;
            }

            String currentCode = pCode.substring(0, Math.min(x, pCode.length()));
            String sql;
            List<EntityBase> list = null;
            if (x == 3) {
                sql = " SELECT NAME FROM FZGJ_BD_SERVICE_TYPE  WHERE CODE = " + cmn.SQLQ(currentCode);
                list = DBHelper.selectList(sql, EntityBase.class);
            } else {
                sql = " SELECT NAME FROM FZGJ_BD_SERVICE_ITEM_PT  WHERE CODE = " + cmn.SQLQ(currentCode);
                list = DBHelper.selectList(sql, EntityBase.class);
            }

            if (list != null && list.size() > 0) {
                serviceName = list.get(0).getString("NAME");
            }

            // 截取 | 之前的内容
            if (serviceName != null && serviceName.contains("|")) {
                serviceName = serviceName.split("\\|")[0];
            }

            if (x == 3) {
                returnName.append(serviceName);
            } else if (serviceName != null && !serviceName.isEmpty()) {
                returnName.append(" > ").append(serviceName);
            }

            x += 3;
        }


        return returnName.toString();
    }

    @Override
    public TgPageInfo queryPageList(OmsOrderFwxmWorkTraceEntity entity) {
        EciQuery<OmsOrderFwxmWorkTraceEntity> eciQuery = EciQuery.buildQuery(entity);
        List<OmsOrderFwxmWorkTraceEntity> entities = omsOrderFwxmWorkTraceDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderFwxmWorkTraceEntity save(OmsOrderFwxmWorkTraceEntity entity) {
        // 返回实体对象
        OmsOrderFwxmWorkTraceEntity omsOrderFwxmWorkTraceEntity = null;
        omsOrderFwxmWorkTraceVal.saveValidate(entity, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            omsOrderFwxmWorkTraceEntity = omsOrderFwxmWorkTraceDao.insertOne(entity);
        } else {
            entity.setUpdateDate(new java.util.Date());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            omsOrderFwxmWorkTraceEntity = omsOrderFwxmWorkTraceDao.updateByEntityId(entity);

            if (entity.getActualOkDate() != null) {
                // 1-#region 调用存储过程-固化
                List<OmsOrderFwxmWorkTraceEntity> list = omsOrderFwxmWorkTraceDao.select()
                        .eq(OmsOrderFwxmWorkTraceEntity::getGuid, entity.getGuid())
                        .list();
                if (list != null && list.size() > 0) {
                    extensions.addOmsGh(list.get(0).getOrderNo());
                }
            }
        }

        return omsOrderFwxmWorkTraceEntity;
    }


    /**
     * 批量保存实际完成时间
     **/


    @Override
    public List<OmsOrderFwxmWorkTraceEntity> selectList(OmsOrderFwxmWorkTraceEntity entity) {
        return omsOrderFwxmWorkTraceDao.selectList(entity);
    }

    @Override
    public OmsOrderFwxmWorkTraceEntity selectOneById(Serializable id) {
        return omsOrderFwxmWorkTraceDao.selectById(id);
    }

    @Override
    public void insertBatch(List<OmsOrderFwxmWorkTraceEntity> list) {
        omsOrderFwxmWorkTraceDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return omsOrderFwxmWorkTraceDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return omsOrderFwxmWorkTraceDao.deleteById(id);
    }

    /// <summary>
    /// 订单下所有协作任务跟踪新增
    /// </summary>
    /// <param name="context"></param>
    /// <param name="orderNo">订单编号</param>
    public void workTraceAllSave(String orderNo) {

        List<OmsOrderFwxmWorkEntity> workList = omsOrderFwxmWorkDao.select().eq(OmsOrderFwxmWorkEntity::getGroupCode, UserContext.getUserInfo().getCompanyCode())
                .eq(OmsOrderFwxmWorkEntity::getOrderNo, orderNo)
                .list();

        List<OmsOrderFwxmWorkTraceEntity> listTraceOld = omsOrderFwxmWorkTraceDao.select()
                .eq(OmsOrderFwxmWorkTraceEntity::getGroupCode, UserContext.getUserInfo().getCompanyCode())
                .eq(OmsOrderFwxmWorkTraceEntity::getOrderNo, orderNo)
                .list();

        QueryWrapper deleteOrderFwxmWorkTraceWrapper = new QueryWrapper();
        deleteOrderFwxmWorkTraceWrapper.eq("GROUP_CODE", UserContext.getUserInfo().getCompanyCode());
        deleteOrderFwxmWorkTraceWrapper.eq("ORDER_NO", orderNo);
        omsOrderFwxmWorkTraceDao.delete(deleteOrderFwxmWorkTraceWrapper);

        if (workList != null && workList.size() > 0) {

            List<OmsOrderFwxmWorkTraceEntity> listSave = new ArrayList<>();

            for (OmsOrderFwxmWorkEntity work : workList) {
                List<FzgjTaskLimitationEntity> tacxLimitation = fzgjTaskLimitationDao.select()
                        .eq(FzgjTaskLimitationEntity::getGroupCode, UserContext.getUserInfo().getCompanyCode())
                        .eq(FzgjTaskLimitationEntity::getTargetCode, work.getFwxmCode())
                        .list();
                if (tacxLimitation.size() == 0) {
                    throw new BaseException("服务项目作业环节未维护");
                }

                for (FzgjTaskLimitationEntity tacx : tacxLimitation) {

                    OmsOrderFwxmWorkTraceEntity trace = new OmsOrderFwxmWorkTraceEntity();

                    OmsOrderFwxmWorkTraceEntity traceOld = listTraceOld.stream()
                            .filter(s -> s.getWorkNo().equals(work.getWorkNo())
                                    && s.getLinkCode().equals(tacx.getCode()))
                            .findFirst()
                            .orElse(null);

                    if (traceOld != null) {
                        trace.setPlanOkDate(traceOld.getPlanOkDate());
                        trace.setActualOkDate(traceOld.getActualOkDate());
                    }

                    trace.setGuid(IdWorker.get32UUID());
                    trace.setWorkNo(work.getWorkNo());
                    trace.setOrderNo(work.getOrderNo());
                    trace.setPreNo(work.getPreNo());
                    trace.setLinkSeq(tacx.getSeq());
                    trace.setLinkCode(tacx.getCode());
                    trace.setJobd(tacx.getMemo());
                    trace.setWorkGuid(work.getGuid());
                    trace.setBizRegId(work.getBizRegId());
                    trace.setCreateDate(new java.util.Date());
                    trace.setUpdateDate(new java.util.Date());
                    trace.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
                    trace.setCreateUserName(UserContext.getUserInfo().getTrueName());
                    trace.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
                    trace.setUpdateUserName(UserContext.getUserInfo().getTrueName());
                    trace.setGroupCode(UserContext.getUserInfo().getCompanyCode());
                    trace.setGroupName(UserContext.getUserInfo().getCompanyName());
                    trace.setNodeCode(UserContext.getUserInfo().getDeptCode());
                    trace.setNodeName(UserContext.getUserInfo().getDeptName());
                    trace.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
                    trace.setCompanyName(UserContext.getUserInfo().getCompanyName());

                    listSave.add(trace);
                }
            }

            if (listSave.size() > 0) {
                omsOrderFwxmWorkTraceDao.insertList(listSave);
            }

        }
    }

    /***
     * 渲染作业环节标识
     * */
    public List<OrderTraceLinkEntity> traceLinkAllByOrderNoSearch(String orderNo) {

        List<OrderTraceLinkEntity> orderTraceLinks = traceLinkSearchDiv(orderNo);

        for (OrderTraceLinkEntity item : orderTraceLinks) {
            if (Enums.YNStatus.Y.getCode().equals(item.getIsException())) {
                item.setLinkColor(LinkColorEntity.RED);
            } else {
                if (item.getPlanOkDate() != null) {  // 计划完成时间有没有值
                    if (item.getActualOkDate() == null) {  // 实际完成时间有没有值
                        if (LocalDateTime.now().isAfter(item.getPlanOkDate())) {
                            item.setLinkColor(LinkColorEntity.YELLOW);
                        }
                    } else {
                        if (!item.getActualOkDate().isAfter(item.getPlanOkDate())) {   // 实际完成时间小于等于计划完成时间
                            item.setLinkColor(LinkColorEntity.GREEN);
                        } else { // 实际完成时间大于计划完成时间
                            item.setLinkColor(LinkColorEntity.YELLOW);
                        }
                    }
                } else {
                    if (item.getActualOkDate() != null) {
                        item.setLinkColor(LinkColorEntity.GREEN);
                    }
                }
            }
        }

        return orderTraceLinks;
    }

    /**
     *
     **/
    public List<OrderTraceLinkEntity> traceLinkSearchDiv(String orderNo) {

        String sql = "SELECT\n" +
                "  A.GUID,\n" +
                "  B.TASK_SEQ,\n" +
                "  B.BIZ_REG_ID,\n" +
                "  B.WORK_NO,\n" +
                "  B.FWXM_CODE,\n" +
                "  A.ACTUAL_OK_DATE,\n" +
                "  A.IS_EXCEPTION,\n" +
                "  A.PLAN_OK_DATE,\n" +
                "  (SELECT CASE WHEN INSTR(NAME,'|')>0 THEN SUBSTR(NAME,0, INSTR(NAME,'|')-1 ) ELSE NAME END  FROM FZGJ_BD_SERVICE_TYPE WHERE CODE = B.FWLX_CODE AND ROWNUM=1) FWLX_NAME,\n" +
                "  A.LINK_CODE,\n" +
                "  (SELECT MAX(PT.NAME)  FROM FZGJ_TASK_LIMITATION_PT PT WHERE PT.STATUS = 'Y' AND PT.CODE = A.LINK_CODE AND ROWNUM=1) LINK_CODE_NAME\n" +
                "   FROM OMS_ORDER_FWXM_WORK B\n" +
                "   LEFT JOIN OMS_ORDER_FWXM_WORK_TRACE A ON A.WORK_NO = B.WORK_NO\n" +
                "   LEFT JOIN OMS_ORDER C ON C.ORDER_NO = B.ORDER_NO\n" +
                "   WHERE B.ORDER_NO = " + cmn.SQLQ(orderNo) + "\n" +
                "   AND A.GROUP_CODE = " + cmn.SQLQ(UserContext.getUserInfo().getCompanyCode()) + "\n" +
                "   AND B.GROUP_CODE = " + cmn.SQLQ(UserContext.getUserInfo().getCompanyCode()) + "\n" +
                "   AND C.GROUP_CODE = " + cmn.SQLQ(UserContext.getUserInfo().getCompanyCode()) + "\n" +
                "   ORDER BY TASK_SEQ,LINK_SEQ ";

        return DBHelper.selectList(sql, OrderTraceLinkEntity.class);
    }

    /// <summary>
    /// 查询订单下所有作业追踪(作业环节)
    /// </summary>
    /// <param name="context"></param>
    /// <param name="orderNo">订单编号</param>
    /// <returns></returns>
    public List<OmsOrderFwxmWorkInfoEntity> traceLinkSearch(String orderNo) {
        // 列表数据
        List<OmsOrderFwxmWorkInfoEntity> list = traceLinkSearchSql(orderNo);

        list.forEach(item -> {
            String fwxmCode = item.getFwxmCode();
            String linkServiceName = parseServiceCode(fwxmCode);
            item.setFwxmCodeName(linkServiceName);
        });

        return list;
    }

    /***
     *批量保存实际完成时间
     * **/
    @Transactional
    public boolean plSave(List<OmsOrderFwxmWorkTraceEntity> list) {

        for (OmsOrderFwxmWorkTraceEntity item : list) {
            if (item.getActualOkDate() != null) {
                OmsOrderFwxmWorkTraceEntity trace = new OmsOrderFwxmWorkTraceEntity();
                trace.setGuid(item.getGuid());
                trace.setActualOkDate(item.getActualOkDate());
                trace.setUpdateDate(new java.util.Date());
                trace.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
                trace.setUpdateUserName(UserContext.getUserInfo().getTrueName());
                omsOrderFwxmWorkTraceDao.updateByEntityId(trace);
            }
        }

        return true;
    }

    /// <summary>
    /// 查询订单下所有作业追踪(作业环节)
    /// </summary>
    /// <param name="context"></param>
    /// <param name="orderNo">订单编号</param>
    /// <returns></returns>
    public List<OmsOrderFwxmWorkInfoEntity> traceLinkSearchSql(String orderNo) {

        String companyCode = UserContext.getUserInfo().getCompanyCode();

        String sql = "SELECT  B.UPDATE_DATE,\n" +
                "        B.UPDATE_USER_NAME,\n" +
                "        A.GUID,\n" +
                "        B.TASK_SEQ,\n" +
                "        B.BIZ_REG_ID,\n" +
                "        B.WORK_NO,\n" +
                "        B.FWXM_CODE,\n" +
                "        B.DOC_NO,\n" +
                "        B.SYS_CODE,\n" +
                "        B.SEND_USER_NAME,\n" +
                "        B.XZWT_NO,\n" +
                "        B.ORDER_NO,\n" +
                "       (SELECT FK.FEEDBACK_URL FROM FZGJ_BD_SERVICE_ITEM_FK FK  WHERE  FK.code like B.FWXM_CODE ||'%' AND FK.GROUP_CODE=B.GROUP_CODE) FEEDBACK_URL,\n" +
                "        '' FWXM_CODE_NAME,\n" +
                "        '' LINK_CODE_COLOR,\n" +
                "        (SELECT MAX(CASE WHEN INSTR(NAME,'|')>0 THEN SUBSTR(NAME,0, INSTR(NAME,'|')-1 ) ELSE NAME END)  FROM FZGJ_BD_SERVICE_ITEM_PT ITEMPT WHERE ITEMPT.STATUS = 'Y'  AND ITEMPT.CODE = B.FWXM_CODE) FWXM_NAME,\n" +
                "        (SELECT BILL.NAME FROM FZGJ_BD_BILL BILL  WHERE BILL.STATUS = 'Y'  AND BILL.CODE = B.BILL_CODE AND ROWNUM=1) BILL_CODE_NAME,\n" +
                "        (SELECT DATADETAIL.NAME FROM FZGJ_BASE_DATA_DETAIL DATADETAIL WHERE DATADETAIL.GROUP_CODE='OMS_WORK_STATUS'  AND DATADETAIL.CODE = B.STATUS) STATUS_NAME,\n" +
                "        B.TASK_PRE,\n" +
                "        B.STAGE,B.STATUS,\n" +
                "        A.LINK_SEQ,\n" +
                "        A.LINK_CODE,\n" +
                "        (SELECT MAX(LIMITATIONPT.NAME) FROM FZGJ_TASK_LIMITATION_PT LIMITATIONPT  WHERE LIMITATIONPT.STATUS = 'Y' AND LIMITATIONPT.CODE = A.LINK_CODE) LINK_CODE_NAME, -- 作业环节\n" +
                "        A.PLAN_OK_DATE,\n" +
                "        A.ACTUAL_OK_DATE,\n" +
                "        (SELECT CASE WHEN COUNT(1) > 0 THEN 'Y' ELSE 'N' END FROM FZGJ_EXCEPTION EX WHERE EX.ORDER_NO=" + cmn.SQLQ(orderNo) + " AND EX.LINK_CODE = A.LINK_CODE AND EX.WORK_NO = B.WORK_NO AND EX.GROUP_CODE=" + cmn.SQLQ(companyCode) + " ) IS_EXCEPTION,\n" +
                "        (SELECT CASE WHEN COUNT(1) > 0 THEN 'Y' ELSE 'N' END FROM FZGJ_FILE A WHERE (A.ORDER_NO=C.BIZ_REG_ID)) IS_HAS_FILE,\n" +
                "         B.IS_WB,\n" +
                "        (SELECT MAX(CUSTOME.NAME)  FROM CRM_CUSTOMER CUSTOME WHERE CUSTOME.STATUS='Y' AND CUSTOME.CODE = B.GYS_CODE AND CUSTOME.GROUP_CODE =B.GROUP_CODE ) GYS_CODE_NAME,\n" +
                "        (SELECT NODE.FULLNAME FROM FZGJ_SSO_NODE NODE WHERE NODE.CODE = B.NODE_CODE_NB AND ROWNUM=1) NODE_CODE_NB_NAME,\n" +
                "         B.RESPONSE_CODE,\n" +
                "         B.SEND_DATE,--分发时间\n" +
                "        C.TEL,--联系电话\n" +
                "        C.E_MAIL,\n" +
                "        B.FWLX_CODE,\n" +
                "        (SELECT NAME FROM FZGJ_BASE_DATA_DETAIL DETAIL  WHERE DETAIL.STATUS = 'Y' AND  DETAIL.GROUP_CODE = 'RESPONSE'  AND DETAIL.CODE = B.RESPONSE_CODE) RESPONSE_CODE_NAME,\n" +
                "        (SELECT CASE WHEN INSTR(NAME,'|')>0 THEN SUBSTR(NAME,0, INSTR(NAME,'|')-1 ) ELSE NAME END\n" +
                "         FROM FZGJ_BD_SERVICE_TYPE  SERVICETYPE\n" +
                "         WHERE SERVICETYPE.CODE = B.FWLX_CODE )FWLX_NAME,\n" +
                "        CASE WHEN B.OP_COMPLETE_OK = 'Y' THEN '√' ELSE NULL END AS OP_COMPLETE_OK, --作业完成\n" +
                "        CASE WHEN B.DATA_OK = 'Y' THEN '√' ELSE NULL END AS DATA_OK,   --作业数据齐全\n" +
                "        CASE WHEN C.ARAP_OK = 'Y' THEN '√' ELSE NULL END AS AR_OK,    --应收费用齐全\n" +
                "        CASE WHEN B.ARAP_OK = 'Y' THEN '√' ELSE NULL END AS ARAP_OK    --应付费用齐全\n" +
                "        ,(SELECT COUNT(*) FROM OMS_ORDER_FWXM_WORK WHERE PRE_WORK_NO=B.WORK_NO ) AS NEXT_ORDER_SUM\n" +
                "        ,B.PRE_WORK_NO\n" +
                "        ,C.CONSIGNEE_CODE\n" +
                "        ,(SELECT COUNT(*) FROM FZGJ_BD_SERVICE_ITEM SERVICEITEM WHERE  SERVICEITEM.CODE =B.FWLX_CODE  AND SERVICEITEM.GROUP_CODE=" + cmn.SQLQ(companyCode) + " AND SERVICEITEM.IS_SAVE='Y' ) COUNT\n" +
                "        FROM OMS_ORDER_FWXM_WORK B\n" +
                "        LEFT JOIN OMS_ORDER_FWXM_WORK_TRACE A ON A.WORK_NO = B.WORK_NO\n" +
                "        LEFT JOIN OMS_ORDER C ON C.ORDER_NO = B.ORDER_NO\n" +
                "        WHERE B.ORDER_NO = " + cmn.SQLQ(orderNo) + "\n" +
                "        AND A.GROUP_CODE = " + cmn.SQLQ(companyCode) + "\n" +
                "        AND B.GROUP_CODE = " + cmn.SQLQ(companyCode) + "\n" +
                "        AND C.GROUP_CODE = " + cmn.SQLQ(companyCode) + "\n" +
                "        ORDER BY TASK_SEQ,LINK_SEQ";

        return DBHelper.selectList(sql, OmsOrderFwxmWorkInfoEntity.class);
    }


}