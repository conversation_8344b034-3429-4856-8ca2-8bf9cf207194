package com.eci.project.omsOrderFwxmWorkFkHzqd.service;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.Extensions;
import com.eci.common.ZsrJson;
import com.eci.common.enums.OrderEnum;
import com.eci.common.utils.CommonLib;
import com.eci.common.validations.ZsrValidationUtil;
import com.eci.crud.service.EciBaseService;
import com.eci.exception.BaseException;
import com.eci.project.omsFile.entity.OmsFileEntity;
import com.eci.project.omsFile.service.OmsFileService;
import com.eci.project.omsOrder.dao.OmsOrderDao;
import com.eci.project.omsOrder.entity.OmsOrderEntity;
import com.eci.project.omsOrder.service.OmsOrderService;
import com.eci.project.omsOrderFwxmWork.dao.OmsOrderFwxmWorkDao;
import com.eci.project.omsOrderFwxmWork.entity.OmsOrderFwxmWorkEntity;
import com.eci.project.omsOrderFwxmWorkFk.dao.OmsOrderFwxmWorkFkDao;
import com.eci.project.omsOrderFwxmWorkFk.validate.OmsOrderFwxmWorkFkVal;
import com.eci.project.omsOrderFwxmWorkFkHfd.dao.OmsOrderFwxmWorkFkHfdDao;
import com.eci.project.omsOrderFwxmWorkFkHzqd.dao.OmsOrderFwxmWorkFkHzqdDao;
import com.eci.project.omsOrderFwxmWorkFkHzqd.entity.*;
import com.eci.project.omsOrderFwxmWorkFkHzqd.validate.OmsOrderFwxmWorkFkHzqdVal;
import com.eci.project.omsOrderFwxmWorkTrace.dao.OmsOrderFwxmWorkTraceDao;
import com.eci.project.omsOrderFwxmWorkTrace.entity.OmsOrderFwxmWorkTraceEntity;
import com.eci.project.omsOrderLog.entity.OmsOrderLogEntity;
import com.eci.project.omsOrderLog.service.OmsOrderLogService;
import com.eci.project.omsReceiveHistory.entity.OmsReceiveHistoryEntity;
import com.eci.project.omsReceiveHistory.service.OmsReceiveHistoryService;
import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;


/**
 * 反馈内容-核注清单Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-06-03
 */
@Service
@Slf4j
public class FeedbackOmsService implements EciBaseService<OmsOrderFwxmWorkFkHzqdEntity> {

    CommonLib cmn = CommonLib.getInstance();

    @Autowired
    private OmsOrderFwxmWorkFkHzqdDao omsOrderFwxmWorkFkHzqdDao;

    @Autowired
    private OmsOrderFwxmWorkFkHzqdVal omsOrderFwxmWorkFkHzqdVal;

    @Autowired
    private OmsOrderFwxmWorkFkVal omsOrderFwxmWorkFkVal;

    @Autowired
    private OmsOrderFwxmWorkFkDao omsOrderFwxmWorkFkDao;

    @Autowired
    private OmsOrderFwxmWorkFkHfdDao omsOrderFwxmWorkFkHfdDao;

    @Autowired
    private OmsOrderService omsOrderService;

    /**
     * 订单服务
     */
    @Autowired
    private OmsOrderDao omsOrderDao;

    /**
     * 记录接收数据的服务
     */
    @Autowired
    private OmsReceiveHistoryService omsReceiveHistoryService;

    /**
     * 附件服务
     */
    @Autowired
    private OmsFileService omsFileService;

    /**
     * 服务项目协作模块
     */
    @Autowired
    private OmsOrderFwxmWorkDao omsOrderFwxmWorkDao;


    /**
     * 作业跟踪（反馈作业节点）
     */
    @Autowired
    private OmsOrderFwxmWorkTraceDao omsOrderFwxmWorkTraceDao;

    /**
     * 扩展类
     **/
    @Autowired
    private Extensions extensions;


    /**
     * 反馈-作业完成-数据保存
     *
     * @param jsonString 请求的字符串
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderFwxmWorkEntity zuoYeWanCheng(String jsonString) {
        // 记录接收内容
        ZsrJson zsrJson = ZsrJson.parse(jsonString);
        OmsReceiveHistoryEntity receiveHistoryEntity = new OmsReceiveHistoryEntity();
        receiveHistoryEntity.setCreatedate(new Date());
        receiveHistoryEntity.setJsonbody(jsonString);
        ZsrJson jsonEntity = null;
        try {
            jsonEntity = zsrJson.check("entity").getJSONObject("entity");
        } catch (Exception e) {
            throw new BaseException("entity 不存在,无法解析", e);
        }
        if (jsonEntity == null) {
            throw new BaseException("entity 解析失败");
        }
        // 获取协作单号
        try {
            receiveHistoryEntity.setWorkno(jsonEntity.check("WORK_NO").getString("WORK_NO"));
        } catch (Exception e) {
            throw new BaseException("WORK_NO 协作任务编号未填写", e);
        }

        // 获取订单号
        try {
            receiveHistoryEntity.setOrderno(jsonEntity.check("ORDER_NO").getString("ORDER_NO"));
        } catch (Exception e) {
            throw new BaseException("ORDER_NO 订单号未填写", e);
        }

        try {
            receiveHistoryEntity.setJsonbody(zsrJson.toCompactString());
            // 保存请求的数据
            omsReceiveHistoryService.save(receiveHistoryEntity, true);
        } catch (Exception e) {
            if (zsrJson.toCompactString().length() > 2000) {
                receiveHistoryEntity.setJsonbody(zsrJson.toCompactString().substring(0, 2000));
            }
            // 重新保存请求的数据
            omsReceiveHistoryService.save(receiveHistoryEntity, true);
        }

        // 获取对接的反馈作业节点数据
        FeedbackOperationCompletion feedbackOperationCompletion = jsonEntity.toObject(FeedbackOperationCompletion.class);

        // 查询数据库的协作任务
        OmsOrderFwxmWorkEntity orderFwxmWorkEntity = omsOrderFwxmWorkDao.select()
                .eq(OmsOrderFwxmWorkEntity::getWorkNo, receiveHistoryEntity.getWorkno())
                .eq(OmsOrderFwxmWorkEntity::getOrderNo, receiveHistoryEntity.getOrderno())
                .one();
        if (orderFwxmWorkEntity == null) {
            throw new BaseException("未找到对应的协作任务");
        }
        OmsOrderEntity orderEntity = omsOrderDao.select()
                .eq(OmsOrderEntity::getOrderNo, orderFwxmWorkEntity.getOrderNo()).one();

        if (feedbackOperationCompletion.getDOC_STATUS() == null) {
            throw new BaseException("单据状态未填写");
        }
        if (feedbackOperationCompletion.getDOC_STATUS().equals("作业完成")) {
            orderEntity.setStage(OrderEnum.OrderStage.QBWC.getCode());
        } else if (feedbackOperationCompletion.getDOC_STATUS().equals("作业数据齐全")) {
            orderEntity.setStage(OrderEnum.OrderStage.BFWC.getCode());
        } else {
            throw new BaseException("不存在的单据状态," + feedbackOperationCompletion.getDOC_STATUS());
        }
        omsOrderDao.updateByEntityId(orderEntity);


        // 操作日志记录
        OmsOrderLogEntity logEntity = new OmsOrderLogEntity();
        logEntity.setOrderNo(receiveHistoryEntity.getOrderno());
        logEntity.setOperName("反馈作业完成");
        logEntity.setDataDetail(feedbackOperationCompletion.getDOC_STATUS());
        omsOrderLogService.writeLog(logEntity);

        return orderFwxmWorkEntity;
    }


    /**
     * 反馈-作业节点-数据保存
     *
     * @param jsonString 请求的字符串
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderFwxmWorkEntity zuoYeJieDian(String jsonString) {
        // 记录接收内容
        ZsrJson zsrJson = ZsrJson.parse(jsonString);
        OmsReceiveHistoryEntity receiveHistoryEntity = new OmsReceiveHistoryEntity();
        receiveHistoryEntity.setCreatedate(new Date());
        receiveHistoryEntity.setJsonbody(jsonString);
        ZsrJson jsonEntity = null;
        try {
            jsonEntity = zsrJson.check("entity").getJSONObject("entity");
        } catch (Exception e) {
            throw new BaseException("entity 不存在,无法解析", e);
        }
        if (jsonEntity == null) {
            throw new BaseException("entity 解析失败");
        }
        // 获取协作单号
        try {
            receiveHistoryEntity.setWorkno(jsonEntity.check("WORK_NO").getString("WORK_NO"));
        } catch (Exception e) {
            throw new BaseException("WORK_NO 协作任务编号未填写", e);
        }

        // 获取订单号
        try {
            receiveHistoryEntity.setOrderno(jsonEntity.check("ORDER_NO").getString("ORDER_NO"));
        } catch (Exception e) {
            throw new BaseException("ORDER_NO 订单号未填写", e);
        }

        try {
            receiveHistoryEntity.setJsonbody(zsrJson.toCompactString());
            // 保存请求的数据
            omsReceiveHistoryService.save(receiveHistoryEntity, true);
        } catch (Exception e) {
            if (zsrJson.toCompactString().length() > 2000) {
                receiveHistoryEntity.setJsonbody(zsrJson.toCompactString().substring(0, 2000));
            }
            // 重新保存请求的数据
            omsReceiveHistoryService.save(receiveHistoryEntity, true);
        }

        // 获取对接的反馈作业节点数据
        FeedbackOperationNode feedbackOperationNode = jsonEntity.toObject(FeedbackOperationNode.class);

        try {
            ZsrValidationUtil.validation(feedbackOperationNode);
        } catch (IllegalAccessException e) {
            throw new BaseException(e.getMessage());
        }

        // 查询数据库的协作任务
        OmsOrderFwxmWorkEntity orderFwxmWorkEntity = omsOrderFwxmWorkDao.select()
                .eq(OmsOrderFwxmWorkEntity::getWorkNo, receiveHistoryEntity.getWorkno())
                .eq(OmsOrderFwxmWorkEntity::getOrderNo, receiveHistoryEntity.getOrderno())
                .one();
        if (orderFwxmWorkEntity == null) {
            throw new BaseException("未找到对应的协作任务");
        }

        OmsOrderFwxmWorkTraceEntity workTraceEntity = omsOrderFwxmWorkTraceDao.select()
                .eq(OmsOrderFwxmWorkTraceEntity::getWorkNo, receiveHistoryEntity.getWorkno())
                .eq(OmsOrderFwxmWorkTraceEntity::getOrderNo, receiveHistoryEntity.getOrderno())
                .eq(OmsOrderFwxmWorkTraceEntity::getLinkCode, feedbackOperationNode.getSTAGE())
                .eq(OmsOrderFwxmWorkTraceEntity::getSysCode, feedbackOperationNode.getSYS_CODE())
                .one();
        if (workTraceEntity == null) {
            workTraceEntity = new OmsOrderFwxmWorkTraceEntity();
            workTraceEntity.setGuid(String.valueOf(IdWorker.getId()));
            workTraceEntity.setWorkNo(receiveHistoryEntity.getWorkno());
            workTraceEntity.setOrderNo(receiveHistoryEntity.getOrderno());
            workTraceEntity.setLinkCode(feedbackOperationNode.getSTAGE());
            workTraceEntity.setSysCode(feedbackOperationNode.getSYS_CODE());
            workTraceEntity.setCreateDate(new Date());
            workTraceEntity.setCreateUser(UserContext.getUserInfo().getTrueName());
            workTraceEntity.setUpdateDate(new Date());
            workTraceEntity.setUpdateUser(UserContext.getUserInfo().getTrueName());
            workTraceEntity.setActualOkDate(feedbackOperationNode.getOK_DATE());
            workTraceEntity.setTrnDate(feedbackOperationNode.getTRN_DATE());
            workTraceEntity.setIsReback(feedbackOperationNode.getIS_REBACK());
            omsOrderFwxmWorkTraceDao.insertOne(workTraceEntity);
        } else {
            workTraceEntity.setUpdateDate(new Date());
            workTraceEntity.setUpdateUser(UserContext.getUserInfo().getTrueName());
            workTraceEntity.setActualOkDate(feedbackOperationNode.getOK_DATE());
            workTraceEntity.setTrnDate(feedbackOperationNode.getTRN_DATE());
            workTraceEntity.setIsReback(feedbackOperationNode.getIS_REBACK());
            omsOrderFwxmWorkTraceDao.updateByEntityId(workTraceEntity);
        }

        // 操作日志记录
        OmsOrderLogEntity logEntity = new OmsOrderLogEntity();
        logEntity.setOrderNo(receiveHistoryEntity.getOrderno());
        logEntity.setOperName("反馈作业节点");
        logEntity.setDataDetail(feedbackOperationNode.getSTAGE());
        omsOrderLogService.writeLog(logEntity);

        return orderFwxmWorkEntity;
    }

    /**
     * 反馈-接单状态-数据保存
     *
     * @param jsonString 请求的字符串
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderFwxmWorkEntity jieDanStatus(String jsonString) {
        // 记录接收内容
        ZsrJson zsrJson = ZsrJson.parse(jsonString);
        OmsReceiveHistoryEntity receiveHistoryEntity = new OmsReceiveHistoryEntity();
        receiveHistoryEntity.setCreatedate(new Date());
        receiveHistoryEntity.setJsonbody(jsonString);
        ZsrJson jsonEntity = null;
        try {
            jsonEntity = zsrJson.check("entity").getJSONObject("entity");
        } catch (Exception e) {
            throw new BaseException("entity 不存在,无法解析", e);
        }
        if (jsonEntity == null) {
            throw new BaseException("entity 解析失败");
        }
        // 获取协作单号
        try {
            receiveHistoryEntity.setWorkno(jsonEntity.check("WORK_NO").getString("WORK_NO"));
        } catch (Exception e) {
            throw new BaseException("WORK_NO 协作任务编号未填写", e);
        }

        // 获取订单号
        try {
            receiveHistoryEntity.setOrderno(jsonEntity.check("ORDER_NO").getString("ORDER_NO"));
        } catch (Exception e) {
            throw new BaseException("ORDER_NO 订单号未填写", e);
        }

        try {
            receiveHistoryEntity.setJsonbody(zsrJson.toCompactString());
            // 保存请求的数据
            omsReceiveHistoryService.save(receiveHistoryEntity, true);
        } catch (Exception e) {
            if (zsrJson.toCompactString().length() > 2000) {
                receiveHistoryEntity.setJsonbody(zsrJson.toCompactString().substring(0, 2000));
            }
            // 重新保存请求的数据
            omsReceiveHistoryService.save(receiveHistoryEntity, true);
        }

        // 获取对接的协作任务数据
        FeedbackOrderAcceptance FeedbackOrderAcceptanceEntity = jsonEntity.toObject(FeedbackOrderAcceptance.class);
        // 查询数据库的协作任务
        OmsOrderFwxmWorkEntity orderFwxmWorkEntity = omsOrderFwxmWorkDao.select()
                .eq(OmsOrderFwxmWorkEntity::getWorkNo, receiveHistoryEntity.getWorkno())
                .eq(OmsOrderFwxmWorkEntity::getOrderNo, receiveHistoryEntity.getOrderno())
                .one();
        if (orderFwxmWorkEntity == null) {
            throw new BaseException("未找到对应的协作任务");
        }
        if (FeedbackOrderAcceptanceEntity.getRESULT() == null) {
            throw new BaseException("受理结果未填写");
        }
        // 找到之后，修改对应协作任务的任务状态，如果是： "确认接单"  或 "拒绝接单"。
        if (FeedbackOrderAcceptanceEntity.getRESULT().equals("确认接单")) {
            orderFwxmWorkEntity.setStatus(OrderEnum.WorkStatus.XZJD.getCode());
        } else if (FeedbackOrderAcceptanceEntity.getRESULT().equals("拒绝接单")) {
            orderFwxmWorkEntity.setStatus(OrderEnum.WorkStatus.TH.getCode());
        } else {
            throw new BaseException("受理结果有误,值应该为 [确认接单或拒绝接单]");
        }
        omsOrderFwxmWorkDao.updateByEntityId(orderFwxmWorkEntity);
        if (FeedbackOrderAcceptanceEntity.getRESULT().equals("确认接单")) {
            updateOrderStatus(orderFwxmWorkEntity);
        }

        // 操作日志记录
        OmsOrderLogEntity logEntity = new OmsOrderLogEntity();
        logEntity.setOrderNo(receiveHistoryEntity.getOrderno());
        logEntity.setOperName("反馈接单状态");
        logEntity.setDataDetail(FeedbackOrderAcceptanceEntity.getRESULT());
        omsOrderLogService.writeLog(logEntity);

        return orderFwxmWorkEntity;
    }

    /**
     * 更新订单状态
     *
     * @param orderFwxmWorkEntity
     */
    private void updateOrderStatus(OmsOrderFwxmWorkEntity orderFwxmWorkEntity) {
        OmsOrderEntity orderEntity = omsOrderDao.select()
                .eq(OmsOrderEntity::getOrderNo, orderFwxmWorkEntity.getOrderNo()).one();
        orderEntity.setStage(OrderEnum.OrderStage.FF.getCode());
        omsOrderDao.updateByEntityId(orderEntity);
    }


    @Autowired
    private OmsOrderLogService omsOrderLogService;

    /**
     * 反馈-业务数据保存
     *
     * @param jsonString 请求的字符串
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderFwxmWorkFkHzqdEntity zuoYeData(String jsonString) {
        // 记录接收内容
        ZsrJson zsrJson = ZsrJson.parse(jsonString);
        OmsReceiveHistoryEntity receiveHistoryEntity = new OmsReceiveHistoryEntity();
        receiveHistoryEntity.setCreatedate(new Date());
        receiveHistoryEntity.setJsonbody(jsonString);
        ZsrJson jsonEntity = null;
        try {
            jsonEntity = zsrJson.check("entity").getJSONObject("entity");
        } catch (Exception e) {
            throw new BaseException("entity 不存在,无法解析", e);
        }
        if (jsonEntity == null) {
            throw new BaseException("entity 解析失败");
        }
        // 获取协作单号
        try {
            receiveHistoryEntity.setWorkno(jsonEntity.check("WORK_NO").getString("WORK_NO"));
        } catch (Exception e) {
            throw new BaseException("WORK_NO 协作任务编号未填写", e);
        }

        // 获取订单号
        try {
            receiveHistoryEntity.setOrderno(jsonEntity.getString("ORDER_NO"));
        } catch (Exception e) {
            // 记录警告日志
            log.warn("ORDER_NO 无法获取: {}", e.getMessage());
        }

        try {
            receiveHistoryEntity.setJsonbody(zsrJson.toCompactString());
            // 保存请求的数据
            omsReceiveHistoryService.save(receiveHistoryEntity, true);
        } catch (Exception e) {
            if (zsrJson.toCompactString().length() > 2000) {
                receiveHistoryEntity.setJsonbody(zsrJson.toCompactString().substring(0, 2000));
            }
            // 重新保存请求的数据
            omsReceiveHistoryService.save(receiveHistoryEntity, true);
        }

        // 查询数据库的协作任务
        OmsOrderFwxmWorkEntity orderFwxmWorkEntity = omsOrderFwxmWorkDao.select()
                .eq(OmsOrderFwxmWorkEntity::getWorkNo, receiveHistoryEntity.getWorkno())
                .one();

        if (orderFwxmWorkEntity == null) {
            throw new BaseException("未找到对应的协作任务");
        }

        // 获取对接的协作任务数据
        FeedbackCollaborationTaskEntity feedbackCollaborationTaskEntity = jsonEntity.toObject(FeedbackCollaborationTaskEntity.class);

        // 给协作任务赋值
        List<OmsOrderFwxmWorkFkHzqdEntity> saveOmsOrderFwxmWorkFkHzqdEntity = getOmsOrderFwxmWorkFkHzqdEntity(orderFwxmWorkEntity, feedbackCollaborationTaskEntity);

        // 返回实体对象
        OmsOrderFwxmWorkFkHzqdEntity omsOrderFwxmWorkFkHzqdEntity = null;
        if (saveOmsOrderFwxmWorkFkHzqdEntity.size() > 0) {
            omsOrderFwxmWorkFkHzqdEntity = saveOmsOrderFwxmWorkFkHzqdEntity.get(0);
        }
        saveOmsOrderFwxmWorkFkHzqdEntity.forEach(entity -> {
            omsOrderFwxmWorkFkHzqdDao.insertOne(entity);
        });

        // 保存附件
        if (feedbackCollaborationTaskEntity.getATTACHMENT_LIST() != null
                && feedbackCollaborationTaskEntity.getATTACHMENT_LIST().size() > 0) {
            feedbackCollaborationTaskEntity.getATTACHMENT_LIST().forEach(attachment -> {

                OmsFileEntity omsFileEntity = new OmsFileEntity();
                try {
                    omsFileEntity.setGuid(String.valueOf(IdWorker.getId()));
                    omsFileEntity.setOrderNo(orderFwxmWorkEntity.getOrderNo());
                    omsFileEntity.setFileType(attachment.getTYPE());
                    omsFileEntity.setUrl(attachment.getFILE_PATH());
                    omsFileEntity.setFileNo(attachment.getFILE_IDENTIFIER());
                    omsFileEntity.setOriginFileName(attachment.getFILE_NAME());
                    omsFileEntity.setSysCode(attachment.getSOURCE_SYSTEM());
                    omsFileEntity.setCreateDate(attachment.getUPLOAD_TIME());

                    omsFileService.save(omsFileEntity, true);



                } catch (Exception ignored) {
                    log.warn("附件保存失败: {}", ignored.getMessage());
                }
            });

        }

        // 操作日志记录
        OmsOrderLogEntity logEntity = new OmsOrderLogEntity();
        logEntity.setOrderNo(receiveHistoryEntity.getOrderno());
        logEntity.setOperName("反馈作业数据");
        omsOrderLogService.writeLog(logEntity);

        return omsOrderFwxmWorkFkHzqdEntity;
    }

    /**
     * 获取订单反馈内容-核注清单实体对象
     *
     * @param orderFwxmWorkEntity
     * @param feedbackCollaborationTaskEntity
     * @return
     */
    private List<OmsOrderFwxmWorkFkHzqdEntity> getOmsOrderFwxmWorkFkHzqdEntity(OmsOrderFwxmWorkEntity orderFwxmWorkEntity,
                                                                               FeedbackCollaborationTaskEntity feedbackCollaborationTaskEntity) {
        List<OmsOrderFwxmWorkFkHzqdEntity> result = new ArrayList<>();
        if (feedbackCollaborationTaskEntity != null && feedbackCollaborationTaskEntity.getBODY() != null) {
            feedbackCollaborationTaskEntity.getBODY().forEach(bodyItem -> {
                // 保存协作任务数据
                OmsOrderFwxmWorkFkHzqdEntity saveOmsOrderFwxmWorkFkHzqdEntity = new OmsOrderFwxmWorkFkHzqdEntity();
                saveOmsOrderFwxmWorkFkHzqdEntity.setGuid(IdWorker.getIdStr());
                saveOmsOrderFwxmWorkFkHzqdEntity.setOrderNo(orderFwxmWorkEntity.getOrderNo());
                saveOmsOrderFwxmWorkFkHzqdEntity.setWorkNo(orderFwxmWorkEntity.getWorkNo());
                saveOmsOrderFwxmWorkFkHzqdEntity.setDecNo(feedbackCollaborationTaskEntity.getDEC_NO()); // 报关单号
                saveOmsOrderFwxmWorkFkHzqdEntity.setDecTableNum(feedbackCollaborationTaskEntity.getDEC_TABLE_NUM()); // 报关单明细行数
                saveOmsOrderFwxmWorkFkHzqdEntity.setdDate(feedbackCollaborationTaskEntity.getD_DATE()); // 申报日期
                saveOmsOrderFwxmWorkFkHzqdEntity.setCheckbillNo(feedbackCollaborationTaskEntity.getCHECKBILL_NO()); // 关联编号（核注清单号、报关单号、出入库单号）
                saveOmsOrderFwxmWorkFkHzqdEntity.setBjType(feedbackCollaborationTaskEntity.getBJ_TYPE()); // 报检类别
                saveOmsOrderFwxmWorkFkHzqdEntity.setJyjyNo(feedbackCollaborationTaskEntity.getJYJY_NO()); // 报检号
                saveOmsOrderFwxmWorkFkHzqdEntity.setYdh(feedbackCollaborationTaskEntity.getYDH()); // 运单号
                saveOmsOrderFwxmWorkFkHzqdEntity.setFdh(feedbackCollaborationTaskEntity.getFDH()); // 分单号
                saveOmsOrderFwxmWorkFkHzqdEntity.setXdh(feedbackCollaborationTaskEntity.getXDH()); // 箱单号
                saveOmsOrderFwxmWorkFkHzqdEntity.setBgdZs(feedbackCollaborationTaskEntity.getBGD_ZS()); // 报关单张数
                saveOmsOrderFwxmWorkFkHzqdEntity.setZgdh(feedbackCollaborationTaskEntity.getZGDH()); // 转关单号
                saveOmsOrderFwxmWorkFkHzqdEntity.setDecNoXt(feedbackCollaborationTaskEntity.getDEC_NO_XT()); // 关联报关单号

                saveOmsOrderFwxmWorkFkHzqdEntity.setCheckbillTableNum(feedbackCollaborationTaskEntity.getCHECKBILL_TABLE_NUM()); // 核注清单明细行数
                saveOmsOrderFwxmWorkFkHzqdEntity.setHkDate(feedbackCollaborationTaskEntity.getHK_DATE()); // 核扣日期
                saveOmsOrderFwxmWorkFkHzqdEntity.setCrkdh(feedbackCollaborationTaskEntity.getCRKDH()); // 出入库单号
                saveOmsOrderFwxmWorkFkHzqdEntity.setContractNo(feedbackCollaborationTaskEntity.getCONTRACT_NO()); // 合同号
                saveOmsOrderFwxmWorkFkHzqdEntity.setOriginArrivalCountry(feedbackCollaborationTaskEntity.getORIGIN_ARRIVAL_COUNTRY()); // 起运国/运抵国
                saveOmsOrderFwxmWorkFkHzqdEntity.setContainerNo(feedbackCollaborationTaskEntity.getCONTAINER_NO()); // 集装箱号
                saveOmsOrderFwxmWorkFkHzqdEntity.setSupervisionMode(feedbackCollaborationTaskEntity.getSUPERVISION_MODE()); // 监管方式
                saveOmsOrderFwxmWorkFkHzqdEntity.setIsCheck(feedbackCollaborationTaskEntity.getIS_CHECK()); // 是否查验（Y/N）
                saveOmsOrderFwxmWorkFkHzqdEntity.setHzqdDate(feedbackCollaborationTaskEntity.getHZQD_DATE()); // 核注清单申报日期
                saveOmsOrderFwxmWorkFkHzqdEntity.setCylx(feedbackCollaborationTaskEntity.getCYLX()); // 查验类型
                saveOmsOrderFwxmWorkFkHzqdEntity.setiEType(feedbackCollaborationTaskEntity.getI_E_TYPE()); // 进出标志：I进E出
                saveOmsOrderFwxmWorkFkHzqdEntity.setiEPort(feedbackCollaborationTaskEntity.getI_E_PORT()); // 进/出境关别
                saveOmsOrderFwxmWorkFkHzqdEntity.setCustomCode(feedbackCollaborationTaskEntity.getCUSTOM_CODE()); // 申报地海关/主管关区
                saveOmsOrderFwxmWorkFkHzqdEntity.setTradeCode(feedbackCollaborationTaskEntity.getTRADE_CODE()); // 区内企业
                saveOmsOrderFwxmWorkFkHzqdEntity.setYsfs(feedbackCollaborationTaskEntity.getYSFS()); // 运输方式
                saveOmsOrderFwxmWorkFkHzqdEntity.setJnsfhr(feedbackCollaborationTaskEntity.getJNSFHR()); // 境内收发货人
                saveOmsOrderFwxmWorkFkHzqdEntity.setIsJyjy(feedbackCollaborationTaskEntity.getIS_JYJY()); // 是否检验检疫YN
                saveOmsOrderFwxmWorkFkHzqdEntity.setPackType(feedbackCollaborationTaskEntity.getPACK_TYPE()); // 包装种类（新的代码）
                saveOmsOrderFwxmWorkFkHzqdEntity.setBgdBgType(feedbackCollaborationTaskEntity.getBGD_BG_TYPE()); // 企业报关类型
                saveOmsOrderFwxmWorkFkHzqdEntity.setHzqdBgType(feedbackCollaborationTaskEntity.getHZQD_BG_TYPE()); // (核注清单)报关类型
                saveOmsOrderFwxmWorkFkHzqdEntity.setBillDate(feedbackCollaborationTaskEntity.getBILL_DATE()); // 制单日期
                saveOmsOrderFwxmWorkFkHzqdEntity.setHdcs(feedbackCollaborationTaskEntity.getHDCS()); // 货代/厂商
                saveOmsOrderFwxmWorkFkHzqdEntity.setBgFlag(feedbackCollaborationTaskEntity.getBG_FLAG()); // 报关标志
                saveOmsOrderFwxmWorkFkHzqdEntity.setTradeCodeOut(feedbackCollaborationTaskEntity.getTRADE_CODE_OUT()); // 区外企业
                saveOmsOrderFwxmWorkFkHzqdEntity.setDecTradeMode(feedbackCollaborationTaskEntity.getDEC_TRADE_MODE()); // 贸易方式
                saveOmsOrderFwxmWorkFkHzqdEntity.setIsSd(feedbackCollaborationTaskEntity.getIS_SD()); // 删单标志YN
                saveOmsOrderFwxmWorkFkHzqdEntity.setIsGd(feedbackCollaborationTaskEntity.getIS_GD()); // 改单标志YN
                saveOmsOrderFwxmWorkFkHzqdEntity.setLds(feedbackCollaborationTaskEntity.getLDS()); // 联单数
                saveOmsOrderFwxmWorkFkHzqdEntity.setQsMan(feedbackCollaborationTaskEntity.getQS_MAN()); // 签收人
                saveOmsOrderFwxmWorkFkHzqdEntity.setQsDate(feedbackCollaborationTaskEntity.getQS_DATE()); // 签收时间
                saveOmsOrderFwxmWorkFkHzqdEntity.setBillMan(feedbackCollaborationTaskEntity.getBILL_MAN()); // 制单人
                saveOmsOrderFwxmWorkFkHzqdEntity.setTradeCountry(feedbackCollaborationTaskEntity.getTRADE_COUNTRY()); // 贸易国
                saveOmsOrderFwxmWorkFkHzqdEntity.setBgdMemo(feedbackCollaborationTaskEntity.getBGD_MEMO()); // 制单备注
                saveOmsOrderFwxmWorkFkHzqdEntity.setIsBj(feedbackCollaborationTaskEntity.getIS_BJ()); // 是否报检

                // 表体的数据
                saveOmsOrderFwxmWorkFkHzqdEntity.setHsCode(bodyItem.getHS_CODE()); // HS编码
                saveOmsOrderFwxmWorkFkHzqdEntity.setAmount(bodyItem.getAMOUNT()); // 金额
                saveOmsOrderFwxmWorkFkHzqdEntity.setCurrency(bodyItem.getCURRENCY()); // 币制
                saveOmsOrderFwxmWorkFkHzqdEntity.setAmountCurrency(bodyItem.getAMOUNT_CURRENCY()); // 金额币制
                saveOmsOrderFwxmWorkFkHzqdEntity.setPiece(bodyItem.getPIECE()); // 件数
                saveOmsOrderFwxmWorkFkHzqdEntity.setWeight(bodyItem.getWEIGHT()); // 毛重
                saveOmsOrderFwxmWorkFkHzqdEntity.setQty(bodyItem.getQTY()); // 数量
                saveOmsOrderFwxmWorkFkHzqdEntity.setNetWeight(bodyItem.getNET_WEIGHT()); // 净重
                saveOmsOrderFwxmWorkFkHzqdEntity.setProductCode(bodyItem.getPRODUCT_CODE()); // 品名

                result.add(saveOmsOrderFwxmWorkFkHzqdEntity);
            });
        } else {
            // 保存协作任务数据
            OmsOrderFwxmWorkFkHzqdEntity saveOmsOrderFwxmWorkFkHzqdEntity = new OmsOrderFwxmWorkFkHzqdEntity();
            saveOmsOrderFwxmWorkFkHzqdEntity.setGuid(IdWorker.getIdStr());
            saveOmsOrderFwxmWorkFkHzqdEntity.setOrderNo(orderFwxmWorkEntity.getOrderNo());
            saveOmsOrderFwxmWorkFkHzqdEntity.setWorkNo(orderFwxmWorkEntity.getWorkNo());
            saveOmsOrderFwxmWorkFkHzqdEntity.setDecNo(feedbackCollaborationTaskEntity.getDEC_NO()); // 报关单号
            saveOmsOrderFwxmWorkFkHzqdEntity.setDecTableNum(feedbackCollaborationTaskEntity.getDEC_TABLE_NUM()); // 报关单明细行数
            saveOmsOrderFwxmWorkFkHzqdEntity.setdDate(feedbackCollaborationTaskEntity.getD_DATE()); // 申报日期
            saveOmsOrderFwxmWorkFkHzqdEntity.setCheckbillNo(feedbackCollaborationTaskEntity.getCHECKBILL_NO()); // 关联编号（核注清单号、报关单号、出入库单号）
            saveOmsOrderFwxmWorkFkHzqdEntity.setBjType(feedbackCollaborationTaskEntity.getBJ_TYPE()); // 报检类别
            saveOmsOrderFwxmWorkFkHzqdEntity.setJyjyNo(feedbackCollaborationTaskEntity.getJYJY_NO()); // 报检号
            saveOmsOrderFwxmWorkFkHzqdEntity.setYdh(feedbackCollaborationTaskEntity.getYDH()); // 运单号
            saveOmsOrderFwxmWorkFkHzqdEntity.setFdh(feedbackCollaborationTaskEntity.getFDH()); // 分单号
            saveOmsOrderFwxmWorkFkHzqdEntity.setXdh(feedbackCollaborationTaskEntity.getXDH()); // 箱单号
            saveOmsOrderFwxmWorkFkHzqdEntity.setBgdZs(feedbackCollaborationTaskEntity.getBGD_ZS()); // 报关单张数
            saveOmsOrderFwxmWorkFkHzqdEntity.setZgdh(feedbackCollaborationTaskEntity.getZGDH()); // 转关单号
            saveOmsOrderFwxmWorkFkHzqdEntity.setDecNoXt(feedbackCollaborationTaskEntity.getDEC_NO_XT()); // 关联报关单号


            saveOmsOrderFwxmWorkFkHzqdEntity.setCheckbillTableNum(feedbackCollaborationTaskEntity.getCHECKBILL_TABLE_NUM()); // 核注清单明细行数
            saveOmsOrderFwxmWorkFkHzqdEntity.setHkDate(feedbackCollaborationTaskEntity.getHK_DATE()); // 核扣日期
            saveOmsOrderFwxmWorkFkHzqdEntity.setCrkdh(feedbackCollaborationTaskEntity.getCRKDH()); // 出入库单号
            saveOmsOrderFwxmWorkFkHzqdEntity.setContractNo(feedbackCollaborationTaskEntity.getCONTRACT_NO()); // 合同号
            saveOmsOrderFwxmWorkFkHzqdEntity.setOriginArrivalCountry(feedbackCollaborationTaskEntity.getORIGIN_ARRIVAL_COUNTRY()); // 起运国/运抵国
            saveOmsOrderFwxmWorkFkHzqdEntity.setContainerNo(feedbackCollaborationTaskEntity.getCONTAINER_NO()); // 集装箱号
            saveOmsOrderFwxmWorkFkHzqdEntity.setSupervisionMode(feedbackCollaborationTaskEntity.getSUPERVISION_MODE()); // 监管方式
            saveOmsOrderFwxmWorkFkHzqdEntity.setIsCheck(feedbackCollaborationTaskEntity.getIS_CHECK()); // 是否查验（Y/N）
            saveOmsOrderFwxmWorkFkHzqdEntity.setHzqdDate(feedbackCollaborationTaskEntity.getHZQD_DATE()); // 核注清单申报日期
            saveOmsOrderFwxmWorkFkHzqdEntity.setCylx(feedbackCollaborationTaskEntity.getCYLX()); // 查验类型
            saveOmsOrderFwxmWorkFkHzqdEntity.setiEType(feedbackCollaborationTaskEntity.getI_E_TYPE()); // 进出标志：I进E出
            saveOmsOrderFwxmWorkFkHzqdEntity.setiEPort(feedbackCollaborationTaskEntity.getI_E_PORT()); // 进/出境关别
            saveOmsOrderFwxmWorkFkHzqdEntity.setCustomCode(feedbackCollaborationTaskEntity.getCUSTOM_CODE()); // 申报地海关/主管关区
            saveOmsOrderFwxmWorkFkHzqdEntity.setTradeCode(feedbackCollaborationTaskEntity.getTRADE_CODE()); // 区内企业
            saveOmsOrderFwxmWorkFkHzqdEntity.setYsfs(feedbackCollaborationTaskEntity.getYSFS()); // 运输方式
            saveOmsOrderFwxmWorkFkHzqdEntity.setJnsfhr(feedbackCollaborationTaskEntity.getJNSFHR()); // 境内收发货人
            saveOmsOrderFwxmWorkFkHzqdEntity.setIsJyjy(feedbackCollaborationTaskEntity.getIS_JYJY()); // 是否检验检疫YN
            saveOmsOrderFwxmWorkFkHzqdEntity.setPackType(feedbackCollaborationTaskEntity.getPACK_TYPE()); // 包装种类（新的代码）
            saveOmsOrderFwxmWorkFkHzqdEntity.setBgdBgType(feedbackCollaborationTaskEntity.getBGD_BG_TYPE()); // 企业报关类型
            saveOmsOrderFwxmWorkFkHzqdEntity.setHzqdBgType(feedbackCollaborationTaskEntity.getHZQD_BG_TYPE()); // (核注清单)报关类型
            saveOmsOrderFwxmWorkFkHzqdEntity.setBillDate(feedbackCollaborationTaskEntity.getBILL_DATE()); // 制单日期
            saveOmsOrderFwxmWorkFkHzqdEntity.setHdcs(feedbackCollaborationTaskEntity.getHDCS()); // 货代/厂商
            saveOmsOrderFwxmWorkFkHzqdEntity.setBgFlag(feedbackCollaborationTaskEntity.getBG_FLAG()); // 报关标志
            saveOmsOrderFwxmWorkFkHzqdEntity.setTradeCodeOut(feedbackCollaborationTaskEntity.getTRADE_CODE_OUT()); // 区外企业
            saveOmsOrderFwxmWorkFkHzqdEntity.setDecTradeMode(feedbackCollaborationTaskEntity.getDEC_TRADE_MODE()); // 贸易方式
            saveOmsOrderFwxmWorkFkHzqdEntity.setIsSd(feedbackCollaborationTaskEntity.getIS_SD()); // 删单标志YN
            saveOmsOrderFwxmWorkFkHzqdEntity.setIsGd(feedbackCollaborationTaskEntity.getIS_GD()); // 改单标志YN
            saveOmsOrderFwxmWorkFkHzqdEntity.setLds(feedbackCollaborationTaskEntity.getLDS()); // 联单数
            saveOmsOrderFwxmWorkFkHzqdEntity.setQsMan(feedbackCollaborationTaskEntity.getQS_MAN()); // 签收人
            saveOmsOrderFwxmWorkFkHzqdEntity.setQsDate(feedbackCollaborationTaskEntity.getQS_DATE()); // 签收时间
            saveOmsOrderFwxmWorkFkHzqdEntity.setBillMan(feedbackCollaborationTaskEntity.getBILL_MAN()); // 制单人
            saveOmsOrderFwxmWorkFkHzqdEntity.setTradeCountry(feedbackCollaborationTaskEntity.getTRADE_COUNTRY()); // 贸易国
            saveOmsOrderFwxmWorkFkHzqdEntity.setBgdMemo(feedbackCollaborationTaskEntity.getBGD_MEMO()); // 制单备注
            saveOmsOrderFwxmWorkFkHzqdEntity.setIsBj(feedbackCollaborationTaskEntity.getIS_BJ()); // 是否报检

            result.add(saveOmsOrderFwxmWorkFkHzqdEntity);
        }
        return result;

    }


}