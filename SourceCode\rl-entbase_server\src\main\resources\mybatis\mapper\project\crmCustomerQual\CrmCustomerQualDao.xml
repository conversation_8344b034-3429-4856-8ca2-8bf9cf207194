<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.crmCustomerQual.dao.CrmCustomerQualDao">
    <resultMap type="CrmCustomerQualEntity" id="CrmCustomerQualResult">
        <result property="guid" column="GUID"/>
        <result property="customerGuid" column="CUSTOMER_GUID"/>
        <result property="qualNo" column="QUAL_NO"/>
        <result property="qualType" column="QUAL_TYPE"/>
        <result property="qualName" column="QUAL_NAME"/>
        <result property="startDate" column="START_DATE"/>
        <result property="endDate" column="END_DATE"/>
        <result property="status" column="STATUS"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="memo" column="MEMO"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="qualLevel" column="QUAL_LEVEL"/>
        <result property="issuingAuthority" column="ISSUING_AUTHORITY"/>
    </resultMap>

    <sql id="selectCrmCustomerQualEntityVo">
        select
            GUID,
            CUSTOMER_GUID,
            QUAL_NO,
            QUAL_TYPE,
            QUAL_NAME,
            START_DATE,
            END_DATE,
            STATUS,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            CREATE_USER_NAME,
            UPDATE_USER_NAME,
            MEMO,
            CREATE_USER,
            UPDATE_USER,
            CREATE_DATE,
            UPDATE_DATE,
            QUAL_LEVEL,
            ISSUING_AUTHORITY
        from CRM_CUSTOMER_QUAL
    </sql>
</mapper>