package com.eci.project.fzgjException.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 订单作业异常对象 FZGJ_EXCEPTION
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-04-25
*/
@ApiModel("订单作业异常")
@TableName("FZGJ_EXCEPTION")
@FieldNameConstants
public class FzgjExceptionEntity extends EciBaseEntity{
    /**
    * GUID
    */
    @ApiModelProperty("GUID(36)")
    @TableId("GUID")
    private String guid;

    /**
    * 明细唯一编号
    */
    @ApiModelProperty("明细唯一编号(36)")
    @TableField("GOODS_GUID")
    private String goodsGuid;

    /**
    * 目标系统
    */
    @ApiModelProperty("目标系统(50)")
    @TableField("TARGET_SYSTEM_CODE")
    private String targetSystemCode;

    /**
    * 单据类型
    */
    @ApiModelProperty("单据类型(20)")
    @TableField("BILL_CODE")
    private String billCode;

    /**
    * 单据编号
    */
    @ApiModelProperty("单据编号(50)")
    @TableField("DOC_NO")
    private String docNo;

    /**
    * 服务类型
    */
    @ApiModelProperty("服务类型(20)")
    @TableField("FWLX_CODE")
    private String fwlxCode;

    /**
    * 作业环节
    */
    @ApiModelProperty("作业环节(50)")
    @TableField("LINK_CODE")
    private String linkCode;

    /**
    * 异常类型
    */
    @ApiModelProperty("异常类型(20)")
    @TableField("EXCEPTION_TYPE")
    private String exceptionType;

    /**
    * 异常描述
    */
    @ApiModelProperty("异常描述(500)")
    @TableField("EXCEPTION_MEMO")
    private String exceptionMemo;

    /**
    * 责任方(我司/供应商/客户/其他)
    */
    @ApiModelProperty("责任方(我司/供应商/客户/其他)(20)")
    @TableField("ZRF")
    private String zrf;

    /**
    * 处理方案
    */
    @ApiModelProperty("处理方案(50)")
    @TableField("DEAL_FA")
    private String dealFa;

    /**
    * 处理结果描述
    */
    @ApiModelProperty("处理结果描述(500)")
    @TableField("DEAL_MEMO")
    private String dealMemo;

    /**
    * 备注
    */
    @ApiModelProperty("备注(50)")
    @TableField("MEMO")
    private String memo;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 创建人名称
    */
    @ApiModelProperty("创建人名称(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 创建时间
    */
    @ApiModelProperty("创建时间(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建时间开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建时间结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 修改人
    */
    @ApiModelProperty("修改人(50)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 修改人名称
    */
    @ApiModelProperty("修改人名称(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 修改时间
    */
    @ApiModelProperty("修改时间(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("修改时间开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("修改时间结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 组织代码
    */
    @ApiModelProperty("组织代码(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 组织名称
    */
    @ApiModelProperty("组织名称(200)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 公司代码
    */
    @ApiModelProperty("公司代码(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 公司名称
    */
    @ApiModelProperty("公司名称(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 集团代码
    */
    @ApiModelProperty("集团代码(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 集团名称
    */
    @ApiModelProperty("集团名称(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 状态
    */
    @ApiModelProperty("状态(10)")
    @TableField("STATUS")
    private String status;

    /**
    * 异常编号
    */
    @ApiModelProperty("异常编号(36)")
    @TableField("EXCEPTION_NO")
    private String exceptionNo;

    /**
    * 异常发生时间
    */
    @ApiModelProperty("异常发生时间(7)")
    @TableField("EXCEPTION_DATE")
    private Date exceptionDate;

    @ApiModelProperty("异常发生时间开始")
    @TableField(exist=false)
    private Date exceptionDateStart;

    @ApiModelProperty("异常发生时间结束")
    @TableField(exist=false)
    private Date exceptionDateEnd;

    /**
    * 订单号
    */
    @ApiModelProperty("订单号(36)")
    @TableField("ORDER_NO")
    private String orderNo;

    /**
    * 委托方
    */
    @ApiModelProperty("委托方(36)")
    @TableField("CONSIGNEE_CODE")
    private String consigneeCode;

    /**
    * 业务类型
    */
    @ApiModelProperty("业务类型(20)")
    @TableField("OP_TYPE")
    private String opType;

    /**
    * 业务产品/项目
    */
    @ApiModelProperty("业务产品/项目(20)")
    @TableField("PRODUCT_CODE")
    private String productCode;

    /**
    * 附件地址
    */
    @ApiModelProperty("附件地址(200)")
    @TableField("FILE_URL")
    private String fileUrl;

    /**
    * 服务项目代码
    */
    @ApiModelProperty("服务项目代码(20)")
    @TableField("FWXM_CODE")
    private String fwxmCode;

    /**
    * 协作委托单编号
    */
    @ApiModelProperty("协作委托单编号(36)")
    @TableField("XZWT_NO")
    private String xzwtNo;

    /**
    * 协作任务编号
    */
    @ApiModelProperty("协作任务编号(36)")
    @TableField("WORK_NO")
    private String workNo;

    /**
    * 业务数据唯一注册编号
    */
    @ApiModelProperty("业务数据唯一注册编号(50)")
    @TableField("BIZ_REG_ID")
    private String bizRegId;

    /**
    * 常用异常描述
    */
    @ApiModelProperty("常用异常描述(500)")
    @TableField("EXCEPTION_MEMO_COMM")
    private String exceptionMemoComm;

    /**
    * 协作作业单据类型
    */
    @ApiModelProperty("协作作业单据类型(50)")
    @TableField("DOC_NO_WORK")
    private String docNoWork;

    /**
    * 协作作业单据类型
    */
    @ApiModelProperty("协作作业单据类型(20)")
    @TableField("BILL_CODE_WORK")
    private String billCodeWork;

    /**
    * 协作接单单据编号
    */
    @ApiModelProperty("协作接单单据编号(50)")
    @TableField("DOC_NO_JD")
    private String docNoJd;

    /**
    * 协作接单单据类型
    */
    @ApiModelProperty("协作接单单据类型(20)")
    @TableField("BILL_CODE_JD")
    private String billCodeJd;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public FzgjExceptionEntity() {
        this.setSubClazz(FzgjExceptionEntity.class);
    }

    public FzgjExceptionEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public FzgjExceptionEntity setGoodsGuid(String goodsGuid) {
        this.goodsGuid = goodsGuid;
        this.nodifySetFiled("goodsGuid", goodsGuid);
        return this;
    }

    public String getGoodsGuid() {
        this.nodifyGetFiled("goodsGuid");
        return goodsGuid;
    }

    public FzgjExceptionEntity setTargetSystemCode(String targetSystemCode) {
        this.targetSystemCode = targetSystemCode;
        this.nodifySetFiled("targetSystemCode", targetSystemCode);
        return this;
    }

    public String getTargetSystemCode() {
        this.nodifyGetFiled("targetSystemCode");
        return targetSystemCode;
    }

    public FzgjExceptionEntity setBillCode(String billCode) {
        this.billCode = billCode;
        this.nodifySetFiled("billCode", billCode);
        return this;
    }

    public String getBillCode() {
        this.nodifyGetFiled("billCode");
        return billCode;
    }

    public FzgjExceptionEntity setDocNo(String docNo) {
        this.docNo = docNo;
        this.nodifySetFiled("docNo", docNo);
        return this;
    }

    public String getDocNo() {
        this.nodifyGetFiled("docNo");
        return docNo;
    }

    public FzgjExceptionEntity setFwlxCode(String fwlxCode) {
        this.fwlxCode = fwlxCode;
        this.nodifySetFiled("fwlxCode", fwlxCode);
        return this;
    }

    public String getFwlxCode() {
        this.nodifyGetFiled("fwlxCode");
        return fwlxCode;
    }

    public FzgjExceptionEntity setLinkCode(String linkCode) {
        this.linkCode = linkCode;
        this.nodifySetFiled("linkCode", linkCode);
        return this;
    }

    public String getLinkCode() {
        this.nodifyGetFiled("linkCode");
        return linkCode;
    }

    public FzgjExceptionEntity setExceptionType(String exceptionType) {
        this.exceptionType = exceptionType;
        this.nodifySetFiled("exceptionType", exceptionType);
        return this;
    }

    public String getExceptionType() {
        this.nodifyGetFiled("exceptionType");
        return exceptionType;
    }

    public FzgjExceptionEntity setExceptionMemo(String exceptionMemo) {
        this.exceptionMemo = exceptionMemo;
        this.nodifySetFiled("exceptionMemo", exceptionMemo);
        return this;
    }

    public String getExceptionMemo() {
        this.nodifyGetFiled("exceptionMemo");
        return exceptionMemo;
    }

    public FzgjExceptionEntity setZrf(String zrf) {
        this.zrf = zrf;
        this.nodifySetFiled("zrf", zrf);
        return this;
    }

    public String getZrf() {
        this.nodifyGetFiled("zrf");
        return zrf;
    }

    public FzgjExceptionEntity setDealFa(String dealFa) {
        this.dealFa = dealFa;
        this.nodifySetFiled("dealFa", dealFa);
        return this;
    }

    public String getDealFa() {
        this.nodifyGetFiled("dealFa");
        return dealFa;
    }

    public FzgjExceptionEntity setDealMemo(String dealMemo) {
        this.dealMemo = dealMemo;
        this.nodifySetFiled("dealMemo", dealMemo);
        return this;
    }

    public String getDealMemo() {
        this.nodifyGetFiled("dealMemo");
        return dealMemo;
    }

    public FzgjExceptionEntity setMemo(String memo) {
        this.memo = memo;
        this.nodifySetFiled("memo", memo);
        return this;
    }

    public String getMemo() {
        this.nodifyGetFiled("memo");
        return memo;
    }

    public FzgjExceptionEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public FzgjExceptionEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public FzgjExceptionEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public FzgjExceptionEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public FzgjExceptionEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public FzgjExceptionEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public FzgjExceptionEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public FzgjExceptionEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public FzgjExceptionEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public FzgjExceptionEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public FzgjExceptionEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public FzgjExceptionEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public FzgjExceptionEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public FzgjExceptionEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public FzgjExceptionEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public FzgjExceptionEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public FzgjExceptionEntity setStatus(String status) {
        this.status = status;
        this.nodifySetFiled("status", status);
        return this;
    }

    public String getStatus() {
        this.nodifyGetFiled("status");
        return status;
    }

    public FzgjExceptionEntity setExceptionNo(String exceptionNo) {
        this.exceptionNo = exceptionNo;
        this.nodifySetFiled("exceptionNo", exceptionNo);
        return this;
    }

    public String getExceptionNo() {
        this.nodifyGetFiled("exceptionNo");
        return exceptionNo;
    }

    public FzgjExceptionEntity setExceptionDate(Date exceptionDate) {
        this.exceptionDate = exceptionDate;
        this.nodifySetFiled("exceptionDate", exceptionDate);
        return this;
    }

    public Date getExceptionDate() {
        this.nodifyGetFiled("exceptionDate");
        return exceptionDate;
    }

    public FzgjExceptionEntity setExceptionDateStart(Date exceptionDateStart) {
        this.exceptionDateStart = exceptionDateStart;
        this.nodifySetFiled("exceptionDateStart", exceptionDateStart);
        return this;
    }

    public Date getExceptionDateStart() {
        this.nodifyGetFiled("exceptionDateStart");
        return exceptionDateStart;
    }

    public FzgjExceptionEntity setExceptionDateEnd(Date exceptionDateEnd) {
        this.exceptionDateEnd = exceptionDateEnd;
        this.nodifySetFiled("exceptionDateEnd", exceptionDateEnd);
        return this;
    }

    public Date getExceptionDateEnd() {
        this.nodifyGetFiled("exceptionDateEnd");
        return exceptionDateEnd;
    }
    public FzgjExceptionEntity setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        this.nodifySetFiled("orderNo", orderNo);
        return this;
    }

    public String getOrderNo() {
        this.nodifyGetFiled("orderNo");
        return orderNo;
    }

    public FzgjExceptionEntity setConsigneeCode(String consigneeCode) {
        this.consigneeCode = consigneeCode;
        this.nodifySetFiled("consigneeCode", consigneeCode);
        return this;
    }

    public String getConsigneeCode() {
        this.nodifyGetFiled("consigneeCode");
        return consigneeCode;
    }

    public FzgjExceptionEntity setOpType(String opType) {
        this.opType = opType;
        this.nodifySetFiled("opType", opType);
        return this;
    }

    public String getOpType() {
        this.nodifyGetFiled("opType");
        return opType;
    }

    public FzgjExceptionEntity setProductCode(String productCode) {
        this.productCode = productCode;
        this.nodifySetFiled("productCode", productCode);
        return this;
    }

    public String getProductCode() {
        this.nodifyGetFiled("productCode");
        return productCode;
    }

    public FzgjExceptionEntity setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
        this.nodifySetFiled("fileUrl", fileUrl);
        return this;
    }

    public String getFileUrl() {
        this.nodifyGetFiled("fileUrl");
        return fileUrl;
    }

    public FzgjExceptionEntity setFwxmCode(String fwxmCode) {
        this.fwxmCode = fwxmCode;
        this.nodifySetFiled("fwxmCode", fwxmCode);
        return this;
    }

    public String getFwxmCode() {
        this.nodifyGetFiled("fwxmCode");
        return fwxmCode;
    }

    public FzgjExceptionEntity setXzwtNo(String xzwtNo) {
        this.xzwtNo = xzwtNo;
        this.nodifySetFiled("xzwtNo", xzwtNo);
        return this;
    }

    public String getXzwtNo() {
        this.nodifyGetFiled("xzwtNo");
        return xzwtNo;
    }

    public FzgjExceptionEntity setWorkNo(String workNo) {
        this.workNo = workNo;
        this.nodifySetFiled("workNo", workNo);
        return this;
    }

    public String getWorkNo() {
        this.nodifyGetFiled("workNo");
        return workNo;
    }

    public FzgjExceptionEntity setBizRegId(String bizRegId) {
        this.bizRegId = bizRegId;
        this.nodifySetFiled("bizRegId", bizRegId);
        return this;
    }

    public String getBizRegId() {
        this.nodifyGetFiled("bizRegId");
        return bizRegId;
    }

    public FzgjExceptionEntity setExceptionMemoComm(String exceptionMemoComm) {
        this.exceptionMemoComm = exceptionMemoComm;
        this.nodifySetFiled("exceptionMemoComm", exceptionMemoComm);
        return this;
    }

    public String getExceptionMemoComm() {
        this.nodifyGetFiled("exceptionMemoComm");
        return exceptionMemoComm;
    }

    public FzgjExceptionEntity setDocNoWork(String docNoWork) {
        this.docNoWork = docNoWork;
        this.nodifySetFiled("docNoWork", docNoWork);
        return this;
    }

    public String getDocNoWork() {
        this.nodifyGetFiled("docNoWork");
        return docNoWork;
    }

    public FzgjExceptionEntity setBillCodeWork(String billCodeWork) {
        this.billCodeWork = billCodeWork;
        this.nodifySetFiled("billCodeWork", billCodeWork);
        return this;
    }

    public String getBillCodeWork() {
        this.nodifyGetFiled("billCodeWork");
        return billCodeWork;
    }

    public FzgjExceptionEntity setDocNoJd(String docNoJd) {
        this.docNoJd = docNoJd;
        this.nodifySetFiled("docNoJd", docNoJd);
        return this;
    }

    public String getDocNoJd() {
        this.nodifyGetFiled("docNoJd");
        return docNoJd;
    }

    public FzgjExceptionEntity setBillCodeJd(String billCodeJd) {
        this.billCodeJd = billCodeJd;
        this.nodifySetFiled("billCodeJd", billCodeJd);
        return this;
    }

    public String getBillCodeJd() {
        this.nodifyGetFiled("billCodeJd");
        return billCodeJd;
    }

}
