<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.etmsBdDriverInsurance.dao.EtmsBdDriverInsuranceDao">
    <resultMap type="EtmsBdDriverInsuranceEntity" id="EtmsBdDriverInsuranceResult">
        <result property="guid" column="GUID"/>
        <result property="driverGuid" column="DRIVER_GUID"/>
        <result property="policyNo" column="POLICY_NO"/>
        <result property="insurer" column="INSURER"/>
        <result property="insuranceType" column="INSURANCE_TYPE"/>
        <result property="startDate" column="START_DATE"/>
        <result property="endDate" column="END_DATE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="status" column="STATUS"/>
        <result property="amount" column="AMOUNT"/>
        <result property="insuranceTerm" column="INSURANCE_TERM"/>
        <result property="memo" column="MEMO"/>
    </resultMap>

    <sql id="selectEtmsBdDriverInsuranceEntityVo">
        select
            GUID,
            DRIVER_GUID,
            POLICY_NO,
            INSURER,
            INSURANCE_TYPE,
            START_DATE,
            END_DATE,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            CREATE_USER_NAME,
            UPDATE_USER_NAME,
            STATUS,
            AMOUNT,
            INSURANCE_TERM,
            MEMO
        from ETMS_BD_DRIVER_INSURANCE
    </sql>
</mapper>