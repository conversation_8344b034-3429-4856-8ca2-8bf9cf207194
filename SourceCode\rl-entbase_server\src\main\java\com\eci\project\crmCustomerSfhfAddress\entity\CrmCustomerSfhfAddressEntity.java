package com.eci.project.crmCustomerSfhfAddress.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.codeConvert.EciCode;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.math.BigDecimal;
import java.util.Date;


/**
* 业务伙伴收发货方常用地区对象 CRM_CUSTOMER_SFHF_ADDRESS
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-05-20
*/
@ApiModel("业务伙伴收发货方常用地区")
@TableName("CRM_CUSTOMER_SFHF_ADDRESS")
@FieldNameConstants
public class CrmCustomerSfhfAddressEntity extends EciBaseEntity{
    /**
    * GUID
    */
    @ApiModelProperty("GUID(50)")
    @TableId("GUID")
    private String guid;

    /**
    * 伙伴GUID
    */
    @ApiModelProperty("伙伴GUID(50)")
    @TableField("CUSTOMER_GUID")
    private String customerGuid;

    @TableField(exist = false)
    private String country;
    @TableField(exist = false)
    private String province;
    @TableField(exist = false)
    private String city;
    @TableField(exist = false)
    private String district;
    /**
    * 作业地区
    */
    @ApiModelProperty("作业地区(50)")
    @TableField("OP_AREA")
    private String opArea;
    @TableField(exist = false)
    private String opAreaName;

    /**
    * 作业地址
    */
    @ApiModelProperty("作业地址(500)")
    @TableField("OP_ADDRESS")
    private String opAddress;

    /**
    * 作业码头
    */
    @ApiModelProperty("作业码头(50)")
    @TableField("OP_WH")
    private String opWh;

    /**
    * 作业联系人
    */
    @ApiModelProperty("作业联系人(20)")
    @TableField("OP_LINK")
    private String opLink;

    /**
    * 作业联系电话
    */
    @ApiModelProperty("作业联系电话(50)")
    @TableField("OP_TEL")
    private String opTel;

    /**
    * 作业要求
    */
    @ApiModelProperty("作业要求(500)")
    @TableField("OP_REQUEST")
    private String opRequest;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 最后修改日期
    */
    @ApiModelProperty("最后修改日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("最后修改日期开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("最后修改日期结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 是否默认
    */
    @ApiModelProperty("是否默认(1)")
    @TableField("IS_MR")
    @EciCode("YNKey")
    private String isMr;

    /**
    * 状态 Y-启用，N-停用
    */
    @ApiModelProperty("状态 Y-启用，N-停用(1)")
    @TableField("STATUS")
    @EciCode("YNKey")
    private String status;

    /**
    * 地址简称
    */
    @ApiModelProperty("地址简称(100)")
    @TableField("OP_ABBREVIATION")
    private String opAbbreviation;

    /**
    * 顺序
    */
    @ApiModelProperty("顺序(22)")
    @TableField("SEQ")
    private Integer seq;

    /**
    * 备注
    */
    @ApiModelProperty("备注(200)")
    @TableField("MEMO")
    private String memo;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(200)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 伙伴代码
    */
    @ApiModelProperty("伙伴代码(50)")
    @TableField("CUSTOMER_CODE")
    private String customerCode;

    /**
    * 距离(KM)
    */
    @ApiModelProperty("距离(KM)(22)")
    @TableField("DISTANCE")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal distance;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public CrmCustomerSfhfAddressEntity() {
        this.setSubClazz(CrmCustomerSfhfAddressEntity.class);
    }

    public CrmCustomerSfhfAddressEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public CrmCustomerSfhfAddressEntity setCountry(String country) {
        this.country = country;
        this.nodifySetFiled("country", country);
        return this;
    }

    public String getCountry() {
        this.nodifyGetFiled("country");
        return country;
    }

    public CrmCustomerSfhfAddressEntity setProvince(String province) {
        this.province = province;
        this.nodifySetFiled("province", province);
        return this;
    }

    public String getProvince() {
        this.nodifyGetFiled("province");
        return province;
    }

    public CrmCustomerSfhfAddressEntity setCity(String city) {
        this.city = city;
        this.nodifySetFiled("city", city);
        return this;
    }

    public String getCity() {
        this.nodifyGetFiled("city");
        return city;
    }

    public CrmCustomerSfhfAddressEntity setDistrict(String district) {
        this.district = district;
        this.nodifySetFiled("district", district);
        return this;
    }

    public String getDistrict() {
        this.nodifyGetFiled("district");
        return district;
    }



    public CrmCustomerSfhfAddressEntity setCustomerGuid(String customerGuid) {
        this.customerGuid = customerGuid;
        this.nodifySetFiled("customerGuid", customerGuid);
        return this;
    }

    public String getCustomerGuid() {
        this.nodifyGetFiled("customerGuid");
        return customerGuid;
    }

    public CrmCustomerSfhfAddressEntity setOpArea(String opArea) {
        this.opArea = opArea;
        this.nodifySetFiled("opArea", opArea);
        return this;
    }

    public String getOpArea() {
        this.nodifyGetFiled("opArea");
        return opArea;
    }
    public CrmCustomerSfhfAddressEntity setOpAreaName(String opAreaName) {
        this.opAreaName = opAreaName;
        this.nodifySetFiled("opAreaName", opAreaName);
        return this;
    }

    public String getOpAreaName() {
        this.nodifyGetFiled("opAreaName");
        return opAreaName;
    }

    public CrmCustomerSfhfAddressEntity setOpAddress(String opAddress) {
        this.opAddress = opAddress;
        this.nodifySetFiled("opAddress", opAddress);
        return this;
    }

    public String getOpAddress() {
        this.nodifyGetFiled("opAddress");
        return opAddress;
    }

    public CrmCustomerSfhfAddressEntity setOpWh(String opWh) {
        this.opWh = opWh;
        this.nodifySetFiled("opWh", opWh);
        return this;
    }

    public String getOpWh() {
        this.nodifyGetFiled("opWh");
        return opWh;
    }

    public CrmCustomerSfhfAddressEntity setOpLink(String opLink) {
        this.opLink = opLink;
        this.nodifySetFiled("opLink", opLink);
        return this;
    }

    public String getOpLink() {
        this.nodifyGetFiled("opLink");
        return opLink;
    }

    public CrmCustomerSfhfAddressEntity setOpTel(String opTel) {
        this.opTel = opTel;
        this.nodifySetFiled("opTel", opTel);
        return this;
    }

    public String getOpTel() {
        this.nodifyGetFiled("opTel");
        return opTel;
    }

    public CrmCustomerSfhfAddressEntity setOpRequest(String opRequest) {
        this.opRequest = opRequest;
        this.nodifySetFiled("opRequest", opRequest);
        return this;
    }

    public String getOpRequest() {
        this.nodifyGetFiled("opRequest");
        return opRequest;
    }

    public CrmCustomerSfhfAddressEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public CrmCustomerSfhfAddressEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public CrmCustomerSfhfAddressEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public CrmCustomerSfhfAddressEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public CrmCustomerSfhfAddressEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public CrmCustomerSfhfAddressEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public CrmCustomerSfhfAddressEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public CrmCustomerSfhfAddressEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public CrmCustomerSfhfAddressEntity setIsMr(String isMr) {
        this.isMr = isMr;
        this.nodifySetFiled("isMr", isMr);
        return this;
    }

    public String getIsMr() {
        this.nodifyGetFiled("isMr");
        return isMr;
    }

    public CrmCustomerSfhfAddressEntity setStatus(String status) {
        this.status = status;
        this.nodifySetFiled("status", status);
        return this;
    }

    public String getStatus() {
        this.nodifyGetFiled("status");
        return status;
    }

    public CrmCustomerSfhfAddressEntity setOpAbbreviation(String opAbbreviation) {
        this.opAbbreviation = opAbbreviation;
        this.nodifySetFiled("opAbbreviation", opAbbreviation);
        return this;
    }

    public String getOpAbbreviation() {
        this.nodifyGetFiled("opAbbreviation");
        return opAbbreviation;
    }

    public CrmCustomerSfhfAddressEntity setSeq(Integer seq) {
        this.seq = seq;
        this.nodifySetFiled("seq", seq);
        return this;
    }

    public Integer getSeq() {
        this.nodifyGetFiled("seq");
        return seq;
    }

    public CrmCustomerSfhfAddressEntity setMemo(String memo) {
        this.memo = memo;
        this.nodifySetFiled("memo", memo);
        return this;
    }

    public String getMemo() {
        this.nodifyGetFiled("memo");
        return memo;
    }

    public CrmCustomerSfhfAddressEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public CrmCustomerSfhfAddressEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public CrmCustomerSfhfAddressEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public CrmCustomerSfhfAddressEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public CrmCustomerSfhfAddressEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public CrmCustomerSfhfAddressEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public CrmCustomerSfhfAddressEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public CrmCustomerSfhfAddressEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public CrmCustomerSfhfAddressEntity setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
        this.nodifySetFiled("customerCode", customerCode);
        return this;
    }

    public String getCustomerCode() {
        this.nodifyGetFiled("customerCode");
        return customerCode;
    }

    public CrmCustomerSfhfAddressEntity setDistance(BigDecimal distance) {
        this.distance = distance;
        this.nodifySetFiled("distance", distance);
        return this;
    }

    public BigDecimal getDistance() {
        this.nodifyGetFiled("distance");
        return distance;
    }

}
