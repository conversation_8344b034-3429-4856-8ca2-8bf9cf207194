package com.eci.project.fzgjExtendType.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;



/**
* 扩展基础资料类型对象 FZGJ_EXTEND_TYPE
*
* @<NAME_EMAIL>
* @date 2025-03-19
*/
@FieldNameConstants
public class FzgjExtendTypeBaseEntity extends EciBaseEntity
{
	/**
	* GUID
	*/
	@ApiModelProperty("GUID(50)")
	@TableId("GUID")
	private String guid;

	/**
	* 类型
	*/
	@ApiModelProperty("类型(20)")
	@TableField("TYPE_CODE")
	private String typeCode;

	/**
	* 类型
	*/
	@ApiModelProperty("类型(200)")
	@TableField("TYPE_NAME")
	private String typeName;

	/**
	* 状态
	*/
	@ApiModelProperty("状态(10)")
	@TableField("STATUS")
	private String status;

	/**
	* 备注
	*/
	@ApiModelProperty("备注(200)")
	@TableField("MEMO")
	private String memo;

	/**
	* 是否挂菜单权限
	*/
	@ApiModelProperty("是否挂菜单权限(200)")
	@TableField("SYS_JSD")
	private String sysJsd;

	@JsonIgnore
	@TableField(exist = false)
	private Class<?> clazz = this.getClass();

	public Class<?> getClazz() {
		return clazz;
	}

	public void setClazz(Class<?> clazz) {
		this.clazz = clazz;
	}

	public FzgjExtendTypeBaseEntity setGuid(String guid) {
		this.guid = guid;
		return this;
	}

	public String getGuid() {
		return guid;
	}

	public FzgjExtendTypeBaseEntity setTypeCode(String typeCode) {
		this.typeCode = typeCode;
		return this;
	}

	public String getTypeCode() {
		return typeCode;
	}

	public FzgjExtendTypeBaseEntity setTypeName(String typeName) {
		this.typeName = typeName;
		return this;
	}

	public String getTypeName() {
		return typeName;
	}

	public FzgjExtendTypeBaseEntity setStatus(String status) {
		this.status = status;
		return this;
	}

	public String getStatus() {
		return status;
	}

	public FzgjExtendTypeBaseEntity setMemo(String memo) {
		this.memo = memo;
		return this;
	}

	public String getMemo() {
		return memo;
	}

	public FzgjExtendTypeBaseEntity setSysJsd(String sysJsd) {
		this.sysJsd = sysJsd;
		return this;
	}

	public String getSysJsd() {
		return sysJsd;
	}

}
