package com.eci.project.omsOrderPre.entity;

/**
 * @ClassName: ResOmsOrderPrePageEntity
 * @Author: guangyan.mei
 * @Date: 2025/4/16 10:24
 * @Description: TODO
 */
public class ResOmsOrderPrePageEntity extends OmsOrderPreEntity {
    public String xdUserName; // 下单人姓名
    public String receiverName; // 收货人姓名
    public String shipperName; // 发货人姓名
    public String productCodeName; // 业务产品/项目名称
    public String consigneeCodeName; // 委托方名称
    public String opTypeName; // 业务类型名称
    public String customerBuName; // 客户事业部
    public String crossItemName; // 项目
    public String crossLineName; // 跨境班车线路
    public String orderNo; // 订单号
}
