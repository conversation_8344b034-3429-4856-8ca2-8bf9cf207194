server:
  # 项目端口
  port: 9527
  servlet:
    context-path: /
  tomcat:
    uri-encoding: UTF-8
spring:
  application:
    name: rl-entbase_server
  # 上传文件功能配置
  servlet:
    multipart:
      max-file-size:  10MB
      max-request-size:  20MB
  cache:
    redis:
      cache-null-values: false
  profiles:
    active: dev

mybatis-plus:
  config-location: classpath:mybatis/mybatis-config.xml
  # 搜索指定包别名
  type-aliases-package: com.eci
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapper-locations: classpath*:mybatis/mapper/**/*Dao.xml
  global-config:
    db-config:
      #主键类型 AUTO:"数据库ID自增 如mysql" INPUT:"用户输入ID,如:oracle",ASSIGN_ID:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: ASSIGN_ID
      #id-type: ID_WORKER
      #字段策略 IGNORED:"忽略判断"  NOT_NULL:"非 NULL 判断")  NOT_EMPTY:"非空判断"
      field-strategy: NOT_EMPTY
      #数据库类型
      db-type: MYSQL
      #db-type: ORACLE
      table-underline: false

pagehelper:
  #helperDialect: oracle
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql
  pageSizeZero: true
project:
  token:
    exclude: