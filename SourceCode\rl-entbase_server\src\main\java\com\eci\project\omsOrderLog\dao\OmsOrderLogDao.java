package com.eci.project.omsOrderLog.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.omsOrderLog.entity.OmsOrderLogEntity;


/**
* 订单操作日志信息Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-06-19
*/
public interface OmsOrderLogDao extends EciBaseDao<OmsOrderLogEntity> {

}