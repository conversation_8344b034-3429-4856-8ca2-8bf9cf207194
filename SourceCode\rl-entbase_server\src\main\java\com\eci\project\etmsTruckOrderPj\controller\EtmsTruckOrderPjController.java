package com.eci.project.etmsTruckOrderPj.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.etmsTruckOrderPj.service.EtmsTruckOrderPjService;
import com.eci.project.etmsTruckOrderPj.entity.EtmsTruckOrderPjEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* Controller
*
* @<NAME_EMAIL>
* @date 2025-04-22
*/
@Api(tags = "")
@RestController
@RequestMapping("/etmsTruckOrderPj")
public class EtmsTruckOrderPjController extends EciBaseController {

    @Autowired
    private EtmsTruckOrderPjService etmsTruckOrderPjService;


    @ApiOperation(":保存")
    @EciLog(title = ":新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody EtmsTruckOrderPjEntity entity){
        EtmsTruckOrderPjEntity etmsTruckOrderPjEntity =etmsTruckOrderPjService.save(entity);
        return ResponseMsgUtil.success(10001,etmsTruckOrderPjEntity);
    }


    @ApiOperation(":查询列表")
    @EciLog(title = ":查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody EtmsTruckOrderPjEntity entity){
        List<EtmsTruckOrderPjEntity> etmsTruckOrderPjEntities = etmsTruckOrderPjService.selectList(entity);
        return ResponseMsgUtil.success(10001,etmsTruckOrderPjEntities);
    }


    @ApiOperation(":分页查询列表")
    @EciLog(title = ":分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody EtmsTruckOrderPjEntity entity){
        if(entity.getRequestParams()==null||!entity.getRequestParams().containsKey("driverName"))
            return ResponseMsgUtil.error(10000,"未查询到相应记录");
        TgPageInfo tgPageInfo = etmsTruckOrderPjService.queryPages(entity.getRequestParams().get("driverName").toString());
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation(":根据ID查一条")
    @EciLog(title = ":根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody EtmsTruckOrderPjEntity entity){
        EtmsTruckOrderPjEntity  etmsTruckOrderPjEntity = etmsTruckOrderPjService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,etmsTruckOrderPjEntity);
    }


    @ApiOperation(":根据ID删除一条")
    @EciLog(title = ":根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody EtmsTruckOrderPjEntity entity){
        int count = etmsTruckOrderPjService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation(":根据ID字符串删除多条")
    @EciLog(title = ":根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody EtmsTruckOrderPjEntity entity) {
        int count = etmsTruckOrderPjService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}