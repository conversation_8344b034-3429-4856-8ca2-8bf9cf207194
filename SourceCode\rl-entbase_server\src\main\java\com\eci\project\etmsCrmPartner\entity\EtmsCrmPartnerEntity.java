package com.eci.project.etmsCrmPartner.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 对象 ETMS_CRM_PARTNER
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-04-16
*/
@ApiModel("")
@TableName("ETMS_CRM_PARTNER")
@FieldNameConstants
public class EtmsCrmPartnerEntity extends EciBaseEntity{
    @ApiModelProperty("(36)")
    @TableField("GUID")
    private String guid;

    @ApiModelProperty("(36)")
    @TableField("CODE")
    private String code;

    @ApiModelProperty("(100)")
    @TableField("NAME")
    private String name;

    @ApiModelProperty("(36)")
    @TableField("ENTE_CODE")
    private String enteCode;

    @ApiModelProperty("(1)")
    @TableField("STATUS")
    private String status;

    @ApiModelProperty("(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("结束")
    @TableField(exist=false)
    private Date createDateEnd;

    @ApiModelProperty("(20)")
    @TableField("CREATE_USER")
    private String createUser;

    @ApiModelProperty("(50)")
    @TableField("CREATE_COMPANY")
    private String createCompany;

    @ApiModelProperty("(8)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    @ApiModelProperty("(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    @ApiModelProperty("(50)")
    @TableField("UPDATE_COMPANY")
    private String updateCompany;

    @ApiModelProperty("(0)")
    @TableField("ENTE_CODE_EX")
    private String enteCodeEx;

    @ApiModelProperty("(1)")
    @TableField("TRADE")
    private String trade;

    @ApiModelProperty("(1)")
    @TableField("LSP")
    private String lsp;

    @ApiModelProperty("(0)")
    @TableField("ENTE_NAME_EN")
    private String enteNameEn;

    @ApiModelProperty("(1,000)")
    @TableField("ENTE_ADDR_CN")
    private String enteAddrCn;

    @ApiModelProperty("(0)")
    @TableField("POSTCODE")
    private String postcode;

    @ApiModelProperty("(0)")
    @TableField("COUNTRY")
    private String country;

    @ApiModelProperty("(20)")
    @TableField("CONTACT_PERSON")
    private String contactPerson;

    @ApiModelProperty("(0)")
    @TableField("TEL")
    private String tel;

    @ApiModelProperty("(0)")
    @TableField("FAX")
    private String fax;

    @ApiModelProperty("(0)")
    @TableField("EMAIL")
    private String email;

    @ApiModelProperty("(0)")
    @TableField("WEBSITE")
    private String website;

    @ApiModelProperty("(0)")
    @TableField("SWIFT_ADDRESS")
    private String swiftAddress;

    @ApiModelProperty("(0)")
    @TableField("BANK_ADDRESS")
    private String bankAddress;

    @ApiModelProperty("(20)")
    @TableField("CUSTOMS_NO")
    private String customsNo;

    @ApiModelProperty("(0)")
    @TableField("BILL_CONTENT")
    private String billContent;

    @ApiModelProperty("(0)")
    @TableField("MAPPING_TYPE")
    private String mappingType;

    @ApiModelProperty("(0)")
    @TableField("MEMO")
    private String memo;

    @ApiModelProperty("(50)")
    @TableField("MAPPING_ABBREVIATION")
    private String mappingAbbreviation;

    @ApiModelProperty("(0)")
    @TableField("PROVINCE")
    private String province;

    @ApiModelProperty("(0)")
    @TableField("CITY")
    private String city;

    @ApiModelProperty("(510)")
    @TableField("ENTE_ADDR_EN")
    private String enteAddrEn;

    @ApiModelProperty("(1)")
    @TableField("IS_EMAIL")
    private String isEmail;

    @ApiModelProperty("(0)")
    @TableField("EXPRESS_WEBSITE")
    private String expressWebsite;

    @ApiModelProperty("(0)")
    @TableField("TEL_QT")
    private String telQt;

    @ApiModelProperty("(0)")
    @TableField("CUSTOMS_TYPE")
    private String customsType;

    @ApiModelProperty("(0)")
    @TableField("INSPECT_NO")
    private String inspectNo;

    @ApiModelProperty("(0)")
    @TableField("INSPECT_TYPE")
    private String inspectType;

    @ApiModelProperty("(0)")
    @TableField("SALES")
    private String sales;

    @ApiModelProperty("(0)")
    @TableField("OPER")
    private String oper;

    @ApiModelProperty("(0)")
    @TableField("ZZ")
    private String zz;

    @ApiModelProperty("(1)")
    @TableField("IS_USER_CONTROL")
    private String isUserControl;

    @ApiModelProperty("(1)")
    @TableField("CAN_BORROW")
    private String canBorrow;

    @ApiModelProperty("(30)")
    @TableField("TAX_ID")
    private String taxId;

    @ApiModelProperty("(80)")
    @TableField("BANK_NAME")
    private String bankName;

    @ApiModelProperty("(50)")
    @TableField("BANK_ACCOUNT")
    private String bankAccount;

    @ApiModelProperty("(20)")
    @TableField("ACCOUNT_MODE")
    private String accountMode;

    @ApiModelProperty("(20)")
    @TableField("PAY_MODE")
    private String payMode;

    @ApiModelProperty("(22)")
    @TableField("PAYEE_DAY")
    private Integer payeeDay;

    @ApiModelProperty("(50)")
    @TableField("ACCOUNT")
    private String account;

    @ApiModelProperty("(80)")
    @TableField("BANK")
    private String bank;

    @ApiModelProperty("(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    @ApiModelProperty("(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public EtmsCrmPartnerEntity() {
        this.setSubClazz(EtmsCrmPartnerEntity.class);
    }

    public EtmsCrmPartnerEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public EtmsCrmPartnerEntity setCode(String code) {
        this.code = code;
        this.nodifySetFiled("code", code);
        return this;
    }

    public String getCode() {
        this.nodifyGetFiled("code");
        return code;
    }

    public EtmsCrmPartnerEntity setName(String name) {
        this.name = name;
        this.nodifySetFiled("name", name);
        return this;
    }

    public String getName() {
        this.nodifyGetFiled("name");
        return name;
    }

    public EtmsCrmPartnerEntity setEnteCode(String enteCode) {
        this.enteCode = enteCode;
        this.nodifySetFiled("enteCode", enteCode);
        return this;
    }

    public String getEnteCode() {
        this.nodifyGetFiled("enteCode");
        return enteCode;
    }

    public EtmsCrmPartnerEntity setStatus(String status) {
        this.status = status;
        this.nodifySetFiled("status", status);
        return this;
    }

    public String getStatus() {
        this.nodifyGetFiled("status");
        return status;
    }

    public EtmsCrmPartnerEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public EtmsCrmPartnerEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public EtmsCrmPartnerEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public EtmsCrmPartnerEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public EtmsCrmPartnerEntity setCreateCompany(String createCompany) {
        this.createCompany = createCompany;
        this.nodifySetFiled("createCompany", createCompany);
        return this;
    }

    public String getCreateCompany() {
        this.nodifyGetFiled("createCompany");
        return createCompany;
    }

    public EtmsCrmPartnerEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public EtmsCrmPartnerEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public EtmsCrmPartnerEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public EtmsCrmPartnerEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public EtmsCrmPartnerEntity setUpdateCompany(String updateCompany) {
        this.updateCompany = updateCompany;
        this.nodifySetFiled("updateCompany", updateCompany);
        return this;
    }

    public String getUpdateCompany() {
        this.nodifyGetFiled("updateCompany");
        return updateCompany;
    }

    public EtmsCrmPartnerEntity setEnteCodeEx(String enteCodeEx) {
        this.enteCodeEx = enteCodeEx;
        this.nodifySetFiled("enteCodeEx", enteCodeEx);
        return this;
    }

    public String getEnteCodeEx() {
        this.nodifyGetFiled("enteCodeEx");
        return enteCodeEx;
    }

    public EtmsCrmPartnerEntity setTrade(String trade) {
        this.trade = trade;
        this.nodifySetFiled("trade", trade);
        return this;
    }

    public String getTrade() {
        this.nodifyGetFiled("trade");
        return trade;
    }

    public EtmsCrmPartnerEntity setLsp(String lsp) {
        this.lsp = lsp;
        this.nodifySetFiled("lsp", lsp);
        return this;
    }

    public String getLsp() {
        this.nodifyGetFiled("lsp");
        return lsp;
    }

    public EtmsCrmPartnerEntity setEnteNameEn(String enteNameEn) {
        this.enteNameEn = enteNameEn;
        this.nodifySetFiled("enteNameEn", enteNameEn);
        return this;
    }

    public String getEnteNameEn() {
        this.nodifyGetFiled("enteNameEn");
        return enteNameEn;
    }

    public EtmsCrmPartnerEntity setEnteAddrCn(String enteAddrCn) {
        this.enteAddrCn = enteAddrCn;
        this.nodifySetFiled("enteAddrCn", enteAddrCn);
        return this;
    }

    public String getEnteAddrCn() {
        this.nodifyGetFiled("enteAddrCn");
        return enteAddrCn;
    }

    public EtmsCrmPartnerEntity setPostcode(String postcode) {
        this.postcode = postcode;
        this.nodifySetFiled("postcode", postcode);
        return this;
    }

    public String getPostcode() {
        this.nodifyGetFiled("postcode");
        return postcode;
    }

    public EtmsCrmPartnerEntity setCountry(String country) {
        this.country = country;
        this.nodifySetFiled("country", country);
        return this;
    }

    public String getCountry() {
        this.nodifyGetFiled("country");
        return country;
    }

    public EtmsCrmPartnerEntity setContactPerson(String contactPerson) {
        this.contactPerson = contactPerson;
        this.nodifySetFiled("contactPerson", contactPerson);
        return this;
    }

    public String getContactPerson() {
        this.nodifyGetFiled("contactPerson");
        return contactPerson;
    }

    public EtmsCrmPartnerEntity setTel(String tel) {
        this.tel = tel;
        this.nodifySetFiled("tel", tel);
        return this;
    }

    public String getTel() {
        this.nodifyGetFiled("tel");
        return tel;
    }

    public EtmsCrmPartnerEntity setFax(String fax) {
        this.fax = fax;
        this.nodifySetFiled("fax", fax);
        return this;
    }

    public String getFax() {
        this.nodifyGetFiled("fax");
        return fax;
    }

    public EtmsCrmPartnerEntity setEmail(String email) {
        this.email = email;
        this.nodifySetFiled("email", email);
        return this;
    }

    public String getEmail() {
        this.nodifyGetFiled("email");
        return email;
    }

    public EtmsCrmPartnerEntity setWebsite(String website) {
        this.website = website;
        this.nodifySetFiled("website", website);
        return this;
    }

    public String getWebsite() {
        this.nodifyGetFiled("website");
        return website;
    }

    public EtmsCrmPartnerEntity setSwiftAddress(String swiftAddress) {
        this.swiftAddress = swiftAddress;
        this.nodifySetFiled("swiftAddress", swiftAddress);
        return this;
    }

    public String getSwiftAddress() {
        this.nodifyGetFiled("swiftAddress");
        return swiftAddress;
    }

    public EtmsCrmPartnerEntity setBankAddress(String bankAddress) {
        this.bankAddress = bankAddress;
        this.nodifySetFiled("bankAddress", bankAddress);
        return this;
    }

    public String getBankAddress() {
        this.nodifyGetFiled("bankAddress");
        return bankAddress;
    }

    public EtmsCrmPartnerEntity setCustomsNo(String customsNo) {
        this.customsNo = customsNo;
        this.nodifySetFiled("customsNo", customsNo);
        return this;
    }

    public String getCustomsNo() {
        this.nodifyGetFiled("customsNo");
        return customsNo;
    }

    public EtmsCrmPartnerEntity setBillContent(String billContent) {
        this.billContent = billContent;
        this.nodifySetFiled("billContent", billContent);
        return this;
    }

    public String getBillContent() {
        this.nodifyGetFiled("billContent");
        return billContent;
    }

    public EtmsCrmPartnerEntity setMappingType(String mappingType) {
        this.mappingType = mappingType;
        this.nodifySetFiled("mappingType", mappingType);
        return this;
    }

    public String getMappingType() {
        this.nodifyGetFiled("mappingType");
        return mappingType;
    }

    public EtmsCrmPartnerEntity setMemo(String memo) {
        this.memo = memo;
        this.nodifySetFiled("memo", memo);
        return this;
    }

    public String getMemo() {
        this.nodifyGetFiled("memo");
        return memo;
    }

    public EtmsCrmPartnerEntity setMappingAbbreviation(String mappingAbbreviation) {
        this.mappingAbbreviation = mappingAbbreviation;
        this.nodifySetFiled("mappingAbbreviation", mappingAbbreviation);
        return this;
    }

    public String getMappingAbbreviation() {
        this.nodifyGetFiled("mappingAbbreviation");
        return mappingAbbreviation;
    }

    public EtmsCrmPartnerEntity setProvince(String province) {
        this.province = province;
        this.nodifySetFiled("province", province);
        return this;
    }

    public String getProvince() {
        this.nodifyGetFiled("province");
        return province;
    }

    public EtmsCrmPartnerEntity setCity(String city) {
        this.city = city;
        this.nodifySetFiled("city", city);
        return this;
    }

    public String getCity() {
        this.nodifyGetFiled("city");
        return city;
    }

    public EtmsCrmPartnerEntity setEnteAddrEn(String enteAddrEn) {
        this.enteAddrEn = enteAddrEn;
        this.nodifySetFiled("enteAddrEn", enteAddrEn);
        return this;
    }

    public String getEnteAddrEn() {
        this.nodifyGetFiled("enteAddrEn");
        return enteAddrEn;
    }

    public EtmsCrmPartnerEntity setIsEmail(String isEmail) {
        this.isEmail = isEmail;
        this.nodifySetFiled("isEmail", isEmail);
        return this;
    }

    public String getIsEmail() {
        this.nodifyGetFiled("isEmail");
        return isEmail;
    }

    public EtmsCrmPartnerEntity setExpressWebsite(String expressWebsite) {
        this.expressWebsite = expressWebsite;
        this.nodifySetFiled("expressWebsite", expressWebsite);
        return this;
    }

    public String getExpressWebsite() {
        this.nodifyGetFiled("expressWebsite");
        return expressWebsite;
    }

    public EtmsCrmPartnerEntity setTelQt(String telQt) {
        this.telQt = telQt;
        this.nodifySetFiled("telQt", telQt);
        return this;
    }

    public String getTelQt() {
        this.nodifyGetFiled("telQt");
        return telQt;
    }

    public EtmsCrmPartnerEntity setCustomsType(String customsType) {
        this.customsType = customsType;
        this.nodifySetFiled("customsType", customsType);
        return this;
    }

    public String getCustomsType() {
        this.nodifyGetFiled("customsType");
        return customsType;
    }

    public EtmsCrmPartnerEntity setInspectNo(String inspectNo) {
        this.inspectNo = inspectNo;
        this.nodifySetFiled("inspectNo", inspectNo);
        return this;
    }

    public String getInspectNo() {
        this.nodifyGetFiled("inspectNo");
        return inspectNo;
    }

    public EtmsCrmPartnerEntity setInspectType(String inspectType) {
        this.inspectType = inspectType;
        this.nodifySetFiled("inspectType", inspectType);
        return this;
    }

    public String getInspectType() {
        this.nodifyGetFiled("inspectType");
        return inspectType;
    }

    public EtmsCrmPartnerEntity setSales(String sales) {
        this.sales = sales;
        this.nodifySetFiled("sales", sales);
        return this;
    }

    public String getSales() {
        this.nodifyGetFiled("sales");
        return sales;
    }

    public EtmsCrmPartnerEntity setOper(String oper) {
        this.oper = oper;
        this.nodifySetFiled("oper", oper);
        return this;
    }

    public String getOper() {
        this.nodifyGetFiled("oper");
        return oper;
    }

    public EtmsCrmPartnerEntity setZz(String zz) {
        this.zz = zz;
        this.nodifySetFiled("zz", zz);
        return this;
    }

    public String getZz() {
        this.nodifyGetFiled("zz");
        return zz;
    }

    public EtmsCrmPartnerEntity setIsUserControl(String isUserControl) {
        this.isUserControl = isUserControl;
        this.nodifySetFiled("isUserControl", isUserControl);
        return this;
    }

    public String getIsUserControl() {
        this.nodifyGetFiled("isUserControl");
        return isUserControl;
    }

    public EtmsCrmPartnerEntity setCanBorrow(String canBorrow) {
        this.canBorrow = canBorrow;
        this.nodifySetFiled("canBorrow", canBorrow);
        return this;
    }

    public String getCanBorrow() {
        this.nodifyGetFiled("canBorrow");
        return canBorrow;
    }

    public EtmsCrmPartnerEntity setTaxId(String taxId) {
        this.taxId = taxId;
        this.nodifySetFiled("taxId", taxId);
        return this;
    }

    public String getTaxId() {
        this.nodifyGetFiled("taxId");
        return taxId;
    }

    public EtmsCrmPartnerEntity setBankName(String bankName) {
        this.bankName = bankName;
        this.nodifySetFiled("bankName", bankName);
        return this;
    }

    public String getBankName() {
        this.nodifyGetFiled("bankName");
        return bankName;
    }

    public EtmsCrmPartnerEntity setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
        this.nodifySetFiled("bankAccount", bankAccount);
        return this;
    }

    public String getBankAccount() {
        this.nodifyGetFiled("bankAccount");
        return bankAccount;
    }

    public EtmsCrmPartnerEntity setAccountMode(String accountMode) {
        this.accountMode = accountMode;
        this.nodifySetFiled("accountMode", accountMode);
        return this;
    }

    public String getAccountMode() {
        this.nodifyGetFiled("accountMode");
        return accountMode;
    }

    public EtmsCrmPartnerEntity setPayMode(String payMode) {
        this.payMode = payMode;
        this.nodifySetFiled("payMode", payMode);
        return this;
    }

    public String getPayMode() {
        this.nodifyGetFiled("payMode");
        return payMode;
    }

    public EtmsCrmPartnerEntity setPayeeDay(Integer payeeDay) {
        this.payeeDay = payeeDay;
        this.nodifySetFiled("payeeDay", payeeDay);
        return this;
    }

    public Integer getPayeeDay() {
        this.nodifyGetFiled("payeeDay");
        return payeeDay;
    }

    public EtmsCrmPartnerEntity setAccount(String account) {
        this.account = account;
        this.nodifySetFiled("account", account);
        return this;
    }

    public String getAccount() {
        this.nodifyGetFiled("account");
        return account;
    }

    public EtmsCrmPartnerEntity setBank(String bank) {
        this.bank = bank;
        this.nodifySetFiled("bank", bank);
        return this;
    }

    public String getBank() {
        this.nodifyGetFiled("bank");
        return bank;
    }

    public EtmsCrmPartnerEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public EtmsCrmPartnerEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

}
