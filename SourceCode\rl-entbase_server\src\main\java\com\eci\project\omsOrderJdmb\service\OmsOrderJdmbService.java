package com.eci.project.omsOrderJdmb.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eci.common.DataExtend;
import com.eci.common.NoManager;
import com.eci.common.ZsrGloableConfig;
import com.eci.common.enums.Enums;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.exception.BaseException;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.omsOrder.entity.OmsOrderEntity;
import com.eci.project.omsOrderJdmb.dao.OmsOrderJdmbDao;
import com.eci.project.omsOrderJdmb.entity.OmsOrderJdmbEntity;
import com.eci.project.omsOrderJdmb.validate.OmsOrderJdmbVal;
import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.List;


/**
 * 接单模板Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-04-24
 */
@Service
@Slf4j
public class OmsOrderJdmbService implements EciBaseService<OmsOrderJdmbEntity> {

    @Autowired
    private OmsOrderJdmbDao omsOrderJdmbDao;

    @Autowired
    private OmsOrderJdmbVal omsOrderJdmbVal;


    @Override
    public TgPageInfo queryPageList(OmsOrderJdmbEntity entity) {
        EciQuery<OmsOrderJdmbEntity> eciQuery = EciQuery.buildQuery(entity);
        List<OmsOrderJdmbEntity> entities = omsOrderJdmbDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderJdmbEntity save(OmsOrderJdmbEntity orderJdmb) {
        // 返回实体对象
        OmsOrderJdmbEntity omsOrderJdmbEntity = null;
        omsOrderJdmbVal.saveValidate(orderJdmb, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            orderJdmb.setConsigneeCode(DataExtend.toCode(orderJdmb.getConsigneeCode()));
            orderJdmb.setShipper(DataExtend.toCode(orderJdmb.getShipper()));
            orderJdmb.setReceiver(DataExtend.toCode(orderJdmb.getReceiver()));
            orderJdmb.setOpType(DataExtend.toCode(orderJdmb.getOpType()));
            orderJdmb.setProductCode(DataExtend.toCode(orderJdmb.getProductCode()));
            orderJdmb.setFwlxCode(orderJdmb.getFwlxCode());
            orderJdmb.setJdNodeCode(DataExtend.toCode(orderJdmb.getJdNodeCode()));

            orderJdmb.setJdmbNo(NoManager.createOrderTemplateNo("JDMB"));
            orderJdmb.setOrderNo(orderJdmb.getJdmbNo());
            orderJdmb.setIsUse(Enums.YNStatus.Y.getCode());

            omsOrderJdmbDao.insertOne(orderJdmb);

        } else {

            QueryWrapper query = new QueryWrapper();
            query.eq("JDMB_NO", orderJdmb.getOrderNo());
            OmsOrderJdmbEntity orderJdMb = omsOrderJdmbDao.selectOne(query);

            orderJdMb.setConsigneeCode(DataExtend.toCode(orderJdMb.getConsigneeCode()));
            orderJdMb.setShipper(DataExtend.toCode(orderJdMb.getShipper()));
            orderJdMb.setReceiver(DataExtend.toCode(orderJdMb.getReceiver()));
            orderJdMb.setOpType(DataExtend.toCode(orderJdMb.getOpType()));
            orderJdMb.setProductCode(DataExtend.toCode(orderJdMb.getProductCode()));
            orderJdMb.setFwlxCode(orderJdMb.getFwlxCode());
            orderJdMb.setJdNodeCode(DataExtend.toCode(orderJdMb.getJdNodeCode()));

            // orderJdMb.UpdateByDataFields(context, ORDER_JDMB.Fields.JDMB_NO, orderJdMb.JDMB_NO);

            omsOrderJdmbDao.updateByEntityId(orderJdMb);
            orderJdmb = orderJdMb;
        }
        return omsOrderJdmbEntity;
    }

    @Transactional(rollbackFor = Exception.class)
    public OmsOrderJdmbEntity save(OmsOrderJdmbEntity orderJdmb, boolean isAdd) {
        // 返回实体对象

        omsOrderJdmbVal.saveValidate(orderJdmb, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            orderJdmb.setConsigneeCode(DataExtend.toCode(orderJdmb.getConsigneeCode()));
            orderJdmb.setShipper(DataExtend.toCode(orderJdmb.getShipper()));
            orderJdmb.setReceiver(DataExtend.toCode(orderJdmb.getReceiver()));
            orderJdmb.setOpType(DataExtend.toCode(orderJdmb.getOpType()));
            orderJdmb.setProductCode(DataExtend.toCode(orderJdmb.getProductCode()));
            orderJdmb.setFwlxCode(orderJdmb.getFwlxCode());
            orderJdmb.setJdNodeCode(DataExtend.toCode(orderJdmb.getJdNodeCode()));

            orderJdmb.setJdmbNo(NoManager.createOrderTemplateNo(null));
            orderJdmb.setOrderNo(orderJdmb.getJdmbNo());
            orderJdmb.setIsUse(Enums.YNStatus.Y.getCode());

            orderJdmb.setCreateDate(new java.util.Date());
            orderJdmb.setUpdateDate(new java.util.Date());
            orderJdmb.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            orderJdmb.setCreateUserName(UserContext.getUserInfo().getTrueName());
            orderJdmb.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            orderJdmb.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            orderJdmb.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            orderJdmb.setGroupName(UserContext.getUserInfo().getCompanyName());
            orderJdmb.setNodeCode(UserContext.getUserInfo().getDeptCode());
            orderJdmb.setNodeName(UserContext.getUserInfo().getDeptName());
            orderJdmb.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            orderJdmb.setCompanyName(UserContext.getUserInfo().getCompanyName());

            omsOrderJdmbDao.insertOne(orderJdmb);

        } else {

            QueryWrapper query = new QueryWrapper();
            query.eq("JDMB_NO", orderJdmb.getOrderNo());
            OmsOrderJdmbEntity orderJdMb = omsOrderJdmbDao.selectOne(query);

            orderJdMb.setConsigneeCode(DataExtend.toCode(orderJdMb.getConsigneeCode()));
            orderJdMb.setShipper(DataExtend.toCode(orderJdMb.getShipper()));
            orderJdMb.setReceiver(DataExtend.toCode(orderJdMb.getReceiver()));
            orderJdMb.setOpType(DataExtend.toCode(orderJdMb.getOpType()));
            orderJdMb.setProductCode(DataExtend.toCode(orderJdMb.getProductCode()));
            orderJdMb.setFwlxCode(orderJdMb.getFwlxCode());
            orderJdMb.setJdNodeCode(DataExtend.toCode(orderJdMb.getJdNodeCode()));

            orderJdMb.setUpdateDate(new java.util.Date());
            orderJdMb.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            orderJdMb.setUpdateUserName(UserContext.getUserInfo().getTrueName());

            omsOrderJdmbDao.updateByEntityId(orderJdMb);

            orderJdmb = orderJdMb;
        }

        return orderJdmb;
    }


    /**
     * 如需修改这个方法，请联系帅哥
     *
     * @param requestOrder
     * @param isAdd
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderJdmbEntity save(OmsOrderEntity requestOrder, boolean isAdd) {
        if (requestOrder == null) {
            throw new BaseException("传入参数为空！");
        }
        // 返回实体对象
        OmsOrderJdmbEntity orderJdmb = new OmsOrderJdmbEntity();

        omsOrderJdmbVal.saveValidate(orderJdmb, BllContext.getBusinessType());

        orderJdmb.setConsigneeCode(DataExtend.toCode(requestOrder.getConsigneeCode()));
        orderJdmb.setShipper(DataExtend.toCode(requestOrder.getShipper()));
        orderJdmb.setReceiver(DataExtend.toCode(requestOrder.getReceiver()));
        orderJdmb.setOpType(DataExtend.toCode(requestOrder.getOpType()));
        orderJdmb.setProductCode(DataExtend.toCode(requestOrder.getProductCode()));
        orderJdmb.setFwlxCode(requestOrder.getFwlxCode());
//            orderJdmb.setJdNodeCode(DataExtend.toCode(requestOrder.getJdNodeCode()));

        orderJdmb.setJdmbNo(requestOrder.getOrderNo());
        orderJdmb.setOrderNo(orderJdmb.getJdmbNo());
        orderJdmb.setIsUse(Enums.YNStatus.Y.getCode());

        orderJdmb.setCreateDate(new java.util.Date());
        orderJdmb.setUpdateDate(new java.util.Date());
        orderJdmb.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
        orderJdmb.setCreateUserName(UserContext.getUserInfo().getTrueName());
        orderJdmb.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
        orderJdmb.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        orderJdmb.setGroupCode(UserContext.getUserInfo().getCompanyCode());
        orderJdmb.setGroupName(UserContext.getUserInfo().getCompanyName());
        orderJdmb.setNodeCode(UserContext.getUserInfo().getDeptCode());
        orderJdmb.setNodeName(UserContext.getUserInfo().getDeptName());
        orderJdmb.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
        orderJdmb.setCompanyName(UserContext.getUserInfo().getCompanyName());

        omsOrderJdmbDao.insertOne(orderJdmb);

        return orderJdmb;
    }


    @Override
    public List<OmsOrderJdmbEntity> selectList(OmsOrderJdmbEntity entity) {
        return omsOrderJdmbDao.selectList(entity);
    }

    @Override
    public OmsOrderJdmbEntity selectOneById(Serializable id) {
        return omsOrderJdmbDao.selectById(id);
    }


    @Override
    public void insertBatch(List<OmsOrderJdmbEntity> list) {
        omsOrderJdmbDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return omsOrderJdmbDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return omsOrderJdmbDao.deleteById(id);
    }

}