package com.eci.project.fzgjBdArea.service;

import com.eci.crud.service.EciBaseService;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjBdArea.entity.FzgjBdAreaBaseEntity;
import com.eci.project.fzgjBdArea.entity.FzgjBdAreaEntity;
import com.github.pagehelper.PageInfo;


/**
* 公路乡镇地区Service接口
* 业务逻辑层, 接口代码, 只需要写自定义的部分, 增删改查部分在父级接口已经实现
* @<NAME_EMAIL>
* @date 2025-03-26
*/
public interface IFzgjBdAreaService extends EciBaseService<FzgjBdAreaEntity> {
    public FzgjBdAreaBaseEntity saveBase(FzgjBdAreaBaseEntity entity);
}
