<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.fzgjBdOmsPagesFk.dao.FzgjBdOmsPagesFkDao">
    <resultMap type="FzgjBdOmsPagesFkEntity" id="FzgjBdOmsPagesFkResult">
        <result property="guid" column="GUID"/>
        <result property="code" column="CODE"/>
        <result property="name" column="NAME"/>
        <result property="status" column="STATUS"/>
        <result property="seq" column="SEQ"/>
        <result property="memo" column="MEMO"/>
        <result property="feedbackUrl" column="FEEDBACK_URL"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="fwxmCode" column="FWXM_CODE"/>
        <result property="fwxmName" column="FWXM_NAME"/>
    </resultMap>

    <sql id="selectFzgjBdOmsPagesFkEntityVo">
        select
            GUID,
            CODE,
            NAME,
            STATUS,
            SEQ,
            MEMO,
            FEEDBACK_URL,
            GROUP_NAME,
            GROUP_CODE,
            CREATE_USER,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_DATE,
            CREATE_USER_NAME,
            UPDATE_USER_NAME,
            FWXM_CODE,
            FWXM_NAME
        from FZGJ_BD_OMS_PAGES_FK
    </sql>
</mapper>