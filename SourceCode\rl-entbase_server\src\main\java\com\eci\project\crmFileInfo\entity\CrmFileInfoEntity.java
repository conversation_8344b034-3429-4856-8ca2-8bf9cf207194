package com.eci.project.crmFileInfo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 附件对象 CRM_FILE_INFO
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-05-21
*/
@ApiModel("附件")
@TableName("CRM_FILE_INFO")
@FieldNameConstants
public class CrmFileInfoEntity extends EciBaseEntity{
    /**
    * 主键
    */
    @ApiModelProperty("主键(36)")
    @TableId("GUID")
    private String guid;

    /**
    * 所属附件类型
    */
    @ApiModelProperty("所属附件类型(50)")
    @TableField("FILE_TYPE")
    private String fileType;
    @TableField(exist = false)
    private String fileTypeName;

    /**
    * 附件地址
    */
    @ApiModelProperty("附件地址(1,000)")
    @TableField("FILE_PATH")
    private String filePath;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 修改人
    */
    @ApiModelProperty("修改人(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 修改日期
    */
    @ApiModelProperty("修改日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("修改日期开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("修改日期结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 原始文件名
    */
    @ApiModelProperty("原始文件名(100)")
    @TableField("ORIGIN_FILE_NAME")
    private String originFileName;

    /**
    * 文件编号,customer.guid
    */
    @ApiModelProperty("文件编号,customer.guid(100)")
    @TableField("FILE_NO")
    private String fileNo;

    /**
    * 文件格式
    */
    @ApiModelProperty("文件格式(20)")
    @TableField("FILE_FORMAT")
    private String fileFormat;

    /**
    * 备注
    */
    @ApiModelProperty("备注(500)")
    @TableField("MEMO")
    private String memo;

    /**
    * 状态
    */
    @ApiModelProperty("状态(20)")
    @TableField("STATUS")
    private String status;

    /**
    * 组织编码（部门）
    */
    @ApiModelProperty("组织编码（部门）(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 组织名称（部门名称）
    */
    @ApiModelProperty("组织名称（部门名称）(100)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 企业编码
    */
    @ApiModelProperty("企业编码(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 企业名称
    */
    @ApiModelProperty("企业名称(100)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 集团编码
    */
    @ApiModelProperty("集团编码(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 集团名称
    */
    @ApiModelProperty("集团名称(100)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 创建人名称
    */
    @ApiModelProperty("创建人名称(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 修改人名称
    */
    @ApiModelProperty("修改人名称(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 是否外部系统上传附件
    */
    @ApiModelProperty("是否外部系统上传附件(1)")
    @TableField("IS_WB")
    private String isWb;

    /**
    * 文件内容
    */
    @ApiModelProperty("文件内容(4,000)")
    @TableField("FILE_CONTENT")
    private String fileContent;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public CrmFileInfoEntity() {
        this.setSubClazz(CrmFileInfoEntity.class);
    }

    public CrmFileInfoEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public CrmFileInfoEntity setFileType(String fileType) {
        this.fileType = fileType;
        this.nodifySetFiled("fileType", fileType);
        return this;
    }

    public String getFileType() {
        this.nodifyGetFiled("fileType");
        return fileType;
    }

    public CrmFileInfoEntity setFileTypeName(String fileTypeName) {
        this.fileTypeName = fileTypeName;
        this.nodifySetFiled("fileTypeName", fileTypeName);
        return this;
    }

    public String getFileTypeName() {
        this.nodifyGetFiled("fileTypeName");
        return fileTypeName;
    }

    public CrmFileInfoEntity setFilePath(String filePath) {
        this.filePath = filePath;
        this.nodifySetFiled("filePath", filePath);
        return this;
    }

    public String getFilePath() {
        this.nodifyGetFiled("filePath");
        return filePath;
    }

    public CrmFileInfoEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public CrmFileInfoEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public CrmFileInfoEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public CrmFileInfoEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public CrmFileInfoEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public CrmFileInfoEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public CrmFileInfoEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public CrmFileInfoEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public CrmFileInfoEntity setOriginFileName(String originFileName) {
        this.originFileName = originFileName;
        this.nodifySetFiled("originFileName", originFileName);
        return this;
    }

    public String getOriginFileName() {
        this.nodifyGetFiled("originFileName");
        return originFileName;
    }

    public CrmFileInfoEntity setFileNo(String fileNo) {
        this.fileNo = fileNo;
        this.nodifySetFiled("fileNo", fileNo);
        return this;
    }

    public String getFileNo() {
        this.nodifyGetFiled("fileNo");
        return fileNo;
    }

    public CrmFileInfoEntity setFileFormat(String fileFormat) {
        this.fileFormat = fileFormat;
        this.nodifySetFiled("fileFormat", fileFormat);
        return this;
    }

    public String getFileFormat() {
        this.nodifyGetFiled("fileFormat");
        return fileFormat;
    }

    public CrmFileInfoEntity setMemo(String memo) {
        this.memo = memo;
        this.nodifySetFiled("memo", memo);
        return this;
    }

    public String getMemo() {
        this.nodifyGetFiled("memo");
        return memo;
    }

    public CrmFileInfoEntity setStatus(String status) {
        this.status = status;
        this.nodifySetFiled("status", status);
        return this;
    }

    public String getStatus() {
        this.nodifyGetFiled("status");
        return status;
    }

    public CrmFileInfoEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public CrmFileInfoEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public CrmFileInfoEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public CrmFileInfoEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public CrmFileInfoEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public CrmFileInfoEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public CrmFileInfoEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public CrmFileInfoEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public CrmFileInfoEntity setIsWb(String isWb) {
        this.isWb = isWb;
        this.nodifySetFiled("isWb", isWb);
        return this;
    }

    public String getIsWb() {
        this.nodifyGetFiled("isWb");
        return isWb;
    }

    public CrmFileInfoEntity setFileContent(String fileContent) {
        this.fileContent = fileContent;
        this.nodifySetFiled("fileContent", fileContent);
        return this;
    }

    public String getFileContent() {
        this.nodifyGetFiled("fileContent");
        return fileContent;
    }

}
