package com.eci.project.vFzgjBdArea.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.vFzgjBdArea.service.VFzgjBdAreaService;
import com.eci.project.vFzgjBdArea.entity.VFzgjBdAreaEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* Controller
*
* @<NAME_EMAIL>
* @date 2025-05-20
*/
@Api(tags = "")
@RestController
@RequestMapping("/vFzgjBdArea")
public class VFzgjBdAreaController extends EciBaseController {

    @Autowired
    private VFzgjBdAreaService vFzgjBdAreaService;


    @ApiOperation(":保存")
    @EciLog(title = ":新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody VFzgjBdAreaEntity entity){
        VFzgjBdAreaEntity vFzgjBdAreaEntity =vFzgjBdAreaService.save(entity);
        return ResponseMsgUtil.success(10001,vFzgjBdAreaEntity);
    }


    @ApiOperation(":查询列表")
    @EciLog(title = ":查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody VFzgjBdAreaEntity entity){
        List<VFzgjBdAreaEntity> vFzgjBdAreaEntities = vFzgjBdAreaService.selectList(entity);
        return ResponseMsgUtil.success(10001,vFzgjBdAreaEntities);
    }


    @ApiOperation(":分页查询列表")
    @EciLog(title = ":分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody VFzgjBdAreaEntity entity){
        TgPageInfo tgPageInfo = vFzgjBdAreaService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }





    @ApiOperation(":根据ID字符串删除多条")
    @EciLog(title = ":根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody VFzgjBdAreaEntity entity) {
        int count = vFzgjBdAreaService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}