package com.eci.project.fzgjTaskLimitationFixed.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjTaskLimitationFixed.dao.FzgjTaskLimitationFixedDao;
import com.eci.project.fzgjTaskLimitationFixed.entity.FzgjTaskLimitationFixedEntity;
import com.eci.project.fzgjTaskLimitationFixed.validate.FzgjTaskLimitationFixedVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 作业环节及时效标准条件Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-05-22
*/
@Service
@Slf4j
public class FzgjTaskLimitationFixedService implements EciBaseService<FzgjTaskLimitationFixedEntity> {

    @Autowired
    private FzgjTaskLimitationFixedDao fzgjTaskLimitationFixedDao;

    @Autowired
    private FzgjTaskLimitationFixedVal fzgjTaskLimitationFixedVal;


    @Override
    public TgPageInfo queryPageList(FzgjTaskLimitationFixedEntity entity) {
        EciQuery<FzgjTaskLimitationFixedEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjTaskLimitationFixedEntity> entities = fzgjTaskLimitationFixedDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjTaskLimitationFixedEntity save(FzgjTaskLimitationFixedEntity entity) {
        // 返回实体对象
        FzgjTaskLimitationFixedEntity fzgjTaskLimitationFixedEntity = null;
        fzgjTaskLimitationFixedVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjTaskLimitationFixedEntity = fzgjTaskLimitationFixedDao.insertOne(entity);

        }else{

            fzgjTaskLimitationFixedEntity = fzgjTaskLimitationFixedDao.updateByEntityId(entity);

        }
        return fzgjTaskLimitationFixedEntity;
    }

    @Override
    public List<FzgjTaskLimitationFixedEntity> selectList(FzgjTaskLimitationFixedEntity entity) {
        return fzgjTaskLimitationFixedDao.selectList(entity);
    }

    @Override
    public FzgjTaskLimitationFixedEntity selectOneById(Serializable id) {
        return fzgjTaskLimitationFixedDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjTaskLimitationFixedEntity> list) {
        fzgjTaskLimitationFixedDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjTaskLimitationFixedDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjTaskLimitationFixedDao.deleteById(id);
    }

}