package com.eci.project.etmsBdTruckGpsNow.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.etmsBdTruckGpsNow.entity.EtmsBdTruckGpsNowEntity;

import org.springframework.stereotype.Service;


/**
* Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-14
*/
@Service
public class EtmsBdTruckGpsNowVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(EtmsBdTruckGpsNowEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(EtmsBdTruckGpsNowEntity entity, BusinessType businessType) {

    }

}
