package com.eci.project.dhlWorkBasic.validate;

import com.eci.common.enums.OrderEnum;
import com.eci.exception.BaseException;
import com.eci.log.enums.BusinessType;
import com.eci.project.dhlWorkBasic.entity.DhlWorkBasicEntity;
import org.springframework.stereotype.Service;


/**
 * 运单信息Val接口
 * 验证接口
 *
 * @<NAME_EMAIL>
 * @date 2025-04-22
 */
@Service
public class DhlWorkBasicVal {

    /**
     * 删除验证
     *
     * @param entity
     */
    public void deleteValidate(DhlWorkBasicEntity entity) {

    }

    /**
     * 保存验证
     *
     * @param entity
     */
    public void saveValidate(DhlWorkBasicEntity entity, BusinessType businessType) {

    }

    /// <summary>
    /// 保存验证
    /// </summary>
    /// <param name="context"></param>
    /// <param name="dhlWorkBasic"></param>
    /// <param name="toStatus"></param>
    public void DhlVendorToDosValidate(DhlWorkBasicEntity dhlWorkBasic, String toStatus) {
        if (dhlWorkBasic == null) {
            throw new BaseException("DHL_WORK_BASIC IS NULL");
            // dhlWorkBasic.IsNull("DHL_WORK_BASIC IS NULL");
        }

        OrderEnum.DHLOrderStatus orderStatus = OrderEnum.DHLOrderStatus.valueOf(toStatus);

        switch (orderStatus) {
            case Submit:
            case Reject:
            case Back:
            case Accept:
                //暂时没验证
                break;
            case Departure:
                if (!dhlWorkBasic.getToStatus().toUpperCase().equals(OrderEnum.DHLOrderStatus.Accept.getCode().toUpperCase())) {
                    throw new BaseException("状态不为【审核通过】，操作失败！");
                }
                break;
            case Arrived:
                if (!dhlWorkBasic.getToStatus().toUpperCase().equals(OrderEnum.DHLOrderStatus.Departure.getCode().toUpperCase())) {
                    throw new BaseException("状态不为【发车】，操作失败！");
                }
                break;
            case Complete:
                if (!dhlWorkBasic.getToStatus().toUpperCase().equals(OrderEnum.DHLOrderStatus.Arrived.getCode().toUpperCase()) && !dhlWorkBasic.getToStatus().toUpperCase().equals(OrderEnum.DHLOrderStatus.Complete.getCode().toUpperCase())) {
                    throw new BaseException("状态不为【到达】，操作失败！");
                }
                break;
            default:
                throw new BaseException("DhlVendorToDos【status】错误，参考OrderEnum.DHLOrderStatus");
        }
    }
}
