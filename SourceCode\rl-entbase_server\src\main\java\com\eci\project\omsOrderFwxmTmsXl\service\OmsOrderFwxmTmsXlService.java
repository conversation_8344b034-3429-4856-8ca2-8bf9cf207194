package com.eci.project.omsOrderFwxmTmsXl.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.omsOrderFwxmTmsXl.dao.OmsOrderFwxmTmsXlDao;
import com.eci.project.omsOrderFwxmTmsXl.entity.OmsOrderFwxmTmsXlEntity;
import com.eci.project.omsOrderFwxmTmsXl.validate.OmsOrderFwxmTmsXlVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 委托内容-委托线路Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-14
*/
@Service
@Slf4j
public class OmsOrderFwxmTmsXlService implements EciBaseService<OmsOrderFwxmTmsXlEntity> {

    @Autowired
    private OmsOrderFwxmTmsXlDao omsOrderFwxmTmsXlDao;

    @Autowired
    private OmsOrderFwxmTmsXlVal omsOrderFwxmTmsXlVal;


    @Override
    public TgPageInfo queryPageList(OmsOrderFwxmTmsXlEntity entity) {
        EciQuery<OmsOrderFwxmTmsXlEntity> eciQuery = EciQuery.buildQuery(entity);
        List<OmsOrderFwxmTmsXlEntity> entities = omsOrderFwxmTmsXlDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderFwxmTmsXlEntity save(OmsOrderFwxmTmsXlEntity entity) {
        // 返回实体对象
        OmsOrderFwxmTmsXlEntity omsOrderFwxmTmsXlEntity = null;
        omsOrderFwxmTmsXlVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            omsOrderFwxmTmsXlEntity = omsOrderFwxmTmsXlDao.insertOne(entity);

        }else{

            omsOrderFwxmTmsXlEntity = omsOrderFwxmTmsXlDao.updateByEntityId(entity);

        }
        return omsOrderFwxmTmsXlEntity;
    }

    @Override
    public List<OmsOrderFwxmTmsXlEntity> selectList(OmsOrderFwxmTmsXlEntity entity) {
        return omsOrderFwxmTmsXlDao.selectList(entity);
    }

    @Override
    public OmsOrderFwxmTmsXlEntity selectOneById(Serializable id) {
        return omsOrderFwxmTmsXlDao.selectById(id);
    }


    @Override
    public void insertBatch(List<OmsOrderFwxmTmsXlEntity> list) {
        omsOrderFwxmTmsXlDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return omsOrderFwxmTmsXlDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return omsOrderFwxmTmsXlDao.deleteById(id);
    }

}