<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.omsOrderFwxmWorkFk.dao.OmsOrderFwxmWorkFkDao">
    <resultMap type="OmsOrderFwxmWorkFkEntity" id="OmsOrderFwxmWorkFkResult">
        <result property="guid" column="GUID"/>
        <result property="workNo" column="WORK_NO"/>
        <result property="orderNo" column="ORDER_NO"/>
        <result property="fwlxCode" column="FWLX_CODE"/>
        <result property="fwxmCode" column="FWXM_CODE"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="opMemo" column="OP_MEMO"/>
        <result property="hgcyNum" column="HGCY_NUM"/>
        <result property="hgcyMemo" column="HGCY_MEMO"/>
        <result property="sjcyNum" column="SJCY_NUM"/>
        <result property="sjcyMemo" column="SJCY_MEMO"/>
        <result property="jjNoRk" column="JJ_NO_RK"/>
        <result property="jjNoCk" column="JJ_NO_CK"/>
        <result property="tlBcNo" column="TL_BC_NO"/>
        <result property="tlDate" column="TL_DATE"/>
        <result property="tlQfNo" column="TL_QF_NO"/>
        <result property="tlGfNo" column="TL_GF_NO"/>
        <result property="tlDcNo" column="TL_DC_NO"/>
        <result property="tlYdNo" column="TL_YD_NO"/>
        <result property="tlYjDate" column="TL_YJ_DATE"/>
        <result property="hyCm" column="HY_CM"/>
        <result property="hyHc" column="HY_HC"/>
        <result property="hyQfNo" column="HY_QF_NO"/>
        <result property="hyDcNo" column="HY_DC_NO"/>
        <result property="hyTdNo" column="HY_TD_NO"/>
        <result property="hyDate" column="HY_DATE"/>
        <result property="hyYjDate" column="HY_YJ_DATE"/>
        <result property="kyMbNo" column="KY_MB_NO"/>
        <result property="kyHbNo" column="KY_HB_NO"/>
        <result property="kyHkgs" column="KY_HKGS"/>
        <result property="kyHbh" column="KY_HBH"/>
        <result property="kyJcbh" column="KY_JCBH"/>
        <result property="kyDate" column="KY_DATE"/>
        <result property="kyYjDate" column="KY_YJ_DATE"/>
        <result property="xzwtNo" column="XZWT_NO"/>
        <result property="sysCode" column="SYS_CODE"/>
        <result property="bizRegId" column="BIZ_REG_ID"/>
        <result property="docType" column="DOC_TYPE"/>
        <result property="tdjfzl" column="TDJFZL"/>
        <result property="cgs" column="CGS"/>
        <result property="poorQualityDesc" column="POOR_QUALITY_DESC"/>
        <result property="goodsProtety" column="GOODS_PROTETY"/>
        <result property="goodsName" column="GOODS_NAME"/>
        <result property="weightTotal" column="WEIGHT_TOTAL"/>
        <result property="pieceTotal" column="PIECE_TOTAL"/>
        <result property="weightCalc" column="WEIGHT_CALC"/>
        <result property="volumeTotal" column="VOLUME_TOTAL"/>
        <result property="khOrderNo" column="KH_ORDER_NO"/>
        <result property="pkyType" column="PKY_TYPE"/>
        <result property="cjfs" column="CJFS"/>
        <result property="qyg" column="QYG"/>
        <result property="mdg" column="MDG"/>
        <result property="tdyq" column="TDYQ"/>
        <result property="gq" column="GQ"/>
        <result property="hx" column="HX"/>
        <result property="zx" column="ZX"/>
        <result property="px" column="PX"/>
        <result property="zdfhr" column="ZDFHR"/>
        <result property="zdshr" column="ZDSHR"/>
        <result property="zdtzr" column="ZDTZR"/>
        <result property="fhr" column="FHR"/>
        <result property="shrConsignee" column="SHR_CONSIGNEE"/>
        <result property="tzr" column="TZR"/>
        <result property="mt" column="MT"/>
        <result property="jhd" column="JHD"/>
        <result property="yftkZd" column="YFTK_ZD"/>
        <result property="zftkZd" column="ZFTK_ZD"/>
        <result property="yftkFd" column="YFTK_FD"/>
        <result property="zftkFd" column="ZFTK_FD"/>
        <result property="tdzzr" column="TDZZR"/>
        <result property="wh" column="WH"/>
        <result property="dclx" column="DCLX"/>
        <result property="dcdl" column="DCDL"/>
        <result property="cdsb" column="CDSB"/>
        <result property="zzgCode" column="ZZG_CODE"/>
        <result property="weightTotalSj" column="WEIGHT_TOTAL_SJ"/>
        <result property="pieceTotalSj" column="PIECE_TOTAL_SJ"/>
        <result property="weightCalcSj" column="WEIGHT_CALC_SJ"/>
        <result property="volumeTotalSj" column="VOLUME_TOTAL_SJ"/>
        <result property="weightTotalZd" column="WEIGHT_TOTAL_ZD"/>
        <result property="pieceTotalZd" column="PIECE_TOTAL_ZD"/>
        <result property="weightCalcZd" column="WEIGHT_CALC_ZD"/>
        <result property="volumeTotalZd" column="VOLUME_TOTAL_ZD"/>
        <result property="gwdl" column="GWDL"/>
        <result property="zdmdd" column="ZDMDD"/>
        <result property="sjhb" column="SJHB"/>
        <result property="whAdd" column="WH_ADD"/>
        <result property="gdQty" column="GD_QTY"/>
        <result property="ysgj" column="YSGJ"/>
        <result property="crk" column="CRK"/>
        <result property="rkDate" column="RK_DATE"/>
        <result property="ckDate" column="CK_DATE"/>
        <result property="ie" column="IE"/>
        <result property="goodsSize" column="GOODS_SIZE"/>
        <result property="lyDcNo" column="LY_DC_NO"/>
        <result property="ccrn" column="CCRN"/>
        <result property="lyLldh" column="LY_LLDH"/>
        <result property="boxNo1" column="BOX_NO1"/>
        <result property="boxFh1" column="BOX_FH1"/>
        <result property="boxPz1" column="BOX_PZ1"/>
        <result property="boxType1" column="BOX_TYPE1"/>
        <result property="boxSize1" column="BOX_SIZE1"/>
        <result property="boxNo2" column="BOX_NO2"/>
        <result property="boxFh2" column="BOX_FH2"/>
        <result property="boxPz2" column="BOX_PZ2"/>
        <result property="boxType2" column="BOX_TYPE2"/>
        <result property="boxSize2" column="BOX_SIZE2"/>
        <result property="tlKa" column="TL_KA"/>
        <result property="kilometers" column="KILOMETERS"/>
    </resultMap>

    <sql id="selectOmsOrderFwxmWorkFkEntityVo">
        select
            GUID,
            WORK_NO,
            ORDER_NO,
            FWLX_CODE,
            FWXM_CODE,
            CREATE_USER,
            CREATE_USER_NAME,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_USER_NAME,
            UPDATE_DATE,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            OP_MEMO,
            HGCY_NUM,
            HGCY_MEMO,
            SJCY_NUM,
            SJCY_MEMO,
            JJ_NO_RK,
            JJ_NO_CK,
            TL_BC_NO,
            TL_DATE,
            TL_QF_NO,
            TL_GF_NO,
            TL_DC_NO,
            TL_YD_NO,
            TL_YJ_DATE,
            HY_CM,
            HY_HC,
            HY_QF_NO,
            HY_DC_NO,
            HY_TD_NO,
            HY_DATE,
            HY_YJ_DATE,
            KY_MB_NO,
            KY_HB_NO,
            KY_HKGS,
            KY_HBH,
            KY_JCBH,
            KY_DATE,
            KY_YJ_DATE,
            XZWT_NO,
            SYS_CODE,
            BIZ_REG_ID,
            DOC_TYPE,
            TDJFZL,
            CGS,
            POOR_QUALITY_DESC,
            GOODS_PROTETY,
            GOODS_NAME,
            WEIGHT_TOTAL,
            PIECE_TOTAL,
            WEIGHT_CALC,
            VOLUME_TOTAL,
            KH_ORDER_NO,
            PKY_TYPE,
            CJFS,
            QYG,
            MDG,
            TDYQ,
            GQ,
            HX,
            ZX,
            PX,
            ZDFHR,
            ZDSHR,
            ZDTZR,
            FHR,
            SHR_CONSIGNEE,
            TZR,
            MT,
            JHD,
            YFTK_ZD,
            ZFTK_ZD,
            YFTK_FD,
            ZFTK_FD,
            TDZZR,
            WH,
            DCLX,
            DCDL,
            CDSB,
            ZZG_CODE,
            WEIGHT_TOTAL_SJ,
            PIECE_TOTAL_SJ,
            WEIGHT_CALC_SJ,
            VOLUME_TOTAL_SJ,
            WEIGHT_TOTAL_ZD,
            PIECE_TOTAL_ZD,
            WEIGHT_CALC_ZD,
            VOLUME_TOTAL_ZD,
            GWDL,
            ZDMDD,
            SJHB,
            WH_ADD,
            GD_QTY,
            YSGJ,
            CRK,
            RK_DATE,
            CK_DATE,
            IE,
            GOODS_SIZE,
            LY_DC_NO,
            CCRN,
            LY_LLDH,
            BOX_NO1,
            BOX_FH1,
            BOX_PZ1,
            BOX_TYPE1,
            BOX_SIZE1,
            BOX_NO2,
            BOX_FH2,
            BOX_PZ2,
            BOX_TYPE2,
            BOX_SIZE2,
            TL_KA,
            KILOMETERS
        from OMS_ORDER_FWXM_WORK_FK
    </sql>
</mapper>