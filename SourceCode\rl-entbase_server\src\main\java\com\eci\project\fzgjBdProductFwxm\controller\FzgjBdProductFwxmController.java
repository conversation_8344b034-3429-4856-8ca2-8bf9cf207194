package com.eci.project.fzgjBdProductFwxm.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjBdProductFwxm.service.FzgjBdProductFwxmService;
import com.eci.project.fzgjBdProductFwxm.entity.FzgjBdProductFwxmEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 产品服务项目Controller
*
* @<NAME_EMAIL>
* @date 2025-04-01
*/
@Api(tags = "产品服务项目")
@RestController
@RequestMapping("/fzgjBdProductFwxm")
public class FzgjBdProductFwxmController extends EciBaseController {

    @Autowired
    private FzgjBdProductFwxmService fzgjBdProductFwxmService;


    @ApiOperation("产品服务项目:保存")
    @EciLog(title = "产品服务项目:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody FzgjBdProductFwxmEntity entity){
        FzgjBdProductFwxmEntity fzgjBdProductFwxmEntity =fzgjBdProductFwxmService.save(entity);
        return ResponseMsgUtil.success(10001,fzgjBdProductFwxmEntity);
    }


    @ApiOperation("产品服务项目:查询列表")
    @EciLog(title = "产品服务项目:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjBdProductFwxmEntity entity){
        List<FzgjBdProductFwxmEntity> fzgjBdProductFwxmEntities = fzgjBdProductFwxmService.selectList(entity);
        return ResponseMsgUtil.success(10001,fzgjBdProductFwxmEntities);
    }


    @ApiOperation("产品服务项目:分页查询列表")
    @EciLog(title = "产品服务项目:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjBdProductFwxmEntity entity){
        TgPageInfo tgPageInfo = fzgjBdProductFwxmService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("产品服务项目:根据ID查一条")
    @EciLog(title = "产品服务项目:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjBdProductFwxmEntity entity){
        FzgjBdProductFwxmEntity  fzgjBdProductFwxmEntity = fzgjBdProductFwxmService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,fzgjBdProductFwxmEntity);
    }


    @ApiOperation("产品服务项目:根据ID删除一条")
    @EciLog(title = "产品服务项目:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjBdProductFwxmEntity entity){
        int count = fzgjBdProductFwxmService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("产品服务项目:根据ID字符串删除多条")
    @EciLog(title = "产品服务项目:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjBdProductFwxmEntity entity) {
        int count = fzgjBdProductFwxmService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}