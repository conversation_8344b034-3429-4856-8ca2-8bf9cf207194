package com.eci.project.fzgjBdDistrict.service.impl;

import com.eci.common.web.BllContext;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjBdDistrict.dao.FzgjBdDistrictDao;
import com.eci.project.fzgjBdDistrict.entity.FzgjBdDistrictEntity;
import com.eci.project.fzgjBdDistrict.entity.FzgjBdDistrictRealtionEntity;
import com.eci.project.fzgjBdDistrict.service.IFzgjBdDistrictService;
import com.eci.project.fzgjBdDistrict.validate.FzgjBdDistrictVal;

import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 区县Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-03-17
*/
@Service
@Slf4j
public class FzgjBdDistrictServiceImpl implements IFzgjBdDistrictService
{
    @Autowired
    private FzgjBdDistrictDao fzgjBdDistrictDao;

    @Autowired
    private FzgjBdDistrictVal fzgjBdDistrictVal;


    @Override
    public TgPageInfo queryPageList(FzgjBdDistrictEntity entity) {
        EciQuery<FzgjBdDistrictEntity> eciQuery = EciQuery.doQuery(entity);
        List<FzgjBdDistrictEntity> entities = fzgjBdDistrictDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjBdDistrictEntity save(FzgjBdDistrictEntity entity) {
        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setCreateDate(new java.util.Date());
            entity.setUpdateDate(new java.util.Date());
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        } else {
            entity.setUpdateDate(new java.util.Date());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        }
        // 返回实体对象
        FzgjBdDistrictEntity fzgjBdDistrictEntity = null;
        fzgjBdDistrictVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjBdDistrictEntity = fzgjBdDistrictDao.insertOne(entity);

        }else{

            fzgjBdDistrictEntity = fzgjBdDistrictDao.updateByEntityId(entity);

        }
        return fzgjBdDistrictEntity;
    }

    @Override
    public List<FzgjBdDistrictEntity> selectList(FzgjBdDistrictEntity entity) {
        return fzgjBdDistrictDao.selectList(entity);
    }

    @Override
    public FzgjBdDistrictEntity selectOneById(Serializable id) {
        return fzgjBdDistrictDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjBdDistrictEntity> list) {
        fzgjBdDistrictDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjBdDistrictDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjBdDistrictDao.deleteById(id);
    }


    /**
     * 区县所属关系分页列表
     * **/
   @Override
   public TgPageInfo selectDistrictRealtionPageList(FzgjBdDistrictRealtionEntity entity){
       startPage();
       List<FzgjBdDistrictRealtionEntity> entities = fzgjBdDistrictDao.selectDistrictRealtionPageList(entity);
       return EciQuery.getPageInfo(entities);
   }

}