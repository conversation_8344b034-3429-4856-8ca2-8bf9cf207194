package com.eci.project.fzgjBdServiceItemPt.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eci.common.db.DBHelper;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjBdServiceItemPt.dao.FzgjBdServiceItemPtDao;
import com.eci.project.fzgjBdServiceItemPt.entity.FzgjBdServiceItemPtEntity;
import com.eci.project.fzgjBdServiceItemPt.entity.TreeModel;
import com.eci.project.fzgjBdServiceItemPt.validate.FzgjBdServiceItemPtVal;

import com.eci.sso.role.entity.UserContext;
import com.eci.wu.core.DataTable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 平台级服务项目Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-03-25
*/
@Service
@Slf4j
public class FzgjBdServiceItemPtService implements EciBaseService<FzgjBdServiceItemPtEntity> {

    @Autowired
    private FzgjBdServiceItemPtDao fzgjBdServiceItemPtDao;

    @Autowired
    private FzgjBdServiceItemPtVal fzgjBdServiceItemPtVal;


    @Override
    public TgPageInfo queryPageList(FzgjBdServiceItemPtEntity entity) {
        EciQuery<FzgjBdServiceItemPtEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjBdServiceItemPtEntity> entities = fzgjBdServiceItemPtDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }

    public List<TreeModel> selectTree(FzgjBdServiceItemPtEntity entity){
        QueryWrapper query=new QueryWrapper();
        query.apply("parentid={0}",entity.getParentid());
        query.apply("status={0}","Y");
        query.orderByAsc("seq");
        List<TreeModel> list=fzgjBdServiceItemPtDao.selectTree(query);
        return list;
    }


    @Override

    public FzgjBdServiceItemPtEntity save(FzgjBdServiceItemPtEntity entity) {
        // 返回实体对象
        FzgjBdServiceItemPtEntity fzgjBdServiceItemPtEntity = null;
        fzgjBdServiceItemPtVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            fzgjBdServiceItemPtEntity = fzgjBdServiceItemPtDao.insertOne(entity);

        }else{

            fzgjBdServiceItemPtEntity = fzgjBdServiceItemPtDao.updateByEntityId(entity);

        }
        return fzgjBdServiceItemPtEntity;
    }


    @Override
    public List<FzgjBdServiceItemPtEntity> selectList(FzgjBdServiceItemPtEntity entity) {
        return fzgjBdServiceItemPtDao.selectList(entity);
    }

    @Override
    public FzgjBdServiceItemPtEntity selectOneById(Serializable id) {
        return fzgjBdServiceItemPtDao.selectById(id);
    }
    public FzgjBdServiceItemPtEntity selectwithparent(FzgjBdServiceItemPtEntity entity) {
        QueryWrapper query=new QueryWrapper();
        query.apply("guid={0}",entity.getGuid());
        return fzgjBdServiceItemPtDao.selectwithparent(query);
    }

    public DataTable getCheckEditItem(String guid){
        String sql="SELECT A.PAGE_URL AS PAGE_URL,A.GUID as \"GUID\",B.GUID AS \"PTGUID\",A.CODE AS \"CODE\",A.NAME as \"NAME\",case NVL(B.GUID,2) when '2' then 0 else 1 end as \"CHECKED\"," +
                "case NVL(B.GUID,2) when '2' then 0 else 1 end as \"ORGCHECKED\"   FROM FZGJ_BD_OMS_PAGES A left join (\n" +
                "SELECT A.GUID,A.CODE FROM FZGJ_BD_SERVICE_ITEM_PAGES_PT A WHERE A.STATUS='Y' \n" +
                "AND A.SERVICE_ITEM_CODE = '%s' \n" +
                ") B on A.CODE=B.CODE";
        sql=String.format(sql,guid);
        return DBHelper.getDataTable(sql);
    }

    @Override
    public void insertBatch(List<FzgjBdServiceItemPtEntity> list) {
        fzgjBdServiceItemPtDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjBdServiceItemPtDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjBdServiceItemPtDao.deleteById(id);
    }

    public boolean existCode(String code,String guid){
        QueryWrapper queryWrapper=new QueryWrapper();
        if(guid!=null&&!guid.isEmpty())
            queryWrapper.ne("GUID",guid);
        queryWrapper.eq("CODE",code);

        return fzgjBdServiceItemPtDao.exists(queryWrapper);
    }


}