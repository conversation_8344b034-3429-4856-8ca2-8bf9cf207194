package com.eci.project.fzgjBdServiceItem.controller;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.ZsrJson;
import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjBdServiceItem.entity.TreeResult;
import com.eci.project.fzgjBdServiceItem.service.FzgjBdServiceItemService;
import com.eci.project.fzgjBdServiceItem.entity.FzgjBdServiceItemEntity;
import com.eci.project.fzgjBdServiceItemPt.entity.FzgjBdServiceItemPtEntity;
import com.eci.project.fzgjBdServiceItemPt.entity.TreeModel;
import com.eci.project.fzgjBdServiceType.entity.FzgjBdServiceTypeEntity;
import com.eci.project.fzgjBdServiceType.service.FzgjBdServiceTypeService;
import com.eci.project.fzgjBdServiceTypeCom.entity.FzgjBdServiceTypeComEntity;
import com.eci.project.fzgjBdServiceTypeCom.service.FzgjBdServiceTypeComService;
import com.eci.sso.role.entity.UserContext;
import com.eci.wu.core.DataTable;
import com.eci.wu.core.EntityBase;
import org.apache.catalina.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* 服务项目Controller
*
* @<NAME_EMAIL>
* @date 2025-04-07
*/
@Api(tags = "服务项目")
@RestController
@RequestMapping("/fzgjBdServiceItem")
public class FzgjBdServiceItemController extends EciBaseController {

    @Autowired
    private FzgjBdServiceItemService fzgjBdServiceItemService;

    @Autowired
    private FzgjBdServiceTypeService fzgjBdServiceTypeService;

    @Autowired
    private FzgjBdServiceTypeComService fzgjBdServiceTypeComService;
    @ApiOperation("服务项目:保存")
    @EciLog(title = "服务项目:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody String jsonstring){
        ZsrJson zsrJson= ZsrJson.parse(jsonstring);
        List<FzgjBdServiceItemEntity> checked=new ArrayList<>();
        if(zsrJson.exists("checked")){

            checked=zsrJson.getList("checked",FzgjBdServiceItemEntity.class);
            checked.forEach(p->{
                p.setGroupCode(UserContext.getUserInfo().getCompanyCode());
                p.setGroupName(UserContext.getUserInfo().getCompanyName());
                p.setCreateDate(new Date());
                p.setCreateUser(UserContext.getUserInfo().getTrueName());
                p.setUpdateDate(new Date());
                p.setUpdateUser(UserContext.getUserInfo().getTrueName());
            });

        }
        checked=checked.stream().filter(p->!p.getParentid().equals("0")).filter(p->p.getOwnedService()!=null).collect(Collectors.toList());
        //先根据GROUP_COCE删除数据
        fzgjBdServiceTypeComService.deleteByGroupCode();
        fzgjBdServiceItemService.deleteByGroupCode();
        //---保存typeCom
        List<FzgjBdServiceItemEntity> list= fzgjBdServiceItemService.selectServiceItem();

        List<String> services= checked.stream().filter(p->p!=null)
                .map(FzgjBdServiceItemEntity::getOwnedService).distinct()
                .collect(Collectors.toList());


        List<FzgjBdServiceTypeEntity> types= fzgjBdServiceTypeService.selectAll();
        types= types.stream().filter(p->services.contains(p.getGuid())).collect(Collectors.toList());

        if(types!=null&&types.size()>0){
            List<FzgjBdServiceTypeComEntity> typeComs=new ArrayList<>();
            types.forEach(p->{
                FzgjBdServiceTypeComEntity typeCom=new FzgjBdServiceTypeComEntity();
                typeCom.setGuid(IdWorker.getIdStr());
                typeCom.setCode(p.getCode());
                typeCom.setName(p.getName());
                typeCom.setStatus(p.getStatus());
                typeCom.setSeq(p.getSeq());
                typeCom.setUpdateUserName(UserContext.getUserInfo().getTrueName());
                typeCom.setUpdateDate(new Date());
                typeCom.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
                typeCom.setCreateUserName(UserContext.getUserInfo().getTrueName());
                typeCom.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
                typeCom.setCreateDate(new Date());
                typeCom.setGroupCode(UserContext.getUserInfo().getCompanyCode());
                typeCom.setGroupName(UserContext.getUserInfo().getCompanyName());
                typeCom.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
                typeCom.setCompanyName(UserContext.getUserInfo().getCompanyName());
                typeComs.add(typeCom);
            });
            fzgjBdServiceTypeComService.insertBatch(typeComs);
        }

        if(checked.size()>0){
            fzgjBdServiceItemService.insertBatch(checked);
            fzgjBdServiceItemService.UpdateParentId();
        }
        return ResponseMsgUtil.success(10001);
    }
    @ApiOperation("服务项目内容:保存")
    @EciLog(title = "服务项目内容:新增", businessType = BusinessType.INSERT)
    @PostMapping("/saveContent")
    @EciAction()
    public ResponseMsg saveContent(@RequestBody FzgjBdServiceItemEntity entity) {

        entity= fzgjBdServiceItemService.saveContent(entity);
        return  ResponseMsgUtil.success(10001,entity);
    }


    @ApiOperation("服务项目:查询列表")
    @EciLog(title = "服务项目:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjBdServiceItemEntity entity){
        List<FzgjBdServiceItemEntity> fzgjBdServiceItemEntities = fzgjBdServiceItemService.selectList(entity);
        return ResponseMsgUtil.success(10001,fzgjBdServiceItemEntities);
    }


    @ApiOperation("服务项目:分页查询列表")
    @EciLog(title = "服务项目:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjBdServiceItemEntity entity){
        TgPageInfo tgPageInfo = fzgjBdServiceItemService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("服务项目:根据ID查一条")
    @EciLog(title = "服务项目:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjBdServiceItemEntity entity){
        FzgjBdServiceItemEntity  fzgjBdServiceItemEntity = fzgjBdServiceItemService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,fzgjBdServiceItemEntity);
    }
    @ApiOperation("服务项目:根据ID查一条")
    @EciLog(title = "服务项目:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/LoadRecord")
    @EciAction()
        public ResponseMsg LoadRecord(@RequestBody FzgjBdServiceItemEntity entity){
        DataTable dt = fzgjBdServiceItemService.LoadRecord(entity.getGuid());
        if(dt.rows.size()==0) {
            dt.rows.add(new EntityBase());
        }
        dt.rows.get(0).setString("group",UserContext.getUserInfo().getCompanyCode()+"|"+UserContext.getUserInfo().getCompanyName());
        dt.rows.get(0).setString("GROUP_CODE",UserContext.getUserInfo().getCompanyCode());
        dt.rows.get(0).setString("GROUP_NAME",UserContext.getUserInfo().getCompanyName());
        return ResponseMsgUtil.success(10001,dt);
    }

    @ApiOperation("服务项目:查询列表")
    @EciLog(title = "服务项目:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectTree")
    @EciAction()
    public ResponseMsg selectTree(){
        String companyCode=UserContext.getUserInfo().getCompanyCode();
        FzgjBdServiceTypeEntity typeEntity=new FzgjBdServiceTypeEntity();
        typeEntity.setParentid("0");
        typeEntity.setStatus("Y");
        List<TreeModel> typelList= fzgjBdServiceTypeService.selectTree(typeEntity);
        List<TreeModel> serviceList = fzgjBdServiceItemService.selectTree(companyCode);
        fzgjBdServiceItemService.Build(typelList,serviceList);
        TreeResult result=new TreeResult();
        result.list=typelList;
        result.defaultCheck=fzgjBdServiceItemService.getDefaultCheck(typelList,serviceList);
        return ResponseMsgUtil.success(10001,result);

    }

    @ApiOperation("服务项目:获取已选择的节点")
    @EciLog(title = "服务项目:获取已选择的节点", businessType = BusinessType.SELECT)
    @GetMapping("/getCheckedNodes")
    @EciAction()
    public ResponseMsg getCheckedNodes(){
        List<String> list=fzgjBdServiceItemService.selectNodes();
        return ResponseMsgUtil.success(10001,list);
    }

    @ApiOperation("服务项目:根据ID删除一条")
    @EciLog(title = "服务项目:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjBdServiceItemEntity entity){
        int count = fzgjBdServiceItemService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("服务项目:根据ID字符串删除多条")
    @EciLog(title = "服务项目:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjBdServiceItemEntity entity) {
        int count = fzgjBdServiceItemService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}