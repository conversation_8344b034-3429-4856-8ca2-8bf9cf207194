package com.eci.project.omsOrderFwxmWorkFk.controller;

import com.eci.common.util.ResponseMsg;
import com.eci.common.util.ResponseMsgUtil;
import com.eci.crud.controller.EciBaseController;
import com.eci.log.annotation.EciAction;
import com.eci.log.annotation.EciLog;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.omsOrderFwxmWorkFk.entity.OmsOrderFwxmWorkFkEntity;
import com.eci.project.omsOrderFwxmWorkFk.entity.ReqOmsOrderFwxmWorkFkEntity;
import com.eci.project.omsOrderFwxmWorkFk.service.OmsOrderFwxmWorkFkService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 反馈内容表头Controller
 *
 * @<NAME_EMAIL>
 * @date 2025-05-21
 */
@Api(tags = "反馈内容表头")
@RestController
@RequestMapping("/omsOrderFwxmWorkFk")
public class OmsOrderFwxmWorkFkController extends EciBaseController {

    @Autowired
    private OmsOrderFwxmWorkFkService omsOrderFwxmWorkFkService;


    @ApiOperation("反馈内容表头:保存")
    @EciLog(title = "反馈内容表头:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody OmsOrderFwxmWorkFkEntity entity) {
        OmsOrderFwxmWorkFkEntity omsOrderFwxmWorkFkEntity = omsOrderFwxmWorkFkService.save(entity);
        return ResponseMsgUtil.success(10001, omsOrderFwxmWorkFkEntity);
    }


    @ApiOperation("反馈内容表头:查询列表")
    @EciLog(title = "反馈内容表头:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody OmsOrderFwxmWorkFkEntity entity) {
        List<OmsOrderFwxmWorkFkEntity> omsOrderFwxmWorkFkEntities = omsOrderFwxmWorkFkService.selectList(entity);
        return ResponseMsgUtil.success(10001, omsOrderFwxmWorkFkEntities);
    }


    @ApiOperation("反馈内容表头:分页查询列表")
    @EciLog(title = "反馈内容表头:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody OmsOrderFwxmWorkFkEntity entity) {
        TgPageInfo tgPageInfo = omsOrderFwxmWorkFkService.queryPageList(entity);
        return ResponseMsgUtil.success(10001, tgPageInfo);
    }


    @ApiOperation("反馈内容表头:根据ID查一条")
    @EciLog(title = "反馈内容表头:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody OmsOrderFwxmWorkFkEntity entity) {
        OmsOrderFwxmWorkFkEntity omsOrderFwxmWorkFkEntity = omsOrderFwxmWorkFkService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001, omsOrderFwxmWorkFkEntity);
    }


    @ApiOperation("反馈内容表头:根据ID删除一条")
    @EciLog(title = "反馈内容表头:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody OmsOrderFwxmWorkFkEntity entity) {
        int count = omsOrderFwxmWorkFkService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001, count);
    }


    @ApiOperation("反馈内容表头:根据ID字符串删除多条")
    @EciLog(title = "反馈内容表头:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody OmsOrderFwxmWorkFkEntity entity) {
        int count = omsOrderFwxmWorkFkService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001, count);
    }

    @ApiOperation("作业完成&作业数据齐全:保存")
    @EciLog(title = "作业完成&作业数据齐全:新增", businessType = BusinessType.INSERT)
    @PostMapping("/savefk")
    @EciAction()
    public ResponseMsg savefk(@RequestBody ReqOmsOrderFwxmWorkFkEntity entity) {
        OmsOrderFwxmWorkFkEntity omsOrderFwxmWorkFkEntity = omsOrderFwxmWorkFkService.savefk(entity);
        return ResponseMsgUtil.success(10001, omsOrderFwxmWorkFkEntity);
    }

}