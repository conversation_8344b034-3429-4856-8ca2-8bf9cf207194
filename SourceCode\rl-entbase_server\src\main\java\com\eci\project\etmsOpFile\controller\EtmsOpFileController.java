package com.eci.project.etmsOpFile.controller;

import com.eci.common.BaseProperties;
import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.etmsOpFile.service.EtmsOpFileService;
import com.eci.project.etmsOpFile.entity.EtmsOpFileEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.UUID;

/**
* 业务附件Controller
*
* @<NAME_EMAIL>
* @date 2025-04-22
*/
@Api(tags = "业务附件")
@RestController
@RequestMapping("/etmsOpFile")
public class EtmsOpFileController extends EciBaseController {

    @Autowired
    private EtmsOpFileService etmsOpFileService;


    @ApiOperation("业务附件:保存")
    @EciLog(title = "业务附件:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody EtmsOpFileEntity entity){
        EtmsOpFileEntity etmsOpFileEntity =etmsOpFileService.save(entity);
        return ResponseMsgUtil.success(10001,etmsOpFileEntity);
    }


    @ApiOperation("业务附件:查询列表")
    @EciLog(title = "业务附件:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody EtmsOpFileEntity entity){
        List<EtmsOpFileEntity> etmsOpFileEntities = etmsOpFileService.selectList(entity);
        return ResponseMsgUtil.success(10001,etmsOpFileEntities);
    }


    @ApiOperation("业务附件:分页查询列表")
    @EciLog(title = "业务附件:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody EtmsOpFileEntity entity){
        TgPageInfo tgPageInfo = etmsOpFileService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("业务附件:根据ID查一条")
    @EciLog(title = "业务附件:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody EtmsOpFileEntity entity){
        EtmsOpFileEntity  etmsOpFileEntity = etmsOpFileService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,etmsOpFileEntity);
    }


    @ApiOperation("业务附件:根据ID删除一条")
    @EciLog(title = "业务附件:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody EtmsOpFileEntity entity){
        int count = etmsOpFileService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("业务附件:根据ID字符串删除多条")
    @EciLog(title = "业务附件:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody EtmsOpFileEntity entity) {
        int count = etmsOpFileService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }

    @ApiOperation("业务附件:上传附件")
    @EciLog(title = "业务附件:上传附件", businessType = BusinessType.SELECT)
    @PostMapping("/upload")
    @EciAction()
    public ResponseMsg upload(@RequestParam MultipartFile file, @RequestParam String type){
        String originalFilename = file.getOriginalFilename();
        String suffix = originalFilename.substring(originalFilename.lastIndexOf("."));
        String fileName = UUID.randomUUID()+suffix;

        Path currentDir = Paths.get(System.getProperty("user.dir"));
        String filepath="/OpFiles/"+type+"/"+DateUtils.getDate()+"/";
        String basePath = BaseProperties.getFilepath()+filepath;
        File dir = new File(basePath);
        if(!dir.exists()){
            dir.mkdirs();
        }
        Path outputPath = currentDir.resolve(basePath);
        try {
            file.transferTo(new File(outputPath+"\\"+fileName));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return ResponseMsgUtil.success(10001,filepath+fileName);
    }
}