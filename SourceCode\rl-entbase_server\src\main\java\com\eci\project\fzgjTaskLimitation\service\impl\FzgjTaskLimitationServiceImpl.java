package com.eci.project.fzgjTaskLimitation.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.util.DateUtils;
import com.eci.common.web.BllContext;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.exception.BaseException;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjTaskLimitation.dao.FzgjTaskLimitationDao;
import com.eci.project.fzgjTaskLimitation.entity.FzgjTaskLimitationEntity;
import com.eci.project.fzgjTaskLimitation.service.IFzgjTaskLimitationService;
import com.eci.project.fzgjTaskLimitation.validate.FzgjTaskLimitationVal;

import com.eci.project.fzgjTaskLimitationPt.dao.FzgjTaskLimitationPtDao;
import com.eci.project.fzgjTaskLimitationPt.entity.FzgjTaskLimitationPtEntity;
import com.eci.sso.role.entity.UserContext;
import com.eci.wu.core.EntityBase;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.jackson.map.annotate.JsonSerialize;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;


/**
* 作业环节及标准时效Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-03-28
*/
@Service
@Slf4j
public class FzgjTaskLimitationServiceImpl implements IFzgjTaskLimitationService
{
    @Autowired
    private FzgjTaskLimitationDao fzgjTaskLimitationDao;
    @Autowired
    private FzgjTaskLimitationPtDao fzgjTaskLimitationPtDao;

    @Autowired
    private FzgjTaskLimitationVal fzgjTaskLimitationVal;


    @Override
    public TgPageInfo queryPageList(FzgjTaskLimitationEntity entity) {
        EciQuery<FzgjTaskLimitationEntity> eciQuery = EciQuery.doQuery(entity);
        List<FzgjTaskLimitationEntity> entities = fzgjTaskLimitationDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjTaskLimitationEntity save(FzgjTaskLimitationEntity entity) {
        // 返回实体对象
        FzgjTaskLimitationEntity fzgjTaskLimitationEntity = null;
        fzgjTaskLimitationVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjTaskLimitationEntity = fzgjTaskLimitationDao.insertOne(entity);

        }else{

            fzgjTaskLimitationEntity = fzgjTaskLimitationDao.updateByEntityId(entity);

        }
        return fzgjTaskLimitationEntity;
    }

    @Override
    public List<FzgjTaskLimitationEntity> selectList(FzgjTaskLimitationEntity entity) {
        return fzgjTaskLimitationDao.selectList(entity);
    }

    @Override
    public FzgjTaskLimitationEntity selectOneById(Serializable id) {
        return fzgjTaskLimitationDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjTaskLimitationEntity> list) {
        fzgjTaskLimitationDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjTaskLimitationDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjTaskLimitationDao.deleteById(id);
    }

    /**
     * 作业环节一键初始化
     * @param date
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean taskLimitationInit() {
        QueryWrapper<FzgjTaskLimitationEntity> wrapper=new QueryWrapper<>();
        wrapper.eq("GROUP_CODE", UserContext.getUserInfo().getCompanyCode());
        fzgjTaskLimitationDao.delete(wrapper);

        QueryWrapper<FzgjTaskLimitationPtEntity> queryWrapper_pt = new QueryWrapper<FzgjTaskLimitationPtEntity>();
        queryWrapper_pt.eq("status", "Y");
        List<FzgjTaskLimitationPtEntity> taskPt=fzgjTaskLimitationPtDao.selectList(queryWrapper_pt);
        if(taskPt.size()>0){
            int i = 1;
            List<FzgjTaskLimitationEntity> listtask=new java.util.ArrayList<>();
            for(FzgjTaskLimitationPtEntity taskPtEntity:taskPt){
                FzgjTaskLimitationEntity entity=new FzgjTaskLimitationEntity();
                BeanUtils.copyProperties(taskPtEntity,entity);
                entity.setGuid(IdWorker.get32UUID());
                entity.setSeq(i);
                entity.setCreateDate(DateUtils.getNowDate());
                entity.setCreateUser(UserContext.getUserInfo().getUserId());
                entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
                entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
                entity.setGroupName(UserContext.getUserInfo().getCompanyName());
                entity.setUpdateDate(DateUtils.getNowDate());
                entity.setUpdateUser(UserContext.getUserInfo().getUserId());
                entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
                entity.setNodeCode(UserContext.getUserInfo().getDeptCode());
                entity.setNodeName(UserContext.getUserInfo().getDeptName());
                entity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
                entity.setCompanyName(UserContext.getUserInfo().getCompanyName());
                listtask.add(entity);
                i++;
            }
            int count=fzgjTaskLimitationDao.insertList(listtask);
            return count==listtask.size();
        }
        return true;
    }

    /**
     * 作业环节勾选保存
     * @param listEntity
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean taskLimitationSaveSelect(List<String> listEntity){
        List<FzgjTaskLimitationEntity> listtask=new java.util.ArrayList<>();
        QueryWrapper<FzgjTaskLimitationPtEntity> wrapper_pt=new QueryWrapper<>();
        wrapper_pt.in("GUID", listEntity);
        List<FzgjTaskLimitationPtEntity> listpt=fzgjTaskLimitationPtDao.selectList(wrapper_pt);
        int count =0;
        if(listpt.size()>0) {
            QueryWrapper<FzgjTaskLimitationEntity> wrapper = new QueryWrapper<>();
            wrapper.eq("GROUP_CODE", UserContext.getUserInfo().getCompanyCode());
            wrapper.eq("TARGET_CODE", listpt.get(0).getTargetCode());
            fzgjTaskLimitationDao.delete(wrapper);
            int i = 1;
            for (FzgjTaskLimitationPtEntity taskPtEntity : listpt) {
                FzgjTaskLimitationEntity task = new FzgjTaskLimitationEntity();
                BeanUtils.copyProperties(taskPtEntity,task);
                task.setGuid(IdWorker.get32UUID());
                task.setSeq(i);
                task.setCreateDate(DateUtils.getNowDate());
                task.setCreateUser(UserContext.getUserInfo().getUserId());
                task.setCreateUserName(UserContext.getUserInfo().getTrueName());
                task.setGroupCode(UserContext.getUserInfo().getCompanyCode());
                task.setGroupName(UserContext.getUserInfo().getCompanyName());
                task.setUpdateDate(DateUtils.getNowDate());
                task.setUpdateUser(UserContext.getUserInfo().getUserId());
                task.setUpdateUserName(UserContext.getUserInfo().getTrueName());
                task.setNodeCode(UserContext.getUserInfo().getDeptCode());
                task.setNodeName(UserContext.getUserInfo().getDeptName());
                task.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
                task.setCompanyName(UserContext.getUserInfo().getCompanyName());
                listtask.add(task);
                i++;
            }

            count = fzgjTaskLimitationDao.insertList(listtask);
        }
        return count==listtask.size();
    }
    /**
     * 上移/下移
     * @param entity
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean taskLimitationSeqUpdate(FzgjTaskLimitationEntity entity){
        FzgjTaskLimitationEntity entity_now=fzgjTaskLimitationDao.selectById(entity.getGuid());
        if(entity_now!=null){
            try {
                if (entity_now.getSeq() == 1) {
                    throw new BaseException("无法上移");
                }
                int seq = entity_now.getSeq();
                if ("up".equals(entity.getMemo())) {
                    seq -= 1;
                } else {
                    //下移
                    seq += 1;
                }
                QueryWrapper<FzgjTaskLimitationEntity> wrapper = new QueryWrapper<>();
                wrapper.eq("GROUP_CODE", UserContext.getUserInfo().getCompanyCode());
                wrapper.eq("TARGET_CODE", entity_now.getTargetCode());
                wrapper.eq("SEQ", seq);
                //上一个节点
                FzgjTaskLimitationEntity entity_updown = fzgjTaskLimitationDao.selectOne(wrapper);
                if (entity_updown != null) {
                    entity_updown.setSeq(entity_now.getSeq());
                    fzgjTaskLimitationDao.updateById(entity_updown);
                }else{
                    throw new BaseException("无法"+("up".equals(entity.getMemo())?"上移":"下移"));
                }
                entity_now.setSeq(seq);
                fzgjTaskLimitationDao.updateById(entity_now);
            }catch (BaseException e){
                throw new BaseException(e.getMessage());
            }
        }else{
            throw new BaseException("找不到节点");
        }
        return true;
    }

    @Override
    public List<Map<String, Object>> getCheckedTask(FzgjTaskLimitationEntity entity) {
        QueryWrapper<FzgjTaskLimitationEntity> queryWrapper = new QueryWrapper<FzgjTaskLimitationEntity>();
        queryWrapper.eq("GROUP_CODE", UserContext.getUserInfo().getCompanyCode());
        queryWrapper.eq("TARGET_CODE", entity.getTargetCode());
        queryWrapper.eq("STATUS",entity.getStatus());
        List<FzgjTaskLimitationEntity> taskList=fzgjTaskLimitationDao.selectList(queryWrapper).stream().sorted(Comparator.comparing(FzgjTaskLimitationEntity::getSeq)).collect(Collectors.toList());
        List<Map<String, Object>> list=new ArrayList<>();
        Map<String,Object> map=null;
        if(taskList.size()>0) {
            for (FzgjTaskLimitationEntity task : taskList) {
                map = new HashMap<>();
                map.put("id", task.getGuid());
                map.put("label", task.getName());
                map.put("children", null);
                map.put("taskid",task.getGuid());
                map.put("seq",task.getSeq());
                list.add(map);
            }
        }
        return list;
    }
}