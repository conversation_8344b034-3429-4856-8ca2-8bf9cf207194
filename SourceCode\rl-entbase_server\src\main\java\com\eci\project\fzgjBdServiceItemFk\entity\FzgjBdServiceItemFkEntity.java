package com.eci.project.fzgjBdServiceItemFk.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 企业服务项目对应反馈页面对象 FZGJ_BD_SERVICE_ITEM_FK
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-04-14
*/
@ApiModel("企业服务项目对应反馈页面")
@TableName("FZGJ_BD_SERVICE_ITEM_FK")
@FieldNameConstants
public class FzgjBdServiceItemFkEntity extends EciBaseEntity{
    /**
    * GUID
    */
    @ApiModelProperty("GUID(72)")
    @TableId("GUID")
    private String guid;
    @ApiModelProperty("服务项目GUID(40)")
    @TableField("SERVICEITEM_ID")
    private String serviceitemid;
    /**
    * 服务项目代码
    */
    @ApiModelProperty("服务项目代码(40)")
    @TableField("CODE")
    private String code;

    /**
    * 服务项目名称
    */
    @ApiModelProperty("服务项目名称(200)")
    @TableField("NAME")
    private String name;

    /**
    * 作业跟踪反馈url
    */
    @ApiModelProperty("作业跟踪反馈url(200)")
    @TableField("FEEDBACK_URL")
    private String feedbackUrl;

    /**
    * 是否启用
    */
    @ApiModelProperty("是否启用(1)")
    @TableField("STATUS")
    private String status;

    /**
    * 顺序
    */
    @ApiModelProperty("顺序(22)")
    @TableField("SEQ")
    private Float seq;

    /**
    * 备注
    */
    @ApiModelProperty("备注(1,000)")
    @TableField("MEMO")
    private String memo;

    /**
    * CREATE_USER
    */
    @ApiModelProperty("CREATE_USER(72)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * CREATE_USER
    */
    @ApiModelProperty("CREATE_USER(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * CREATE_DATE
    */
    @ApiModelProperty("CREATE_DATE(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("CREATE_DATE开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("CREATE_DATE结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * UPDATE_USER
    */
    @ApiModelProperty("UPDATE_USER(72)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * UPDATE_USER
    */
    @ApiModelProperty("UPDATE_USER(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * UPDATE_DATE
    */
    @ApiModelProperty("UPDATE_DATE(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("UPDATE_DATE开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("UPDATE_DATE结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(200)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public FzgjBdServiceItemFkEntity() {
        this.setSubClazz(FzgjBdServiceItemFkEntity.class);
    }

    public FzgjBdServiceItemFkEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public FzgjBdServiceItemFkEntity setServiceitemid(String serviceitemid) {
        this.serviceitemid = serviceitemid;
        this.nodifySetFiled("serviceitemid", serviceitemid);
        return this;
    }

    public String getServiceitemid() {
        this.nodifyGetFiled("serviceitemid");
        return serviceitemid;
    }

    public FzgjBdServiceItemFkEntity setCode(String code) {
        this.code = code;
        this.nodifySetFiled("code", code);
        return this;
    }

    public String getCode() {
        this.nodifyGetFiled("code");
        return code;
    }

    public FzgjBdServiceItemFkEntity setName(String name) {
        this.name = name;
        this.nodifySetFiled("name", name);
        return this;
    }

    public String getName() {
        this.nodifyGetFiled("name");
        return name;
    }

    public FzgjBdServiceItemFkEntity setFeedbackUrl(String feedbackUrl) {
        this.feedbackUrl = feedbackUrl;
        this.nodifySetFiled("feedbackUrl", feedbackUrl);
        return this;
    }

    public String getFeedbackUrl() {
        this.nodifyGetFiled("feedbackUrl");
        return feedbackUrl;
    }

    public FzgjBdServiceItemFkEntity setStatus(String status) {
        this.status = status;
        this.nodifySetFiled("status", status);
        return this;
    }

    public String getStatus() {
        this.nodifyGetFiled("status");
        return status;
    }

    public FzgjBdServiceItemFkEntity setSeq(Float seq) {
        this.seq = seq;
        this.nodifySetFiled("seq", seq);
        return this;
    }

    public Float getSeq() {
        this.nodifyGetFiled("seq");
        return seq;
    }

    public FzgjBdServiceItemFkEntity setMemo(String memo) {
        this.memo = memo;
        this.nodifySetFiled("memo", memo);
        return this;
    }

    public String getMemo() {
        this.nodifyGetFiled("memo");
        return memo;
    }

    public FzgjBdServiceItemFkEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public FzgjBdServiceItemFkEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public FzgjBdServiceItemFkEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public FzgjBdServiceItemFkEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public FzgjBdServiceItemFkEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public FzgjBdServiceItemFkEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public FzgjBdServiceItemFkEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public FzgjBdServiceItemFkEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public FzgjBdServiceItemFkEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public FzgjBdServiceItemFkEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public FzgjBdServiceItemFkEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public FzgjBdServiceItemFkEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public FzgjBdServiceItemFkEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public FzgjBdServiceItemFkEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public FzgjBdServiceItemFkEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public FzgjBdServiceItemFkEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

}
