package com.eci.project.omsOrderFwxmWork.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.omsOrderFwxmWork.entity.OmsOrderFwxmWorkEntity;

import org.springframework.stereotype.Service;


/**
* 供方协作任务Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-21
*/
@Service
public class OmsOrderFwxmWorkVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(OmsOrderFwxmWorkEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(OmsOrderFwxmWorkEntity entity, BusinessType businessType) {

    }

}
