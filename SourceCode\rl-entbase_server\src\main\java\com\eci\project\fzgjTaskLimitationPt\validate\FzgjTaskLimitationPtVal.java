package com.eci.project.fzgjTaskLimitationPt.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjTaskLimitationPt.entity.FzgjTaskLimitationPtEntity;

import org.springframework.stereotype.Service;


/**
* 平台级作业环节及参考时效Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-03-28
*/
@Service
public class FzgjTaskLimitationPtVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjTaskLimitationPtEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjTaskLimitationPtEntity entity, BusinessType businessType) {

    }

}
