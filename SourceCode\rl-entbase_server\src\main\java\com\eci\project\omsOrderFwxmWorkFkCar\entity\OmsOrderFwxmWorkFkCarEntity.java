package com.eci.project.omsOrderFwxmWorkFkCar.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.common.DictField;
import com.eci.common.ZsrBaseEntity;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.math.BigDecimal;
import java.util.Date;


/**
* 反馈内容-车辆对象 OMS_ORDER_FWXM_WORK_FK_CAR
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-05-21
*/
@ApiModel("反馈内容-车辆")
@TableName("OMS_ORDER_FWXM_WORK_FK_CAR")
@FieldNameConstants
public class OmsOrderFwxmWorkFkCarEntity extends ZsrBaseEntity{
    /**
    * 主键
    */
    @ApiModelProperty("主键(40)")
    @TableId("GUID")
    private String guid;

    /**
    * OMS订单号
    */
    @ApiModelProperty("OMS订单号(20)")
    @TableField("ORDER_NO")
    private String orderNo;

    /**
    * OMS协作委托单编号
    */
    @ApiModelProperty("OMS协作委托单编号(36)")
    @TableField("XZWT_NO")
    private String xzwtNo;

    /**
    * OMS任务协助编号
    */
    @ApiModelProperty("OMS任务协助编号(50)")
    @TableField("WORK_NO")
    private String workNo;

    /**
    * 协作服务项目
    */
    @ApiModelProperty("协作服务项目(20)")
    @TableField("FWXM_CODE")
    private String fwxmCode;

    /**
    * 车牌号
    */
    @ApiModelProperty("车牌号(20)")
    @TableField("CAR_NO")
    private String carNo;

    /**
    * 车辆类型
    */
    @ApiModelProperty("车辆类型(20)")
    @TableField("CAR_TYPE")
    @DictField(queryKey = "OMS_BD_CLLX")
    private String carType;

    /**
    * 车辆尺寸
    */
    @ApiModelProperty("车辆尺寸(20)")
    @TableField("CAR_SIZE")
    @DictField(queryKey = "OMS_BD_CLCC_ETMS")
    private String carSize;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 最后修改日期
    */
    @ApiModelProperty("最后修改日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("最后修改日期开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("最后修改日期结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(200)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 提货时间
    */
    @ApiModelProperty("提货时间(7)")
    @TableField("TAKE_DATE")
    private Date takeDate;

    @ApiModelProperty("提货时间开始")
    @TableField(exist=false)
    private Date takeDateStart;

    @ApiModelProperty("提货时间结束")
    @TableField(exist=false)
    private Date takeDateEnd;

    /**
    * 卸货时间
    */
    @ApiModelProperty("卸货时间(7)")
    @TableField("RETURN_DATE")
    private Date returnDate;

    @ApiModelProperty("卸货时间开始")
    @TableField(exist=false)
    private Date returnDateStart;

    @ApiModelProperty("卸货时间结束")
    @TableField(exist=false)
    private Date returnDateEnd;

    /**
    * 定位设备编号
    */
    @ApiModelProperty("定位设备编号(20)")
    @TableField("GPS_NO")
    private String gpsNo;

    /**
    * 来源系统代码
    */
    @ApiModelProperty("来源系统代码(20)")
    @TableField("SYS_CODE")
    private String sysCode;

    /**
    * 派车单号
    */
    @ApiModelProperty("派车单号(40)")
    @TableField("PCD_NO")
    private String pcdNo;

    /**
    * 车挂号
    */
    @ApiModelProperty("车挂号(50)")
    @TableField("TRAILER_NO")
    private String trailerNo;

    /**
    * 司机姓名
    */
    @ApiModelProperty("司机姓名(200)")
    @TableField("DRIVER_NAME")
    private String driverName;

    /**
    * 联系电话
    */
    @ApiModelProperty("联系电话(50)")
    @TableField("TEL")
    private String tel;

    /**
    * 司机身份证号码
    */
    @ApiModelProperty("司机身份证号码(50)")
    @TableField("ID_CARD")
    private String idCard;

    /**
    * 车辆重量
    */
    @ApiModelProperty("车辆重量(200)")
    @TableField("WEIGHT")
    private String weight;

    /**
    * 集装箱号
    */
    @ApiModelProperty("集装箱号(50)")
    @TableField("BOX_NO")
    private String boxNo;

    /**
    * 业务数据唯一注册编号
    */
    @ApiModelProperty("业务数据唯一注册编号(50)")
    @TableField("BIZ_REG_ID")
    private String bizRegId;

    /**
    * 车牌颜色
    */
    @ApiModelProperty("车牌颜色(10)")
    @TableField("CAR_COLOR")
    private String carColor;

    /**
    * 行号
    */
    @ApiModelProperty("行号(22)")
    @TableField("LINE_NUM")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal lineNum;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public OmsOrderFwxmWorkFkCarEntity() {
        this.setSubClazz(OmsOrderFwxmWorkFkCarEntity.class);
    }

    public OmsOrderFwxmWorkFkCarEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public OmsOrderFwxmWorkFkCarEntity setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        this.nodifySetFiled("orderNo", orderNo);
        return this;
    }

    public String getOrderNo() {
        this.nodifyGetFiled("orderNo");
        return orderNo;
    }

    public OmsOrderFwxmWorkFkCarEntity setXzwtNo(String xzwtNo) {
        this.xzwtNo = xzwtNo;
        this.nodifySetFiled("xzwtNo", xzwtNo);
        return this;
    }

    public String getXzwtNo() {
        this.nodifyGetFiled("xzwtNo");
        return xzwtNo;
    }

    public OmsOrderFwxmWorkFkCarEntity setWorkNo(String workNo) {
        this.workNo = workNo;
        this.nodifySetFiled("workNo", workNo);
        return this;
    }

    public String getWorkNo() {
        this.nodifyGetFiled("workNo");
        return workNo;
    }

    public OmsOrderFwxmWorkFkCarEntity setFwxmCode(String fwxmCode) {
        this.fwxmCode = fwxmCode;
        this.nodifySetFiled("fwxmCode", fwxmCode);
        return this;
    }

    public String getFwxmCode() {
        this.nodifyGetFiled("fwxmCode");
        return fwxmCode;
    }

    public OmsOrderFwxmWorkFkCarEntity setCarNo(String carNo) {
        this.carNo = carNo;
        this.nodifySetFiled("carNo", carNo);
        return this;
    }

    public String getCarNo() {
        this.nodifyGetFiled("carNo");
        return carNo;
    }

    public OmsOrderFwxmWorkFkCarEntity setCarType(String carType) {
        this.carType = carType;
        this.nodifySetFiled("carType", carType);
        return this;
    }

    public String getCarType() {
        this.nodifyGetFiled("carType");
        return carType;
    }

    public OmsOrderFwxmWorkFkCarEntity setCarSize(String carSize) {
        this.carSize = carSize;
        this.nodifySetFiled("carSize", carSize);
        return this;
    }

    public String getCarSize() {
        this.nodifyGetFiled("carSize");
        return carSize;
    }

    public OmsOrderFwxmWorkFkCarEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public OmsOrderFwxmWorkFkCarEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public OmsOrderFwxmWorkFkCarEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public OmsOrderFwxmWorkFkCarEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public OmsOrderFwxmWorkFkCarEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public OmsOrderFwxmWorkFkCarEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public OmsOrderFwxmWorkFkCarEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public OmsOrderFwxmWorkFkCarEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public OmsOrderFwxmWorkFkCarEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public OmsOrderFwxmWorkFkCarEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public OmsOrderFwxmWorkFkCarEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public OmsOrderFwxmWorkFkCarEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public OmsOrderFwxmWorkFkCarEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public OmsOrderFwxmWorkFkCarEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public OmsOrderFwxmWorkFkCarEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public OmsOrderFwxmWorkFkCarEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public OmsOrderFwxmWorkFkCarEntity setTakeDate(Date takeDate) {
        this.takeDate = takeDate;
        this.nodifySetFiled("takeDate", takeDate);
        return this;
    }

    public Date getTakeDate() {
        this.nodifyGetFiled("takeDate");
        return takeDate;
    }

    public OmsOrderFwxmWorkFkCarEntity setTakeDateStart(Date takeDateStart) {
        this.takeDateStart = takeDateStart;
        this.nodifySetFiled("takeDateStart", takeDateStart);
        return this;
    }

    public Date getTakeDateStart() {
        this.nodifyGetFiled("takeDateStart");
        return takeDateStart;
    }

    public OmsOrderFwxmWorkFkCarEntity setTakeDateEnd(Date takeDateEnd) {
        this.takeDateEnd = takeDateEnd;
        this.nodifySetFiled("takeDateEnd", takeDateEnd);
        return this;
    }

    public Date getTakeDateEnd() {
        this.nodifyGetFiled("takeDateEnd");
        return takeDateEnd;
    }
    public OmsOrderFwxmWorkFkCarEntity setReturnDate(Date returnDate) {
        this.returnDate = returnDate;
        this.nodifySetFiled("returnDate", returnDate);
        return this;
    }

    public Date getReturnDate() {
        this.nodifyGetFiled("returnDate");
        return returnDate;
    }

    public OmsOrderFwxmWorkFkCarEntity setReturnDateStart(Date returnDateStart) {
        this.returnDateStart = returnDateStart;
        this.nodifySetFiled("returnDateStart", returnDateStart);
        return this;
    }

    public Date getReturnDateStart() {
        this.nodifyGetFiled("returnDateStart");
        return returnDateStart;
    }

    public OmsOrderFwxmWorkFkCarEntity setReturnDateEnd(Date returnDateEnd) {
        this.returnDateEnd = returnDateEnd;
        this.nodifySetFiled("returnDateEnd", returnDateEnd);
        return this;
    }

    public Date getReturnDateEnd() {
        this.nodifyGetFiled("returnDateEnd");
        return returnDateEnd;
    }
    public OmsOrderFwxmWorkFkCarEntity setGpsNo(String gpsNo) {
        this.gpsNo = gpsNo;
        this.nodifySetFiled("gpsNo", gpsNo);
        return this;
    }

    public String getGpsNo() {
        this.nodifyGetFiled("gpsNo");
        return gpsNo;
    }

    public OmsOrderFwxmWorkFkCarEntity setSysCode(String sysCode) {
        this.sysCode = sysCode;
        this.nodifySetFiled("sysCode", sysCode);
        return this;
    }

    public String getSysCode() {
        this.nodifyGetFiled("sysCode");
        return sysCode;
    }

    public OmsOrderFwxmWorkFkCarEntity setPcdNo(String pcdNo) {
        this.pcdNo = pcdNo;
        this.nodifySetFiled("pcdNo", pcdNo);
        return this;
    }

    public String getPcdNo() {
        this.nodifyGetFiled("pcdNo");
        return pcdNo;
    }

    public OmsOrderFwxmWorkFkCarEntity setTrailerNo(String trailerNo) {
        this.trailerNo = trailerNo;
        this.nodifySetFiled("trailerNo", trailerNo);
        return this;
    }

    public String getTrailerNo() {
        this.nodifyGetFiled("trailerNo");
        return trailerNo;
    }

    public OmsOrderFwxmWorkFkCarEntity setDriverName(String driverName) {
        this.driverName = driverName;
        this.nodifySetFiled("driverName", driverName);
        return this;
    }

    public String getDriverName() {
        this.nodifyGetFiled("driverName");
        return driverName;
    }

    public OmsOrderFwxmWorkFkCarEntity setTel(String tel) {
        this.tel = tel;
        this.nodifySetFiled("tel", tel);
        return this;
    }

    public String getTel() {
        this.nodifyGetFiled("tel");
        return tel;
    }

    public OmsOrderFwxmWorkFkCarEntity setIdCard(String idCard) {
        this.idCard = idCard;
        this.nodifySetFiled("idCard", idCard);
        return this;
    }

    public String getIdCard() {
        this.nodifyGetFiled("idCard");
        return idCard;
    }

    public OmsOrderFwxmWorkFkCarEntity setWeight(String weight) {
        this.weight = weight;
        this.nodifySetFiled("weight", weight);
        return this;
    }

    public String getWeight() {
        this.nodifyGetFiled("weight");
        return weight;
    }

    public OmsOrderFwxmWorkFkCarEntity setBoxNo(String boxNo) {
        this.boxNo = boxNo;
        this.nodifySetFiled("boxNo", boxNo);
        return this;
    }

    public String getBoxNo() {
        this.nodifyGetFiled("boxNo");
        return boxNo;
    }

    public OmsOrderFwxmWorkFkCarEntity setBizRegId(String bizRegId) {
        this.bizRegId = bizRegId;
        this.nodifySetFiled("bizRegId", bizRegId);
        return this;
    }

    public String getBizRegId() {
        this.nodifyGetFiled("bizRegId");
        return bizRegId;
    }

    public OmsOrderFwxmWorkFkCarEntity setCarColor(String carColor) {
        this.carColor = carColor;
        this.nodifySetFiled("carColor", carColor);
        return this;
    }

    public String getCarColor() {
        this.nodifyGetFiled("carColor");
        return carColor;
    }

    public OmsOrderFwxmWorkFkCarEntity setLineNum(BigDecimal lineNum) {
        this.lineNum = lineNum;
        this.nodifySetFiled("lineNum", lineNum);
        return this;
    }

    public BigDecimal getLineNum() {
        this.nodifyGetFiled("lineNum");
        return lineNum;
    }

}
