package com.eci.project.etmsOpHead.service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

public class SerialNumberGenerator {

    // 生成 ATT_NO：B + START_NO + yyMMdd + 5位序号
    public static String generateAttNo(String startNo, int sequence) {

        String dateSuffix = LocalDate.now().format(DateTimeFormatter.ofPattern("yyMMdd"));

        String sequenceStr = String.format("%05d", sequence);

        return "B" + startNo + dateSuffix + sequenceStr;
    }

    // 生成 OP_NO：9位序号
    public static String generateOpNo(int sequence) {
        return String.format("%09d", sequence);
    }

//    // 测试示例
//    public static void main(String[] args) {
//        // 假设组织编码是 "orgCode"
//        String orgCode = "orgCode";
//
//        // 假设当前序号为1和2
//        int attSequence = 1;
//        int opSequence = 1;
//
//        String attNo = generateAttNo(orgCode, attSequence);
//        String opNo = generateOpNo(opSequence);
//
//        System.out.println("运输委托单号 ATT_NO: " + attNo);
//        System.out.println("平台号 OP_NO: " + opNo);
//    }
}