package com.eci.project.etmsOpAttemperLine.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.etmsOpAttemperLine.dao.EtmsOpAttemperLineDao;
import com.eci.project.etmsOpAttemperLine.entity.EtmsOpAttemperLineEntity;
import com.eci.project.etmsOpAttemperLine.validate.EtmsOpAttemperLineVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 托运线路站点信息Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-06-13
*/
@Service
@Slf4j
public class EtmsOpAttemperLineService implements EciBaseService<EtmsOpAttemperLineEntity> {

    @Autowired
    private EtmsOpAttemperLineDao etmsOpAttemperLineDao;

    @Autowired
    private EtmsOpAttemperLineVal etmsOpAttemperLineVal;


    @Override
    public TgPageInfo queryPageList(EtmsOpAttemperLineEntity entity) {
        EciQuery<EtmsOpAttemperLineEntity> eciQuery = EciQuery.buildQuery(entity);
        List<EtmsOpAttemperLineEntity> entities = etmsOpAttemperLineDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public EtmsOpAttemperLineEntity save(EtmsOpAttemperLineEntity entity) {
        // 返回实体对象
        EtmsOpAttemperLineEntity etmsOpAttemperLineEntity = null;
        etmsOpAttemperLineVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            etmsOpAttemperLineEntity = etmsOpAttemperLineDao.insertOne(entity);

        }else{

            etmsOpAttemperLineEntity = etmsOpAttemperLineDao.updateByEntityId(entity);

        }
        return etmsOpAttemperLineEntity;
    }

    @Override
    public List<EtmsOpAttemperLineEntity> selectList(EtmsOpAttemperLineEntity entity) {
        return etmsOpAttemperLineDao.selectList(entity);
    }

    @Override
    public EtmsOpAttemperLineEntity selectOneById(Serializable id) {
        return etmsOpAttemperLineDao.selectById(id);
    }


    @Override
    public void insertBatch(List<EtmsOpAttemperLineEntity> list) {
        etmsOpAttemperLineDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return etmsOpAttemperLineDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return etmsOpAttemperLineDao.deleteById(id);
    }

}