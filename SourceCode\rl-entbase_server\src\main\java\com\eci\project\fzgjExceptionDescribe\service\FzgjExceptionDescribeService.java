package com.eci.project.fzgjExceptionDescribe.service;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjExceptionDescribe.dao.FzgjExceptionDescribeDao;
import com.eci.project.fzgjExceptionDescribe.entity.FzgjExceptionDescribeEntity;
import com.eci.project.fzgjExceptionDescribe.validate.FzgjExceptionDescribeVal;

import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 异常描述Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-01
*/
@Service
@Slf4j
public class FzgjExceptionDescribeService implements EciBaseService<FzgjExceptionDescribeEntity> {

    @Autowired
    private FzgjExceptionDescribeDao fzgjExceptionDescribeDao;

    @Autowired
    private FzgjExceptionDescribeVal fzgjExceptionDescribeVal;


    @Override
    public TgPageInfo queryPageList(FzgjExceptionDescribeEntity entity) {
        entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
        EciQuery<FzgjExceptionDescribeEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjExceptionDescribeEntity> entities = fzgjExceptionDescribeDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjExceptionDescribeEntity save(FzgjExceptionDescribeEntity entity) {
        // 返回实体对象
        FzgjExceptionDescribeEntity fzgjExceptionDescribeEntity = null;
        fzgjExceptionDescribeVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setGuid(IdWorker.get32UUID());
            entity.setCreateDate(new java.util.Date());
            entity.setUpdateDate(new java.util.Date());
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
            entity.setNodeCode(UserContext.getUserInfo().getDeptCode());
            entity.setNodeName(UserContext.getUserInfo().getDeptName());
            entity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            entity.setCompanyName(UserContext.getUserInfo().getCompanyName());
            fzgjExceptionDescribeEntity = fzgjExceptionDescribeDao.insertOne(entity);

        }else{
            entity.setUpdateDate(new java.util.Date());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            fzgjExceptionDescribeEntity = fzgjExceptionDescribeDao.updateByEntityId(entity);

        }
        return fzgjExceptionDescribeEntity;
    }

    @Override
    public List<FzgjExceptionDescribeEntity> selectList(FzgjExceptionDescribeEntity entity) {
        return fzgjExceptionDescribeDao.selectList(entity);
    }

    @Override
    public FzgjExceptionDescribeEntity selectOneById(Serializable id) {
        return fzgjExceptionDescribeDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjExceptionDescribeEntity> list) {
        fzgjExceptionDescribeDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjExceptionDescribeDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjExceptionDescribeDao.deleteById(id);
    }

}