package com.eci.project.fzgjScoreCar.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjScoreCar.entity.FzgjScoreCarEntity;

import org.springframework.stereotype.Service;


/**
* 企业评分Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-06-16
*/
@Service
public class FzgjScoreCarVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjScoreCarEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjScoreCarEntity entity, BusinessType businessType) {

    }

}
