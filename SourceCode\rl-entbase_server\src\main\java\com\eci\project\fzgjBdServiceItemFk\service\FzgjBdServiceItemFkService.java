package com.eci.project.fzgjBdServiceItemFk.service;

import com.eci.common.db.DBHelper;
import com.eci.common.utils.CommonLib;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjBdServiceItemFk.dao.FzgjBdServiceItemFkDao;
import com.eci.project.fzgjBdServiceItemFk.entity.FzgjBdServiceItemFkEntity;
import com.eci.project.fzgjBdServiceItemFk.validate.FzgjBdServiceItemFkVal;

import com.eci.sso.role.entity.UserContext;
import com.eci.wu.core.DataTable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 企业服务项目对应反馈页面Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-14
*/
@Service
@Slf4j
public class FzgjBdServiceItemFkService implements EciBaseService<FzgjBdServiceItemFkEntity> {

    @Autowired
    private FzgjBdServiceItemFkDao fzgjBdServiceItemFkDao;

    @Autowired
    private FzgjBdServiceItemFkVal fzgjBdServiceItemFkVal;

    CommonLib cmn = CommonLib.getInstance();
    @Override
    public TgPageInfo queryPageList(FzgjBdServiceItemFkEntity entity) {
        EciQuery<FzgjBdServiceItemFkEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjBdServiceItemFkEntity> entities = fzgjBdServiceItemFkDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }

    public DataTable getCheckEditItem(String serviceCode){
        String sql="SELECT A.GUID as \"GUID\",B.GUID \"PTGUID\",A.CODE as \"CODE\",A.NAME as \"NAME\"," +
                "case NVL(B.GUID,2) when '2' then 0 else 1 end as \"CHECKED\",\n" +
                "case NVL(B.GUID,2) when '2' then 0 else 1 end as \"ORGCHECKED\"  FROM FZGJ_BD_OMS_PAGES_FK A left join (\n" +
                "  SELECT A.GUID,A.CODE FROM fzgj_bd_service_item_FK A WHERE A.STATUS='Y'\n" +
                "  AND A.serviceItem_Id = %s and group_code='%s' \n" +
                "  ) B on A.CODE=B.CODE";
        serviceCode=cmn.SQLQ(serviceCode);
        sql=String.format(sql,serviceCode, UserContext.getUserInfo().getCompanyCode(),serviceCode);
        return DBHelper.getDataTable(sql);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjBdServiceItemFkEntity save(FzgjBdServiceItemFkEntity entity) {
        // 返回实体对象
        FzgjBdServiceItemFkEntity fzgjBdServiceItemFkEntity = null;
        fzgjBdServiceItemFkVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjBdServiceItemFkEntity = fzgjBdServiceItemFkDao.insertOne(entity);

        }else{

            fzgjBdServiceItemFkEntity = fzgjBdServiceItemFkDao.updateByEntityId(entity);

        }
        return fzgjBdServiceItemFkEntity;
    }

    @Override
    public List<FzgjBdServiceItemFkEntity> selectList(FzgjBdServiceItemFkEntity entity) {
        return fzgjBdServiceItemFkDao.selectList(entity);
    }

    @Override
    public FzgjBdServiceItemFkEntity selectOneById(Serializable id) {
        return fzgjBdServiceItemFkDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjBdServiceItemFkEntity> list) {
        fzgjBdServiceItemFkDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjBdServiceItemFkDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjBdServiceItemFkDao.deleteById(id);
    }

}