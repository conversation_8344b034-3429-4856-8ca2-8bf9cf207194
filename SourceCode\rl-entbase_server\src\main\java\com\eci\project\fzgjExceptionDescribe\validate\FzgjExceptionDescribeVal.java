package com.eci.project.fzgjExceptionDescribe.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjExceptionDescribe.entity.FzgjExceptionDescribeEntity;

import org.springframework.stereotype.Service;


/**
* 异常描述Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-01
*/
@Service
public class FzgjExceptionDescribeVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjExceptionDescribeEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjExceptionDescribeEntity entity, BusinessType businessType) {

    }

}
