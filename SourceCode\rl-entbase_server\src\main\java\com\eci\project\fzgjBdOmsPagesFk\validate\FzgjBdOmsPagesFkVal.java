package com.eci.project.fzgjBdOmsPagesFk.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjBdOmsPagesFk.entity.FzgjBdOmsPagesFkEntity;

import org.springframework.stereotype.Service;


/**
* 订单反馈页面Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-14
*/
@Service
public class FzgjBdOmsPagesFkVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjBdOmsPagesFkEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjBdOmsPagesFkEntity entity, BusinessType businessType) {

    }

}
