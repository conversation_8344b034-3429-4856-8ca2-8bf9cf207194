package com.eci.project.fzgjBdCountry.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;


/**
* 国家对象 FZGJ_BD_COUNTRY
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-03-14
*/
@ApiModel("国家")
@TableName("FZGJ_BD_COUNTRY")
public class FzgjBdCountryEntity extends FzgjBdCountryBaseEntity{

}
