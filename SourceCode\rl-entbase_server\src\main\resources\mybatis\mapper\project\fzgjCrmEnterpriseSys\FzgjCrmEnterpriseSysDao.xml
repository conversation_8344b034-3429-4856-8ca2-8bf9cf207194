<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.fzgjCrmEnterpriseSys.dao.FzgjCrmEnterpriseSysDao">
    <resultMap type="FzgjCrmEnterpriseSysEntity" id="FzgjCrmEnterpriseSysResult">
        <result property="guid" column="GUID"/>
        <result property="sysCode" column="SYS_CODE"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
    </resultMap>

    <sql id="selectFzgjCrmEnterpriseSysEntityVo">
        select
            GUID,
            SYS_CODE,
            CREATE_USER,
            CREATE_USER_NAME,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_USER_NAME,
            UPDATE_DATE,
            GROUP_CODE,
            GROUP_NAME
        from FZGJ_CRM_ENTERPRISE_SYS
    </sql>
</mapper>