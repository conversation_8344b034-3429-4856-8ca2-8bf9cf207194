package com.eci.project.omsISlResult.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.omsISlResult.entity.OmsISlResultEntity;

import org.springframework.stereotype.Service;


/**
* 受理结果及单据编号Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-21
*/
@Service
public class OmsISlResultVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(OmsISlResultEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(OmsISlResultEntity entity, BusinessType businessType) {

    }

}
