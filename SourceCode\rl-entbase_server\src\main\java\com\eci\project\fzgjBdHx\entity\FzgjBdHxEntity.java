package com.eci.project.fzgjBdHx.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.common.utils.ExportUtils.Excel;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.math.BigDecimal;
import java.util.Date;


/**
* 航线信息对象 FZGJ_BD_HX
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-03-14
*/
@ApiModel("航线信息")
@TableName("FZGJ_BD_HX")
@FieldNameConstants
public class FzgjBdHxEntity extends EciBaseEntity{
    /**
    * GUID
    */
    @ApiModelProperty("GUID(72)")
    @TableId("GUID")
    private String guid;

    /**
    * 代码
    */
    @ApiModelProperty("代码(100)")
    @TableField("CODE")
    @Excel(value = "航线代码",order = 0)
    private String code;

    /**
    * 名称
    */
    @ApiModelProperty("名称(200)")
    @TableField("NAME")
    @Excel(value = "航线名称",order = 1)
    private String name;

    /**
    * 英文名称
    */
    @ApiModelProperty("英文名称(50)")
    @TableField("EN_NAME")
    private String enName;

    /**
    * 状态
    */
    @ApiModelProperty("状态(1)")
    @TableField("STATUS")
    @Excel(value = "状态",order = 2)
    private String status;

    /**
    * 顺序
    */
    @ApiModelProperty("顺序(22)")
    @TableField("SEQ")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal seq;

    /**
    * 描述
    */
    @ApiModelProperty("描述(1,000)")
    @TableField("MEMO")
    @Excel(value = "描述",order = 3)
    private String memo;

    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    @Excel(value = "录入时间",order = 6)
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(72)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * CREATE_USER
    */
    @ApiModelProperty("CREATE_USER(50)")
    @TableField("CREATE_USER_NAME")
    @Excel(value = "录入人",order = 5)
    private String createUserName;

    /**
    * 修改日期
    */
    @ApiModelProperty("修改日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("修改日期开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("修改日期结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 修改人
    */
    @ApiModelProperty("修改人(72)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * UPDATE_USER
    */
    @ApiModelProperty("UPDATE_USER(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public FzgjBdHxEntity() {
        this.setSubClazz(FzgjBdHxEntity.class);
    }

    public FzgjBdHxEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public FzgjBdHxEntity setCode(String code) {
        this.code = code;
        this.nodifySetFiled("code", code);
        return this;
    }

    public String getCode() {
        this.nodifyGetFiled("code");
        return code;
    }

    public FzgjBdHxEntity setName(String name) {
        this.name = name;
        this.nodifySetFiled("name", name);
        return this;
    }

    public String getName() {
        this.nodifyGetFiled("name");
        return name;
    }

    public FzgjBdHxEntity setEnName(String enName) {
        this.enName = enName;
        this.nodifySetFiled("enName", enName);
        return this;
    }

    public String getEnName() {
        this.nodifyGetFiled("enName");
        return enName;
    }

    public FzgjBdHxEntity setStatus(String status) {
        this.status = status;
        this.nodifySetFiled("status", status);
        return this;
    }

    public String getStatus() {
        this.nodifyGetFiled("status");
        return status;
    }

    public FzgjBdHxEntity setSeq(BigDecimal seq) {
        this.seq = seq;
        this.nodifySetFiled("seq", seq);
        return this;
    }

    public BigDecimal getSeq() {
        this.nodifyGetFiled("seq");
        return seq;
    }

    public FzgjBdHxEntity setMemo(String memo) {
        this.memo = memo;
        this.nodifySetFiled("memo", memo);
        return this;
    }

    public String getMemo() {
        this.nodifyGetFiled("memo");
        return memo;
    }

    public FzgjBdHxEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public FzgjBdHxEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public FzgjBdHxEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public FzgjBdHxEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public FzgjBdHxEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public FzgjBdHxEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public FzgjBdHxEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public FzgjBdHxEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public FzgjBdHxEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public FzgjBdHxEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

}
