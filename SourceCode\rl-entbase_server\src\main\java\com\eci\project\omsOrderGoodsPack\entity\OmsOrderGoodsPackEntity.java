package com.eci.project.omsOrderGoodsPack.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.common.DictField;
import com.eci.common.ZsrBaseEntity;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 货物包装表对象 OMS_ORDER_GOODS_PACK
 * 可以自己扩展字段
 *
 * @<NAME_EMAIL>
 * @date 2025-06-10
 */
@ApiModel("货物包装表")
@TableName("OMS_ORDER_GOODS_PACK")
@FieldNameConstants
public class OmsOrderGoodsPackEntity extends ZsrBaseEntity {
    /**
     * GUID
     */
    @ApiModelProperty("GUID(36)")
    @TableId("GUID")
    private String guid;

    /**
     * 明细唯一编号
     */
    @ApiModelProperty("明细唯一编号(36)")
    @TableField("GOODS_GUID")
    private String goodsGuid;

    /**
     * 订单号
     */
    @ApiModelProperty("订单号(36)")
    @TableField("ORDER_NO")
    private String orderNo;

    /**
     * 委托单编号
     */
    @ApiModelProperty("委托单编号(36)")
    @TableField("PRE_NO")
    private String preNo;

    /**
     * 包装类型
     */
    @ApiModelProperty("包装类型(10)")
    @TableField("PACK_TYPE")
    @DictField(queryKey = "OMS_GOODS_PACK_TYPE")
    private String packType;

    /**
     * 是否木质包装
     */
    @ApiModelProperty("是否木质包装(1)")
    @TableField("IS_MZBZ")
    private String isMzbz;

    /**
     * 件数改为包装数
     */
    @ApiModelProperty("件数改为包装数(22)")
    @TableField("QTY_PACK")
    private Integer qtyPack;

    /**
     * 单件长(m)
     */
    @ApiModelProperty("单件长(m)(22)")
    @TableField("LONGS")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal longs;

    /**
     * 单件宽(m)
     */
    @ApiModelProperty("单件宽(m)(22)")
    @TableField("WIDTHS")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal widths;

    /**
     * 单件高(m)
     */
    @ApiModelProperty("单件高(m)(22)")
    @TableField("HEIGHTS")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal heights;

    /**
     * 单件毛重(kg)
     */
    @ApiModelProperty("单件毛重(kg)(22)")
    @TableField("WEIGHT_PIECE")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal weightPiece;

    /**
     * 可叠放层数
     */
    @ApiModelProperty("可叠放层数(22)")
    @TableField("KDFCS")
    private Integer kdfcs;

    /**
     * 特殊包装说明
     */
    @ApiModelProperty("特殊包装说明(500)")
    @TableField("PACK_MEMO")
    private String packMemo;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
     * 创建日期
     */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist = false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist = false)
    private Date createDateEnd;

    /**
     * 最后修改人
     */
    @ApiModelProperty("最后修改人(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
     * 最后修改人
     */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
     * 最后修改日期
     */
    @ApiModelProperty("最后修改日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("最后修改日期开始")
    @TableField(exist = false)
    private Date updateDateStart;

    @ApiModelProperty("最后修改日期结束")
    @TableField(exist = false)
    private Date updateDateEnd;

    /**
     * 归属公司
     */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
     * 归属公司
     */
    @ApiModelProperty("归属公司(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
     * 归属组织
     */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
     * 归属组织
     */
    @ApiModelProperty("归属组织(200)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
     * 归属集团
     */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
     * 归属集团
     */
    @ApiModelProperty("归属集团(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
     * 货物数量
     */
    @ApiModelProperty("货物数量(22)")
    @TableField("QTY_GOODS")
    private Integer qtyGoods;

    /**
     * 计量单位
     */
    @ApiModelProperty("计量单位(20)")
    @TableField("UNIT")
    private String unit;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public OmsOrderGoodsPackEntity() {
        this.setSubClazz(OmsOrderGoodsPackEntity.class);
    }

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public OmsOrderGoodsPackEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGoodsGuid() {
        this.nodifyGetFiled("goodsGuid");
        return goodsGuid;
    }

    public OmsOrderGoodsPackEntity setGoodsGuid(String goodsGuid) {
        this.goodsGuid = goodsGuid;
        this.nodifySetFiled("goodsGuid", goodsGuid);
        return this;
    }

    public String getOrderNo() {
        this.nodifyGetFiled("orderNo");
        return orderNo;
    }

    public OmsOrderGoodsPackEntity setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        this.nodifySetFiled("orderNo", orderNo);
        return this;
    }

    public String getPreNo() {
        this.nodifyGetFiled("preNo");
        return preNo;
    }

    public OmsOrderGoodsPackEntity setPreNo(String preNo) {
        this.preNo = preNo;
        this.nodifySetFiled("preNo", preNo);
        return this;
    }

    public String getPackType() {
        this.nodifyGetFiled("packType");
        return packType;
    }

    public OmsOrderGoodsPackEntity setPackType(String packType) {
        this.packType = packType;
        this.nodifySetFiled("packType", packType);
        return this;
    }

    public String getIsMzbz() {
        this.nodifyGetFiled("isMzbz");
        return isMzbz;
    }

    public OmsOrderGoodsPackEntity setIsMzbz(String isMzbz) {
        this.isMzbz = isMzbz;
        this.nodifySetFiled("isMzbz", isMzbz);
        return this;
    }

    public Integer getQtyPack() {
        this.nodifyGetFiled("qtyPack");
        return qtyPack;
    }

    public OmsOrderGoodsPackEntity setQtyPack(Integer qtyPack) {
        this.qtyPack = qtyPack;
        this.nodifySetFiled("qtyPack", qtyPack);
        return this;
    }

    public BigDecimal getLongs() {
        this.nodifyGetFiled("longs");
        return longs;
    }

    public OmsOrderGoodsPackEntity setLongs(BigDecimal longs) {
        this.longs = longs;
        this.nodifySetFiled("longs", longs);
        return this;
    }

    public BigDecimal getWidths() {
        this.nodifyGetFiled("widths");
        return widths;
    }

    public OmsOrderGoodsPackEntity setWidths(BigDecimal widths) {
        this.widths = widths;
        this.nodifySetFiled("widths", widths);
        return this;
    }

    public BigDecimal getHeights() {
        this.nodifyGetFiled("heights");
        return heights;
    }

    public OmsOrderGoodsPackEntity setHeights(BigDecimal heights) {
        this.heights = heights;
        this.nodifySetFiled("heights", heights);
        return this;
    }

    public BigDecimal getWeightPiece() {
        this.nodifyGetFiled("weightPiece");
        return weightPiece;
    }

    public OmsOrderGoodsPackEntity setWeightPiece(BigDecimal weightPiece) {
        this.weightPiece = weightPiece;
        this.nodifySetFiled("weightPiece", weightPiece);
        return this;
    }

    public Integer getKdfcs() {
        this.nodifyGetFiled("kdfcs");
        return kdfcs;
    }

    public OmsOrderGoodsPackEntity setKdfcs(Integer kdfcs) {
        this.kdfcs = kdfcs;
        this.nodifySetFiled("kdfcs", kdfcs);
        return this;
    }

    public String getPackMemo() {
        this.nodifyGetFiled("packMemo");
        return packMemo;
    }

    public OmsOrderGoodsPackEntity setPackMemo(String packMemo) {
        this.packMemo = packMemo;
        this.nodifySetFiled("packMemo", packMemo);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public OmsOrderGoodsPackEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public OmsOrderGoodsPackEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public OmsOrderGoodsPackEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public OmsOrderGoodsPackEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }

    public OmsOrderGoodsPackEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public OmsOrderGoodsPackEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public OmsOrderGoodsPackEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public OmsOrderGoodsPackEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public OmsOrderGoodsPackEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }

    public OmsOrderGoodsPackEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public OmsOrderGoodsPackEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public OmsOrderGoodsPackEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public OmsOrderGoodsPackEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public OmsOrderGoodsPackEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public OmsOrderGoodsPackEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public OmsOrderGoodsPackEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public Integer getQtyGoods() {
        this.nodifyGetFiled("qtyGoods");
        return qtyGoods;
    }

    public OmsOrderGoodsPackEntity setQtyGoods(Integer qtyGoods) {
        this.qtyGoods = qtyGoods;
        this.nodifySetFiled("qtyGoods", qtyGoods);
        return this;
    }

    public String getUnit() {
        this.nodifyGetFiled("unit");
        return unit;
    }

    public OmsOrderGoodsPackEntity setUnit(String unit) {
        this.unit = unit;
        this.nodifySetFiled("unit", unit);
        return this;
    }

}
