package com.eci.project.fzgjTaskLimitation.service;

import com.eci.crud.service.EciBaseService;
import com.eci.project.fzgjTaskLimitation.entity.FzgjTaskLimitationEntity;

import java.util.Date;
import java.util.List;
import java.util.Map;


/**
* 作业环节及标准时效Service接口
* 业务逻辑层, 接口代码, 只需要写自定义的部分, 增删改查部分在父级接口已经实现
* @<NAME_EMAIL>
* @date 2025-03-28
*/
public interface IFzgjTaskLimitationService extends EciBaseService<FzgjTaskLimitationEntity> {

    boolean taskLimitationInit();
    boolean taskLimitationSaveSelect(List<String> entity);
    boolean taskLimitationSeqUpdate(FzgjTaskLimitationEntity entity);
    List<Map<String, Object>> getCheckedTask(FzgjTaskLimitationEntity entity);
}
