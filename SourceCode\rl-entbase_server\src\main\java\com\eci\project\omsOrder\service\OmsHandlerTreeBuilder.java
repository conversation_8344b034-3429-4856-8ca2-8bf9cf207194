package com.eci.project.omsOrder.service;

import com.eci.common.elementui.TreeNode;
import com.eci.project.fzgjBdServiceItem.entity.FzgjBdServiceItemEntity;
import com.eci.project.fzgjBdServiceItemPages.entity.FzgjBdServiceItemPagesEntity;
import com.eci.project.fzgjBdServiceItemPt.entity.FzgjBdServiceItemPtEntity;
import com.eci.project.fzgjBdServiceType.entity.FzgjBdServiceTypeEntity;
import com.eci.project.fzgjBdServiceTypeCom.entity.FzgjBdServiceTypeComEntity;

import java.util.*;
import java.util.stream.Collectors;


public class OmsHandlerTreeBuilder {

    public static class TreeResult {
        private List<TreeNode> tree;
        private Map<String, String> parentMap; // key: nodeGuid -> value: parentGuid

        public TreeResult(List<TreeNode> tree, Map<String, String> parentMap) {
            this.tree = tree;
            this.parentMap = parentMap;
        }

        public List<TreeNode> getTree() {
            return tree;
        }

        public Map<String, String> getParentMap() {
            return parentMap;
        }
    }

    public static TreeResult buildTreeWithParentMap(
            List<FzgjBdServiceTypeEntity> serviceTypes,
            List<FzgjBdServiceItemPtEntity> fzgjBdServiceItemPtEntityList,
            List<FzgjBdServiceItemEntity> serviceItems, List<FzgjBdServiceItemPagesEntity> listFwxmPage) {

        Map<String, TreeNode> nodeMap = new HashMap<>();
        Map<String, String> parentMap = new HashMap<>();

        // 添加 SERVICE_TYPE 节点
        for (FzgjBdServiceTypeEntity type : serviceTypes) {
            String pageUrl = listFwxmPage.stream()
                    .filter(p -> p.getServiceItemCode().equals(type.getCode()))
                    .findFirst()
                    .map(FzgjBdServiceItemPagesEntity::getPageUrl)
                    .orElse(null);
            TreeNode node = new TreeNode(type.getGuid(), type.getCode(), type.getName(), type.getStatus(), pageUrl);
            nodeMap.put(type.getGuid(), node);
        }

        // 添加 SERVICE_ITEM 节点，并建立父子关系
        for (FzgjBdServiceItemEntity item : serviceItems) {
            fzgjBdServiceItemPtEntityList.stream()
                    .filter(p -> p.getCode().equals(item.getCode()))
                    .findFirst()
                    .ifPresent(p -> {
                        listFwxmPage.stream().filter(c -> c.getServiceItemCode().equals(p.getGuid())
                        ).findFirst().ifPresent(d -> {
                            TreeNode node = new TreeNode(item.getGuid(), d.getCode(), p.getName(),
                                    item.getStatus(), d.getPageUrl());
                            nodeMap.put(item.getGuid(), node);

                            String parentId = item.getParentid();
                            if (parentId != null && nodeMap.containsKey(parentId)) {
                                nodeMap.get(parentId).addChild(node);
                                parentMap.put(item.getGuid(), parentId); // 记录父子关系
                            }
                        });

                    });


//            String pageUrl = listFwxmPage.stream()
//                    .filter(p -> p.getServiceItemCode().equals(item.getCode())
//                            || p.getCode().equals(item.getCode()))
//                    .findFirst()
//                    .map(FzgjBdServiceItemPagesEntity::getPageUrl)
//                    .orElse(null);
//            TreeNode node = new TreeNode(item.getGuid(), item.getCode(), item.getName(), item.getStatus(), pageUrl);
//            nodeMap.put(item.getGuid(), node);
//
//            String parentId = item.getParentid();
//            if (parentId != null && nodeMap.containsKey(parentId)) {
//                nodeMap.get(parentId).addChild(node);
//                parentMap.put(item.getGuid(), parentId); // 记录父子关系
//            }
        }

        // 根节点是所有 serviceType 的节点
        List<TreeNode> rootNodes = serviceTypes.stream()
                .map(t -> nodeMap.get(t.getGuid()))
                .collect(Collectors.toList());

        return new TreeResult(rootNodes, parentMap);
    }

    /**
     * 查找指定子节点 GUID 所属根节点的父节点 GUID
     */
    public static String findRootParentGuidByChildGuid(Map<String, String> parentMap, String childGuid) {
        String currentNodeGuid = childGuid;

        // 向上查找直到没有父节点为止
        while (parentMap.containsKey(currentNodeGuid)) {
            String parentGuid = parentMap.get(currentNodeGuid);
            currentNodeGuid = parentGuid;
        }

        // 此时 currentNodeGuid 是根节点 GUID，再查一次它的父节点
        // 如果根节点本身还有父节点，则返回，否则返回 null
        return parentMap.get(currentNodeGuid);
    }

}
