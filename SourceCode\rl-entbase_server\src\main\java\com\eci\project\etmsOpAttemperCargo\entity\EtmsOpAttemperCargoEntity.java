package com.eci.project.etmsOpAttemperCargo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.math.BigDecimal;
import java.util.Date;


/**
* 托运货物信息对象 ETMS_OP_ATTEMPER_CARGO
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-06-13
*/
@ApiModel("托运货物信息")
@TableName("ETMS_OP_ATTEMPER_CARGO")
@FieldNameConstants
public class EtmsOpAttemperCargoEntity extends EciBaseEntity{
    /**
    * 主键
    */
    @ApiModelProperty("主键(50)")
    @TableId("GUID")
    private String guid;

    /**
    * 业务编号
    */
    @ApiModelProperty("业务编号(20)")
    @TableField("OP_NO")
    private String opNo;

    /**
    * 委托GUID
    */
    @ApiModelProperty("委托GUID(50)")
    @TableField("ATT_GUID")
    private String attGuid;

    /**
    * 用车需求信息GUID
    */
    @ApiModelProperty("用车需求信息GUID(50)")
    @TableField("CAR_GUID")
    private String carGuid;

    /**
    * 起运站点信息GUID
    */
    @ApiModelProperty("起运站点信息GUID(50)")
    @TableField("START_LINE_GUID")
    private String startLineGuid;

    /**
    * 作业顺序(本站点)
    */
    @ApiModelProperty("作业顺序(本站点)(22)")
    @TableField("OP_SEQ")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal opSeq;

    /**
    * 任务(提货/卸货)
    */
    @ApiModelProperty("任务(提货/卸货)(20)")
    @TableField("OP_TASK")
    private String opTask;

    /**
    * 货物型态
    */
    @ApiModelProperty("货物型态(20)")
    @TableField("CARGO_TYPE")
    private String cargoType;

    /**
    * 品名
    */
    @ApiModelProperty("品名(200)")
    @TableField("CARGO")
    private String cargo;

    /**
    * 料号
    */
    @ApiModelProperty("料号(40)")
    @TableField("SKU")
    private String sku;

    /**
    * 码货方式
    */
    @ApiModelProperty("码货方式(20)")
    @TableField("DELIVERY_WAY")
    private String deliveryWay;

    /**
    * 集装箱箱号
    */
    @ApiModelProperty("集装箱箱号(40)")
    @TableField("BOX_NO")
    private String boxNo;

    /**
    * 集装箱箱型
    */
    @ApiModelProperty("集装箱箱型(20)")
    @TableField("BOX_TYPE")
    private String boxType;

    /**
    * 封号
    */
    @ApiModelProperty("封号(40)")
    @TableField("SF_NO")
    private String sfNo;

    /**
    * 件数
    */
    @ApiModelProperty("件数(22)")
    @TableField("PIECES")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal pieces;

    /**
    * 件数单位
    */
    @ApiModelProperty("件数单位(10)")
    @TableField("PIECES_UNIT")
    private String piecesUnit;

    /**
    * 重量
    */
    @ApiModelProperty("重量(22)")
    @TableField("WEIGHT")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal weight;

    /**
    * 重量单位
    */
    @ApiModelProperty("重量单位(10)")
    @TableField("WEIGHT_UNIT")
    private String weightUnit;

    /**
    * 体积
    */
    @ApiModelProperty("体积(22)")
    @TableField("VOLUME")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal volume;

    /**
    * 体积单位
    */
    @ApiModelProperty("体积单位(10)")
    @TableField("VOLUME_UNIT")
    private String volumeUnit;

    /**
    * 备注
    */
    @ApiModelProperty("备注(500)")
    @TableField("MEMO")
    private String memo;

    /**
    * 创建企业
    */
    @ApiModelProperty("创建企业(20)")
    @TableField("CREATE_COMPANY")
    private String createCompany;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 编辑人
    */
    @ApiModelProperty("编辑人(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 编辑日期
    */
    @ApiModelProperty("编辑日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("编辑日期开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("编辑日期结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 终到站点信息GUID
    */
    @ApiModelProperty("终到站点信息GUID(50)")
    @TableField("END_LINE_GUID")
    private String endLineGuid;

    /**
    * 起始要求作业时间
    */
    @ApiModelProperty("起始要求作业时间(7)")
    @TableField("START_REQUEST_DATE")
    private Date startRequestDate;

    @ApiModelProperty("起始要求作业时间开始")
    @TableField(exist=false)
    private Date startRequestDateStart;

    @ApiModelProperty("起始要求作业时间结束")
    @TableField(exist=false)
    private Date startRequestDateEnd;

    /**
    * 起始作业要求
    */
    @ApiModelProperty("起始作业要求(500)")
    @TableField("START_OP_REQUEST")
    private String startOpRequest;

    /**
    * 终到要求作业时间
    */
    @ApiModelProperty("终到要求作业时间(7)")
    @TableField("END_REQUEST_DATE")
    private Date endRequestDate;

    @ApiModelProperty("终到要求作业时间开始")
    @TableField(exist=false)
    private Date endRequestDateStart;

    @ApiModelProperty("终到要求作业时间结束")
    @TableField(exist=false)
    private Date endRequestDateEnd;

    /**
    * 终到作业要求
    */
    @ApiModelProperty("终到作业要求(500)")
    @TableField("END_OP_REQUEST")
    private String endOpRequest;

    /**
    * 托运货物编号
    */
    @ApiModelProperty("托运货物编号(20)")
    @TableField("OPCARGO_NO")
    private String opcargoNo;

    /**
    * 发货方业务伙伴代码
    */
    @ApiModelProperty("发货方业务伙伴代码(50)")
    @TableField("SENDER_CODE")
    private String senderCode;

    /**
    * 收货方业务伙伴代码
    */
    @ApiModelProperty("收货方业务伙伴代码(20)")
    @TableField("CONSIGNEE_CODE")
    private String consigneeCode;

    /**
    * 所属部门ID
    */
    @ApiModelProperty("所属部门ID(50)")
    @TableField("ORG_DEP_ID")
    private String orgDepId;

    /**
    * 所属部门CODE
    */
    @ApiModelProperty("所属部门CODE(50)")
    @TableField("ORG_DEP_CODE")
    private String orgDepCode;

    /**
    * 所属部门名称
    */
    @ApiModelProperty("所属部门名称(50)")
    @TableField("ORG_DEP_NAME")
    private String orgDepName;

    /**
    * 委托编号
    */
    @ApiModelProperty("委托编号(20)")
    @TableField("ATT_NO")
    private String attNo;

    /**
    * 用车需求编号
    */
    @ApiModelProperty("用车需求编号(20)")
    @TableField("OPCAR_NO")
    private String opcarNo;

    /**
    * 上级委托GUID
    */
    @ApiModelProperty("上级委托GUID(50)")
    @TableField("PARENT_ATT_GUID")
    private String parentAttGuid;

    /**
    * 上级委托货物GUID
    */
    @ApiModelProperty("上级委托货物GUID(50)")
    @TableField("PARENT_LOAD_GUID")
    private String parentLoadGuid;

    /**
    * 卸货地联系电话
    */
    @ApiModelProperty("卸货地联系电话(50)")
    @TableField("END_OP_TEL")
    private String endOpTel;

    /**
    * 卸货地作业联系人
    */
    @ApiModelProperty("卸货地作业联系人(100)")
    @TableField("END_OP_LINK")
    private String endOpLink;

    /**
    * 卸货地作业码头
    */
    @ApiModelProperty("卸货地作业码头(50)")
    @TableField("END_OP_WH")
    private String endOpWh;

    /**
    * 提货地联系电话
    */
    @ApiModelProperty("提货地联系电话(50)")
    @TableField("START_OP_TEL")
    private String startOpTel;

    /**
    * 提货地作业联系人
    */
    @ApiModelProperty("提货地作业联系人(100)")
    @TableField("START_OP_LINK")
    private String startOpLink;

    /**
    * 提货地作业码头
    */
    @ApiModelProperty("提货地作业码头(50)")
    @TableField("START_OP_WH")
    private String startOpWh;

    /**
    * 集装箱尺寸
    */
    @ApiModelProperty("集装箱尺寸(50)")
    @TableField("BOX_SIZE")
    private String boxSize;

    /**
    * 集装箱类型
    */
    @ApiModelProperty("集装箱类型(50)")
    @TableField("BOX_TYPE_LY")
    private String boxTypeLy;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * OMS订单号
    */
    @ApiModelProperty("OMS订单号(50)")
    @TableField("OMS_ORDER_NO")
    private String omsOrderNo;

    /**
    * OMS协作任务编号
    */
    @ApiModelProperty("OMS协作任务编号(50)")
    @TableField("OMS_WORK_NO")
    private String omsWorkNo;

    /**
    * OMS订单溯源码
    */
    @ApiModelProperty("OMS订单溯源码(50)")
    @TableField("BIZ_REG_ID")
    private String bizRegId;

    /**
    * CARGO_UDF1
    */
    @ApiModelProperty("CARGO_UDF1(100)")
    @TableField("CARGO_UDF1")
    private String cargoUdf1;

    /**
    * CARGO_UDF2
    */
    @ApiModelProperty("CARGO_UDF2(100)")
    @TableField("CARGO_UDF2")
    private String cargoUdf2;

    /**
    * CARGO_UDF3
    */
    @ApiModelProperty("CARGO_UDF3(100)")
    @TableField("CARGO_UDF3")
    private String cargoUdf3;

    /**
    * CARGO_UDF4
    */
    @ApiModelProperty("CARGO_UDF4(100)")
    @TableField("CARGO_UDF4")
    private String cargoUdf4;

    /**
    * CARGO_UDF5
    */
    @ApiModelProperty("CARGO_UDF5(100)")
    @TableField("CARGO_UDF5")
    private String cargoUdf5;

    /**
    * CARGO_UDF6
    */
    @ApiModelProperty("CARGO_UDF6(100)")
    @TableField("CARGO_UDF6")
    private String cargoUdf6;

    /**
    * CARGO_UDF7
    */
    @ApiModelProperty("CARGO_UDF7(100)")
    @TableField("CARGO_UDF7")
    private String cargoUdf7;

    /**
    * CARGO_UDF8
    */
    @ApiModelProperty("CARGO_UDF8(400)")
    @TableField("CARGO_UDF8")
    private String cargoUdf8;

    /**
    * CARGO_UDF9
    */
    @ApiModelProperty("CARGO_UDF9(400)")
    @TableField("CARGO_UDF9")
    private String cargoUdf9;

    /**
    * CARGO_UDF10
    */
    @ApiModelProperty("CARGO_UDF10(100)")
    @TableField("CARGO_UDF10")
    private String cargoUdf10;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public EtmsOpAttemperCargoEntity() {
        this.setSubClazz(EtmsOpAttemperCargoEntity.class);
    }

    public EtmsOpAttemperCargoEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public EtmsOpAttemperCargoEntity setOpNo(String opNo) {
        this.opNo = opNo;
        this.nodifySetFiled("opNo", opNo);
        return this;
    }

    public String getOpNo() {
        this.nodifyGetFiled("opNo");
        return opNo;
    }

    public EtmsOpAttemperCargoEntity setAttGuid(String attGuid) {
        this.attGuid = attGuid;
        this.nodifySetFiled("attGuid", attGuid);
        return this;
    }

    public String getAttGuid() {
        this.nodifyGetFiled("attGuid");
        return attGuid;
    }

    public EtmsOpAttemperCargoEntity setCarGuid(String carGuid) {
        this.carGuid = carGuid;
        this.nodifySetFiled("carGuid", carGuid);
        return this;
    }

    public String getCarGuid() {
        this.nodifyGetFiled("carGuid");
        return carGuid;
    }

    public EtmsOpAttemperCargoEntity setStartLineGuid(String startLineGuid) {
        this.startLineGuid = startLineGuid;
        this.nodifySetFiled("startLineGuid", startLineGuid);
        return this;
    }

    public String getStartLineGuid() {
        this.nodifyGetFiled("startLineGuid");
        return startLineGuid;
    }

    public EtmsOpAttemperCargoEntity setOpSeq(BigDecimal opSeq) {
        this.opSeq = opSeq;
        this.nodifySetFiled("opSeq", opSeq);
        return this;
    }

    public BigDecimal getOpSeq() {
        this.nodifyGetFiled("opSeq");
        return opSeq;
    }

    public EtmsOpAttemperCargoEntity setOpTask(String opTask) {
        this.opTask = opTask;
        this.nodifySetFiled("opTask", opTask);
        return this;
    }

    public String getOpTask() {
        this.nodifyGetFiled("opTask");
        return opTask;
    }

    public EtmsOpAttemperCargoEntity setCargoType(String cargoType) {
        this.cargoType = cargoType;
        this.nodifySetFiled("cargoType", cargoType);
        return this;
    }

    public String getCargoType() {
        this.nodifyGetFiled("cargoType");
        return cargoType;
    }

    public EtmsOpAttemperCargoEntity setCargo(String cargo) {
        this.cargo = cargo;
        this.nodifySetFiled("cargo", cargo);
        return this;
    }

    public String getCargo() {
        this.nodifyGetFiled("cargo");
        return cargo;
    }

    public EtmsOpAttemperCargoEntity setSku(String sku) {
        this.sku = sku;
        this.nodifySetFiled("sku", sku);
        return this;
    }

    public String getSku() {
        this.nodifyGetFiled("sku");
        return sku;
    }

    public EtmsOpAttemperCargoEntity setDeliveryWay(String deliveryWay) {
        this.deliveryWay = deliveryWay;
        this.nodifySetFiled("deliveryWay", deliveryWay);
        return this;
    }

    public String getDeliveryWay() {
        this.nodifyGetFiled("deliveryWay");
        return deliveryWay;
    }

    public EtmsOpAttemperCargoEntity setBoxNo(String boxNo) {
        this.boxNo = boxNo;
        this.nodifySetFiled("boxNo", boxNo);
        return this;
    }

    public String getBoxNo() {
        this.nodifyGetFiled("boxNo");
        return boxNo;
    }

    public EtmsOpAttemperCargoEntity setBoxType(String boxType) {
        this.boxType = boxType;
        this.nodifySetFiled("boxType", boxType);
        return this;
    }

    public String getBoxType() {
        this.nodifyGetFiled("boxType");
        return boxType;
    }

    public EtmsOpAttemperCargoEntity setSfNo(String sfNo) {
        this.sfNo = sfNo;
        this.nodifySetFiled("sfNo", sfNo);
        return this;
    }

    public String getSfNo() {
        this.nodifyGetFiled("sfNo");
        return sfNo;
    }

    public EtmsOpAttemperCargoEntity setPieces(BigDecimal pieces) {
        this.pieces = pieces;
        this.nodifySetFiled("pieces", pieces);
        return this;
    }

    public BigDecimal getPieces() {
        this.nodifyGetFiled("pieces");
        return pieces;
    }

    public EtmsOpAttemperCargoEntity setPiecesUnit(String piecesUnit) {
        this.piecesUnit = piecesUnit;
        this.nodifySetFiled("piecesUnit", piecesUnit);
        return this;
    }

    public String getPiecesUnit() {
        this.nodifyGetFiled("piecesUnit");
        return piecesUnit;
    }

    public EtmsOpAttemperCargoEntity setWeight(BigDecimal weight) {
        this.weight = weight;
        this.nodifySetFiled("weight", weight);
        return this;
    }

    public BigDecimal getWeight() {
        this.nodifyGetFiled("weight");
        return weight;
    }

    public EtmsOpAttemperCargoEntity setWeightUnit(String weightUnit) {
        this.weightUnit = weightUnit;
        this.nodifySetFiled("weightUnit", weightUnit);
        return this;
    }

    public String getWeightUnit() {
        this.nodifyGetFiled("weightUnit");
        return weightUnit;
    }

    public EtmsOpAttemperCargoEntity setVolume(BigDecimal volume) {
        this.volume = volume;
        this.nodifySetFiled("volume", volume);
        return this;
    }

    public BigDecimal getVolume() {
        this.nodifyGetFiled("volume");
        return volume;
    }

    public EtmsOpAttemperCargoEntity setVolumeUnit(String volumeUnit) {
        this.volumeUnit = volumeUnit;
        this.nodifySetFiled("volumeUnit", volumeUnit);
        return this;
    }

    public String getVolumeUnit() {
        this.nodifyGetFiled("volumeUnit");
        return volumeUnit;
    }

    public EtmsOpAttemperCargoEntity setMemo(String memo) {
        this.memo = memo;
        this.nodifySetFiled("memo", memo);
        return this;
    }

    public String getMemo() {
        this.nodifyGetFiled("memo");
        return memo;
    }

    public EtmsOpAttemperCargoEntity setCreateCompany(String createCompany) {
        this.createCompany = createCompany;
        this.nodifySetFiled("createCompany", createCompany);
        return this;
    }

    public String getCreateCompany() {
        this.nodifyGetFiled("createCompany");
        return createCompany;
    }

    public EtmsOpAttemperCargoEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public EtmsOpAttemperCargoEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public EtmsOpAttemperCargoEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public EtmsOpAttemperCargoEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public EtmsOpAttemperCargoEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public EtmsOpAttemperCargoEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public EtmsOpAttemperCargoEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public EtmsOpAttemperCargoEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public EtmsOpAttemperCargoEntity setEndLineGuid(String endLineGuid) {
        this.endLineGuid = endLineGuid;
        this.nodifySetFiled("endLineGuid", endLineGuid);
        return this;
    }

    public String getEndLineGuid() {
        this.nodifyGetFiled("endLineGuid");
        return endLineGuid;
    }

    public EtmsOpAttemperCargoEntity setStartRequestDate(Date startRequestDate) {
        this.startRequestDate = startRequestDate;
        this.nodifySetFiled("startRequestDate", startRequestDate);
        return this;
    }

    public Date getStartRequestDate() {
        this.nodifyGetFiled("startRequestDate");
        return startRequestDate;
    }

    public EtmsOpAttemperCargoEntity setStartRequestDateStart(Date startRequestDateStart) {
        this.startRequestDateStart = startRequestDateStart;
        this.nodifySetFiled("startRequestDateStart", startRequestDateStart);
        return this;
    }

    public Date getStartRequestDateStart() {
        this.nodifyGetFiled("startRequestDateStart");
        return startRequestDateStart;
    }

    public EtmsOpAttemperCargoEntity setStartRequestDateEnd(Date startRequestDateEnd) {
        this.startRequestDateEnd = startRequestDateEnd;
        this.nodifySetFiled("startRequestDateEnd", startRequestDateEnd);
        return this;
    }

    public Date getStartRequestDateEnd() {
        this.nodifyGetFiled("startRequestDateEnd");
        return startRequestDateEnd;
    }
    public EtmsOpAttemperCargoEntity setStartOpRequest(String startOpRequest) {
        this.startOpRequest = startOpRequest;
        this.nodifySetFiled("startOpRequest", startOpRequest);
        return this;
    }

    public String getStartOpRequest() {
        this.nodifyGetFiled("startOpRequest");
        return startOpRequest;
    }

    public EtmsOpAttemperCargoEntity setEndRequestDate(Date endRequestDate) {
        this.endRequestDate = endRequestDate;
        this.nodifySetFiled("endRequestDate", endRequestDate);
        return this;
    }

    public Date getEndRequestDate() {
        this.nodifyGetFiled("endRequestDate");
        return endRequestDate;
    }

    public EtmsOpAttemperCargoEntity setEndRequestDateStart(Date endRequestDateStart) {
        this.endRequestDateStart = endRequestDateStart;
        this.nodifySetFiled("endRequestDateStart", endRequestDateStart);
        return this;
    }

    public Date getEndRequestDateStart() {
        this.nodifyGetFiled("endRequestDateStart");
        return endRequestDateStart;
    }

    public EtmsOpAttemperCargoEntity setEndRequestDateEnd(Date endRequestDateEnd) {
        this.endRequestDateEnd = endRequestDateEnd;
        this.nodifySetFiled("endRequestDateEnd", endRequestDateEnd);
        return this;
    }

    public Date getEndRequestDateEnd() {
        this.nodifyGetFiled("endRequestDateEnd");
        return endRequestDateEnd;
    }
    public EtmsOpAttemperCargoEntity setEndOpRequest(String endOpRequest) {
        this.endOpRequest = endOpRequest;
        this.nodifySetFiled("endOpRequest", endOpRequest);
        return this;
    }

    public String getEndOpRequest() {
        this.nodifyGetFiled("endOpRequest");
        return endOpRequest;
    }

    public EtmsOpAttemperCargoEntity setOpcargoNo(String opcargoNo) {
        this.opcargoNo = opcargoNo;
        this.nodifySetFiled("opcargoNo", opcargoNo);
        return this;
    }

    public String getOpcargoNo() {
        this.nodifyGetFiled("opcargoNo");
        return opcargoNo;
    }

    public EtmsOpAttemperCargoEntity setSenderCode(String senderCode) {
        this.senderCode = senderCode;
        this.nodifySetFiled("senderCode", senderCode);
        return this;
    }

    public String getSenderCode() {
        this.nodifyGetFiled("senderCode");
        return senderCode;
    }

    public EtmsOpAttemperCargoEntity setConsigneeCode(String consigneeCode) {
        this.consigneeCode = consigneeCode;
        this.nodifySetFiled("consigneeCode", consigneeCode);
        return this;
    }

    public String getConsigneeCode() {
        this.nodifyGetFiled("consigneeCode");
        return consigneeCode;
    }

    public EtmsOpAttemperCargoEntity setOrgDepId(String orgDepId) {
        this.orgDepId = orgDepId;
        this.nodifySetFiled("orgDepId", orgDepId);
        return this;
    }

    public String getOrgDepId() {
        this.nodifyGetFiled("orgDepId");
        return orgDepId;
    }

    public EtmsOpAttemperCargoEntity setOrgDepCode(String orgDepCode) {
        this.orgDepCode = orgDepCode;
        this.nodifySetFiled("orgDepCode", orgDepCode);
        return this;
    }

    public String getOrgDepCode() {
        this.nodifyGetFiled("orgDepCode");
        return orgDepCode;
    }

    public EtmsOpAttemperCargoEntity setOrgDepName(String orgDepName) {
        this.orgDepName = orgDepName;
        this.nodifySetFiled("orgDepName", orgDepName);
        return this;
    }

    public String getOrgDepName() {
        this.nodifyGetFiled("orgDepName");
        return orgDepName;
    }

    public EtmsOpAttemperCargoEntity setAttNo(String attNo) {
        this.attNo = attNo;
        this.nodifySetFiled("attNo", attNo);
        return this;
    }

    public String getAttNo() {
        this.nodifyGetFiled("attNo");
        return attNo;
    }

    public EtmsOpAttemperCargoEntity setOpcarNo(String opcarNo) {
        this.opcarNo = opcarNo;
        this.nodifySetFiled("opcarNo", opcarNo);
        return this;
    }

    public String getOpcarNo() {
        this.nodifyGetFiled("opcarNo");
        return opcarNo;
    }

    public EtmsOpAttemperCargoEntity setParentAttGuid(String parentAttGuid) {
        this.parentAttGuid = parentAttGuid;
        this.nodifySetFiled("parentAttGuid", parentAttGuid);
        return this;
    }

    public String getParentAttGuid() {
        this.nodifyGetFiled("parentAttGuid");
        return parentAttGuid;
    }

    public EtmsOpAttemperCargoEntity setParentLoadGuid(String parentLoadGuid) {
        this.parentLoadGuid = parentLoadGuid;
        this.nodifySetFiled("parentLoadGuid", parentLoadGuid);
        return this;
    }

    public String getParentLoadGuid() {
        this.nodifyGetFiled("parentLoadGuid");
        return parentLoadGuid;
    }

    public EtmsOpAttemperCargoEntity setEndOpTel(String endOpTel) {
        this.endOpTel = endOpTel;
        this.nodifySetFiled("endOpTel", endOpTel);
        return this;
    }

    public String getEndOpTel() {
        this.nodifyGetFiled("endOpTel");
        return endOpTel;
    }

    public EtmsOpAttemperCargoEntity setEndOpLink(String endOpLink) {
        this.endOpLink = endOpLink;
        this.nodifySetFiled("endOpLink", endOpLink);
        return this;
    }

    public String getEndOpLink() {
        this.nodifyGetFiled("endOpLink");
        return endOpLink;
    }

    public EtmsOpAttemperCargoEntity setEndOpWh(String endOpWh) {
        this.endOpWh = endOpWh;
        this.nodifySetFiled("endOpWh", endOpWh);
        return this;
    }

    public String getEndOpWh() {
        this.nodifyGetFiled("endOpWh");
        return endOpWh;
    }

    public EtmsOpAttemperCargoEntity setStartOpTel(String startOpTel) {
        this.startOpTel = startOpTel;
        this.nodifySetFiled("startOpTel", startOpTel);
        return this;
    }

    public String getStartOpTel() {
        this.nodifyGetFiled("startOpTel");
        return startOpTel;
    }

    public EtmsOpAttemperCargoEntity setStartOpLink(String startOpLink) {
        this.startOpLink = startOpLink;
        this.nodifySetFiled("startOpLink", startOpLink);
        return this;
    }

    public String getStartOpLink() {
        this.nodifyGetFiled("startOpLink");
        return startOpLink;
    }

    public EtmsOpAttemperCargoEntity setStartOpWh(String startOpWh) {
        this.startOpWh = startOpWh;
        this.nodifySetFiled("startOpWh", startOpWh);
        return this;
    }

    public String getStartOpWh() {
        this.nodifyGetFiled("startOpWh");
        return startOpWh;
    }

    public EtmsOpAttemperCargoEntity setBoxSize(String boxSize) {
        this.boxSize = boxSize;
        this.nodifySetFiled("boxSize", boxSize);
        return this;
    }

    public String getBoxSize() {
        this.nodifyGetFiled("boxSize");
        return boxSize;
    }

    public EtmsOpAttemperCargoEntity setBoxTypeLy(String boxTypeLy) {
        this.boxTypeLy = boxTypeLy;
        this.nodifySetFiled("boxTypeLy", boxTypeLy);
        return this;
    }

    public String getBoxTypeLy() {
        this.nodifyGetFiled("boxTypeLy");
        return boxTypeLy;
    }

    public EtmsOpAttemperCargoEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public EtmsOpAttemperCargoEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public EtmsOpAttemperCargoEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public EtmsOpAttemperCargoEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public EtmsOpAttemperCargoEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public EtmsOpAttemperCargoEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public EtmsOpAttemperCargoEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public EtmsOpAttemperCargoEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public EtmsOpAttemperCargoEntity setOmsOrderNo(String omsOrderNo) {
        this.omsOrderNo = omsOrderNo;
        this.nodifySetFiled("omsOrderNo", omsOrderNo);
        return this;
    }

    public String getOmsOrderNo() {
        this.nodifyGetFiled("omsOrderNo");
        return omsOrderNo;
    }

    public EtmsOpAttemperCargoEntity setOmsWorkNo(String omsWorkNo) {
        this.omsWorkNo = omsWorkNo;
        this.nodifySetFiled("omsWorkNo", omsWorkNo);
        return this;
    }

    public String getOmsWorkNo() {
        this.nodifyGetFiled("omsWorkNo");
        return omsWorkNo;
    }

    public EtmsOpAttemperCargoEntity setBizRegId(String bizRegId) {
        this.bizRegId = bizRegId;
        this.nodifySetFiled("bizRegId", bizRegId);
        return this;
    }

    public String getBizRegId() {
        this.nodifyGetFiled("bizRegId");
        return bizRegId;
    }

    public EtmsOpAttemperCargoEntity setCargoUdf1(String cargoUdf1) {
        this.cargoUdf1 = cargoUdf1;
        this.nodifySetFiled("cargoUdf1", cargoUdf1);
        return this;
    }

    public String getCargoUdf1() {
        this.nodifyGetFiled("cargoUdf1");
        return cargoUdf1;
    }

    public EtmsOpAttemperCargoEntity setCargoUdf2(String cargoUdf2) {
        this.cargoUdf2 = cargoUdf2;
        this.nodifySetFiled("cargoUdf2", cargoUdf2);
        return this;
    }

    public String getCargoUdf2() {
        this.nodifyGetFiled("cargoUdf2");
        return cargoUdf2;
    }

    public EtmsOpAttemperCargoEntity setCargoUdf3(String cargoUdf3) {
        this.cargoUdf3 = cargoUdf3;
        this.nodifySetFiled("cargoUdf3", cargoUdf3);
        return this;
    }

    public String getCargoUdf3() {
        this.nodifyGetFiled("cargoUdf3");
        return cargoUdf3;
    }

    public EtmsOpAttemperCargoEntity setCargoUdf4(String cargoUdf4) {
        this.cargoUdf4 = cargoUdf4;
        this.nodifySetFiled("cargoUdf4", cargoUdf4);
        return this;
    }

    public String getCargoUdf4() {
        this.nodifyGetFiled("cargoUdf4");
        return cargoUdf4;
    }

    public EtmsOpAttemperCargoEntity setCargoUdf5(String cargoUdf5) {
        this.cargoUdf5 = cargoUdf5;
        this.nodifySetFiled("cargoUdf5", cargoUdf5);
        return this;
    }

    public String getCargoUdf5() {
        this.nodifyGetFiled("cargoUdf5");
        return cargoUdf5;
    }

    public EtmsOpAttemperCargoEntity setCargoUdf6(String cargoUdf6) {
        this.cargoUdf6 = cargoUdf6;
        this.nodifySetFiled("cargoUdf6", cargoUdf6);
        return this;
    }

    public String getCargoUdf6() {
        this.nodifyGetFiled("cargoUdf6");
        return cargoUdf6;
    }

    public EtmsOpAttemperCargoEntity setCargoUdf7(String cargoUdf7) {
        this.cargoUdf7 = cargoUdf7;
        this.nodifySetFiled("cargoUdf7", cargoUdf7);
        return this;
    }

    public String getCargoUdf7() {
        this.nodifyGetFiled("cargoUdf7");
        return cargoUdf7;
    }

    public EtmsOpAttemperCargoEntity setCargoUdf8(String cargoUdf8) {
        this.cargoUdf8 = cargoUdf8;
        this.nodifySetFiled("cargoUdf8", cargoUdf8);
        return this;
    }

    public String getCargoUdf8() {
        this.nodifyGetFiled("cargoUdf8");
        return cargoUdf8;
    }

    public EtmsOpAttemperCargoEntity setCargoUdf9(String cargoUdf9) {
        this.cargoUdf9 = cargoUdf9;
        this.nodifySetFiled("cargoUdf9", cargoUdf9);
        return this;
    }

    public String getCargoUdf9() {
        this.nodifyGetFiled("cargoUdf9");
        return cargoUdf9;
    }

    public EtmsOpAttemperCargoEntity setCargoUdf10(String cargoUdf10) {
        this.cargoUdf10 = cargoUdf10;
        this.nodifySetFiled("cargoUdf10", cargoUdf10);
        return this;
    }

    public String getCargoUdf10() {
        this.nodifyGetFiled("cargoUdf10");
        return cargoUdf10;
    }

}
