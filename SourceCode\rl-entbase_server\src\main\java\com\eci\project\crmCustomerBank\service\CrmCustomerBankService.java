package com.eci.project.crmCustomerBank.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.crmCustomerBank.dao.CrmCustomerBankDao;
import com.eci.project.crmCustomerBank.entity.CrmCustomerBankEntity;
import com.eci.project.crmCustomerBank.validate.CrmCustomerBankVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 供应商开户行信息Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-05-13
*/
@Service
@Slf4j
public class CrmCustomerBankService implements EciBaseService<CrmCustomerBankEntity> {

    @Autowired
    private CrmCustomerBankDao crmCustomerBankDao;

    @Autowired
    private CrmCustomerBankVal crmCustomerBankVal;


    @Override
    public TgPageInfo queryPageList(CrmCustomerBankEntity entity) {
        EciQuery<CrmCustomerBankEntity> eciQuery = EciQuery.buildQuery(entity);
        List<CrmCustomerBankEntity> entities = crmCustomerBankDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public CrmCustomerBankEntity save(CrmCustomerBankEntity entity) {
        // 返回实体对象
        CrmCustomerBankEntity crmCustomerBankEntity = null;
        crmCustomerBankVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            crmCustomerBankEntity = crmCustomerBankDao.insertOne(entity);

        }else{

            crmCustomerBankEntity = crmCustomerBankDao.updateByEntityId(entity);

        }
        return crmCustomerBankEntity;
    }

    @Override
    public List<CrmCustomerBankEntity> selectList(CrmCustomerBankEntity entity) {
        return crmCustomerBankDao.selectList(entity);
    }

    @Override
    public CrmCustomerBankEntity selectOneById(Serializable id) {
        return crmCustomerBankDao.selectById(id);
    }


    @Override
    public void insertBatch(List<CrmCustomerBankEntity> list) {
        crmCustomerBankDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return crmCustomerBankDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return crmCustomerBankDao.deleteById(id);
    }

}