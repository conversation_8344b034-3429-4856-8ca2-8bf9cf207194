<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.dhlWorkBasic.dao.DhlWorkBasicDao">
    <resultMap type="DhlWorkBasicEntity" id="DhlWorkBasicResult">
        <result property="guid" column="GUID"/>
        <result property="toNo" column="TO_NO"/>
        <result property="transportationType" column="TRANSPORTATION_TYPE"/>
        <result property="transportMode" column="TRANSPORT_MODE"/>
        <result property="productType" column="PRODUCT_TYPE"/>
        <result property="toCreator" column="TO_CREATOR"/>
        <result property="issueTime" column="ISSUE_TIME"/>
        <result property="tocreatorContact" column="TOCREATOR_CONTACT"/>
        <result property="toStatus" column="TO_STATUS"/>
        <result property="truckType" column="TRUCK_TYPE"/>
        <result property="truckSize" column="TRUCK_SIZE"/>
        <result property="customsDeclarationType" column="CUSTOMS_DECLARATION_TYPE"/>
        <result property="cargoType" column="CARGO_TYPE"/>
        <result property="actualCommodity" column="ACTUAL_COMMODITY"/>
        <result property="totalQuantity" column="TOTAL_QUANTITY"/>
        <result property="quantityUnit" column="QUANTITY_UNIT"/>
        <result property="totalWeight" column="TOTAL_WEIGHT"/>
        <result property="totalVolume" column="TOTAL_VOLUME"/>
        <result property="remarkLp" column="REMARK_LP"/>
        <result property="remarksTo" column="REMARKS_TO"/>
        <result property="containerNo" column="CONTAINER_NO"/>
        <result property="containerTareWeight" column="CONTAINER_TARE_WEIGHT"/>
        <result property="soNo" column="SO_NO"/>
        <result property="sealNo" column="SEAL_NO"/>
        <result property="pickupContainerYard" column="PICKUP_CONTAINER_YARD"/>
        <result property="returnContainerYard" column="RETURN_CONTAINER_YARD"/>
        <result property="loadingPlace" column="LOADING_PLACE"/>
        <result property="emptyContainerCity" column="EMPTY_CONTAINER_CITY"/>
        <result property="carrier" column="CARRIER"/>
        <result property="vessel" column="VESSEL"/>
        <result property="voyage" column="VOYAGE"/>
        <result property="containerType" column="CONTAINER_TYPE"/>
        <result property="containerQty" column="CONTAINER_QTY"/>
        <result property="expectedArriveWhsTime" column="EXPECTED_ARRIVE_WHS_TIME"/>
        <result property="expectedArriveFactoryTime" column="EXPECTED_ARRIVE_FACTORY_TIME"/>
        <result property="currency" column="CURRENCY"/>
        <result property="taxRate" column="TAX_RATE"/>
        <result property="referenceFreight" column="REFERENCE_FREIGHT"/>
        <result property="totalCost" column="TOTAL_COST"/>
        <result property="taxReferenceFreight" column="TAX_REFERENCE_FREIGHT"/>
        <result property="taxTotalCost" column="TAX_TOTAL_COST"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="workNo" column="WORK_NO"/>
        <result property="orderNo" column="ORDER_NO"/>
        <result property="preNo" column="PRE_NO"/>
        <result property="xzwtNo" column="XZWT_NO"/>
        <result property="bizRegId" column="BIZ_REG_ID"/>
        <result property="truckWeight" column="TRUCK_WEIGHT"/>
        <result property="iSeeTruckPlateNo" column="I_SEE_TRUCK_PLATE_NO"/>
        <result property="wmsTruckPlateNo" column="WMS_TRUCK_PLATE_NO"/>
        <result property="vehicleEntryTime" column="VEHICLE_ENTRY_TIME"/>
        <result property="standingTime" column="STANDING_TIME"/>
        <result property="loadingStartingTime" column="LOADING_STARTING_TIME"/>
        <result property="loadingEndTime" column="LOADING_END_TIME"/>
        <result property="vehicleExitTime" column="VEHICLE_EXIT_TIME"/>
        <result property="actualFreight" column="ACTUAL_FREIGHT"/>
        <result property="taxactualFreight" column="TAXACTUAL_FREIGHT"/>
    </resultMap>

    <sql id="selectDhlWorkBasicEntityVo">
        select
            GUID,
            TO_NO,
            TRANSPORTATION_TYPE,
            TRANSPORT_MODE,
            PRODUCT_TYPE,
            TO_CREATOR,
            ISSUE_TIME,
            TOCREATOR_CONTACT,
            TO_STATUS,
            TRUCK_TYPE,
            TRUCK_SIZE,
            CUSTOMS_DECLARATION_TYPE,
            CARGO_TYPE,
            ACTUAL_COMMODITY,
            TOTAL_QUANTITY,
            QUANTITY_UNIT,
            TOTAL_WEIGHT,
            TOTAL_VOLUME,
            REMARK_LP,
            REMARKS_TO,
            CONTAINER_NO,
            CONTAINER_TARE_WEIGHT,
            SO_NO,
            SEAL_NO,
            PICKUP_CONTAINER_YARD,
            RETURN_CONTAINER_YARD,
            LOADING_PLACE,
            EMPTY_CONTAINER_CITY,
            CARRIER,
            VESSEL,
            VOYAGE,
            CONTAINER_TYPE,
            CONTAINER_QTY,
            EXPECTED_ARRIVE_WHS_TIME,
            EXPECTED_ARRIVE_FACTORY_TIME,
            CURRENCY,
            TAX_RATE,
            REFERENCE_FREIGHT,
            TOTAL_COST,
            TAX_REFERENCE_FREIGHT,
            TAX_TOTAL_COST,
            CREATE_USER,
            CREATE_USER_NAME,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_USER_NAME,
            UPDATE_DATE,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            WORK_NO,
            ORDER_NO,
            PRE_NO,
            XZWT_NO,
            BIZ_REG_ID,
            TRUCK_WEIGHT,
            I_SEE_TRUCK_PLATE_NO,
            WMS_TRUCK_PLATE_NO,
            VEHICLE_ENTRY_TIME,
            STANDING_TIME,
            LOADING_STARTING_TIME,
            LOADING_END_TIME,
            VEHICLE_EXIT_TIME,
            ACTUAL_FREIGHT,
            TAXACTUAL_FREIGHT
        from DHL_WORK_BASIC
    </sql>
</mapper>