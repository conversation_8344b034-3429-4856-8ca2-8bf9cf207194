package com.eci.project.crmCustomerHzTrade.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.crmCustomerHzTrade.entity.CrmCustomerHzTradeEntity;


/**
* 业务伙伴货主贸易关系Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-05-20
*/
public interface CrmCustomerHzTradeDao extends EciBaseDao<CrmCustomerHzTradeEntity> {

}