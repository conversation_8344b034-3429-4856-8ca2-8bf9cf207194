package com.eci.project.fzgjCrmEnterpriseService.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.fzgjCrmEnterpriseService.entity.FzgjCrmEnterpriseServiceEntity;


/**
* 注册企业服务Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-03-13
*/
public interface FzgjCrmEnterpriseServiceDao extends EciBaseDao<FzgjCrmEnterpriseServiceEntity> {

}