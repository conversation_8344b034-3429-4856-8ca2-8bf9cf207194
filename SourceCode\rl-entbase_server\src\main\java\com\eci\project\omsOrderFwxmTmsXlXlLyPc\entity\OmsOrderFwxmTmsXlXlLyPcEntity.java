package com.eci.project.omsOrderFwxmTmsXlXlLyPc.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 委托内容-程运序列-陆运-拼车对象 OMS_ORDER_FWXM_TMS_XL_XL_LY_PC
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-06-05
*/
@ApiModel("委托内容-程运序列-陆运-拼车")
@TableName("OMS_ORDER_FWXM_TMS_XL_XL_LY_PC")
@FieldNameConstants
public class OmsOrderFwxmTmsXlXlLyPcEntity extends EciBaseEntity{
    /**
    * GUID
    */
    @ApiModelProperty("GUID(36)")
    @TableId("GUID")
    private String guid;

    /**
    * 委托单编号
    */
    @ApiModelProperty("委托单编号(36)")
    @TableField("PRE_NO")
    private String preNo;

    /**
    * 订单号
    */
    @ApiModelProperty("订单号(36)")
    @TableField("ORDER_NO")
    private String orderNo;

    /**
    * 需求唯一编号
    */
    @ApiModelProperty("需求唯一编号(36)")
    @TableField("TMS_NO")
    private String tmsNo;

    /**
    * 结算线路唯一编号
    */
    @ApiModelProperty("结算线路唯一编号(36)")
    @TableField("LINE_NO")
    private String lineNo;

    /**
    * 明细唯一编号
    */
    @ApiModelProperty("明细唯一编号(36)")
    @TableField("LY_NO")
    private String lyNo;

    /**
    * 程运序列唯一编号
    */
    @ApiModelProperty("程运序列唯一编号(36)")
    @TableField("SEQ_NO")
    private String seqNo;

    /**
    * 主拼程运序列唯一编号
    */
    @ApiModelProperty("主拼程运序列唯一编号(36)")
    @TableField("ZP_SEQ_NO")
    private String zpSeqNo;

    /**
    * 被拼程运序列唯一编号
    */
    @ApiModelProperty("被拼程运序列唯一编号(36)")
    @TableField("BP_SEQ_NO")
    private String bpSeqNo;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 最后修改日期
    */
    @ApiModelProperty("最后修改日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("最后修改日期开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("最后修改日期结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(200)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public OmsOrderFwxmTmsXlXlLyPcEntity() {
        this.setSubClazz(OmsOrderFwxmTmsXlXlLyPcEntity.class);
    }

    public OmsOrderFwxmTmsXlXlLyPcEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public OmsOrderFwxmTmsXlXlLyPcEntity setPreNo(String preNo) {
        this.preNo = preNo;
        this.nodifySetFiled("preNo", preNo);
        return this;
    }

    public String getPreNo() {
        this.nodifyGetFiled("preNo");
        return preNo;
    }

    public OmsOrderFwxmTmsXlXlLyPcEntity setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        this.nodifySetFiled("orderNo", orderNo);
        return this;
    }

    public String getOrderNo() {
        this.nodifyGetFiled("orderNo");
        return orderNo;
    }

    public OmsOrderFwxmTmsXlXlLyPcEntity setTmsNo(String tmsNo) {
        this.tmsNo = tmsNo;
        this.nodifySetFiled("tmsNo", tmsNo);
        return this;
    }

    public String getTmsNo() {
        this.nodifyGetFiled("tmsNo");
        return tmsNo;
    }

    public OmsOrderFwxmTmsXlXlLyPcEntity setLineNo(String lineNo) {
        this.lineNo = lineNo;
        this.nodifySetFiled("lineNo", lineNo);
        return this;
    }

    public String getLineNo() {
        this.nodifyGetFiled("lineNo");
        return lineNo;
    }

    public OmsOrderFwxmTmsXlXlLyPcEntity setLyNo(String lyNo) {
        this.lyNo = lyNo;
        this.nodifySetFiled("lyNo", lyNo);
        return this;
    }

    public String getLyNo() {
        this.nodifyGetFiled("lyNo");
        return lyNo;
    }

    public OmsOrderFwxmTmsXlXlLyPcEntity setSeqNo(String seqNo) {
        this.seqNo = seqNo;
        this.nodifySetFiled("seqNo", seqNo);
        return this;
    }

    public String getSeqNo() {
        this.nodifyGetFiled("seqNo");
        return seqNo;
    }

    public OmsOrderFwxmTmsXlXlLyPcEntity setZpSeqNo(String zpSeqNo) {
        this.zpSeqNo = zpSeqNo;
        this.nodifySetFiled("zpSeqNo", zpSeqNo);
        return this;
    }

    public String getZpSeqNo() {
        this.nodifyGetFiled("zpSeqNo");
        return zpSeqNo;
    }

    public OmsOrderFwxmTmsXlXlLyPcEntity setBpSeqNo(String bpSeqNo) {
        this.bpSeqNo = bpSeqNo;
        this.nodifySetFiled("bpSeqNo", bpSeqNo);
        return this;
    }

    public String getBpSeqNo() {
        this.nodifyGetFiled("bpSeqNo");
        return bpSeqNo;
    }

    public OmsOrderFwxmTmsXlXlLyPcEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public OmsOrderFwxmTmsXlXlLyPcEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public OmsOrderFwxmTmsXlXlLyPcEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public OmsOrderFwxmTmsXlXlLyPcEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public OmsOrderFwxmTmsXlXlLyPcEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public OmsOrderFwxmTmsXlXlLyPcEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public OmsOrderFwxmTmsXlXlLyPcEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public OmsOrderFwxmTmsXlXlLyPcEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public OmsOrderFwxmTmsXlXlLyPcEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public OmsOrderFwxmTmsXlXlLyPcEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public OmsOrderFwxmTmsXlXlLyPcEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public OmsOrderFwxmTmsXlXlLyPcEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public OmsOrderFwxmTmsXlXlLyPcEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public OmsOrderFwxmTmsXlXlLyPcEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public OmsOrderFwxmTmsXlXlLyPcEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public OmsOrderFwxmTmsXlXlLyPcEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

}
