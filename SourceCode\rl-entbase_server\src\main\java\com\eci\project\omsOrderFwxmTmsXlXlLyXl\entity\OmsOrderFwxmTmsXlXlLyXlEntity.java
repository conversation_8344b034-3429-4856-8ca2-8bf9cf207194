package com.eci.project.omsOrderFwxmTmsXlXlLyXl.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.common.DictField;
import com.eci.common.ZsrBaseEntity;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 委托内容-程运序列-陆运-线路对象 OMS_ORDER_FWXM_TMS_XL_XL_LY_XL
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-06-04
*/
@ApiModel("委托内容-程运序列-陆运-线路")
@TableName("OMS_ORDER_FWXM_TMS_XL_XL_LY_XL")
@FieldNameConstants
public class OmsOrderFwxmTmsXlXlLyXlEntity extends ZsrBaseEntity {
    /**
    * GUID
    */
    @ApiModelProperty("GUID(36)")
    @TableId("GUID")
    private String guid;

    /**
    * 委托单编号
    */
    @ApiModelProperty("委托单编号(36)")
    @TableField("PRE_NO")
    private String preNo;

    /**
    * 订单号
    */
    @ApiModelProperty("订单号(36)")
    @TableField("ORDER_NO")
    private String orderNo;

    /**
    * 需求唯一编号
    */
    @ApiModelProperty("需求唯一编号(36)")
    @TableField("TMS_NO")
    private String tmsNo;

    /**
    * 结算线路唯一编号
    */
    @ApiModelProperty("结算线路唯一编号(36)")
    @TableField("LINE_NO")
    private String lineNo;

    /**
    * 明细唯一编号
    */
    @ApiModelProperty("明细唯一编号(36)")
    @TableField("LY_NO")
    private String lyNo;

    /**
    * 程运序列唯一编号
    */
    @ApiModelProperty("程运序列唯一编号(36)")
    @TableField("SEQ_NO")
    private String seqNo;

    /**
    * 节点序号
    */
    @ApiModelProperty("节点序号(22)")
    @TableField("XL_SEQ")
    private Integer xlSeq;

    /**
    * 节点类型(起运地/途径地/目的地)
    */
    @ApiModelProperty("节点类型(起运地/途径地/目的地)(20)")
    @TableField("XL_TYPE")
    private String xlType;

    /**
    * 国家
    */
    @ApiModelProperty("国家(20)")
    @TableField("COUNTY")
    private String county;

    /**
    * 省
    */
    @ApiModelProperty("省(20)")
    @TableField("PROVINCE")
    private String province;

    /**
    * 市
    */
    @ApiModelProperty("市(20)")
    @TableField("CITY")
    private String city;

    /**
    * 区县
    */
    @ApiModelProperty("区县(20)")
    @TableField("REGION")
    private String region;

    /**
    * 乡镇
    */
    @ApiModelProperty("乡镇(20)")
    @TableField("TOWN")
    @DictField(queryKey = "OMS_BD_AREA",params = {"companyCode"})
    private String town;

    /**
    * 详细地址
    */
    @ApiModelProperty("详细地址(500)")
    @TableField("ADDRESS")
    private String address;

    /**
    * 交接方
    */
    @ApiModelProperty("交接方(50)")
    @TableField("JJF")
    @DictField(queryKey = "CRM_CUSTOMER_SFHF_DFT_ADDR",params = {"companyCode"})
    private String jjf;

    /**
    * 联系人
    */
    @ApiModelProperty("联系人(100)")
    @TableField("LINK_MAN")
    private String linkMan;

    /**
    * 联系电话
    */
    @ApiModelProperty("联系电话(50)")
    @TableField("LINK_TEL")
    private String linkTel;

    /**
    * 要求交接时间
    */
    @ApiModelProperty("要求交接时间(7)")
    @TableField("JJ_DATE")
    private Date jjDate;

    @ApiModelProperty("要求交接时间开始")
    @TableField(exist=false)
    private Date jjDateStart;

    @ApiModelProperty("要求交接时间结束")
    @TableField(exist=false)
    private Date jjDateEnd;

    /**
    * 是否禁止带货进厂
    */
    @ApiModelProperty("是否禁止带货进厂(1)")
    @TableField("IS_DHJC")
    private String isDhjc;

    /**
    * 是否需要安装拆卸
    */
    @ApiModelProperty("是否需要安装拆卸(1)")
    @TableField("IS_AZCX")
    private String isAzcx;

    /**
    * 是否需要提货单证
    */
    @ApiModelProperty("是否需要提货单证(1)")
    @TableField("IS_THDZ")
    private String isThdz;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 最后修改日期
    */
    @ApiModelProperty("最后修改日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("最后修改日期开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("最后修改日期结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(200)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 注意事项
    */
    @ApiModelProperty("注意事项(50)")
    @TableField("NOTE")
    private String note;

    /**
    * 其他注意事项
    */
    @ApiModelProperty("其他注意事项(200)")
    @TableField("MEMO")
    private String memo;

    /**
    * 线路类型(T提货X卸货)
    */
    @ApiModelProperty("线路类型(T提货X卸货)(1)")
    @TableField("XL_MODE")
    private String xlMode;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity() {
        this.setSubClazz(OmsOrderFwxmTmsXlXlLyXlEntity.class);
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity setPreNo(String preNo) {
        this.preNo = preNo;
        this.nodifySetFiled("preNo", preNo);
        return this;
    }

    public String getPreNo() {
        this.nodifyGetFiled("preNo");
        return preNo;
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        this.nodifySetFiled("orderNo", orderNo);
        return this;
    }

    public String getOrderNo() {
        this.nodifyGetFiled("orderNo");
        return orderNo;
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity setTmsNo(String tmsNo) {
        this.tmsNo = tmsNo;
        this.nodifySetFiled("tmsNo", tmsNo);
        return this;
    }

    public String getTmsNo() {
        this.nodifyGetFiled("tmsNo");
        return tmsNo;
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity setLineNo(String lineNo) {
        this.lineNo = lineNo;
        this.nodifySetFiled("lineNo", lineNo);
        return this;
    }

    public String getLineNo() {
        this.nodifyGetFiled("lineNo");
        return lineNo;
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity setLyNo(String lyNo) {
        this.lyNo = lyNo;
        this.nodifySetFiled("lyNo", lyNo);
        return this;
    }

    public String getLyNo() {
        this.nodifyGetFiled("lyNo");
        return lyNo;
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity setSeqNo(String seqNo) {
        this.seqNo = seqNo;
        this.nodifySetFiled("seqNo", seqNo);
        return this;
    }

    public String getSeqNo() {
        this.nodifyGetFiled("seqNo");
        return seqNo;
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity setXlSeq(Integer xlSeq) {
        this.xlSeq = xlSeq;
        this.nodifySetFiled("xlSeq", xlSeq);
        return this;
    }

    public Integer getXlSeq() {
        this.nodifyGetFiled("xlSeq");
        return xlSeq;
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity setXlType(String xlType) {
        this.xlType = xlType;
        this.nodifySetFiled("xlType", xlType);
        return this;
    }

    public String getXlType() {
        this.nodifyGetFiled("xlType");
        return xlType;
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity setCounty(String county) {
        this.county = county;
        this.nodifySetFiled("county", county);
        return this;
    }

    public String getCounty() {
        this.nodifyGetFiled("county");
        return county;
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity setProvince(String province) {
        this.province = province;
        this.nodifySetFiled("province", province);
        return this;
    }

    public String getProvince() {
        this.nodifyGetFiled("province");
        return province;
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity setCity(String city) {
        this.city = city;
        this.nodifySetFiled("city", city);
        return this;
    }

    public String getCity() {
        this.nodifyGetFiled("city");
        return city;
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity setRegion(String region) {
        this.region = region;
        this.nodifySetFiled("region", region);
        return this;
    }

    public String getRegion() {
        this.nodifyGetFiled("region");
        return region;
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity setTown(String town) {
        this.town = town;
        this.nodifySetFiled("town", town);
        return this;
    }

    public String getTown() {
        this.nodifyGetFiled("town");
        return town;
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity setAddress(String address) {
        this.address = address;
        this.nodifySetFiled("address", address);
        return this;
    }

    public String getAddress() {
        this.nodifyGetFiled("address");
        return address;
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity setJjf(String jjf) {
        this.jjf = jjf;
        this.nodifySetFiled("jjf", jjf);
        return this;
    }

    public String getJjf() {
        this.nodifyGetFiled("jjf");
        return jjf;
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity setLinkMan(String linkMan) {
        this.linkMan = linkMan;
        this.nodifySetFiled("linkMan", linkMan);
        return this;
    }

    public String getLinkMan() {
        this.nodifyGetFiled("linkMan");
        return linkMan;
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity setLinkTel(String linkTel) {
        this.linkTel = linkTel;
        this.nodifySetFiled("linkTel", linkTel);
        return this;
    }

    public String getLinkTel() {
        this.nodifyGetFiled("linkTel");
        return linkTel;
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity setJjDate(Date jjDate) {
        this.jjDate = jjDate;
        this.nodifySetFiled("jjDate", jjDate);
        return this;
    }

    public Date getJjDate() {
        this.nodifyGetFiled("jjDate");
        return jjDate;
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity setJjDateStart(Date jjDateStart) {
        this.jjDateStart = jjDateStart;
        this.nodifySetFiled("jjDateStart", jjDateStart);
        return this;
    }

    public Date getJjDateStart() {
        this.nodifyGetFiled("jjDateStart");
        return jjDateStart;
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity setJjDateEnd(Date jjDateEnd) {
        this.jjDateEnd = jjDateEnd;
        this.nodifySetFiled("jjDateEnd", jjDateEnd);
        return this;
    }

    public Date getJjDateEnd() {
        this.nodifyGetFiled("jjDateEnd");
        return jjDateEnd;
    }
    public OmsOrderFwxmTmsXlXlLyXlEntity setIsDhjc(String isDhjc) {
        this.isDhjc = isDhjc;
        this.nodifySetFiled("isDhjc", isDhjc);
        return this;
    }

    public String getIsDhjc() {
        this.nodifyGetFiled("isDhjc");
        return isDhjc;
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity setIsAzcx(String isAzcx) {
        this.isAzcx = isAzcx;
        this.nodifySetFiled("isAzcx", isAzcx);
        return this;
    }

    public String getIsAzcx() {
        this.nodifyGetFiled("isAzcx");
        return isAzcx;
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity setIsThdz(String isThdz) {
        this.isThdz = isThdz;
        this.nodifySetFiled("isThdz", isThdz);
        return this;
    }

    public String getIsThdz() {
        this.nodifyGetFiled("isThdz");
        return isThdz;
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public OmsOrderFwxmTmsXlXlLyXlEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public OmsOrderFwxmTmsXlXlLyXlEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity setNote(String note) {
        this.note = note;
        this.nodifySetFiled("note", note);
        return this;
    }

    public String getNote() {
        this.nodifyGetFiled("note");
        return note;
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity setMemo(String memo) {
        this.memo = memo;
        this.nodifySetFiled("memo", memo);
        return this;
    }

    public String getMemo() {
        this.nodifyGetFiled("memo");
        return memo;
    }

    public OmsOrderFwxmTmsXlXlLyXlEntity setXlMode(String xlMode) {
        this.xlMode = xlMode;
        this.nodifySetFiled("xlMode", xlMode);
        return this;
    }

    public String getXlMode() {
        this.nodifyGetFiled("xlMode");
        return xlMode;
    }

}
