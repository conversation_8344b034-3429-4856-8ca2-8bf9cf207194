package com.eci.project.fzgjExtendData.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;

/**
 * @ClassName: FzgjExtendDataPageEntity
 * @Author: guangyan.mei
 * @Date: 2025/4/11 15:45
 * @Description: TODO
 */
public class FzgjExtendDataPageEntity extends FzgjExtendDataBaseEntity {

    /**
     * 分组名称
     */
    @ApiModelProperty("分组名称")
    @TableField("TYPENAME")
    private String typeName;

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }
}
