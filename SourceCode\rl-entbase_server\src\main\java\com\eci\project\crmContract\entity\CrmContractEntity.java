package com.eci.project.crmContract.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.codeConvert.EciCode;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.math.BigDecimal;
import java.util.Date;


/**
* 合同对象 CRM_CONTRACT
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-05-21
*/
@ApiModel("合同")
@TableName("CRM_CONTRACT")
@FieldNameConstants
public class CrmContractEntity extends EciBaseEntity{
    /**
    * 系统合同编号(HT+8位流水号)
    */
    @ApiModelProperty("系统合同编号(HT+8位流水号)(36)")
    @TableId("GUID")
    private String guid;

    /**
    * 企业代码
    */
    @ApiModelProperty("企业代码(36)")
    @TableField("CUSTOMER_CODE")
    private String customerCode;
    @TableField(exist = false)
    private int fileCount;

    /**
    * 企业全称
    */
    @ApiModelProperty("企业全称(100)")
    @TableField("CUSTOMER_NAME")
    private String customerName;

    /**
    * 合同编号
    */
    @ApiModelProperty("合同编号(100)")
    @TableField("CONTRACT_NUM")
    private String contractNum;

    /**
    * 合同类型
    */
    @ApiModelProperty("合同类型(100)")
    @TableField("CONTRACT_TYPE")
    private String contractType;
    @TableField(exist = false)
    private String contractTypeName;
    /**
    * 首次签约日期
    */
    @ApiModelProperty("首次签约日期(7)")
    @TableField("SIGN_DATE_FIRST")
    private Date signDateFirst;

    @ApiModelProperty("首次签约日期开始")
    @TableField(exist=false)
    private Date signDateFirstStart;

    @ApiModelProperty("首次签约日期结束")
    @TableField(exist=false)
    private Date signDateFirstEnd;

    /**
    * 合同生效日期
    */
    @ApiModelProperty("合同生效日期(7)")
    @TableField("EFFECTIVE_DATE")
    private Date effectiveDate;

    @ApiModelProperty("合同生效日期开始")
    @TableField(exist=false)
    private Date effectiveDateStart;

    @ApiModelProperty("合同生效日期结束")
    @TableField(exist=false)
    private Date effectiveDateEnd;

    /**
    * 合同失效日期
    */
    @ApiModelProperty("合同失效日期(7)")
    @TableField("EXPIRATION_DATE")
    private Date expirationDate;

    @ApiModelProperty("合同失效日期开始")
    @TableField(exist=false)
    private Date expirationDateStart;

    @ApiModelProperty("合同失效日期结束")
    @TableField(exist=false)
    private Date expirationDateEnd;

    /**
    * 合同终止时间
    */
    @ApiModelProperty("合同终止时间(7)")
    @TableField("TERMINATION_DATE")
    private Date terminationDate;

    @ApiModelProperty("合同终止时间开始")
    @TableField(exist=false)
    private Date terminationDateStart;

    @ApiModelProperty("合同终止时间结束")
    @TableField(exist=false)
    private Date terminationDateEnd;

    /**
    * 续签日期
    */
    @ApiModelProperty("续签日期(7)")
    @TableField("RENEWAL_DATE")
    private Date renewalDate;

    @ApiModelProperty("续签日期开始")
    @TableField(exist=false)
    private Date renewalDateStart;

    @ApiModelProperty("续签日期结束")
    @TableField(exist=false)
    private Date renewalDateEnd;

    /**
    * 续签合同失效日期
    */
    @ApiModelProperty("续签合同失效日期(7)")
    @TableField("RENEWAL_EXPIRATION_DATE")
    private Date renewalExpirationDate;

    @ApiModelProperty("续签合同失效日期开始")
    @TableField(exist=false)
    private Date renewalExpirationDateStart;

    @ApiModelProperty("续签合同失效日期结束")
    @TableField(exist=false)
    private Date renewalExpirationDateEnd;

    /**
    * 最新合同失效日期
    */
    @ApiModelProperty("最新合同失效日期(7)")
    @TableField("LATEST_EXPIRATION_DATE")
    private Date latestExpirationDate;

    @ApiModelProperty("最新合同失效日期开始")
    @TableField(exist=false)
    private Date latestExpirationDateStart;

    @ApiModelProperty("最新合同失效日期结束")
    @TableField(exist=false)
    private Date latestExpirationDateEnd;

    /**
    * 是否手动终止合同
    */
    @ApiModelProperty("是否手动终止合同(1)")
    @TableField("STATUS")
    @EciCode("YNKey")
    private String status;

    /**
    * 备注
    */
    @ApiModelProperty("备注(500)")
    @TableField("MEMO")
    private String memo;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 最后修改日期
    */
    @ApiModelProperty("最后修改日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("最后修改日期开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("最后修改日期结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(200)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 企业简称
    */
    @ApiModelProperty("企业简称(50)")
    @TableField("CUSTOMER_SHORT_NAME")
    private String customerShortName;

    /**
    * 企业GUID
    */
    @ApiModelProperty("企业GUID(50)")
    @TableField("CUSTOMER_GUID")
    private String customerGuid;

    /**
    * 合同名称
    */
    @ApiModelProperty("合同名称(200)")
    @TableField("CONTRACT_NAME")
    private String contractName;

    /**
    * 合同状态Y失效N有效，D待生效
    */
    @ApiModelProperty("合同状态Y失效N有效，D待生效(1)")
    @TableField("HTZT")
    private String htzt;

    /**
    * 收款期限(天)
    */
    @ApiModelProperty("收款期限(天)(22)")
    @TableField("SK_DATELINE")
    private Integer skDateline;

    /**
    * 服务要求
    */
    @ApiModelProperty("服务要求(1,000)")
    @TableField("SERVICE_ASK")
    private String serviceAsk;

    /**
    * 注意事项
    */
    @ApiModelProperty("注意事项(1,000)")
    @TableField("CAREFUL_ITEM")
    private String carefulItem;

    /**
    * 是否有合同运输押金
    */
    @ApiModelProperty("是否有合同运输押金(1)")
    @TableField("IS_DEPOSIT")
    private String isDeposit;

    /**
    * 押金金额
    */
    @ApiModelProperty("押金金额(22)")
    @TableField("AMOUNT_YJ")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal amountYj;

    /**
    * 合同运输押金是否退还
    */
    @ApiModelProperty("合同运输押金是否退还(1)")
    @TableField("IS_TH")
    private String isTh;

    /**
    * 押金退还说明
    */
    @ApiModelProperty("押金退还说明(1,000)")
    @TableField("MEMO_TH")
    private String memoTh;

    /**
    * OA退款单编号
    */
    @ApiModelProperty("OA退款单编号(100)")
    @TableField("OA_TKD_NO")
    private String oaTkdNo;

    /**
    * 是否单价含税（Y是/N否）
    */
    @ApiModelProperty("是否单价含税（Y是/N否）(1)")
    @TableField("TAX_STATUS")
    private String taxStatus;

    /**
    * 发票类型
    */
    @ApiModelProperty("发票类型(100)")
    @TableField("INVOICE_TYPE")
    private String invoiceType;

    /**
    * 税率
    */
    @ApiModelProperty("税率(22)")
    @TableField("TAX_RATE")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal taxRate;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public CrmContractEntity() {
        this.setSubClazz(CrmContractEntity.class);
    }

    public CrmContractEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public CrmContractEntity setFileCount(int fileCount) {
        this.fileCount = fileCount;
        this.nodifySetFiled("fileCount", fileCount);
        return this;
    }

    public int getFileCount() {
        this.nodifyGetFiled("fileCount");
        return fileCount;
    }

    public CrmContractEntity setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
        this.nodifySetFiled("customerCode", customerCode);
        return this;
    }

    public String getCustomerCode() {
        this.nodifyGetFiled("customerCode");
        return customerCode;
    }

    public CrmContractEntity setCustomerName(String customerName) {
        this.customerName = customerName;
        this.nodifySetFiled("customerName", customerName);
        return this;
    }

    public String getCustomerName() {
        this.nodifyGetFiled("customerName");
        return customerName;
    }

    public CrmContractEntity setContractNum(String contractNum) {
        this.contractNum = contractNum;
        this.nodifySetFiled("contractNum", contractNum);
        return this;
    }

    public String getContractNum() {
        this.nodifyGetFiled("contractNum");
        return contractNum;
    }

    public CrmContractEntity setContractType(String contractType) {
        this.contractType = contractType;
        this.nodifySetFiled("contractType", contractType);
        return this;
    }

    public String getContractType() {
        this.nodifyGetFiled("contractType");
        return contractType;
    }

    public CrmContractEntity setContractTypeName(String contractTypeName) {
        this.contractTypeName = contractTypeName;
        this.nodifySetFiled("contractTypeName", contractTypeName);
        return this;
    }

    public String getContractTypeName() {
        this.nodifyGetFiled("contractTypeName");
        return contractTypeName;
    }

    public CrmContractEntity setSignDateFirst(Date signDateFirst) {
        this.signDateFirst = signDateFirst;
        this.nodifySetFiled("signDateFirst", signDateFirst);
        return this;
    }

    public Date getSignDateFirst() {
        this.nodifyGetFiled("signDateFirst");
        return signDateFirst;
    }

    public CrmContractEntity setSignDateFirstStart(Date signDateFirstStart) {
        this.signDateFirstStart = signDateFirstStart;
        this.nodifySetFiled("signDateFirstStart", signDateFirstStart);
        return this;
    }

    public Date getSignDateFirstStart() {
        this.nodifyGetFiled("signDateFirstStart");
        return signDateFirstStart;
    }

    public CrmContractEntity setSignDateFirstEnd(Date signDateFirstEnd) {
        this.signDateFirstEnd = signDateFirstEnd;
        this.nodifySetFiled("signDateFirstEnd", signDateFirstEnd);
        return this;
    }

    public Date getSignDateFirstEnd() {
        this.nodifyGetFiled("signDateFirstEnd");
        return signDateFirstEnd;
    }
    public CrmContractEntity setEffectiveDate(Date effectiveDate) {
        this.effectiveDate = effectiveDate;
        this.nodifySetFiled("effectiveDate", effectiveDate);
        return this;
    }

    public Date getEffectiveDate() {
        this.nodifyGetFiled("effectiveDate");
        return effectiveDate;
    }

    public CrmContractEntity setEffectiveDateStart(Date effectiveDateStart) {
        this.effectiveDateStart = effectiveDateStart;
        this.nodifySetFiled("effectiveDateStart", effectiveDateStart);
        return this;
    }

    public Date getEffectiveDateStart() {
        this.nodifyGetFiled("effectiveDateStart");
        return effectiveDateStart;
    }

    public CrmContractEntity setEffectiveDateEnd(Date effectiveDateEnd) {
        this.effectiveDateEnd = effectiveDateEnd;
        this.nodifySetFiled("effectiveDateEnd", effectiveDateEnd);
        return this;
    }

    public Date getEffectiveDateEnd() {
        this.nodifyGetFiled("effectiveDateEnd");
        return effectiveDateEnd;
    }
    public CrmContractEntity setExpirationDate(Date expirationDate) {
        this.expirationDate = expirationDate;
        this.nodifySetFiled("expirationDate", expirationDate);
        return this;
    }

    public Date getExpirationDate() {
        this.nodifyGetFiled("expirationDate");
        return expirationDate;
    }

    public CrmContractEntity setExpirationDateStart(Date expirationDateStart) {
        this.expirationDateStart = expirationDateStart;
        this.nodifySetFiled("expirationDateStart", expirationDateStart);
        return this;
    }

    public Date getExpirationDateStart() {
        this.nodifyGetFiled("expirationDateStart");
        return expirationDateStart;
    }

    public CrmContractEntity setExpirationDateEnd(Date expirationDateEnd) {
        this.expirationDateEnd = expirationDateEnd;
        this.nodifySetFiled("expirationDateEnd", expirationDateEnd);
        return this;
    }

    public Date getExpirationDateEnd() {
        this.nodifyGetFiled("expirationDateEnd");
        return expirationDateEnd;
    }
    public CrmContractEntity setTerminationDate(Date terminationDate) {
        this.terminationDate = terminationDate;
        this.nodifySetFiled("terminationDate", terminationDate);
        return this;
    }

    public Date getTerminationDate() {
        this.nodifyGetFiled("terminationDate");
        return terminationDate;
    }

    public CrmContractEntity setTerminationDateStart(Date terminationDateStart) {
        this.terminationDateStart = terminationDateStart;
        this.nodifySetFiled("terminationDateStart", terminationDateStart);
        return this;
    }

    public Date getTerminationDateStart() {
        this.nodifyGetFiled("terminationDateStart");
        return terminationDateStart;
    }

    public CrmContractEntity setTerminationDateEnd(Date terminationDateEnd) {
        this.terminationDateEnd = terminationDateEnd;
        this.nodifySetFiled("terminationDateEnd", terminationDateEnd);
        return this;
    }

    public Date getTerminationDateEnd() {
        this.nodifyGetFiled("terminationDateEnd");
        return terminationDateEnd;
    }
    public CrmContractEntity setRenewalDate(Date renewalDate) {
        this.renewalDate = renewalDate;
        this.nodifySetFiled("renewalDate", renewalDate);
        return this;
    }

    public Date getRenewalDate() {
        this.nodifyGetFiled("renewalDate");
        return renewalDate;
    }

    public CrmContractEntity setRenewalDateStart(Date renewalDateStart) {
        this.renewalDateStart = renewalDateStart;
        this.nodifySetFiled("renewalDateStart", renewalDateStart);
        return this;
    }

    public Date getRenewalDateStart() {
        this.nodifyGetFiled("renewalDateStart");
        return renewalDateStart;
    }

    public CrmContractEntity setRenewalDateEnd(Date renewalDateEnd) {
        this.renewalDateEnd = renewalDateEnd;
        this.nodifySetFiled("renewalDateEnd", renewalDateEnd);
        return this;
    }

    public Date getRenewalDateEnd() {
        this.nodifyGetFiled("renewalDateEnd");
        return renewalDateEnd;
    }
    public CrmContractEntity setRenewalExpirationDate(Date renewalExpirationDate) {
        this.renewalExpirationDate = renewalExpirationDate;
        this.nodifySetFiled("renewalExpirationDate", renewalExpirationDate);
        return this;
    }

    public Date getRenewalExpirationDate() {
        this.nodifyGetFiled("renewalExpirationDate");
        return renewalExpirationDate;
    }

    public CrmContractEntity setRenewalExpirationDateStart(Date renewalExpirationDateStart) {
        this.renewalExpirationDateStart = renewalExpirationDateStart;
        this.nodifySetFiled("renewalExpirationDateStart", renewalExpirationDateStart);
        return this;
    }

    public Date getRenewalExpirationDateStart() {
        this.nodifyGetFiled("renewalExpirationDateStart");
        return renewalExpirationDateStart;
    }

    public CrmContractEntity setRenewalExpirationDateEnd(Date renewalExpirationDateEnd) {
        this.renewalExpirationDateEnd = renewalExpirationDateEnd;
        this.nodifySetFiled("renewalExpirationDateEnd", renewalExpirationDateEnd);
        return this;
    }

    public Date getRenewalExpirationDateEnd() {
        this.nodifyGetFiled("renewalExpirationDateEnd");
        return renewalExpirationDateEnd;
    }
    public CrmContractEntity setLatestExpirationDate(Date latestExpirationDate) {
        this.latestExpirationDate = latestExpirationDate;
        this.nodifySetFiled("latestExpirationDate", latestExpirationDate);
        return this;
    }

    public Date getLatestExpirationDate() {
        this.nodifyGetFiled("latestExpirationDate");
        return latestExpirationDate;
    }

    public CrmContractEntity setLatestExpirationDateStart(Date latestExpirationDateStart) {
        this.latestExpirationDateStart = latestExpirationDateStart;
        this.nodifySetFiled("latestExpirationDateStart", latestExpirationDateStart);
        return this;
    }

    public Date getLatestExpirationDateStart() {
        this.nodifyGetFiled("latestExpirationDateStart");
        return latestExpirationDateStart;
    }

    public CrmContractEntity setLatestExpirationDateEnd(Date latestExpirationDateEnd) {
        this.latestExpirationDateEnd = latestExpirationDateEnd;
        this.nodifySetFiled("latestExpirationDateEnd", latestExpirationDateEnd);
        return this;
    }

    public Date getLatestExpirationDateEnd() {
        this.nodifyGetFiled("latestExpirationDateEnd");
        return latestExpirationDateEnd;
    }
    public CrmContractEntity setStatus(String status) {
        this.status = status;
        this.nodifySetFiled("status", status);
        return this;
    }

    public String getStatus() {
        this.nodifyGetFiled("status");
        return status;
    }

    public CrmContractEntity setMemo(String memo) {
        this.memo = memo;
        this.nodifySetFiled("memo", memo);
        return this;
    }

    public String getMemo() {
        this.nodifyGetFiled("memo");
        return memo;
    }

    public CrmContractEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public CrmContractEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public CrmContractEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public CrmContractEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public CrmContractEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public CrmContractEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public CrmContractEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public CrmContractEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public CrmContractEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public CrmContractEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public CrmContractEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public CrmContractEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public CrmContractEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public CrmContractEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public CrmContractEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public CrmContractEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public CrmContractEntity setCustomerShortName(String customerShortName) {
        this.customerShortName = customerShortName;
        this.nodifySetFiled("customerShortName", customerShortName);
        return this;
    }

    public String getCustomerShortName() {
        this.nodifyGetFiled("customerShortName");
        return customerShortName;
    }

    public CrmContractEntity setCustomerGuid(String customerGuid) {
        this.customerGuid = customerGuid;
        this.nodifySetFiled("customerGuid", customerGuid);
        return this;
    }

    public String getCustomerGuid() {
        this.nodifyGetFiled("customerGuid");
        return customerGuid;
    }

    public CrmContractEntity setContractName(String contractName) {
        this.contractName = contractName;
        this.nodifySetFiled("contractName", contractName);
        return this;
    }

    public String getContractName() {
        this.nodifyGetFiled("contractName");
        return contractName;
    }

    public CrmContractEntity setHtzt(String htzt) {
        this.htzt = htzt;
        this.nodifySetFiled("htzt", htzt);
        return this;
    }

    public String getHtzt() {
        this.nodifyGetFiled("htzt");
        return htzt;
    }

    public CrmContractEntity setSkDateline(Integer skDateline) {
        this.skDateline = skDateline;
        this.nodifySetFiled("skDateline", skDateline);
        return this;
    }

    public Integer getSkDateline() {
        this.nodifyGetFiled("skDateline");
        return skDateline;
    }

    public CrmContractEntity setServiceAsk(String serviceAsk) {
        this.serviceAsk = serviceAsk;
        this.nodifySetFiled("serviceAsk", serviceAsk);
        return this;
    }

    public String getServiceAsk() {
        this.nodifyGetFiled("serviceAsk");
        return serviceAsk;
    }

    public CrmContractEntity setCarefulItem(String carefulItem) {
        this.carefulItem = carefulItem;
        this.nodifySetFiled("carefulItem", carefulItem);
        return this;
    }

    public String getCarefulItem() {
        this.nodifyGetFiled("carefulItem");
        return carefulItem;
    }

    public CrmContractEntity setIsDeposit(String isDeposit) {
        this.isDeposit = isDeposit;
        this.nodifySetFiled("isDeposit", isDeposit);
        return this;
    }

    public String getIsDeposit() {
        this.nodifyGetFiled("isDeposit");
        return isDeposit;
    }

    public CrmContractEntity setAmountYj(BigDecimal amountYj) {
        this.amountYj = amountYj;
        this.nodifySetFiled("amountYj", amountYj);
        return this;
    }

    public BigDecimal getAmountYj() {
        this.nodifyGetFiled("amountYj");
        return amountYj;
    }

    public CrmContractEntity setIsTh(String isTh) {
        this.isTh = isTh;
        this.nodifySetFiled("isTh", isTh);
        return this;
    }

    public String getIsTh() {
        this.nodifyGetFiled("isTh");
        return isTh;
    }

    public CrmContractEntity setMemoTh(String memoTh) {
        this.memoTh = memoTh;
        this.nodifySetFiled("memoTh", memoTh);
        return this;
    }

    public String getMemoTh() {
        this.nodifyGetFiled("memoTh");
        return memoTh;
    }

    public CrmContractEntity setOaTkdNo(String oaTkdNo) {
        this.oaTkdNo = oaTkdNo;
        this.nodifySetFiled("oaTkdNo", oaTkdNo);
        return this;
    }

    public String getOaTkdNo() {
        this.nodifyGetFiled("oaTkdNo");
        return oaTkdNo;
    }

    public CrmContractEntity setTaxStatus(String taxStatus) {
        this.taxStatus = taxStatus;
        this.nodifySetFiled("taxStatus", taxStatus);
        return this;
    }

    public String getTaxStatus() {
        this.nodifyGetFiled("taxStatus");
        return taxStatus;
    }

    public CrmContractEntity setInvoiceType(String invoiceType) {
        this.invoiceType = invoiceType;
        this.nodifySetFiled("invoiceType", invoiceType);
        return this;
    }

    public String getInvoiceType() {
        this.nodifyGetFiled("invoiceType");
        return invoiceType;
    }

    public CrmContractEntity setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
        this.nodifySetFiled("taxRate", taxRate);
        return this;
    }

    public BigDecimal getTaxRate() {
        this.nodifyGetFiled("taxRate");
        return taxRate;
    }

}
