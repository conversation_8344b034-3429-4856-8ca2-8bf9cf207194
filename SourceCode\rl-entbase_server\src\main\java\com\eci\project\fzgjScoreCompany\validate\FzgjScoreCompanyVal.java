package com.eci.project.fzgjScoreCompany.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjScoreCompany.entity.FzgjScoreCompanyEntity;

import org.springframework.stereotype.Service;


/**
* 企业评分Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-06-16
*/
@Service
public class FzgjScoreCompanyVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjScoreCompanyEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjScoreCompanyEntity entity, BusinessType businessType) {

    }

}
