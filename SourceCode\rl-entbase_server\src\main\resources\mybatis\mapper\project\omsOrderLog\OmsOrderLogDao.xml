<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.omsOrderLog.dao.OmsOrderLogDao">
    <resultMap type="OmsOrderLogEntity" id="OmsOrderLogResult">
        <result property="orderNo" column="ORDER_NO"/>
        <result property="bizType" column="BIZ_TYPE"/>
        <result property="operName" column="OPER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="dataDetail" column="DATA_DETAIL"/>
        <result property="memo" column="MEMO"/>
        <result property="userName" column="USER_NAME"/>
        <result property="trueName" column="TRUE_NAME"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="guid" column="GUID"/>
    </resultMap>

    <sql id="selectOmsOrderLogEntityVo">
        select
            ORDER_NO,
            BIZ_TYPE,
            OPER_NAME,
            CREATE_DATE,
            DATA_DETAIL,
            MEMO,
            USER_NAME,
            TRUE_NAME,
            COMPANY_CODE,
            COMPANY_NAME,
            GUID
        from OMS_ORDER_LOG
    </sql>
</mapper>