package com.eci.project.fzgjBdProductFwxmFf.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.fzgjBdProductFwxmFf.entity.FzgjBdProductFwxmFfEntity;


/**
* 不分发的服务项目Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-04-01
*/
public interface FzgjBdProductFwxmFfDao extends EciBaseDao<FzgjBdProductFwxmFfEntity> {

}