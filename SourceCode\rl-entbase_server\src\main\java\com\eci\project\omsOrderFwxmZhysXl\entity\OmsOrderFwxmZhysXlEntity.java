package com.eci.project.omsOrderFwxmZhysXl.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.common.DictField;
import com.eci.common.ZsrBaseEntity;
import com.eci.crud.entity.EciBaseEntity;
import com.eci.project.omsOrderFwxmTmsXlXl.entity.OmsOrderFwxmTmsXlXlEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
 * 综合运输-线路对象 OMS_ORDER_FWXM_ZHYS_XL
 * 可以自己扩展字段
 *
 * @<NAME_EMAIL>
 * @date 2025-06-18
 */
@ApiModel("综合运输-线路")
@TableName("OMS_ORDER_FWXM_ZHYS_XL")
@FieldNameConstants
public class OmsOrderFwxmZhysXlEntity extends ZsrBaseEntity {
    /**
     * 线路唯一编号
     */
    @ApiModelProperty("线路唯一编号(36)")
    @TableId("LINE_NO")
    private String lineNo;

    /**
     * 订单号
     */
    @ApiModelProperty("订单号(36)")
    @TableField("ORDER_NO")
    private String orderNo;

    /**
     * 线路序号
     */
    @ApiModelProperty("线路序号(22)")
    @TableField("LINE_SEQ")
    private Integer lineSeq;

    /**
     * 程运序列唯一编号
     */
    @ApiModelProperty("程运序列唯一编号(36)")
    @TableField("SEQ_NO")
    private String seqNo;

    @ApiModelProperty("(36)")
    @TableField("ZHYS_GUID")
    private String zhysGuid;

    /**
     * 外包
     */
    @ApiModelProperty("外包(20)")
    @TableField("IS_WB")
    @DictField(queryKey = "YNKey")
    private String isWb;

    /**
     * 单程运输服务
     */
    @ApiModelProperty("单程运输服务(20)")
    @TableField("DCYSXL")
    @DictField(queryKey = "OMS_ORDER_DCYSFW")
    private String dcysxl;

    /**
     * 供应商
     */
    @ApiModelProperty("供应商(200)")
    @TableField("GYS")
    private String gys;

    /**
     * 内部作业组织
     */
    @ApiModelProperty("内部作业组织(200)")
    @TableField("NBZYZZ")
    private String nbzyzz;

    /**
     * 协作任务编号
     */
    @ApiModelProperty("协作任务编号(200)")
    @TableField("XZRWBH")
    private String xzrwbh;

    /**
     * 单程运输线路-国家
     */
    @ApiModelProperty("单程运输线路-国家(20)")
    @TableField("NATIONALID_COUNTY")
    @DictField(sql = "select  a.code,a.ch_name name from fzgj_bd_country a where 1=1 and a.status='Y'")
    private String nationalidCounty;

    /**
     * 单程运输线路-省
     */
    @ApiModelProperty("单程运输线路-省(20)")
    @TableField("NATIONALID_PROVINCE")
    @DictField(sql = "select a.guid code ,a.name from fzgj_bd_province  a where 1=1 and a.status='Y'")
    private String nationalidProvince;

    /**
     * 单程运输线路-市
     */
    @ApiModelProperty("单程运输线路-市(20)")
    @TableField("NATIONALID_CITY")
    @DictField(sql = "select a.guid code,a.name from fzgj_bd_city a where  1=1 and  a.status='Y'")
    private String nationalidCity;

    /**
     * 单程运输线路-区县
     */
    @ApiModelProperty("单程运输线路-区县(20)")
    @TableField("NATIONALID_REGION")
    @DictField(sql = "select a.code,a.name from fzgj_bd_area a where 1=1 and  a.status='Y'")
    private String nationalidRegion;

    /**
     * 单程运输线路-乡镇/港口/站点
     */
    @ApiModelProperty("单程运输线路-乡镇/港口/站点(20)")
    @TableField("NATIONALID_TOWN")
    @DictField(sql = "select a.guid  code ,a.name  from fzgj_bd_district a where 1=1 and  a.status='Y'")
    private String nationalidTown;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
     * 创建日期
     */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist = false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist = false)
    private Date createDateEnd;

    /**
     * 最后修改人
     */
    @ApiModelProperty("最后修改人(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
     * 最后修改人
     */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
     * 最后修改日期
     */
    @ApiModelProperty("最后修改日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("最后修改日期开始")
    @TableField(exist = false)
    private Date updateDateStart;

    @ApiModelProperty("最后修改日期结束")
    @TableField(exist = false)
    private Date updateDateEnd;

    /**
     * 归属公司
     */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
     * 归属公司
     */
    @ApiModelProperty("归属公司(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
     * 归属组织
     */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
     * 归属组织
     */
    @ApiModelProperty("归属组织(200)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
     * 归属集团
     */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
     * 归属集团
     */
    @ApiModelProperty("归属集团(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
     * 结算目的地-国家
     */
    @ApiModelProperty("结算目的地-国家(20)")
    @TableField("TERMINUSID_COUNTRY")
    @DictField(sql = "select  a.code,a.ch_name name from fzgj_bd_country a where 1=1 and a.status='Y'")
    private String terminusidCountry;

    /**
     * 结算目的地-市
     */
    @ApiModelProperty("结算目的地-市(20)")
    @TableField("TERMINUSID_CITY")
    @DictField(sql = "select a.guid code,a.name from fzgj_bd_city a where 1=1 and  a.status='Y'")
    private String terminusidCity;

    /**
     * 结算目的地-省
     */
    @ApiModelProperty("结算目的地-省(20)")
    @TableField("TERMINUSID_PROVINCE")
    @DictField(sql = "select a.guid code ,a.name from fzgj_bd_province  a where 1=1 and a.status='Y'")
    private String terminusidProvince;

    /**
     * 结算目的地-区县
     */
    @ApiModelProperty("结算目的地-区县(20)")
    @TableField("TERMINUSID_REGION")
    @DictField(sql = "select a.code,a.name from fzgj_bd_area a where 1=1 and  a.status='Y'")
    private String terminusidRegion;

    /**
     * 结算目的地-乡镇、港口、站点
     */
    @ApiModelProperty("结算目的地-乡镇、港口、站点(20)")
    @TableField("TERMINUSID_TOWN")
    @DictField(sql = "select a.guid  code ,a.name  from fzgj_bd_district a where 1=1 and  a.status='Y'")
    private String terminusidTown;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public OmsOrderFwxmZhysXlEntity() {
        this.setSubClazz(OmsOrderFwxmZhysXlEntity.class);
    }

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public String getLineNo() {
        this.nodifyGetFiled("lineNo");
        return lineNo;
    }

    public OmsOrderFwxmZhysXlEntity setLineNo(String lineNo) {
        this.lineNo = lineNo;
        this.nodifySetFiled("lineNo", lineNo);
        return this;
    }

    public String getOrderNo() {
        this.nodifyGetFiled("orderNo");
        return orderNo;
    }

    public OmsOrderFwxmZhysXlEntity setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        this.nodifySetFiled("orderNo", orderNo);
        return this;
    }

    public Integer getLineSeq() {
        this.nodifyGetFiled("lineSeq");
        return lineSeq;
    }

    public OmsOrderFwxmZhysXlEntity setLineSeq(Integer lineSeq) {
        this.lineSeq = lineSeq;
        this.nodifySetFiled("lineSeq", lineSeq);
        return this;
    }

    public String getSeqNo() {
        this.nodifyGetFiled("seqNo");
        return seqNo;
    }

    public OmsOrderFwxmZhysXlEntity setSeqNo(String seqNo) {
        this.seqNo = seqNo;
        this.nodifySetFiled("seqNo", seqNo);
        return this;
    }

    public String getZhysGuid() {
        this.nodifyGetFiled("zhysGuid");
        return zhysGuid;
    }

    public OmsOrderFwxmZhysXlEntity setZhysGuid(String zhysGuid) {
        this.zhysGuid = zhysGuid;
        this.nodifySetFiled("zhysGuid", zhysGuid);
        return this;
    }

    public String getIsWb() {
        this.nodifyGetFiled("isWb");
        return isWb;
    }

    public OmsOrderFwxmZhysXlEntity setIsWb(String isWb) {
        this.isWb = isWb;
        this.nodifySetFiled("isWb", isWb);
        return this;
    }

    public String getDcysxl() {
        this.nodifyGetFiled("dcysxl");
        return dcysxl;
    }

    public OmsOrderFwxmZhysXlEntity setDcysxl(String dcysxl) {
        this.dcysxl = dcysxl;
        this.nodifySetFiled("dcysxl", dcysxl);
        return this;
    }

    public String getGys() {
        this.nodifyGetFiled("gys");
        return gys;
    }

    public OmsOrderFwxmZhysXlEntity setGys(String gys) {
        this.gys = gys;
        this.nodifySetFiled("gys", gys);
        return this;
    }

    public String getNbzyzz() {
        this.nodifyGetFiled("nbzyzz");
        return nbzyzz;
    }

    public OmsOrderFwxmZhysXlEntity setNbzyzz(String nbzyzz) {
        this.nbzyzz = nbzyzz;
        this.nodifySetFiled("nbzyzz", nbzyzz);
        return this;
    }

    public String getXzrwbh() {
        this.nodifyGetFiled("xzrwbh");
        return xzrwbh;
    }

    public OmsOrderFwxmZhysXlEntity setXzrwbh(String xzrwbh) {
        this.xzrwbh = xzrwbh;
        this.nodifySetFiled("xzrwbh", xzrwbh);
        return this;
    }

    public String getNationalidCounty() {
        this.nodifyGetFiled("nationalidCounty");
        return nationalidCounty;
    }

    public OmsOrderFwxmZhysXlEntity setNationalidCounty(String nationalidCounty) {
        this.nationalidCounty = nationalidCounty;
        this.nodifySetFiled("nationalidCounty", nationalidCounty);
        return this;
    }

    public String getNationalidProvince() {
        this.nodifyGetFiled("nationalidProvince");
        return nationalidProvince;
    }

    public OmsOrderFwxmZhysXlEntity setNationalidProvince(String nationalidProvince) {
        this.nationalidProvince = nationalidProvince;
        this.nodifySetFiled("nationalidProvince", nationalidProvince);
        return this;
    }

    public String getNationalidCity() {
        this.nodifyGetFiled("nationalidCity");
        return nationalidCity;
    }

    public OmsOrderFwxmZhysXlEntity setNationalidCity(String nationalidCity) {
        this.nationalidCity = nationalidCity;
        this.nodifySetFiled("nationalidCity", nationalidCity);
        return this;
    }

    public String getNationalidRegion() {
        this.nodifyGetFiled("nationalidRegion");
        return nationalidRegion;
    }

    public OmsOrderFwxmZhysXlEntity setNationalidRegion(String nationalidRegion) {
        this.nationalidRegion = nationalidRegion;
        this.nodifySetFiled("nationalidRegion", nationalidRegion);
        return this;
    }

    public String getNationalidTown() {
        this.nodifyGetFiled("nationalidTown");
        return nationalidTown;
    }

    public OmsOrderFwxmZhysXlEntity setNationalidTown(String nationalidTown) {
        this.nationalidTown = nationalidTown;
        this.nodifySetFiled("nationalidTown", nationalidTown);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public OmsOrderFwxmZhysXlEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public OmsOrderFwxmZhysXlEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public OmsOrderFwxmZhysXlEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public OmsOrderFwxmZhysXlEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }

    public OmsOrderFwxmZhysXlEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public OmsOrderFwxmZhysXlEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public OmsOrderFwxmZhysXlEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public OmsOrderFwxmZhysXlEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public OmsOrderFwxmZhysXlEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }

    public OmsOrderFwxmZhysXlEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public OmsOrderFwxmZhysXlEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public OmsOrderFwxmZhysXlEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public OmsOrderFwxmZhysXlEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public OmsOrderFwxmZhysXlEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public OmsOrderFwxmZhysXlEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public OmsOrderFwxmZhysXlEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getTerminusidCountry() {
        this.nodifyGetFiled("terminusidCountry");
        return terminusidCountry;
    }

    public OmsOrderFwxmZhysXlEntity setTerminusidCountry(String terminusidCountry) {
        this.terminusidCountry = terminusidCountry;
        this.nodifySetFiled("terminusidCountry", terminusidCountry);
        return this;
    }

    public String getTerminusidCity() {
        this.nodifyGetFiled("terminusidCity");
        return terminusidCity;
    }

    public OmsOrderFwxmZhysXlEntity setTerminusidCity(String terminusidCity) {
        this.terminusidCity = terminusidCity;
        this.nodifySetFiled("terminusidCity", terminusidCity);
        return this;
    }

    public String getTerminusidProvince() {
        this.nodifyGetFiled("terminusidProvince");
        return terminusidProvince;
    }

    public OmsOrderFwxmZhysXlEntity setTerminusidProvince(String terminusidProvince) {
        this.terminusidProvince = terminusidProvince;
        this.nodifySetFiled("terminusidProvince", terminusidProvince);
        return this;
    }

    public String getTerminusidRegion() {
        this.nodifyGetFiled("terminusidRegion");
        return terminusidRegion;
    }

    public OmsOrderFwxmZhysXlEntity setTerminusidRegion(String terminusidRegion) {
        this.terminusidRegion = terminusidRegion;
        this.nodifySetFiled("terminusidRegion", terminusidRegion);
        return this;
    }

    public String getTerminusidTown() {
        this.nodifyGetFiled("terminusidTown");
        return terminusidTown;
    }

    public OmsOrderFwxmZhysXlEntity setTerminusidTown(String terminusidTown) {
        this.terminusidTown = terminusidTown;
        this.nodifySetFiled("terminusidTown", terminusidTown);
        return this;
    }

}
