package com.eci.project.etmsBdTruckGcQz.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.etmsBdTruckGcQz.entity.EtmsBdTruckGcQzEntity;

import org.springframework.stereotype.Service;


/**
* 挂车号Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-16
*/
@Service
public class EtmsBdTruckGcQzVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(EtmsBdTruckGcQzEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(EtmsBdTruckGcQzEntity entity, BusinessType businessType) {

    }

}
