package com.eci.project.fzgjBdServiceItemPagesPt.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 企业服务项目对应页面编辑区对象 FZGJ_BD_SERVICE_ITEM_PAGES_PT
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-04-01
*/
@ApiModel("企业服务项目对应页面编辑区")
@TableName("FZGJ_BD_SERVICE_ITEM_PAGES_PT")
@FieldNameConstants
public class FzgjBdServiceItemPagesPtEntity extends EciBaseEntity{
    /**
    * GUID
    */
    @ApiModelProperty("GUID(36)")
    @TableId("GUID")
    private String guid;

    /**
    * 服务项目CODE
    */
    @ApiModelProperty("服务项目CODE(50)")
    @TableField("SERVICE_ITEM_CODE")
    private String serviceItemCode;

    /**
    * 代码
    */
    @ApiModelProperty("代码(20)")
    @TableField("CODE")
    private String code;

    /**
    * 名称
    */
    @ApiModelProperty("名称(100)")
    @TableField("NAME")
    private String name;

    /**
    * 顺序
    */
    @ApiModelProperty("顺序(22)")
    @TableField("SEQ")
    private Long seq;

    /**
    * 状态
    */
    @ApiModelProperty("状态(1)")
    @TableField("STATUS")
    private String status;

    /**
    * 服务项目页面URL
    */
    @ApiModelProperty("服务项目页面URL(50)")
    @TableField("PAGE_URL")
    private String pageUrl;

    /**
    * 备注
    */
    @ApiModelProperty("备注(500)")
    @TableField("MEMO")
    private String memo;

    /**
    * CREATE_USER
    */
    @ApiModelProperty("CREATE_USER(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * CREATE_DATE
    */
    @ApiModelProperty("CREATE_DATE(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("CREATE_DATE开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("CREATE_DATE结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * UPDATE_USER
    */
    @ApiModelProperty("UPDATE_USER(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * UPDATE_DATE
    */
    @ApiModelProperty("UPDATE_DATE(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("UPDATE_DATE开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("UPDATE_DATE结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * CREATE_USER
    */
    @ApiModelProperty("CREATE_USER(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * UPDATE_USER
    */
    @ApiModelProperty("UPDATE_USER(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public FzgjBdServiceItemPagesPtEntity() {
        this.setSubClazz(FzgjBdServiceItemPagesPtEntity.class);
    }

    public FzgjBdServiceItemPagesPtEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public FzgjBdServiceItemPagesPtEntity setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = serviceItemCode;
        this.nodifySetFiled("serviceItemCode", serviceItemCode);
        return this;
    }

    public String getServiceItemCode() {
        this.nodifyGetFiled("serviceItemCode");
        return serviceItemCode;
    }

    public FzgjBdServiceItemPagesPtEntity setCode(String code) {
        this.code = code;
        this.nodifySetFiled("code", code);
        return this;
    }

    public String getCode() {
        this.nodifyGetFiled("code");
        return code;
    }

    public FzgjBdServiceItemPagesPtEntity setName(String name) {
        this.name = name;
        this.nodifySetFiled("name", name);
        return this;
    }

    public String getName() {
        this.nodifyGetFiled("name");
        return name;
    }

    public FzgjBdServiceItemPagesPtEntity setSeq(Long seq) {
        this.seq = seq;
        this.nodifySetFiled("seq", seq);
        return this;
    }

    public Long getSeq() {
        this.nodifyGetFiled("seq");
        return seq;
    }

    public FzgjBdServiceItemPagesPtEntity setStatus(String status) {
        this.status = status;
        this.nodifySetFiled("status", status);
        return this;
    }

    public String getStatus() {
        this.nodifyGetFiled("status");
        return status;
    }

    public FzgjBdServiceItemPagesPtEntity setPageUrl(String pageUrl) {
        this.pageUrl = pageUrl;
        this.nodifySetFiled("pageUrl", pageUrl);
        return this;
    }

    public String getPageUrl() {
        this.nodifyGetFiled("pageUrl");
        return pageUrl;
    }

    public FzgjBdServiceItemPagesPtEntity setMemo(String memo) {
        this.memo = memo;
        this.nodifySetFiled("memo", memo);
        return this;
    }

    public String getMemo() {
        this.nodifyGetFiled("memo");
        return memo;
    }

    public FzgjBdServiceItemPagesPtEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public FzgjBdServiceItemPagesPtEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public FzgjBdServiceItemPagesPtEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public FzgjBdServiceItemPagesPtEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public FzgjBdServiceItemPagesPtEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public FzgjBdServiceItemPagesPtEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public FzgjBdServiceItemPagesPtEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public FzgjBdServiceItemPagesPtEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public FzgjBdServiceItemPagesPtEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public FzgjBdServiceItemPagesPtEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

}
