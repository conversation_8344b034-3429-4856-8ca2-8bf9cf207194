package com.eci.project.fzgjBdBill.entity;

import com.eci.common.DictField;
import com.eci.common.ZsrBaseEntity;
import lombok.Data;
import java.time.LocalDateTime; // Or java.sql.Timestamp if preferred for direct JDBC mapping

/**
 * Represents data retrieved from the FZGJ_BD_PRODUCT table
 * based on the provided SQL query.
 */
@Data
public class FzgjBdProductDto extends ZsrBaseEntity {

    private String guid;
    private String code;
    private String name;
    @DictField(queryKey = "YNKey")
    private String status;
    private String seq; // Assuming SEQ is a whole number
    private String xzfaNo;
    private String memo;
    private String createUser;
    private String createDate; // Java 8 Date/Time API
    private String updateUser;
    private String updateDate; // Java 8 Date/Time API
    private String opType;
    private String companyCode;
    private String createUserName;
    private String updateUserName;
    private String companyName;
    private String groupCode;
    private String groupName;
    private String enName;
    @DictField(queryKey = "FZGJ_BD_BILL_Key")
    private String billCode;
    private String isFwxmEdit; // Assuming this is stored as a char/varchar like 'Y'/'N'
    private String serviceTypeName; // Result of the WM_CONCAT subquery
    private String opTypeName;      // Result of the name lookup subquery
    @DictField(queryKey = "YNKey")
    private String isStandard;    // Assuming this is stored as a char/varchar like 'Y'/'N'

//    @DictField(queryKey = "CRM_CUSTOMER_Key")
    private String customerCode;

//    private String statusName;      // Result of FZGJ_NAME function
//    private String billCodeName;    // Result of FZGJ_NAME function
//    private String isStandardName;  // Result of FZGJ_NAME function
//    private String customerCodeName;// Result of FZGJ_NAMECOM function
    private String isChecked;// isChecked

}