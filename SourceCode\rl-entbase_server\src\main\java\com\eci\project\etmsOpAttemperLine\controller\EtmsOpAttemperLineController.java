package com.eci.project.etmsOpAttemperLine.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.etmsOpAttemperLine.service.EtmsOpAttemperLineService;
import com.eci.project.etmsOpAttemperLine.entity.EtmsOpAttemperLineEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 托运线路站点信息Controller
*
* @<NAME_EMAIL>
* @date 2025-06-13
*/
@Api(tags = "托运线路站点信息")
@RestController
@RequestMapping("/etmsOpAttemperLine")
public class EtmsOpAttemperLineController extends EciBaseController {

    @Autowired
    private EtmsOpAttemperLineService etmsOpAttemperLineService;


    @ApiOperation("托运线路站点信息:保存")
    @EciLog(title = "托运线路站点信息:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody EtmsOpAttemperLineEntity entity){
        EtmsOpAttemperLineEntity etmsOpAttemperLineEntity =etmsOpAttemperLineService.save(entity);
        return ResponseMsgUtil.success(10001,etmsOpAttemperLineEntity);
    }


    @ApiOperation("托运线路站点信息:查询列表")
    @EciLog(title = "托运线路站点信息:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody EtmsOpAttemperLineEntity entity){
        List<EtmsOpAttemperLineEntity> etmsOpAttemperLineEntities = etmsOpAttemperLineService.selectList(entity);
        return ResponseMsgUtil.success(10001,etmsOpAttemperLineEntities);
    }


    @ApiOperation("托运线路站点信息:分页查询列表")
    @EciLog(title = "托运线路站点信息:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody EtmsOpAttemperLineEntity entity){
        TgPageInfo tgPageInfo = etmsOpAttemperLineService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("托运线路站点信息:根据ID查一条")
    @EciLog(title = "托运线路站点信息:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody EtmsOpAttemperLineEntity entity){
        EtmsOpAttemperLineEntity  etmsOpAttemperLineEntity = etmsOpAttemperLineService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,etmsOpAttemperLineEntity);
    }


    @ApiOperation("托运线路站点信息:根据ID删除一条")
    @EciLog(title = "托运线路站点信息:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody EtmsOpAttemperLineEntity entity){
        int count = etmsOpAttemperLineService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("托运线路站点信息:根据ID字符串删除多条")
    @EciLog(title = "托运线路站点信息:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody EtmsOpAttemperLineEntity entity) {
        int count = etmsOpAttemperLineService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}