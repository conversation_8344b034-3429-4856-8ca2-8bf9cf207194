package com.eci.project.omsOrder.entity;

import com.eci.project.omsOrderPre.entity.OmsOrderPreEntity;
import org.omg.CORBA.PUBLIC_MEMBER;

import java.util.List;

/**
 * @ClassName: ResOmsOrderTracePageEntity
 * @Author: guangyan.mei
 * @Date: 2025/5/7 14:48
 * @Description: TODO
 */
public class ResOmsOrderTracePageEntity  extends OmsOrderEntity {

    // 作业状态
    public  String uiNrwZyzt;
    // 业务产品/项目
    public  String productCodeName;
    // 业务类型
    public  String opTypeName;
    // 委托方
    public  String consigneeCodeName;

    // 全部作业完成
    public String  opCompleteOkn;

    // 部分作业完成
    public String  opCompleteOknPart;

    // 是否异常
    public String isException;

    public List<ExceptionStatusListEntity> exceptionList;

    public List<ExceptionStatusListEntity> getExceptionList() {
        return exceptionList;
    }

    public void setExceptionList(List<ExceptionStatusListEntity> exceptionList) {
        this.exceptionList = exceptionList;
    }


    public String getOpCompleteOkn() {
        return opCompleteOkn;
    }

    public void setOpCompleteOkn(String opCompleteOkn) {
        this.opCompleteOkn = opCompleteOkn;
    }

    public String getOpCompleteOknPart() {
        return opCompleteOknPart;
    }

    public void setOpCompleteOknPart(String opCompleteOknPart) {
        this.opCompleteOknPart = opCompleteOknPart;
    }
}
