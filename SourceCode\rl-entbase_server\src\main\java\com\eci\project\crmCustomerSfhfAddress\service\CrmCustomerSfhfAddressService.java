package com.eci.project.crmCustomerSfhfAddress.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ExceptionUtils;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.crmCustomerSfhfAddress.dao.CrmCustomerSfhfAddressDao;
import com.eci.project.crmCustomerSfhfAddress.entity.CrmCustomerSfhfAddressEntity;
import com.eci.project.crmCustomerSfhfAddress.validate.CrmCustomerSfhfAddressVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 业务伙伴收发货方常用地区Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-05-20
*/
@Service
@Slf4j
public class CrmCustomerSfhfAddressService implements EciBaseService<CrmCustomerSfhfAddressEntity> {

    @Autowired
    private CrmCustomerSfhfAddressDao crmCustomerSfhfAddressDao;

    @Autowired
    private CrmCustomerSfhfAddressVal crmCustomerSfhfAddressVal;


    @Override
    public TgPageInfo queryPageList(CrmCustomerSfhfAddressEntity entity) {
        EciQuery<CrmCustomerSfhfAddressEntity> eciQuery = EciQuery.buildQuery(entity);
        List<CrmCustomerSfhfAddressEntity> entities = crmCustomerSfhfAddressDao.selectlist(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public CrmCustomerSfhfAddressEntity save(CrmCustomerSfhfAddressEntity entity) {
        // 返回实体对象
        CrmCustomerSfhfAddressEntity crmCustomerSfhfAddressEntity = null;
        crmCustomerSfhfAddressVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            crmCustomerSfhfAddressEntity = crmCustomerSfhfAddressDao.insertOne(entity);

        }else{

            crmCustomerSfhfAddressEntity = crmCustomerSfhfAddressDao.updateByEntityId(entity);

        }
        return crmCustomerSfhfAddressEntity;
    }

    @Override
    public List<CrmCustomerSfhfAddressEntity> selectList(CrmCustomerSfhfAddressEntity entity) {
        return crmCustomerSfhfAddressDao.selectList(entity);
    }

    @Override
    public CrmCustomerSfhfAddressEntity selectOneById(Serializable id) {
        QueryWrapper query =new QueryWrapper();
        query.eq("GUID",id);
        List<CrmCustomerSfhfAddressEntity> entities = crmCustomerSfhfAddressDao.selectlist(query);
        if (CollectionUtils.isNotEmpty(entities)) {
            if (entities.size() != 1) {
                throw ExceptionUtils.mpe("One record is expected, but the query result is multiple records", new Object[0]);
            } else {
                return entities.get(0);
            }
        } else {
            return null;
        }
    }




    @Override
    public void insertBatch(List<CrmCustomerSfhfAddressEntity> list) {
        crmCustomerSfhfAddressDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return crmCustomerSfhfAddressDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return crmCustomerSfhfAddressDao.deleteById(id);
    }

}