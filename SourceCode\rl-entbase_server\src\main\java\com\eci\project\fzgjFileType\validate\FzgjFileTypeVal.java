package com.eci.project.fzgjFileType.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjFileType.entity.FzgjFileTypeEntity;

import org.springframework.stereotype.Service;


/**
* 附件类型Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-11
*/
@Service
public class FzgjFileTypeVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjFileTypeEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjFileTypeEntity entity, BusinessType businessType) {

    }

}
