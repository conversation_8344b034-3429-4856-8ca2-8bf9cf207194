package com.eci.project.etmsBdUnit.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.etmsBdUnit.dao.EtmsBdUnitDao;
import com.eci.project.etmsBdUnit.entity.EtmsBdUnitEntity;
import com.eci.project.etmsBdUnit.validate.EtmsBdUnitVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 计量单位Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-03-21
*/
@Service
@Slf4j
public class EtmsBdUnitService implements EciBaseService<EtmsBdUnitEntity> {

    @Autowired
    private EtmsBdUnitDao etmsBdUnitDao;

    @Autowired
    private EtmsBdUnitVal etmsBdUnitVal;


    @Override
    public TgPageInfo queryPageList(EtmsBdUnitEntity entity) {
        EciQuery<EtmsBdUnitEntity> eciQuery = EciQuery.buildQuery(entity);
        List<EtmsBdUnitEntity> entities = etmsBdUnitDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public EtmsBdUnitEntity save(EtmsBdUnitEntity entity) {
        // 返回实体对象
        EtmsBdUnitEntity etmsBdUnitEntity = null;
        etmsBdUnitVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            etmsBdUnitEntity = etmsBdUnitDao.insertOne(entity);

        }else{

            etmsBdUnitEntity = etmsBdUnitDao.updateByEntityId(entity);

        }
        return etmsBdUnitEntity;
    }

    @Override
    public List<EtmsBdUnitEntity> selectList(EtmsBdUnitEntity entity) {
        return etmsBdUnitDao.selectList(entity);
    }

    @Override
    public EtmsBdUnitEntity selectOneById(Serializable id) {
        return etmsBdUnitDao.selectById(id);
    }


    @Override
    public void insertBatch(List<EtmsBdUnitEntity> list) {
        etmsBdUnitDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return etmsBdUnitDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return etmsBdUnitDao.deleteById(id);
    }

}