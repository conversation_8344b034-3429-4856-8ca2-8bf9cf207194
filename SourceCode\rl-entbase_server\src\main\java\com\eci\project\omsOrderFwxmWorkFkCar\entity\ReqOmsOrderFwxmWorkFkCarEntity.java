package com.eci.project.omsOrderFwxmWorkFkCar.entity;

import com.eci.project.omsOrderFwxmWorkFk.entity.OmsOrderFwxmWorkFkEntity;
import com.eci.project.omsOrderFwxmWorkFkZzfw.entity.OmsOrderFwxmWorkFkZzfwEntity;
import com.eci.wu.core.EntityBase;

import java.util.List;

/**
 * @ClassName: ReqOmsOrderFwxmWorkFkCarEntity
 * @Author: guangyan.mei
 * @Date: 2025/5/22 9:41
 * @Description: TODO
 */
public class ReqOmsOrderFwxmWorkFkCarEntity {

    public OmsOrderFwxmWorkFkEntity getFkEntity() {
        return fkEntity;
    }

    public void setFkEntity(OmsOrderFwxmWorkFkEntity fkEntity) {
        this.fkEntity = fkEntity;
    }

    public List<OmsOrderFwxmWorkFkCarEntity> getFkCarList() {
        return fkCarList;
    }

    public void setFkCarList(List<OmsOrderFwxmWorkFkCarEntity> fkCarList) {
        this.fkCarList = fkCarList;
    }

    public List<OmsOrderFwxmWorkFkZzfwEntity> getDateQtfwList() {
        return dateQtfwList;
    }

    public void setDateQtfwList(List<OmsOrderFwxmWorkFkZzfwEntity> dateQtfwList) {
        this.dateQtfwList = dateQtfwList;
    }

    // 反馈-作业说明
    public OmsOrderFwxmWorkFkEntity fkEntity;

    // 反馈-车辆信息
    public List<OmsOrderFwxmWorkFkCarEntity> fkCarList;

    // 反馈-其他作业事项
    public List<OmsOrderFwxmWorkFkZzfwEntity> dateQtfwList;
}
