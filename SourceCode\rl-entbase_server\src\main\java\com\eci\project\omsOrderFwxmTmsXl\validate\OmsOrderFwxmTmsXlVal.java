package com.eci.project.omsOrderFwxmTmsXl.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.omsOrderFwxmTmsXl.entity.OmsOrderFwxmTmsXlEntity;

import org.springframework.stereotype.Service;


/**
* 委托内容-委托线路Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-14
*/
@Service
public class OmsOrderFwxmTmsXlVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(OmsOrderFwxmTmsXlEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(OmsOrderFwxmTmsXlEntity entity, BusinessType businessType) {

    }

}
