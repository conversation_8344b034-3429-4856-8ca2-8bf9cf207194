package com.eci.project.fzgjBdSeaPort.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjBdSeaPort.entity.FzgjBdSeaPortEntity;

import com.eci.sso.role.entity.UserContext;
import org.springframework.stereotype.Service;

import java.util.Date;


/**
* 海运港口Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-03-14
*/
@Service
public class FzgjBdSeaPortVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjBdSeaPortEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjBdSeaPortEntity entity, BusinessType businessType) {
        entity.setUpdateUser(UserContext.getUserInfo().getUserId());
        entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        entity.setUpdateDate(new Date());
        if(businessType==BusinessType.INSERT){
            entity.setCreateUser(UserContext.getUserInfo().getUserId());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setCreateDate(new Date());
        }
    }

}
