package com.eci.project.etmsBdDriverInsurance.service;

import com.eci.common.db.DBHelper;
import com.eci.common.util.DateUtils;
import com.eci.common.util.StringUtils;
import com.eci.common.utils.ExportUtils.ExcelProcess;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.ActionType;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.etmsBdDriverInsurance.dao.EtmsBdDriverInsuranceDao;
import com.eci.project.etmsBdDriverInsurance.entity.EtmsBdDriverInsuranceEntity;
import com.eci.project.etmsBdDriverInsurance.entity.EtmsBdDriverInsuranceSearchEntity;
import com.eci.project.etmsBdDriverInsurance.validate.EtmsBdDriverInsuranceVal;

import com.eci.project.etmsBdTruckInsuranceQz.entity.EtmsBdTruckInsuranceQzSearchEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
* 司机保险管理Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-27
*/
@Service
@Slf4j
public class EtmsBdDriverInsuranceService implements EciBaseService<EtmsBdDriverInsuranceEntity> {

    @Autowired
    private EtmsBdDriverInsuranceDao etmsBdDriverInsuranceDao;

    @Autowired
    private EtmsBdDriverInsuranceVal etmsBdDriverInsuranceVal;


    @Override
    public TgPageInfo queryPageList(EtmsBdDriverInsuranceEntity entity) {
        EciQuery<EtmsBdDriverInsuranceEntity> eciQuery = EciQuery.buildQuery(entity);
        List<EtmsBdDriverInsuranceEntity> entities = etmsBdDriverInsuranceDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }

    public TgPageInfo queryInsurancePageList(EtmsBdDriverInsuranceSearchEntity entity){
        StringBuilder stringBuilder=new StringBuilder();
        stringBuilder.append("select A.GUID,B.NAME,A.INSURANCE_TYPE,A.POLICY_NO,A.POLICYHOLDER,A.INSURER,A.AMOUNT,A.START_DATE,A.END_DATE,A.INSURANCE_TERM,A.STATUS,A.COMPANY_NAME,A.CREATE_DATE from \n" +
                "ETMS_BD_DRIVER_INSURANCE A INNER JOIN ETMS_BD_DRIVER B ON A.DRIVER_GUID=B.GUID where 1=1");
        if(StringUtils.hasValue(entity.getName())){
            stringBuilder.append(" AND B.NAME like '%"+entity.getName()+"%'");
        }
        if(StringUtils.hasValue(entity.getInsurer())){
            stringBuilder.append(" AND A.INSURER like '%"+entity.getInsurer()+"%'");
        }
        if(StringUtils.hasValue(entity.getPolicyNo())){
            stringBuilder.append(" AND A.POLICY_NO like '%"+entity.getPolicyNo()+"%'");
        }
        if(StringUtils.hasValue(entity.getCompanyName())){
            stringBuilder.append(" AND A.COMPANY_CODE like '%"+entity.getCompanyName()+"%'");
        }
        if (entity.getCreateDateStart()!=null) {
            stringBuilder.append(" AND A.CREATE_DATE >= TO_DATE('" + DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss",entity.getCreateDateStart()) + "', 'yyyy-mm-dd hh24:mi:ss')");
        }
        if (entity.getCreateDateEnd()!=null) {
            stringBuilder.append(" AND A.CREATE_DATE <= TO_DATE('" + DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss",entity.getCreateDateEnd()) + "', 'yyyy-mm-dd hh24:mi:ss')");
        }
        if (entity.getStartDateStart()!=null) {
            stringBuilder.append(" AND B.START_DATE >= TO_DATE('" + DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss",entity.getStartDateStart()) + "', 'yyyy-mm-dd hh24:mi:ss')");
        }
        if (entity.getStartDateEnd()!=null) {
            stringBuilder.append(" AND B.START_DATE <= TO_DATE('" + DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss",entity.getStartDateEnd()) + "', 'yyyy-mm-dd hh24:mi:ss')");
        }
        if (entity.getEndDateStart()!=null) {
            stringBuilder.append(" AND B.END_DATE >= TO_DATE('" + DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss",entity.getEndDateStart()) + "', 'yyyy-mm-dd hh24:mi:ss')");
        }
        if (entity.getEndDateEnd()!=null) {
            stringBuilder.append(" AND B.END_DATE <= TO_DATE('" + DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss",entity.getEndDateEnd()) + "', 'yyyy-mm-dd hh24:mi:ss')");
        }
        if (BllContext.getBusinessType() == BusinessType.EXPORT) {
            if (BllContext.getRequestEntity().getActionType() == ActionType.ASYNC) {
                etmsBdDriverInsuranceDao.asyncExportDefaultExcel(()->{
                    List<EtmsBdDriverInsuranceSearchEntity> entities = DBHelper.selectList(stringBuilder.toString(), EtmsBdDriverInsuranceSearchEntity.class);
                    entities.forEach(qzSearchEntity -> {
                        if (StringUtils.hasValue(qzSearchEntity.getEndDate()) && DateUtils.parseDate(qzSearchEntity.getEndDate().substring(0,19)).compareTo(DateUtils.parseDate(DateUtils.getDate())) >= 0) {
                            qzSearchEntity.setStatus("有效");
                        } else {
                            qzSearchEntity.setStatus("无效");
                        }
                        qzSearchEntity.setStartDate(qzSearchEntity.getStartDate().substring(0, 10));
                        qzSearchEntity.setEndDate(qzSearchEntity.getEndDate().substring(0, 10));
                        qzSearchEntity.setModMark("否");
                    });
                    return entities;
                }, ExcelProcess.BuildConfig("车辆保险管理", EtmsBdDriverInsuranceSearchEntity.class));
            } else {
                etmsBdDriverInsuranceDao.exportDefaultExcel(() -> {
                    List<EtmsBdDriverInsuranceSearchEntity> entities = DBHelper.selectList(stringBuilder.toString(), EtmsBdDriverInsuranceSearchEntity.class);
                    entities.forEach(qzSearchEntity -> {
                        if (StringUtils.hasValue(qzSearchEntity.getEndDate()) && DateUtils.parseDate(qzSearchEntity.getEndDate().substring(0,19)).compareTo(DateUtils.parseDate(DateUtils.getDate())) >= 0) {
                            qzSearchEntity.setStatus("有效");
                        } else {
                            qzSearchEntity.setStatus("无效");
                        }
                        qzSearchEntity.setStartDate(qzSearchEntity.getStartDate().substring(0, 10));
                        qzSearchEntity.setEndDate(qzSearchEntity.getEndDate().substring(0, 10));
                        qzSearchEntity.setModMark("否");
                    });
                    return entities;
                }, ExcelProcess.BuildConfig("司机证件管理", EtmsBdDriverInsuranceSearchEntity.class));
            }
            return new TgPageInfo<>();
        }else{
            startPage();
            List<EtmsBdDriverInsuranceSearchEntity> entities = DBHelper.selectList(stringBuilder.toString(), EtmsBdDriverInsuranceSearchEntity.class);
            entities.forEach(qzSearchEntity -> {
                Date now=DateUtils.parseDate(DateUtils.getDate());
                if (StringUtils.hasValue(qzSearchEntity.getEndDate()) && DateUtils.parseDate(qzSearchEntity.getEndDate().substring(0, 19)).compareTo(now) >= 0) {
                    qzSearchEntity.setStatus("有效");
                } else {
                    qzSearchEntity.setStatus("无效");
                }
                qzSearchEntity.setModMark("否");
            });
            return EciQuery.getPageInfo(entities);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public EtmsBdDriverInsuranceEntity save(EtmsBdDriverInsuranceEntity entity) {
        // 返回实体对象
        EtmsBdDriverInsuranceEntity etmsBdDriverInsuranceEntity = null;
        etmsBdDriverInsuranceVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            etmsBdDriverInsuranceEntity = etmsBdDriverInsuranceDao.insertOne(entity);

        }else{

            etmsBdDriverInsuranceEntity = etmsBdDriverInsuranceDao.updateByEntityId(entity);

        }
        return etmsBdDriverInsuranceEntity;
    }

    @Override
    public List<EtmsBdDriverInsuranceEntity> selectList(EtmsBdDriverInsuranceEntity entity) {
        return etmsBdDriverInsuranceDao.selectList(entity);
    }

    @Override
    public EtmsBdDriverInsuranceEntity selectOneById(Serializable id) {
        return etmsBdDriverInsuranceDao.selectById(id);
    }


    @Override
    public void insertBatch(List<EtmsBdDriverInsuranceEntity> list) {
        etmsBdDriverInsuranceDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return etmsBdDriverInsuranceDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return etmsBdDriverInsuranceDao.deleteById(id);
    }

}