package com.eci.project.fzgjCrmEnterpriseService.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjCrmEnterpriseService.service.FzgjCrmEnterpriseServiceService;
import com.eci.project.fzgjCrmEnterpriseService.entity.FzgjCrmEnterpriseServiceEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 注册企业服务Controller
*
* @<NAME_EMAIL>
* @date 2025-03-13
*/
@Api(tags = "注册企业服务")
@RestController
@RequestMapping("/fzgjCrmEnterpriseService")
public class FzgjCrmEnterpriseServiceController extends EciBaseController {

    @Autowired
    private FzgjCrmEnterpriseServiceService fzgjCrmEnterpriseServiceService;


    @ApiOperation("注册企业服务:保存")
    @EciLog(title = "注册企业服务:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody FzgjCrmEnterpriseServiceEntity entity){
        FzgjCrmEnterpriseServiceEntity fzgjCrmEnterpriseServiceEntity =fzgjCrmEnterpriseServiceService.save(entity);
        return ResponseMsgUtil.success(10001,fzgjCrmEnterpriseServiceEntity);
    }


    @ApiOperation("注册企业服务:查询列表")
    @EciLog(title = "注册企业服务:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjCrmEnterpriseServiceEntity entity){
        List<FzgjCrmEnterpriseServiceEntity> fzgjCrmEnterpriseServiceEntities = fzgjCrmEnterpriseServiceService.selectList(entity);
        return ResponseMsgUtil.success(10001,fzgjCrmEnterpriseServiceEntities);
    }


    @ApiOperation("注册企业服务:分页查询列表")
    @EciLog(title = "注册企业服务:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjCrmEnterpriseServiceEntity entity){
        TgPageInfo tgPageInfo = fzgjCrmEnterpriseServiceService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("注册企业服务:根据ID查一条")
    @EciLog(title = "注册企业服务:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjCrmEnterpriseServiceEntity entity){
        FzgjCrmEnterpriseServiceEntity  fzgjCrmEnterpriseServiceEntity = fzgjCrmEnterpriseServiceService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,fzgjCrmEnterpriseServiceEntity);
    }


    @ApiOperation("注册企业服务:根据ID删除一条")
    @EciLog(title = "注册企业服务:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjCrmEnterpriseServiceEntity entity){
        int count = fzgjCrmEnterpriseServiceService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("注册企业服务:根据ID字符串删除多条")
    @EciLog(title = "注册企业服务:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjCrmEnterpriseServiceEntity entity) {
        int count = fzgjCrmEnterpriseServiceService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}