package com.eci.project.etmsBdTruckInsuranceQz.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.etmsBdTruckInsuranceQz.entity.EtmsBdTruckInsuranceQzSearchEntity;
import com.eci.project.etmsBdTruckInsuranceQz.service.EtmsBdTruckInsuranceQzService;
import com.eci.project.etmsBdTruckInsuranceQz.entity.EtmsBdTruckInsuranceQzEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 车辆保险历史Controller
*
* @<NAME_EMAIL>
* @date 2025-04-15
*/
@Api(tags = "车辆保险历史")
@RestController
@RequestMapping("/etmsBdTruckInsuranceQz")
public class EtmsBdTruckInsuranceQzController extends EciBaseController {

    @Autowired
    private EtmsBdTruckInsuranceQzService etmsBdTruckInsuranceQzService;


    @ApiOperation("车辆保险历史:保存")
    @EciLog(title = "车辆保险历史:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody EtmsBdTruckInsuranceQzEntity entity){
        EtmsBdTruckInsuranceQzEntity etmsBdTruckInsuranceQzEntity =etmsBdTruckInsuranceQzService.save(entity);
        return ResponseMsgUtil.success(10001,etmsBdTruckInsuranceQzEntity);
    }


    @ApiOperation("车辆保险历史:查询列表")
    @EciLog(title = "车辆保险历史:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody EtmsBdTruckInsuranceQzEntity entity){
        List<EtmsBdTruckInsuranceQzEntity> etmsBdTruckInsuranceQzEntities = etmsBdTruckInsuranceQzService.selectList(entity);
        return ResponseMsgUtil.success(10001,etmsBdTruckInsuranceQzEntities);
    }


    @ApiOperation("车辆保险历史:分页查询列表")
    @EciLog(title = "车辆保险历史:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody EtmsBdTruckInsuranceQzEntity entity){
        TgPageInfo tgPageInfo = etmsBdTruckInsuranceQzService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("车辆保险历史:根据ID查一条")
    @EciLog(title = "车辆保险历史:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody EtmsBdTruckInsuranceQzEntity entity){
        EtmsBdTruckInsuranceQzEntity  etmsBdTruckInsuranceQzEntity = etmsBdTruckInsuranceQzService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,etmsBdTruckInsuranceQzEntity);
    }


    @ApiOperation("车辆保险历史:根据ID删除一条")
    @EciLog(title = "车辆保险历史:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody EtmsBdTruckInsuranceQzEntity entity){
        int count = etmsBdTruckInsuranceQzService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("车辆保险历史:根据ID字符串删除多条")
    @EciLog(title = "车辆保险历史:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody EtmsBdTruckInsuranceQzEntity entity) {
        int count = etmsBdTruckInsuranceQzService.deleteByList(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }
    @ApiOperation("保险资质审核:车辆保险管理分页查询列表")
    @EciLog(title = "保险资质审核:车辆保险管理分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectInsurancePageList")
    @EciAction()
    public ResponseMsg selectInsurancePageList(@RequestBody EtmsBdTruckInsuranceQzSearchEntity entity){
        TgPageInfo tgPageInfo = etmsBdTruckInsuranceQzService.queryInsurancePageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }

}