package com.eci.project.etmsBdTruckGpsNow.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.etmsBdTruckGpsNow.service.EtmsBdTruckGpsNowService;
import com.eci.project.etmsBdTruckGpsNow.entity.EtmsBdTruckGpsNowEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* Controller
*
* @<NAME_EMAIL>
* @date 2025-04-14
*/
@Api(tags = "")
@RestController
@RequestMapping("/etmsBdTruckGpsNow")
public class EtmsBdTruckGpsNowController extends EciBaseController {

    @Autowired
    private EtmsBdTruckGpsNowService etmsBdTruckGpsNowService;


    @ApiOperation(":保存")
    @EciLog(title = ":新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody EtmsBdTruckGpsNowEntity entity){
        EtmsBdTruckGpsNowEntity etmsBdTruckGpsNowEntity =etmsBdTruckGpsNowService.save(entity);
        return ResponseMsgUtil.success(10001,etmsBdTruckGpsNowEntity);
    }


    @ApiOperation(":查询列表")
    @EciLog(title = ":查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody EtmsBdTruckGpsNowEntity entity){
        List<EtmsBdTruckGpsNowEntity> etmsBdTruckGpsNowEntities = etmsBdTruckGpsNowService.selectList(entity);
        return ResponseMsgUtil.success(10001,etmsBdTruckGpsNowEntities);
    }


    @ApiOperation(":分页查询列表")
    @EciLog(title = ":分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody EtmsBdTruckGpsNowEntity entity){
        TgPageInfo tgPageInfo = etmsBdTruckGpsNowService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation(":根据ID查一条")
    @EciLog(title = ":根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody EtmsBdTruckGpsNowEntity entity){
        EtmsBdTruckGpsNowEntity  etmsBdTruckGpsNowEntity = etmsBdTruckGpsNowService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,etmsBdTruckGpsNowEntity);
    }


    @ApiOperation(":根据ID删除一条")
    @EciLog(title = ":根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody EtmsBdTruckGpsNowEntity entity){
        int count = etmsBdTruckGpsNowService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation(":根据ID字符串删除多条")
    @EciLog(title = ":根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody EtmsBdTruckGpsNowEntity entity) {
        int count = etmsBdTruckGpsNowService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}