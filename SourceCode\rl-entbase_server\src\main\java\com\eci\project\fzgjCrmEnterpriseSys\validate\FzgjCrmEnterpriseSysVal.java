package com.eci.project.fzgjCrmEnterpriseSys.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjCrmEnterpriseSys.entity.FzgjCrmEnterpriseSysEntity;

import org.springframework.stereotype.Service;


/**
* 作业系统-数据归属集团Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-03-13
*/
@Service
public class FzgjCrmEnterpriseSysVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjCrmEnterpriseSysEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjCrmEnterpriseSysEntity entity, BusinessType businessType) {

    }

}
