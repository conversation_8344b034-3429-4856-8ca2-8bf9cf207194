package com.eci.project.fzgjBdFileBiz.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjBdFileBiz.service.FzgjBdFileBizService;
import com.eci.project.fzgjBdFileBiz.entity.FzgjBdFileBizEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 业务附件类型及授权Controller
*
* @<NAME_EMAIL>
* @date 2025-04-11
*/
@Api(tags = "业务附件类型及授权")
@RestController
@RequestMapping("/fzgjBdFileBiz")
public class FzgjBdFileBizController extends EciBaseController {

    @Autowired
    private FzgjBdFileBizService fzgjBdFileBizService;


    @ApiOperation("业务附件类型及授权:保存")
    @EciLog(title = "业务附件类型及授权:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody FzgjBdFileBizEntity entity){
        FzgjBdFileBizEntity fzgjBdFileBizEntity =fzgjBdFileBizService.save(entity);
        return ResponseMsgUtil.success(10001,fzgjBdFileBizEntity);
    }


    @ApiOperation("业务附件类型及授权:查询列表")
    @EciLog(title = "业务附件类型及授权:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjBdFileBizEntity entity){
        List<FzgjBdFileBizEntity> fzgjBdFileBizEntities = fzgjBdFileBizService.selectList(entity);
        return ResponseMsgUtil.success(10001,fzgjBdFileBizEntities);
    }


    @ApiOperation("业务附件类型及授权:分页查询列表")
    @EciLog(title = "业务附件类型及授权:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjBdFileBizEntity entity){
        TgPageInfo tgPageInfo = fzgjBdFileBizService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("业务附件类型及授权:根据ID查一条")
    @EciLog(title = "业务附件类型及授权:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjBdFileBizEntity entity){
        FzgjBdFileBizEntity  fzgjBdFileBizEntity = fzgjBdFileBizService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,fzgjBdFileBizEntity);
    }


    @ApiOperation("业务附件类型及授权:根据ID删除一条")
    @EciLog(title = "业务附件类型及授权:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjBdFileBizEntity entity){
        int count = fzgjBdFileBizService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("业务附件类型及授权:根据ID字符串删除多条")
    @EciLog(title = "业务附件类型及授权:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjBdFileBizEntity entity) {
        int count = fzgjBdFileBizService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}