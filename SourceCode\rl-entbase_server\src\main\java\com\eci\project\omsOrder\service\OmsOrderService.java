package com.eci.project.omsOrder.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.*;
import com.eci.common.db.DBHelper;
import com.eci.common.enums.Enums;
import com.eci.common.enums.OrderEnum;
import com.eci.common.util.StringUtils;
import com.eci.common.utils.CommonLib;
import com.eci.common.validations.ZsrValidationUtil;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.exception.BaseException;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjTaskLimitation.dao.FzgjTaskLimitationDao;
import com.eci.project.fzgjTaskLimitation.entity.FzgjTaskLimitationEntity;
import com.eci.project.omsOrder.dao.OmsOrderDao;
import com.eci.project.omsOrder.entity.OmsOrderEntity;
import com.eci.project.omsOrder.validate.OmsOrderVal;
import com.eci.project.omsOrderFw.dao.OmsOrderFwDao;
import com.eci.project.omsOrderFw.entity.OmsOrderFwEntity;
import com.eci.project.omsOrderFw.service.OmsOrderFwService;
import com.eci.project.omsOrderFwxm.dao.OmsOrderFwxmDao;
import com.eci.project.omsOrderFwxm.entity.OmsOrderFwxmEntity;
import com.eci.project.omsOrderFwxmWork.dao.OmsOrderFwxmWorkDao;
import com.eci.project.omsOrderFwxmWork.entity.OmsOrderFwxmWorkEntity;
import com.eci.project.omsOrderFwxmWorkTrace.dao.OmsOrderFwxmWorkTraceDao;
import com.eci.project.omsOrderFwxmWorkTrace.entity.OmsOrderFwxmWorkTraceEntity;
import com.eci.project.omsOrderJdmb.entity.OmsOrderJdmbEntity;
import com.eci.project.omsOrderJdmb.service.OmsOrderJdmbService;
import com.eci.project.omsOrderLog.entity.OmsOrderLogEntity;
import com.eci.project.omsOrderLog.service.OmsOrderLogService;
import com.eci.project.omsOrderPre.dao.OmsOrderPreDao;
import com.eci.project.omsOrderPre.entity.OmsOrderPreEntity;
import com.eci.sso.login.entity.UserInfo;
import com.eci.sso.role.entity.UserContext;
import com.eci.wu.core.EntityBase;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 订单表Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-04-14
 */
@Service
@Slf4j
public class OmsOrderService implements EciBaseService<OmsOrderEntity> {

    CommonLib cmn = CommonLib.getInstance();

    @Autowired
    private OmsOrderDao omsOrderDao;

    @Autowired
    private OmsOrderVal omsOrderVal;

    @Autowired
    private OmsOrderFwService omsOrderFwService;

    @Autowired
    private OmsOrderJdmbService omsOrderJdmbService;

    @Autowired
    private OmsOrderPreDao omsOrderPreDao;

    /**
     * 订单的服务类型
     */
    @Autowired
    private OmsOrderFwDao omsOrderFwDao;

    /**
     * 订单服务项目
     */
    @Autowired
    private OmsOrderFwxmDao omsOrderFwxmDao;

    /**
     * 扩展类
     **/
    @Autowired
    private Extensions extensions;
    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Override
    public TgPageInfo queryPageList(OmsOrderEntity entity) {
        EciQuery<OmsOrderEntity> eciQuery = EciQuery.buildQuery(entity);
        List<OmsOrderEntity> entities = omsOrderDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }

    @SneakyThrows
    @Override
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderEntity save(OmsOrderEntity entity) {
        // 缓存用户信息，避免多次调用
        UserInfo userInfo = UserContext.getUserInfo();
        String trueName = userInfo.getTrueName();
        String companyCode = userInfo.getCompanyCode();
        ZsrValidationUtil.validation(entity);
        OmsOrderEntity omsOrderEntity = null;


        if (!Zsr.String.IsNullOrWhiteSpace(entity.getOrderNo()) && entity.getOrderNo().startsWith(ZsrGloableConfig.jieDanMoBanPrefixCode)) {
            // 从订单模板新增的订单
            omsOrderEntity = CreateOrderInfo(entity, trueName, companyCode);
        } else if (BllContext.getBusinessType() == BusinessType.INSERT || Zsr.String.IsNullOrWhiteSpace(entity.getOrderNo())) {
            // 直接从订单管理界面新增的订单
            omsOrderEntity = CreateOrderInfo(entity, trueName, companyCode);
        } else {
            // 更新逻辑
            // 1. 更新订单主表信息
            omsOrderEntity = omsOrderDao.updateByEntityId(entity);

            // 2. 更新服务类型
            updateServiceTypes(entity, trueName, companyCode);

            // 3. 更新服务项目
            updateServiceTypesItem(entity, trueName, companyCode);

            // 操作日志记录
            OmsOrderLogEntity logEntity = new OmsOrderLogEntity();
            logEntity.setOrderNo(entity.getOrderNo());
            logEntity.setOperName("修改订单");
            omsOrderLogService.writeLog(logEntity);
        }

        return omsOrderEntity;
    }

    /**
     * 创建订单信息
     *
     * @param entity
     * @param trueName
     * @param companyCode
     * @return
     */
    private OmsOrderEntity CreateOrderInfo(OmsOrderEntity entity, String trueName, String companyCode) {
        OmsOrderEntity omsOrderEntity;
        // 使用订单号创建生成器生成订单号，1表示手动创建的订单
        entity.setOrderNo(NoManager.createOrderNo(OrderEnum.OrderConstants.ORDER_SOURCE_MANUAL.getCode()));
        // 新增的订单，初始状态为暂存
        entity.setStatus(Enums.OrderStatus.ZC.getCode());
        // 新增订单，订单执行阶段初始化状态为暂存
        entity.setStage(OrderEnum.OrderStage.ZC.getCode());

        entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
        entity.setGroupName(UserContext.getUserInfo().getCompanyName());
        omsOrderEntity = omsOrderDao.insertOne(entity);

        // 2. 更新服务类型
        handleServiceTypes(omsOrderEntity, trueName, companyCode);

        // 3. 更新服务项目
        handleServiceTypesItem(entity, trueName, companyCode);

        // 操作日志记录
        OmsOrderLogEntity logEntity = new OmsOrderLogEntity();
        logEntity.setOrderNo(entity.getOrderNo());
        logEntity.setOperName("新增订单");
        logEntity.setBizType(OrderEnum.OrderLogStatus.ZC.getCode());
        omsOrderLogService.writeLog(logEntity);
        return omsOrderEntity;
    }


    /**
     * 保存订单模板
     *
     * @param entity
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderEntity saveTemplate(OmsOrderEntity entity) {
        if (Zsr.String.IsNullOrWhiteSpace(entity.getOpType())) {
            throw new BaseException("业务类型未填写");
        }
        if (Zsr.String.IsNullOrWhiteSpace(entity.getConsigneeCode())) {
            throw new BaseException("请先选择委托方");
        }
        if (Zsr.String.IsNullOrWhiteSpace(entity.getProductCode())) {
            throw new BaseException("请选择业务产品/项目");
        }
        // 缓存用户信息，避免多次调用
        UserInfo userInfo = UserContext.getUserInfo();
        String trueName = userInfo.getTrueName();
        String companyCode = userInfo.getCompanyCode();

        OmsOrderEntity omsOrderEntity = null;
        String orderTemplateNo = ZsrGloableConfig.jieDanMoBanPrefixCode + NoManager.createOrderNo(OrderEnum.OrderConstants.ORDER_SOURCE_TEMPLATE.getCode());
        // 使用订单号创建生成器生成订单号，1表示手动创建的订单
        entity.setOrderNo(orderTemplateNo);
        // 新增的订单，初始状态为null
        entity.setStatus(null);
        // 新增订单，订单执行阶段初始化状态为null
        entity.setStage(null);

        // 把数据保存到订单模板
        OmsOrderJdmbEntity omsOrderJdmbEntity = omsOrderJdmbService.save(entity, true);
        entity.setOrderNo(omsOrderJdmbEntity.getOrderNo());
        // 订单模板数据保存好，在保存订单数据
        entity.setSysOrderBatch(null);

        //  #region T029188 在订单中点击“生成接单模板”时不带必填勾选项（确认接单必填项和协作分发必填项）；
//        entity.IS_QRJD = CommonData.YNStatus.N;
//        entity.IS_XZFF = CommonData.YNStatus.N;
//        entity.PRE_ORDER_NO = string.Empty;
//        entity.UDF1 = string.Empty;


        omsOrderEntity = omsOrderDao.insertOne(entity);

        // 2. 更新服务类型
        handleServiceTypes(omsOrderEntity, trueName, companyCode);

        // 3. 更新服务项目
        handleServiceTypesItem(entity, trueName, companyCode);

        return omsOrderEntity;
    }


    /**
     * 新增的时候，增加服务项目
     *
     * @param entity
     * @param trueName
     * @param companyCode
     */
    private void handleServiceTypesItem(OmsOrderEntity entity, String trueName, String companyCode) {
        String fwlxItem = entity.getFwlxItem();
        if (!Zsr.String.IsNullOrWhiteSpace(fwlxItem)) {
            List<String> fwCodeList = Zsr.String.string2List(fwlxItem);
            String orderNo = entity.getOrderNo();

            fwCodeList.forEach(fw -> {
                OmsOrderFwxmEntity fwEntity = omsOrderFwxmDao.select()
                        .eq(OmsOrderFwxmEntity::getFwlxCode, fw)
                        .eq(OmsOrderFwxmEntity::getOrderNo, orderNo).one();

                if (fwEntity == null) {
                    OmsOrderFwxmEntity fwxmEntity = new OmsOrderFwxmEntity();
                    fwxmEntity.setOrderNo(orderNo);
                    fwxmEntity.setFwlxCode(Zsr.String.safeSubstring(fw, 3));
                    fwxmEntity.setFwxmCode(fw);
                    fwxmEntity.setCreateDate(new Date());
                    fwxmEntity.setCreateUser(trueName);
                    fwxmEntity.setNodeCode(companyCode);
                    fwxmEntity.setCompanyCode(companyCode);
                    fwxmEntity.setGroupCode(companyCode);
                    omsOrderFwxmDao.insertOne(fwxmEntity);
                }
            });
        }
    }

    /**
     * 新增的时候，新增服务类型
     *
     * @param entity
     * @param trueName
     * @param companyCode
     */
    private void handleServiceTypes(OmsOrderEntity entity, String trueName, String companyCode) {
        String fwlxCode = entity.getFwlxCode();
        if (!Zsr.String.IsNullOrWhiteSpace(fwlxCode)) {
            List<String> fwCodeList = Zsr.String.string2List(fwlxCode);
            String orderNo = entity.getOrderNo();

            fwCodeList.forEach(fw -> {
                OmsOrderFwEntity fwEntity = omsOrderFwDao.select()
                        .eq(OmsOrderFwEntity::getFwlxCode, fw)
                        .eq(OmsOrderFwEntity::getOrderNo, orderNo).one();

                if (fwEntity == null) {
                    OmsOrderFwEntity orderFwEntity = new OmsOrderFwEntity();
                    orderFwEntity.setOrderNo(orderNo);
                    orderFwEntity.setFwlxCode(fw);
                    orderFwEntity.setCreateDate(new Date());
                    orderFwEntity.setCreateUser(trueName);
                    orderFwEntity.setNodeCode(companyCode);
                    orderFwEntity.setCompanyCode(companyCode);
                    orderFwEntity.setGroupCode(companyCode);
                    omsOrderFwDao.insertOne(orderFwEntity);
                }
            });
        }
    }

    /**
     * 跟新服务项目
     *
     * @param entity
     * @param trueName
     * @param companyCode
     */
    private void updateServiceTypesItem(OmsOrderEntity entity, String trueName, String companyCode) {
        String fwlxItem = entity.getFwlxItem();
        if (Zsr.String.IsNullOrWhiteSpace(fwlxItem)) {
            return;
        }

        String orderNo = entity.getOrderNo();

        // 查询已有服务项
        List<OmsOrderFwxmEntity> existingFwList = omsOrderFwxmDao.select()
                .eq(OmsOrderFwxmEntity::getOrderNo, orderNo)
                .list();

        // 新传入的 fwxmCode 列表
        List<String> newFwCodeList = Zsr.String.string2List(fwlxItem);

        // 已有的 fwxmCode 集合
        Set<String> existingFwCodeSet = existingFwList.stream()
                .map(OmsOrderFwxmEntity::getFwxmCode)
                .collect(Collectors.toSet());

        // 需要新增的服务项
        List<String> toAddFwCodeList = newFwCodeList.stream()
                .filter(fwCode -> !existingFwCodeSet.contains(fwCode))
                .collect(Collectors.toList());

        // 执行新增
        toAddFwCodeList.forEach(fwCode -> {
            OmsOrderFwxmEntity orderFwxmEntity = new OmsOrderFwxmEntity();
            orderFwxmEntity.setGuid(IdWorker.getIdStr());
            orderFwxmEntity.setOrderNo(orderNo);
            orderFwxmEntity.setFwlxCode(Zsr.String.safeSubstring(fwCode, 3)); // 根据业务决定怎么处理
            orderFwxmEntity.setFwxmCode(fwCode);
            orderFwxmEntity.setCreateDate(new Date());
            orderFwxmEntity.setCreateUser(trueName);
            orderFwxmEntity.setNodeCode(companyCode);
            orderFwxmEntity.setCompanyCode(companyCode);
            orderFwxmEntity.setGroupCode(companyCode);
            omsOrderFwxmDao.insertOne(orderFwxmEntity);
        });

        // 需要删除的服务项
        List<String> toDeleteFwCodeList = existingFwList.stream()
                .filter(fw -> !newFwCodeList.contains(fw.getFwxmCode())) // ✅ 改成用 fwxmCode 对比
                .map(OmsOrderFwxmEntity::getFwxmCode)
                .collect(Collectors.toList());

        if (!toDeleteFwCodeList.isEmpty()) {
            omsOrderFwxmDao.delete()
                    .eq(OmsOrderFwxmEntity::getOrderNo, orderNo)
                    .in(OmsOrderFwxmEntity::getFwxmCode, toDeleteFwCodeList)
                    .execute();
        }
    }


    private void updateServiceTypes(OmsOrderEntity entity, String trueName, String companyCode) {
        String newFwlxCode = entity.getFwlxCode();
        if (Zsr.String.IsNullOrWhiteSpace(newFwlxCode)) {
            return;
        }

        String orderNo = entity.getOrderNo();
        List<OmsOrderFwEntity> existingFwList = omsOrderFwDao.select()
                .eq(OmsOrderFwEntity::getOrderNo, orderNo)
                .list();

        List<String> newFwCodeList = Zsr.String.string2List(newFwlxCode);
        Set<String> existingFwCodeSet = existingFwList.stream()
                .map(OmsOrderFwEntity::getFwlxCode)
                .collect(Collectors.toSet());

        // 需要新增的服务类型
        List<String> toAddFwCodeList = newFwCodeList.stream()
                .filter(fwCode -> !existingFwCodeSet.contains(fwCode))
                .collect(Collectors.toList());

        // 执行新增
        toAddFwCodeList.forEach(fw -> {
            OmsOrderFwEntity orderFwEntity = new OmsOrderFwEntity();
            orderFwEntity.setGuid(IdWorker.getIdStr());
            orderFwEntity.setOrderNo(orderNo);
            orderFwEntity.setFwlxCode(fw);
            orderFwEntity.setCreateDate(new Date());
            orderFwEntity.setCreateUser(trueName);
            orderFwEntity.setNodeCode(companyCode);
            orderFwEntity.setCompanyCode(companyCode);
            orderFwEntity.setGroupCode(companyCode);
            omsOrderFwDao.insertOne(orderFwEntity);
        });

        // 需要删除的服务类型
        List<String> toDeleteFwCodeList = existingFwList.stream()
                .filter(fw -> !newFwCodeList.contains(fw.getFwlxCode()))
                .map(OmsOrderFwEntity::getFwlxCode)
                .collect(Collectors.toList());

        if (!toDeleteFwCodeList.isEmpty()) {
            omsOrderFwDao.delete()
                    .eq(OmsOrderFwEntity::getOrderNo, orderNo)
                    .in(OmsOrderFwEntity::getFwlxCode, toDeleteFwCodeList)
                    .execute();
        }
    }


    @Override
    public List<OmsOrderEntity> selectList(OmsOrderEntity entity) {
        return omsOrderDao.selectList(entity);
    }

    @Override
    public OmsOrderEntity selectOneById(Serializable id) {
        return omsOrderDao.selectById(id);
    }


    @Override
    public void insertBatch(List<OmsOrderEntity> list) {
        omsOrderDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return omsOrderDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return omsOrderDao.deleteById(id);
    }


    /**
     * 获取订单信息
     **/
    public List<EntityBase> getOrder(String preNo, String bizRegId, String sysDocNo) {
        String companyCode = UserContext.getUserInfo().getCompanyCode();
        String groupCode = UserContext.getUserInfo().getCompanyCode();

        String sql = " SELECT A.* FROM OMS_ORDER A WHERE A.PRE_NO IN (SELECT P.PRE_NO FROM OMS_ORDER_PRE P WHERE ( ( P.PRE_NO !=" + cmn.SQLQ(preNo) + " AND P.SYS_CODE = 'OMS') OR (P.PRE_NO = " + cmn.SQLQ(preNo) + "AND P.SYS_CODE !='OMS'))  AND P.SYS_DOC_NO = " + cmn.SQLQ(sysDocNo) + " AND P.BIZ_REG_ID = " + cmn.SQLQ(bizRegId) + " AND P.COMPANY_CODE =" + cmn.SQLQ(companyCode) + " AND P.GROUP_CODE =" + cmn.SQLQ(groupCode) + " ) AND A.COMPANY_CODE =" + cmn.SQLQ(companyCode) + " AND A.GROUP_CODE =" + cmn.SQLQ(groupCode) + " AND A.STATUS !=" + cmn.SQLQ(Enums.OrderStatus.ZF.getCode()) + "";

        List<EntityBase> list = DBHelper.selectList(sql, EntityBase.class);
        return list;
    }

    /**
     * 订单保存
     */
    public OmsOrderEntity save(OmsOrderEntity order, boolean isAdd, String saveType, String batchNo) {

        OmsOrderEntity omsOrderEntity = null;

        if (order == null) {
            throw new BaseException("订单参数为空！");
        }

        orderParamToCode(order);
        omsOrderVal.SaveValidate(order, isAdd, saveType);

        if (isAdd) {
            order.setJdUser(DataExtend.toCode(order.getJdUser()));
            order.setFwlxName(omsOrderFwService.GetName(order.getFwlxCode()));
            order.setIsQrjd(StringUtils.isNull(order.getIsQrjd()) ? Enums.YNStatus.N.getCode() : order.getIsQrjd());
            order.setIsXzff(StringUtils.isNull(order.getIsXzff()) ? Enums.YNStatus.N.getCode() : order.getIsXzff());
            order.setIsAutoAr(StringUtils.isNull(order.getIsAutoAr()) ? Enums.YNStatus.N.getCode() : order.getIsAutoAr());
            order.setOrderNo(NoManager.createOrderNo('1'));
            order.setCreateDate(new java.util.Date());
            order.setUpdateDate(new java.util.Date());
            order.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            order.setCreateUserName(UserContext.getUserInfo().getTrueName());
            order.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            order.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            order.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            order.setGroupName(UserContext.getUserInfo().getCompanyName());
            order.setNodeCode(UserContext.getUserInfo().getDeptCode());
            order.setNodeName(UserContext.getUserInfo().getDeptName());
            order.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            order.setCompanyName(UserContext.getUserInfo().getCompanyName());
            order.setDataOk(Enums.YNStatus.N.getCode());
            order.setArapOk(Enums.YNStatus.N.getCode());
            order.setOpCompleteOk(Enums.YNStatus.N.getCode());
            order.setApOk(Enums.YNStatus.N.getCode());
            order.setStatus(Enums.OrderStatus.ZC.getCode());
            order.setBizRegId(IdWorker.get32UUID());
            order.setIsCancel(Enums.YNStatus.N.getCode());
            order.setCancelFlag(Enums.YNStatus.N.getCode());
            omsOrderEntity = omsOrderDao.insertOne(order);

            return omsOrderEntity;
        } else {

            return null;
        }
    }


    private void orderParamToCode(OmsOrderEntity order) {
        order.setConsigneeCode(DataExtend.toCode(order.getConsigneeCode()));
        order.setShipper(DataExtend.toCode(order.getShipper()));
        order.setReceiver(DataExtend.toCode(order.getReceiver()));
        order.setCustomerBu(DataExtend.toCode(order.getCustomerBu()));
        order.setOpType(DataExtend.toCode(order.getOpType()));
        order.setProductCode(DataExtend.toCode(order.getProductCode()));
        order.setFkfaCode(DataExtend.toCode(order.getFkfaCode()));
        order.setXzfaNo(DataExtend.toCode(order.getXzfaNo()));
        order.setJdUser(DataExtend.toCode(order.getJdUser()));
    }


    private String getSeqBatchBybatchNo(String batchNo) {

        String res = "";

        QueryWrapper query = new QueryWrapper();
        query.eq("SYS_ORDER_BATCH", batchNo);
        query.orderByDesc("SEQ_BATCH");
        List<OmsOrderEntity> orderDt = omsOrderDao.selectList(query);
        if (orderDt != null && orderDt.size() > 0) {
            res = (orderDt.get(0).getSeqBatch() + 1);
        } else {
            res = "1";
        }

        return res;
    }


    public void orderCheckType(String itm, String type) {

        String groupCode = UserContext.getUserInfo().getCompanyCode();

        // 查验类型页面调用
        if ("CHECK_PAGE".equals(type)) {
            String sql = String.format(
                    "SELECT ORDER_NO, CHECKBILL_NO, DEC_NO, CRKDH FROM OMS_ORDER_FWXM_WORK_FK_HZQD WHERE 1=1 " +
                            "AND (CHECKBILL_NO = %s OR DEC_NO = %s OR CRKDH = %s) " +
                            "AND GROUP_CODE = %s",
                    cmn.SQLQ(itm), cmn.SQLQ(itm), cmn.SQLQ(itm),
                    cmn.SQLQ(groupCode)
            );

            List<EntityBase> list = DBHelper.selectList(sql, EntityBase.class);

            if (list != null && list.size() > 0) {
                String orderNo = list.get(0).getString("ORDER_NO");

                // 修改 check 表
                String sqlCheck = String.format(
                        "UPDATE OMS_ORDER_CHECK_TYPE SET ORDER_NO = %s " +
                                "WHERE GWSB_NO = %s AND GROUP_CODE = %s",
                        cmn.SQLQ(orderNo), cmn.SQLQ(itm), cmn.SQLQ(groupCode)
                );

                DBHelper.execute(sqlCheck);

                // 调用固化方法
                extensions.addOmsGh(orderNo);
            }
        } else if ("FK_PAGE".equals(type)) {   // 反馈页面保存核注清单、报关单、出入库单调用
            String sqlEx = String.format(
                    "SELECT * FROM OMS_ORDER_CHECK_TYPE WHERE GWSB_NO = %s AND GROUP_CODE = %s",
                    cmn.SQLQ(itm), cmn.SQLQ(groupCode)
            );

            List<EntityBase> listEx = DBHelper.selectList(sqlEx, EntityBase.class);
            if (listEx.size() <= 0) {

                String sql = String.format(
                        "SELECT ORDER_NO, CHECKBILL_NO, DEC_NO, CRKDH FROM OMS_ORDER_FWXM_WORK_FK_HZQD WHERE 1=1 " +
                                "AND (CHECKBILL_NO = %s OR DEC_NO = %s OR CRKDH = %s) " +
                                "AND GROUP_CODE = %s",
                        cmn.SQLQ(itm), cmn.SQLQ(itm), cmn.SQLQ(itm),
                        cmn.SQLQ(groupCode)
                );

                List<EntityBase> dt = DBHelper.selectList(sql, EntityBase.class);

                if (dt != null && dt.size() > 0) {
                    String orderNo = dt.get(0).getString("ORDER_NO");

                    // 修改 check 表
                    String sqlCheck = String.format(
                            "UPDATE OMS_ORDER_CHECK_TYPE SET ORDER_NO = %s " +
                                    "WHERE GWSB_NO = %s AND GROUP_CODE = %s",
                            cmn.SQLQ(orderNo), cmn.SQLQ(itm), cmn.SQLQ(groupCode)
                    );

                    DBHelper.execute(sqlCheck);

                    // 调用固化方法
                    extensions.addOmsGh(orderNo);
                }
            }
        }
    }

    /**
     * 获取客户下单信息
     *
     * @param orderNo 协同委托号
     * @return
     */
    public OmsOrderPreEntity getOrderPre(String orderNo) {
        // 1. 输入校验
        if (Zsr.String.IsNullOrWhiteSpace(orderNo)) {
            return new OmsOrderPreEntity(); // 返回空的预订单实体
        }

        // 2. 查询 OmsOrderEntity
        OmsOrderEntity omsOrderEntity = omsOrderDao.select()
                .eq(OmsOrderEntity::getOrderNo, orderNo)
                .one();

        // 3. 判断 OmsOrderEntity 是否存在或 preNo 是否有效
        if (omsOrderEntity == null || Zsr.String.IsNullOrWhiteSpace(omsOrderEntity.getPreNo())) {
            return new OmsOrderPreEntity(); // 返回空的预订单实体
        }

        // 4. 查询 OmsOrderPreEntity
        String preNo = omsOrderEntity.getPreNo();
        OmsOrderPreEntity omsOrderPreEntity = omsOrderPreDao.select()
                .eq(OmsOrderPreEntity::getPreNo, preNo)
                .one();

        // 5. 关键修复：判断 omsOrderPreEntity 是否存在，防止 NullPointerException
        if (omsOrderPreEntity != null) {
            omsOrderPreEntity.push("orderNo", orderNo); // 数据增强
            return omsOrderPreEntity; // 返回找到并增强的预订单实体
        } else {
            return new OmsOrderPreEntity(); // 如果预订单不存在，返回空的预订单实体
        }
    }


}