package com.eci.project.fzgjBdServiceItemPages.service;

import com.eci.common.db.DBHelper;
import com.eci.common.utils.CommonLib;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjBdServiceItemPages.dao.FzgjBdServiceItemPagesDao;
import com.eci.project.fzgjBdServiceItemPages.entity.FzgjBdServiceItemPagesEntity;
import com.eci.project.fzgjBdServiceItemPages.validate.FzgjBdServiceItemPagesVal;

import com.eci.sso.role.entity.UserContext;
import com.eci.wu.core.DataTable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 平台服务项目对应页面编辑区Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-07
*/
@Service
@Slf4j
public class FzgjBdServiceItemPagesService implements EciBaseService<FzgjBdServiceItemPagesEntity> {

    @Autowired
    private FzgjBdServiceItemPagesDao fzgjBdServiceItemPagesDao;

    @Autowired
    private FzgjBdServiceItemPagesVal fzgjBdServiceItemPagesVal;
    CommonLib cmn = CommonLib.getInstance();

    @Override
    public TgPageInfo queryPageList(FzgjBdServiceItemPagesEntity entity) {
        EciQuery<FzgjBdServiceItemPagesEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjBdServiceItemPagesEntity> entities = fzgjBdServiceItemPagesDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjBdServiceItemPagesEntity save(FzgjBdServiceItemPagesEntity entity) {
        // 返回实体对象
        FzgjBdServiceItemPagesEntity fzgjBdServiceItemPagesEntity = null;
        fzgjBdServiceItemPagesVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjBdServiceItemPagesEntity = fzgjBdServiceItemPagesDao.insertOne(entity);

        }else{

            fzgjBdServiceItemPagesEntity = fzgjBdServiceItemPagesDao.updateByEntityId(entity);

        }
        return fzgjBdServiceItemPagesEntity;
    }
    public DataTable  getCheckEditItem(String serviceCode){
        String sql="SELECT A.GUID as \"GUID\",B.GUID \"PTGUID\",A.CODE as \"CODE\",A.NAME as \"NAME\"," +
                "case NVL(B.GUID,2) when '2' then 0 else 1 end as \"CHECKED\",\n" +
                "case NVL(B.GUID,2) when '2' then 0 else 1 end as \"ORGCHECKED\"  FROM FZGJ_BD_SERVICE_ITEM_PAGES_PT A left join (\n" +
                "  SELECT A.GUID,A.CODE FROM FZGJ_BD_SERVICE_ITEM_PAGES A WHERE A.STATUS='Y'\n" +
                "  AND A.SERVICE_ITEM_CODE = %s and group_code='%s' \n" +
                "  ) B on A.CODE=B.CODE where A.SERVICE_ITEM_CODE=%s";
        serviceCode=cmn.SQLQ(serviceCode);
        sql=String.format(sql,serviceCode, UserContext.getUserInfo().getCompanyCode(),serviceCode);
        return DBHelper.getDataTable(sql);
    }
    @Override
    public List<FzgjBdServiceItemPagesEntity> selectList(FzgjBdServiceItemPagesEntity entity) {
        return fzgjBdServiceItemPagesDao.selectList(entity);
    }

    @Override
    public FzgjBdServiceItemPagesEntity selectOneById(Serializable id) {
        return fzgjBdServiceItemPagesDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjBdServiceItemPagesEntity> list) {
        fzgjBdServiceItemPagesDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjBdServiceItemPagesDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjBdServiceItemPagesDao.deleteById(id);
    }

}