package com.eci.project.dhlResponseRecord.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.dhlResponseRecord.entity.DhlResponseRecordEntity;

import org.springframework.stereotype.Service;


/**
* 反馈DHL跟踪Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-22
*/
@Service
public class DhlResponseRecordVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(DhlResponseRecordEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(DhlResponseRecordEntity entity, BusinessType businessType) {

    }

}
