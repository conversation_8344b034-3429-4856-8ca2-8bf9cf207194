package com.eci.project.fzgjBdDistrict.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjBdDistrict.entity.FzgjBdDistrictEntity;

import org.springframework.stereotype.Service;


/**
* 区县Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-03-17
*/
@Service
public class FzgjBdDistrictVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjBdDistrictEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjBdDistrictEntity entity, BusinessType businessType) {

    }

}
