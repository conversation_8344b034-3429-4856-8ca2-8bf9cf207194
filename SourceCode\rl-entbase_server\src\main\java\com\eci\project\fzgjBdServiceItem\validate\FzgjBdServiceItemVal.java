package com.eci.project.fzgjBdServiceItem.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjBdServiceItem.entity.FzgjBdServiceItemEntity;

import com.eci.sso.role.entity.UserContext;
import org.springframework.stereotype.Service;

import java.util.Date;


/**
* 服务项目Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-07
*/
@Service
public class FzgjBdServiceItemVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjBdServiceItemEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjBdServiceItemEntity entity, BusinessType businessType) {
        if(businessType==BusinessType.INSERT) {
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
            entity.setCreateDate(new Date());
            entity.setCreateUser(UserContext.getUserInfo().getTrueName());
        }
        entity.setUpdateDate(new Date());
        entity.setUpdateUser(UserContext.getUserInfo().getTrueName());
    }

}
