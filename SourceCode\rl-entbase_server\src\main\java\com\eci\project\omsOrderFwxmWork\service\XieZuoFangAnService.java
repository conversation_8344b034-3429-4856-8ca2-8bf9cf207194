package com.eci.project.omsOrderFwxmWork.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.Extension;
import com.eci.common.NoManager;
import com.eci.common.OmsHelper;
import com.eci.common.db.DBHelper;
import com.eci.common.enums.Enums;
import com.eci.common.enums.OrderEnum;
import com.eci.common.util.StringUtils;
import com.eci.common.utils.CommonLib;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.exception.BaseException;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.omsIBillStatus.dao.OmsIBillStatusDao;
import com.eci.project.omsIBillStatus.entity.OmsIBillStatusEntity;
import com.eci.project.omsOrderFwxmWork.dao.OmsOrderFwxmWorkDao;
import com.eci.project.omsOrderFwxmWork.entity.OmsOrderFwxmWorkEntity;
import com.eci.project.omsOrderFwxmWork.entity.OmsOrderFwxmWorkVO;
import com.eci.project.omsOrderFwxmWork.entity.OrderFwxmWorkResponse;
import com.eci.project.omsOrderFwxmWork.entity.ReqOrderFwxmWorkEntity;
import com.eci.project.omsOrderFwxmWork.validate.OmsOrderFwxmWorkVal;
import com.eci.project.omsOrderPre.dao.OmsOrderPreDao;
import com.eci.project.omsOrderPre.entity.OmsOrderPreEntity;
import com.eci.sso.role.entity.UserContext;
import com.eci.wu.core.EntityBase;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;


/**
 * 供方协作任务Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-04-21
 */
@Service
@Slf4j
public class XieZuoFangAnService implements EciBaseService<OmsOrderFwxmWorkEntity> {

    CommonLib cmn = CommonLib.getInstance();

    @Autowired
    private OmsOrderFwxmWorkDao omsOrderFwxmWorkDao;
    @Autowired
    private OmsOrderFwxmWorkVal omsOrderFwxmWorkVal;
    @Autowired
    private OmsOrderPreDao omsOrderPreDao;

    @Autowired
    private OmsIBillStatusDao omsIBillStatusDao;

    /**
     * 协作方案查询
     * @param entity
     * @return
     */
    @Override
    public TgPageInfo queryPageList(OmsOrderFwxmWorkEntity entity) {
        EciQuery<OmsOrderFwxmWorkEntity> eciQuery = EciQuery.buildQuery(entity);
        eciQuery.eq(OmsOrderFwxmWorkEntity::getOrderNo,entity.getOrderNo());
        List<OmsOrderFwxmWorkVO> entities = omsOrderFwxmWorkDao.selectOmsOrderFwxmWorkList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }

}