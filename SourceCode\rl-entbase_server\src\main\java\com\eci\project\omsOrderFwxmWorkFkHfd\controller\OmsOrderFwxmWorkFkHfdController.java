package com.eci.project.omsOrderFwxmWorkFkHfd.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.omsOrderFwxmWorkFkHfd.service.OmsOrderFwxmWorkFkHfdService;
import com.eci.project.omsOrderFwxmWorkFkHfd.entity.OmsOrderFwxmWorkFkHfdEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 反馈内容-核放单Controller
*
* @<NAME_EMAIL>
* @date 2025-06-03
*/
@Api(tags = "反馈内容-核放单")
@RestController
@RequestMapping("/omsOrderFwxmWorkFkHfd")
public class OmsOrderFwxmWorkFkHfdController extends EciBaseController {

    @Autowired
    private OmsOrderFwxmWorkFkHfdService omsOrderFwxmWorkFkHfdService;


    @ApiOperation("反馈内容-核放单:保存")
    @EciLog(title = "反馈内容-核放单:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody OmsOrderFwxmWorkFkHfdEntity entity){
        OmsOrderFwxmWorkFkHfdEntity omsOrderFwxmWorkFkHfdEntity =omsOrderFwxmWorkFkHfdService.save(entity);
        return ResponseMsgUtil.success(10001,omsOrderFwxmWorkFkHfdEntity);
    }


    @ApiOperation("反馈内容-核放单:查询列表")
    @EciLog(title = "反馈内容-核放单:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody OmsOrderFwxmWorkFkHfdEntity entity){
        List<OmsOrderFwxmWorkFkHfdEntity> omsOrderFwxmWorkFkHfdEntities = omsOrderFwxmWorkFkHfdService.selectList(entity);
        return ResponseMsgUtil.success(10001,omsOrderFwxmWorkFkHfdEntities);
    }


    @ApiOperation("反馈内容-核放单:分页查询列表")
    @EciLog(title = "反馈内容-核放单:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody OmsOrderFwxmWorkFkHfdEntity entity){
        TgPageInfo tgPageInfo = omsOrderFwxmWorkFkHfdService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("反馈内容-核放单:根据ID查一条")
    @EciLog(title = "反馈内容-核放单:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody OmsOrderFwxmWorkFkHfdEntity entity){
        OmsOrderFwxmWorkFkHfdEntity  omsOrderFwxmWorkFkHfdEntity = omsOrderFwxmWorkFkHfdService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,omsOrderFwxmWorkFkHfdEntity);
    }


    @ApiOperation("反馈内容-核放单:根据ID删除一条")
    @EciLog(title = "反馈内容-核放单:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody OmsOrderFwxmWorkFkHfdEntity entity){
        int count = omsOrderFwxmWorkFkHfdService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("反馈内容-核放单:根据ID字符串删除多条")
    @EciLog(title = "反馈内容-核放单:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody OmsOrderFwxmWorkFkHfdEntity entity) {
        int count = omsOrderFwxmWorkFkHfdService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}