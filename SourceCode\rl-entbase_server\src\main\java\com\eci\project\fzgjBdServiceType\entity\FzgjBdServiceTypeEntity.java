package com.eci.project.fzgjBdServiceType.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.common.DictField;
import com.eci.common.ZsrBaseEntity;
import com.eci.common.validations.ZsrValidation;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 服务类型对象 FZGJ_BD_SERVICE_TYPE
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-03-13
*/
@ApiModel("服务类型")
@TableName("FZGJ_BD_SERVICE_TYPE")
@FieldNameConstants
public class FzgjBdServiceTypeEntity extends ZsrBaseEntity {
    /**
    * GUID
    */
    @ApiModelProperty("GUID(36)")
    @TableId("GUID")
    private String guid;

    /**
    * 代码
    */
    @ApiModelProperty("代码(20)")
    @TableField("CODE")
    @ZsrValidation(name = "代码", required = true, length = 100)
    private String code;

    /**
    * 名称
    */
    @ApiModelProperty("名称(100)")
    @TableField("NAME")
    @ZsrValidation(name = "名称", required = true, length = 100)
    private String name;

    /**
    * 状态
    */
    @ApiModelProperty("是否启用(1)")
    @TableField("STATUS")
    @DictField(queryKey = "YNKey")
    @ZsrValidation(name = "是否启用", required = true, length = 1)
    private String status;

    /**
    * 顺序
    */
    @ApiModelProperty("顺序(22)")
    @TableField("SEQ")
    @ZsrValidation(name = "顺序", required = true, length = 22)
    private Integer seq;

    /**
    * 备注
    */
    @ApiModelProperty("备注(500)")
    @TableField("MEMO")
    private String memo;

    /**
    * CREATE_USER
    */
    @ApiModelProperty("CREATE_USER(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * CREATE_DATE
    */
    @ApiModelProperty("CREATE_DATE(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("CREATE_DATE开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("CREATE_DATE结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * UPDATE_USER
    */
    @ApiModelProperty("UPDATE_USER(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * UPDATE_DATE
    */
    @ApiModelProperty("UPDATE_DATE(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("UPDATE_DATE开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("UPDATE_DATE结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * CREATE_USER
    */
    @ApiModelProperty("CREATE_USER(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * UPDATE_USER
    */
    @ApiModelProperty("UPDATE_USER(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 父级ID
    */
    @ApiModelProperty("父级ID(50)")
    @TableField("PARENTID")
    private String parentid;

    /**
    * 计算对象GUID
    */
    @ApiModelProperty("计算对象GUID(50)")
    @TableField("CLASS_GUID")
    private String classGuid;

    /**
    * 计算对象
    */
    @ApiModelProperty("计算对象(50)")
    @TableField("CLASS_CODE")
    private String classCode;

    /**
    * 英文名称
    */
    @ApiModelProperty("英文名称(200)")
    @TableField("EN_NAME")
    private String enName;

    /**
    * 传输日期
    */
    @ApiModelProperty("传输日期(7)")
    @TableField("TRN_DATE")
    private Date trnDate;

    @ApiModelProperty("传输日期开始")
    @TableField(exist=false)
    private Date trnDateStart;

    @ApiModelProperty("传输日期结束")
    @TableField(exist=false)
    private Date trnDateEnd;

    /**
    * 选择类型：是否单选
    */
    @ApiModelProperty("选择类型：是否单选(1)")
    @TableField("SELECT_TYPE")
    private String selectType;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public FzgjBdServiceTypeEntity() {
        this.setSubClazz(FzgjBdServiceTypeEntity.class);
    }

    public FzgjBdServiceTypeEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public FzgjBdServiceTypeEntity setCode(String code) {
        this.code = code;
        this.nodifySetFiled("code", code);
        return this;
    }

    public String getCode() {
        this.nodifyGetFiled("code");
        return code;
    }

    public FzgjBdServiceTypeEntity setName(String name) {
        this.name = name;
        this.nodifySetFiled("name", name);
        return this;
    }

    public String getName() {
        this.nodifyGetFiled("name");
        return name;
    }

    public FzgjBdServiceTypeEntity setStatus(String status) {
        this.status = status;
        this.nodifySetFiled("status", status);
        return this;
    }

    public String getStatus() {
        this.nodifyGetFiled("status");
        return status;
    }

    public FzgjBdServiceTypeEntity setSeq(Integer seq) {
        this.seq = seq;
        this.nodifySetFiled("seq", seq);
        return this;
    }

    public Integer getSeq() {
        this.nodifyGetFiled("seq");
        return seq;
    }

    public FzgjBdServiceTypeEntity setMemo(String memo) {
        this.memo = memo;
        this.nodifySetFiled("memo", memo);
        return this;
    }

    public String getMemo() {
        this.nodifyGetFiled("memo");
        return memo;
    }

    public FzgjBdServiceTypeEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public FzgjBdServiceTypeEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public FzgjBdServiceTypeEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public FzgjBdServiceTypeEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public FzgjBdServiceTypeEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public FzgjBdServiceTypeEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public FzgjBdServiceTypeEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public FzgjBdServiceTypeEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public FzgjBdServiceTypeEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public FzgjBdServiceTypeEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public FzgjBdServiceTypeEntity setParentid(String parentid) {
        this.parentid = parentid;
        this.nodifySetFiled("parentid", parentid);
        return this;
    }

    public String getParentid() {
        this.nodifyGetFiled("parentid");
        return parentid;
    }

    public FzgjBdServiceTypeEntity setClassGuid(String classGuid) {
        this.classGuid = classGuid;
        this.nodifySetFiled("classGuid", classGuid);
        return this;
    }

    public String getClassGuid() {
        this.nodifyGetFiled("classGuid");
        return classGuid;
    }

    public FzgjBdServiceTypeEntity setClassCode(String classCode) {
        this.classCode = classCode;
        this.nodifySetFiled("classCode", classCode);
        return this;
    }

    public String getClassCode() {
        this.nodifyGetFiled("classCode");
        return classCode;
    }

    public FzgjBdServiceTypeEntity setEnName(String enName) {
        this.enName = enName;
        this.nodifySetFiled("enName", enName);
        return this;
    }

    public String getEnName() {
        this.nodifyGetFiled("enName");
        return enName;
    }

    public FzgjBdServiceTypeEntity setTrnDate(Date trnDate) {
        this.trnDate = trnDate;
        this.nodifySetFiled("trnDate", trnDate);
        return this;
    }

    public Date getTrnDate() {
        this.nodifyGetFiled("trnDate");
        return trnDate;
    }

    public FzgjBdServiceTypeEntity setTrnDateStart(Date trnDateStart) {
        this.trnDateStart = trnDateStart;
        this.nodifySetFiled("trnDateStart", trnDateStart);
        return this;
    }

    public Date getTrnDateStart() {
        this.nodifyGetFiled("trnDateStart");
        return trnDateStart;
    }

    public FzgjBdServiceTypeEntity setTrnDateEnd(Date trnDateEnd) {
        this.trnDateEnd = trnDateEnd;
        this.nodifySetFiled("trnDateEnd", trnDateEnd);
        return this;
    }

    public Date getTrnDateEnd() {
        this.nodifyGetFiled("trnDateEnd");
        return trnDateEnd;
    }
    public FzgjBdServiceTypeEntity setSelectType(String selectType) {
        this.selectType = selectType;
        this.nodifySetFiled("selectType", selectType);
        return this;
    }

    public String getSelectType() {
        this.nodifyGetFiled("selectType");
        return selectType;
    }

}
