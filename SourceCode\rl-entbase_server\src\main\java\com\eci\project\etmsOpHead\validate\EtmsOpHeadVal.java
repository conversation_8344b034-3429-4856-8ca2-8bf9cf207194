package com.eci.project.etmsOpHead.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.etmsOpHead.entity.EtmsOpHeadEntity;

import org.springframework.stereotype.Service;


/**
* 平台业务主表Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-06-13
*/
@Service
public class EtmsOpHeadVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(EtmsOpHeadEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(EtmsOpHeadEntity entity, BusinessType businessType) {

    }

}
