package com.eci.project.omsReceiveHistory.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.omsReceiveHistory.dao.OmsReceiveHistoryDao;
import com.eci.project.omsReceiveHistory.entity.OmsReceiveHistoryEntity;
import com.eci.project.omsReceiveHistory.validate.OmsReceiveHistoryVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 报文接收记录Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-06-04
*/
@Service
@Slf4j
public class OmsReceiveHistoryService implements EciBaseService<OmsReceiveHistoryEntity> {

    @Autowired
    private OmsReceiveHistoryDao omsReceiveHistoryDao;

    @Autowired
    private OmsReceiveHistoryVal omsReceiveHistoryVal;


    @Override
    public TgPageInfo queryPageList(OmsReceiveHistoryEntity entity) {
        EciQuery<OmsReceiveHistoryEntity> eciQuery = EciQuery.buildQuery(entity);
        List<OmsReceiveHistoryEntity> entities = omsReceiveHistoryDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }


    @Transactional(rollbackFor = Exception.class)
    public OmsReceiveHistoryEntity save(OmsReceiveHistoryEntity entity,boolean isAdd) {
        // 返回实体对象
        OmsReceiveHistoryEntity omsReceiveHistoryEntity = null;

        if (isAdd) {

            omsReceiveHistoryEntity = omsReceiveHistoryDao.insertOne(entity);

        }else{

            omsReceiveHistoryEntity = omsReceiveHistoryDao.updateByEntityId(entity);

        }
        return omsReceiveHistoryEntity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OmsReceiveHistoryEntity save(OmsReceiveHistoryEntity entity) {
        // 返回实体对象
        OmsReceiveHistoryEntity omsReceiveHistoryEntity = null;
        omsReceiveHistoryVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            omsReceiveHistoryEntity = omsReceiveHistoryDao.insertOne(entity);

        }else{

            omsReceiveHistoryEntity = omsReceiveHistoryDao.updateByEntityId(entity);

        }
        return omsReceiveHistoryEntity;
    }

    @Override
    public List<OmsReceiveHistoryEntity> selectList(OmsReceiveHistoryEntity entity) {
        return omsReceiveHistoryDao.selectList(entity);
    }

    @Override
    public OmsReceiveHistoryEntity selectOneById(Serializable id) {
        return omsReceiveHistoryDao.selectById(id);
    }


    @Override
    public void insertBatch(List<OmsReceiveHistoryEntity> list) {
        omsReceiveHistoryDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return omsReceiveHistoryDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return omsReceiveHistoryDao.deleteById(id);
    }

}