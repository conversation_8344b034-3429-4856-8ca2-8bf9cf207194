package com.eci.project.fzgjExtendData.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


/**
 * 扩展基础资料对象 FZGJ_EXTEND_DATA
 * 可以自己扩展字段
 *
 * @<NAME_EMAIL>
 * @date 2025-03-19
 */
@ApiModel("扩展基础资料")
@TableName("FZGJ_EXTEND_DATA")
public class FzgjExtendDataEntity extends FzgjExtendDataBaseEntity {

    @Override
    protected void addConvertMap() {
        convertMap.put(Fields.dataType, () -> "FZGJ_EXTEND_DATA_TYPE_SYS");
    }
}
