<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.etmsCrmEnterprise.dao.EtmsCrmEnterpriseDao">
    <resultMap type="EtmsCrmEnterpriseEntity" id="EtmsCrmEnterpriseResult">
        <result property="guid" column="GUID"/>
        <result property="code" column="CODE"/>
        <result property="name" column="NAME"/>
        <result property="shortName" column="SHORT_NAME"/>
        <result property="enName" column="EN_NAME"/>
        <result property="otherName" column="OTHER_NAME"/>
        <result property="customNo" column="CUSTOM_NO"/>
        <result property="address" column="ADDRESS"/>
        <result property="addressEn" column="ADDRESS_EN"/>
        <result property="person" column="PERSON"/>
        <result property="tel1" column="TEL1"/>
        <result property="tel2" column="TEL2"/>
        <result property="fax" column="FAX"/>
        <result property="email" column="EMAIL"/>
        <result property="zip" column="ZIP"/>
        <result property="country" column="COUNTRY"/>
        <result property="state" column="STATE"/>
        <result property="city" column="CITY"/>
        <result property="contract" column="CONTRACT"/>
        <result property="contractFromDate" column="CONTRACT_FROM_DATE"/>
        <result property="contractToDate" column="CONTRACT_TO_DATE"/>
        <result property="account" column="ACCOUNT"/>
        <result property="bank" column="BANK"/>
        <result property="tax" column="TAX"/>
        <result property="creditRating" column="CREDIT_RATING"/>
        <result property="creditLimit" column="CREDIT_LIMIT"/>
        <result property="chargeCycle" column="CHARGE_CYCLE"/>
        <result property="treaty" column="TREATY"/>
        <result property="notes" column="NOTES"/>
        <result property="status" column="STATUS"/>
        <result property="memo" column="MEMO"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="companyType" column="COMPANY_TYPE"/>
        <result property="companyTypeName" column="COMPANY_TYPE_NAME"/>
        <result property="flag" column="FLAG"/>
        <result property="customerB2b" column="CUSTOMER_B2B"/>
        <result property="logo" column="LOGO"/>
        <result property="appname" column="APPNAME"/>
        <result property="endUserDate" column="END_USER_DATE"/>
        <result property="autoCreateArap" column="AUTO_CREATE_ARAP"/>
        <result property="startNo" column="START_NO"/>
        <result property="isUseTrade" column="IS_USE_TRADE"/>
        <result property="smtpEmail" column="SMTP_EMAIL"/>
        <result property="smtpEmailUser" column="SMTP_EMAIL_USER"/>
        <result property="smtpEmailPwd" column="SMTP_EMAIL_PWD"/>
        <result property="smtpEmailHost" column="SMTP_EMAIL_HOST"/>
        <result property="fcompanyCode" column="FCOMPANY_CODE"/>
        <result property="bmsSys" column="BMS_SYS"/>
        <result property="sysUrl" column="SYS_URL"/>
        <result property="clientCompanyCode" column="CLIENT_COMPANY_CODE"/>
        <result property="ssoCompanyGuid" column="SSO_COMPANY_GUID"/>
    </resultMap>

    <sql id="selectEtmsCrmEnterpriseEntityVo">
        select
            GUID,
            CODE,
            NAME,
            SHORT_NAME,
            EN_NAME,
            OTHER_NAME,
            CUSTOM_NO,
            ADDRESS,
            ADDRESS_EN,
            PERSON,
            TEL1,
            TEL2,
            FAX,
            EMAIL,
            ZIP,
            COUNTRY,
            STATE,
            CITY,
            CONTRACT,
            CONTRACT_FROM_DATE,
            CONTRACT_TO_DATE,
            ACCOUNT,
            BANK,
            TAX,
            CREDIT_RATING,
            CREDIT_LIMIT,
            CHARGE_CYCLE,
            TREATY,
            NOTES,
            STATUS,
            MEMO,
            CREATE_USER,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_DATE,
            COMPANY_TYPE,
            COMPANY_TYPE_NAME,
            FLAG,
            CUSTOMER_B2B,
            LOGO,
            APPNAME,
            END_USER_DATE,
            AUTO_CREATE_ARAP,
            START_NO,
            IS_USE_TRADE,
            SMTP_EMAIL,
            SMTP_EMAIL_USER,
            SMTP_EMAIL_PWD,
            SMTP_EMAIL_HOST,
            FCOMPANY_CODE,
            BMS_SYS,
            SYS_URL,
            CLIENT_COMPANY_CODE,
            SSO_COMPANY_GUID
        from ETMS_CRM_ENTERPRISE
    </sql>
</mapper>