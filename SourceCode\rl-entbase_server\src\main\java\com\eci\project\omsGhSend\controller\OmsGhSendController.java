package com.eci.project.omsGhSend.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.omsGhSend.service.OmsGhSendService;
import com.eci.project.omsGhSend.entity.OmsGhSendEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* OMS固化路由表Controller
*
* @<NAME_EMAIL>
* @date 2025-05-16
*/
@Api(tags = "OMS固化路由表")
@RestController
@RequestMapping("/omsGhSend")
public class OmsGhSendController extends EciBaseController {

    @Autowired
    private OmsGhSendService omsGhSendService;


    @ApiOperation("OMS固化路由表:保存")
    @EciLog(title = "OMS固化路由表:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody OmsGhSendEntity entity){
        OmsGhSendEntity omsGhSendEntity =omsGhSendService.save(entity);
        return ResponseMsgUtil.success(10001,omsGhSendEntity);
    }


    @ApiOperation("OMS固化路由表:查询列表")
    @EciLog(title = "OMS固化路由表:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody OmsGhSendEntity entity){
        List<OmsGhSendEntity> omsGhSendEntities = omsGhSendService.selectList(entity);
        return ResponseMsgUtil.success(10001,omsGhSendEntities);
    }


    @ApiOperation("OMS固化路由表:分页查询列表")
    @EciLog(title = "OMS固化路由表:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody OmsGhSendEntity entity){
        TgPageInfo tgPageInfo = omsGhSendService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }

    @ApiOperation("OMS固化路由表:根据ID字符串删除多条")
    @EciLog(title = "OMS固化路由表:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody OmsGhSendEntity entity) {
        int count = omsGhSendService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}