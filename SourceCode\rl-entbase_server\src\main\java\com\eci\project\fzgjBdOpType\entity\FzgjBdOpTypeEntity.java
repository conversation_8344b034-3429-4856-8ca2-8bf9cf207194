package com.eci.project.fzgjBdOpType.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.common.ZsrBaseEntity;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 业务类型对象 FZGJ_BD_OP_TYPE
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-03-27
*/
@ApiModel("业务类型")
@TableName("FZGJ_BD_OP_TYPE")
@FieldNameConstants
public class FzgjBdOpTypeEntity extends ZsrBaseEntity{
    /**
    * 主键GUID
    */
    @ApiModelProperty("主键GUID(50)")
    @TableId("GUID")
    private String guid;

    /**
    * 代码
    */
    @ApiModelProperty("代码(50)")
    @TableField("CODE")
    private String code;

    /**
    * 名称
    */
    @ApiModelProperty("名称(50)")
    @TableField("NAME")
    private String name;

    /**
    * 备注
    */
    @ApiModelProperty("备注(200)")
    @TableField("MEMO")
    private String memo;

    /**
    * 状态YN
    */
    @ApiModelProperty("状态YN(1)")
    @TableField("STATUS")
    private String status;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 修改人
    */
    @ApiModelProperty("修改人(50)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 修改日期
    */
    @ApiModelProperty("修改日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("修改日期开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("修改日期结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 顺序
    */
    @ApiModelProperty("顺序(22)")
    @TableField("SEQ")
    private Integer seq;

    /**
    * PARENTID
    */
    @ApiModelProperty("PARENTID(50)")
    @TableField("PARENTID")
    private String parentid;

    /**
    * AP_GK
    */
    @ApiModelProperty("AP_GK(1)")
    @TableField("AP_GK")
    private String apGk;

    /**
    * AR_GK
    */
    @ApiModelProperty("AR_GK(1)")
    @TableField("AR_GK")
    private String arGk;

    /**
    * 单据类型GUID
    */
    @ApiModelProperty("单据类型GUID(50)")
    @TableField("CLASS_GUID")
    private String classGuid;

    /**
    * CLASS_CODE
    */
    @ApiModelProperty("CLASS_CODE(50)")
    @TableField("CLASS_CODE")
    private String classCode;

    /**
    * 单据类型代码
    */
    @ApiModelProperty("单据类型代码(50)")
    @TableField("SYS_CODE")
    private String sysCode;

    /**
    * 创建人名称
    */
    @ApiModelProperty("创建人名称(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 修改人名称
    */
    @ApiModelProperty("修改人名称(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 集团代码
    */
    @ApiModelProperty("集团代码(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 集团名称
    */
    @ApiModelProperty("集团名称(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 英文名称
    */
    @ApiModelProperty("英文名称(200)")
    @TableField("EN_NAME")
    private String enName;

    /**
    * 传输日期
    */
    @ApiModelProperty("传输日期(7)")
    @TableField("TRN_DATE")
    private Date trnDate;

    @ApiModelProperty("传输日期开始")
    @TableField(exist=false)
    private Date trnDateStart;

    @ApiModelProperty("传输日期结束")
    @TableField(exist=false)
    private Date trnDateEnd;

    /**
    * 检索应收结算业务逻辑
    */
    @ApiModelProperty("检索应收结算业务逻辑(4,000)")
    @TableField("SHARE_CONFIG")
    private String shareConfig;

    /**
    * 对外名称
    */
    @ApiModelProperty("对外名称(100)")
    @TableField("EXTERNAL_NAME")
    private String externalName;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public FzgjBdOpTypeEntity() {
        this.setSubClazz(FzgjBdOpTypeEntity.class);
    }

    public FzgjBdOpTypeEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public FzgjBdOpTypeEntity setCode(String code) {
        this.code = code;
        this.nodifySetFiled("code", code);
        return this;
    }

    public String getCode() {
        this.nodifyGetFiled("code");
        return code;
    }

    public FzgjBdOpTypeEntity setName(String name) {
        this.name = name;
        this.nodifySetFiled("name", name);
        return this;
    }

    public String getName() {
        this.nodifyGetFiled("name");
        return name;
    }

    public FzgjBdOpTypeEntity setMemo(String memo) {
        this.memo = memo;
        this.nodifySetFiled("memo", memo);
        return this;
    }

    public String getMemo() {
        this.nodifyGetFiled("memo");
        return memo;
    }

    public FzgjBdOpTypeEntity setStatus(String status) {
        this.status = status;
        this.nodifySetFiled("status", status);
        return this;
    }

    public String getStatus() {
        this.nodifyGetFiled("status");
        return status;
    }

    public FzgjBdOpTypeEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public FzgjBdOpTypeEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public FzgjBdOpTypeEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public FzgjBdOpTypeEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public FzgjBdOpTypeEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public FzgjBdOpTypeEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public FzgjBdOpTypeEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public FzgjBdOpTypeEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public FzgjBdOpTypeEntity setSeq(Integer seq) {
        this.seq = seq;
        this.nodifySetFiled("seq", seq);
        return this;
    }

    public Integer getSeq() {
        this.nodifyGetFiled("seq");
        return seq;
    }

    public FzgjBdOpTypeEntity setParentid(String parentid) {
        this.parentid = parentid;
        this.nodifySetFiled("parentid", parentid);
        return this;
    }

    public String getParentid() {
        this.nodifyGetFiled("parentid");
        return parentid;
    }

    public FzgjBdOpTypeEntity setApGk(String apGk) {
        this.apGk = apGk;
        this.nodifySetFiled("apGk", apGk);
        return this;
    }

    public String getApGk() {
        this.nodifyGetFiled("apGk");
        return apGk;
    }

    public FzgjBdOpTypeEntity setArGk(String arGk) {
        this.arGk = arGk;
        this.nodifySetFiled("arGk", arGk);
        return this;
    }

    public String getArGk() {
        this.nodifyGetFiled("arGk");
        return arGk;
    }

    public FzgjBdOpTypeEntity setClassGuid(String classGuid) {
        this.classGuid = classGuid;
        this.nodifySetFiled("classGuid", classGuid);
        return this;
    }

    public String getClassGuid() {
        this.nodifyGetFiled("classGuid");
        return classGuid;
    }

    public FzgjBdOpTypeEntity setClassCode(String classCode) {
        this.classCode = classCode;
        this.nodifySetFiled("classCode", classCode);
        return this;
    }

    public String getClassCode() {
        this.nodifyGetFiled("classCode");
        return classCode;
    }

    public FzgjBdOpTypeEntity setSysCode(String sysCode) {
        this.sysCode = sysCode;
        this.nodifySetFiled("sysCode", sysCode);
        return this;
    }

    public String getSysCode() {
        this.nodifyGetFiled("sysCode");
        return sysCode;
    }

    public FzgjBdOpTypeEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public FzgjBdOpTypeEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public FzgjBdOpTypeEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public FzgjBdOpTypeEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public FzgjBdOpTypeEntity setEnName(String enName) {
        this.enName = enName;
        this.nodifySetFiled("enName", enName);
        return this;
    }

    public String getEnName() {
        this.nodifyGetFiled("enName");
        return enName;
    }

    public FzgjBdOpTypeEntity setTrnDate(Date trnDate) {
        this.trnDate = trnDate;
        this.nodifySetFiled("trnDate", trnDate);
        return this;
    }

    public Date getTrnDate() {
        this.nodifyGetFiled("trnDate");
        return trnDate;
    }

    public FzgjBdOpTypeEntity setTrnDateStart(Date trnDateStart) {
        this.trnDateStart = trnDateStart;
        this.nodifySetFiled("trnDateStart", trnDateStart);
        return this;
    }

    public Date getTrnDateStart() {
        this.nodifyGetFiled("trnDateStart");
        return trnDateStart;
    }

    public FzgjBdOpTypeEntity setTrnDateEnd(Date trnDateEnd) {
        this.trnDateEnd = trnDateEnd;
        this.nodifySetFiled("trnDateEnd", trnDateEnd);
        return this;
    }

    public Date getTrnDateEnd() {
        this.nodifyGetFiled("trnDateEnd");
        return trnDateEnd;
    }
    public FzgjBdOpTypeEntity setShareConfig(String shareConfig) {
        this.shareConfig = shareConfig;
        this.nodifySetFiled("shareConfig", shareConfig);
        return this;
    }

    public String getShareConfig() {
        this.nodifyGetFiled("shareConfig");
        return shareConfig;
    }

    public FzgjBdOpTypeEntity setExternalName(String externalName) {
        this.externalName = externalName;
        this.nodifySetFiled("externalName", externalName);
        return this;
    }

    public String getExternalName() {
        this.nodifyGetFiled("externalName");
        return externalName;
    }

}
