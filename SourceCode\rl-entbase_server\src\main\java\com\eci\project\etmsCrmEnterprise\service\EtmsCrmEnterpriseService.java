package com.eci.project.etmsCrmEnterprise.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.etmsCrmEnterprise.dao.EtmsCrmEnterpriseDao;
import com.eci.project.etmsCrmEnterprise.entity.EtmsCrmEnterpriseEntity;
import com.eci.project.etmsCrmEnterprise.validate.EtmsCrmEnterpriseVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-06-13
*/
@Service
@Slf4j
public class EtmsCrmEnterpriseService implements EciBaseService<EtmsCrmEnterpriseEntity> {

    @Autowired
    private EtmsCrmEnterpriseDao etmsCrmEnterpriseDao;

    @Autowired
    private EtmsCrmEnterpriseVal etmsCrmEnterpriseVal;


    @Override
    public TgPageInfo queryPageList(EtmsCrmEnterpriseEntity entity) {
        EciQuery<EtmsCrmEnterpriseEntity> eciQuery = EciQuery.buildQuery(entity);
        List<EtmsCrmEnterpriseEntity> entities = etmsCrmEnterpriseDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public EtmsCrmEnterpriseEntity save(EtmsCrmEnterpriseEntity entity) {
        // 返回实体对象
        EtmsCrmEnterpriseEntity etmsCrmEnterpriseEntity = null;
        etmsCrmEnterpriseVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            etmsCrmEnterpriseEntity = etmsCrmEnterpriseDao.insertOne(entity);

        }else{

            etmsCrmEnterpriseEntity = etmsCrmEnterpriseDao.updateByEntityId(entity);

        }
        return etmsCrmEnterpriseEntity;
    }

    @Override
    public List<EtmsCrmEnterpriseEntity> selectList(EtmsCrmEnterpriseEntity entity) {
        return etmsCrmEnterpriseDao.selectList(entity);
    }

    @Override
    public EtmsCrmEnterpriseEntity selectOneById(Serializable id) {
        return etmsCrmEnterpriseDao.selectById(id);
    }


    @Override
    public void insertBatch(List<EtmsCrmEnterpriseEntity> list) {
        etmsCrmEnterpriseDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return etmsCrmEnterpriseDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return etmsCrmEnterpriseDao.deleteById(id);
    }

}