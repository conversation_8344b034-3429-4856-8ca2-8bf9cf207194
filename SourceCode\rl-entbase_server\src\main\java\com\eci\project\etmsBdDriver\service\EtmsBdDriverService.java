package com.eci.project.etmsBdDriver.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.eci.common.db.DBHelper;
import com.eci.common.utils.CommonLib;
import com.eci.common.utils.ExportUtils.ExcelProcess;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.ActionType;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.etmsBdDriver.dao.EtmsBdDriverDao;
import com.eci.project.etmsBdDriver.entity.DriverInfo;
import com.eci.project.etmsBdDriver.entity.EtmsBdDriverEntity;
import com.eci.project.etmsBdDriver.entity.queryEntity;
import com.eci.project.etmsBdDriver.validate.EtmsBdDriverVal;

import com.eci.project.etmsBdTruckSpec.entity.EtmsBdTruckSpecEntity;
import com.eci.project.fzgjBdHx.entity.FzgjBdHxEntity;
import com.eci.sso.role.entity.UserContext;
import com.github.pagehelper.PageHelper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.eci.common.util.ResponseMsgUtil.listCodeToName;


/**
* 司机基础信息Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-14
*/
@Service
@Slf4j
public class EtmsBdDriverService implements EciBaseService<EtmsBdDriverEntity> {

    @Autowired
    private EtmsBdDriverDao etmsBdDriverDao;

    @Autowired
    private EtmsBdDriverVal etmsBdDriverVal;
    CommonLib cmn = CommonLib.getInstance();

    @Override
    public TgPageInfo queryPageList(EtmsBdDriverEntity entity) {
        EciQuery<EtmsBdDriverEntity> eciQuery = EciQuery.buildQuery(entity);
        List<EtmsBdDriverEntity> entities = etmsBdDriverDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }


    public TgPageInfo queryPages(queryEntity entity) {
        QueryWrapper query=new QueryWrapper();
        if(entity.type.equals("Y"))
            query.apply(" A.IS_GK='N'");
        else
            query.apply(" A.IS_GK='Y'");
        if(!entity.type.equals("VIEW")){
            query.apply( "A.CREATE_COMPANY={0}",UserContext.getUserInfo().getCompanyCode());

            query.apply(String.format(" ( a.partner_guid is null or a.partner_guid in " +
                    "( SELECT X.GUID FROM CRM_CUSTOMER X WHERE X.STATUS='Y' AND X.GROUP_CODE='%s' " +
                    " AND X.GUID = A.PARTNER_GUID)) ",UserContext.getUserInfo().getCompanyCode(),UserContext.getUserInfo().getUserLoginNo()));
        }

        if(!entity.CREATE_COMPANY.isEmpty())
            query.apply(" A.CREATE_COMPANY={0}",entity.CREATE_COMPANY);
        if(!entity.IS_DRIVER.isEmpty())
            query.apply(" A.IS_DRIVER={0}",cmn.SQLQ(entity.IS_DRIVER));
        if(!entity.ID_NUMBER.isEmpty())
            query.apply(" A.ID_NUMBER={0}",cmn.SQLQ(entity.ID_NUMBER));
        if(!entity.NAME.isEmpty())
            query.apply("A.NAME like "+cmn.sqlQL(entity.NAME));
        if(!entity.PARTNER_GUID.isEmpty()){
            query.apply(" EXISTS(SELECT * FROM CRM_CUSTOMER X WHERE X.GUID = A.PARTNER_GUID  " +
                    " AND X.CODE={0}  AND X.GROUP_CODE ={1} )",entity.PARTNER_GUID,UserContext.getUserInfo().getCompanyCode());
        }

        if(!entity.PERSONAL_TYPE.isEmpty()) {
            query.apply(" EXISTS(SELECT * FROM ETMS_BD_DRIVER_QUAL X, etms_bd_CERTIFICATE_TYPE Y " +
                    "WHERE  X.CERTIFICATE_TYPE = Y.CODE AND X.DRIVER_GUID = A.GUID   AND Y.PERSONAL_TYPE={0} )",entity.PERSONAL_TYPE);
        }
        if(!entity.PHONE.isEmpty())
            query.apply("A.PHONE like "+cmn.sqlQL(entity.PHONE));
        if(!entity.QUASI_CAR_TYPE.isEmpty())
            query.apply("A.QUASI_CAR_TYPE = "+cmn.SQLQ(entity.QUASI_CAR_TYPE));
        if(!entity.USER_ID.isEmpty())
            query.apply("A.USER_ID like "+cmn.sqlQL(entity.USER_ID));
        if(!entity.STATUS.isEmpty())
            query.apply("A.STATUS = {0}",entity.STATUS);
        if(!entity.MANAGESTATUS.isEmpty())
            query.apply("A.MANAGE_STATUS = {0}",entity.MANAGESTATUS);
        List<DriverInfo> entities = Search(query);
        return EciQuery.getPageInfo(entities);
    }
    public List<DriverInfo> Search(QueryWrapper query){
        Integer pageSize = BllContext.getPaging().getPageSize();
        Integer pageNum = BllContext.getPaging().getPageNum();
        List<DriverInfo> entities = new ArrayList();
        if (BllContext.getRequestEntity() != null && BllContext.getRequestEntity().getBusinessType() != null) {
            if (BllContext.getRequestEntity().getBusinessType() == BusinessType.EXPORT) {
                if (BllContext.getRequestEntity().getActionType() == ActionType.ASYNC) {
                    etmsBdDriverDao.asyncExportDefaultExcel(()->{
                        List<DriverInfo> list=etmsBdDriverDao.queryPages(query);
                        List convertList = listCodeToName(list);
                        return list;
                    }, ExcelProcess.BuildConfig("从业人员管理", DriverInfo.class));
                } else {
                    etmsBdDriverDao.exportDefaultExcel(() -> {
                        List<DriverInfo> list=etmsBdDriverDao.queryPages(query);
                        List convertList = listCodeToName(list);
                        return list;
                    }, ExcelProcess.BuildConfig("从业人员管理", DriverInfo.class));
                }
            } else {
                PageHelper.startPage(pageNum, pageSize == -1 ? 0 : pageSize);
                entities = etmsBdDriverDao.queryPages(query);
            }
        } else {
            PageHelper.startPage(pageNum, pageSize == -1 ? 0 : pageSize);
            entities = etmsBdDriverDao.queryPages(query);
        }
        return entities;
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public EtmsBdDriverEntity save(EtmsBdDriverEntity entity)  {
        // 返回实体对象
        EtmsBdDriverEntity etmsBdDriverEntity = null;
        etmsBdDriverVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setManageStatus("0");
            etmsBdDriverEntity = etmsBdDriverDao.insertOne(entity);

        }else{
            if(entity.getManageStatus().equals("-1"))
                entity.setManageStatus("0");
            etmsBdDriverEntity = etmsBdDriverDao.updateByEntityId(entity);

        }
        return etmsBdDriverEntity;
    }

    @Override
    public List<EtmsBdDriverEntity> selectList(EtmsBdDriverEntity entity) {
        return etmsBdDriverDao.selectList(entity);
    }

    @Override
    public EtmsBdDriverEntity selectOneById(Serializable id) {
        EciQuery<EtmsBdDriverEntity> eciQuery = EciQuery.buildQuery(new EtmsBdDriverEntity());
        eciQuery.select("(select Name from CRM_CUSTOMER where guid=A.PARTNER_GUID) as partnerName"
        ,"(SELECT MAX(NAME) FROM ETMS_BD_QUASI_CAR_TYPE A where A.CODE=QUASI_CAR_TYPE) AS quasiCarTypeName"
        ,"A.*");

        eciQuery.eq("A.guid",id.toString());
        EtmsBdDriverEntity result=  etmsBdDriverDao.selectOne(eciQuery);
        return result;
    }


    @Override
    public void insertBatch(List<EtmsBdDriverEntity> list) {
        etmsBdDriverDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return etmsBdDriverDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return etmsBdDriverDao.deleteById(id);
    }

    public void SendAudit(String ids){
        String sqlInClause= Arrays.stream(ids.split(",")).map(value->"'"+value+"'")
                .collect(Collectors.joining(","));
        String sql=String.format("Update ETMS_BD_DRIVER set MANAGE_STATUS=1 WHERE GUID IN (%s)"
                ,sqlInClause);
        DBHelper.execute(sql);
    }
    /**
     * <AUTHOR>
     * @Description 审批退回
     * @Date  2025/4/28 14:19
     * @Param [ids]
     * @return void
     **/
    public void AuditBack(String ids){
        String sqlInClause= Arrays.stream(ids.split(",")).map(value->"'"+value+"'")
                .collect(Collectors.joining(","));
        String sql=String.format("Update ETMS_BD_DRIVER set MANAGE_STATUS=-1 WHERE GUID IN (%s)"
                ,sqlInClause);
        DBHelper.execute(sql);
    }

    /**
     * <AUTHOR>
     * @Description 审批通过
     * @Date  2025/4/28 14:19
     * @Param [ids]
     * @return void
     **/
    public void AuditSuccess(String ids){
        String sqlInClause= Arrays.stream(ids.split(",")).map(value->"'"+value+"'")
                .collect(Collectors.joining(","));
        String sql=String.format("Update ETMS_BD_DRIVER set MANAGE_STATUS=2 WHERE GUID IN (%s)"
                ,sqlInClause);
        DBHelper.execute(sql);
    }

    public boolean userIdExist(String userId){
        QueryWrapper query=new QueryWrapper();
        query.eq("USER_ID",userId);
        return etmsBdDriverDao.exists(query);
    }
}