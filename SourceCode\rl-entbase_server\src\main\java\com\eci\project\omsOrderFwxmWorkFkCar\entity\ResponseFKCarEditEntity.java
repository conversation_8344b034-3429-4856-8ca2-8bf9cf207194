package com.eci.project.omsOrderFwxmWorkFkCar.entity;

import com.eci.project.omsOrderFwxmWork.entity.OmsOrderFwxmWorkEntity;
import com.eci.project.omsOrderFwxmWorkFk.entity.OmsOrderFwxmWorkFkEntity;
import com.eci.project.omsOrderFwxmWorkFkZzfw.entity.OmsOrderFwxmWorkFkZzfwEntity;
import com.eci.wu.core.EntityBase;

import java.util.List;

/**
 * @ClassName: ResponseFKCarEditEntity
 * @Author: guangyan.mei
 * @Date: 2025/5/21 15:34
 * @Description: TODO
 */
public class ResponseFKCarEditEntity {

    public List<OmsOrderFwxmWorkFkCarEntity> getFkCar() {
        return fkCar;
    }

    public void setFkCar(List<OmsOrderFwxmWorkFkCarEntity> fkCar) {
        this.fkCar = fkCar;
    }

    // 反馈表头
    public List<OmsOrderFwxmWorkFkCarEntity> fkCar;

    public List<OmsOrderFwxmWorkFkZzfwEntity> getDtDateQTFW() {
        return dtDateQTFW;
    }

    public void setDtDateQTFW(List<OmsOrderFwxmWorkFkZzfwEntity> dtDateQTFW) {
        this.dtDateQTFW = dtDateQTFW;
    }

    // 其他服务
    public List<OmsOrderFwxmWorkFkZzfwEntity> dtDateQTFW;


    public List<OmsOrderFwxmWorkFkEntity> getFk() {
        return fk;
    }

    public void setFk(List<OmsOrderFwxmWorkFkEntity> fk) {
        this.fk = fk;
    }

    // 反馈信息
    public List<OmsOrderFwxmWorkFkEntity> fk;
}
