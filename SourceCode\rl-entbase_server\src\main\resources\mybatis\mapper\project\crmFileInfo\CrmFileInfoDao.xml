<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.crmFileInfo.dao.CrmFileInfoDao">
    <resultMap type="CrmFileInfoEntity" id="CrmFileInfoResult">
        <result property="guid" column="GUID"/>
        <result property="fileType" column="FILE_TYPE"/>
        <result property="filePath" column="FILE_PATH"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="originFileName" column="ORIGIN_FILE_NAME"/>
        <result property="fileNo" column="FILE_NO"/>
        <result property="fileFormat" column="FILE_FORMAT"/>
        <result property="memo" column="MEMO"/>
        <result property="status" column="STATUS"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="isWb" column="IS_WB"/>
        <result property="fileContent" column="FILE_CONTENT"/>
    </resultMap>

    <sql id="selectCrmFileInfoEntityVo">
        select
            GUID,
            FILE_TYPE,
            FILE_PATH,
            CREATE_USER,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_DATE,
            ORIGIN_FILE_NAME,
            FILE_NO,
            FILE_FORMAT,
            MEMO,
            STATUS,
            NODE_CODE,
            NODE_NAME,
            COMPANY_CODE,
            COMPANY_NAME,
            GROUP_CODE,
            GROUP_NAME,
            CREATE_USER_NAME,
            UPDATE_USER_NAME,
            IS_WB,
            FILE_CONTENT
        from CRM_FILE_INFO
    </sql>
</mapper>