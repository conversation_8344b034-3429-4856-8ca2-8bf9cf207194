package com.eci.project.omsOrderFwxmBgbj.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.omsOrderFwxmBgbj.entity.OmsOrderFwxmBgbjEntity;

import org.springframework.stereotype.Service;


/**
* 委托内容-报关报检Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-05-14
*/
@Service
public class OmsOrderFwxmBgbjVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(OmsOrderFwxmBgbjEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(OmsOrderFwxmBgbjEntity entity, BusinessType businessType) {

    }

}
