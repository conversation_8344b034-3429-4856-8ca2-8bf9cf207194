package com.eci.project.etmsBdTruckLimitedQz.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 车辆年审历史对象 ETMS_BD_TRUCK_LIMITED_QZ
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-04-16
*/
@ApiModel("车辆年审历史")
@TableName("ETMS_BD_TRUCK_LIMITED_QZ")
@FieldNameConstants
public class EtmsBdTruckLimitedQzEntity extends EciBaseEntity{
    /**
    * GUID
    */
    @ApiModelProperty("GUID(50)")
    @TableField("GUID")
    private String guid;

    /**
    * GUID
    */
    @ApiModelProperty("GUID(50)")
    @TableField("TRUCK_GUID")
    private String truckGuid;

    /**
    * 二级维护日期
    */
    @ApiModelProperty("二级维护日期(7)")
    @TableField("LIMITED_DATE")
    private Date limitedDate;

    @ApiModelProperty("二级维护日期开始")
    @TableField(exist=false)
    private Date limitedDateStart;

    @ApiModelProperty("二级维护日期结束")
    @TableField(exist=false)
    private Date limitedDateEnd;

    /**
    * 下次维护日期
    */
    @ApiModelProperty("下次维护日期(7)")
    @TableField("NEXT_DATE")
    private Date nextDate;

    @ApiModelProperty("下次维护日期开始")
    @TableField(exist=false)
    private Date nextDateStart;

    @ApiModelProperty("下次维护日期结束")
    @TableField(exist=false)
    private Date nextDateEnd;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 修改标志（0：不变，1：修改，2：删除，3：新增）
    */
    @ApiModelProperty("修改标志（0：不变，1：修改，2：删除，3：新增）(50)")
    @TableField("MOD_MARK")
    private String modMark;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public EtmsBdTruckLimitedQzEntity() {
        this.setSubClazz(EtmsBdTruckLimitedQzEntity.class);
    }

    public EtmsBdTruckLimitedQzEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public EtmsBdTruckLimitedQzEntity setTruckGuid(String truckGuid) {
        this.truckGuid = truckGuid;
        this.nodifySetFiled("truckGuid", truckGuid);
        return this;
    }

    public String getTruckGuid() {
        this.nodifyGetFiled("truckGuid");
        return truckGuid;
    }

    public EtmsBdTruckLimitedQzEntity setLimitedDate(Date limitedDate) {
        this.limitedDate = limitedDate;
        this.nodifySetFiled("limitedDate", limitedDate);
        return this;
    }

    public Date getLimitedDate() {
        this.nodifyGetFiled("limitedDate");
        return limitedDate;
    }

    public EtmsBdTruckLimitedQzEntity setLimitedDateStart(Date limitedDateStart) {
        this.limitedDateStart = limitedDateStart;
        this.nodifySetFiled("limitedDateStart", limitedDateStart);
        return this;
    }

    public Date getLimitedDateStart() {
        this.nodifyGetFiled("limitedDateStart");
        return limitedDateStart;
    }

    public EtmsBdTruckLimitedQzEntity setLimitedDateEnd(Date limitedDateEnd) {
        this.limitedDateEnd = limitedDateEnd;
        this.nodifySetFiled("limitedDateEnd", limitedDateEnd);
        return this;
    }

    public Date getLimitedDateEnd() {
        this.nodifyGetFiled("limitedDateEnd");
        return limitedDateEnd;
    }
    public EtmsBdTruckLimitedQzEntity setNextDate(Date nextDate) {
        this.nextDate = nextDate;
        this.nodifySetFiled("nextDate", nextDate);
        return this;
    }

    public Date getNextDate() {
        this.nodifyGetFiled("nextDate");
        return nextDate;
    }

    public EtmsBdTruckLimitedQzEntity setNextDateStart(Date nextDateStart) {
        this.nextDateStart = nextDateStart;
        this.nodifySetFiled("nextDateStart", nextDateStart);
        return this;
    }

    public Date getNextDateStart() {
        this.nodifyGetFiled("nextDateStart");
        return nextDateStart;
    }

    public EtmsBdTruckLimitedQzEntity setNextDateEnd(Date nextDateEnd) {
        this.nextDateEnd = nextDateEnd;
        this.nodifySetFiled("nextDateEnd", nextDateEnd);
        return this;
    }

    public Date getNextDateEnd() {
        this.nodifyGetFiled("nextDateEnd");
        return nextDateEnd;
    }
    public EtmsBdTruckLimitedQzEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public EtmsBdTruckLimitedQzEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public EtmsBdTruckLimitedQzEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public EtmsBdTruckLimitedQzEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public EtmsBdTruckLimitedQzEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public EtmsBdTruckLimitedQzEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public EtmsBdTruckLimitedQzEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public EtmsBdTruckLimitedQzEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public EtmsBdTruckLimitedQzEntity setModMark(String modMark) {
        this.modMark = modMark;
        this.nodifySetFiled("modMark", modMark);
        return this;
    }

    public String getModMark() {
        this.nodifyGetFiled("modMark");
        return modMark;
    }

}
