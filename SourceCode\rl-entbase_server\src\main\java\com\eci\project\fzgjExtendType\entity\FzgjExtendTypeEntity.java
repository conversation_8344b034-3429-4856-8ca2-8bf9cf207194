package com.eci.project.fzgjExtendType.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;



/**
* 扩展基础资料类型对象 FZGJ_EXTEND_TYPE
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-03-19
*/
@ApiModel("扩展基础资料类型")
@TableName("FZGJ_EXTEND_TYPE")
public class FzgjExtendTypeEntity extends FzgjExtendTypeBaseEntity{

}
