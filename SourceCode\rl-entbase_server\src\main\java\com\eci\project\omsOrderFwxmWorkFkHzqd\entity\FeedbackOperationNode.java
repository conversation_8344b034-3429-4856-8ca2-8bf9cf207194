package com.eci.project.omsOrderFwxmWorkFkHzqd.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.eci.common.validations.ZsrValidation;
import lombok.Data;

import java.util.Date;

/**
 * 作业反馈节点的请求对象。
 * 环节编号（对应基础资料中代码） 写入/更新到系统
 */
@Data
public class FeedbackOperationNode {

    /**
     * OMS协作任务编号。
     * 必填。
     */
    @ZsrValidation(required = true, length = 100,errorMessage = "OMS协作任务编号未填写")
    private String WORK_NO;

    /**
     * OMS订单号。
     * 必填。
     */
    @ZsrValidation(required = true, length = 100,errorMessage = "OMS订单号未填写")
    private String ORDER_NO;

    /**
     * 环节编号。
     * 必填。对应系统基础资料中的代码，用于标识作业流程中的具体环节。
     */
    @ZsrValidation(required = true, length = 100,errorMessage = "环节编号未填写")
    private String STAGE;

    /**
     * 作业完成时间。
     * 可选。格式通常为 "yyyy-MM-dd HH:mm:ss"。
     */
    @ZsrValidation(required = true, length = 100,errorMessage = "作业完成时间未填写")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date OK_DATE;

    /**
     * 是否撤销。
     * 可选。'Y' 表示撤销，'N' 表示未撤销。
     */
    private String IS_REBACK;

    /**
     * 作业系统代码。
     * 必填。
     */
    @ZsrValidation(required = true, length = 100,errorMessage = "作业系统代码未填写")
    private String SYS_CODE;

    /**
     * 传输时间。
     * 可选。格式通常为 "yyyy-MM-dd HH:mm:ss"。
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date TRN_DATE;

}