package com.eci.project.omsOrderFwxm.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.omsOrderFwxm.entity.OmsOrderFwxmEntity;

import org.springframework.stereotype.Service;


/**
* 订单服务项目Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-14
*/
@Service
public class OmsOrderFwxmVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(OmsOrderFwxmEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(OmsOrderFwxmEntity entity, BusinessType businessType) {

    }

}
