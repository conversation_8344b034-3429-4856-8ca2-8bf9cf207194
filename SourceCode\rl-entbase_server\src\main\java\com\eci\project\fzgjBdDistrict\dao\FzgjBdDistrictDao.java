package com.eci.project.fzgjBdDistrict.dao;

import com.eci.crud.dao.EciBaseDao;
import com.eci.project.fzgjBdDistrict.entity.FzgjBdDistrictEntity;
import com.eci.project.fzgjBdDistrict.entity.FzgjBdDistrictRealtionEntity;

import java.util.List;


/**
* 区县Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-03-17
*/
public interface FzgjBdDistrictDao extends EciBaseDao<FzgjBdDistrictEntity> {

    /**
     * 区县所属关系列表数据查询
     * ***/
    List<FzgjBdDistrictRealtionEntity> selectDistrictRealtionPageList(FzgjBdDistrictRealtionEntity entity);
}
