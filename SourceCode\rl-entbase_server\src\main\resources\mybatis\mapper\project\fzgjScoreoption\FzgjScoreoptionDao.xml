<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.fzgjScoreoption.dao.FzgjScoreoptionDao">
    <resultMap type="FzgjScoreoptionEntity" id="FzgjScoreoptionResult">
        <result property="guid" column="GUID"/>
        <result property="roleName" column="ROLE_NAME"/>
        <result property="category" column="CATEGORY"/>
        <result property="formula" column="FORMULA"/>
        <result property="roleValue" column="ROLE_VALUE"/>
        <result property="roleScore" column="ROLE_SCORE"/>
        <result property="status" column="STATUS"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="type" column="TYPE"/>
        <result property="remark" column="REMARK"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
    </resultMap>

    <sql id="selectFzgjScoreoptionEntityVo">
        select
            GUID,
            ROLE_NAME,
            CATEGORY,
            FORMULA,
            ROLE_VALUE,
            ROLE_SCORE,
            STATUS,
            CREATE_USER,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_DATE,
            CREATE_USER_NAME,
            UPDATE_USER_NAME,
            TYPE,
            REMARK,
            COMPANY_CODE,
            COMPANY_NAME,
            GROUP_CODE,
            GROUP_NAME
        from FZGJ_SCOREOPTION
    </sql>
</mapper>