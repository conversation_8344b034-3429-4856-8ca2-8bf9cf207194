package com.eci.project.fzgjCrmEnterprise.validate;

import com.eci.common.validations.ZsrValidationUtil;
import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjCrmEnterprise.entity.FzgjCrmEnterpriseEntity;

import org.springframework.stereotype.Service;


/**
* 注册企业Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-03-13
*/
@Service
public class FzgjCrmEnterpriseVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjCrmEnterpriseEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjCrmEnterpriseEntity entity, BusinessType businessType) throws IllegalAccessException {
        ZsrValidationUtil.validation(entity);
    }

}
