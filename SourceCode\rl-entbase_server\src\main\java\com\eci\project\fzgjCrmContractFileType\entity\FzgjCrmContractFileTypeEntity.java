package com.eci.project.fzgjCrmContractFileType.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;


/**
 * 合同附件类型对象 FZGJ_CRM_CONTRACT_FILE_TYPE
 * 可以自己扩展字段
 *
 * @<NAME_EMAIL>
 * @date 2025-03-19
 */
@ApiModel("合同附件类型")
@TableName("FZGJ_CRM_CONTRACT_FILE_TYPE")
public class FzgjCrmContractFileTypeEntity extends FzgjCrmContractFileTypeBaseEntity {

    @Override
    protected void addConvertMap() {
        convertMap.put(FzgjCrmContractFileTypeEntity.Fields.status, () -> "YNKey");//是否启用
    }
}
