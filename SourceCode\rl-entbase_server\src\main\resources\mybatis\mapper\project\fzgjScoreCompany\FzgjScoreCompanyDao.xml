<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.fzgjScoreCompany.dao.FzgjScoreCompanyDao">
    <resultMap type="FzgjScoreCompanyEntity" id="FzgjScoreCompanyResult">
        <result property="guid" column="GUID"/>
        <result property="category" column="CATEGORY"/>
        <result property="score" column="SCORE"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
    </resultMap>

    <sql id="selectFzgjScoreCompanyEntityVo">
        select
            GUID,
            CATEGORY,
            SCORE,
            CREATE_USER,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_DATE,
            CREATE_USER_NAME,
            UPDATE_USER_NAME,
            COMPANY_CODE,
            COMPANY_NAME,
            GROUP_CODE,
            GROUP_NAME
        from FZGJ_SCORE_COMPANY
    </sql>
</mapper>