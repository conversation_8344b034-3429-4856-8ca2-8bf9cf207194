package com.eci.project.crmCustomerHead.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.crmCustomerHead.dao.CrmCustomerHeadDao;
import com.eci.project.crmCustomerHead.entity.CrmCustomerHeadEntity;
import com.eci.project.crmCustomerHead.validate.CrmCustomerHeadVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 业务伙伴表头Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-29
*/
@Service
@Slf4j
public class CrmCustomerHeadService implements EciBaseService<CrmCustomerHeadEntity> {

    @Autowired
    private CrmCustomerHeadDao crmCustomerHeadDao;

    @Autowired
    private CrmCustomerHeadVal crmCustomerHeadVal;


    @Override
    public TgPageInfo queryPageList(CrmCustomerHeadEntity entity) {
        EciQuery<CrmCustomerHeadEntity> eciQuery = EciQuery.buildQuery(entity);
        List<CrmCustomerHeadEntity> entities = crmCustomerHeadDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public CrmCustomerHeadEntity save(CrmCustomerHeadEntity entity) {
        // 返回实体对象
        CrmCustomerHeadEntity crmCustomerHeadEntity = null;
        crmCustomerHeadVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            crmCustomerHeadEntity = crmCustomerHeadDao.insertOne(entity);

        }else{

            crmCustomerHeadEntity = crmCustomerHeadDao.updateByEntityId(entity);

        }
        return crmCustomerHeadEntity;
    }

    @Override
    public List<CrmCustomerHeadEntity> selectList(CrmCustomerHeadEntity entity) {
        return crmCustomerHeadDao.selectList(entity);
    }

    @Override
    public CrmCustomerHeadEntity selectOneById(Serializable id) {
        return crmCustomerHeadDao.selectById(id);
    }


    @Override
    public void insertBatch(List<CrmCustomerHeadEntity> list) {
        crmCustomerHeadDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return crmCustomerHeadDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return crmCustomerHeadDao.deleteById(id);
    }
    /**
     * <AUTHOR>
     * @Description 通过code查找head数据
     * @Date  2025/5/7 16:44
     * @Param [code]
     * @return com.eci.project.crmCustomerHead.entity.CrmCustomerHeadEntity
     **/
    public CrmCustomerHeadEntity selectOneByCode(String code){
        QueryWrapper query=new QueryWrapper();
        query.eq("CODE",code);
        return crmCustomerHeadDao.selectOne(query);
    }
}