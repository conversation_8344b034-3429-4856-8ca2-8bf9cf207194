package com.eci.project.fzgjBdTruckType.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjBdTruckType.service.FzgjBdTruckTypeService;
import com.eci.project.fzgjBdTruckType.entity.FzgjBdTruckTypeEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 车辆类型Controller
*
* @<NAME_EMAIL>
* @date 2025-03-14
*/
@Api(tags = "车辆类型")
@RestController
@RequestMapping("/fzgjBdTruckType")
public class FzgjBdTruckTypeController extends EciBaseController {

    @Autowired
    private FzgjBdTruckTypeService fzgjBdTruckTypeService;


    @ApiOperation("车辆类型:保存")
    @EciLog(title = "车辆类型:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody FzgjBdTruckTypeEntity entity){
        FzgjBdTruckTypeEntity fzgjBdTruckTypeEntity =fzgjBdTruckTypeService.save(entity);
        return ResponseMsgUtil.success(10001,fzgjBdTruckTypeEntity);
    }


    @ApiOperation("车辆类型:查询列表")
    @EciLog(title = "车辆类型:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjBdTruckTypeEntity entity){
        List<FzgjBdTruckTypeEntity> fzgjBdTruckTypeEntities = fzgjBdTruckTypeService.selectList(entity);
        return ResponseMsgUtil.success(10001,fzgjBdTruckTypeEntities);
    }


    @ApiOperation("车辆类型:分页查询列表")
    @EciLog(title = "车辆类型:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjBdTruckTypeEntity entity){
        TgPageInfo tgPageInfo = fzgjBdTruckTypeService.queryPageList(entity);
        return ResponseMsgUtil.successPlus(10001,tgPageInfo);
    }


    @ApiOperation("车辆类型:根据ID查一条")
    @EciLog(title = "车辆类型:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjBdTruckTypeEntity entity){
        FzgjBdTruckTypeEntity  fzgjBdTruckTypeEntity = fzgjBdTruckTypeService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,fzgjBdTruckTypeEntity);
    }


    @ApiOperation("车辆类型:根据ID删除一条")
    @EciLog(title = "车辆类型:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjBdTruckTypeEntity entity){
        int count = fzgjBdTruckTypeService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("车辆类型:根据ID字符串删除多条")
    @EciLog(title = "车辆类型:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjBdTruckTypeEntity entity) {
        int count = fzgjBdTruckTypeService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}