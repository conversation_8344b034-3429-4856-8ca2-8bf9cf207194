package com.eci.project.fzgjScoreoption.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjScoreoption.dao.FzgjScoreoptionDao;
import com.eci.project.fzgjScoreoption.entity.FzgjScoreoptionEntity;
import com.eci.project.fzgjScoreoption.validate.FzgjScoreoptionVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 评分标准设置Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-06-16
*/
@Service
@Slf4j
public class FzgjScoreoptionService implements EciBaseService<FzgjScoreoptionEntity> {

    @Autowired
    private FzgjScoreoptionDao fzgjScoreoptionDao;

    @Autowired
    private FzgjScoreoptionVal fzgjScoreoptionVal;


    @Override
    public TgPageInfo queryPageList(FzgjScoreoptionEntity entity) {
        EciQuery<FzgjScoreoptionEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjScoreoptionEntity> entities = fzgjScoreoptionDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjScoreoptionEntity save(FzgjScoreoptionEntity entity) {
        // 返回实体对象
        FzgjScoreoptionEntity fzgjScoreoptionEntity = null;
        fzgjScoreoptionVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjScoreoptionEntity = fzgjScoreoptionDao.insertOne(entity);

        }else{

            fzgjScoreoptionEntity = fzgjScoreoptionDao.updateByEntityId(entity);

        }
        return fzgjScoreoptionEntity;
    }

    @Override
    public List<FzgjScoreoptionEntity> selectList(FzgjScoreoptionEntity entity) {
        return fzgjScoreoptionDao.selectList(entity);
    }

    @Override
    public FzgjScoreoptionEntity selectOneById(Serializable id) {
        return fzgjScoreoptionDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjScoreoptionEntity> list) {
        fzgjScoreoptionDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjScoreoptionDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjScoreoptionDao.deleteById(id);
    }

}