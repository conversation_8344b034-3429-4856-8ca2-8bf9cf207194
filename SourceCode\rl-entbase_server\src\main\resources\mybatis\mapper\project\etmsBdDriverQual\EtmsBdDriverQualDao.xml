<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.etmsBdDriverQual.dao.EtmsBdDriverQualDao">
    <resultMap type="EtmsBdDriverQualEntity" id="EtmsBdDriverQualResult">
        <result property="guid" column="GUID"/>
        <result property="driverGuid" column="DRIVER_GUID"/>
        <result property="startDate" column="START_DATE"/>
        <result property="endDate" column="END_DATE"/>
        <result property="certificateType" column="CERTIFICATE_TYPE"/>
        <result property="firstIssueDate" column="FIRST_ISSUE_DATE"/>
        <result property="cardNo" column="CARD_NO"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="createCompany" column="CREATE_COMPANY"/>
        <result property="status" column="STATUS"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
    </resultMap>

    <sql id="selectEtmsBdDriverQualEntityVo">
        select
            GUID,
            DRIVER_GUID,
            START_DATE,
            END_DATE,
            CERTIFICATE_TYPE,
            FIRST_ISSUE_DATE,
            CARD_NO,
            CREATE_USER,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_DATE,
            CREATE_COMPANY,
            STATUS,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            CREATE_USER_NAME,
            UPDATE_USER_NAME
        from ETMS_BD_DRIVER_QUAL
    </sql>

    <select id="queryPages" parameterType="com.baomidou.mybatisplus.core.conditions.Wrapper"
            resultType="com.eci.project.etmsBdDriverQual.entity.EtmsBdDriverQualEntity">
        select * from (select GUID,
                              CARD_NO,
                              DRIVER_GUID,
                              STATUS,
                              CERTIFICATE_TYPE,
                              UPDATE_USER_NAME,
                              (SELECT max((
                                  SELECT MAX(T.KEY_NAME)  FROM ECI_DATA_CODE T  WHERE T.GROUP_CODE ='00004' AND T.KEY_VALUE=x.personal_type
                              )) FROM ETMS_BD_CERTIFICATE_TYPE X WHERE X.CODE = CERTIFICATE_TYPE  ) as personal_type,
                              START_DATE,
                              END_DATE ,
                              FIRST_ISSUE_DATE,
                              (case status when 'Y' THEN '是' when 'N' THEN '否' END) AS STATUSName,
                              (case WHEN TRUNC(SYSDATE)>END_DATE THEN '失效' else '有效' end) as cardStatus,
                              UPDATE_DATE
                       from etms_bd_driver_qual)  A

        ${ew.customSqlSegment}

    </select>
</mapper>