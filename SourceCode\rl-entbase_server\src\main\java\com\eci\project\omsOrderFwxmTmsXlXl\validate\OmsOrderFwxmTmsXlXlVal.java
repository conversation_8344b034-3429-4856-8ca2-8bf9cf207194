package com.eci.project.omsOrderFwxmTmsXlXl.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.omsOrderFwxmTmsXlXl.entity.OmsOrderFwxmTmsXlXlEntity;

import org.springframework.stereotype.Service;


/**
* 委托内容-程运序列Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-05-28
*/
@Service
public class OmsOrderFwxmTmsXlXlVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(OmsOrderFwxmTmsXlXlEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(OmsOrderFwxmTmsXlXlEntity entity, BusinessType businessType) {

    }

}
