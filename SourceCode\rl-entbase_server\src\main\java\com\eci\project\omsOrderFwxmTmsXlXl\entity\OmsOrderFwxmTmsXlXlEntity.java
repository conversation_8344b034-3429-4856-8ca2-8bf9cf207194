package com.eci.project.omsOrderFwxmTmsXlXl.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 委托内容-程运序列对象 OMS_ORDER_FWXM_TMS_XL_XL
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-05-28
*/
@ApiModel("委托内容-程运序列")
@TableName("OMS_ORDER_FWXM_TMS_XL_XL")
@FieldNameConstants
public class OmsOrderFwxmTmsXlXlEntity extends EciBaseEntity{
    /**
    * 委托单编号
    */
    @ApiModelProperty("委托单编号(36)")
    @TableField("PRE_NO")
    private String preNo;

    /**
    * 订单号
    */
    @ApiModelProperty("订单号(36)")
    @TableField("ORDER_NO")
    private String orderNo;

    /**
    * 需求唯一编号
    */
    @ApiModelProperty("需求唯一编号(36)")
    @TableField("TMS_NO")
    private String tmsNo;

    /**
    * 程运序列唯一编号
    */
    @ApiModelProperty("程运序列唯一编号(36)")
    @TableId("SEQ_NO")
    private String seqNo;

    /**
    * 结算线路唯一编号
    */
    @ApiModelProperty("结算线路唯一编号(36)")
    @TableField("LINE_NO")
    private String lineNo;

    /**
    * 程运序号
    */
    @ApiModelProperty("程运序号(22)")
    @TableField("SEQ_SEQ")
    private Integer seqSeq;

    /**
    * 服务项目
    */
    @ApiModelProperty("服务项目(20)")
    @TableField("FWXM_CODE")
    private String fwxmCode;

    /**
    * 其他特殊要求
    */
    @ApiModelProperty("其他特殊要求(500)")
    @TableField("OTHER_MEMO")
    private String otherMemo;

    /**
    * 协作任务编号
    */
    @ApiModelProperty("协作任务编号(50)")
    @TableField("WORK_NO")
    private String workNo;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 最后修改日期
    */
    @ApiModelProperty("最后修改日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("最后修改日期开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("最后修改日期结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(200)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public OmsOrderFwxmTmsXlXlEntity() {
        this.setSubClazz(OmsOrderFwxmTmsXlXlEntity.class);
    }

    public OmsOrderFwxmTmsXlXlEntity setPreNo(String preNo) {
        this.preNo = preNo;
        this.nodifySetFiled("preNo", preNo);
        return this;
    }

    public String getPreNo() {
        this.nodifyGetFiled("preNo");
        return preNo;
    }

    public OmsOrderFwxmTmsXlXlEntity setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        this.nodifySetFiled("orderNo", orderNo);
        return this;
    }

    public String getOrderNo() {
        this.nodifyGetFiled("orderNo");
        return orderNo;
    }

    public OmsOrderFwxmTmsXlXlEntity setTmsNo(String tmsNo) {
        this.tmsNo = tmsNo;
        this.nodifySetFiled("tmsNo", tmsNo);
        return this;
    }

    public String getTmsNo() {
        this.nodifyGetFiled("tmsNo");
        return tmsNo;
    }

    public OmsOrderFwxmTmsXlXlEntity setSeqNo(String seqNo) {
        this.seqNo = seqNo;
        this.nodifySetFiled("seqNo", seqNo);
        return this;
    }

    public String getSeqNo() {
        this.nodifyGetFiled("seqNo");
        return seqNo;
    }

    public OmsOrderFwxmTmsXlXlEntity setLineNo(String lineNo) {
        this.lineNo = lineNo;
        this.nodifySetFiled("lineNo", lineNo);
        return this;
    }

    public String getLineNo() {
        this.nodifyGetFiled("lineNo");
        return lineNo;
    }

    public OmsOrderFwxmTmsXlXlEntity setSeqSeq(Integer seqSeq) {
        this.seqSeq = seqSeq;
        this.nodifySetFiled("seqSeq", seqSeq);
        return this;
    }

    public Integer getSeqSeq() {
        this.nodifyGetFiled("seqSeq");
        return seqSeq;
    }

    public OmsOrderFwxmTmsXlXlEntity setFwxmCode(String fwxmCode) {
        this.fwxmCode = fwxmCode;
        this.nodifySetFiled("fwxmCode", fwxmCode);
        return this;
    }

    public String getFwxmCode() {
        this.nodifyGetFiled("fwxmCode");
        return fwxmCode;
    }

    public OmsOrderFwxmTmsXlXlEntity setOtherMemo(String otherMemo) {
        this.otherMemo = otherMemo;
        this.nodifySetFiled("otherMemo", otherMemo);
        return this;
    }

    public String getOtherMemo() {
        this.nodifyGetFiled("otherMemo");
        return otherMemo;
    }

    public OmsOrderFwxmTmsXlXlEntity setWorkNo(String workNo) {
        this.workNo = workNo;
        this.nodifySetFiled("workNo", workNo);
        return this;
    }

    public String getWorkNo() {
        this.nodifyGetFiled("workNo");
        return workNo;
    }

    public OmsOrderFwxmTmsXlXlEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public OmsOrderFwxmTmsXlXlEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public OmsOrderFwxmTmsXlXlEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public OmsOrderFwxmTmsXlXlEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public OmsOrderFwxmTmsXlXlEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public OmsOrderFwxmTmsXlXlEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public OmsOrderFwxmTmsXlXlEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public OmsOrderFwxmTmsXlXlEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public OmsOrderFwxmTmsXlXlEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public OmsOrderFwxmTmsXlXlEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public OmsOrderFwxmTmsXlXlEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public OmsOrderFwxmTmsXlXlEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public OmsOrderFwxmTmsXlXlEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public OmsOrderFwxmTmsXlXlEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public OmsOrderFwxmTmsXlXlEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public OmsOrderFwxmTmsXlXlEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

}
