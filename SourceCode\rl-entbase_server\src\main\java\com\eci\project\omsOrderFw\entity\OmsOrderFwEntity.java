package com.eci.project.omsOrderFw.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 订单服务类型对象 OMS_ORDER_FW
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-04-24
*/
@ApiModel("订单服务类型")
@TableName("OMS_ORDER_FW")
@FieldNameConstants
public class OmsOrderFwEntity extends EciBaseEntity{
    /**
    * GUID
    */
    @ApiModelProperty("GUID(36)")
    @TableId("GUID")
    private String guid;

    /**
    * 订单号
    */
    @ApiModelProperty("订单号(36)")
    @TableField("ORDER_NO")
    private String orderNo;

    /**
    * 委托单编号
    */
    @ApiModelProperty("委托单编号(36)")
    @TableField("PRE_NO")
    private String preNo;

    /**
    * 服务类型代码
    */
    @ApiModelProperty("服务类型代码(20)")
    @TableField("FWLX_CODE")
    private String fwlxCode;

    /**
    * 阶段(作业完成/作业数据齐全/……)
    */
    @ApiModelProperty("阶段(作业完成/作业数据齐全/……)(20)")
    @TableField("STAGE")
    private String stage;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 最后修改日期
    */
    @ApiModelProperty("最后修改日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("最后修改日期开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("最后修改日期结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(200)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 作业完成时间
    */
    @ApiModelProperty("作业完成时间(7)")
    @TableField("OP_COMPLETE_OK_DATE")
    private Date opCompleteOkDate;

    @ApiModelProperty("作业完成时间开始")
    @TableField(exist=false)
    private Date opCompleteOkDateStart;

    @ApiModelProperty("作业完成时间结束")
    @TableField(exist=false)
    private Date opCompleteOkDateEnd;

    /**
    * 作业完成标识
    */
    @ApiModelProperty("作业完成标识(1)")
    @TableField("OP_COMPLETE_OK")
    private String opCompleteOk;

    /**
    * 作业数据齐全时间
    */
    @ApiModelProperty("作业数据齐全时间(7)")
    @TableField("DATA_OK_DATE")
    private Date dataOkDate;

    @ApiModelProperty("作业数据齐全时间开始")
    @TableField(exist=false)
    private Date dataOkDateStart;

    @ApiModelProperty("作业数据齐全时间结束")
    @TableField(exist=false)
    private Date dataOkDateEnd;

    /**
    * 作业数据齐全标志（源数据齐全）
    */
    @ApiModelProperty("作业数据齐全标志（源数据齐全）(1)")
    @TableField("DATA_OK")
    private String dataOk;

    /**
    * 结算完成标记（应收应付齐全）
    */
    @ApiModelProperty("结算完成标记（应收应付齐全）(1)")
    @TableField("ARAP_OK")
    private String arapOk;

    /**
    * 结算完成日期
    */
    @ApiModelProperty("结算完成日期(7)")
    @TableField("ARAP_OK_DATE")
    private Date arapOkDate;

    @ApiModelProperty("结算完成日期开始")
    @TableField(exist=false)
    private Date arapOkDateStart;

    @ApiModelProperty("结算完成日期结束")
    @TableField(exist=false)
    private Date arapOkDateEnd;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public OmsOrderFwEntity() {
        this.setSubClazz(OmsOrderFwEntity.class);
    }

    public OmsOrderFwEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public OmsOrderFwEntity setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        this.nodifySetFiled("orderNo", orderNo);
        return this;
    }

    public String getOrderNo() {
        this.nodifyGetFiled("orderNo");
        return orderNo;
    }

    public OmsOrderFwEntity setPreNo(String preNo) {
        this.preNo = preNo;
        this.nodifySetFiled("preNo", preNo);
        return this;
    }

    public String getPreNo() {
        this.nodifyGetFiled("preNo");
        return preNo;
    }

    public OmsOrderFwEntity setFwlxCode(String fwlxCode) {
        this.fwlxCode = fwlxCode;
        this.nodifySetFiled("fwlxCode", fwlxCode);
        return this;
    }

    public String getFwlxCode() {
        this.nodifyGetFiled("fwlxCode");
        return fwlxCode;
    }

    public OmsOrderFwEntity setStage(String stage) {
        this.stage = stage;
        this.nodifySetFiled("stage", stage);
        return this;
    }

    public String getStage() {
        this.nodifyGetFiled("stage");
        return stage;
    }

    public OmsOrderFwEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public OmsOrderFwEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public OmsOrderFwEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public OmsOrderFwEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public OmsOrderFwEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public OmsOrderFwEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public OmsOrderFwEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public OmsOrderFwEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public OmsOrderFwEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public OmsOrderFwEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public OmsOrderFwEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public OmsOrderFwEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public OmsOrderFwEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public OmsOrderFwEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public OmsOrderFwEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public OmsOrderFwEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public OmsOrderFwEntity setOpCompleteOkDate(Date opCompleteOkDate) {
        this.opCompleteOkDate = opCompleteOkDate;
        this.nodifySetFiled("opCompleteOkDate", opCompleteOkDate);
        return this;
    }

    public Date getOpCompleteOkDate() {
        this.nodifyGetFiled("opCompleteOkDate");
        return opCompleteOkDate;
    }

    public OmsOrderFwEntity setOpCompleteOkDateStart(Date opCompleteOkDateStart) {
        this.opCompleteOkDateStart = opCompleteOkDateStart;
        this.nodifySetFiled("opCompleteOkDateStart", opCompleteOkDateStart);
        return this;
    }

    public Date getOpCompleteOkDateStart() {
        this.nodifyGetFiled("opCompleteOkDateStart");
        return opCompleteOkDateStart;
    }

    public OmsOrderFwEntity setOpCompleteOkDateEnd(Date opCompleteOkDateEnd) {
        this.opCompleteOkDateEnd = opCompleteOkDateEnd;
        this.nodifySetFiled("opCompleteOkDateEnd", opCompleteOkDateEnd);
        return this;
    }

    public Date getOpCompleteOkDateEnd() {
        this.nodifyGetFiled("opCompleteOkDateEnd");
        return opCompleteOkDateEnd;
    }
    public OmsOrderFwEntity setOpCompleteOk(String opCompleteOk) {
        this.opCompleteOk = opCompleteOk;
        this.nodifySetFiled("opCompleteOk", opCompleteOk);
        return this;
    }

    public String getOpCompleteOk() {
        this.nodifyGetFiled("opCompleteOk");
        return opCompleteOk;
    }

    public OmsOrderFwEntity setDataOkDate(Date dataOkDate) {
        this.dataOkDate = dataOkDate;
        this.nodifySetFiled("dataOkDate", dataOkDate);
        return this;
    }

    public Date getDataOkDate() {
        this.nodifyGetFiled("dataOkDate");
        return dataOkDate;
    }

    public OmsOrderFwEntity setDataOkDateStart(Date dataOkDateStart) {
        this.dataOkDateStart = dataOkDateStart;
        this.nodifySetFiled("dataOkDateStart", dataOkDateStart);
        return this;
    }

    public Date getDataOkDateStart() {
        this.nodifyGetFiled("dataOkDateStart");
        return dataOkDateStart;
    }

    public OmsOrderFwEntity setDataOkDateEnd(Date dataOkDateEnd) {
        this.dataOkDateEnd = dataOkDateEnd;
        this.nodifySetFiled("dataOkDateEnd", dataOkDateEnd);
        return this;
    }

    public Date getDataOkDateEnd() {
        this.nodifyGetFiled("dataOkDateEnd");
        return dataOkDateEnd;
    }
    public OmsOrderFwEntity setDataOk(String dataOk) {
        this.dataOk = dataOk;
        this.nodifySetFiled("dataOk", dataOk);
        return this;
    }

    public String getDataOk() {
        this.nodifyGetFiled("dataOk");
        return dataOk;
    }

    public OmsOrderFwEntity setArapOk(String arapOk) {
        this.arapOk = arapOk;
        this.nodifySetFiled("arapOk", arapOk);
        return this;
    }

    public String getArapOk() {
        this.nodifyGetFiled("arapOk");
        return arapOk;
    }

    public OmsOrderFwEntity setArapOkDate(Date arapOkDate) {
        this.arapOkDate = arapOkDate;
        this.nodifySetFiled("arapOkDate", arapOkDate);
        return this;
    }

    public Date getArapOkDate() {
        this.nodifyGetFiled("arapOkDate");
        return arapOkDate;
    }

    public OmsOrderFwEntity setArapOkDateStart(Date arapOkDateStart) {
        this.arapOkDateStart = arapOkDateStart;
        this.nodifySetFiled("arapOkDateStart", arapOkDateStart);
        return this;
    }

    public Date getArapOkDateStart() {
        this.nodifyGetFiled("arapOkDateStart");
        return arapOkDateStart;
    }

    public OmsOrderFwEntity setArapOkDateEnd(Date arapOkDateEnd) {
        this.arapOkDateEnd = arapOkDateEnd;
        this.nodifySetFiled("arapOkDateEnd", arapOkDateEnd);
        return this;
    }

    public Date getArapOkDateEnd() {
        this.nodifyGetFiled("arapOkDateEnd");
        return arapOkDateEnd;
    }
}
