package com.eci.project.etmsBdCargoType.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.etmsBdCargoType.entity.EtmsBdCargoTypeEntity;


/**
* 货物形态Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-03-21
*/
public interface EtmsBdCargoTypeDao extends EciBaseDao<EtmsBdCargoTypeEntity> {

}