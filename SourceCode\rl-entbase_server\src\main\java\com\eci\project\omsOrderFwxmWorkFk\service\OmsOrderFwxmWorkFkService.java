package com.eci.project.omsOrderFwxmWorkFk.service;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.eci.common.Extension;
import com.eci.common.Extensions;
import com.eci.common.db.DBHelper;
import com.eci.common.utils.CommonLib;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.omsIBillStatus.dao.OmsIBillStatusDao;
import com.eci.project.omsIBillStatus.entity.OmsIBillStatusEntity;
import com.eci.project.omsOrderFwxmCrkZzfw.dao.OmsOrderFwxmCrkZzfwDao;
import com.eci.project.omsOrderFwxmCrkZzfw.service.OmsOrderFwxmCrkZzfwService;
import com.eci.project.omsOrderFwxmWork.dao.OmsOrderFwxmWorkDao;
import com.eci.project.omsOrderFwxmWork.entity.OmsOrderFwxmWorkEntity;
import com.eci.project.omsOrderFwxmWorkFk.dao.OmsOrderFwxmWorkFkDao;
import com.eci.project.omsOrderFwxmWorkFk.entity.OmsOrderFwxmWorkFkEntity;
import com.eci.project.omsOrderFwxmWorkFk.entity.ReqOmsOrderFwxmWorkFkEntity;
import com.eci.project.omsOrderFwxmWorkFk.validate.OmsOrderFwxmWorkFkVal;
import com.eci.project.omsOrderFwxmWorkFkCar.dao.OmsOrderFwxmWorkFkCarDao;
import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.List;


/**
 * 反馈内容表头Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-05-21
 */
@Service
@Slf4j
public class OmsOrderFwxmWorkFkService implements EciBaseService<OmsOrderFwxmWorkFkEntity> {

    CommonLib cmn = CommonLib.getInstance();

    @Autowired
    private OmsOrderFwxmWorkFkDao omsOrderFwxmWorkFkDao;

    @Autowired
    private OmsOrderFwxmWorkFkVal omsOrderFwxmWorkFkVal;

    @Autowired
    private OmsOrderFwxmWorkFkCarDao omsOrderFwxmWorkFkCarDao;

    @Autowired
    private OmsOrderFwxmCrkZzfwDao omsOrderFwxmCrkZzfwDao;

    @Autowired
    private OmsOrderFwxmCrkZzfwService omsOrderFwxmCrkZzfwService;

    @Autowired
    private OmsOrderFwxmWorkDao omsOrderFwxmWorkDao;

    @Autowired
    private OmsIBillStatusDao omsIBillStatusDao;

    @Autowired
    private Extensions extensions;


    @Override
    public TgPageInfo queryPageList(OmsOrderFwxmWorkFkEntity entity) {
        EciQuery<OmsOrderFwxmWorkFkEntity> eciQuery = EciQuery.buildQuery(entity);
        List<OmsOrderFwxmWorkFkEntity> entities = omsOrderFwxmWorkFkDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderFwxmWorkFkEntity save(OmsOrderFwxmWorkFkEntity entity) {
        // 返回实体对象
        OmsOrderFwxmWorkFkEntity omsOrderFwxmWorkFkEntity = null;
        omsOrderFwxmWorkFkVal.saveValidate(entity, BllContext.getBusinessType());

        omsOrderFwxmWorkFkEntity = omsOrderFwxmWorkFkDao.insertOne(entity);
        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            omsOrderFwxmWorkFkEntity = omsOrderFwxmWorkFkDao.insertOne(entity);

        } else {

            omsOrderFwxmWorkFkEntity = omsOrderFwxmWorkFkDao.updateByEntityId(entity);

        }
        return omsOrderFwxmWorkFkEntity;
    }

    @Override
    public List<OmsOrderFwxmWorkFkEntity> selectList(OmsOrderFwxmWorkFkEntity entity) {
        return omsOrderFwxmWorkFkDao.selectList(entity);
    }

    @Override
    public OmsOrderFwxmWorkFkEntity selectOneById(Serializable id) {
        return omsOrderFwxmWorkFkDao.selectById(id);
    }


    @Override
    public void insertBatch(List<OmsOrderFwxmWorkFkEntity> list) {
        omsOrderFwxmWorkFkDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return omsOrderFwxmWorkFkDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return omsOrderFwxmWorkFkDao.deleteById(id);
    }


    /**
     * 反馈-/ 作业完成，作业数据齐全等。。。。
     **/
    @Transactional
    public OmsOrderFwxmWorkFkEntity savefk(ReqOmsOrderFwxmWorkFkEntity orderFwxmWorkFk) {

        String poStatus = Extension.getFeedbackDocStatus(orderFwxmWorkFk.getType());
        omsOrderFwxmWorkFkVal.feedBackValidate(orderFwxmWorkFk.getWorkNo(), poStatus);

        List<OmsOrderFwxmWorkEntity> workList = omsOrderFwxmWorkDao.select().eq(OmsOrderFwxmWorkEntity::getWorkNo, orderFwxmWorkFk.getWorkNo())
                .list();

        for (OmsOrderFwxmWorkEntity item : workList) {
            OmsIBillStatusEntity billStatus = new OmsIBillStatusEntity();
            billStatus.setBizRegId(item.getBizRegId());
            billStatus.setDocStatus(poStatus);
            billStatus.setGuid(IdWorker.get32UUID());
            billStatus.setOkDate(new java.util.Date());
            billStatus.setOpDate(new java.util.Date());
            billStatus.setOpFlag("0");
            billStatus.setOrderNo(item.getOrderNo());
            billStatus.setSysCode(!StringUtils.isEmpty(item.getSysCode()) ? item.getSysCode() : "OMS");
            billStatus.setTrnDate(new java.util.Date());
            billStatus.setWorkNo(item.getWorkNo());
            billStatus.setXzwtNo(item.getXzwtNo());
            omsIBillStatusDao.insertOne(billStatus);

//            PO po = new PO("SP_OMS_I_BILL_STATUS");
//            po.AddColumn("P_GUID", billStatus.GUID);
//            po.ExecuteProcedure(context.Transaction, "");

        }


        // 本来这里是存储过程的，现在直接修改表的状态
        // 修改供方协作任务 作业完成状态
        String groupCode = UserContext.getUserInfo().getCompanyCode();
        if (poStatus.equals("01")) { // 作业完成
            String comsql = " UPDATE OMS_ORDER_FWXM_WORK SET OP_COMPLETE_OK='Y' , OP_COMPLETE_OK_DATE=sysdate WHERE WORK_NO=" + cmn.SQLQ(orderFwxmWorkFk.getWorkNo()) + " AND GROUP_CODE=" + cmn.SQLQ(groupCode) + "";
            DBHelper.execute(comsql);
        }
        if (poStatus.equals("02")) { // 作业数据齐全
            String qqSql = "UPDATE OMS_ORDER_FWXM_WORK SET DATA_OK='Y' , DATA_OK_DATE=sysdate WHERE WORK_NO=" + cmn.SQLQ(orderFwxmWorkFk.getWorkNo()) + " AND GROUP_CODE=" + cmn.SQLQ(groupCode) + "";
            DBHelper.execute(qqSql);
        }

        List<OmsOrderFwxmWorkEntity> list = omsOrderFwxmWorkDao.select().eq(OmsOrderFwxmWorkEntity::getWorkNo, orderFwxmWorkFk.getWorkNo())
                .list();
        if (list.size() > 0) {
            extensions.addOmsGh(list.get(0).getOrderNo());
        }

        return orderFwxmWorkFk;
    }
}