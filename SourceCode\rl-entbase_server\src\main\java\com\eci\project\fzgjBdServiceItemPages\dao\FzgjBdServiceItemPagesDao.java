package com.eci.project.fzgjBdServiceItemPages.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.fzgjBdServiceItemPages.entity.FzgjBdServiceItemPagesEntity;
import org.apache.ibatis.annotations.Select;

import java.util.List;


/**
* 平台服务项目对应页面编辑区Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-04-07
*/
public interface FzgjBdServiceItemPagesDao extends EciBaseDao<FzgjBdServiceItemPagesEntity> {

    @Select("  SELECT A.*\n" +
            "    FROM FZGJ_BD_SERVICE_ITEM_PAGES A\n" +
            "    WHERE A.GROUP_CODE = #{groupCode}\n" +
            "    AND A.STATUS = 'Y' " +
            "      AND (\n" +
            "            EXISTS (\n" +
            "                SELECT 1 FROM OMS_ORDER_FWXM B\n" +
            "                WHERE B.FWXM_CODE = A.SERVICE_ITEM_CODE\n" +
            "                  AND B.FWXM_CODE IN #{orderFwxmList} " +
            "            OR EXISTS ( " +
            "                SELECT 1 FROM OMS_ORDER_FWXM_WORK C\n" +
            "                WHERE C.GROUP_CODE = A.GROUP_CODE\n" +
            "                  AND C.FWXM_CODE = A.SERVICE_ITEM_CODE\n" +
            "                  AND C.FWXM_CODE IN #{orderFwxmList} " +
            "            )\n" +
            "        )")
    List<FzgjBdServiceItemPagesEntity> OrderFwxmPageSearch(@Param("groupCode") String groupCode,
                                                           @Param("orderFwxm") String orderFwxmList);
}