package com.eci.project.fzgjBaseInformation.controller;

import com.eci.common.BaseProperties;
import com.eci.common.ZsrJson;
import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjBaseInformation.service.FzgjBaseInformationService;
import com.eci.project.fzgjBaseInformation.entity.FzgjBaseInformationEntity;
import com.eci.project.omsFile.entity.OmsFileEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.URLConnection;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.UUID;

/**
* 新闻通知公告信息表Controller
*
* @<NAME_EMAIL>
* @date 2025-06-18
*/
@Api(tags = "新闻通知公告信息表")
@RestController
@RequestMapping("/fzgjBaseInformation")
public class FzgjBaseInformationController extends EciBaseController {

    @Autowired
    private FzgjBaseInformationService fzgjBaseInformationService;


    @ApiOperation("新闻通知公告信息表:保存")
    @EciLog(title = "新闻通知公告信息表:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestParam(value = "imgfile", required = false) MultipartFile imgfile,@RequestParam String strentity){
        FzgjBaseInformationEntity entity=ZsrJson.parse(strentity).toObject(FzgjBaseInformationEntity.class);
        if(imgfile!=null) {
            String fileName = imgfile.getOriginalFilename();
            Path currentDir = Paths.get(System.getProperty("user.dir"));
            String filepath = "/BaseInfomation/" + (entity.getInfoType()==1?"News":"Notice") + "/" + DateUtils.getDate() + "/";
            String basePath = BaseProperties.getFilepath() + filepath;
            File dir = new File(basePath);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            //Path outputPath = currentDir.resolve(basePath);
            try {
                imgfile.transferTo(new File(basePath + fileName));
                entity.setAttachmentUrl(filepath + fileName);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        FzgjBaseInformationEntity fzgjBaseInformationEntity =fzgjBaseInformationService.save(entity);
        return ResponseMsgUtil.success(10001,fzgjBaseInformationEntity);
    }


    @ApiOperation("新闻通知公告信息表:查询列表")
    @EciLog(title = "新闻通知公告信息表:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjBaseInformationEntity entity){
        List<FzgjBaseInformationEntity> fzgjBaseInformationEntities = fzgjBaseInformationService.selectList(entity);
        return ResponseMsgUtil.success(10001,fzgjBaseInformationEntities);
    }


    @ApiOperation("新闻通知公告信息表:分页查询列表")
    @EciLog(title = "新闻通知公告信息表:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjBaseInformationEntity entity){
        TgPageInfo tgPageInfo = fzgjBaseInformationService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("新闻通知公告信息表:根据ID查一条")
    @EciLog(title = "新闻通知公告信息表:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjBaseInformationEntity entity){
        FzgjBaseInformationEntity  fzgjBaseInformationEntity = fzgjBaseInformationService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,fzgjBaseInformationEntity);
    }


    @ApiOperation("新闻通知公告信息表:根据ID删除一条")
    @EciLog(title = "新闻通知公告信息表:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjBaseInformationEntity entity){
        int count = fzgjBaseInformationService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("新闻通知公告信息表:根据ID字符串删除多条")
    @EciLog(title = "新闻通知公告信息表:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjBaseInformationEntity entity) {
        int count = fzgjBaseInformationService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }

    @ApiOperation("附件:附件预览")
    @EciLog(title = "附件:附件预览", businessType = BusinessType.OTHER)
    @GetMapping("/previewAttr")
    @EciAction()
    public void previewAttr(String fid) throws Exception {
        FzgjBaseInformationEntity  entity = fzgjBaseInformationService.selectOneById(fid);
        if (entity == null) {throw new Exception("文件不存在");}

        String basePath = BaseProperties.getFilepath() + entity.getAttachmentUrl();
        File file = new File(basePath);

        if (!file.exists()) {
            throw new Exception("文件不存在");
        }

        HttpServletResponse response = ServletUtils.getResponse();

        // 设置 MIME 类型（可选，但推荐）
        String contentType = URLConnection.guessContentTypeFromName(file.getName());
        if (contentType == null) {
            contentType = "application/octet-stream";
        }
        response.setContentType(contentType);

        // 关键修改：使用 inline 表示内联预览
        response.setHeader("Content-Disposition", "inline;filename=" + file.getName());

        try (FileInputStream inStream = new FileInputStream(file);
             ServletOutputStream outputStream = response.getOutputStream()) {

            byte[] buf = new byte[4096];
            int readLength;
            while ((readLength = inStream.read(buf)) != -1) {
                outputStream.write(buf, 0, readLength);
            }

            outputStream.flush();
        } catch (Exception ex) {
            // 可记录日志或处理异常
            ex.printStackTrace();
        }
    }


}