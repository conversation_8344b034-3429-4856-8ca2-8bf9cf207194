package com.eci.common;

import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

@Component
public class CoreComboxSearchInterceptor implements HandlerInterceptor {

//    @Override
//    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
//        // 检查请求路径
//        if ("/coreCombox/search".equals(request.getRequestURI())) {
//
//            // 获取请求 URL
//            String url = request.getRequestURL().toString();
//
//            // 获取请求参数
//            String queryString = request.getQueryString();
//
//            boolean hasKey = TgCacheHelper.hasKey("yesOrNokey");
//            if (hasKey) {
//                System.out.println("存在key 准备删除: " + url);
//                TgCacheHelper.del("yesOrNokey");
//            }
//
//            // 获取请求体
//            String body = getRequestBody(request);
//
//            System.out.println("Request URL: " + url);
//            System.out.println("Query Parameters: " + queryString);
//            System.out.println("Request Body: " + body);
////
////            // 构造自定义返回数据
////            Map<String, Object> customResponse = new HashMap<>();
////            customResponse.put("code", 200);
////            customResponse.put("message", "Custom response data");
////            customResponse.put("data", "");
////
////            // 将自定义数据写入响应
////            response.setContentType("application/json;charset=UTF-8");
////            PrintWriter writer = response.getWriter();
////            writer.write(new ObjectMapper().writeValueAsString(customResponse)); // 使用 Jackson 序列化为 JSON
////            writer.flush();
//
//            return true; // 阻止请求继续处理
//        }
//
//        return true; // 如果不是目标请求，继续执行后续逻辑
//    }
//
//    private String getRequestBody(HttpServletRequest request) {
//        try {
//            StringBuilder stringBuilder = new StringBuilder();
//            BufferedReader reader = request.getReader();
//            String line;
//            while ((line = reader.readLine()) != null) {
//                stringBuilder.append(line);
//            }
//            return stringBuilder.toString();
//        } catch (IOException e) {
//            e.printStackTrace();
//            return null;
//        }
//    }
}