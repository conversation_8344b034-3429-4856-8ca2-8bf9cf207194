<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.etmsBdTruckEjwhQz.dao.EtmsBdTruckEjwhQzDao">
    <resultMap type="EtmsBdTruckEjwhQzEntity" id="EtmsBdTruckEjwhQzResult">
        <result property="guid" column="GUID"/>
        <result property="truckGuid" column="TRUCK_GUID"/>
        <result property="whDate" column="WH_DATE"/>
        <result property="nextDate" column="NEXT_DATE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="modMark" column="MOD_MARK"/>
    </resultMap>

    <sql id="selectEtmsBdTruckEjwhQzEntityVo">
        select
            GUID,
            TRUCK_GUID,
            WH_DATE,
            NEXT_DATE,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            CREATE_USER_NAME,
            UPDATE_USER_NAME,
            MOD_MARK
        from ETMS_BD_TRUCK_EJWH_QZ
    </sql>
</mapper>