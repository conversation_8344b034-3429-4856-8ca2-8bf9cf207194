package com.eci.project.omsOrderPre.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.common.DictField;
import com.eci.common.ZsrBaseEntity;
import com.eci.crud.entity.EciBaseEntity;
import com.eci.project.omsOrder.entity.OmsOrderEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 客户委托单对象 OMS_ORDER_PRE
 * 可以自己扩展字段
 *
 * @<NAME_EMAIL>
 * @date 2025-04-15
 */
@ApiModel("客户委托单")
@TableName("OMS_ORDER_PRE")
@FieldNameConstants
public class OmsOrderPreEntity extends ZsrBaseEntity  {

    /**
     * 客户委托单协同编号，规则XTDM1908000201
     */
    @ApiModelProperty("客户委托单协同编号，规则XTDM1908000201(36)")
    @TableId("PRE_NO")
    private String preNo;

    /**
     * 来源系统，CSC固定
     */
    @ApiModelProperty("来源系统，CSC固定(10)")
    @TableField("SYS_CODE")
    private String sysCode;

    /**
     * 来源系统业务编号，CSC：客户委托编号WTDM1908000201
     */
    @ApiModelProperty("来源系统业务编号，CSC：客户委托编号WTDM1908000201(50)")
    @TableField("SYS_DOC_NO")
    private String sysDocNo;

    /**
     * 下单人
     */
    @ApiModelProperty("下单人(20)")
    @TableField("XD_USER")
    private String xdUser;

    /**
     * 下单时间
     */
    @ApiModelProperty("下单时间(7)")
    @TableField("XD_DATE")
    private Date xdDate;

    @ApiModelProperty("下单时间开始")
    @TableField(exist = false)
    private Date xdDateStart;

    @ApiModelProperty("下单时间结束")
    @TableField(exist = false)
    private Date xdDateEnd;

    /**
     * 委托方
     */
    @ApiModelProperty("委托方(200)")
    @TableField("CONSIGNEE_CODE")
    @DictField(sql = "SELECT A.CODE, A.NAME FROM CRM_CUSTOMER A")
    private String consigneeCode;

    /**
     * 实际发货方
     */
    @ApiModelProperty("实际发货方(36)")
    @TableField("SHIPPER")
    @DictField(sql = "SELECT CODE,NAME FROM CRM_CUSTOMER A WHERE A.STATUS='Y' ")
    private String shipper;

    /**
     * 实际收货方
     */
    @ApiModelProperty("实际收货方(36)")
    @TableField("RECEIVER")
    @DictField(sql = "SELECT CODE,NAME FROM CRM_CUSTOMER A WHERE A.STATUS='Y' ")
    private String receiver;

    /**
     * 客户事业部
     */
    @ApiModelProperty("客户事业部(50)")
    @TableField("CUSTOMER_BU")
    @DictField(sql = "SELECT A.CODE, A.NAME FROM CRM_CUSTOMER_KHSYB A WHERE A.STATUS = 'Y' ")
    private String customerBu;

    /**
     * 客户单据编号(委托方+客户单据编号唯一)
     */
    @ApiModelProperty("客户单据编号(委托方+客户单据编号唯一)(50)")
    @TableField("CUSTOMER_ORDER_NO")
    private String customerOrderNo;

    /**
     * 业务类型
     */
    @ApiModelProperty("业务类型(20)")
    @TableField("OP_TYPE")
    @DictField(sql = "SELECT A.CODE,A.NAME FROM FZGJ_BD_OP_TYPE A")
    private String opType;

    /**
     * 业务产品/项目
     */
    @ApiModelProperty("业务产品/项目(20)")
    @TableField("PRODUCT_CODE")
    @DictField(sql = "SELECT A.CODE, A.NAME FROM FZGJ_BD_PRODUCT A")
    private String productCode;

    /**
     * 是否加急
     */
    @ApiModelProperty("是否加急(1)")
    @TableField("IS_JJH")
    @DictField(data = {
            "{'code':'Y','name':'是'}",
            "{'code':'N','name':'否'}",
            "{'code':'1','name':'是'}",
            "{'code':'0','name':'否'}"
    })
    private String isJjh;

    /**
     * 接单日期
     */
    @ApiModelProperty("接单日期(7)")
    @TableField("OP_DATE")
    private Date opDate;

    @ApiModelProperty("接单日期开始")
    @TableField(exist = false)
    private Date opDateStart;

    @ApiModelProperty("接单日期结束")
    @TableField(exist = false)
    private Date opDateEnd;

    /**
     * 客户付款方案编号
     */
    @ApiModelProperty("客户付款方案编号(20)")
    @TableField("FKFA_CODE")
    private String fkfaCode;

    /**
     * 应收结算方(1~n个)
     */
    @ApiModelProperty("应收结算方(1~n个)(50)")
    @TableField("ACCOUNT_CODE")
    @DictField(sql = "SELECT A.CODE, A.NAME FROM BMC_CUSTOMER A  WHERE A.CODE != '-' AND A.STATUS = 'Y' ")
    private String accountCode;

    /**
     * 结算方式
     */
    @ApiModelProperty("结算方式(20)")
    @TableField("ACCOUNT_MODE")
    private String accountMode;

    /**
     * 支付方式
     */
    @ApiModelProperty("支付方式(20)")
    @TableField("PAY_MODE")
    private String payMode;

    /**
     * 受理人
     */
    @ApiModelProperty("受理人(50)")
    @TableField("JD_USER")
    private String jdUser;

    /**
     * 受理组织
     */
    @ApiModelProperty("受理组织(50)")
    @TableField("JD_NODE")
    private String jdNode;

    /**
     * 受理公司
     */
    @ApiModelProperty("受理公司(50)")
    @TableField("JD_COMPANY")
    private String jdCompany;

    /**
     * 销售员
     */
    @ApiModelProperty("销售员(50)")
    @TableField("XS_USER")
    private String xsUser;

    /**
     * 销售组织
     */
    @ApiModelProperty("销售组织(50)")
    @TableField("XS_NODE")
    private String xsNode;

    /**
     * 结算员
     */
    @ApiModelProperty("结算员(50)")
    @TableField("JS_USER")
    private String jsUser;

    /**
     * 结算组织
     */
    @ApiModelProperty("结算组织(50)")
    @TableField("JS_NODE")
    private String jsNode;

    /**
     * 协作方案编号
     */
    @ApiModelProperty("协作方案编号(36)")
    @TableField("XZFA_NO")
    private String xzfaNo;

    /**
     * 状态
     */
    @ApiModelProperty("状态(20)")
    @TableField("STATUS")
    private String status;

    /**
     * 阶段
     */
    @ApiModelProperty("阶段(20)")
    @TableField("STAGE")
    private String stage;

    /**
     * 数据齐全标志
     */
    @ApiModelProperty("数据齐全标志(1)")
    @TableField("DATA_OK")
    private String dataOk;

    /**
     * 结算完成标志
     */
    @ApiModelProperty("结算完成标志(1)")
    @TableField("ARAP_OK")
    private String arapOk;

    /**
     * 退单/作废标志
     */
    @ApiModelProperty("退单/作废标志(1)")
    @TableField("CANCEL_FLAG")
    private String cancelFlag;

    /**
     * 退单/作废原因
     */
    @ApiModelProperty("退单/作废原因(500)")
    @TableField("CANCEL_REASON")
    private String cancelReason;

    /**
     * 业务备注
     */
    @ApiModelProperty("业务备注(1,000)")
    @TableField("BIZ_MEMO")
    private String bizMemo;

    /**
     * 结算备注
     */
    @ApiModelProperty("结算备注(500)")
    @TableField("ACCOUNT_MEMO")
    private String accountMemo;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
     * 创建日期
     */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist = false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist = false)
    private Date createDateEnd;

    /**
     * 最后修改人
     */
    @ApiModelProperty("最后修改人(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
     * 最后修改人
     */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
     * 最后修改日期
     */
    @ApiModelProperty("最后修改日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("最后修改日期开始")
    @TableField(exist = false)
    private Date updateDateStart;

    @ApiModelProperty("最后修改日期结束")
    @TableField(exist = false)
    private Date updateDateEnd;

    /**
     * 归属公司
     */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
     * 归属公司
     */
    @ApiModelProperty("归属公司(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
     * 归属组织
     */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
     * 归属组织
     */
    @ApiModelProperty("归属组织(200)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
     * 归属集团
     */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
     * 归属集团
     */
    @ApiModelProperty("归属集团(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
     * 联系电话
     */
    @ApiModelProperty("联系电话(200)")
    @TableField("TEL")
    private String tel;

    /**
     * 邮箱
     */
    @ApiModelProperty("邮箱(200)")
    @TableField("E_MAIL")
    private String eMail;

    /**
     * 要求完成时间
     */
    @ApiModelProperty("要求完成时间(7)")
    @TableField("REQUEST_OK_DATE")
    private Date requestOkDate;

    @ApiModelProperty("要求完成时间开始")
    @TableField(exist = false)
    private Date requestOkDateStart;

    @ApiModelProperty("要求完成时间结束")
    @TableField(exist = false)
    private Date requestOkDateEnd;

    /**
     * 服务类型，多个逗号分隔
     */
    @ApiModelProperty("服务类型，多个逗号分隔(100)")
    @TableField("FWLX_CODE")
    private String fwlxCode;

    /**
     * 送审人
     */
    @ApiModelProperty("送审人(20)")
    @TableField("SEND_USER")
    private String sendUser;

    /**
     * 送审时间
     */
    @ApiModelProperty("送审时间(7)")
    @TableField("SEND_DATE")
    private Date sendDate;

    @ApiModelProperty("送审时间开始")
    @TableField(exist = false)
    private Date sendDateStart;

    @ApiModelProperty("送审时间结束")
    @TableField(exist = false)
    private Date sendDateEnd;

    /**
     * 送审组织
     */
    @ApiModelProperty("送审组织(36)")
    @TableField("SEND_NODE")
    private String sendNode;

    /**
     * 送审组织
     */
    @ApiModelProperty("送审组织(200)")
    @TableField("SEND_NODE_NAME")
    private String sendNodeName;

    /**
     * 审核状态
     */
    @ApiModelProperty("审核状态(10)")
    @TableField("AUDIT_STATUS")
    @DictField(data = {
                 "{'code':'TG','name':'通过'}",
                 "{'code':'TH','name':'退回'}"
             })
    private String auditStatus;

    /**
     * 审核人
     */
    @ApiModelProperty("审核人(20)")
    @TableField("AUDIT_USER")
    private String auditUser;

    /**
     * 审核时间
     */
    @ApiModelProperty("审核时间(7)")
    @TableField("AUDIT_DATE")
    private Date auditDate;

    @ApiModelProperty("审核时间开始")
    @TableField(exist = false)
    private Date auditDateStart;

    @ApiModelProperty("审核时间结束")
    @TableField(exist = false)
    private Date auditDateEnd;

    /**
     * 审核组织
     */
    @ApiModelProperty("审核组织(36)")
    @TableField("AUDIT_NODE")
    private String auditNode;

    /**
     * 审核人
     */
    @ApiModelProperty("审核人(200)")
    @TableField("AUDIT_NODE_NAME")
    private String auditNodeName;

    /**
     * 是否作废
     */
    @ApiModelProperty("是否作废(1)")
    @TableField("IS_CANCEL")
    private String isCancel;

    /**
     * 作废时间
     */
    @ApiModelProperty("作废时间(7)")
    @TableField("CANCEL_DATE")
    private Date cancelDate;

    @ApiModelProperty("作废时间开始")
    @TableField(exist = false)
    private Date cancelDateStart;

    @ApiModelProperty("作废时间结束")
    @TableField(exist = false)
    private Date cancelDateEnd;

    /**
     * 作废人
     */
    @ApiModelProperty("作废人(20)")
    @TableField("CANCEL_USER")
    private String cancelUser;

    /**
     * 作废人
     */
    @ApiModelProperty("作废人(50)")
    @TableField("CANCEL_USER_NAME")
    private String cancelUserName;

    /**
     * 作废组织
     */
    @ApiModelProperty("作废组织(36)")
    @TableField("CANCEL_NODE")
    private String cancelNode;

    /**
     * 作废组织名称
     */
    @ApiModelProperty("作废组织名称(100)")
    @TableField("CANCEL_NODE_NAME")
    private String cancelNodeName;

    /**
     * 作废原因
     */
    @ApiModelProperty("作废原因(500)")
    @TableField("CANCEL_REMARK")
    private String cancelRemark;

    /**
     * 审核备注
     */
    @ApiModelProperty("审核备注(500)")
    @TableField("AUDIT_MEMO")
    private String auditMemo;

    /**
     * 送审人
     */
    @ApiModelProperty("送审人(50)")
    @TableField("SEND_USER_NAME")
    private String sendUserName;

    /**
     * 审核人
     */
    @ApiModelProperty("审核人(50)")
    @TableField("AUDIT_USER_NAME")
    private String auditUserName;

    /**
     * 服务类型，多个逗号分隔
     */
    @ApiModelProperty("服务类型，多个逗号分隔(200)")
    @TableField("FWLX_NAME")
    private String fwlxName;

    /**
     * 手机APP是否已读
     */
    @ApiModelProperty("手机APP是否已读(1)")
    @TableField("APP_READ")
    private String appRead;

    /**
     * 受理企业所属集团
     */
    @ApiModelProperty("受理企业所属集团(50)")
    @TableField("JD_GROUP_CODE")
    private String jdGroupCode;

    /**
     * 作业完成时间
     */
    @ApiModelProperty("作业完成时间(7)")
    @TableField("OP_COMPLETE_OK_DATE")
    private Date opCompleteOkDate;

    @ApiModelProperty("作业完成时间开始")
    @TableField(exist = false)
    private Date opCompleteOkDateStart;

    @ApiModelProperty("作业完成时间结束")
    @TableField(exist = false)
    private Date opCompleteOkDateEnd;

    /**
     * 作业数据齐全时间
     */
    @ApiModelProperty("作业数据齐全时间(7)")
    @TableField("DATA_OK_DATE")
    private Date dataOkDate;

    @ApiModelProperty("作业数据齐全时间开始")
    @TableField(exist = false)
    private Date dataOkDateStart;

    @ApiModelProperty("作业数据齐全时间结束")
    @TableField(exist = false)
    private Date dataOkDateEnd;

    /**
     * 结算完成日期
     */
    @ApiModelProperty("结算完成日期(7)")
    @TableField("ARAP_OK_DATE")
    private Date arapOkDate;

    @ApiModelProperty("结算完成日期开始")
    @TableField(exist = false)
    private Date arapOkDateStart;

    @ApiModelProperty("结算完成日期结束")
    @TableField(exist = false)
    private Date arapOkDateEnd;

    /**
     * 作业完成标识
     */
    @ApiModelProperty("作业完成标识(1)")
    @TableField("OP_COMPLETE_OK")
    private String opCompleteOk;

    /**
     * 语言
     */
    @ApiModelProperty("语言(10)")
    @TableField("LANGUAGE")
    private String language;

    /**
     * 下单人
     */
    @ApiModelProperty("下单人(50)")
    @TableField("XD_USER_NAME")
    private String xdUserName;

    /**
     * 下单组织
     */
    @ApiModelProperty("下单组织(50)")
    @TableField("XD_NODE_CODE")
    private String xdNodeCode;

    /**
     * 下单组织
     */
    @ApiModelProperty("下单组织(50)")
    @TableField("XD_NODE_NAME")
    private String xdNodeName;

    /**
     * 下单公司
     */
    @ApiModelProperty("下单公司(50)")
    @TableField("XD_COMPANY_CODE")
    private String xdCompanyCode;

    /**
     * 下单公司
     */
    @ApiModelProperty("下单公司(50)")
    @TableField("XD_COMPANY_NAME")
    private String xdCompanyName;

    /**
     * 下单企业所属集团
     */
    @ApiModelProperty("下单企业所属集团(50)")
    @TableField("XD_GROUP_CODE")
    private String xdGroupCode;

    /**
     * 下单企业所属集团
     */
    @ApiModelProperty("下单企业所属集团(50)")
    @TableField("XD_GROUP_NAME")
    private String xdGroupName;

    /**
     * 受理人
     */
    @ApiModelProperty("受理人(50)")
    @TableField("JD_USER_NAME")
    private String jdUserName;

    /**
     * 业务数据唯一注册编号
     */
    @ApiModelProperty("业务数据唯一注册编号(50)")
    @TableField("BIZ_REG_ID")
    private String bizRegId;

    /**
     * 确认接单必填项
     */
    @ApiModelProperty("确认接单必填项(1)")
    @TableField("IS_QRJD")
    private String isQrjd;

    /**
     * 进出流向
     */
    @ApiModelProperty("进出流向(1)")
    @TableField("IE")
    private String ie;

    /**
     * 预估里程数
     */
    @ApiModelProperty("预估里程数(22)")
    @TableField("YG_MILEAGE")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal ygMileage;

    /**
     * 是否报关
     */
    @ApiModelProperty("是否报关(1)")
    @TableField("IS_BG")
    private String isBg;

    /**
     * 账册号
     */
    @ApiModelProperty("账册号(50)")
    @TableField("EMS_NO")
    private String emsNo;

    /**
     * 预估里程数提货
     */
    @ApiModelProperty("预估里程数提货(22)")
    @TableField("YG_MILEAGE_T")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal ygMileageT;

    /**
     * 预估里程数收货
     */
    @ApiModelProperty("预估里程数收货(22)")
    @TableField("YG_MILEAGE_S")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal ygMileageS;

    /**
     * 快递单号
     */
    @ApiModelProperty("快递单号(50)")
    @TableField("EXPRESS_NO")
    private String expressNo;

    /**
     * 车牌号
     */
    @ApiModelProperty("车牌号(50)")
    @TableField("CAR_NO")
    private String carNo;

    /**
     * 司机电话
     */
    @ApiModelProperty("司机电话(50)")
    @TableField("DRIVER_PHONE")
    private String driverPhone;

    /**
     * 总件数
     */
    @ApiModelProperty("总件数(22)")
    @TableField("TOTAL_PIECES")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal totalPieces;

    /**
     * 件数单位
     */
    @ApiModelProperty("件数单位(50)")
    @TableField("PIECE_UNIT")
    private String pieceUnit;

    /**
     * 自送时间
     */
    @ApiModelProperty("自送时间(7)")
    @TableField("ZS_DATE")
    private Date zsDate;

    @ApiModelProperty("自送时间开始")
    @TableField(exist = false)
    private Date zsDateStart;

    @ApiModelProperty("自送时间结束")
    @TableField(exist = false)
    private Date zsDateEnd;

    /**
     * 是否有明细
     */
    @ApiModelProperty("是否有明细(1)")
    @TableField("IS_DETAIL")
    private String isDetail;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public OmsOrderPreEntity() {
        this.setSubClazz(OmsOrderPreEntity.class);
    }

    public OmsOrderPreEntity setPreNo(String preNo) {
        this.preNo = preNo;
        this.nodifySetFiled("preNo", preNo);
        return this;
    }

    public String getPreNo() {
        this.nodifyGetFiled("preNo");
        return preNo;
    }

    public OmsOrderPreEntity setSysCode(String sysCode) {
        this.sysCode = sysCode;
        this.nodifySetFiled("sysCode", sysCode);
        return this;
    }

    public String getSysCode() {
        this.nodifyGetFiled("sysCode");
        return sysCode;
    }

    public OmsOrderPreEntity setSysDocNo(String sysDocNo) {
        this.sysDocNo = sysDocNo;
        this.nodifySetFiled("sysDocNo", sysDocNo);
        return this;
    }

    public String getSysDocNo() {
        this.nodifyGetFiled("sysDocNo");
        return sysDocNo;
    }

    public OmsOrderPreEntity setXdUser(String xdUser) {
        this.xdUser = xdUser;
        this.nodifySetFiled("xdUser", xdUser);
        return this;
    }

    public String getXdUser() {
        this.nodifyGetFiled("xdUser");
        return xdUser;
    }

    public OmsOrderPreEntity setXdDate(Date xdDate) {
        this.xdDate = xdDate;
        this.nodifySetFiled("xdDate", xdDate);
        return this;
    }

    public Date getXdDate() {
        this.nodifyGetFiled("xdDate");
        return xdDate;
    }

    public OmsOrderPreEntity setXdDateStart(Date xdDateStart) {
        this.xdDateStart = xdDateStart;
        this.nodifySetFiled("xdDateStart", xdDateStart);
        return this;
    }

    public Date getXdDateStart() {
        this.nodifyGetFiled("xdDateStart");
        return xdDateStart;
    }

    public OmsOrderPreEntity setXdDateEnd(Date xdDateEnd) {
        this.xdDateEnd = xdDateEnd;
        this.nodifySetFiled("xdDateEnd", xdDateEnd);
        return this;
    }

    public Date getXdDateEnd() {
        this.nodifyGetFiled("xdDateEnd");
        return xdDateEnd;
    }

    public OmsOrderPreEntity setConsigneeCode(String consigneeCode) {
        this.consigneeCode = consigneeCode;
        this.nodifySetFiled("consigneeCode", consigneeCode);
        return this;
    }

    public String getConsigneeCode() {
        this.nodifyGetFiled("consigneeCode");
        return consigneeCode;
    }

    public OmsOrderPreEntity setShipper(String shipper) {
        this.shipper = shipper;
        this.nodifySetFiled("shipper", shipper);
        return this;
    }

    public String getShipper() {
        this.nodifyGetFiled("shipper");
        return shipper;
    }

    public OmsOrderPreEntity setReceiver(String receiver) {
        this.receiver = receiver;
        this.nodifySetFiled("receiver", receiver);
        return this;
    }

    public String getReceiver() {
        this.nodifyGetFiled("receiver");
        return receiver;
    }

    public OmsOrderPreEntity setCustomerBu(String customerBu) {
        this.customerBu = customerBu;
        this.nodifySetFiled("customerBu", customerBu);
        return this;
    }

    public String getCustomerBu() {
        this.nodifyGetFiled("customerBu");
        return customerBu;
    }

    public OmsOrderPreEntity setCustomerOrderNo(String customerOrderNo) {
        this.customerOrderNo = customerOrderNo;
        this.nodifySetFiled("customerOrderNo", customerOrderNo);
        return this;
    }

    public String getCustomerOrderNo() {
        this.nodifyGetFiled("customerOrderNo");
        return customerOrderNo;
    }

    public OmsOrderPreEntity setOpType(String opType) {
        this.opType = opType;
        this.nodifySetFiled("opType", opType);
        return this;
    }

    public String getOpType() {
        this.nodifyGetFiled("opType");
        return opType;
    }

    public OmsOrderPreEntity setProductCode(String productCode) {
        this.productCode = productCode;
        this.nodifySetFiled("productCode", productCode);
        return this;
    }

    public String getProductCode() {
        this.nodifyGetFiled("productCode");
        return productCode;
    }

    public OmsOrderPreEntity setIsJjh(String isJjh) {
        this.isJjh = isJjh;
        this.nodifySetFiled("isJjh", isJjh);
        return this;
    }

    public String getIsJjh() {
        this.nodifyGetFiled("isJjh");
        return isJjh;
    }

    public OmsOrderPreEntity setOpDate(Date opDate) {
        this.opDate = opDate;
        this.nodifySetFiled("opDate", opDate);
        return this;
    }

    public Date getOpDate() {
        this.nodifyGetFiled("opDate");
        return opDate;
    }

    public OmsOrderPreEntity setOpDateStart(Date opDateStart) {
        this.opDateStart = opDateStart;
        this.nodifySetFiled("opDateStart", opDateStart);
        return this;
    }

    public Date getOpDateStart() {
        this.nodifyGetFiled("opDateStart");
        return opDateStart;
    }

    public OmsOrderPreEntity setOpDateEnd(Date opDateEnd) {
        this.opDateEnd = opDateEnd;
        this.nodifySetFiled("opDateEnd", opDateEnd);
        return this;
    }

    public Date getOpDateEnd() {
        this.nodifyGetFiled("opDateEnd");
        return opDateEnd;
    }

    public OmsOrderPreEntity setFkfaCode(String fkfaCode) {
        this.fkfaCode = fkfaCode;
        this.nodifySetFiled("fkfaCode", fkfaCode);
        return this;
    }

    public String getFkfaCode() {
        this.nodifyGetFiled("fkfaCode");
        return fkfaCode;
    }

    public OmsOrderPreEntity setAccountCode(String accountCode) {
        this.accountCode = accountCode;
        this.nodifySetFiled("accountCode", accountCode);
        return this;
    }

    public String getAccountCode() {
        this.nodifyGetFiled("accountCode");
        return accountCode;
    }

    public OmsOrderPreEntity setAccountMode(String accountMode) {
        this.accountMode = accountMode;
        this.nodifySetFiled("accountMode", accountMode);
        return this;
    }

    public String getAccountMode() {
        this.nodifyGetFiled("accountMode");
        return accountMode;
    }

    public OmsOrderPreEntity setPayMode(String payMode) {
        this.payMode = payMode;
        this.nodifySetFiled("payMode", payMode);
        return this;
    }

    public String getPayMode() {
        this.nodifyGetFiled("payMode");
        return payMode;
    }

    public OmsOrderPreEntity setJdUser(String jdUser) {
        this.jdUser = jdUser;
        this.nodifySetFiled("jdUser", jdUser);
        return this;
    }

    public String getJdUser() {
        this.nodifyGetFiled("jdUser");
        return jdUser;
    }

    public OmsOrderPreEntity setJdNode(String jdNode) {
        this.jdNode = jdNode;
        this.nodifySetFiled("jdNode", jdNode);
        return this;
    }

    public String getJdNode() {
        this.nodifyGetFiled("jdNode");
        return jdNode;
    }

    public OmsOrderPreEntity setJdCompany(String jdCompany) {
        this.jdCompany = jdCompany;
        this.nodifySetFiled("jdCompany", jdCompany);
        return this;
    }

    public String getJdCompany() {
        this.nodifyGetFiled("jdCompany");
        return jdCompany;
    }

    public OmsOrderPreEntity setXsUser(String xsUser) {
        this.xsUser = xsUser;
        this.nodifySetFiled("xsUser", xsUser);
        return this;
    }

    public String getXsUser() {
        this.nodifyGetFiled("xsUser");
        return xsUser;
    }

    public OmsOrderPreEntity setXsNode(String xsNode) {
        this.xsNode = xsNode;
        this.nodifySetFiled("xsNode", xsNode);
        return this;
    }

    public String getXsNode() {
        this.nodifyGetFiled("xsNode");
        return xsNode;
    }

    public OmsOrderPreEntity setJsUser(String jsUser) {
        this.jsUser = jsUser;
        this.nodifySetFiled("jsUser", jsUser);
        return this;
    }

    public String getJsUser() {
        this.nodifyGetFiled("jsUser");
        return jsUser;
    }

    public OmsOrderPreEntity setJsNode(String jsNode) {
        this.jsNode = jsNode;
        this.nodifySetFiled("jsNode", jsNode);
        return this;
    }

    public String getJsNode() {
        this.nodifyGetFiled("jsNode");
        return jsNode;
    }

    public OmsOrderPreEntity setXzfaNo(String xzfaNo) {
        this.xzfaNo = xzfaNo;
        this.nodifySetFiled("xzfaNo", xzfaNo);
        return this;
    }

    public String getXzfaNo() {
        this.nodifyGetFiled("xzfaNo");
        return xzfaNo;
    }

    public OmsOrderPreEntity setStatus(String status) {
        this.status = status;
        this.nodifySetFiled("status", status);
        return this;
    }

    public String getStatus() {
        this.nodifyGetFiled("status");
        return status;
    }

    public OmsOrderPreEntity setStage(String stage) {
        this.stage = stage;
        this.nodifySetFiled("stage", stage);
        return this;
    }

    public String getStage() {
        this.nodifyGetFiled("stage");
        return stage;
    }

    public OmsOrderPreEntity setDataOk(String dataOk) {
        this.dataOk = dataOk;
        this.nodifySetFiled("dataOk", dataOk);
        return this;
    }

    public String getDataOk() {
        this.nodifyGetFiled("dataOk");
        return dataOk;
    }

    public OmsOrderPreEntity setArapOk(String arapOk) {
        this.arapOk = arapOk;
        this.nodifySetFiled("arapOk", arapOk);
        return this;
    }

    public String getArapOk() {
        this.nodifyGetFiled("arapOk");
        return arapOk;
    }

    public OmsOrderPreEntity setCancelFlag(String cancelFlag) {
        this.cancelFlag = cancelFlag;
        this.nodifySetFiled("cancelFlag", cancelFlag);
        return this;
    }

    public String getCancelFlag() {
        this.nodifyGetFiled("cancelFlag");
        return cancelFlag;
    }

    public OmsOrderPreEntity setCancelReason(String cancelReason) {
        this.cancelReason = cancelReason;
        this.nodifySetFiled("cancelReason", cancelReason);
        return this;
    }

    public String getCancelReason() {
        this.nodifyGetFiled("cancelReason");
        return cancelReason;
    }

    public OmsOrderPreEntity setBizMemo(String bizMemo) {
        this.bizMemo = bizMemo;
        this.nodifySetFiled("bizMemo", bizMemo);
        return this;
    }

    public String getBizMemo() {
        this.nodifyGetFiled("bizMemo");
        return bizMemo;
    }

    public OmsOrderPreEntity setAccountMemo(String accountMemo) {
        this.accountMemo = accountMemo;
        this.nodifySetFiled("accountMemo", accountMemo);
        return this;
    }

    public String getAccountMemo() {
        this.nodifyGetFiled("accountMemo");
        return accountMemo;
    }

    public OmsOrderPreEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public OmsOrderPreEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public OmsOrderPreEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public OmsOrderPreEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public OmsOrderPreEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }

    public OmsOrderPreEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public OmsOrderPreEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public OmsOrderPreEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public OmsOrderPreEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public OmsOrderPreEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }

    public OmsOrderPreEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public OmsOrderPreEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public OmsOrderPreEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public OmsOrderPreEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public OmsOrderPreEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public OmsOrderPreEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public OmsOrderPreEntity setTel(String tel) {
        this.tel = tel;
        this.nodifySetFiled("tel", tel);
        return this;
    }

    public String getTel() {
        this.nodifyGetFiled("tel");
        return tel;
    }

    public OmsOrderPreEntity seteMail(String eMail) {
        this.eMail = eMail;
        this.nodifySetFiled("eMail", eMail);
        return this;
    }

    public String geteMail() {
        this.nodifyGetFiled("eMail");
        return eMail;
    }

    public OmsOrderPreEntity setRequestOkDate(Date requestOkDate) {
        this.requestOkDate = requestOkDate;
        this.nodifySetFiled("requestOkDate", requestOkDate);
        return this;
    }

    public Date getRequestOkDate() {
        this.nodifyGetFiled("requestOkDate");
        return requestOkDate;
    }

    public OmsOrderPreEntity setRequestOkDateStart(Date requestOkDateStart) {
        this.requestOkDateStart = requestOkDateStart;
        this.nodifySetFiled("requestOkDateStart", requestOkDateStart);
        return this;
    }

    public Date getRequestOkDateStart() {
        this.nodifyGetFiled("requestOkDateStart");
        return requestOkDateStart;
    }

    public OmsOrderPreEntity setRequestOkDateEnd(Date requestOkDateEnd) {
        this.requestOkDateEnd = requestOkDateEnd;
        this.nodifySetFiled("requestOkDateEnd", requestOkDateEnd);
        return this;
    }

    public Date getRequestOkDateEnd() {
        this.nodifyGetFiled("requestOkDateEnd");
        return requestOkDateEnd;
    }

    public OmsOrderPreEntity setFwlxCode(String fwlxCode) {
        this.fwlxCode = fwlxCode;
        this.nodifySetFiled("fwlxCode", fwlxCode);
        return this;
    }

    public String getFwlxCode() {
        this.nodifyGetFiled("fwlxCode");
        return fwlxCode;
    }

    public OmsOrderPreEntity setSendUser(String sendUser) {
        this.sendUser = sendUser;
        this.nodifySetFiled("sendUser", sendUser);
        return this;
    }

    public String getSendUser() {
        this.nodifyGetFiled("sendUser");
        return sendUser;
    }

    public OmsOrderPreEntity setSendDate(Date sendDate) {
        this.sendDate = sendDate;
        this.nodifySetFiled("sendDate", sendDate);
        return this;
    }

    public Date getSendDate() {
        this.nodifyGetFiled("sendDate");
        return sendDate;
    }

    public OmsOrderPreEntity setSendDateStart(Date sendDateStart) {
        this.sendDateStart = sendDateStart;
        this.nodifySetFiled("sendDateStart", sendDateStart);
        return this;
    }

    public Date getSendDateStart() {
        this.nodifyGetFiled("sendDateStart");
        return sendDateStart;
    }

    public OmsOrderPreEntity setSendDateEnd(Date sendDateEnd) {
        this.sendDateEnd = sendDateEnd;
        this.nodifySetFiled("sendDateEnd", sendDateEnd);
        return this;
    }

    public Date getSendDateEnd() {
        this.nodifyGetFiled("sendDateEnd");
        return sendDateEnd;
    }

    public OmsOrderPreEntity setSendNode(String sendNode) {
        this.sendNode = sendNode;
        this.nodifySetFiled("sendNode", sendNode);
        return this;
    }

    public String getSendNode() {
        this.nodifyGetFiled("sendNode");
        return sendNode;
    }

    public OmsOrderPreEntity setSendNodeName(String sendNodeName) {
        this.sendNodeName = sendNodeName;
        this.nodifySetFiled("sendNodeName", sendNodeName);
        return this;
    }

    public String getSendNodeName() {
        this.nodifyGetFiled("sendNodeName");
        return sendNodeName;
    }

    public OmsOrderPreEntity setAuditStatus(String auditStatus) {
        this.auditStatus = auditStatus;
        this.nodifySetFiled("auditStatus", auditStatus);
        return this;
    }

    public String getAuditStatus() {
        this.nodifyGetFiled("auditStatus");
        return auditStatus;
    }

    public OmsOrderPreEntity setAuditUser(String auditUser) {
        this.auditUser = auditUser;
        this.nodifySetFiled("auditUser", auditUser);
        return this;
    }

    public String getAuditUser() {
        this.nodifyGetFiled("auditUser");
        return auditUser;
    }

    public OmsOrderPreEntity setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
        this.nodifySetFiled("auditDate", auditDate);
        return this;
    }

    public Date getAuditDate() {
        this.nodifyGetFiled("auditDate");
        return auditDate;
    }

    public OmsOrderPreEntity setAuditDateStart(Date auditDateStart) {
        this.auditDateStart = auditDateStart;
        this.nodifySetFiled("auditDateStart", auditDateStart);
        return this;
    }

    public Date getAuditDateStart() {
        this.nodifyGetFiled("auditDateStart");
        return auditDateStart;
    }

    public OmsOrderPreEntity setAuditDateEnd(Date auditDateEnd) {
        this.auditDateEnd = auditDateEnd;
        this.nodifySetFiled("auditDateEnd", auditDateEnd);
        return this;
    }

    public Date getAuditDateEnd() {
        this.nodifyGetFiled("auditDateEnd");
        return auditDateEnd;
    }

    public OmsOrderPreEntity setAuditNode(String auditNode) {
        this.auditNode = auditNode;
        this.nodifySetFiled("auditNode", auditNode);
        return this;
    }

    public String getAuditNode() {
        this.nodifyGetFiled("auditNode");
        return auditNode;
    }

    public OmsOrderPreEntity setAuditNodeName(String auditNodeName) {
        this.auditNodeName = auditNodeName;
        this.nodifySetFiled("auditNodeName", auditNodeName);
        return this;
    }

    public String getAuditNodeName() {
        this.nodifyGetFiled("auditNodeName");
        return auditNodeName;
    }

    public OmsOrderPreEntity setIsCancel(String isCancel) {
        this.isCancel = isCancel;
        this.nodifySetFiled("isCancel", isCancel);
        return this;
    }

    public String getIsCancel() {
        this.nodifyGetFiled("isCancel");
        return isCancel;
    }

    public OmsOrderPreEntity setCancelDate(Date cancelDate) {
        this.cancelDate = cancelDate;
        this.nodifySetFiled("cancelDate", cancelDate);
        return this;
    }

    public Date getCancelDate() {
        this.nodifyGetFiled("cancelDate");
        return cancelDate;
    }

    public OmsOrderPreEntity setCancelDateStart(Date cancelDateStart) {
        this.cancelDateStart = cancelDateStart;
        this.nodifySetFiled("cancelDateStart", cancelDateStart);
        return this;
    }

    public Date getCancelDateStart() {
        this.nodifyGetFiled("cancelDateStart");
        return cancelDateStart;
    }

    public OmsOrderPreEntity setCancelDateEnd(Date cancelDateEnd) {
        this.cancelDateEnd = cancelDateEnd;
        this.nodifySetFiled("cancelDateEnd", cancelDateEnd);
        return this;
    }

    public Date getCancelDateEnd() {
        this.nodifyGetFiled("cancelDateEnd");
        return cancelDateEnd;
    }

    public OmsOrderPreEntity setCancelUser(String cancelUser) {
        this.cancelUser = cancelUser;
        this.nodifySetFiled("cancelUser", cancelUser);
        return this;
    }

    public String getCancelUser() {
        this.nodifyGetFiled("cancelUser");
        return cancelUser;
    }

    public OmsOrderPreEntity setCancelUserName(String cancelUserName) {
        this.cancelUserName = cancelUserName;
        this.nodifySetFiled("cancelUserName", cancelUserName);
        return this;
    }

    public String getCancelUserName() {
        this.nodifyGetFiled("cancelUserName");
        return cancelUserName;
    }

    public OmsOrderPreEntity setCancelNode(String cancelNode) {
        this.cancelNode = cancelNode;
        this.nodifySetFiled("cancelNode", cancelNode);
        return this;
    }

    public String getCancelNode() {
        this.nodifyGetFiled("cancelNode");
        return cancelNode;
    }

    public OmsOrderPreEntity setCancelNodeName(String cancelNodeName) {
        this.cancelNodeName = cancelNodeName;
        this.nodifySetFiled("cancelNodeName", cancelNodeName);
        return this;
    }

    public String getCancelNodeName() {
        this.nodifyGetFiled("cancelNodeName");
        return cancelNodeName;
    }

    public OmsOrderPreEntity setCancelRemark(String cancelRemark) {
        this.cancelRemark = cancelRemark;
        this.nodifySetFiled("cancelRemark", cancelRemark);
        return this;
    }

    public String getCancelRemark() {
        this.nodifyGetFiled("cancelRemark");
        return cancelRemark;
    }

    public OmsOrderPreEntity setAuditMemo(String auditMemo) {
        this.auditMemo = auditMemo;
        this.nodifySetFiled("auditMemo", auditMemo);
        return this;
    }

    public String getAuditMemo() {
        this.nodifyGetFiled("auditMemo");
        return auditMemo;
    }

    public OmsOrderPreEntity setSendUserName(String sendUserName) {
        this.sendUserName = sendUserName;
        this.nodifySetFiled("sendUserName", sendUserName);
        return this;
    }

    public String getSendUserName() {
        this.nodifyGetFiled("sendUserName");
        return sendUserName;
    }

    public OmsOrderPreEntity setAuditUserName(String auditUserName) {
        this.auditUserName = auditUserName;
        this.nodifySetFiled("auditUserName", auditUserName);
        return this;
    }

    public String getAuditUserName() {
        this.nodifyGetFiled("auditUserName");
        return auditUserName;
    }

    public OmsOrderPreEntity setFwlxName(String fwlxName) {
        this.fwlxName = fwlxName;
        this.nodifySetFiled("fwlxName", fwlxName);
        return this;
    }

    public String getFwlxName() {
        this.nodifyGetFiled("fwlxName");
        return fwlxName;
    }

    public OmsOrderPreEntity setAppRead(String appRead) {
        this.appRead = appRead;
        this.nodifySetFiled("appRead", appRead);
        return this;
    }

    public String getAppRead() {
        this.nodifyGetFiled("appRead");
        return appRead;
    }

    public OmsOrderPreEntity setJdGroupCode(String jdGroupCode) {
        this.jdGroupCode = jdGroupCode;
        this.nodifySetFiled("jdGroupCode", jdGroupCode);
        return this;
    }

    public String getJdGroupCode() {
        this.nodifyGetFiled("jdGroupCode");
        return jdGroupCode;
    }

    public OmsOrderPreEntity setOpCompleteOkDate(Date opCompleteOkDate) {
        this.opCompleteOkDate = opCompleteOkDate;
        this.nodifySetFiled("opCompleteOkDate", opCompleteOkDate);
        return this;
    }

    public Date getOpCompleteOkDate() {
        this.nodifyGetFiled("opCompleteOkDate");
        return opCompleteOkDate;
    }

    public OmsOrderPreEntity setOpCompleteOkDateStart(Date opCompleteOkDateStart) {
        this.opCompleteOkDateStart = opCompleteOkDateStart;
        this.nodifySetFiled("opCompleteOkDateStart", opCompleteOkDateStart);
        return this;
    }

    public Date getOpCompleteOkDateStart() {
        this.nodifyGetFiled("opCompleteOkDateStart");
        return opCompleteOkDateStart;
    }

    public OmsOrderPreEntity setOpCompleteOkDateEnd(Date opCompleteOkDateEnd) {
        this.opCompleteOkDateEnd = opCompleteOkDateEnd;
        this.nodifySetFiled("opCompleteOkDateEnd", opCompleteOkDateEnd);
        return this;
    }

    public Date getOpCompleteOkDateEnd() {
        this.nodifyGetFiled("opCompleteOkDateEnd");
        return opCompleteOkDateEnd;
    }

    public OmsOrderPreEntity setDataOkDate(Date dataOkDate) {
        this.dataOkDate = dataOkDate;
        this.nodifySetFiled("dataOkDate", dataOkDate);
        return this;
    }

    public Date getDataOkDate() {
        this.nodifyGetFiled("dataOkDate");
        return dataOkDate;
    }

    public OmsOrderPreEntity setDataOkDateStart(Date dataOkDateStart) {
        this.dataOkDateStart = dataOkDateStart;
        this.nodifySetFiled("dataOkDateStart", dataOkDateStart);
        return this;
    }

    public Date getDataOkDateStart() {
        this.nodifyGetFiled("dataOkDateStart");
        return dataOkDateStart;
    }

    public OmsOrderPreEntity setDataOkDateEnd(Date dataOkDateEnd) {
        this.dataOkDateEnd = dataOkDateEnd;
        this.nodifySetFiled("dataOkDateEnd", dataOkDateEnd);
        return this;
    }

    public Date getDataOkDateEnd() {
        this.nodifyGetFiled("dataOkDateEnd");
        return dataOkDateEnd;
    }

    public OmsOrderPreEntity setArapOkDate(Date arapOkDate) {
        this.arapOkDate = arapOkDate;
        this.nodifySetFiled("arapOkDate", arapOkDate);
        return this;
    }

    public Date getArapOkDate() {
        this.nodifyGetFiled("arapOkDate");
        return arapOkDate;
    }

    public OmsOrderPreEntity setArapOkDateStart(Date arapOkDateStart) {
        this.arapOkDateStart = arapOkDateStart;
        this.nodifySetFiled("arapOkDateStart", arapOkDateStart);
        return this;
    }

    public Date getArapOkDateStart() {
        this.nodifyGetFiled("arapOkDateStart");
        return arapOkDateStart;
    }

    public OmsOrderPreEntity setArapOkDateEnd(Date arapOkDateEnd) {
        this.arapOkDateEnd = arapOkDateEnd;
        this.nodifySetFiled("arapOkDateEnd", arapOkDateEnd);
        return this;
    }

    public Date getArapOkDateEnd() {
        this.nodifyGetFiled("arapOkDateEnd");
        return arapOkDateEnd;
    }

    public OmsOrderPreEntity setOpCompleteOk(String opCompleteOk) {
        this.opCompleteOk = opCompleteOk;
        this.nodifySetFiled("opCompleteOk", opCompleteOk);
        return this;
    }

    public String getOpCompleteOk() {
        this.nodifyGetFiled("opCompleteOk");
        return opCompleteOk;
    }

    public OmsOrderPreEntity setLanguage(String language) {
        this.language = language;
        this.nodifySetFiled("language", language);
        return this;
    }

    public String getLanguage() {
        this.nodifyGetFiled("language");
        return language;
    }

    public OmsOrderPreEntity setXdUserName(String xdUserName) {
        this.xdUserName = xdUserName;
        this.nodifySetFiled("xdUserName", xdUserName);
        return this;
    }

    public String getXdUserName() {
        this.nodifyGetFiled("xdUserName");
        return xdUserName;
    }

    public OmsOrderPreEntity setXdNodeCode(String xdNodeCode) {
        this.xdNodeCode = xdNodeCode;
        this.nodifySetFiled("xdNodeCode", xdNodeCode);
        return this;
    }

    public String getXdNodeCode() {
        this.nodifyGetFiled("xdNodeCode");
        return xdNodeCode;
    }

    public OmsOrderPreEntity setXdNodeName(String xdNodeName) {
        this.xdNodeName = xdNodeName;
        this.nodifySetFiled("xdNodeName", xdNodeName);
        return this;
    }

    public String getXdNodeName() {
        this.nodifyGetFiled("xdNodeName");
        return xdNodeName;
    }

    public OmsOrderPreEntity setXdCompanyCode(String xdCompanyCode) {
        this.xdCompanyCode = xdCompanyCode;
        this.nodifySetFiled("xdCompanyCode", xdCompanyCode);
        return this;
    }

    public String getXdCompanyCode() {
        this.nodifyGetFiled("xdCompanyCode");
        return xdCompanyCode;
    }

    public OmsOrderPreEntity setXdCompanyName(String xdCompanyName) {
        this.xdCompanyName = xdCompanyName;
        this.nodifySetFiled("xdCompanyName", xdCompanyName);
        return this;
    }

    public String getXdCompanyName() {
        this.nodifyGetFiled("xdCompanyName");
        return xdCompanyName;
    }

    public OmsOrderPreEntity setXdGroupCode(String xdGroupCode) {
        this.xdGroupCode = xdGroupCode;
        this.nodifySetFiled("xdGroupCode", xdGroupCode);
        return this;
    }

    public String getXdGroupCode() {
        this.nodifyGetFiled("xdGroupCode");
        return xdGroupCode;
    }

    public OmsOrderPreEntity setXdGroupName(String xdGroupName) {
        this.xdGroupName = xdGroupName;
        this.nodifySetFiled("xdGroupName", xdGroupName);
        return this;
    }

    public String getXdGroupName() {
        this.nodifyGetFiled("xdGroupName");
        return xdGroupName;
    }

    public OmsOrderPreEntity setJdUserName(String jdUserName) {
        this.jdUserName = jdUserName;
        this.nodifySetFiled("jdUserName", jdUserName);
        return this;
    }

    public String getJdUserName() {
        this.nodifyGetFiled("jdUserName");
        return jdUserName;
    }

    public OmsOrderPreEntity setBizRegId(String bizRegId) {
        this.bizRegId = bizRegId;
        this.nodifySetFiled("bizRegId", bizRegId);
        return this;
    }

    public String getBizRegId() {
        this.nodifyGetFiled("bizRegId");
        return bizRegId;
    }

    public OmsOrderPreEntity setIsQrjd(String isQrjd) {
        this.isQrjd = isQrjd;
        this.nodifySetFiled("isQrjd", isQrjd);
        return this;
    }

    public String getIsQrjd() {
        this.nodifyGetFiled("isQrjd");
        return isQrjd;
    }

    public OmsOrderPreEntity setIe(String ie) {
        this.ie = ie;
        this.nodifySetFiled("ie", ie);
        return this;
    }

    public String getIe() {
        this.nodifyGetFiled("ie");
        return ie;
    }

    public OmsOrderPreEntity setYgMileage(BigDecimal ygMileage) {
        this.ygMileage = ygMileage;
        this.nodifySetFiled("ygMileage", ygMileage);
        return this;
    }

    public BigDecimal getYgMileage() {
        this.nodifyGetFiled("ygMileage");
        return ygMileage;
    }

    public OmsOrderPreEntity setIsBg(String isBg) {
        this.isBg = isBg;
        this.nodifySetFiled("isBg", isBg);
        return this;
    }

    public String getIsBg() {
        this.nodifyGetFiled("isBg");
        return isBg;
    }

    public OmsOrderPreEntity setEmsNo(String emsNo) {
        this.emsNo = emsNo;
        this.nodifySetFiled("emsNo", emsNo);
        return this;
    }

    public String getEmsNo() {
        this.nodifyGetFiled("emsNo");
        return emsNo;
    }

    public OmsOrderPreEntity setYgMileageT(BigDecimal ygMileageT) {
        this.ygMileageT = ygMileageT;
        this.nodifySetFiled("ygMileageT", ygMileageT);
        return this;
    }

    public BigDecimal getYgMileageT() {
        this.nodifyGetFiled("ygMileageT");
        return ygMileageT;
    }

    public OmsOrderPreEntity setYgMileageS(BigDecimal ygMileageS) {
        this.ygMileageS = ygMileageS;
        this.nodifySetFiled("ygMileageS", ygMileageS);
        return this;
    }

    public BigDecimal getYgMileageS() {
        this.nodifyGetFiled("ygMileageS");
        return ygMileageS;
    }

    public OmsOrderPreEntity setExpressNo(String expressNo) {
        this.expressNo = expressNo;
        this.nodifySetFiled("expressNo", expressNo);
        return this;
    }

    public String getExpressNo() {
        this.nodifyGetFiled("expressNo");
        return expressNo;
    }

    public OmsOrderPreEntity setCarNo(String carNo) {
        this.carNo = carNo;
        this.nodifySetFiled("carNo", carNo);
        return this;
    }

    public String getCarNo() {
        this.nodifyGetFiled("carNo");
        return carNo;
    }

    public OmsOrderPreEntity setDriverPhone(String driverPhone) {
        this.driverPhone = driverPhone;
        this.nodifySetFiled("driverPhone", driverPhone);
        return this;
    }

    public String getDriverPhone() {
        this.nodifyGetFiled("driverPhone");
        return driverPhone;
    }

    public OmsOrderPreEntity setTotalPieces(BigDecimal totalPieces) {
        this.totalPieces = totalPieces;
        this.nodifySetFiled("totalPieces", totalPieces);
        return this;
    }

    public BigDecimal getTotalPieces() {
        this.nodifyGetFiled("totalPieces");
        return totalPieces;
    }

    public OmsOrderPreEntity setPieceUnit(String pieceUnit) {
        this.pieceUnit = pieceUnit;
        this.nodifySetFiled("pieceUnit", pieceUnit);
        return this;
    }

    public String getPieceUnit() {
        this.nodifyGetFiled("pieceUnit");
        return pieceUnit;
    }

    public OmsOrderPreEntity setZsDate(Date zsDate) {
        this.zsDate = zsDate;
        this.nodifySetFiled("zsDate", zsDate);
        return this;
    }

    public Date getZsDate() {
        this.nodifyGetFiled("zsDate");
        return zsDate;
    }

    public OmsOrderPreEntity setZsDateStart(Date zsDateStart) {
        this.zsDateStart = zsDateStart;
        this.nodifySetFiled("zsDateStart", zsDateStart);
        return this;
    }

    public Date getZsDateStart() {
        this.nodifyGetFiled("zsDateStart");
        return zsDateStart;
    }

    public OmsOrderPreEntity setZsDateEnd(Date zsDateEnd) {
        this.zsDateEnd = zsDateEnd;
        this.nodifySetFiled("zsDateEnd", zsDateEnd);
        return this;
    }

    public Date getZsDateEnd() {
        this.nodifyGetFiled("zsDateEnd");
        return zsDateEnd;
    }

    public OmsOrderPreEntity setIsDetail(String isDetail) {
        this.isDetail = isDetail;
        this.nodifySetFiled("isDetail", isDetail);
        return this;
    }

    public String getIsDetail() {
        this.nodifyGetFiled("isDetail");
        return isDetail;
    }

    /**
     * 服务项目 用于接收前端传过来的参数，不用存数据库
     */
    @ApiModelProperty("服务项目")
    @TableField(exist = false)
    private String fwlxItem;

    public OmsOrderPreEntity setFwlxItem(String fwlxItem) {
        this.fwlxItem = fwlxItem;
        this.nodifySetFiled("fwlxItem", fwlxItem);
        return this;
    }

    public String getFwlxItem() {
        this.nodifyGetFiled("fwlxItem");
        return fwlxItem;
    }
}
