package com.eci.project.fzgjBdCountry.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;

import java.util.Date;


/**
* 国家对象 FZGJ_BD_COUNTRY
*
* @<NAME_EMAIL>
* @date 2025-03-14
*/
@FieldNameConstants
public class FzgjBdCountryBaseEntity extends EciBaseEntity
{
	/**
	* GUID
	*/
	@ApiModelProperty("GUID(50)")
	@TableId("GUID")
	private String guid;

	/**
	* 国家代码
	*/
	@ApiModelProperty("国家代码(50)")
	@TableField("CODE")
	private String code;

	/**
	* 英文名称
	*/
	@ApiModelProperty("英文名称(50)")
	@TableField("EN_NAME")
	private String enName;

	/**
	* 中文名称
	*/
	@ApiModelProperty("中文名称(50)")
	@TableField("CH_NAME")
	private String chName;

	/**
	* 备注
	*/
	@ApiModelProperty("备注(50)")
	@TableField("MEMO")
	private String memo;

	/**
	* 状态
	*/
	@ApiModelProperty("状态(1)")
	@TableField("STATUS")
	private String status;

	/**
	* 创建人
	*/
	@ApiModelProperty("创建人(50)")
	@TableField("CREATE_USER")
	private String createUser;

	/**
	* 创建日期
	*/
	@ApiModelProperty("创建日期(7)")
	@TableField("CREATE_DATE")
	private Date createDate;

	@ApiModelProperty("创建日期开始")
	@TableField(exist=false)
	private Date createDateStart;

	@ApiModelProperty("创建日期结束")
	@TableField(exist=false)
	private Date createDateEnd;

	/**
	* NUM_CODE
	*/
	@ApiModelProperty("NUM_CODE(10)")
	@TableField("NUM_CODE")
	private String numCode;

	/**
	* 海关代码
	*/
	@ApiModelProperty("海关代码(10)")
	@TableField("THREE_CODE")
	private String threeCode;

	/**
	* 修改日期
	*/
	@ApiModelProperty("修改日期(7)")
	@TableField("UPDATE_DATE")
	private Date updateDate;

	@ApiModelProperty("修改日期开始")
	@TableField(exist=false)
	private Date updateDateStart;

	@ApiModelProperty("修改日期结束")
	@TableField(exist=false)
	private Date updateDateEnd;

	/**
	* UPDATE_USER
	*/
	@ApiModelProperty("UPDATE_USER(200)")
	@TableField("UPDATE_USER")
	private String updateUser;

	/**
	* CREATE_USER
	*/
	@ApiModelProperty("CREATE_USER(200)")
	@TableField("CREATE_USER_NAME")
	private String createUserName;

	/**
	* UPDATE_USER
	*/
	@ApiModelProperty("UPDATE_USER(200)")
	@TableField("UPDATE_USER_NAME")
	private String updateUserName;

	@JsonIgnore
	@TableField(exist = false)
	private Class<?> clazz = this.getClass();

	public Class<?> getClazz() {
		return clazz;
	}

	public void setClazz(Class<?> clazz) {
		this.clazz = clazz;
	}

	public FzgjBdCountryBaseEntity setGuid(String guid) {
		this.guid = guid;
		return this;
	}

	public String getGuid() {
		return guid;
	}

	public FzgjBdCountryBaseEntity setCode(String code) {
		this.code = code;
		return this;
	}

	public String getCode() {
		return code;
	}

	public FzgjBdCountryBaseEntity setEnName(String enName) {
		this.enName = enName;
		return this;
	}

	public String getEnName() {
		return enName;
	}

	public FzgjBdCountryBaseEntity setChName(String chName) {
		this.chName = chName;
		return this;
	}

	public String getChName() {
		return chName;
	}

	public FzgjBdCountryBaseEntity setMemo(String memo) {
		this.memo = memo;
		return this;
	}

	public String getMemo() {
		return memo;
	}

	public FzgjBdCountryBaseEntity setStatus(String status) {
		this.status = status;
		return this;
	}

	public String getStatus() {
		return status;
	}

	public FzgjBdCountryBaseEntity setCreateUser(String createUser) {
		this.createUser = createUser;
		return this;
	}

	public String getCreateUser() {
		return createUser;
	}

	public FzgjBdCountryBaseEntity setCreateDate(Date createDate) {
		this.createDate = createDate;
		return this;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public FzgjBdCountryBaseEntity setCreateDateStart(Date createDateStart) {
		this.createDateStart = createDateStart;
		return this;
	}

	public Date getCreateDateStart() {
		return createDateStart;
	}

	public FzgjBdCountryBaseEntity setCreateDateEnd(Date createDateEnd) {
		this.createDateEnd = createDateEnd;
		return this;
	}

	public Date getCreateDateEnd() {
		return createDateEnd;
	}
	public FzgjBdCountryBaseEntity setNumCode(String numCode) {
		this.numCode = numCode;
		return this;
	}

	public String getNumCode() {
		return numCode;
	}

	public FzgjBdCountryBaseEntity setThreeCode(String threeCode) {
		this.threeCode = threeCode;
		return this;
	}

	public String getThreeCode() {
		return threeCode;
	}

	public FzgjBdCountryBaseEntity setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
		return this;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public FzgjBdCountryBaseEntity setUpdateDateStart(Date updateDateStart) {
		this.updateDateStart = updateDateStart;
		return this;
	}

	public Date getUpdateDateStart() {
		return updateDateStart;
	}

	public FzgjBdCountryBaseEntity setUpdateDateEnd(Date updateDateEnd) {
		this.updateDateEnd = updateDateEnd;
		return this;
	}

	public Date getUpdateDateEnd() {
		return updateDateEnd;
	}
	public FzgjBdCountryBaseEntity setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
		return this;
	}

	public String getUpdateUser() {
		return updateUser;
	}

	public FzgjBdCountryBaseEntity setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
		return this;
	}

	public String getCreateUserName() {
		return createUserName;
	}

	public FzgjBdCountryBaseEntity setUpdateUserName(String updateUserName) {
		this.updateUserName = updateUserName;
		return this;
	}

	public String getUpdateUserName() {
		return updateUserName;
	}

}
