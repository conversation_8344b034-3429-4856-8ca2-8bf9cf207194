<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.fzgjCrmEnterpriseService.dao.FzgjCrmEnterpriseServiceDao">
    <resultMap type="FzgjCrmEnterpriseServiceEntity" id="FzgjCrmEnterpriseServiceResult">
        <result property="guid" column="GUID"/>
        <result property="crmCode" column="CRM_CODE"/>
        <result property="serviceType" column="SERVICE_TYPE"/>
        <result property="serviceName" column="SERVICE_NAME"/>
        <result property="serviceValue" column="SERVICE_VALUE"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
    </resultMap>

    <sql id="selectFzgjCrmEnterpriseServiceEntityVo">
        select
            GUID,
            CRM_CODE,
            SERVICE_TYPE,
            SERVICE_NAME,
            SERVICE_VALUE,
            CREATE_USER,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_DATE,
            CREATE_USER_NAME,
            UPDATE_USER_NAME
        from FZGJ_CRM_ENTERPRISE_SERVICE
    </sql>
</mapper>