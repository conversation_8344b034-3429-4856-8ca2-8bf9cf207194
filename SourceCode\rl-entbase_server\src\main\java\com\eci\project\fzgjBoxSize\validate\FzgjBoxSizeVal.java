package com.eci.project.fzgjBoxSize.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjBoxSize.entity.FzgjBoxSizeEntity;

import org.springframework.stereotype.Service;


/**
* 集装箱尺寸Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-17
*/
@Service
public class FzgjBoxSizeVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjBoxSizeEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjBoxSizeEntity entity, BusinessType businessType) {

    }

}
