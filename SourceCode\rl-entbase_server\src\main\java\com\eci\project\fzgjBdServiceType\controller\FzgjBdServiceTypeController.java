package com.eci.project.fzgjBdServiceType.controller;

import com.eci.common.ResponseMsgUtilX;
import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjBdServiceType.service.FzgjBdServiceTypeService;
import com.eci.project.fzgjBdServiceType.entity.FzgjBdServiceTypeEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 服务类型Controller
*
* @<NAME_EMAIL>
* @date 2025-03-13
*/
@Api(tags = "服务类型")
@RestController
@RequestMapping("/fzgjBdServiceType")
public class FzgjBdServiceTypeController extends EciBaseController {

    @Autowired
    private FzgjBdServiceTypeService fzgjBdServiceTypeService;


    @ApiOperation("服务类型:保存")
    @EciLog(title = "服务类型:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody FzgjBdServiceTypeEntity entity){
        FzgjBdServiceTypeEntity fzgjBdServiceTypeEntity =fzgjBdServiceTypeService.save(entity);
        return ResponseMsgUtil.success(10001,fzgjBdServiceTypeEntity);
    }


    @ApiOperation("服务类型:查询列表")
    @EciLog(title = "服务类型:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjBdServiceTypeEntity entity){
        List<FzgjBdServiceTypeEntity> fzgjBdServiceTypeEntities = fzgjBdServiceTypeService.selectList(entity);
        return ResponseMsgUtilX.success(10001,fzgjBdServiceTypeEntities);
    }


    @ApiOperation("服务类型:分页查询列表")
    @EciLog(title = "服务类型:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjBdServiceTypeEntity entity){
        TgPageInfo tgPageInfo = fzgjBdServiceTypeService.queryPageList(entity);
        return ResponseMsgUtilX.success(10001,tgPageInfo);
    }


    @ApiOperation("服务类型:根据ID查一条")
    @EciLog(title = "服务类型:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjBdServiceTypeEntity entity){
        FzgjBdServiceTypeEntity  fzgjBdServiceTypeEntity = fzgjBdServiceTypeService.selectOneById(entity.getGuid());
        return ResponseMsgUtilX.success(10001,fzgjBdServiceTypeEntity);
    }


    @ApiOperation("服务类型:根据ID删除一条")
    @EciLog(title = "服务类型:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjBdServiceTypeEntity entity){
        int count = fzgjBdServiceTypeService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("服务类型:根据ID字符串删除多条")
    @EciLog(title = "服务类型:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjBdServiceTypeEntity entity) {
        int count = fzgjBdServiceTypeService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}