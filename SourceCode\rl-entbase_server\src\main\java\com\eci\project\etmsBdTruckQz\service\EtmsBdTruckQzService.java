package com.eci.project.etmsBdTruckQz.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.db.DBHelper;
import com.eci.common.util.DateUtils;
import com.eci.common.util.StringUtils;
import com.eci.common.utils.ExportUtils.ExcelProcess;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.excel.config.ExportConfig;
import com.eci.exception.BaseException;
import com.eci.log.enums.ActionType;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.etmsBdDriver.dao.EtmsBdDriverDao;
import com.eci.project.etmsBdDriver.entity.DriverInfo;
import com.eci.project.etmsBdDriver.entity.EtmsBdDriverEntity;
import com.eci.project.etmsBdTruck.dao.EtmsBdTruckDao;
import com.eci.project.etmsBdTruck.entity.EtmsBdTruckEntity;
import com.eci.project.etmsBdTruckGcQz.dao.EtmsBdTruckGcQzDao;
import com.eci.project.etmsBdTruckGcQz.entity.EtmsBdTruckGcQzEntity;
import com.eci.project.etmsBdTruckGpsNow.dao.EtmsBdTruckGpsNowDao;
import com.eci.project.etmsBdTruckGpsNow.entity.EtmsBdTruckGpsNowEntity;
import com.eci.project.etmsBdTruckQz.dao.EtmsBdTruckQzDTODao;
import com.eci.project.etmsBdTruckQz.dao.EtmsBdTruckQzDao;
import com.eci.project.etmsBdTruckQz.dao.EtmsBdTruckQzExtendDao;
import com.eci.project.etmsBdTruckQz.entity.EtmsBdTruckQzDTOEntity;
import com.eci.project.etmsBdTruckQz.entity.EtmsBdTruckQzEntity;
import com.eci.project.etmsBdTruckQz.entity.EtmsBdTruckQzExtendEntity;
import com.eci.project.etmsBdTruckQz.validate.EtmsBdTruckQzVal;

import com.eci.project.etmsOpFileQz.dao.EtmsOpFileQzDao;
import com.eci.project.etmsOpFileQz.entity.EtmsOpFileQzEntity;
import com.eci.project.fzgjBdArea.entity.FzgjBdAreaEntity;
import com.eci.sso.role.entity.UserContext;
import jdk.nashorn.internal.parser.DateParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.eci.common.util.ResponseMsgUtil.listCodeToName;


/**
* 车辆信息Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-09
*/
@Service
@Slf4j
public class EtmsBdTruckQzService implements EciBaseService<EtmsBdTruckQzEntity> {

    @Autowired
    private EtmsBdTruckQzDao etmsBdTruckQzDao;
    @Autowired
    private EtmsBdTruckDao etmsBdTruckDao;
    @Autowired
    private EtmsBdTruckQzExtendDao etmsBdTruckQzExtendDao;
    @Autowired
    private EtmsBdTruckGpsNowDao etmsBdTruckGpsNowDao;
    @Autowired
    private EtmsBdDriverDao etmsBdDriverDao;
    @Autowired
    private EtmsOpFileQzDao etmsOpFileQzDao;
    @Autowired
    private EtmsBdTruckGcQzDao etmsBdTruckGcQzDao;
    @Autowired
    private EtmsBdTruckQzDTODao etmsBdTruckQzDTODao;

    @Autowired
    private EtmsBdTruckQzVal etmsBdTruckQzVal;

    public TgPageInfo queryPageList(EtmsBdTruckQzExtendEntity entity) {
        if (BllContext.getBusinessType() == BusinessType.EXPORT) {
            if (BllContext.getRequestEntity().getActionType() == ActionType.ASYNC) {
                etmsBdTruckQzDTODao.asyncExportDefaultExcel(()->{
                    List<EtmsBdTruckQzExtendEntity> list=etmsBdTruckQzExtendDao.selectListInfo(entity);
                    List convertList= listCodeToName(list);
                    List<EtmsBdTruckQzDTOEntity> dtoList = convertListToDTO(convertList);
                    return dtoList;
                }, ExcelProcess.BuildConfig("车辆管理（公司级）", EtmsBdTruckQzDTOEntity.class));
            } else {
                etmsBdTruckQzDTODao.exportDefaultExcel(() -> {
                    List<EtmsBdTruckQzExtendEntity> list=etmsBdTruckQzExtendDao.selectListInfo(entity);
                    List convertList= listCodeToName(list);
                    List<EtmsBdTruckQzDTOEntity> dtoList = convertListToDTO(convertList);
                    return dtoList;
                }, ExcelProcess.BuildConfig("车辆管理（公司级）", EtmsBdTruckQzDTOEntity.class));
            }
            return new TgPageInfo<>();
        }else {
            startPage();
            List<EtmsBdTruckQzExtendEntity> entities = etmsBdTruckQzExtendDao.selectListInfo(entity);
            return EciQuery.getPageInfo(entities);
        }
    }

    private EtmsBdTruckQzDTOEntity convertToDTO(Map<String,String> entity) {
        EtmsBdTruckQzDTOEntity dto = new EtmsBdTruckQzDTOEntity();
        // 假设属性名称相同，直接赋值
        dto.setTruckNo(entity.get("truckNo"));
        dto.setGpsNo(entity.get("gpsNo"));
        dto.setDriverAtt(entity.get("driverAtt"));
        dto.setDriverName(entity.get("driverName"));
        dto.setNewCllx(entity.get("newCllx"));
        dto.setNewClcc(entity.get("newClcc"));
        dto.setIsGkName(entity.get("isGkName"));
        dto.setGpsMode(entity.get("gpsMode"));
        dto.setPartnerGuid(entity.get("partnerGuid"));
        dto.setCheckStatusName(entity.get("checkStatusName"));
        dto.setCheckRmk(entity.get("checkRmk"));
        dto.setLlOil(new BigDecimal(entity.get("llOil")));
        dto.setIsUserName(entity.get("isUserName"));
        dto.setCreateDate(DateUtils.parseDate(entity.get("createDate")));
        dto.setCreateUserName(entity.get("createUserName"));
        dto.setCreateCompanyName(entity.get("createCompanyName"));
        dto.setUpdateDate(DateUtils.parseDate(entity.get("updateDate")));
        dto.setUpdateUserName(entity.get("updateUserName"));
        dto.setOrgDepName(entity.get("orgDepName"));
        // 其他属性赋值
        return dto;
    }

    // 转换列表
    public List<EtmsBdTruckQzDTOEntity> convertListToDTO(List<Map<String,String>> entityList) {
        return entityList.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public EtmsBdTruckQzExtendEntity save(EtmsBdTruckQzEntity entity) {
        // 返回实体对象
        EtmsBdTruckQzExtendEntity etmsBdTruckQzExtendEntity = null;
        etmsBdTruckQzVal.saveValidate(entity,BllContext.getBusinessType());
        if(entity.getCarLong()!=null){
            if(entity.getCarLongType().equals("1") && entity.getCarLong().compareTo(BigDecimal.valueOf(9.6))>0){
                entity.setIsBt("Y");
            }else{
                entity.setIsBt("N");
            }
        }else{
            entity.setIsBt("N");
        }
        // 获取规格|车主|驾驶人GUID
        String DriverCode = "", DriverPhone = "";
        if(StringUtils.hasValue(entity.getDriverGuid())){
            EtmsBdDriverEntity driverEntity =etmsBdDriverDao.selectById(entity.getDriverGuid());
            if(driverEntity!=null){
                DriverCode = driverEntity.getUserId();
                DriverPhone = driverEntity.getPhone();
            }
        }
        QueryWrapper<EtmsBdTruckEntity> queryWrapper = new QueryWrapper<>();
        if(StringUtils.hasValue(entity.getGpsNo())){
            //校验设备编号是否存在
            queryWrapper.eq("CREATE_COMPANY",UserContext.getUserInfo().getCompanyCode());
            queryWrapper.eq("GPS_NO",entity.getGpsNo());
            queryWrapper.ne("GUID",entity.getGuid());
            if(etmsBdTruckDao.selectList(queryWrapper).size()>0){
                throw new RuntimeException("当前维护的设备编号已存在当前登陆企业中,请检查!");
            }
        }
        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            if(StringUtils.hasValue(entity.getTruckNo())){
                queryWrapper.clear();
                queryWrapper.eq("TRUCK_NO",entity.getTruckNo());
                //GPS_MODE IS NOT NULL and GPS_NO IS NOT NULL
                queryWrapper.isNotNull("GPS_MODE");
                queryWrapper.isNotNull("GPS_NO");
                if(etmsBdTruckDao.selectList(queryWrapper).size()>0){
                    throw new RuntimeException("车牌号["+entity.getTruckNo()+"]在协同平台已存在,请重新维护");
                }
                queryWrapper.clear();
                //校验设备编号是否存在
                queryWrapper.eq("CREATE_COMPANY",UserContext.getUserInfo().getCompanyCode());
                queryWrapper.eq("TRUCK_NO",entity.getTruckNo());
                if(etmsBdTruckDao.selectList(queryWrapper).size()>0){
                    throw new RuntimeException("当前维护的车辆信息代码已存在当前登陆企业中,请检查!");
                }
            }
            entity.setGuid(IdWorker.get32UUID());
            entity.setCreateDate(DateUtils.getNowDate());
            entity.setCreateUser(UserContext.getUserInfo().getUserId());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            entity.setCompanyName(UserContext.getUserInfo().getCompanyName());
            entity.setNodeCode(UserContext.getUserInfo().getDeptCode());
            entity.setNodeName(UserContext.getUserInfo().getDeptName());
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
            entity.setOrgDepId(UserContext.getUserInfo().getDeptId());
            entity.setOrgDepCode(UserContext.getUserInfo().getDeptCode());
            entity.setOrgDepName(UserContext.getUserInfo().getDeptName());
            entity.setCreateCompany(UserContext.getUserInfo().getCompanyCode());
            //entity.setSsoCompanyGuid(UserContext.getUserInfo().getCompanyCiqCode())
            entity.setCheckStatus("ZC");
            if(etmsBdTruckQzDao.insertOne(entity)!=null){
                etmsBdTruckQzExtendEntity=etmsBdTruckQzExtendDao.selectByOneId(entity.getGuid());
            }
        }else{
            entity.setUpdateDate(DateUtils.getNowDate());
            entity.setUpdateUser(UserContext.getUserInfo().getUserId());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            entity.setOrgDepId(UserContext.getUserInfo().getDeptId());
            entity.setOrgDepCode(UserContext.getUserInfo().getDeptCode());
            entity.setOrgDepName(UserContext.getUserInfo().getDeptName());
            EciQuery<EtmsBdTruckQzEntity> updateWrapper =new EciQuery<>();
            updateWrapper.eq("GUID",entity.getGuid());
            int count = etmsBdTruckQzDao.update(entity,updateWrapper);
            if(count>0){
                etmsBdTruckQzExtendEntity=etmsBdTruckQzExtendDao.selectByOneId(entity.getGuid());
            }
        }
        TongbuBdTruckGpsNow(entity.getTruckNo(), entity.getGpsNo(),entity.getGpsMode(),DriverCode,DriverPhone,entity.getStatus(),"",UserContext.getUserInfo().getCompanyCode());
        return etmsBdTruckQzExtendEntity;
    }

    public void TongbuBdTruckGpsNow(String truckNo, String gpsNo,String gpsMode, String driverName, String driverPhone, String status, String orderCarGuid, String create_Company){
        QueryWrapper<EtmsBdTruckGpsNowEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("TRUCK_NO",truckNo);
        queryWrapper.eq("CREATE_COMPANY",create_Company);
        List<EtmsBdTruckGpsNowEntity> list=etmsBdTruckGpsNowDao.selectList(queryWrapper);
        if(list.size()>0){
            EtmsBdTruckGpsNowEntity etmsBdTruckGpsNowEntity=list.get(0);
            etmsBdTruckGpsNowEntity.setGpsMode(gpsMode);
            etmsBdTruckGpsNowEntity.setGpsNo(gpsNo);
            etmsBdTruckGpsNowEntity.setDriverName(driverName);
            etmsBdTruckGpsNowEntity.setDriverPhone(driverPhone);
            etmsBdTruckGpsNowEntity.setStatus(status);
            if (!orderCarGuid.isEmpty()){
                etmsBdTruckGpsNowEntity.setOrderCarGuid(orderCarGuid);
            }
            if("0".equals(status)){
                etmsBdTruckGpsNowEntity.setOrderCarGuid("");
            }
            etmsBdTruckGpsNowDao.updateByEntityId(etmsBdTruckGpsNowEntity);
        }else{
            EtmsBdTruckGpsNowEntity etmsBdTruckGpsNowEntity=new EtmsBdTruckGpsNowEntity();
            etmsBdTruckGpsNowEntity.setGuid(IdWorker.get32UUID());
            etmsBdTruckGpsNowEntity.setTruckNo(truckNo);
            etmsBdTruckGpsNowEntity.setGpsMode(gpsMode);
            etmsBdTruckGpsNowEntity.setGpsNo(gpsNo);
            etmsBdTruckGpsNowEntity.setDriverName(driverName);
            etmsBdTruckGpsNowEntity.setDriverPhone(driverPhone);
            etmsBdTruckGpsNowEntity.setStatus("0");
            etmsBdTruckGpsNowEntity.setOrderCarGuid(orderCarGuid);
            etmsBdTruckGpsNowEntity.setCreateCompany(create_Company);
            etmsBdTruckGpsNowEntity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            etmsBdTruckGpsNowDao.insertOne(etmsBdTruckGpsNowEntity);
        }
    }

    @Override
    public List<EtmsBdTruckQzEntity> selectList(EtmsBdTruckQzEntity entity) {
        return etmsBdTruckQzDao.selectList(entity);
    }

    public EtmsBdTruckQzExtendEntity selectByOneId(Serializable id) {
        EtmsBdTruckQzExtendEntity  etmsBdTruckQzEntity = etmsBdTruckQzExtendDao.selectByOneId(id.toString());
        return etmsBdTruckQzEntity;
    }


    @Override
    public void insertBatch(List<EtmsBdTruckQzEntity> list) {
        etmsBdTruckQzDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return etmsBdTruckQzDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return etmsBdTruckQzDao.deleteById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean batchSubmitByIds(String ids) {
        List<String> list= Arrays.asList(ids.split(","));
        boolean isSuccess=false;
        for(String str:list){
            QueryWrapper<EtmsOpFileQzEntity> queryWrapper_file=new QueryWrapper<>();
            queryWrapper_file.eq("OP_NO",str);
            queryWrapper_file.and(wrapper -> wrapper.eq("OP_TYPE","XSZ").or().eq("OP_TYPE","YYZ"));
            if(etmsOpFileQzDao.selectList(queryWrapper_file).size()==0){
                throw new BaseException("送审失败，附件中必须有行驶证或营运证");
            }
            queryWrapper_file.clear();
            queryWrapper_file.eq("OP_NO",str);
            queryWrapper_file.eq("OP_TYPE","GCXSZ");
            List<EtmsOpFileQzEntity> listfile=etmsOpFileQzDao.selectList(queryWrapper_file);
            QueryWrapper<EtmsBdTruckGcQzEntity> queryWrapper_gc=new QueryWrapper<>();
            queryWrapper_gc.eq("TRUCK_GUID",str);
            List<EtmsBdTruckGcQzEntity> listgc=etmsBdTruckGcQzDao.selectList(queryWrapper_gc);
            if(listgc.size()>0 && listgc.size()!=listfile.size()){
                throw new BaseException("送审失败，附件中必须有车挂行驶证");
            }
        }
        String sql="select GUID from ETMS_BD_TRUCK_QZ";
        sql += " WHERE GUID IN ('" + String.join("','", ids.split(",")) + "')";
        sql+=" AND CHECK_STATUS IN ('SS','SX')";
        if(DBHelper.selectList(sql, EtmsBdTruckQzEntity.class).size()>0){
            throw new BaseException("送审的车辆中存在已送审或已生效的数据");
        }
        sql="update ETMS_BD_TRUCK_QZ set CHECK_STATUS='SX' WHERE GUID IN ('" + String.join("','", ids.split(",")) + "') ";
        try {
            DBHelper.execute(sql);
            isSuccess=true;
        } catch (Exception e) {
            throw new BaseException(e.getMessage());
        }
        return isSuccess;

    }
}