package com.eci.project.fzgjBaseDataDetail.service.impl;

import com.eci.common.db.DBHelper;
import com.eci.common.utils.CommonLib;
import com.eci.common.web.BllContext;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjBaseDataDetail.dao.FzgjBaseDataDetailDao;
import com.eci.project.fzgjBaseDataDetail.entity.FzgjBaseDataDetailEntity;
import com.eci.project.fzgjBaseDataDetail.service.IFzgjBaseDataDetailService;
import com.eci.project.fzgjBaseDataDetail.validate.FzgjBaseDataDetailVal;
import com.eci.wu.core.DataTable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.List;


/**
 * 扩展基础资料明细Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-03-18
 */
@Service
@Slf4j
public class FzgjBaseDataDetailServiceImpl implements IFzgjBaseDataDetailService {
    @Autowired
    private FzgjBaseDataDetailDao fzgjBaseDataDetailDao;

    @Autowired
    private FzgjBaseDataDetailVal fzgjBaseDataDetailVal;

    CommonLib cmn = CommonLib.getInstance();
    @Override
    public TgPageInfo queryPageList(FzgjBaseDataDetailEntity entity) {

        startPage();
        List<FzgjBaseDataDetailEntity> entities = fzgjBaseDataDetailDao.selectListInfo(entity);
        return EciQuery.getPageInfo(entities);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjBaseDataDetailEntity save(FzgjBaseDataDetailEntity entity) {

        // 返回实体对象
        FzgjBaseDataDetailEntity fzgjBaseDataDetailEntity = null;
        fzgjBaseDataDetailVal.saveValidate(entity, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjBaseDataDetailEntity = fzgjBaseDataDetailDao.insertOne(entity);

        } else {

            fzgjBaseDataDetailEntity = fzgjBaseDataDetailDao.updateByEntityId(entity);

        }
        return fzgjBaseDataDetailEntity;
    }

    @Override
    public List<FzgjBaseDataDetailEntity> selectList(FzgjBaseDataDetailEntity entity) {
        return fzgjBaseDataDetailDao.selectList(entity);
    }

    @Override
    public FzgjBaseDataDetailEntity selectOneById(Serializable id) {
        return fzgjBaseDataDetailDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjBaseDataDetailEntity> list) {
        fzgjBaseDataDetailDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjBaseDataDetailDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjBaseDataDetailDao.deleteById(id);
    }


    @Override
    public DataTable getInfo(String groupCode, String status) {
        String sql="select CODE,NAME from FZGJ_BASE_DATA_DETAIL WHERE GROUP_CODE=%s AND STATUS=%s ORDER BY SEQ";
        sql=String.format(sql,cmn.SQLQ(groupCode),cmn.SQLQ(status));
        return DBHelper.getDataTable(sql);
    }
}