<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.etmsOpFileQz.dao.EtmsOpFileQzDao">
    <resultMap type="EtmsOpFileQzEntity" id="EtmsOpFileQzResult">
        <result property="guid" column="GUID"/>
        <result property="opNo" column="OP_NO"/>
        <result property="opType" column="OP_TYPE"/>
        <result property="fileType" column="FILE_TYPE"/>
        <result property="fileNo" column="FILE_NO"/>
        <result property="fileName" column="FILE_NAME"/>
        <result property="fileUrl" column="FILE_URL"/>
        <result property="memo" column="MEMO"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="createCompany" column="CREATE_COMPANY"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="isDelete" column="IS_DELETE"/>
        <result property="deleteUser" column="DELETE_USER"/>
        <result property="deleteUserName" column="DELETE_USER_NAME"/>
        <result property="deleteDate" column="DELETE_DATE"/>
        <result property="stationType" column="STATION_TYPE"/>
        <result property="modMark" column="MOD_MARK"/>
    </resultMap>

    <sql id="selectEtmsOpFileQzEntityVo">
        select
            GUID,
            OP_NO,
            OP_TYPE,
            FILE_TYPE,
            FILE_NO,
            FILE_NAME,
            FILE_URL,
            MEMO,
            CREATE_USER,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_DATE,
            CREATE_COMPANY,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            CREATE_USER_NAME,
            UPDATE_USER_NAME,
            IS_DELETE,
            DELETE_USER,
            DELETE_USER_NAME,
            DELETE_DATE,
            STATION_TYPE,
            MOD_MARK
        from ETMS_OP_FILE_QZ
    </sql>
</mapper>