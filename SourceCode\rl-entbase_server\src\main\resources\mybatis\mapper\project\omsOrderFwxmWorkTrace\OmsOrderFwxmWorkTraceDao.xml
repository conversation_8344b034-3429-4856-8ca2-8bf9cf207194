<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.omsOrderFwxmWorkTrace.dao.OmsOrderFwxmWorkTraceDao">
    <resultMap type="OmsOrderFwxmWorkTraceEntity" id="OmsOrderFwxmWorkTraceResult">
        <result property="guid" column="GUID"/>
        <result property="workNo" column="WORK_NO"/>
        <result property="orderNo" column="ORDER_NO"/>
        <result property="preNo" column="PRE_NO"/>
        <result property="linkSeq" column="LINK_SEQ"/>
        <result property="linkCode" column="LINK_CODE"/>
        <result property="planOkDate" column="PLAN_OK_DATE"/>
        <result property="actualOkDate" column="ACTUAL_OK_DATE"/>
        <result property="jobd" column="JOBD"/>
        <result property="isException" column="IS_EXCEPTION"/>
        <result property="isDelay" column="IS_DELAY"/>
        <result property="sjlrf" column="SJLRF"/>
        <result property="sjlrCode" column="SJLR_CODE"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="workGuid" column="WORK_GUID"/>
        <result property="bizRegId" column="BIZ_REG_ID"/>
        <result property="isSendEmail" column="IS_SEND_EMAIL"/>
    </resultMap>

    <sql id="selectOmsOrderFwxmWorkTraceEntityVo">
        select
            GUID,
            WORK_NO,
            ORDER_NO,
            PRE_NO,
            LINK_SEQ,
            LINK_CODE,
            PLAN_OK_DATE,
            ACTUAL_OK_DATE,
            JOBD,
            IS_EXCEPTION,
            IS_DELAY,
            SJLRF,
            SJLR_CODE,
            CREATE_USER,
            CREATE_USER_NAME,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_USER_NAME,
            UPDATE_DATE,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            WORK_GUID,
            BIZ_REG_ID,
            IS_SEND_EMAIL
        from OMS_ORDER_FWXM_WORK_TRACE
    </sql>
</mapper>