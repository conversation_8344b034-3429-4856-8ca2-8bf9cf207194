package com.eci.project.fzgjBdProduct.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.math.BigDecimal;
import java.util.Date;


/**
* 业务产品对象 FZGJ_BD_PRODUCT
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-04-01
*/
@ApiModel("业务产品")
@TableName("FZGJ_BD_PRODUCT")
@FieldNameConstants
public class FzgjBdProductEntity extends EciBaseEntity{
    /**
    * GUID
    */
    @ApiModelProperty("GUID(50)")
    @TableId("GUID")
    private String guid;

    /**
    * 代码
    */
    @ApiModelProperty("代码(20)")
    @TableField("CODE")
    private String code;

    /**
    * 名称
    */
    @ApiModelProperty("名称(100)")
    @TableField("NAME")
    private String name;

    /**
    * 状态
    */
    @ApiModelProperty("状态(1)")
    @TableField("STATUS")
    private String status;

    /**
    * 顺序
    */
    @ApiModelProperty("顺序(22)")
    @TableField("SEQ")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal seq;

    /**
    * 备注
    */
    @ApiModelProperty("备注(500)")
    @TableField("MEMO")
    private String memo;

    /**
    * CREATE_USER
    */
    @ApiModelProperty("CREATE_USER(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * CREATE_DATE
    */
    @ApiModelProperty("CREATE_DATE(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("CREATE_DATE开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("CREATE_DATE结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * UPDATE_USER
    */
    @ApiModelProperty("UPDATE_USER(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * UPDATE_DATE
    */
    @ApiModelProperty("UPDATE_DATE(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("UPDATE_DATE开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("UPDATE_DATE结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 业务类型
    */
    @ApiModelProperty("业务类型(20)")
    @TableField("OP_TYPE")
    private String opType;

    /**
    * 公司代码
    */
    @ApiModelProperty("公司代码(36)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * CREATE_USER
    */
    @ApiModelProperty("CREATE_USER(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * UPDATE_USER
    */
    @ApiModelProperty("UPDATE_USER(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 公司名称
    */
    @ApiModelProperty("公司名称(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 集团代码
    */
    @ApiModelProperty("集团代码(36)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 集团名称
    */
    @ApiModelProperty("集团名称(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 英文名称
    */
    @ApiModelProperty("英文名称(200)")
    @TableField("EN_NAME")
    private String enName;

    /**
    * 单据类型
    */
    @ApiModelProperty("单据类型(100)")
    @TableField("BILL_CODE")
    private String billCode;

    /**
    * 标准产品
    */
    @ApiModelProperty("标准产品(1)")
    @TableField("IS_STANDARD")
    private String isStandard;

    /**
    * 客户
    */
    @ApiModelProperty("客户(50)")
    @TableField("CUSTOMER_CODE")
    private String customerCode;

    /**
    * 订单服务项目可编辑
    */
    @ApiModelProperty("订单服务项目可编辑(1)")
    @TableField("IS_FWXM_EDIT")
    private String isFwxmEdit;

    /**
    * 默认协作方案编号
    */
    @ApiModelProperty("默认协作方案编号(50)")
    @TableField("XZFA_NO")
    private String xzfaNo;

    /**
    * 产品图标名称
    */
    @ApiModelProperty("产品图标名称(200)")
    @TableField("PIC_NAME")
    private String picName;

    /**
    * 产品图标路径
    */
    @ApiModelProperty("产品图标路径(2,000)")
    @TableField("PIC_PATH")
    private String picPath;

    /**
    * 产品图标流
    */
    @ApiModelProperty("产品图标流(4,000)")
    @TableField("PIC_STREAM")
    private String picStream;

    /**
    * 客户自助可选(无需授权)
    */
    @ApiModelProperty("客户自助可选(无需授权)(1)")
    @TableField("IS_CSC")
    private String isCsc;

    /**
    * 扩展属性1
    */
    @ApiModelProperty("扩展属性1(100)")
    @TableField("UDF1")
    private String udf1;

    /**
    * 扩展属性2
    */
    @ApiModelProperty("扩展属性2(100)")
    @TableField("UDF2")
    private String udf2;

    /**
    * 扩展属性3
    */
    @ApiModelProperty("扩展属性3(100)")
    @TableField("UDF3")
    private String udf3;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public FzgjBdProductEntity() {
        this.setSubClazz(FzgjBdProductEntity.class);
    }

    public FzgjBdProductEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public FzgjBdProductEntity setCode(String code) {
        this.code = code;
        this.nodifySetFiled("code", code);
        return this;
    }

    public String getCode() {
        this.nodifyGetFiled("code");
        return code;
    }

    public FzgjBdProductEntity setName(String name) {
        this.name = name;
        this.nodifySetFiled("name", name);
        return this;
    }

    public String getName() {
        this.nodifyGetFiled("name");
        return name;
    }

    public FzgjBdProductEntity setStatus(String status) {
        this.status = status;
        this.nodifySetFiled("status", status);
        return this;
    }

    public String getStatus() {
        this.nodifyGetFiled("status");
        return status;
    }

    public FzgjBdProductEntity setSeq(BigDecimal seq) {
        this.seq = seq;
        this.nodifySetFiled("seq", seq);
        return this;
    }

    public BigDecimal getSeq() {
        this.nodifyGetFiled("seq");
        return seq;
    }

    public FzgjBdProductEntity setMemo(String memo) {
        this.memo = memo;
        this.nodifySetFiled("memo", memo);
        return this;
    }

    public String getMemo() {
        this.nodifyGetFiled("memo");
        return memo;
    }

    public FzgjBdProductEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public FzgjBdProductEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public FzgjBdProductEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public FzgjBdProductEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public FzgjBdProductEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public FzgjBdProductEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public FzgjBdProductEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public FzgjBdProductEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public FzgjBdProductEntity setOpType(String opType) {
        this.opType = opType;
        this.nodifySetFiled("opType", opType);
        return this;
    }

    public String getOpType() {
        this.nodifyGetFiled("opType");
        return opType;
    }

    public FzgjBdProductEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public FzgjBdProductEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public FzgjBdProductEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public FzgjBdProductEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public FzgjBdProductEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public FzgjBdProductEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public FzgjBdProductEntity setEnName(String enName) {
        this.enName = enName;
        this.nodifySetFiled("enName", enName);
        return this;
    }

    public String getEnName() {
        this.nodifyGetFiled("enName");
        return enName;
    }

    public FzgjBdProductEntity setBillCode(String billCode) {
        this.billCode = billCode;
        this.nodifySetFiled("billCode", billCode);
        return this;
    }

    public String getBillCode() {
        this.nodifyGetFiled("billCode");
        return billCode;
    }

    public FzgjBdProductEntity setIsStandard(String isStandard) {
        this.isStandard = isStandard;
        this.nodifySetFiled("isStandard", isStandard);
        return this;
    }

    public String getIsStandard() {
        this.nodifyGetFiled("isStandard");
        return isStandard;
    }

    public FzgjBdProductEntity setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
        this.nodifySetFiled("customerCode", customerCode);
        return this;
    }

    public String getCustomerCode() {
        this.nodifyGetFiled("customerCode");
        return customerCode;
    }

    public FzgjBdProductEntity setIsFwxmEdit(String isFwxmEdit) {
        this.isFwxmEdit = isFwxmEdit;
        this.nodifySetFiled("isFwxmEdit", isFwxmEdit);
        return this;
    }

    public String getIsFwxmEdit() {
        this.nodifyGetFiled("isFwxmEdit");
        return isFwxmEdit;
    }

    public FzgjBdProductEntity setXzfaNo(String xzfaNo) {
        this.xzfaNo = xzfaNo;
        this.nodifySetFiled("xzfaNo", xzfaNo);
        return this;
    }

    public String getXzfaNo() {
        this.nodifyGetFiled("xzfaNo");
        return xzfaNo;
    }

    public FzgjBdProductEntity setPicName(String picName) {
        this.picName = picName;
        this.nodifySetFiled("picName", picName);
        return this;
    }

    public String getPicName() {
        this.nodifyGetFiled("picName");
        return picName;
    }

    public FzgjBdProductEntity setPicPath(String picPath) {
        this.picPath = picPath;
        this.nodifySetFiled("picPath", picPath);
        return this;
    }

    public String getPicPath() {
        this.nodifyGetFiled("picPath");
        return picPath;
    }

    public FzgjBdProductEntity setPicStream(String picStream) {
        this.picStream = picStream;
        this.nodifySetFiled("picStream", picStream);
        return this;
    }

    public String getPicStream() {
        this.nodifyGetFiled("picStream");
        return picStream;
    }

    public FzgjBdProductEntity setIsCsc(String isCsc) {
        this.isCsc = isCsc;
        this.nodifySetFiled("isCsc", isCsc);
        return this;
    }

    public String getIsCsc() {
        this.nodifyGetFiled("isCsc");
        return isCsc;
    }

    public FzgjBdProductEntity setUdf1(String udf1) {
        this.udf1 = udf1;
        this.nodifySetFiled("udf1", udf1);
        return this;
    }

    public String getUdf1() {
        this.nodifyGetFiled("udf1");
        return udf1;
    }

    public FzgjBdProductEntity setUdf2(String udf2) {
        this.udf2 = udf2;
        this.nodifySetFiled("udf2", udf2);
        return this;
    }

    public String getUdf2() {
        this.nodifyGetFiled("udf2");
        return udf2;
    }

    public FzgjBdProductEntity setUdf3(String udf3) {
        this.udf3 = udf3;
        this.nodifySetFiled("udf3", udf3);
        return this;
    }

    public String getUdf3() {
        this.nodifyGetFiled("udf3");
        return udf3;
    }

}
