package com.eci.project.etmsBdDriverQual.entity;

import com.eci.common.utils.ExportUtils.Excel;
import com.eci.crud.entity.EciBaseEntity;

import java.util.Date;

public class EtmsBdDriverQualSearchEntity extends EciBaseEntity {
    private String guid;
    @Excel(value = "司机姓名",order = 1)
    private String name;
    @Excel(value = "自有",order = 2)
    private String isGk;
    @Excel(value = "所属业务伙伴",order = 3)
    private String partnerName;
    @Excel(value = "发证日期",order = 4)
    private String startDate;
    private Date startDateStart;
    private Date startDateEnd;
    @Excel(value = "有效期",order = 5)
    private String endDate;
    private Date endDateEnd;
    private Date endDateStart;
    @Excel(value = "证件状态",order = 6)
    private String status;
    @Excel(value = "是否送审",order = 7)
    private String modMark;
    @Excel(value = "审核通过",order = 8)
    private String checkMark;
    private String createCompany;
    @Excel(value = "创建企业",order = 9)
    private String createCompanyName;
    @Excel(value = "创建时间",order = 10)
    private Date createDate;
    private Date createDateStart;
    private Date createDateEnd;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIsGk() {
        return isGk;
    }

    public void setIsGk(String isGk) {
        this.isGk = isGk;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public Date getStartDateStart() {
        return startDateStart;
    }

    public void setStartDateStart(Date startDateStart) {
        this.startDateStart = startDateStart;
    }

    public Date getStartDateEnd() {
        return startDateEnd;
    }

    public void setStartDateEnd(Date startDateEnd) {
        this.startDateEnd = startDateEnd;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public Date getEndDateEnd() {
        return endDateEnd;
    }

    public void setEndDateEnd(Date endDateEnd) {
        this.endDateEnd = endDateEnd;
    }

    public Date getEndDateStart() {
        return endDateStart;
    }

    public void setEndDateStart(Date endDateStart) {
        this.endDateStart = endDateStart;
    }

    public String getModMark() {
        return modMark;
    }

    public void setModMark(String modMark) {
        this.modMark = modMark;
    }

    public String getCheckMark() {
        return checkMark;
    }

    public void setCheckMark(String checkMark) {
        this.checkMark = checkMark;
    }

    public String getCreateCompany() {
        return createCompany;
    }

    public void setCreateCompany(String createCompany) {
        this.createCompany = createCompany;
    }

    public String getCreateCompanyName() {
        return createCompanyName;
    }

    public void setCreateCompanyName(String createCompanyName) {
        this.createCompanyName = createCompanyName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getCreateDateStart() {
        return createDateStart;
    }

    public void setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
    }

    public Date getCreateDateEnd() {
        return createDateEnd;
    }

    public void setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
    }
    public String getStatus() {
        return status;
    }
    public void setStatus(String status) {
        this.status = status;
    }
}
