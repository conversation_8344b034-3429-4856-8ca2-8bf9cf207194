package com.eci.project.etmsTruckOrderPj.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.eci.project.etmsTruckOrderPj.entity.orderPj;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.etmsTruckOrderPj.entity.EtmsTruckOrderPjEntity;

import java.util.List;


/**
* Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-04-22
*/
public interface EtmsTruckOrderPjDao extends EciBaseDao<EtmsTruckOrderPjEntity> {
    List<orderPj> queryPages(@Param(Constants.WRAPPER) Wrapper queryWrapper);
}