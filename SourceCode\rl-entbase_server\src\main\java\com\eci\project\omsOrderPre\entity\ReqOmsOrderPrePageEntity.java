package com.eci.project.omsOrderPre.entity;

/**
 * @ClassName: ReqOmsOrderPrePageEntity
 * @Author: guangyan.mei
 * @Date: 2025/4/15 16:51
 * @Description: TODO
 */
public class ReqOmsOrderPrePageEntity extends OmsOrderPreEntity {
    public String pageType;

    public String type;

    public String crossItem;
    public String crossLine;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String orderNo; // 订单号

    public String getPageType() {
        return pageType;
    }

    public void setPageType(String pageType) {
        this.pageType = pageType;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCrossItem() {
        return crossItem;
    }

    public void setCrossItem(String crossItem) {
        this.crossItem = crossItem;
    }

    public String getCrossLine() {
        return crossLine;
    }

    public void setCrossLine(String crossLine) {
        this.crossLine = crossLine;
    }

}
