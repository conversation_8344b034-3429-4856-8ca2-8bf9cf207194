package com.eci.project.fzgjCrmEnterprise.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.Zsr;
import com.eci.common.ZsrJson;
import com.eci.common.db.DBHelper;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.exception.BaseException;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjCrmEnterprise.dao.FzgjCrmEnterpriseDao;
import com.eci.project.fzgjCrmEnterprise.entity.FzgjCrmEnterpriseEntity;
import com.eci.project.fzgjCrmEnterprise.entity.FzgjCrmEnterpriseEntityDto;
import com.eci.project.fzgjCrmEnterprise.validate.FzgjCrmEnterpriseVal;

import com.eci.project.fzgjCrmEnterpriseSys.dao.FzgjCrmEnterpriseSysDao;
import com.eci.project.fzgjCrmEnterpriseSys.entity.FzgjCrmEnterpriseSysEntity;
import com.eci.sso.role.entity.UserContext;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.List;


/**
 * 注册企业Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-03-13
 */
@Service
@Slf4j
public class FzgjCrmEnterpriseService implements EciBaseService<FzgjCrmEnterpriseEntity> {

    @Autowired
    private FzgjCrmEnterpriseDao fzgjCrmEnterpriseDao;

    @Autowired
    private FzgjCrmEnterpriseVal fzgjCrmEnterpriseVal;

    @Autowired
    private FzgjCrmEnterpriseSysDao fzgjCrmEnterpriseSysDao;

    @SneakyThrows
    public void selectAddBase(String jsonString) {
        ZsrJson zsrJson = ZsrJson.parse(jsonString);

        // 校验必填项
        zsrJson.check("groupCode").check("groupName").check("listKey");

        List<String> listKey = zsrJson.getStringList("listKey");
        selectAdd(listKey, zsrJson.getString("groupCode"), zsrJson.getString("groupName"));
    }

    @Transactional
    public void selectAdd(List<String> listKey, String groupCode, String groupName) {
        listKey.forEach(code -> {
            boolean exists = fzgjCrmEnterpriseSysDao
                    .select(FzgjCrmEnterpriseSysEntity.Fields.guid)
                    .eq(FzgjCrmEnterpriseSysEntity::getSysCode, code)
                    .eq(FzgjCrmEnterpriseSysEntity::getGroupCode, groupCode)
                    .exists();
            if (!exists) {
                FzgjCrmEnterpriseSysEntity entity = new FzgjCrmEnterpriseSysEntity();
                entity.setGuid(IdWorker.get32UUID());// = Guid.NewGuid().ToString();
                entity.setSysCode(code);
                entity.setGroupCode(groupCode);
                entity.setGroupName(groupName);
                entity.setCreateDate(new java.util.Date());
                entity.setUpdateDate(new java.util.Date());
                entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
                entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
                entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
                entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());

                fzgjCrmEnterpriseSysDao.insertOne(entity);
            }
        });

    }


    /**
     * 右边表格的值移除
     * @param jsonString
     */
    @SneakyThrows
    public void selectRemoveBase(String jsonString) {
        ZsrJson zsrJson = ZsrJson.parse(jsonString);

        // 校验必填项
        zsrJson.check("listKey");

        List<String> listKey = zsrJson.getStringList("listKey");
        selectRemove(listKey);
    }

    @Transactional
    public void selectRemove(List<String> listKey) {
        listKey.forEach(guid -> {
            fzgjCrmEnterpriseSysDao.deleteById(guid);
        });

    }

    @Override
    public TgPageInfo queryPageList(FzgjCrmEnterpriseEntity entity) {
        EciQuery<FzgjCrmEnterpriseEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjCrmEnterpriseEntity> entities = fzgjCrmEnterpriseDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }

    /**
     * 查询左表数据-作业系统
     *
     * @param entity
     * @return
     */
    public TgPageInfo queryPageLeftTable(FzgjCrmEnterpriseEntity entity) {
        if (Zsr.String.IsNullOrWhiteSpace(entity.getGroupCode())) {
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
        }
        // 开始分页, 自定义的方法, 自己决定是否要分页
        startPage();
        List<FzgjCrmEnterpriseEntityDto> entities = DBHelper.selectList(
                "SELECT A.CODE, A.NAME\n" +
                        "        FROM FZGJ_BASE_DATA_DETAIL A\n" +
                        "                 LEFT JOIN FZGJ_BASE_DATA B\n" +
                        "                           ON A.GROUP_CODE = B.GROUP_CODE\n" +
                        "        WHERE B.STATUS = 'Y'\n" +
                        "          AND A.GROUP_CODE = 'SYS'\n" +
                        "          AND CODE NOT IN (SELECT SYS_CODE FROM FZGJ_CRM_ENTERPRISE_SYS WHERE GROUP_CODE = ? " +
                        "        )",
                FzgjCrmEnterpriseEntityDto.class,
                entity.getGroupCode());

        return EciQuery.getPageInfo(entities);
    }

    /**
     * 查询又表数据-作业系统
     *
     * @param entity
     * @return
     */
    public TgPageInfo queryPageRightTable(FzgjCrmEnterpriseEntity entity) {
        EciQuery<FzgjCrmEnterpriseEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjCrmEnterpriseEntityDto> entities = fzgjCrmEnterpriseDao.selectRightTable(eciQuery);
        return EciQuery.getPageInfo(entities);
    }


    @SneakyThrows
    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjCrmEnterpriseEntity save(FzgjCrmEnterpriseEntity entity) {
        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setGuid(IdWorker.getIdStr());
            entity.setCreateDate(new java.util.Date());
            entity.setUpdateDate(new java.util.Date());
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        } else {
            entity.setUpdateDate(new java.util.Date());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        }

        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setGuid(IdWorker.getIdStr());
            entity.setCreateDate(new java.util.Date());
            entity.setUpdateDate(new java.util.Date());
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        } else {
            entity.setUpdateDate(new java.util.Date());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        }

        // 返回实体对象
        FzgjCrmEnterpriseEntity fzgjCrmEnterpriseEntity = null;
        fzgjCrmEnterpriseVal.saveValidate(entity, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjCrmEnterpriseEntity = fzgjCrmEnterpriseDao.insertOne(entity);

        } else {

            fzgjCrmEnterpriseEntity = fzgjCrmEnterpriseDao.updateByEntityId(entity);

        }
        return fzgjCrmEnterpriseEntity;
    }


    @Override
    public List<FzgjCrmEnterpriseEntity> selectList(FzgjCrmEnterpriseEntity entity) {
        return fzgjCrmEnterpriseDao.selectList(entity);
    }

    @Override
    public FzgjCrmEnterpriseEntity selectOneById(Serializable id) {
        return fzgjCrmEnterpriseDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjCrmEnterpriseEntity> list) {
        fzgjCrmEnterpriseDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjCrmEnterpriseDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjCrmEnterpriseDao.deleteById(id);
    }

}