package com.eci.project.fzgjException.service;

import com.eci.common.db.DBHelper;
import com.eci.common.util.StringUtils;
import com.eci.common.utils.CommonLib;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjException.dao.FzgjExceptionDao;
import com.eci.project.fzgjException.entity.FzgjExceptionEntity;
import com.eci.project.fzgjException.entity.ReqFzgjExceptionPageEntity;
import com.eci.project.fzgjException.entity.ResFzgjExceptionPageEntity;
import com.eci.project.fzgjException.validate.FzgjExceptionVal;
import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.List;


/**
 * 订单作业异常Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-04-25
 */
@Service
@Slf4j
public class FzgjExceptionService implements EciBaseService<FzgjExceptionEntity> {

    CommonLib cmn = CommonLib.getInstance();

    @Autowired
    private FzgjExceptionDao fzgjExceptionDao;

    @Autowired
    private FzgjExceptionVal fzgjExceptionVal;


    @Override
    public TgPageInfo queryPageList(FzgjExceptionEntity entity) {
        EciQuery<FzgjExceptionEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjExceptionEntity> entities = fzgjExceptionDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }

    /**
     * 重写-分页列表查询
     */
    public TgPageInfo selectExceptionPageList(ReqFzgjExceptionPageEntity entity) {
        String sql = selectPageListSql(entity);
        startPage();
        List<ResFzgjExceptionPageEntity> entities = DBHelper.selectList(sql, ResFzgjExceptionPageEntity.class);
        return EciQuery.getPageInfo(entities);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjExceptionEntity save(FzgjExceptionEntity entity) {
        // 返回实体对象
        FzgjExceptionEntity fzgjExceptionEntity = null;
        fzgjExceptionVal.saveValidate(entity, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjExceptionEntity = fzgjExceptionDao.insertOne(entity);

        } else {

            fzgjExceptionEntity = fzgjExceptionDao.updateByEntityId(entity);

        }
        return fzgjExceptionEntity;
    }

    @Override
    public List<FzgjExceptionEntity> selectList(FzgjExceptionEntity entity) {
        return fzgjExceptionDao.selectList(entity);
    }

    @Override
    public FzgjExceptionEntity selectOneById(Serializable id) {
        return fzgjExceptionDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjExceptionEntity> list) {
        fzgjExceptionDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjExceptionDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjExceptionDao.deleteById(id);
    }


    /**
     * 列表查询sql语句
     ***/
    public String selectPageListSql(ReqFzgjExceptionPageEntity entity) {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        String sql = "SELECT A.GUID,A.GOODS_GUID,A.TARGET_SYSTEM_CODE,A.BILL_CODE,A.DOC_NO\n" +
                ",A.FWLX_CODE,A.LINK_CODE,A.EXCEPTION_TYPE,A.EXCEPTION_MEMO,A.ZRF\n" +
                ",A.DEAL_FA,A.DEAL_MEMO,A.MEMO,A.CREATE_USER,A.CREATE_USER_NAME\n" +
                ",A.CREATE_DATE,A.UPDATE_USER,A.UPDATE_USER_NAME,A.UPDATE_DATE,A.NODE_CODE\n" +
                ",A.NODE_NAME,A.COMPANY_CODE,A.COMPANY_NAME,A.GROUP_CODE,A.GROUP_NAME\n" +
                ",A.STATUS,A.EXCEPTION_NO,A.EXCEPTION_DATE,A.ORDER_NO,B.CONSIGNEE_CODE\n" +
                ",B.OP_TYPE,B.PRODUCT_CODE\n" +
                ",(SELECT NAME FROM FZGJ_EXCEPTION_TYPE WHERE GROUP_CODE=A.GROUP_CODE AND STATUS='Y' AND CODE = A.EXCEPTION_TYPE AND ROWNUM=1) EXCEPTION_TYPE_NAME\n" +
                ",(SELECT CASE WHEN INSTR(NAME,'|')>0 THEN SUBSTR(NAME,0, INSTR(NAME,'|')-1 ) ELSE NAME END  FROM FZGJ_BD_SERVICE_TYPE WHERE CODE = A.FWLX_CODE AND ROWNUM=1) FWLX_CODE_NAME\n" +
                ",(SELECT NAME FROM FZGJ_BD_BILL  WHERE STATUS = 'Y' AND CODE = A.BILL_CODE AND ROWNUM=1)  BILL_CODE_NAM\n" +
                ",(SELECT NAME FROM FZGJ_BASE_DATA_DETAIL  WHERE STATUS = 'Y' AND GROUP_CODE = a.GROUP_CODE  AND CODE = A.ZRF AND ROWNUM=1) ZRF_NAME\n" +
                ",(DECODE(A.STATUS,'Y','已处理','N','未处理','')) STATUS_NAME\n" +
                ",(SELECT MAX(NAME)  FROM FZGJ_TASK_LIMITATION_PT  WHERE STATUS = 'Y'AND CODE = A.LINK_CODE AND ROWNUM=1) LINK_CODE_NAME\n" +
                ",(SELECT MAX(NAME)  FROM CRM_CUSTOMER  WHERE STATUS='Y' AND CODE = A.CONSIGNEE_CODE AND A.GROUP_CODE = A.GROUP_CODE AND ROWNUM=1) CONSIGNEE_CODE_NAME\n" +
                ",(SELECT MAX(NAME)  FROM FZGJ_BD_OP_TYPE  WHERE STATUS='Y' AND GROUP_CODE=A.GROUP_CODE AND CODE = A.OP_TYPE AND SYS_CODE='OMS_ORDER' AND ROWNUM=1) OP_TYPE_NAME\n" +
                ",(SELECT MAX(NAME) FROM FZGJ_BD_PRODUCT  WHERE STATUS='Y' AND GROUP_CODE=A.GROUP_CODE AND CODE = A.PRODUCT_CODE AND ROWNUM=1) PRODUCT_CODE_NAME\n" +
                "FROM FZGJ_EXCEPTION A  LEFT JOIN OMS_ORDER B ON A.ORDER_NO=B.ORDER_NO AND A.GROUP_CODE=B.GROUP_CODE\n" +
                "WHERE 1=1";

         sql += " AND A.GROUP_CODE=" + cmn.SQLQ(UserContext.getUserInfo().getCompanyCode());

        /**查询条件***/
        if (!StringUtils.isEmpty(entity.getOrderNo())) {
            sql += " AND A.ORDER_NO LIKE " + cmn.SQLQL(entity.getOrderNo());
        }

        if (!StringUtils.isEmpty(entity.getDocNo())) {
            sql += " AND A.DOC_NO LIKE" + cmn.SQLQL(entity.getDocNo());
        }

        if (!StringUtils.isEmpty(entity.getConsigneeCode())) {
            sql += " AND B.CONSIGNEE_CODE LIKE" + cmn.SQLQL(entity.getConsigneeCode());
        }

        if (!StringUtils.isEmpty(entity.getOpType())) {
            sql += " AND B.OP_TYPE=" + cmn.SQLQ(entity.getOpType());
        }

        if (!StringUtils.isEmpty(entity.getFwlxCode())) {
            sql += " AND A.FWLX_CODE=" + cmn.SQLQ(entity.getFwlxCode());
        }

        if (!StringUtils.isEmpty(entity.getIsJJH())) {
            sql += " AND B.IS_JJH=" + cmn.SQLQ(entity.getIsJJH());
        }

        if (!StringUtils.isEmpty(entity.getJdUser())) {
            sql += " AND B.JD_USER=" + cmn.SQLQ(entity.getJdUser());
        }

        if (!StringUtils.isEmpty(entity.getExceptionNo())) {
            sql += " AND A.EXCEPTION_NO LIKE" + cmn.SQLQL(entity.getExceptionNo());
        }

        if (!StringUtils.isEmpty(entity.getExceptionType())) {
            sql += " AND A.EXCEPTION_TYPE=" + cmn.SQLQ(entity.getExceptionType());
        }

        if (entity.getExceptionDateStart() != null) {
            String strStartDate = sdf.format(entity.getExceptionDateStart()); // 将 Calendar 中的日期和时间格式化成字符串
            sql += " AND A.EXCEPTION_DATE>=TO_DATE(" + cmn.SQLQ(strStartDate) + ",'yyyy-MM-dd')";
        }

        if (entity.getExceptionDateEnd() != null) {
            String strEndDate = sdf.format(entity.getExceptionDateEnd()); // 将 Calendar 中的日期和时间格式化成字符串
            sql += " AND A.EXCEPTION_DATE<=TO_DATE(" + cmn.SQLQ(strEndDate) + ",'yyyy-MM-dd')";
        }
        if (!StringUtils.isEmpty(entity.getStatus())) {
            sql += " AND A.STATUS=" + cmn.SQLQ(entity.getStatus());
        }

        return sql;
    }
}