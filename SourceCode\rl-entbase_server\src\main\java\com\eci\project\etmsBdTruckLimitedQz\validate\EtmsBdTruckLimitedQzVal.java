package com.eci.project.etmsBdTruckLimitedQz.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.etmsBdTruckLimitedQz.entity.EtmsBdTruckLimitedQzEntity;

import org.springframework.stereotype.Service;


/**
* 车辆年审历史Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-16
*/
@Service
public class EtmsBdTruckLimitedQzVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(EtmsBdTruckLimitedQzEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(EtmsBdTruckLimitedQzEntity entity, BusinessType businessType) {

    }

}
