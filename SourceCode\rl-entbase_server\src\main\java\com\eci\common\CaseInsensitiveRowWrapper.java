package com.eci.common;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.locks.StampedLock;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 包装一个行数据对象，使其支持不区分大小写的键值查找
 * 该类通过维护原始数据和规范化数据的映射，实现了在不区分大小写的情况下高效查找行数据
 */
public class CaseInsensitiveRowWrapper {

    // 原始和规范化数据存储
    private final Map<String, Object> originalRow;
    private final Map<String, Object> normalizedRow;

    // 高级缓存管理
    private final AdvancedConcurrentCache<String, Object> lookupCache;

    // 并发控制
    private final StampedLock stampedLock;

    /**
     * 构造函数
     * 对原始数据进行防御性拷贝，并初始化缓存及并发锁
     * 预处理数据以生成规范化映射
     *
     * @param row 原始行数据，如果为null，则创建一个空的Map
     */
    public CaseInsensitiveRowWrapper(Map<String, Object> row) {
        // 防御性拷贝
        this.originalRow = row == null ? Collections.emptyMap() : new HashMap<>(row);

        // 缓存初始化
        this.lookupCache = new AdvancedConcurrentCache<>(1000);

        // 并发锁
        this.stampedLock = new StampedLock();

        // 预处理数据
        this.normalizedRow = preprocessMap(this.originalRow);
    }

    /**
     * 预处理 Map，将键进行规范化处理以支持不区分大小写的查找
     * 使用并发HashMap并行处理大数据量，确保线程安全和高效
     *
     * @param row 原始行数据，如果为null或空，则返回一个空的Map
     * @return 规范化后的行数据Map
     */
    private Map<String, Object> preprocessMap(Map<String, Object> row) {
        if (row == null || row.isEmpty()) {
            return Collections.emptyMap();
        }

        Map<String, Object> normalized = new ConcurrentHashMap<>();

        Stream<Map.Entry<String, Object>> stream = row.size() > 1000 ? row.entrySet().parallelStream() : row.entrySet().stream();
        normalized.putAll(
                stream
                        .filter(entry -> entry.getValue() != null) // 增加过滤器，直接排除值为 null 的 entry
                        .collect(Collectors.toMap(
                                entry -> normalizeKey(entry.getKey()),
//                        entry -> Optional.ofNullable(entry.getValue()).orElse(null), // Handle potential null value from getValue()
                                Map.Entry::getValue, // Now getValue() should not be null (after filter)
                                (v1, v2) -> v1  // 冲突时保留第一个值
                        ))
        );

        return normalized;
    }


    private static final Pattern PREFIX_PATTERN = Pattern.compile("^[a-zA-Z]+_");
    private static final Pattern UNDERSCORE_PATTERN = Pattern.compile("_");

    /**
     * Key 标准化方法
     * 移除前缀和下划线，转换为小写，以实现不区分大小写的键值查找
     *
     * @param key 原始键值，如果为null或空，则返回空字符串
     * @return 规范化后的键值
     */
    private String normalizeKey(String key) {
        if (key == null || key.trim().isEmpty()) {
            return "";
        }

        String normalizedKey = key.toLowerCase();
        normalizedKey = PREFIX_PATTERN.matcher(normalizedKey).replaceAll("");
        normalizedKey = UNDERSCORE_PATTERN.matcher(normalizedKey).replaceAll("");
        return normalizedKey;
    }

    /**
     * 高级查找方法，首先尝试从缓存中获取值，未命中则从原始或规范化Map中查找
     * 使用乐观读锁和悲观读锁升级机制，保证并发安全和高效
     *
     * @param columnName 列名，如果为null或空，则返回null
     * @return 查找到的值，如果未找到，则返回null
     */
    public Object get(String columnName) {
        if (columnName == null || columnName.trim().isEmpty()) {
            return null;
        }

        // 优先从缓存获取
        Object cachedValue = lookupCache.get(normalizeKey(columnName));
        if (cachedValue != null) {
            return cachedValue;
        }

        // 使用乐观读锁
        long stamp = stampedLock.tryOptimisticRead();
        Object result = null;

        // 精确匹配原始 Map
        result = originalRow.get(columnName);
        if (result != null) {
            // 更新缓存
            lookupCache.put(normalizeKey(columnName), result);
            return result;
        }

        // 使用规范化查找
        String normalizedColumnName = normalizeKey(columnName);
        result = normalizedRow.get(normalizedColumnName);

        // 验证读取一致性
        if (!stampedLock.validate(stamp)) {
            // 升级为悲观读锁
            stamp = stampedLock.readLock();
            try {
                result = normalizedRow.get(normalizedColumnName);
            } finally {
                stampedLock.unlockRead(stamp);
            }
        }

        // 缓存结果
        if (result != null) {
            lookupCache.put(normalizedColumnName, result);
        }

        return result;
    }

    /**
     * 批量查找方法，对多个列名进行批量查找，返回非空值的Map
     * 利用并行流提高查找效率
     *
     * @param columnNames 列名列表，如果为null或空，则返回一个空的Map
     * @return 查找到的列值Map，如果未找到任何值，则返回一个空的Map
     */
    public Map<String, Object> getBatch(List<String> columnNames) {
        if (columnNames == null || columnNames.isEmpty()) {
            return Collections.emptyMap();
        }

        Stream<String> stream = columnNames.size() > 1000 ? columnNames.parallelStream() : columnNames.stream();
        return stream
                .map(col -> new AbstractMap.SimpleEntry<>(col, get(col)))
                .filter(entry -> entry.getValue() != null)
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue
                ));
    }

    /**
     * 带默认值的查找，如果未找到指定列名的值，则返回默认值，当前默认值为：""
     *
     * @param columnName 列名，如果为null或空，则返回null
     * @return 查找到的值或默认值
     */
    public Object getOrDefault(String columnName) {
        Object result = get(columnName);
        return result != null ? result : "";
    }

    /**
     * 带默认值的查找，如果未找到指定列名的值，则返回默认值，当前默认值为：""
     *
     * @param columnName 列名，如果为null或空，则返回null
     * @return 查找到的值或默认值, String 类型
     */
    public String getOrDefaultToString(String columnName) {
        Object result = get(columnName);
        return result != null ? result.toString() : "";
    }

    /**
     * 带默认值的查找，如果未找到指定列名的值，则返回默认值
     *
     * @param columnName   列名，如果为null或空，则返回null
     * @param defaultValue 默认值
     * @return 查找到的值或默认值
     */
    public Object getOrDefault(String columnName, Object defaultValue) {
        Object result = get(columnName);
        return result != null ? result : defaultValue;
    }

    /**
     * 带默认值的整型查找
     *
     * @param columnName   列名
     * @param defaultValue 默认值
     * @return 整型值或默认值
     */
    public int getOrDefaultInt(String columnName, int defaultValue) {
        Object result = get(columnName);
        if (result == null) {
            return defaultValue;
        }

        try {
            if (result instanceof Number) {
                return ((Number) result).intValue();
            }
            return Integer.parseInt(result.toString());
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    /**
     * 带默认值的双精度浮点型查找
     *
     * @param columnName   列名
     * @param defaultValue 默认值
     * @return 双精度浮点值或默认值
     */
    public double getOrDefaultDouble(String columnName, double defaultValue) {
        Object result = get(columnName);
        if (result == null) {
            return defaultValue;
        }

        try {
            if (result instanceof Number) {
                return ((Number) result).doubleValue();
            }
            return Double.parseDouble(result.toString());
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    /**
     * 带默认值的长整型查找
     *
     * @param columnName   列名
     * @param defaultValue 默认值
     * @return 长整型值或默认值
     */
    public long getOrDefaultLong(String columnName, long defaultValue) {
        Object result = get(columnName);
        if (result == null) {
            return defaultValue;
        }

        try {
            if (result instanceof Number) {
                return ((Number) result).longValue();
            }
            return Long.parseLong(result.toString());
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    /**
     * 带默认值的BigDecimal查找
     *
     * @param columnName   列名
     * @param defaultValue 默认值（建议使用BigDecimal.ZERO等常量）
     * @return BigDecimal值或默认值
     */
    public BigDecimal getOrDefaultBigDecimal(String columnName, BigDecimal defaultValue) {
        Object result = get(columnName);
        if (result == null) {
            return defaultValue;
        }

        try {
            if (result instanceof BigDecimal) {
                return (BigDecimal) result;
            }
            if (result instanceof Number) {
                return new BigDecimal(result.toString());
            }
            return new BigDecimal(result.toString().trim());
        } catch (NumberFormatException | NullPointerException e) {
            return defaultValue;
        }
    }

    /**
     * 获取原始 Map 副本，返回一个包含原始数据的新的HashMap
     *
     * @return 原始行数据的Map副本
     */
    public Map<String, Object> getOriginalRow() {
        return new HashMap<>(originalRow);
    }

    /**
     * 获取规范化 Map 副本，返回一个包含规范化数据的新的HashMap
     *
     * @return 规范化行数据的Map副本
     */
    public Map<String, Object> getNormalizedRow() {
        return new HashMap<>(normalizedRow);
    }

    /**
     * 内部高级缓存实现，使用写优先策略，支持并发读写
     * 通过LRU策略管理缓存大小，确保缓存不会无限制增长
     */
    private static class AdvancedConcurrentCache<K, V> {
        private final Map<K, V> cache;
        private final StampedLock stampedLock;
        private final int maxSize;
        private final ConcurrentHashMap<K, Long> accessOrder;
        private final ExecutorService cacheExecutor;

        public AdvancedConcurrentCache(int maxSize) {
            this.maxSize = maxSize;
            this.cache = new ConcurrentHashMap<>(maxSize);
            this.stampedLock = new StampedLock();
            this.accessOrder = new ConcurrentHashMap<>();

            this.cacheExecutor = new ThreadPoolExecutor(
                    2, 4, 60L, TimeUnit.SECONDS,
                    new LinkedBlockingQueue<>(100),
                    new ThreadPoolExecutor.CallerRunsPolicy()
            );
        }

        public void put(K key, V value) {
            cacheExecutor.submit(() -> {
                long stamp = stampedLock.writeLock();
                try {
                    if (cache.size() >= maxSize) {
                        evictCache();
                    }
                    cache.put(key, value);
                    accessOrder.put(key, System.nanoTime());
                } catch (Exception e) {
                    PrintUtil.customPrint("Cache update failed", e);
                } finally {
                    stampedLock.unlockWrite(stamp);
                }
            });
        }

        public V get(K key) {
            long stamp = stampedLock.tryOptimisticRead();
            V value = cache.get(key);

            if (!stampedLock.validate(stamp)) {
                stamp = stampedLock.readLock();
                try {
                    value = cache.get(key);
                } finally {
                    stampedLock.unlockRead(stamp);
                }
            }

            if (value != null) {
                accessOrder.put(key, System.nanoTime());
            }

            return value;
        }

        private void evictCache() {
            while (cache.size() >= maxSize) {
                K oldestKey = accessOrder.entrySet().stream()
                        .min(Map.Entry.comparingByValue())
                        .map(Map.Entry::getKey)
                        .orElse(null);

                if (oldestKey != null) {
                    cache.remove(oldestKey);
                    accessOrder.remove(oldestKey);
                }
            }
        }

        /**
         * 关闭缓存执行器
         */
        public void shutdown() {
            cacheExecutor.shutdown();
            try {
                if (!cacheExecutor.awaitTermination(60, TimeUnit.SECONDS)) {
                    cacheExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                cacheExecutor.shutdownNow();
            }
        }
    }


}


/**
 * 处理大量数据，并且支持缓存
 * <p>
 * public class CaseInsensitiveRowWrapper {
 * private static final Logger logger = Logger.getLogger(CaseInsensitiveRowWrapper.class.getName());
 * <p>
 * // 使用 ConcurrentHashMap
 * private final ConcurrentMap<String, Object> originalRow;
 * private final ConcurrentMap<String, Object> normalizedRow;
 * <p>
 * // 缓存淘汰相关
 * private static final int MAX_CACHE_SIZE = 10_000; // 最大缓存数量
 * private static final long CACHE_EXPIRATION_TIME_MS = TimeUnit.HOURS.toMillis(2); // 缓存过期时间（毫秒）
 * private static final Map<String, CachedValue> valueCache = new LinkedHashMap<String, CachedValue>(MAX_CACHE_SIZE + 1, 0.75f, true) {
 *
 * @Override protected boolean removeEldestEntry(Map.Entry<String, CachedValue> eldest) {
 * return size() > MAX_CACHE_SIZE || eldest.getValue().isExpired();
 * }
 * };
 * <p>
 * <p>
 * // 定时任务服务用于缓存清理
 * private static final ScheduledExecutorService scheduler =
 * Executors.newSingleThreadScheduledExecutor();
 * <p>
 * // 缓存值包装器
 * private static class CachedValue {
 * private final Object value;
 * private final long timestamp;
 * <p>
 * public CachedValue(Object value) {
 * this.value = value;
 * this.timestamp = System.currentTimeMillis();
 * }
 * <p>
 * public Object getValue() {
 * return value;
 * }
 * <p>
 * public boolean isExpired() {
 * return System.currentTimeMillis() - timestamp > CACHE_EXPIRATION_TIME_MS;
 * }
 * }
 * <p>
 * // 静态初始化缓存清理任务
 * static {
 * scheduler.scheduleAtFixedRate(() -> {
 * try {
 * // 定期清理过期缓存
 * Iterator<Map.Entry<String, CachedValue>> iterator = valueCache.entrySet().iterator();
 * while (iterator.hasNext()) {
 * Map.Entry<String, CachedValue> entry = iterator.next();
 * if (entry.getValue().isExpired()) {
 * iterator.remove();
 * }
 * }
 * } catch (Exception e) {
 * // 记录异常，但不中断定时任务
 * logger.log(Level.SEVERE, "Error during cache cleanup", e);
 * }
 * }, CACHE_EXPIRATION_TIME_MS, CACHE_EXPIRATION_TIME_MS, TimeUnit.MILLISECONDS);
 * }
 * <p>
 * public CaseInsensitiveRowWrapper(Map<String, Object> row) {
 * if (row == null) {
 * throw new IllegalArgumentException("Input map cannot be null");
 * }
 * <p>
 * // 转换为 ConcurrentHashMap
 * this.originalRow = new ConcurrentHashMap<>(row);
 * this.normalizedRow = preprocessMap(row);
 * }
 * <p>
 * // 预处理 Map，构建一个标准化的映射
 * private ConcurrentMap<String, Object> preprocessMap(Map<String, Object> row) {
 * ConcurrentMap<String, Object> normalized = new ConcurrentHashMap<>(row);
 * <p>
 * // 预处理的多种匹配键
 * row.forEach((key, value) -> {
 * // 移除前缀和下划线的 key
 * String normalizedKey = normalizeKey(key);
 * <p>
 * // 只有在不存在时才添加，保留第一个匹配
 * normalized.putIfAbsent(normalizedKey, value);
 * });
 * <p>
 * return normalized;
 * }
 * <p>
 * // key 标准化方法
 * private static final Pattern PREFIX_PATTERN = Pattern.compile("^[a-zA-Z]+_");
 * private static final Pattern UNDERSCORE_PATTERN = Pattern.compile("_");
 * <p>
 * private String normalizeKey(String key) {
 * if (key == null) {
 * return "";
 * }
 * <p>
 * return PREFIX_PATTERN.matcher(key.toLowerCase()).replaceAll("").replaceAll("_", "");
 * }
 * <p>
 * public Object get(String columnName) {
 * // 如果columnName为null，直接返回null
 * if (columnName == null) {
 * return null;
 * }
 * <p>
 * // 检查缓存
 * CachedValue cachedValue = valueCache.get(columnName);
 * if (cachedValue != null && !cachedValue.isExpired()) {
 * return cachedValue.getValue();
 * }
 * <p>
 * // 先尝试精确匹配原始 Map
 * Object exactMatch = originalRow.get(columnName);
 * if (exactMatch != null) {
 * cacheValue(columnName, exactMatch);
 * return exactMatch;
 * }
 * <p>
 * // 使用预处理的 Map 查找
 * String normalizedColumnName = normalizeKey(columnName);
 * Object normalizedMatch = normalizedRow.get(normalizedColumnName);
 * <p>
 * // 缓存结果
 * if (normalizedMatch != null) {
 * cacheValue(columnName, normalizedMatch);
 * }
 * <p>
 * return normalizedMatch;
 * }
 * <p>
 * // 缓存值的方法
 * private void cacheValue(String key, Object value) {
 * valueCache.put(key, new CachedValue(value));
 * }
 * <p>
 * // 获取原始 Map
 * public Map<String, Object> getOriginalRow() {
 * return Collections.unmodifiableMap(originalRow);
 * }
 * <p>
 * // 获取预处理后的 Map
 * public Map<String, Object> getNormalizedRow() {
 * return Collections.unmodifiableMap(normalizedRow);
 * }
 * <p>
 * // 应用关闭时调用，关闭调度器
 * public static void shutdown() {
 * scheduler.shutdown();
 * }
 * }
 */

//
//public class CaseInsensitiveRowWrapper {
//
//    /**
//     *  方案1：使用 TreeMap 并忽略大小写
//     *     优点：
//     *         性能最佳
//     *         代码最简洁
//     *         查找时间复杂度 O(log n)
//     *         无需每次查找都遍历整个 Map
//     */
//    private final Map<String, Object> row;
//
//    public CaseInsensitiveRowWrapper(Map<String, Object> row) {
//        this.row = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
//        this.row.putAll(row);
//    }
//
//    public Object get(String columnName) {
//        return row.get(columnName);
//    }
//
///**
// * 方案2
// */
///*
//    方案2：使用 computeIfAbsent 缓存
//    优点：
//        带缓存机制
//        后续相同 key 的查找性能更好
//        线程安全
//
//        public class CaseInsensitiveRowWrapper {
//        private final Map<String, Object> row;
//        private final Map<String, Object> caseInsensitiveCache = new ConcurrentHashMap<>();
//
//        public CaseInsensitiveRowWrapper(Map<String, Object> row) {
//            this.row = row;
//        }
//
//        public Object get(String columnName) {
//            return caseInsensitiveCache.computeIfAbsent(columnName.toLowerCase(), key ->
//                row.entrySet().stream()
//                    .filter(entry -> entry.getKey().toLowerCase().equals(key))
//                    .map(Map.Entry::getValue)
//                    .findFirst()
//                    .orElse(null)
//            );
//        }
//    }
// */
//
///**
// * 方案3
// */
///*
//    方案3：预处理 Map
//    优点：
//        查找性能 O(1)
//        初始化时一次性处理
//        代码清晰
//
//
//    public class CaseInsensitiveRowWrapper {
//        private final Map<String, Object> caseInsensitiveMap;
//
//        public CaseInsensitiveRowWrapper(Map<String, Object> row) {
//            caseInsensitiveMap = row.entrySet().stream()
//                .collect(Collectors.toMap(
//                    entry -> entry.getKey().toLowerCase(),
//                    Map.Entry::getValue,
//                    (v1, v2) -> v1  // 如果有重复 key，保留第一个
//                ));
//        }
//
//        public Object get(String columnName) {
//            return caseInsensitiveMap.get(columnName.toLowerCase());
//        }
//    }
// */
//
//    /**
//     * 建议：
//     *
//     * 对于大多数场景，推荐方案1（TreeMap）
//     * 如果需要缓存，用方案2
//     * 如果查找非常频繁，可以用方案3
//     *
//     * 选择哪种方案取决于：
//     *      数据量大小
//     *      查找频率
//     *      性能要求
//     *      内存使用情况
//     */
//
// /*
//
// private final Map<String, Object> row;
//
//    public CaseInsensitiveRowWrapper(Map<String, Object> row) {
//        this.row = row;
//    }
//
//    public Object get(String columnName) {
//        return row.entrySet().stream()
//                .filter(entry -> entry.getKey().equalsIgnoreCase(columnName))
//                .map(Map.Entry::getValue)
//                .findFirst()
//                .orElse(null);
//    }
//
//  */
//}
