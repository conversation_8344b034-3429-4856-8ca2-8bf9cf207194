package com.eci.project.fzgjBdTruckSpecCom.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjBdTruckSpecCom.entity.FzgjBdTruckSpecComEntity;

import org.springframework.stereotype.Service;


/**
* 计费车辆尺寸Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-03-25
*/
@Service
public class FzgjBdTruckSpecComVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjBdTruckSpecComEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjBdTruckSpecComEntity entity, BusinessType businessType) {

    }

}
