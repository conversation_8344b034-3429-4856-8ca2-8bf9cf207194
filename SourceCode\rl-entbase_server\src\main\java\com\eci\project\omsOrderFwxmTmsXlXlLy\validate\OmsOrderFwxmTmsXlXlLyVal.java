package com.eci.project.omsOrderFwxmTmsXlXlLy.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.omsOrderFwxmTmsXlXlLy.entity.OmsOrderFwxmTmsXlXlLyEntity;

import org.springframework.stereotype.Service;


/**
* 委托内容-程运序列-陆运Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-06-04
*/
@Service
public class OmsOrderFwxmTmsXlXlLyVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(OmsOrderFwxmTmsXlXlLyEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(OmsOrderFwxmTmsXlXlLyEntity entity, BusinessType businessType) {

    }

}
