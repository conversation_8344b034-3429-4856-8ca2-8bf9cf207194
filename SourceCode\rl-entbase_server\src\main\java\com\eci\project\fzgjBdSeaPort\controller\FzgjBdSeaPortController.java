package com.eci.project.fzgjBdSeaPort.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjBdSeaPort.service.FzgjBdSeaPortService;
import com.eci.project.fzgjBdSeaPort.entity.FzgjBdSeaPortEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 海运港口Controller
*
* @<NAME_EMAIL>
* @date 2025-03-14
*/
@Api(tags = "海运港口")
@RestController
@RequestMapping("/fzgjBdSeaPort")
public class FzgjBdSeaPortController extends EciBaseController {

    @Autowired
    private FzgjBdSeaPortService fzgjBdSeaPortService;


    @ApiOperation("海运港口:保存")
    @EciLog(title = "海运港口:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody FzgjBdSeaPortEntity entity){
        FzgjBdSeaPortEntity fzgjBdSeaPortEntity =fzgjBdSeaPortService.save(entity);
        return ResponseMsgUtil.success(10001,fzgjBdSeaPortEntity);
    }


    @ApiOperation("海运港口:查询列表")
    @EciLog(title = "海运港口:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjBdSeaPortEntity entity){
        List<FzgjBdSeaPortEntity> fzgjBdSeaPortEntities = fzgjBdSeaPortService.selectList(entity);
        return ResponseMsgUtil.success(10001,fzgjBdSeaPortEntities);
    }


    @ApiOperation("海运港口:分页查询列表")
    @EciLog(title = "海运港口:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjBdSeaPortEntity entity){
        TgPageInfo tgPageInfo = fzgjBdSeaPortService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("海运港口:根据ID查一条")
    @EciLog(title = "海运港口:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjBdSeaPortEntity entity){
        FzgjBdSeaPortEntity  fzgjBdSeaPortEntity = fzgjBdSeaPortService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,fzgjBdSeaPortEntity);
    }


    @ApiOperation("海运港口:根据ID删除一条")
    @EciLog(title = "海运港口:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjBdSeaPortEntity entity){
        int count = fzgjBdSeaPortService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("海运港口:根据ID字符串删除多条")
    @EciLog(title = "海运港口:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjBdSeaPortEntity entity) {
        int count = fzgjBdSeaPortService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}