<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.etmsBdTruckQualificateQz.dao.EtmsBdTruckQualificateQzDao">
    <resultMap type="EtmsBdTruckQualificateQzEntity" id="EtmsBdTruckQualificateQzResult">
        <result property="guid" column="GUID"/>
        <result property="truckGuid" column="TRUCK_GUID"/>
        <result property="qualificateType" column="QUALIFICATE_TYPE"/>
        <result property="qualificateName" column="QUALIFICATE_NAME"/>
        <result property="qualificateNo" column="QUALIFICATE_NO"/>
        <result property="qualificateStatus" column="QUALIFICATE_STATUS"/>
        <result property="issueDate" column="ISSUE_DATE"/>
        <result property="validityDate" column="VALIDITY_DATE"/>
        <result property="remark" column="REMARK"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="modMark" column="MOD_MARK"/>
    </resultMap>

    <sql id="selectEtmsBdTruckQualificateQzEntityVo">
        select
            GUID,
            TRUCK_GUID,
            QUALIFICATE_TYPE,
            QUALIFICATE_NAME,
            QUALIFICATE_NO,
            QUALIFICATE_STATUS,
            ISSUE_DATE,
            VALIDITY_DATE,
            REMARK,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            CREATE_USER_NAME,
            UPDATE_USER_NAME,
            MOD_MARK
        from ETMS_BD_TRUCK_QUALIFICATE_QZ
    </sql>
</mapper>