package com.eci.project.omsOrder.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.common.DictField;
import com.eci.common.ZsrBaseEntity;
import com.eci.common.validations.ZsrValidation;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 订单表对象 OMS_ORDER
 * 可以自己扩展字段
 *
 * @<NAME_EMAIL>
 * @date 2025-04-14
 */
@ApiModel("订单表")
@TableName("OMS_ORDER")
@FieldNameConstants
public class OmsOrderEntity extends ZsrBaseEntity {
    /**
     * 服务项目 用于接收前端传过来的参数，不用存数据库
     */
    @ApiModelProperty("服务项目")
    @TableField(exist = false)
    private String fwlxItem;

    /**
     * 订单号,保存为模板时，借用此字段存模板编号
     */
    @ApiModelProperty("订单号,保存为模板时，借用此字段存模板编号(36)")
    @TableId("ORDER_NO")
    private String orderNo;

    /**
     * 委托单编号
     */
    @ApiModelProperty("委托单编号(36)")
    @TableField("PRE_NO")
    private String preNo;

    /**
     * 委托方
     */
    @DictField(sql = "SELECT A.CODE, A.NAME FROM CRM_CUSTOMER A")
    @ApiModelProperty("委托方(200)")
    @TableField("CONSIGNEE_CODE")
    @ZsrValidation(required = true, displayName = "委托方")
    private String consigneeCode;

    /**
     * 实际发货方
     */
    @DictField(sql = "SELECT CODE, NAME FROM CRM_CUSTOMER A WHERE A.STATUS = 'Y'")
    @ApiModelProperty("实际发货方(36)")
    @TableField("SHIPPER")
    private String shipper;

    /**
     * 实际收货方
     */
    @DictField(sql = "SELECT CODE, NAME FROM CRM_CUSTOMER A WHERE A.STATUS = 'Y'")
    @ApiModelProperty("实际收货方(36)")
    @TableField("RECEIVER")
    private String receiver;

    /**
     * 客户事业部
     */
    @DictField(sql = "SELECT A.CODE, A.NAME FROM CRM_CUSTOMER_KHSYB A WHERE A.STATUS = 'Y'")
    @ApiModelProperty("客户事业部(50)")
    @TableField("CUSTOMER_BU")
    private String customerBu;

    /**
     * 客户单据编号(委托方+客户单据编号唯一)
     */
    @ApiModelProperty("客户单据编号(委托方+客户单据编号唯一)(50)")
    @TableField("CUSTOMER_ORDER_NO")
    private String customerOrderNo;

    /**
     * 业务类型
     */
    @DictField(sql = "SELECT A.CODE, A.NAME FROM FZGJ_BD_OP_TYPE A WHERE A.STATUS = 'Y'")
    @ApiModelProperty("业务类型(20)")
    @TableField("OP_TYPE")
    @ZsrValidation(required = true, displayName = "业务类型")
    private String opType;

    /**
     * 业务产品/项目
     */
//    @DictField(queryKey = "OMS_BD_PRODUCTS")
    @DictField(sql="SELECT A.CODE, A.NAME  FROM FZGJ_BD_PRODUCT A  WHERE A.STATUS = 'Y'")
    @ApiModelProperty("业务产品/项目(20)")
    @TableField("PRODUCT_CODE")
    @ZsrValidation(required = true, displayName = "业务产品/项目")
    private String productCode;

    /**
     * 是否加急
     */
    @ApiModelProperty("是否加急(1)")
    @TableField("IS_JJH")
    private String isJjh;

    /**
     * 业务日期
     */
    @ApiModelProperty("业务日期(7)")
    @TableField("OP_DATE")
    @JSONField(format = "yyyy-MM-dd")
    @DictField(useDateFormat = true)
    private Date opDate;

    @ApiModelProperty("业务日期开始")
    @TableField(exist = false)
    private Date opDateStart;

    @ApiModelProperty("业务日期结束")
    @TableField(exist = false)
    private Date opDateEnd;

    /**
     * 客户付款方案编号
     */
    @ApiModelProperty("客户付款方案编号(20)")
    @TableField("FKFA_CODE")
    private String fkfaCode;

    /**
     * 应收结算方(1~n个)
     */
    @DictField(sql = "SELECT A.CODE, A.NAME FROM BMC_CUSTOMER A WHERE A.CODE != '-' AND A.STATUS = 'Y' AND (COMPANY_TYPE LIKE '%1%' OR COMPANY_TYPE LIKE '%3%')")
    @ApiModelProperty("应收结算方(1~n个)(200)")
    @TableField("ACCOUNT_CODE")
    private String accountCode;

    /**
     * 结算方式
     */
    @ApiModelProperty("结算方式(20)")
    @TableField("ACCOUNT_MODE")
    private String accountMode;

    /**
     * 支付方式
     */
    @ApiModelProperty("支付方式(20)")
    @TableField("PAY_MODE")
    private String payMode;

    /**
     * 接单员
     */
    @ApiModelProperty("接单员(50)")
    @TableField("JD_USER")
    private String jdUser;

    /**
     * 接单组织
     */
    @ApiModelProperty("接单组织(50)")
    @TableField("JD_NODE")
    private String jdNode;

    /**
     * 接单公司
     */
    @ApiModelProperty("接单公司(50)")
    @TableField("JD_COMPANY")
    private String jdCompany;

    /**
     * 销售员
     */
    @ApiModelProperty("销售员(50)")
    @TableField("XS_USER")
    private String xsUser;

    /**
     * 销售组织
     */
    @ApiModelProperty("销售组织(50)")
    @TableField("XS_NODE")
    private String xsNode;

    /**
     * 结算员
     */
    @ApiModelProperty("结算员(50)")
    @TableField("JS_USER")
    private String jsUser;

    /**
     * 结算组织
     */
    @ApiModelProperty("结算组织(50)")
    @TableField("JS_NODE")
    private String jsNode;

    /**
     * 协作方案编号
     */
    @ApiModelProperty("协作方案编号(36)")
    @TableField("XZFA_NO")
    private String xzfaNo;

    /**
     * 状态
     */
    @DictField(queryKey = "orderStatusKey")
    @ApiModelProperty("状态(20)")
    @TableField("STATUS")
    private String status;

    /**
     * 阶段
     */
    @DictField(queryKey = "OrderStageKey")
    @ApiModelProperty("阶段(20)")
    @TableField("STAGE")
    private String stage;

    /**
     * 作业数据齐全标志（源数据齐全）
     */
    @ApiModelProperty("作业数据齐全标志（源数据齐全）(1)")
    @TableField("DATA_OK")
    private String dataOk;

    /**
     * 结算完成标记（应收齐全）
     */
    @ApiModelProperty("结算完成标记（应收齐全）(1)")
    @TableField("ARAP_OK")
    private String arapOk;

    /**
     * 退单/作废标志
     */
    @ApiModelProperty("退单/作废标志(1)")
    @TableField("CANCEL_FLAG")
    private String cancelFlag;

    /**
     * 退单/作废原因
     */
    @ApiModelProperty("退单/作废原因(500)")
    @TableField("CANCEL_REASON")
    private String cancelReason;

    /**
     * 业务备注
     */
    @ApiModelProperty("业务备注(1,000)")
    @TableField("BIZ_MEMO")
    private String bizMemo;

    /**
     * 结算备注
     */
    @ApiModelProperty("结算备注(500)")
    @TableField("ACCOUNT_MEMO")
    private String accountMemo;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
     * 创建日期
     */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist = false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist = false)
    private Date createDateEnd;

    /**
     * 最后修改人
     */
    @ApiModelProperty("最后修改人(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
     * 最后修改人
     */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
     * 最后修改日期
     */
    @ApiModelProperty("最后修改日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("最后修改日期开始")
    @TableField(exist = false)
    private Date updateDateStart;

    @ApiModelProperty("最后修改日期结束")
    @TableField(exist = false)
    private Date updateDateEnd;

    /**
     * 归属公司
     */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
     * 归属公司
     */
    @ApiModelProperty("归属公司(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
     * 归属组织
     */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
     * 归属组织
     */
    @ApiModelProperty("归属组织(200)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
     * 归属集团
     */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
     * 归属集团
     */
    @ApiModelProperty("归属集团(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
     * 要求完成时间
     */
    @ApiModelProperty("要求完成时间(7)")
    @TableField("REQUEST_OK_DATE")
    private Date requestOkDate;

    @ApiModelProperty("要求完成时间开始")
    @TableField(exist = false)
    private Date requestOkDateStart;

    @ApiModelProperty("要求完成时间结束")
    @TableField(exist = false)
    private Date requestOkDateEnd;

    /**
     * 服务类型，多个逗号分隔
     */
    @ApiModelProperty("服务类型，多个逗号分隔(100)")
    @TableField("FWLX_CODE")
    private String fwlxCode;

    /**
     * 分发人
     */
    @ApiModelProperty("分发人(20)")
    @TableField("SEND_USER")
    private String sendUser;

    /**
     * 分发时间
     */
    @ApiModelProperty("分发时间(7)")
    @TableField("SEND_DATE")
    private Date sendDate;

    @ApiModelProperty("分发时间开始")
    @TableField(exist = false)
    private Date sendDateStart;

    @ApiModelProperty("分发时间结束")
    @TableField(exist = false)
    private Date sendDateEnd;

    /**
     * 分发组织
     */
    @ApiModelProperty("分发组织(36)")
    @TableField("SEND_NODE")
    private String sendNode;

    /**
     * 分发组织
     */
    @ApiModelProperty("分发组织(200)")
    @TableField("SEND_NODE_NAME")
    private String sendNodeName;

    /**
     * 审核状态
     */
    @ApiModelProperty("审核状态(10)")
    @TableField("AUDIT_STATUS")
    private String auditStatus;

    /**
     * 审核人
     */
    @ApiModelProperty("审核人(20)")
    @TableField("AUDIT_USER")
    private String auditUser;

    /**
     * 审核时间
     */
    @ApiModelProperty("审核时间(7)")
    @TableField("AUDIT_DATE")
    private Date auditDate;

    @ApiModelProperty("审核时间开始")
    @TableField(exist = false)
    private Date auditDateStart;

    @ApiModelProperty("审核时间结束")
    @TableField(exist = false)
    private Date auditDateEnd;

    /**
     * 审核组织
     */
    @ApiModelProperty("审核组织(36)")
    @TableField("AUDIT_NODE")
    private String auditNode;

    /**
     * 审核组织
     */
    @ApiModelProperty("审核组织(200)")
    @TableField("AUDIT_NODE_NAME")
    private String auditNodeName;

    /**
     * 是否作废
     */
    @ApiModelProperty("是否作废(1)")
    @TableField("IS_CANCEL")
    private String isCancel;

    /**
     * 作废时间
     */
    @ApiModelProperty("作废时间(7)")
    @TableField("CANCEL_DATE")
    private Date cancelDate;

    @ApiModelProperty("作废时间开始")
    @TableField(exist = false)
    private Date cancelDateStart;

    @ApiModelProperty("作废时间结束")
    @TableField(exist = false)
    private Date cancelDateEnd;

    /**
     * 作废人
     */
    @ApiModelProperty("作废人(20)")
    @TableField("CANCEL_USER")
    private String cancelUser;

    /**
     * 作废人
     */
    @ApiModelProperty("作废人(50)")
    @TableField("CANCEL_USER_NAME")
    private String cancelUserName;

    /**
     * 作废组织
     */
    @ApiModelProperty("作废组织(36)")
    @TableField("CANCEL_NODE")
    private String cancelNode;

    /**
     * 作废组织名称
     */
    @ApiModelProperty("作废组织名称(100)")
    @TableField("CANCEL_NODE_NAME")
    private String cancelNodeName;

    /**
     * 作废原因
     */
    @ApiModelProperty("作废原因(500)")
    @TableField("CANCEL_REMARK")
    private String cancelRemark;

    /**
     * 审核备注
     */
    @ApiModelProperty("审核备注(500)")
    @TableField("AUDIT_MEMO")
    private String auditMemo;

    /**
     * 接单确认日期
     */
    @ApiModelProperty("接单确认日期(7)")
    @TableField("CONFIRM_DATE")
    private Date confirmDate;

    @ApiModelProperty("接单确认日期开始")
    @TableField(exist = false)
    private Date confirmDateStart;

    @ApiModelProperty("接单确认日期结束")
    @TableField(exist = false)
    private Date confirmDateEnd;

    /**
     * 分发人
     */
    @ApiModelProperty("分发人(50)")
    @TableField("SEND_USER_NAME")
    private String sendUserName;

    /**
     * 审核人
     */
    @ApiModelProperty("审核人(50)")
    @TableField("AUDIT_USER_NAME")
    private String auditUserName;

    /**
     * 联系电话
     */
    @ApiModelProperty("联系电话(200)")
    @TableField("TEL")
    private String tel;

    /**
     * 邮箱
     */
    @ApiModelProperty("邮箱(200)")
    @TableField("E_MAIL")
    private String eMail;

    /**
     * 接单确认人
     */
    @ApiModelProperty("接单确认人(20)")
    @TableField("CONFIRM_USER")
    private String confirmUser;

    /**
     * 接单确认人
     */
    @ApiModelProperty("接单确认人(50)")
    @TableField("CONFIRM_USER_NAME")
    private String confirmUserName;

    /**
     * 模板TEMPLATE,普通订单为空,EXCEL,协同订单XT,复制新增COPY
     */
    @ApiModelProperty("模板TEMPLATE,普通订单为空,EXCEL,协同订单XT,复制新增COPY(20)")
    @TableField("FROM_DATA")
    private String fromData;

    /**
     * 服务类型，多个逗号分隔
     */
    @ApiModelProperty("服务类型，多个逗号分隔(200)")
    @TableField("FWLX_NAME")
    private String fwlxName;

    /**
     * 作业完成时间
     */
    @ApiModelProperty("作业完成时间(7)")
    @TableField("OP_COMPLETE_OK_DATE")
    private Date opCompleteOkDate;

    @ApiModelProperty("作业完成时间开始")
    @TableField(exist = false)
    private Date opCompleteOkDateStart;

    @ApiModelProperty("作业完成时间结束")
    @TableField(exist = false)
    private Date opCompleteOkDateEnd;

    /**
     * 作业完成标识
     */
    @ApiModelProperty("作业完成标识(1)")
    @TableField("OP_COMPLETE_OK")
    private String opCompleteOk;

    /**
     * 作业数据齐全时间
     */
    @ApiModelProperty("作业数据齐全时间(7)")
    @TableField("DATA_OK_DATE")
    private Date dataOkDate;

    @ApiModelProperty("作业数据齐全时间开始")
    @TableField(exist = false)
    private Date dataOkDateStart;

    @ApiModelProperty("作业数据齐全时间结束")
    @TableField(exist = false)
    private Date dataOkDateEnd;

    /**
     * 结算完成日期
     */
    @ApiModelProperty("结算完成日期(7)")
    @TableField("ARAP_OK_DATE")
    private Date arapOkDate;

    @ApiModelProperty("结算完成日期开始")
    @TableField(exist = false)
    private Date arapOkDateStart;

    @ApiModelProperty("结算完成日期结束")
    @TableField(exist = false)
    private Date arapOkDateEnd;

    /**
     * 业务数据唯一注册编号
     */
    @ApiModelProperty("业务数据唯一注册编号(50)")
    @TableField("BIZ_REG_ID")
    private String bizRegId;

    /**
     * 应付费用齐全标记
     */
    @ApiModelProperty("应付费用齐全标记(1)")
    @TableField("AP_OK")
    private String apOk;

    /**
     * 应付费用齐全日期
     */
    @ApiModelProperty("应付费用齐全日期(7)")
    @TableField("AP_OK_DATE")
    private Date apOkDate;

    @ApiModelProperty("应付费用齐全日期开始")
    @TableField(exist = false)
    private Date apOkDateStart;

    @ApiModelProperty("应付费用齐全日期结束")
    @TableField(exist = false)
    private Date apOkDateEnd;

    /**
     * 确认接单必填项
     */
    @ApiModelProperty("确认接单必填项(1)")
    @TableField("IS_QRJD")
    private String isQrjd;

    /**
     * 协作分发必填项
     */
    @ApiModelProperty("协作分发必填项(1)")
    @TableField("IS_XZFF")
    private String isXzff;

    /**
     * 应收结算方名称(1~n个)
     */
    @ApiModelProperty("应收结算方名称(1~n个)(500)")
    @TableField("ACCOUNT_CODE_NAME")
    private String accountCodeName;

    /**
     * 本单自动结算
     */
    @ApiModelProperty("本单自动结算(1)")
    @TableField("IS_AUTO_AR")
    private String isAutoAr;

    /**
     * 上级订单编号
     */
    @ApiModelProperty("上级订单编号(50)")
    @TableField("PRE_ORDER_NO")
    private String preOrderNo;

    /**
     * 订单层级关系
     */
    @ApiModelProperty("订单层级关系(100)")
    @TableField("UDF1")
    private String udf1;

    /**
     * UDF2
     */
    @ApiModelProperty("UDF2(100)")
    @TableField("UDF2")
    private String udf2;

    /**
     * UDF3
     */
    @ApiModelProperty("UDF3(100)")
    @TableField("UDF3")
    private String udf3;

    /**
     * UDF4
     */
    @ApiModelProperty("UDF4(100)")
    @TableField("UDF4")
    private String udf4;

    /**
     * UDF5
     */
    @ApiModelProperty("UDF5(100)")
    @TableField("UDF5")
    private String udf5;

    /**
     * UDF6
     */
    @ApiModelProperty("UDF6(50)")
    @TableField("UDF6")
    private String udf6;

    /**
     * BMC是否固化(锦达财务报表用)
     */
    @ApiModelProperty("BMC是否固化(锦达财务报表用)(50)")
    @TableField("UDF7")
    private String udf7;

    /**
     * 综合查询|XD菜单：已通知客户
     */
    @ApiModelProperty("综合查询|XD菜单：已通知客户(20)")
    @TableField("UDF8")
    private String udf8;

    /**
     * 结案时间
     */
    @ApiModelProperty("结案时间(7)")
    @TableField("UDF9")
    private Date udf9;

    @ApiModelProperty("结案时间开始")
    @TableField(exist = false)
    private Date udf9Start;

    @ApiModelProperty("结案时间结束")
    @TableField(exist = false)
    private Date udf9End;

    /**
     * UDF10
     */
    @ApiModelProperty("UDF10(7)")
    @TableField("UDF10")
    private Date udf10;

    @ApiModelProperty("UDF10开始")
    @TableField(exist = false)
    private Date udf10Start;

    @ApiModelProperty("UDF10结束")
    @TableField(exist = false)
    private Date udf10End;

    /**
     * 系统订单批次
     */
    @ApiModelProperty("系统订单批次(20)")
    @TableField("SYS_ORDER_BATCH")
    private String sysOrderBatch;

    /**
     * 订单批次 顺序号
     */
    @ApiModelProperty("订单批次 顺序号(20)")
    @TableField("SEQ_BATCH")
    private String seqBatch;

    /**
     * 预估里程数
     */
    @ApiModelProperty("预估里程数(22)")
    @TableField("YG_MILEAGE")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal ygMileage;

    /**
     * 预估费用
     */
    @ApiModelProperty("预估费用(22)")
    @TableField("YG_COST")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal ygCost;

    /**
     * 发送批次号
     */
    @ApiModelProperty("发送批次号(20)")
    @TableField("BATCH_NUMBER")
    private String batchNumber;

    /**
     * 进出流向
     */
    @ApiModelProperty("进出流向(1)")
    @TableField("IE")
    private String ie;

    /**
     * 是否报关
     */
    @ApiModelProperty("是否报关(1)")
    @TableField("IS_BG")
    private String isBg;

    /**
     * 账册号
     */
    @ApiModelProperty("账册号(50)")
    @TableField("EMS_NO")
    private String emsNo;

    /**
     * YG_MILEAGE_T
     */
    @ApiModelProperty("YG_MILEAGE_T(22)")
    @TableField("YG_MILEAGE_T")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal ygMileageT;

    /**
     * 预估里程数收货
     */
    @ApiModelProperty("预估里程数收货(22)")
    @TableField("YG_MILEAGE_S")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal ygMileageS;

    /**
     * 快递单号
     */
    @ApiModelProperty("快递单号(50)")
    @TableField("EXPRESS_NO")
    private String expressNo;

    /**
     * 车牌号
     */
    @ApiModelProperty("车牌号(50)")
    @TableField("CAR_NO")
    private String carNo;

    /**
     * 司机电话
     */
    @ApiModelProperty("司机电话(50)")
    @TableField("DRIVER_PHONE")
    private String driverPhone;

    /**
     * 总件数
     */
    @ApiModelProperty("总件数(22)")
    @TableField("TOTAL_PIECES")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal totalPieces;

    /**
     * 件数单位
     */
    @ApiModelProperty("件数单位(50)")
    @TableField("PIECE_UNIT")
    private String pieceUnit;

    /**
     * 自送时间
     */
    @ApiModelProperty("自送时间(7)")
    @TableField("ZS_DATE")
    private Date zsDate;

    @ApiModelProperty("自送时间开始")
    @TableField(exist = false)
    private Date zsDateStart;

    @ApiModelProperty("自送时间结束")
    @TableField(exist = false)
    private Date zsDateEnd;

    /**
     * 是否有明细
     */
    @ApiModelProperty("是否有明细(1)")
    @TableField("IS_DETAIL")
    private String isDetail;


    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public OmsOrderEntity() {
        this.setSubClazz(OmsOrderEntity.class);
    }

    public OmsOrderEntity setFwlxItem(String fwlxItem) {
        this.fwlxItem = fwlxItem;
        this.nodifySetFiled("fwlxItem", fwlxItem);
        return this;
    }

    public String getFwlxItem() {
        this.nodifyGetFiled("fwlxItem");
        return fwlxItem;
    }

    public OmsOrderEntity setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        this.nodifySetFiled("orderNo", orderNo);
        return this;
    }

    public String getOrderNo() {
        this.nodifyGetFiled("orderNo");
        return orderNo;
    }

    public OmsOrderEntity setPreNo(String preNo) {
        this.preNo = preNo;
        this.nodifySetFiled("preNo", preNo);
        return this;
    }

    public String getPreNo() {
        this.nodifyGetFiled("preNo");
        return preNo;
    }

    public OmsOrderEntity setConsigneeCode(String consigneeCode) {
        this.consigneeCode = consigneeCode;
        this.nodifySetFiled("consigneeCode", consigneeCode);
        return this;
    }

    public String getConsigneeCode() {
        this.nodifyGetFiled("consigneeCode");
        return consigneeCode;
    }

    public OmsOrderEntity setShipper(String shipper) {
        this.shipper = shipper;
        this.nodifySetFiled("shipper", shipper);
        return this;
    }

    public String getShipper() {
        this.nodifyGetFiled("shipper");
        return shipper;
    }

    public OmsOrderEntity setReceiver(String receiver) {
        this.receiver = receiver;
        this.nodifySetFiled("receiver", receiver);
        return this;
    }

    public String getReceiver() {
        this.nodifyGetFiled("receiver");
        return receiver;
    }

    public OmsOrderEntity setCustomerBu(String customerBu) {
        this.customerBu = customerBu;
        this.nodifySetFiled("customerBu", customerBu);
        return this;
    }

    public String getCustomerBu() {
        this.nodifyGetFiled("customerBu");
        return customerBu;
    }

    public OmsOrderEntity setCustomerOrderNo(String customerOrderNo) {
        this.customerOrderNo = customerOrderNo;
        this.nodifySetFiled("customerOrderNo", customerOrderNo);
        return this;
    }

    public String getCustomerOrderNo() {
        this.nodifyGetFiled("customerOrderNo");
        return customerOrderNo;
    }

    public OmsOrderEntity setOpType(String opType) {
        this.opType = opType;
        this.nodifySetFiled("opType", opType);
        return this;
    }

    public String getOpType() {
        this.nodifyGetFiled("opType");
        return opType;
    }

    public OmsOrderEntity setProductCode(String productCode) {
        this.productCode = productCode;
        this.nodifySetFiled("productCode", productCode);
        return this;
    }

    public String getProductCode() {
        this.nodifyGetFiled("productCode");
        return productCode;
    }

    public OmsOrderEntity setIsJjh(String isJjh) {
        this.isJjh = isJjh;
        this.nodifySetFiled("isJjh", isJjh);
        return this;
    }

    public String getIsJjh() {
        this.nodifyGetFiled("isJjh");
        return isJjh;
    }

    public OmsOrderEntity setOpDate(Date opDate) {
        this.opDate = opDate;
        this.nodifySetFiled("opDate", opDate);
        return this;
    }

    public Date getOpDate() {
        this.nodifyGetFiled("opDate");
        return opDate;
    }

    public OmsOrderEntity setOpDateStart(Date opDateStart) {
        this.opDateStart = opDateStart;
        this.nodifySetFiled("opDateStart", opDateStart);
        return this;
    }

    public Date getOpDateStart() {
        this.nodifyGetFiled("opDateStart");
        return opDateStart;
    }

    public OmsOrderEntity setOpDateEnd(Date opDateEnd) {
        this.opDateEnd = opDateEnd;
        this.nodifySetFiled("opDateEnd", opDateEnd);
        return this;
    }

    public Date getOpDateEnd() {
        this.nodifyGetFiled("opDateEnd");
        return opDateEnd;
    }

    public OmsOrderEntity setFkfaCode(String fkfaCode) {
        this.fkfaCode = fkfaCode;
        this.nodifySetFiled("fkfaCode", fkfaCode);
        return this;
    }

    public String getFkfaCode() {
        this.nodifyGetFiled("fkfaCode");
        return fkfaCode;
    }

    public OmsOrderEntity setAccountCode(String accountCode) {
        this.accountCode = accountCode;
        this.nodifySetFiled("accountCode", accountCode);
        return this;
    }

    public String getAccountCode() {
        this.nodifyGetFiled("accountCode");
        return accountCode;
    }

    public OmsOrderEntity setAccountMode(String accountMode) {
        this.accountMode = accountMode;
        this.nodifySetFiled("accountMode", accountMode);
        return this;
    }

    public String getAccountMode() {
        this.nodifyGetFiled("accountMode");
        return accountMode;
    }

    public OmsOrderEntity setPayMode(String payMode) {
        this.payMode = payMode;
        this.nodifySetFiled("payMode", payMode);
        return this;
    }

    public String getPayMode() {
        this.nodifyGetFiled("payMode");
        return payMode;
    }

    public OmsOrderEntity setJdUser(String jdUser) {
        this.jdUser = jdUser;
        this.nodifySetFiled("jdUser", jdUser);
        return this;
    }

    public String getJdUser() {
        this.nodifyGetFiled("jdUser");
        return jdUser;
    }

    public OmsOrderEntity setJdNode(String jdNode) {
        this.jdNode = jdNode;
        this.nodifySetFiled("jdNode", jdNode);
        return this;
    }

    public String getJdNode() {
        this.nodifyGetFiled("jdNode");
        return jdNode;
    }

    public OmsOrderEntity setJdCompany(String jdCompany) {
        this.jdCompany = jdCompany;
        this.nodifySetFiled("jdCompany", jdCompany);
        return this;
    }

    public String getJdCompany() {
        this.nodifyGetFiled("jdCompany");
        return jdCompany;
    }

    public OmsOrderEntity setXsUser(String xsUser) {
        this.xsUser = xsUser;
        this.nodifySetFiled("xsUser", xsUser);
        return this;
    }

    public String getXsUser() {
        this.nodifyGetFiled("xsUser");
        return xsUser;
    }

    public OmsOrderEntity setXsNode(String xsNode) {
        this.xsNode = xsNode;
        this.nodifySetFiled("xsNode", xsNode);
        return this;
    }

    public String getXsNode() {
        this.nodifyGetFiled("xsNode");
        return xsNode;
    }

    public OmsOrderEntity setJsUser(String jsUser) {
        this.jsUser = jsUser;
        this.nodifySetFiled("jsUser", jsUser);
        return this;
    }

    public String getJsUser() {
        this.nodifyGetFiled("jsUser");
        return jsUser;
    }

    public OmsOrderEntity setJsNode(String jsNode) {
        this.jsNode = jsNode;
        this.nodifySetFiled("jsNode", jsNode);
        return this;
    }

    public String getJsNode() {
        this.nodifyGetFiled("jsNode");
        return jsNode;
    }

    public OmsOrderEntity setXzfaNo(String xzfaNo) {
        this.xzfaNo = xzfaNo;
        this.nodifySetFiled("xzfaNo", xzfaNo);
        return this;
    }

    public String getXzfaNo() {
        this.nodifyGetFiled("xzfaNo");
        return xzfaNo;
    }

    public OmsOrderEntity setStatus(String status) {
        this.status = status;
        this.nodifySetFiled("status", status);
        return this;
    }

    public String getStatus() {
        this.nodifyGetFiled("status");
        return status;
    }

    public OmsOrderEntity setStage(String stage) {
        this.stage = stage;
        this.nodifySetFiled("stage", stage);
        return this;
    }

    public String getStage() {
        this.nodifyGetFiled("stage");
        return stage;
    }

    public OmsOrderEntity setDataOk(String dataOk) {
        this.dataOk = dataOk;
        this.nodifySetFiled("dataOk", dataOk);
        return this;
    }

    public String getDataOk() {
        this.nodifyGetFiled("dataOk");
        return dataOk;
    }

    public OmsOrderEntity setArapOk(String arapOk) {
        this.arapOk = arapOk;
        this.nodifySetFiled("arapOk", arapOk);
        return this;
    }

    public String getArapOk() {
        this.nodifyGetFiled("arapOk");
        return arapOk;
    }

    public OmsOrderEntity setCancelFlag(String cancelFlag) {
        this.cancelFlag = cancelFlag;
        this.nodifySetFiled("cancelFlag", cancelFlag);
        return this;
    }

    public String getCancelFlag() {
        this.nodifyGetFiled("cancelFlag");
        return cancelFlag;
    }

    public OmsOrderEntity setCancelReason(String cancelReason) {
        this.cancelReason = cancelReason;
        this.nodifySetFiled("cancelReason", cancelReason);
        return this;
    }

    public String getCancelReason() {
        this.nodifyGetFiled("cancelReason");
        return cancelReason;
    }

    public OmsOrderEntity setBizMemo(String bizMemo) {
        this.bizMemo = bizMemo;
        this.nodifySetFiled("bizMemo", bizMemo);
        return this;
    }

    public String getBizMemo() {
        this.nodifyGetFiled("bizMemo");
        return bizMemo;
    }

    public OmsOrderEntity setAccountMemo(String accountMemo) {
        this.accountMemo = accountMemo;
        this.nodifySetFiled("accountMemo", accountMemo);
        return this;
    }

    public String getAccountMemo() {
        this.nodifyGetFiled("accountMemo");
        return accountMemo;
    }

    public OmsOrderEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public OmsOrderEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public OmsOrderEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public OmsOrderEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public OmsOrderEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }

    public OmsOrderEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public OmsOrderEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public OmsOrderEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public OmsOrderEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public OmsOrderEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }

    public OmsOrderEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public OmsOrderEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public OmsOrderEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public OmsOrderEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public OmsOrderEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public OmsOrderEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public OmsOrderEntity setRequestOkDate(Date requestOkDate) {
        this.requestOkDate = requestOkDate;
        this.nodifySetFiled("requestOkDate", requestOkDate);
        return this;
    }

    public Date getRequestOkDate() {
        this.nodifyGetFiled("requestOkDate");
        return requestOkDate;
    }

    public OmsOrderEntity setRequestOkDateStart(Date requestOkDateStart) {
        this.requestOkDateStart = requestOkDateStart;
        this.nodifySetFiled("requestOkDateStart", requestOkDateStart);
        return this;
    }

    public Date getRequestOkDateStart() {
        this.nodifyGetFiled("requestOkDateStart");
        return requestOkDateStart;
    }

    public OmsOrderEntity setRequestOkDateEnd(Date requestOkDateEnd) {
        this.requestOkDateEnd = requestOkDateEnd;
        this.nodifySetFiled("requestOkDateEnd", requestOkDateEnd);
        return this;
    }

    public Date getRequestOkDateEnd() {
        this.nodifyGetFiled("requestOkDateEnd");
        return requestOkDateEnd;
    }

    public OmsOrderEntity setFwlxCode(String fwlxCode) {
        this.fwlxCode = fwlxCode;
        this.nodifySetFiled("fwlxCode", fwlxCode);
        return this;
    }

    public String getFwlxCode() {
        this.nodifyGetFiled("fwlxCode");
        return fwlxCode;
    }

    public OmsOrderEntity setSendUser(String sendUser) {
        this.sendUser = sendUser;
        this.nodifySetFiled("sendUser", sendUser);
        return this;
    }

    public String getSendUser() {
        this.nodifyGetFiled("sendUser");
        return sendUser;
    }

    public OmsOrderEntity setSendDate(Date sendDate) {
        this.sendDate = sendDate;
        this.nodifySetFiled("sendDate", sendDate);
        return this;
    }

    public Date getSendDate() {
        this.nodifyGetFiled("sendDate");
        return sendDate;
    }

    public OmsOrderEntity setSendDateStart(Date sendDateStart) {
        this.sendDateStart = sendDateStart;
        this.nodifySetFiled("sendDateStart", sendDateStart);
        return this;
    }

    public Date getSendDateStart() {
        this.nodifyGetFiled("sendDateStart");
        return sendDateStart;
    }

    public OmsOrderEntity setSendDateEnd(Date sendDateEnd) {
        this.sendDateEnd = sendDateEnd;
        this.nodifySetFiled("sendDateEnd", sendDateEnd);
        return this;
    }

    public Date getSendDateEnd() {
        this.nodifyGetFiled("sendDateEnd");
        return sendDateEnd;
    }

    public OmsOrderEntity setSendNode(String sendNode) {
        this.sendNode = sendNode;
        this.nodifySetFiled("sendNode", sendNode);
        return this;
    }

    public String getSendNode() {
        this.nodifyGetFiled("sendNode");
        return sendNode;
    }

    public OmsOrderEntity setSendNodeName(String sendNodeName) {
        this.sendNodeName = sendNodeName;
        this.nodifySetFiled("sendNodeName", sendNodeName);
        return this;
    }

    public String getSendNodeName() {
        this.nodifyGetFiled("sendNodeName");
        return sendNodeName;
    }

    public OmsOrderEntity setAuditStatus(String auditStatus) {
        this.auditStatus = auditStatus;
        this.nodifySetFiled("auditStatus", auditStatus);
        return this;
    }

    public String getAuditStatus() {
        this.nodifyGetFiled("auditStatus");
        return auditStatus;
    }

    public OmsOrderEntity setAuditUser(String auditUser) {
        this.auditUser = auditUser;
        this.nodifySetFiled("auditUser", auditUser);
        return this;
    }

    public String getAuditUser() {
        this.nodifyGetFiled("auditUser");
        return auditUser;
    }

    public OmsOrderEntity setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
        this.nodifySetFiled("auditDate", auditDate);
        return this;
    }

    public Date getAuditDate() {
        this.nodifyGetFiled("auditDate");
        return auditDate;
    }

    public OmsOrderEntity setAuditDateStart(Date auditDateStart) {
        this.auditDateStart = auditDateStart;
        this.nodifySetFiled("auditDateStart", auditDateStart);
        return this;
    }

    public Date getAuditDateStart() {
        this.nodifyGetFiled("auditDateStart");
        return auditDateStart;
    }

    public OmsOrderEntity setAuditDateEnd(Date auditDateEnd) {
        this.auditDateEnd = auditDateEnd;
        this.nodifySetFiled("auditDateEnd", auditDateEnd);
        return this;
    }

    public Date getAuditDateEnd() {
        this.nodifyGetFiled("auditDateEnd");
        return auditDateEnd;
    }

    public OmsOrderEntity setAuditNode(String auditNode) {
        this.auditNode = auditNode;
        this.nodifySetFiled("auditNode", auditNode);
        return this;
    }

    public String getAuditNode() {
        this.nodifyGetFiled("auditNode");
        return auditNode;
    }

    public OmsOrderEntity setAuditNodeName(String auditNodeName) {
        this.auditNodeName = auditNodeName;
        this.nodifySetFiled("auditNodeName", auditNodeName);
        return this;
    }

    public String getAuditNodeName() {
        this.nodifyGetFiled("auditNodeName");
        return auditNodeName;
    }

    public OmsOrderEntity setIsCancel(String isCancel) {
        this.isCancel = isCancel;
        this.nodifySetFiled("isCancel", isCancel);
        return this;
    }

    public String getIsCancel() {
        this.nodifyGetFiled("isCancel");
        return isCancel;
    }

    public OmsOrderEntity setCancelDate(Date cancelDate) {
        this.cancelDate = cancelDate;
        this.nodifySetFiled("cancelDate", cancelDate);
        return this;
    }

    public Date getCancelDate() {
        this.nodifyGetFiled("cancelDate");
        return cancelDate;
    }

    public OmsOrderEntity setCancelDateStart(Date cancelDateStart) {
        this.cancelDateStart = cancelDateStart;
        this.nodifySetFiled("cancelDateStart", cancelDateStart);
        return this;
    }

    public Date getCancelDateStart() {
        this.nodifyGetFiled("cancelDateStart");
        return cancelDateStart;
    }

    public OmsOrderEntity setCancelDateEnd(Date cancelDateEnd) {
        this.cancelDateEnd = cancelDateEnd;
        this.nodifySetFiled("cancelDateEnd", cancelDateEnd);
        return this;
    }

    public Date getCancelDateEnd() {
        this.nodifyGetFiled("cancelDateEnd");
        return cancelDateEnd;
    }

    public OmsOrderEntity setCancelUser(String cancelUser) {
        this.cancelUser = cancelUser;
        this.nodifySetFiled("cancelUser", cancelUser);
        return this;
    }

    public String getCancelUser() {
        this.nodifyGetFiled("cancelUser");
        return cancelUser;
    }

    public OmsOrderEntity setCancelUserName(String cancelUserName) {
        this.cancelUserName = cancelUserName;
        this.nodifySetFiled("cancelUserName", cancelUserName);
        return this;
    }

    public String getCancelUserName() {
        this.nodifyGetFiled("cancelUserName");
        return cancelUserName;
    }

    public OmsOrderEntity setCancelNode(String cancelNode) {
        this.cancelNode = cancelNode;
        this.nodifySetFiled("cancelNode", cancelNode);
        return this;
    }

    public String getCancelNode() {
        this.nodifyGetFiled("cancelNode");
        return cancelNode;
    }

    public OmsOrderEntity setCancelNodeName(String cancelNodeName) {
        this.cancelNodeName = cancelNodeName;
        this.nodifySetFiled("cancelNodeName", cancelNodeName);
        return this;
    }

    public String getCancelNodeName() {
        this.nodifyGetFiled("cancelNodeName");
        return cancelNodeName;
    }

    public OmsOrderEntity setCancelRemark(String cancelRemark) {
        this.cancelRemark = cancelRemark;
        this.nodifySetFiled("cancelRemark", cancelRemark);
        return this;
    }

    public String getCancelRemark() {
        this.nodifyGetFiled("cancelRemark");
        return cancelRemark;
    }

    public OmsOrderEntity setAuditMemo(String auditMemo) {
        this.auditMemo = auditMemo;
        this.nodifySetFiled("auditMemo", auditMemo);
        return this;
    }

    public String getAuditMemo() {
        this.nodifyGetFiled("auditMemo");
        return auditMemo;
    }

    public OmsOrderEntity setConfirmDate(Date confirmDate) {
        this.confirmDate = confirmDate;
        this.nodifySetFiled("confirmDate", confirmDate);
        return this;
    }

    public Date getConfirmDate() {
        this.nodifyGetFiled("confirmDate");
        return confirmDate;
    }

    public OmsOrderEntity setConfirmDateStart(Date confirmDateStart) {
        this.confirmDateStart = confirmDateStart;
        this.nodifySetFiled("confirmDateStart", confirmDateStart);
        return this;
    }

    public Date getConfirmDateStart() {
        this.nodifyGetFiled("confirmDateStart");
        return confirmDateStart;
    }

    public OmsOrderEntity setConfirmDateEnd(Date confirmDateEnd) {
        this.confirmDateEnd = confirmDateEnd;
        this.nodifySetFiled("confirmDateEnd", confirmDateEnd);
        return this;
    }

    public Date getConfirmDateEnd() {
        this.nodifyGetFiled("confirmDateEnd");
        return confirmDateEnd;
    }

    public OmsOrderEntity setSendUserName(String sendUserName) {
        this.sendUserName = sendUserName;
        this.nodifySetFiled("sendUserName", sendUserName);
        return this;
    }

    public String getSendUserName() {
        this.nodifyGetFiled("sendUserName");
        return sendUserName;
    }

    public OmsOrderEntity setAuditUserName(String auditUserName) {
        this.auditUserName = auditUserName;
        this.nodifySetFiled("auditUserName", auditUserName);
        return this;
    }

    public String getAuditUserName() {
        this.nodifyGetFiled("auditUserName");
        return auditUserName;
    }

    public OmsOrderEntity setTel(String tel) {
        this.tel = tel;
        this.nodifySetFiled("tel", tel);
        return this;
    }

    public String getTel() {
        this.nodifyGetFiled("tel");
        return tel;
    }

    public OmsOrderEntity seteMail(String eMail) {
        this.eMail = eMail;
        this.nodifySetFiled("eMail", eMail);
        return this;
    }

    public String geteMail() {
        this.nodifyGetFiled("eMail");
        return eMail;
    }

    public OmsOrderEntity setConfirmUser(String confirmUser) {
        this.confirmUser = confirmUser;
        this.nodifySetFiled("confirmUser", confirmUser);
        return this;
    }

    public String getConfirmUser() {
        this.nodifyGetFiled("confirmUser");
        return confirmUser;
    }

    public OmsOrderEntity setConfirmUserName(String confirmUserName) {
        this.confirmUserName = confirmUserName;
        this.nodifySetFiled("confirmUserName", confirmUserName);
        return this;
    }

    public String getConfirmUserName() {
        this.nodifyGetFiled("confirmUserName");
        return confirmUserName;
    }

    public OmsOrderEntity setFromData(String fromData) {
        this.fromData = fromData;
        this.nodifySetFiled("fromData", fromData);
        return this;
    }

    public String getFromData() {
        this.nodifyGetFiled("fromData");
        return fromData;
    }

    public OmsOrderEntity setFwlxName(String fwlxName) {
        this.fwlxName = fwlxName;
        this.nodifySetFiled("fwlxName", fwlxName);
        return this;
    }

    public String getFwlxName() {
        this.nodifyGetFiled("fwlxName");
        return fwlxName;
    }

    public OmsOrderEntity setOpCompleteOkDate(Date opCompleteOkDate) {
        this.opCompleteOkDate = opCompleteOkDate;
        this.nodifySetFiled("opCompleteOkDate", opCompleteOkDate);
        return this;
    }

    public Date getOpCompleteOkDate() {
        this.nodifyGetFiled("opCompleteOkDate");
        return opCompleteOkDate;
    }

    public OmsOrderEntity setOpCompleteOkDateStart(Date opCompleteOkDateStart) {
        this.opCompleteOkDateStart = opCompleteOkDateStart;
        this.nodifySetFiled("opCompleteOkDateStart", opCompleteOkDateStart);
        return this;
    }

    public Date getOpCompleteOkDateStart() {
        this.nodifyGetFiled("opCompleteOkDateStart");
        return opCompleteOkDateStart;
    }

    public OmsOrderEntity setOpCompleteOkDateEnd(Date opCompleteOkDateEnd) {
        this.opCompleteOkDateEnd = opCompleteOkDateEnd;
        this.nodifySetFiled("opCompleteOkDateEnd", opCompleteOkDateEnd);
        return this;
    }

    public Date getOpCompleteOkDateEnd() {
        this.nodifyGetFiled("opCompleteOkDateEnd");
        return opCompleteOkDateEnd;
    }

    public OmsOrderEntity setOpCompleteOk(String opCompleteOk) {
        this.opCompleteOk = opCompleteOk;
        this.nodifySetFiled("opCompleteOk", opCompleteOk);
        return this;
    }

    public String getOpCompleteOk() {
        this.nodifyGetFiled("opCompleteOk");
        return opCompleteOk;
    }

    public OmsOrderEntity setDataOkDate(Date dataOkDate) {
        this.dataOkDate = dataOkDate;
        this.nodifySetFiled("dataOkDate", dataOkDate);
        return this;
    }

    public Date getDataOkDate() {
        this.nodifyGetFiled("dataOkDate");
        return dataOkDate;
    }

    public OmsOrderEntity setDataOkDateStart(Date dataOkDateStart) {
        this.dataOkDateStart = dataOkDateStart;
        this.nodifySetFiled("dataOkDateStart", dataOkDateStart);
        return this;
    }

    public Date getDataOkDateStart() {
        this.nodifyGetFiled("dataOkDateStart");
        return dataOkDateStart;
    }

    public OmsOrderEntity setDataOkDateEnd(Date dataOkDateEnd) {
        this.dataOkDateEnd = dataOkDateEnd;
        this.nodifySetFiled("dataOkDateEnd", dataOkDateEnd);
        return this;
    }

    public Date getDataOkDateEnd() {
        this.nodifyGetFiled("dataOkDateEnd");
        return dataOkDateEnd;
    }

    public OmsOrderEntity setArapOkDate(Date arapOkDate) {
        this.arapOkDate = arapOkDate;
        this.nodifySetFiled("arapOkDate", arapOkDate);
        return this;
    }

    public Date getArapOkDate() {
        this.nodifyGetFiled("arapOkDate");
        return arapOkDate;
    }

    public OmsOrderEntity setArapOkDateStart(Date arapOkDateStart) {
        this.arapOkDateStart = arapOkDateStart;
        this.nodifySetFiled("arapOkDateStart", arapOkDateStart);
        return this;
    }

    public Date getArapOkDateStart() {
        this.nodifyGetFiled("arapOkDateStart");
        return arapOkDateStart;
    }

    public OmsOrderEntity setArapOkDateEnd(Date arapOkDateEnd) {
        this.arapOkDateEnd = arapOkDateEnd;
        this.nodifySetFiled("arapOkDateEnd", arapOkDateEnd);
        return this;
    }

    public Date getArapOkDateEnd() {
        this.nodifyGetFiled("arapOkDateEnd");
        return arapOkDateEnd;
    }

    public OmsOrderEntity setBizRegId(String bizRegId) {
        this.bizRegId = bizRegId;
        this.nodifySetFiled("bizRegId", bizRegId);
        return this;
    }

    public String getBizRegId() {
        this.nodifyGetFiled("bizRegId");
        return bizRegId;
    }

    public OmsOrderEntity setApOk(String apOk) {
        this.apOk = apOk;
        this.nodifySetFiled("apOk", apOk);
        return this;
    }

    public String getApOk() {
        this.nodifyGetFiled("apOk");
        return apOk;
    }

    public OmsOrderEntity setApOkDate(Date apOkDate) {
        this.apOkDate = apOkDate;
        this.nodifySetFiled("apOkDate", apOkDate);
        return this;
    }

    public Date getApOkDate() {
        this.nodifyGetFiled("apOkDate");
        return apOkDate;
    }

    public OmsOrderEntity setApOkDateStart(Date apOkDateStart) {
        this.apOkDateStart = apOkDateStart;
        this.nodifySetFiled("apOkDateStart", apOkDateStart);
        return this;
    }

    public Date getApOkDateStart() {
        this.nodifyGetFiled("apOkDateStart");
        return apOkDateStart;
    }

    public OmsOrderEntity setApOkDateEnd(Date apOkDateEnd) {
        this.apOkDateEnd = apOkDateEnd;
        this.nodifySetFiled("apOkDateEnd", apOkDateEnd);
        return this;
    }

    public Date getApOkDateEnd() {
        this.nodifyGetFiled("apOkDateEnd");
        return apOkDateEnd;
    }

    public OmsOrderEntity setIsQrjd(String isQrjd) {
        this.isQrjd = isQrjd;
        this.nodifySetFiled("isQrjd", isQrjd);
        return this;
    }

    public String getIsQrjd() {
        this.nodifyGetFiled("isQrjd");
        return isQrjd;
    }

    public OmsOrderEntity setIsXzff(String isXzff) {
        this.isXzff = isXzff;
        this.nodifySetFiled("isXzff", isXzff);
        return this;
    }

    public String getIsXzff() {
        this.nodifyGetFiled("isXzff");
        return isXzff;
    }

    public OmsOrderEntity setAccountCodeName(String accountCodeName) {
        this.accountCodeName = accountCodeName;
        this.nodifySetFiled("accountCodeName", accountCodeName);
        return this;
    }

    public String getAccountCodeName() {
        this.nodifyGetFiled("accountCodeName");
        return accountCodeName;
    }

    public OmsOrderEntity setIsAutoAr(String isAutoAr) {
        this.isAutoAr = isAutoAr;
        this.nodifySetFiled("isAutoAr", isAutoAr);
        return this;
    }

    public String getIsAutoAr() {
        this.nodifyGetFiled("isAutoAr");
        return isAutoAr;
    }

    public OmsOrderEntity setPreOrderNo(String preOrderNo) {
        this.preOrderNo = preOrderNo;
        this.nodifySetFiled("preOrderNo", preOrderNo);
        return this;
    }

    public String getPreOrderNo() {
        this.nodifyGetFiled("preOrderNo");
        return preOrderNo;
    }

    public OmsOrderEntity setUdf1(String udf1) {
        this.udf1 = udf1;
        this.nodifySetFiled("udf1", udf1);
        return this;
    }

    public String getUdf1() {
        this.nodifyGetFiled("udf1");
        return udf1;
    }

    public OmsOrderEntity setUdf2(String udf2) {
        this.udf2 = udf2;
        this.nodifySetFiled("udf2", udf2);
        return this;
    }

    public String getUdf2() {
        this.nodifyGetFiled("udf2");
        return udf2;
    }

    public OmsOrderEntity setUdf3(String udf3) {
        this.udf3 = udf3;
        this.nodifySetFiled("udf3", udf3);
        return this;
    }

    public String getUdf3() {
        this.nodifyGetFiled("udf3");
        return udf3;
    }

    public OmsOrderEntity setUdf4(String udf4) {
        this.udf4 = udf4;
        this.nodifySetFiled("udf4", udf4);
        return this;
    }

    public String getUdf4() {
        this.nodifyGetFiled("udf4");
        return udf4;
    }

    public OmsOrderEntity setUdf5(String udf5) {
        this.udf5 = udf5;
        this.nodifySetFiled("udf5", udf5);
        return this;
    }

    public String getUdf5() {
        this.nodifyGetFiled("udf5");
        return udf5;
    }

    public OmsOrderEntity setUdf6(String udf6) {
        this.udf6 = udf6;
        this.nodifySetFiled("udf6", udf6);
        return this;
    }

    public String getUdf6() {
        this.nodifyGetFiled("udf6");
        return udf6;
    }

    public OmsOrderEntity setUdf7(String udf7) {
        this.udf7 = udf7;
        this.nodifySetFiled("udf7", udf7);
        return this;
    }

    public String getUdf7() {
        this.nodifyGetFiled("udf7");
        return udf7;
    }

    public OmsOrderEntity setUdf8(String udf8) {
        this.udf8 = udf8;
        this.nodifySetFiled("udf8", udf8);
        return this;
    }

    public String getUdf8() {
        this.nodifyGetFiled("udf8");
        return udf8;
    }

    public OmsOrderEntity setUdf9(Date udf9) {
        this.udf9 = udf9;
        this.nodifySetFiled("udf9", udf9);
        return this;
    }

    public Date getUdf9() {
        this.nodifyGetFiled("udf9");
        return udf9;
    }

    public OmsOrderEntity setUdf9Start(Date udf9Start) {
        this.udf9Start = udf9Start;
        this.nodifySetFiled("udf9Start", udf9Start);
        return this;
    }

    public Date getUdf9Start() {
        this.nodifyGetFiled("udf9Start");
        return udf9Start;
    }

    public OmsOrderEntity setUdf9End(Date udf9End) {
        this.udf9End = udf9End;
        this.nodifySetFiled("udf9End", udf9End);
        return this;
    }

    public Date getUdf9End() {
        this.nodifyGetFiled("udf9End");
        return udf9End;
    }

    public OmsOrderEntity setUdf10(Date udf10) {
        this.udf10 = udf10;
        this.nodifySetFiled("udf10", udf10);
        return this;
    }

    public Date getUdf10() {
        this.nodifyGetFiled("udf10");
        return udf10;
    }

    public OmsOrderEntity setUdf10Start(Date udf10Start) {
        this.udf10Start = udf10Start;
        this.nodifySetFiled("udf10Start", udf10Start);
        return this;
    }

    public Date getUdf10Start() {
        this.nodifyGetFiled("udf10Start");
        return udf10Start;
    }

    public OmsOrderEntity setUdf10End(Date udf10End) {
        this.udf10End = udf10End;
        this.nodifySetFiled("udf10End", udf10End);
        return this;
    }

    public Date getUdf10End() {
        this.nodifyGetFiled("udf10End");
        return udf10End;
    }

    public OmsOrderEntity setSysOrderBatch(String sysOrderBatch) {
        this.sysOrderBatch = sysOrderBatch;
        this.nodifySetFiled("sysOrderBatch", sysOrderBatch);
        return this;
    }

    public String getSysOrderBatch() {
        this.nodifyGetFiled("sysOrderBatch");
        return sysOrderBatch;
    }

    public OmsOrderEntity setSeqBatch(String seqBatch) {
        this.seqBatch = seqBatch;
        this.nodifySetFiled("seqBatch", seqBatch);
        return this;
    }

    public String getSeqBatch() {
        this.nodifyGetFiled("seqBatch");
        return seqBatch;
    }

    public OmsOrderEntity setYgMileage(BigDecimal ygMileage) {
        this.ygMileage = ygMileage;
        this.nodifySetFiled("ygMileage", ygMileage);
        return this;
    }

    public BigDecimal getYgMileage() {
        this.nodifyGetFiled("ygMileage");
        return ygMileage;
    }

    public OmsOrderEntity setYgCost(BigDecimal ygCost) {
        this.ygCost = ygCost;
        this.nodifySetFiled("ygCost", ygCost);
        return this;
    }

    public BigDecimal getYgCost() {
        this.nodifyGetFiled("ygCost");
        return ygCost;
    }

    public OmsOrderEntity setBatchNumber(String batchNumber) {
        this.batchNumber = batchNumber;
        this.nodifySetFiled("batchNumber", batchNumber);
        return this;
    }

    public String getBatchNumber() {
        this.nodifyGetFiled("batchNumber");
        return batchNumber;
    }

    public OmsOrderEntity setIe(String ie) {
        this.ie = ie;
        this.nodifySetFiled("ie", ie);
        return this;
    }

    public String getIe() {
        this.nodifyGetFiled("ie");
        return ie;
    }

    public OmsOrderEntity setIsBg(String isBg) {
        this.isBg = isBg;
        this.nodifySetFiled("isBg", isBg);
        return this;
    }

    public String getIsBg() {
        this.nodifyGetFiled("isBg");
        return isBg;
    }

    public OmsOrderEntity setEmsNo(String emsNo) {
        this.emsNo = emsNo;
        this.nodifySetFiled("emsNo", emsNo);
        return this;
    }

    public String getEmsNo() {
        this.nodifyGetFiled("emsNo");
        return emsNo;
    }

    public OmsOrderEntity setYgMileageT(BigDecimal ygMileageT) {
        this.ygMileageT = ygMileageT;
        this.nodifySetFiled("ygMileageT", ygMileageT);
        return this;
    }

    public BigDecimal getYgMileageT() {
        this.nodifyGetFiled("ygMileageT");
        return ygMileageT;
    }

    public OmsOrderEntity setYgMileageS(BigDecimal ygMileageS) {
        this.ygMileageS = ygMileageS;
        this.nodifySetFiled("ygMileageS", ygMileageS);
        return this;
    }

    public BigDecimal getYgMileageS() {
        this.nodifyGetFiled("ygMileageS");
        return ygMileageS;
    }

    public OmsOrderEntity setExpressNo(String expressNo) {
        this.expressNo = expressNo;
        this.nodifySetFiled("expressNo", expressNo);
        return this;
    }

    public String getExpressNo() {
        this.nodifyGetFiled("expressNo");
        return expressNo;
    }

    public OmsOrderEntity setCarNo(String carNo) {
        this.carNo = carNo;
        this.nodifySetFiled("carNo", carNo);
        return this;
    }

    public String getCarNo() {
        this.nodifyGetFiled("carNo");
        return carNo;
    }

    public OmsOrderEntity setDriverPhone(String driverPhone) {
        this.driverPhone = driverPhone;
        this.nodifySetFiled("driverPhone", driverPhone);
        return this;
    }

    public String getDriverPhone() {
        this.nodifyGetFiled("driverPhone");
        return driverPhone;
    }

    public OmsOrderEntity setTotalPieces(BigDecimal totalPieces) {
        this.totalPieces = totalPieces;
        this.nodifySetFiled("totalPieces", totalPieces);
        return this;
    }

    public BigDecimal getTotalPieces() {
        this.nodifyGetFiled("totalPieces");
        return totalPieces;
    }

    public OmsOrderEntity setPieceUnit(String pieceUnit) {
        this.pieceUnit = pieceUnit;
        this.nodifySetFiled("pieceUnit", pieceUnit);
        return this;
    }

    public String getPieceUnit() {
        this.nodifyGetFiled("pieceUnit");
        return pieceUnit;
    }

    public OmsOrderEntity setZsDate(Date zsDate) {
        this.zsDate = zsDate;
        this.nodifySetFiled("zsDate", zsDate);
        return this;
    }

    public Date getZsDate() {
        this.nodifyGetFiled("zsDate");
        return zsDate;
    }

    public OmsOrderEntity setZsDateStart(Date zsDateStart) {
        this.zsDateStart = zsDateStart;
        this.nodifySetFiled("zsDateStart", zsDateStart);
        return this;
    }

    public Date getZsDateStart() {
        this.nodifyGetFiled("zsDateStart");
        return zsDateStart;
    }

    public OmsOrderEntity setZsDateEnd(Date zsDateEnd) {
        this.zsDateEnd = zsDateEnd;
        this.nodifySetFiled("zsDateEnd", zsDateEnd);
        return this;
    }

    public Date getZsDateEnd() {
        this.nodifyGetFiled("zsDateEnd");
        return zsDateEnd;
    }

    public OmsOrderEntity setIsDetail(String isDetail) {
        this.isDetail = isDetail;
        this.nodifySetFiled("isDetail", isDetail);
        return this;
    }

    public String getIsDetail() {
        this.nodifyGetFiled("isDetail");
        return isDetail;
    }

}
