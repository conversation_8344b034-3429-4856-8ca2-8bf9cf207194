package com.eci.project.omsISlResult.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.omsISlResult.dao.OmsISlResultDao;
import com.eci.project.omsISlResult.entity.OmsISlResultEntity;
import com.eci.project.omsISlResult.validate.OmsISlResultVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 受理结果及单据编号Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-21
*/
@Service
@Slf4j
public class OmsISlResultService implements EciBaseService<OmsISlResultEntity> {

    @Autowired
    private OmsISlResultDao omsISlResultDao;

    @Autowired
    private OmsISlResultVal omsISlResultVal;


    @Override
    public TgPageInfo queryPageList(OmsISlResultEntity entity) {
        EciQuery<OmsISlResultEntity> eciQuery = EciQuery.buildQuery(entity);
        List<OmsISlResultEntity> entities = omsISlResultDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public OmsISlResultEntity save(OmsISlResultEntity entity) {
        // 返回实体对象
        OmsISlResultEntity omsISlResultEntity = null;
        omsISlResultVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            omsISlResultEntity = omsISlResultDao.insertOne(entity);

        }else{

            omsISlResultEntity = omsISlResultDao.updateByEntityId(entity);

        }
        return omsISlResultEntity;
    }

    @Override
    public List<OmsISlResultEntity> selectList(OmsISlResultEntity entity) {
        return omsISlResultDao.selectList(entity);
    }

    @Override
    public OmsISlResultEntity selectOneById(Serializable id) {
        return omsISlResultDao.selectById(id);
    }


    @Override
    public void insertBatch(List<OmsISlResultEntity> list) {
        omsISlResultDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return omsISlResultDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return omsISlResultDao.deleteById(id);
    }

}