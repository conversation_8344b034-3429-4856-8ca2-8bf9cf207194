package com.eci.project.etmsBdTruckCertificateQz.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.etmsBdTruckCertificateQz.entity.EtmsBdTruckCertificateQzEntity;


/**
* 证件管理Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-04-27
*/
public interface EtmsBdTruckCertificateQzDao extends EciBaseDao<EtmsBdTruckCertificateQzEntity> {

}