package com.eci.project.fzgjCrmEnterpriseApi.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.fzgjCrmEnterpriseApi.entity.FzgjCrmEnterpriseApiEntity;


/**
* 注册企业发送接口Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-03-13
*/
public interface FzgjCrmEnterpriseApiDao extends EciBaseDao<FzgjCrmEnterpriseApiEntity> {

}