package com.eci.common;

import com.eci.common.db.DBHelper;
import com.eci.wu.core.DataTable;
import com.eci.wu.core.EntityBase;

import javax.swing.*;
import java.io.InputStream;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

public class DBHelperX {

    /**
     * 执行查询
     *
     * @param dsName 数据源名称
     * @param sql    查询sql
     * @return 数据集
     */
    private static DataTable executeQuery(String dsName, String sql) {
        try {
            Log.Write(sql, dsName);

            DBHelper.changeDs(dsName); // 切换库
            DataTable dataTable = DBHelper.getDataTable(sql);
            return dataTable;
        } catch (Exception ex) {
            Log.Error(ex.getMessage(), ex);
            throw new RuntimeException("Database operation failed: " + ex.getMessage(), ex);
        } finally {
            DBHelper.clearDs(); // 切换到主库
        }
    }

    /**
     * 执行更新
     *
     * @param dsName 数据源名称
     * @param sql    执行语句
     * @return 影响行数
     */
    private static int executeUpdate(String dsName, String sql) {
        try {
            Log.Write(sql, dsName);

            DBHelper.changeDs(dsName); // 切换库
            DBHelper.execute(sql);
            return 1;
        } catch (Exception ex) {
            Log.Error(ex.getMessage(), ex);
            return 0;
        } finally {
            DBHelper.clearDs(); // 切换到主库
        }
    }

    //
    // 摘要:
    //     判断数据重复, 如果重复则抛出异常Message,否则返回False
    //
    // 参数:
    //   sql:
    //
    //   compareKey:
    //
    //   message:
    public static boolean checkRepeat(String sql, String columnName, String compareKey, String message) {
        boolean flag = false;

        try {
            List<EntityBase> list = DBHelper.selectList(sql, EntityBase.class);
            if (list != null && list.size() > 0) {
                EntityBase entity = list.get(0);
                String a = entity.getString(columnName);
                flag = a.equals(compareKey);
            }

        } catch (Exception ex) {
            Log.Error(ex.getMessage(), ex);
            throw new RuntimeException("checkRepeat failed: " + ex.getMessage(), ex);
        }

        if (flag) {
            throw new RuntimeException(message);
        }

        return false;
    }

    public static class GetPortalDB {
        private final static String dsName = "portal";

        public static DataTable GetDataTable(String sql) {
            return executeQuery(dsName, sql);
        }

        public static DataTable getDataTable(String sql) {
            return executeQuery(dsName, sql);
        }

        public static int Execute(String sql) {
            return executeUpdate(dsName, sql);
        }

        public static int execute(String sql) {
            return executeUpdate(dsName, sql);
        }
    }

    public static class GetShareQueryDB {
        private final static String dsName = "shareQuery";

        public static DataTable GetDataTable(String sql) {
            return executeQuery(dsName, sql);
        }

        public static DataTable getDataTable(String sql) {
            return executeQuery(dsName, sql);
        }

        public static int Execute(String sql) {
            return executeUpdate(dsName, sql);
        }

        public static int execute(String sql) {
            return executeUpdate(dsName, sql);
        }
    }

}
