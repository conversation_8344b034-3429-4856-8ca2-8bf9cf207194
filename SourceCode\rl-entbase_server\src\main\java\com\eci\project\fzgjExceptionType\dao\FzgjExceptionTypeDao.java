package com.eci.project.fzgjExceptionType.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.fzgjExceptionType.entity.FzgjExceptionTypeEntity;


/**
* 异常类型Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-04-02
*/
public interface FzgjExceptionTypeDao extends EciBaseDao<FzgjExceptionTypeEntity> {

}