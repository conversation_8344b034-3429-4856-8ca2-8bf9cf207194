<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.omsOrderFwxmWork.dao.OmsOrderFwxmWorkDao">
    <resultMap type="OmsOrderFwxmWorkEntity" id="OmsOrderFwxmWorkResult">
        <result property="workNo" column="WORK_NO"/>
        <result property="orderNo" column="ORDER_NO"/>
        <result property="preNo" column="PRE_NO"/>
        <result property="fwxmCode" column="FWXM_CODE"/>
        <result property="taskSeq" column="TASK_SEQ"/>
        <result property="taskPre" column="TASK_PRE"/>
        <result property="isWb" column="IS_WB"/>
        <result property="gysCode" column="GYS_CODE"/>
        <result property="nodeCodeNb" column="NODE_CODE_NB"/>
        <result property="quoteCode" column="QUOTE_CODE"/>
        <result property="status" column="STATUS"/>
        <result property="stage" column="STAGE"/>
        <result property="sysCode" column="SYS_CODE"/>
        <result property="billCode" column="BILL_CODE"/>
        <result property="docNo" column="DOC_NO"/>
        <result property="responseCode" column="RESPONSE_CODE"/>
        <result property="sendDate" column="SEND_DATE"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="guid" column="GUID"/>
        <result property="sendFlag" column="SEND_FLAG"/>
        <result property="sendUser" column="SEND_USER"/>
        <result property="sendNodeCode" column="SEND_NODE_CODE"/>
        <result property="sendNodeName" column="SEND_NODE_NAME"/>
        <result property="sendUserName" column="SEND_USER_NAME"/>
        <result property="opCompleteOkDate" column="OP_COMPLETE_OK_DATE"/>
        <result property="opCompleteOk" column="OP_COMPLETE_OK"/>
        <result property="dataOkDate" column="DATA_OK_DATE"/>
        <result property="dataOk" column="DATA_OK"/>
        <result property="arapOk" column="ARAP_OK"/>
        <result property="arapOkDate" column="ARAP_OK_DATE"/>
        <result property="xzwtNo" column="XZWT_NO"/>
        <result property="fwlxCode" column="FWLX_CODE"/>
        <result property="bizRegId" column="BIZ_REG_ID"/>
        <result property="preWorkNo" column="PRE_WORK_NO"/>
        <result property="isAp" column="IS_AP"/>
        <result property="udf1" column="UDF1"/>
        <result property="udf2" column="UDF2"/>
        <result property="udf3" column="UDF3"/>
        <result property="udf4" column="UDF4"/>
        <result property="udf5" column="UDF5"/>
        <result property="udf6" column="UDF6"/>
        <result property="udf7" column="UDF7"/>
        <result property="udf8" column="UDF8"/>
        <result property="udf9" column="UDF9"/>
        <result property="udf10" column="UDF10"/>
    </resultMap>

    <sql id="selectOmsOrderFwxmWorkEntityVo">
        select
            WORK_NO,
            ORDER_NO,
            PRE_NO,
            FWXM_CODE,
            TASK_SEQ,
            TASK_PRE,
            IS_WB,
            GYS_CODE,
            NODE_CODE_NB,
            QUOTE_CODE,
            STATUS,
            STAGE,
            SYS_CODE,
            BILL_CODE,
            DOC_NO,
            RESPONSE_CODE,
            SEND_DATE,
            CREATE_USER,
            CREATE_USER_NAME,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_USER_NAME,
            UPDATE_DATE,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            GUID,
            SEND_FLAG,
            SEND_USER,
            SEND_NODE_CODE,
            SEND_NODE_NAME,
            SEND_USER_NAME,
            OP_COMPLETE_OK_DATE,
            OP_COMPLETE_OK,
            DATA_OK_DATE,
            DATA_OK,
            ARAP_OK,
            ARAP_OK_DATE,
            XZWT_NO,
            FWLX_CODE,
            BIZ_REG_ID,
            PRE_WORK_NO,
            IS_AP,
            UDF1,
            UDF2,
            UDF3,
            UDF4,
            UDF5,
            UDF6,
            UDF7,
            UDF8,
            UDF9,
            UDF10
        from OMS_ORDER_FWXM_WORK
    </sql>
</mapper>