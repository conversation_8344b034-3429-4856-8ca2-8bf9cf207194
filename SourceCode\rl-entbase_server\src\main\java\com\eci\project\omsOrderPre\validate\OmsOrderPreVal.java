package com.eci.project.omsOrderPre.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.omsOrderPre.entity.OmsOrderPreEntity;

import org.springframework.stereotype.Service;


/**
* 客户委托单Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-15
*/
@Service
public class OmsOrderPreVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(OmsOrderPreEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(OmsOrderPreEntity entity, BusinessType businessType) {

    }

}
