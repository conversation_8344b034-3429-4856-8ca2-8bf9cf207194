package com.eci.project.fzgjBdArea.dao;

import com.eci.crud.dao.EciBaseDao;
import com.eci.project.fzgjBaseDataDetail.entity.FzgjBaseDataDetailEntity;
import com.eci.project.fzgjBdArea.entity.FzgjBdAreaEntity;
import com.eci.wu.core.EntityBase;

import java.util.List;


/**
* 公路乡镇地区Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-03-26
*/
public interface FzgjBdAreaDao extends EciBaseDao<FzgjBdAreaEntity> {
    List<FzgjBdAreaEntity> selectListInfo(FzgjBdAreaEntity entity);

    FzgjBdAreaEntity selectInfo(String guid);
}
