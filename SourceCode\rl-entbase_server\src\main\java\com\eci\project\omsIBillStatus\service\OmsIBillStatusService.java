package com.eci.project.omsIBillStatus.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.omsIBillStatus.dao.OmsIBillStatusDao;
import com.eci.project.omsIBillStatus.entity.OmsIBillStatusEntity;
import com.eci.project.omsIBillStatus.validate.OmsIBillStatusVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 单据状态Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-05-16
*/
@Service
@Slf4j
public class OmsIBillStatusService implements EciBaseService<OmsIBillStatusEntity> {

    @Autowired
    private OmsIBillStatusDao omsIBillStatusDao;

    @Autowired
    private OmsIBillStatusVal omsIBillStatusVal;


    @Override
    public TgPageInfo queryPageList(OmsIBillStatusEntity entity) {
        EciQuery<OmsIBillStatusEntity> eciQuery = EciQuery.buildQuery(entity);
        List<OmsIBillStatusEntity> entities = omsIBillStatusDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public OmsIBillStatusEntity save(OmsIBillStatusEntity entity) {
        // 返回实体对象
        OmsIBillStatusEntity omsIBillStatusEntity = null;
        omsIBillStatusVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            omsIBillStatusEntity = omsIBillStatusDao.insertOne(entity);

        }else{

            omsIBillStatusEntity = omsIBillStatusDao.updateByEntityId(entity);

        }
        return omsIBillStatusEntity;
    }

    @Override
    public List<OmsIBillStatusEntity> selectList(OmsIBillStatusEntity entity) {
        return omsIBillStatusDao.selectList(entity);
    }

    @Override
    public OmsIBillStatusEntity selectOneById(Serializable id) {
        return omsIBillStatusDao.selectById(id);
    }


    @Override
    public void insertBatch(List<OmsIBillStatusEntity> list) {
        omsIBillStatusDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return omsIBillStatusDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return omsIBillStatusDao.deleteById(id);
    }

}