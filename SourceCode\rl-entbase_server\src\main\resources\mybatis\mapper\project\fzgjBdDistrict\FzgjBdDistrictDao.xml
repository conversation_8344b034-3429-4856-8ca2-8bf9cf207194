<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.fzgjBdDistrict.dao.FzgjBdDistrictDao">
    <resultMap type="FzgjBdDistrictEntity" id="FzgjBdDistrictResult">
        <result property="guid" column="GUID"/>
        <result property="name" column="NAME"/>
        <result property="cityId" column="CITY_ID"/>
        <result property="memo" column="MEMO"/>
        <result property="status" column="STATUS"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="seq" column="SEQ"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="enName" column="EN_NAME"/>
    </resultMap>

    <resultMap type="FzgjBdDistrictRealtionEntity" id="FzgjBdDistrictRealtionResult">
        <result property="countryCode" column="COUNTRY_CODE"/>
        <result property="countryName" column="COUNTRY_NAME"/>
        <result property="provinceCode" column="PROVINCE_CODE"/>
        <result property="provinceName" column="PROVINCE_NAME"/>
        <result property="cityCode" column="CITY_CODE"/>
        <result property="cityName" column="CITY_NAME"/>
        <result property="districtCode" column="DISTRICT_CODE"/>
        <result property="districtName" column="DISTRICT_NAME"/>
    </resultMap>

    <sql id="selectFzgjBdDistrictEntityVo">
        select
            GUID,
            NAME,
            CITY_ID,
            MEMO,
            STATUS,
            CREATE_USER,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_DATE,
            SEQ,
            CREATE_USER_NAME,
            UPDATE_USER_NAME,
            EN_NAME
        from FZGJ_BD_DISTRICT
    </sql>

    <select id="selectDistrictRealtionPageList" parameterType="FzgjBdDistrictRealtionEntity" resultMap="FzgjBdDistrictRealtionResult">
        SELECT
            BD.GUID AS DISTRICT_CODE,
            BD.NAME AS DISTRICT_NAME,
            BCO.CH_NAME AS COUNTRY_NAME,
            BCO.CODE AS COUNTRY_CODE,
            BP.NAME AS PROVINCE_NAME,
            BP.GUID AS PROVINCE_CODE,
            BC.NAME AS CITY_NAME,
            BC.GUID AS CITY_CODE
        FROM FZGJ_BD_DISTRICT BD
        LEFT JOIN FZGJ_BD_CITY BC ON BC.GUID = BD.CITY_ID
        LEFT JOIN FZGJ_BD_PROVINCE BP ON BP.GUID = BC.PROVINCE_ID
        LEFT JOIN FZGJ_BD_COUNTRY BCO ON BCO.CODE = BP.COUNTRY_ID
        WHERE 1=1  AND BD.STATUS='Y' AND BC.STATUS='Y' AND BP.STATUS='Y' AND BCO.STATUS='Y'
        <if test="districtCode != null and districtCode != ''">
            AND (
            BD.CITY_ID like '%' || #{districtCode} || '%'
            OR  BD.NAME like '%' || #{districtCode} || '%'
            OR  BC.NAME like '%' || #{districtCode} || '%'
            OR  BC.PROVINCE_ID like '%' || #{districtCode} || '%'
            OR  BP.NAME like '%' || #{districtCode} || '%'
            OR  BP.GUID like '%' || #{districtCode} || '%'
            OR  BCO.CODE like '%' || #{districtCode} || '%'
            OR  BCO.CH_NAME like '%' || #{districtCode} || '%'
            )
        </if>
    </select>
</mapper>