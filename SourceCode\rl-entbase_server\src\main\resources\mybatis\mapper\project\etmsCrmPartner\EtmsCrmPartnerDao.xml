<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.etmsCrmPartner.dao.EtmsCrmPartnerDao">
    <resultMap type="EtmsCrmPartnerEntity" id="EtmsCrmPartnerResult">
        <result property="guid" column="GUID"/>
        <result property="code" column="CODE"/>
        <result property="name" column="NAME"/>
        <result property="enteCode" column="ENTE_CODE"/>
        <result property="status" column="STATUS"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createCompany" column="CREATE_COMPANY"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateCompany" column="UPDATE_COMPANY"/>
        <result property="enteCodeEx" column="ENTE_CODE_EX"/>
        <result property="trade" column="TRADE"/>
        <result property="lsp" column="LSP"/>
        <result property="enteNameEn" column="ENTE_NAME_EN"/>
        <result property="enteAddrCn" column="ENTE_ADDR_CN"/>
        <result property="postcode" column="POSTCODE"/>
        <result property="country" column="COUNTRY"/>
        <result property="contactPerson" column="CONTACT_PERSON"/>
        <result property="tel" column="TEL"/>
        <result property="fax" column="FAX"/>
        <result property="email" column="EMAIL"/>
        <result property="website" column="WEBSITE"/>
        <result property="swiftAddress" column="SWIFT_ADDRESS"/>
        <result property="bankAddress" column="BANK_ADDRESS"/>
        <result property="customsNo" column="CUSTOMS_NO"/>
        <result property="billContent" column="BILL_CONTENT"/>
        <result property="mappingType" column="MAPPING_TYPE"/>
        <result property="memo" column="MEMO"/>
        <result property="mappingAbbreviation" column="MAPPING_ABBREVIATION"/>
        <result property="province" column="PROVINCE"/>
        <result property="city" column="CITY"/>
        <result property="enteAddrEn" column="ENTE_ADDR_EN"/>
        <result property="isEmail" column="IS_EMAIL"/>
        <result property="expressWebsite" column="EXPRESS_WEBSITE"/>
        <result property="telQt" column="TEL_QT"/>
        <result property="customsType" column="CUSTOMS_TYPE"/>
        <result property="inspectNo" column="INSPECT_NO"/>
        <result property="inspectType" column="INSPECT_TYPE"/>
        <result property="sales" column="SALES"/>
        <result property="oper" column="OPER"/>
        <result property="zz" column="ZZ"/>
        <result property="isUserControl" column="IS_USER_CONTROL"/>
        <result property="canBorrow" column="CAN_BORROW"/>
        <result property="taxId" column="TAX_ID"/>
        <result property="bankName" column="BANK_NAME"/>
        <result property="bankAccount" column="BANK_ACCOUNT"/>
        <result property="accountMode" column="ACCOUNT_MODE"/>
        <result property="payMode" column="PAY_MODE"/>
        <result property="payeeDay" column="PAYEE_DAY"/>
        <result property="account" column="ACCOUNT"/>
        <result property="bank" column="BANK"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
    </resultMap>

    <sql id="selectEtmsCrmPartnerEntityVo">
        select
            GUID,
            CODE,
            NAME,
            ENTE_CODE,
            STATUS,
            CREATE_DATE,
            CREATE_USER,
            CREATE_COMPANY,
            UPDATE_DATE,
            UPDATE_USER,
            UPDATE_COMPANY,
            ENTE_CODE_EX,
            TRADE,
            LSP,
            ENTE_NAME_EN,
            ENTE_ADDR_CN,
            POSTCODE,
            COUNTRY,
            CONTACT_PERSON,
            TEL,
            FAX,
            EMAIL,
            WEBSITE,
            SWIFT_ADDRESS,
            BANK_ADDRESS,
            CUSTOMS_NO,
            BILL_CONTENT,
            MAPPING_TYPE,
            MEMO,
            MAPPING_ABBREVIATION,
            PROVINCE,
            CITY,
            ENTE_ADDR_EN,
            IS_EMAIL,
            EXPRESS_WEBSITE,
            TEL_QT,
            CUSTOMS_TYPE,
            INSPECT_NO,
            INSPECT_TYPE,
            SALES,
            OPER,
            ZZ,
            IS_USER_CONTROL,
            CAN_BORROW,
            TAX_ID,
            BANK_NAME,
            BANK_ACCOUNT,
            ACCOUNT_MODE,
            PAY_MODE,
            PAYEE_DAY,
            ACCOUNT,
            BANK,
            GROUP_CODE,
            COMPANY_CODE
        from ETMS_CRM_PARTNER
    </sql>
</mapper>