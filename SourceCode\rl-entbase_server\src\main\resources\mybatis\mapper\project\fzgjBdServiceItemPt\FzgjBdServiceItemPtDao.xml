<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.fzgjBdServiceItemPt.dao.FzgjBdServiceItemPtDao">
    <resultMap type="FzgjBdServiceItemPtEntity" id="FzgjBdServiceItemPtResult">
        <result property="guid" column="GUID"/>
        <result property="code" column="CODE"/>
        <result property="name" column="NAME"/>
        <result property="status" column="STATUS"/>
        <result property="seq" column="SEQ"/>
        <result property="memo" column="MEMO"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="ownedCompany" column="OWNED_COMPANY"/>
        <result property="parentid" column="PARENTID"/>
        <result property="ownedService" column="OWNED_SERVICE"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="pageUrl" column="PAGE_URL"/>
        <result property="selectType" column="SELECT_TYPE"/>
        <result property="ysfs" column="YSFS"/>
        <result property="sysCode" column="SYS_CODE"/>
        <result property="jdBill" column="JD_BILL"/>
        <result property="czBill" column="CZ_BILL"/>
        <result property="feedbackUrl" column="FEEDBACK_URL"/>
        <result property="isZy" column="IS_ZY"/>
        <result property="enName" column="EN_NAME"/>
    </resultMap>
    <select id="selectTree" parameterType="com.baomidou.mybatisplus.core.conditions.Wrapper"
         resultType="com.eci.project.fzgjBdServiceItemPt.entity.TreeModel">
        select
            GUID as id,
            CODE,
            NAME as label,
            STATUS,
            SEQ,
            MEMO,
            CREATE_DATE,
            CREATE_USER,
            UPDATE_DATE,
            UPDATE_USER,
            OWNED_COMPANY,
            PARENTID,
            OWNED_SERVICE,
            CREATE_USER_NAME,
            UPDATE_USER_NAME,
            PAGE_URL,
            SELECT_TYPE,
            YSFS,
            SYS_CODE,
            JD_BILL,
            CZ_BILL,
            FEEDBACK_URL,
            IS_ZY,
            EN_NAME
        from FZGJ_BD_SERVICE_ITEM_PT
                 ${ew.customSqlSegment}
    </select>
    <sql id="selectFzgjBdServiceItemPtEntityVo">
        select
            GUID,
            CODE,
            NAME,
            STATUS,
            SEQ,
            MEMO,
            CREATE_DATE,
            CREATE_USER,
            UPDATE_DATE,
            UPDATE_USER,
            OWNED_COMPANY,
            PARENTID,
            OWNED_SERVICE,
            CREATE_USER_NAME,
            UPDATE_USER_NAME,
            PAGE_URL,
            SELECT_TYPE,
            YSFS,
            SYS_CODE,
            JD_BILL,
            CZ_BILL,
            FEEDBACK_URL,
            IS_ZY,
            EN_NAME
        from FZGJ_BD_SERVICE_ITEM_PT
    </sql>

    <select id="selectwithparent" parameterType="com.baomidou.mybatisplus.core.conditions.Wrapper"
            resultType="com.eci.project.fzgjBdServiceItemPt.entity.FzgjBdServiceItemPtEntity">
        select * from (select
                           I.*,I1.name as parentname,T.name as owned_ServiceName
                       from FZGJ_BD_SERVICE_ITEM_PT I LEFT JOIN FZGJ_BD_SERVICE_ITEM_PT I1 on I1.guid=I.parentid
                       LEFT JOIN FZGJ_BD_SERVICE_TYPE T on I.owned_service=T.guid)
                 ${ew.customSqlSegment}
    </select>

</mapper>