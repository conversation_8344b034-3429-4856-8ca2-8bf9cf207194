package com.eci.project.omsOrderFwxmTmsXlXlLyCl.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.omsOrderFwxmTmsXlXlLyCl.entity.OmsOrderFwxmTmsXlXlLyClEntity;


/**
* 委托内容-程运序列-陆运-车辆信息Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-06-04
*/
public interface OmsOrderFwxmTmsXlXlLyClDao extends EciBaseDao<OmsOrderFwxmTmsXlXlLyClEntity> {

}