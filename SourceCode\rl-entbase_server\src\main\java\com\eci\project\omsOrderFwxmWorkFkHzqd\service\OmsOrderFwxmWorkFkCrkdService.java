package com.eci.project.omsOrderFwxmWorkFkHzqd.service;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.DataExtend;
import com.eci.common.Extensions;
import com.eci.common.ZsrJson;
import com.eci.common.db.DBHelper;
import com.eci.common.util.StringUtils;
import com.eci.common.utils.CommonLib;
import com.eci.exception.BaseException;
import com.eci.project.omsOrder.service.OmsOrderService;
import com.eci.project.omsOrderFwxmWorkFk.dao.OmsOrderFwxmWorkFkDao;
import com.eci.project.omsOrderFwxmWorkFk.entity.OmsOrderFwxmWorkFkEntity;
import com.eci.project.omsOrderFwxmWorkFk.validate.OmsOrderFwxmWorkFkVal;
import com.eci.project.omsOrderFwxmWorkFkHzqd.dao.OmsOrderFwxmWorkFkHzqdDao;
import com.eci.project.omsOrderFwxmWorkFkHzqd.entity.OmsOrderFwxmWorkFkHzqdEntity;
import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * 出入库单
 *
 * @<NAME_EMAIL>
 * @date 2025-05-21
 */
@Service
@Slf4j
public class OmsOrderFwxmWorkFkCrkdService {

    CommonLib cmn = CommonLib.getInstance();

    @Autowired
    private OmsOrderFwxmWorkFkDao omsOrderFwxmWorkFkDao;

    @Autowired
    private OmsOrderFwxmWorkFkVal omsOrderFwxmWorkFkVal;

    @Autowired
    private OmsOrderFwxmWorkFkHzqdDao omsOrderFwxmWorkFkHzqdDao;

    @Autowired
    private Extensions extensions;

    @Autowired
    private OmsOrderService omsOrderService;

    /**
     * 出入库单 - 加载
     **/
    public List<OmsOrderFwxmWorkFkHzqdEntity> loadOrderFwxmWorkFkCrkd(OmsOrderFwxmWorkFkEntity entity) {

        String groupCode = UserContext.getUserInfo().getCompanyCode();

        String sql = "SELECT A.GUID,A.CRKDH,A.PIECE,A.WEIGHT,A.QTY,A.PRODUCT_CODE\n" +
                "            ,I_E_TYPE\n" +
                "            ,CUSTOM_CODE||'|'||OMS_NAME(A.CUSTOM_CODE,'OMS_BD_CUSTOMS') AS CUSTOM_CODE\n" +
                "            ,TRADE_CODE||'|'||OMS_NAMECOM(A.TRADE_CODE," + cmn.SQLQ(groupCode) + ",'CRM_CUSTOMER_SFHF') AS TRADE_CODE\n" +
                "            ,YSFS||'|'||OMS_NAME(A.YSFS,'OMS_BD_YSFS') AS YSFS\n" +
                "            ,DEC_TRADE_MODE||'|'||OMS_CODE_NAME(A.DEC_TRADE_MODE,'OMS_BD_TRADE') DEC_TRADE_MODE\n" +
                "            ,NET_WEIGHT\n" +
                "            ,AMOUNT\n" +
                "            ,AMOUNT_CURRENCY\n" +
                "            ,OMS_CODE_NAME(A.CURRENCY, 'OMS_BD_CURRENCY') CURRENCY\n" +
                "            ,HS_CODE\n" +
                "            ,IS_CHECK\n" +
                "            ,IS_SD\n" +
                "            ,IS_GD\n" +
                "            ,BILL_MAN\n" +
                "            ,BGD_MEMO\n" +
                "            ,TO_CHAR(A.BILL_DATE,'yyyy-mm-dd') AS BILL_DATE\n" +
                "            ,TO_CHAR(A.D_DATE,'yyyy-mm-dd') AS D_DATE\n" +
                "            FROM OMS_ORDER_FWXM_WORK_FK_HZQD A";

        sql += " WHERE A.WORK_NO=" + cmn.SQLQ(entity.getWorkNo()) + " and A.BIZ_REG_ID=" + cmn.SQLQ(entity.getBizRegId()) + "";
        sql += " order  by create_date  ";

        return DBHelper.selectList(sql, OmsOrderFwxmWorkFkHzqdEntity.class);
    }

    /**
     * 出入库单-保存
     **/
    @Transactional
    public void saveOrderFwxmWorkFkCrkd(String jsonString) {

        ZsrJson jsonStr = ZsrJson.parse(jsonString);

        OmsOrderFwxmWorkFkEntity saveEntity = jsonStr.check("entity").getObject("entity", OmsOrderFwxmWorkFkEntity.class);
        List<OmsOrderFwxmWorkFkHzqdEntity> dtDateHZQD = jsonStr.check("dtDateHZQD").getList("dtDateHZQD", OmsOrderFwxmWorkFkHzqdEntity.class);
        if (saveEntity == null) {
            throw new BaseException("entity 解析失败");
        }

        boolean isAdd = false;
        List<OmsOrderFwxmWorkFkEntity> fklist = omsOrderFwxmWorkFkDao.select().eq(OmsOrderFwxmWorkFkEntity::getBizRegId, saveEntity.getBizRegId()).list();
        if (fklist.size() <= 0) {
            isAdd = true;
        }

        OmsOrderFwxmWorkFkEntity fkEntity = new OmsOrderFwxmWorkFkEntity();
        fkEntity.setWorkNo(saveEntity.getWorkNo());
        fkEntity.setXzwtNo(saveEntity.getXzwtNo());
        fkEntity.setOrderNo(saveEntity.getOrderNo());
        fkEntity.setFwxmCode(saveEntity.getFwxmCode());
        fkEntity.setBizRegId(saveEntity.getBizRegId());

        omsOrderFwxmWorkFkVal.feedBackValidate(fkEntity.getWorkNo(), "");

        if (isAdd) {
            fkEntity.setGuid(IdWorker.get32UUID());
            fkEntity.setCreateDate(new java.util.Date());
            fkEntity.setUpdateDate(new java.util.Date());
            fkEntity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            fkEntity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            fkEntity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            fkEntity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            omsOrderFwxmWorkFkDao.insertOne(fkEntity);
        } else {
            fkEntity.setUpdateDate(new java.util.Date());
            fkEntity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            fkEntity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            omsOrderFwxmWorkFkDao.updateByEntityId(fkEntity);
        }

        // 删除已存在的出入库单数据
        if (StringUtils.hasValue(saveEntity.getBizRegId())) {
            String delSql = "DELETE FROM OMS_ORDER_FWXM_WORK_FK_HZQD WHERE BIZ_REG_ID=" + cmn.SQLQ(saveEntity.getBizRegId());
            DBHelper.execute(delSql);
        }

        // 验证
        omsOrderFwxmWorkFkVal.validateOrderOnly("CRKDH", dtDateHZQD);

        // 新增出入库单数据
        if (dtDateHZQD.size() > 0) {
            for (OmsOrderFwxmWorkFkHzqdEntity rows : dtDateHZQD) {
                OmsOrderFwxmWorkFkHzqdEntity cbsCheckInfo = new OmsOrderFwxmWorkFkHzqdEntity();

                rows.setIsCheck(rows.getIsCheck().equals("true") ? "Y" : "N");
                rows.setIsGd(rows.getIsGd().equals("true") ? "Y" : "N");
                rows.setIsSd(rows.getIsSd().equals("true") ? "Y" : "N");
                BeanUtils.copyProperties(rows, cbsCheckInfo);

                cbsCheckInfo.setGuid(IdWorker.get32UUID());
                cbsCheckInfo.setOrderNo(saveEntity.getOrderNo());
                cbsCheckInfo.setWorkNo(saveEntity.getWorkNo());
                cbsCheckInfo.setBizRegId(saveEntity.getBizRegId());
                cbsCheckInfo.setFwxmCode(saveEntity.getFwxmCode());
                cbsCheckInfo.setiEType(DataExtend.toCode(rows.getiEType()));
                cbsCheckInfo.setiEPort(DataExtend.toCode(rows.getiEPort()));
                cbsCheckInfo.setCustomCode(DataExtend.toCode(rows.getCustomCode()));
                cbsCheckInfo.setTradeCode(DataExtend.toCode(rows.getTradeCode()));
                cbsCheckInfo.setYsfs(DataExtend.toCode(rows.getYsfs()));
                cbsCheckInfo.setCurrency(DataExtend.toCode(rows.getCurrency()));
                cbsCheckInfo.setSupervisionMode(DataExtend.toCode(rows.getSupervisionMode()));
                cbsCheckInfo.setOriginArrivalCountry(DataExtend.toCode(rows.getOriginArrivalCountry()));
                cbsCheckInfo.setHzqdBgType(DataExtend.toCode(rows.getHzqdBgType()));
                cbsCheckInfo.setBgFlag(DataExtend.toCode(rows.getBgFlag()));

                cbsCheckInfo.setCreateDate(new java.util.Date());
                cbsCheckInfo.setUpdateDate(new java.util.Date());
                cbsCheckInfo.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
                cbsCheckInfo.setCreateUserName(UserContext.getUserInfo().getTrueName());
                cbsCheckInfo.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
                cbsCheckInfo.setUpdateUserName(UserContext.getUserInfo().getTrueName());
                cbsCheckInfo.setGroupCode(UserContext.getUserInfo().getCompanyCode());
                cbsCheckInfo.setGroupName(UserContext.getUserInfo().getCompanyName());
                cbsCheckInfo.setNodeCode(UserContext.getUserInfo().getDeptCode());
                cbsCheckInfo.setNodeName(UserContext.getUserInfo().getDeptName());
                cbsCheckInfo.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
                cbsCheckInfo.setCompanyName(UserContext.getUserInfo().getCompanyName());
                omsOrderFwxmWorkFkHzqdDao.insertOne(cbsCheckInfo);

                // T032189 保存 核注清单，    报关单， 出入库单 时  关联 查验类型
                // CHECKBILL_NO  DEC_NO  CRKDH
                String itm = cbsCheckInfo.getCheckbillNo();
                if (StringUtils.hasValue(itm)) {
                    omsOrderService.orderCheckType(itm, "FK_PAGE");
                }
            }
        }

        // 调用存储过程-固化
        extensions.addOmsGh(saveEntity.getOrderNo());
    }

}