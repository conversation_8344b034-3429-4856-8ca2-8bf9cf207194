package com.eci.project.fzgjBdCity.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;


/**
* 市对象 FZGJ_BD_CITY
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-03-17
*/
@ApiModel("市")
@TableName("FZGJ_BD_CITY")
public class FzgjBdCityEntity extends FzgjBdCityBaseEntity{

}
