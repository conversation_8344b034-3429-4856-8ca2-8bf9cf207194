package com.eci.project.omsOrder.controller;

import com.eci.common.CodeNameCommon;
import com.eci.common.ZsrBaseEntity;
import com.eci.common.util.ResponseMsg;
import com.eci.common.util.ResponseMsgUtil;
import com.eci.crud.controller.EciBaseController;
import com.eci.log.annotation.EciAction;
import com.eci.log.annotation.EciLog;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.omsOrder.entity.OmsOrderEntity;
import com.eci.project.omsOrder.entity.RequestOmsOrderTracePageEntity;
import com.eci.project.omsOrder.service.OmsOrderCustomService;
import com.eci.project.omsOrder.service.OmsOrderService;
import com.eci.project.omsOrder.service.OmsOrderTraceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 订单操作界面-左边树形结构Controller
 *
 * @<NAME_EMAIL>
 * @date 2025-04-14
 */
@Api(tags = "数据处理服务")
@RestController
@RequestMapping("/handlerServiceData")
public class HandlerServiceDataController extends EciBaseController {

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OmsOrderCustomService omsOrderCustomService;

    @Autowired
    private OmsOrderTraceService omsOrderTraceService;

    @ApiOperation("订单-树形结构菜单:查询")
    @EciLog(title = "订单-树形结构菜单:查询", businessType = BusinessType.SELECT)
    @PostMapping("/getNodeList")
    @EciAction()
    public ResponseMsg GetNodeList(@RequestBody String jsonString) {
        ZsrBaseEntity zsrBaseEntity = omsOrderCustomService.GetNodeList(jsonString);
        return ResponseMsgUtil.success(10001, zsrBaseEntity);
    }

    @ApiOperation("订单-树形结构菜单:查询")
    @EciLog(title = "订单-树形结构菜单:查询", businessType = BusinessType.SELECT)
    @PostMapping("/getNodeListZiZhu")
    @EciAction()
    public ResponseMsg getNodeListZiZhu(@RequestBody String jsonString) {
        ZsrBaseEntity zsrBaseEntity = omsOrderCustomService.GetNodeListZizhu(jsonString);
        return ResponseMsgUtil.success(10001, zsrBaseEntity);
    }


}