package com.eci.project.fzgjBdProvince.controller;

import com.eci.common.ResponseMsgUtilX;
import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.project.fzgjBdProvince.service.IFzgjBdProvinceService;
import com.eci.project.fzgjBdProvince.entity.FzgjBdProvinceEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.EciLog;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


/**
* 省Controller
*
* @<NAME_EMAIL>
* @date 2025-03-17
*/
@Api(tags = "省")
@RestController
@RequestMapping("/fzgjBdProvince")
public class FzgjBdProvinceController extends EciBaseController {

    @Autowired
    private IFzgjBdProvinceService fzgjBdProvinceService;

    /**
    * 根据实体插入数据库
    * @param entity
    * @return
    */
    @ApiOperation("省:保存")
    @EciLog(title = "省:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public ResponseMsg save(@RequestBody FzgjBdProvinceEntity entity){
        return ResponseMsgUtil.success(10001,fzgjBdProvinceService.save(entity));
    }

    /**
    * 根据条件查询一个集合
    * @param entity
    * @return
    */
    @ApiOperation("省:查询列表")
    @EciLog(title = "省:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    public ResponseMsg selectList(@RequestBody FzgjBdProvinceEntity entity){
        return ResponseMsgUtil.success(10001,fzgjBdProvinceService.selectList(entity));
    }

    /**
    * 分页查询
    * @param entity
    * @return
    */
    @ApiOperation("省:分页查询列表")
    @EciLog(title = "省:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    public ResponseMsg selectPageList(@RequestBody FzgjBdProvinceEntity entity){
        return ResponseMsgUtilX.success(10001,fzgjBdProvinceService.queryPageList(entity));
    }

    /**
    * 根据ID查一条记录
    * @param entity
    * @return
    */
    @ApiOperation("省:根据ID查一条")
    @EciLog(title = "省:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    public ResponseMsg selectOneById(@RequestBody FzgjBdProvinceEntity entity){
        return ResponseMsgUtilX.success(10001,fzgjBdProvinceService.selectOneById(entity.getGuid()));
    }


    /**
    * 根据ID删除
    * @param entity
    * @return
    */
    @ApiOperation("省:根据ID删除一条")
    @EciLog(title = "省:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    public ResponseMsg deleteById(@RequestBody FzgjBdProvinceEntity entity){
        return ResponseMsgUtil.success(10001,fzgjBdProvinceService.deleteById(entity.getGuid()));
    }

    /**
    * 根据IDS删除
    *
    * @param entity
    * @return
    */
    @ApiOperation("省:根据ID字符串删除多条")
    @EciLog(title = "省:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    public ResponseMsg deleteByIds(@RequestBody FzgjBdProvinceEntity entity) {
        return ResponseMsgUtil.success(10001, fzgjBdProvinceService.deleteByIds(entity.getIds()));
    }


}