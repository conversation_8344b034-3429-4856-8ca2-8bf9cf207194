package com.eci.project.omsOrderFwxmBgbj.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.common.DictField;
import com.eci.common.ZsrBaseEntity;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 委托内容-报关报检对象 OMS_ORDER_FWXM_BGBJ
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-05-14
*/
@ApiModel("委托内容-报关报检")
@TableName("OMS_ORDER_FWXM_BGBJ")
@FieldNameConstants
public class OmsOrderFwxmBgbjEntity extends ZsrBaseEntity {
    /**
    * 委托单编号
    */
    @ApiModelProperty("委托单编号(36)")
    @TableField("PRE_NO")
    private String preNo;

    /**
    * 订单号
    */
    @ApiModelProperty("订单号(36)")
    @TableField("ORDER_NO")
    private String orderNo;

    /**
    * 需求唯一编号
    */
    @ApiModelProperty("需求唯一编号(36)")
    @TableId("BGBJ_NO")
    private String bgbjNo;

    /**
    * 服务类型代码
    */
    @ApiModelProperty("服务类型代码(20)")
    @TableField("FWLX_CODE")
    private String fwlxCode;

    /**
    * 服务项目代码
    */
    @ApiModelProperty("服务项目代码(20)")
    @TableField("FWXM_CODE")
    private String fwxmCode;

    /**
    * 进出标志：I进E出
    */
    @ApiModelProperty("进出标志：I进E出(10)")
    @TableField("I_E_TYPE")
    private String iEType;

    /**
    * 贸易方式
    */
    @ApiModelProperty("贸易方式(10)")
    @TableField("TRADE_MODE")
    private String tradeMode;

    /**
    * 成交条款
    */
    @ApiModelProperty("成交条款(50)")
    @TableField("CJTK")
    private String cjtk;

    /**
    * 协作任务编号
    */
    @ApiModelProperty("协作任务编号(50)")
    @TableField("WORK_NO")
    private String workNo;

    /**
    * 境内收货人/账册企业名称
    */
    @ApiModelProperty("境内收货人/账册企业名称(100)")
    @TableField("JNSHR")
    @DictField(queryKey = "CRM_CUSTOMER_SFHF")
    private String jnshr;

    /**
    * 境外发货人/账册企业名称
    */
    @ApiModelProperty("境外发货人/账册企业名称(100)")
    @TableField("JNFHR")
    @DictField(queryKey = "CRM_CUSTOMER_SFHF")
    private String jnfhr;

    /**
    * 进/出境关别
    */
    @ApiModelProperty("进/出境关别(10)")
    @TableField("I_E_PORT")
    @DictField(queryKey = "OMS_BD_CUSTOMS")
    private String iEPort;

    /**
    * 消费使用单位/生产销售单位/经营单位
    */
    @ApiModelProperty("消费使用单位/生产销售单位/经营单位(20)")
    @TableField("TRADE_CODE")
    @DictField(queryKey = "CRM_CUSTOMER_HGBM")
    private String tradeCode;

    /**
    * 手册号/账册号
    */
    @ApiModelProperty("手册号/账册号(20)")
    @TableField("ENROLNO")
    private String enrolno;

    /**
    * 入/离境口岸、出境关别
    */
    @ApiModelProperty("入/离境口岸、出境关别(10)")
    @TableField("ENTYPORT_CODE")
    private String entyportCode;

    /**
    * 成交方式
    */
    @ApiModelProperty("成交方式(10)")
    @TableField("CJFS")
    private String cjfs;

    /**
    * 原产国
    */
    @ApiModelProperty("原产国(10)")
    @TableField("ORIGIN_CODE")
    private String originCode;

    /**
    * 最终目的国
    */
    @ApiModelProperty("最终目的国(10)")
    @TableField("DESTINATION_COUNTRY")
    private String destinationCountry;

    /**
    * 查验要求
    */
    @ApiModelProperty("查验要求(500)")
    @TableField("CY_MEMO")
    private String cyMemo;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 最后修改日期
    */
    @ApiModelProperty("最后修改日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("最后修改日期开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("最后修改日期结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(200)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 监管方式 
    */
    @ApiModelProperty("监管方式 (200)")
    @TableField("SUPERVISION")
    private String supervision;

    /**
    * 核注清单编号
    */
    @ApiModelProperty("核注清单编号(200)")
    @TableField("XQDH")
    private String xqdh;

    /**
    * 报关核准单号/企业内控编号
    */
    @ApiModelProperty("报关核准单号/企业内控编号(50)")
    @TableField("DEC_NO")
    private String decNo;

    /**
    * 报关模式/清关模式/报核类型:1. 预报核；2.正式报核
    */
    @ApiModelProperty("报关模式/清关模式/报核类型:1. 预报核；2.正式报核(10)")
    @TableField("BH_TYPE")
    private String bhType;

    /**
    * 核销日期起
    */
    @ApiModelProperty("核销日期起(7)")
    @TableField("HX_DATE_BEGIN")
    private Date hxDateBegin;

    @ApiModelProperty("核销日期起开始")
    @TableField(exist=false)
    private Date hxDateBeginStart;

    @ApiModelProperty("核销日期起结束")
    @TableField(exist=false)
    private Date hxDateBeginEnd;

    /**
    * 操作类型1删单2改单
    */
    @ApiModelProperty("操作类型1删单2改单(10)")
    @TableField("SGDLX")
    private String sgdlx;

    /**
    * 关联订单号
    */
    @ApiModelProperty("关联订单号(50)")
    @TableField("GL_ORDER_NO")
    private String glOrderNo;

    /**
    * 删改单原因
    */
    @ApiModelProperty("删改单原因(10)")
    @TableField("SGDYY")
    private String sgdyy;

    /**
    * 计量单位/作业计量单位
    */
    @ApiModelProperty("计量单位/作业计量单位(10)")
    @TableField("UNIT")
    private String unit;

    /**
    * 数量/作业数量
    */
    @ApiModelProperty("数量/作业数量(22)")
    @TableField("QTY")
    private Integer qty;

    /**
    * 预检验时间
    */
    @ApiModelProperty("预检验时间(7)")
    @TableField("YJY_DATE")
    private Date yjyDate;

    @ApiModelProperty("预检验时间开始")
    @TableField(exist=false)
    private Date yjyDateStart;

    @ApiModelProperty("预检验时间结束")
    @TableField(exist=false)
    private Date yjyDateEnd;

    /**
    * 预检验地点
    */
    @ApiModelProperty("预检验地点(100)")
    @TableField("YJY_ADDRESS")
    private String yjyAddress;

    /**
    * 客户联系人
    */
    @ApiModelProperty("客户联系人(20)")
    @TableField("KH_LINK_MAN")
    private String khLinkMan;

    /**
    * 客户联系人电话
    */
    @ApiModelProperty("客户联系人电话(50)")
    @TableField("KH_TEL")
    private String khTel;

    /**
    * 国检到达时间
    */
    @ApiModelProperty("国检到达时间(7)")
    @TableField("CIQ_DATE")
    private Date ciqDate;

    @ApiModelProperty("国检到达时间开始")
    @TableField(exist=false)
    private Date ciqDateStart;

    @ApiModelProperty("国检到达时间结束")
    @TableField(exist=false)
    private Date ciqDateEnd;

    /**
    * 国检联系人
    */
    @ApiModelProperty("国检联系人(20)")
    @TableField("CIQ_LINK_MAN")
    private String ciqLinkMan;

    /**
    * 国检联系人电话
    */
    @ApiModelProperty("国检联系人电话(50)")
    @TableField("CIQ_TEL")
    private String ciqTel;

    /**
    * 单据名称
    */
    @ApiModelProperty("单据名称(10)")
    @TableField("DJMC")
    private String djmc;

    /**
    * 转出地
    */
    @ApiModelProperty("转出地(20)")
    @TableField("ZC_ADDRESS")
    private String zcAddress;

    /**
    * 转入地
    */
    @ApiModelProperty("转入地(20)")
    @TableField("ZR_ADDRESS")
    private String zrAddress;

    /**
    * 核销日期至
    */
    @ApiModelProperty("核销日期至(7)")
    @TableField("HX_DATE_END")
    private Date hxDateEnd;

    @ApiModelProperty("核销日期至开始")
    @TableField(exist=false)
    private Date hxDateEndStart;

    @ApiModelProperty("核销日期至结束")
    @TableField(exist=false)
    private Date hxDateEndEnd;

    /**
    * 转出关别
    */
    @ApiModelProperty("转出关别(10)")
    @TableField("ZC_CUSTOM")
    private String zcCustom;

    /**
    * 转入关别
    */
    @ApiModelProperty("转入关别(10)")
    @TableField("ZR_CUSTOM")
    private String zrCustom;

    /**
    * 运输方式
    */
    @ApiModelProperty("运输方式(10)")
    @TableField("YSFS")
    @DictField(queryKey = "OMS_BD_YSFS")
    private String ysfs;

    /**
    * 核注清单主管海关/保税区名称/清关地点
    */
    @ApiModelProperty("核注清单主管海关/保税区名称/清关地点(10)")
    @TableField("CUSTOM_CODE")
    @DictField(queryKey = "OMS_BD_CUSTOMS")
    private String customCode;

    /**
    * 箱单号
    */
    @ApiModelProperty("箱单号(2,000)")
    @TableField("XDH")
    private String xdh;

    /**
    * 其他参考号
    */
    @ApiModelProperty("其他参考号(50)")
    @TableField("QTCKH")
    private String qtckh;

    /**
    * 报检类型
    */
    @ApiModelProperty("报检类型(20)")
    @TableField("BJ_TYPE")
    private String bjType;

    /**
    * 企业运输方式
    */
    @ApiModelProperty("企业运输方式(20)")
    @TableField("YSFS_COMPANY")
    @DictField(queryKey = "OMS_BD_BGYSFS")
    private String ysfsCompany;

    /**
    * 货代/厂商
    */
    @ApiModelProperty("货代/厂商(20)")
    @TableField("HDCS")
    @DictField(queryKey = "CRM_CUSTOMER_KH")
    private String hdcs;

    /**
    * 企业报关类型
    */
    @ApiModelProperty("企业报关类型(20)")
    @TableField("BG_TYPE_COMPANY")
    @DictField(queryKey = "OMS_BD_BGLX")
    private String bgTypeCompany;

    /**
    * 区外企业
    */
    @ApiModelProperty("区外企业(100)")
    @TableField("QW_COMPANY")
    @DictField(queryKey = "CRM_CUSTOMER_HGBM")
    private String qwCompany;

    /**
    * 报关单申报地海关
    */
    @ApiModelProperty("报关单申报地海关(10)")
    @TableField("BGD_CUSTOM_CODE")
    @DictField(queryKey = "OMS_BD_CUSTOMS")
    private String bgdCustomCode;

    @ApiModelProperty("报关单是否平台申报(10)")
    @TableField("IS_PTSB")
    private Integer isPtsb;

    @ApiModelProperty("核注清单是否平台申报(10)")
    @TableField("IS_PTSB_HZQD")
    private Integer isPtsbHzqd;

    @ApiModelProperty("出入库单是否平台申报(10)")
    @TableField("IS_PTSB_CRKD")
    private Integer isPtsbCrkd;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public OmsOrderFwxmBgbjEntity() {
        this.setSubClazz(OmsOrderFwxmBgbjEntity.class);
    }

    public OmsOrderFwxmBgbjEntity setPreNo(String preNo) {
        this.preNo = preNo;
        this.nodifySetFiled("preNo", preNo);
        return this;
    }

    public String getPreNo() {
        this.nodifyGetFiled("preNo");
        return preNo;
    }

    public OmsOrderFwxmBgbjEntity setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        this.nodifySetFiled("orderNo", orderNo);
        return this;
    }

    public String getOrderNo() {
        this.nodifyGetFiled("orderNo");
        return orderNo;
    }

    public OmsOrderFwxmBgbjEntity setBgbjNo(String bgbjNo) {
        this.bgbjNo = bgbjNo;
        this.nodifySetFiled("bgbjNo", bgbjNo);
        return this;
    }

    public String getBgbjNo() {
        this.nodifyGetFiled("bgbjNo");
        return bgbjNo;
    }

    public OmsOrderFwxmBgbjEntity setFwlxCode(String fwlxCode) {
        this.fwlxCode = fwlxCode;
        this.nodifySetFiled("fwlxCode", fwlxCode);
        return this;
    }

    public String getFwlxCode() {
        this.nodifyGetFiled("fwlxCode");
        return fwlxCode;
    }

    public OmsOrderFwxmBgbjEntity setFwxmCode(String fwxmCode) {
        this.fwxmCode = fwxmCode;
        this.nodifySetFiled("fwxmCode", fwxmCode);
        return this;
    }

    public String getFwxmCode() {
        this.nodifyGetFiled("fwxmCode");
        return fwxmCode;
    }

    public OmsOrderFwxmBgbjEntity setiEType(String iEType) {
        this.iEType = iEType;
        this.nodifySetFiled("iEType", iEType);
        return this;
    }

    public String getiEType() {
        this.nodifyGetFiled("iEType");
        return iEType;
    }

    public OmsOrderFwxmBgbjEntity setTradeMode(String tradeMode) {
        this.tradeMode = tradeMode;
        this.nodifySetFiled("tradeMode", tradeMode);
        return this;
    }

    public String getTradeMode() {
        this.nodifyGetFiled("tradeMode");
        return tradeMode;
    }

    public OmsOrderFwxmBgbjEntity setCjtk(String cjtk) {
        this.cjtk = cjtk;
        this.nodifySetFiled("cjtk", cjtk);
        return this;
    }

    public String getCjtk() {
        this.nodifyGetFiled("cjtk");
        return cjtk;
    }

    public OmsOrderFwxmBgbjEntity setWorkNo(String workNo) {
        this.workNo = workNo;
        this.nodifySetFiled("workNo", workNo);
        return this;
    }

    public String getWorkNo() {
        this.nodifyGetFiled("workNo");
        return workNo;
    }

    public OmsOrderFwxmBgbjEntity setJnshr(String jnshr) {
        this.jnshr = jnshr;
        this.nodifySetFiled("jnshr", jnshr);
        return this;
    }

    public String getJnshr() {
        this.nodifyGetFiled("jnshr");
        return jnshr;
    }

    public OmsOrderFwxmBgbjEntity setJnfhr(String jnfhr) {
        this.jnfhr = jnfhr;
        this.nodifySetFiled("jnfhr", jnfhr);
        return this;
    }

    public String getJnfhr() {
        this.nodifyGetFiled("jnfhr");
        return jnfhr;
    }

    public OmsOrderFwxmBgbjEntity setiEPort(String iEPort) {
        this.iEPort = iEPort;
        this.nodifySetFiled("iEPort", iEPort);
        return this;
    }

    public String getiEPort() {
        this.nodifyGetFiled("iEPort");
        return iEPort;
    }

    public OmsOrderFwxmBgbjEntity setTradeCode(String tradeCode) {
        this.tradeCode = tradeCode;
        this.nodifySetFiled("tradeCode", tradeCode);
        return this;
    }

    public String getTradeCode() {
        this.nodifyGetFiled("tradeCode");
        return tradeCode;
    }

    public OmsOrderFwxmBgbjEntity setEnrolno(String enrolno) {
        this.enrolno = enrolno;
        this.nodifySetFiled("enrolno", enrolno);
        return this;
    }

    public String getEnrolno() {
        this.nodifyGetFiled("enrolno");
        return enrolno;
    }

    public OmsOrderFwxmBgbjEntity setEntyportCode(String entyportCode) {
        this.entyportCode = entyportCode;
        this.nodifySetFiled("entyportCode", entyportCode);
        return this;
    }

    public String getEntyportCode() {
        this.nodifyGetFiled("entyportCode");
        return entyportCode;
    }

    public OmsOrderFwxmBgbjEntity setCjfs(String cjfs) {
        this.cjfs = cjfs;
        this.nodifySetFiled("cjfs", cjfs);
        return this;
    }

    public String getCjfs() {
        this.nodifyGetFiled("cjfs");
        return cjfs;
    }

    public OmsOrderFwxmBgbjEntity setOriginCode(String originCode) {
        this.originCode = originCode;
        this.nodifySetFiled("originCode", originCode);
        return this;
    }

    public String getOriginCode() {
        this.nodifyGetFiled("originCode");
        return originCode;
    }

    public OmsOrderFwxmBgbjEntity setDestinationCountry(String destinationCountry) {
        this.destinationCountry = destinationCountry;
        this.nodifySetFiled("destinationCountry", destinationCountry);
        return this;
    }

    public String getDestinationCountry() {
        this.nodifyGetFiled("destinationCountry");
        return destinationCountry;
    }

    public OmsOrderFwxmBgbjEntity setCyMemo(String cyMemo) {
        this.cyMemo = cyMemo;
        this.nodifySetFiled("cyMemo", cyMemo);
        return this;
    }

    public String getCyMemo() {
        this.nodifyGetFiled("cyMemo");
        return cyMemo;
    }

    public OmsOrderFwxmBgbjEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public OmsOrderFwxmBgbjEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public OmsOrderFwxmBgbjEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public OmsOrderFwxmBgbjEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public OmsOrderFwxmBgbjEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public OmsOrderFwxmBgbjEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public OmsOrderFwxmBgbjEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public OmsOrderFwxmBgbjEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public OmsOrderFwxmBgbjEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public OmsOrderFwxmBgbjEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public OmsOrderFwxmBgbjEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public OmsOrderFwxmBgbjEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public OmsOrderFwxmBgbjEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public OmsOrderFwxmBgbjEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public OmsOrderFwxmBgbjEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public OmsOrderFwxmBgbjEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public OmsOrderFwxmBgbjEntity setSupervision(String supervision) {
        this.supervision = supervision;
        this.nodifySetFiled("supervision", supervision);
        return this;
    }

    public String getSupervision() {
        this.nodifyGetFiled("supervision");
        return supervision;
    }

    public OmsOrderFwxmBgbjEntity setXqdh(String xqdh) {
        this.xqdh = xqdh;
        this.nodifySetFiled("xqdh", xqdh);
        return this;
    }

    public String getXqdh() {
        this.nodifyGetFiled("xqdh");
        return xqdh;
    }

    public OmsOrderFwxmBgbjEntity setDecNo(String decNo) {
        this.decNo = decNo;
        this.nodifySetFiled("decNo", decNo);
        return this;
    }

    public String getDecNo() {
        this.nodifyGetFiled("decNo");
        return decNo;
    }

    public OmsOrderFwxmBgbjEntity setBhType(String bhType) {
        this.bhType = bhType;
        this.nodifySetFiled("bhType", bhType);
        return this;
    }

    public String getBhType() {
        this.nodifyGetFiled("bhType");
        return bhType;
    }

    public OmsOrderFwxmBgbjEntity setHxDateBegin(Date hxDateBegin) {
        this.hxDateBegin = hxDateBegin;
        this.nodifySetFiled("hxDateBegin", hxDateBegin);
        return this;
    }

    public Date getHxDateBegin() {
        this.nodifyGetFiled("hxDateBegin");
        return hxDateBegin;
    }

    public OmsOrderFwxmBgbjEntity setHxDateBeginStart(Date hxDateBeginStart) {
        this.hxDateBeginStart = hxDateBeginStart;
        this.nodifySetFiled("hxDateBeginStart", hxDateBeginStart);
        return this;
    }

    public Date getHxDateBeginStart() {
        this.nodifyGetFiled("hxDateBeginStart");
        return hxDateBeginStart;
    }

    public OmsOrderFwxmBgbjEntity setHxDateBeginEnd(Date hxDateBeginEnd) {
        this.hxDateBeginEnd = hxDateBeginEnd;
        this.nodifySetFiled("hxDateBeginEnd", hxDateBeginEnd);
        return this;
    }

    public Date getHxDateBeginEnd() {
        this.nodifyGetFiled("hxDateBeginEnd");
        return hxDateBeginEnd;
    }
    public OmsOrderFwxmBgbjEntity setSgdlx(String sgdlx) {
        this.sgdlx = sgdlx;
        this.nodifySetFiled("sgdlx", sgdlx);
        return this;
    }

    public String getSgdlx() {
        this.nodifyGetFiled("sgdlx");
        return sgdlx;
    }

    public OmsOrderFwxmBgbjEntity setGlOrderNo(String glOrderNo) {
        this.glOrderNo = glOrderNo;
        this.nodifySetFiled("glOrderNo", glOrderNo);
        return this;
    }

    public String getGlOrderNo() {
        this.nodifyGetFiled("glOrderNo");
        return glOrderNo;
    }

    public OmsOrderFwxmBgbjEntity setSgdyy(String sgdyy) {
        this.sgdyy = sgdyy;
        this.nodifySetFiled("sgdyy", sgdyy);
        return this;
    }

    public String getSgdyy() {
        this.nodifyGetFiled("sgdyy");
        return sgdyy;
    }

    public OmsOrderFwxmBgbjEntity setUnit(String unit) {
        this.unit = unit;
        this.nodifySetFiled("unit", unit);
        return this;
    }

    public String getUnit() {
        this.nodifyGetFiled("unit");
        return unit;
    }

    public OmsOrderFwxmBgbjEntity setQty(Integer qty) {
        this.qty = qty;
        this.nodifySetFiled("qty", qty);
        return this;
    }

    public Integer getQty() {
        this.nodifyGetFiled("qty");
        return qty;
    }

    public OmsOrderFwxmBgbjEntity setYjyDate(Date yjyDate) {
        this.yjyDate = yjyDate;
        this.nodifySetFiled("yjyDate", yjyDate);
        return this;
    }

    public Date getYjyDate() {
        this.nodifyGetFiled("yjyDate");
        return yjyDate;
    }

    public OmsOrderFwxmBgbjEntity setYjyDateStart(Date yjyDateStart) {
        this.yjyDateStart = yjyDateStart;
        this.nodifySetFiled("yjyDateStart", yjyDateStart);
        return this;
    }

    public Date getYjyDateStart() {
        this.nodifyGetFiled("yjyDateStart");
        return yjyDateStart;
    }

    public OmsOrderFwxmBgbjEntity setYjyDateEnd(Date yjyDateEnd) {
        this.yjyDateEnd = yjyDateEnd;
        this.nodifySetFiled("yjyDateEnd", yjyDateEnd);
        return this;
    }

    public Date getYjyDateEnd() {
        this.nodifyGetFiled("yjyDateEnd");
        return yjyDateEnd;
    }
    public OmsOrderFwxmBgbjEntity setYjyAddress(String yjyAddress) {
        this.yjyAddress = yjyAddress;
        this.nodifySetFiled("yjyAddress", yjyAddress);
        return this;
    }

    public String getYjyAddress() {
        this.nodifyGetFiled("yjyAddress");
        return yjyAddress;
    }

    public OmsOrderFwxmBgbjEntity setKhLinkMan(String khLinkMan) {
        this.khLinkMan = khLinkMan;
        this.nodifySetFiled("khLinkMan", khLinkMan);
        return this;
    }

    public String getKhLinkMan() {
        this.nodifyGetFiled("khLinkMan");
        return khLinkMan;
    }

    public OmsOrderFwxmBgbjEntity setKhTel(String khTel) {
        this.khTel = khTel;
        this.nodifySetFiled("khTel", khTel);
        return this;
    }

    public String getKhTel() {
        this.nodifyGetFiled("khTel");
        return khTel;
    }

    public OmsOrderFwxmBgbjEntity setCiqDate(Date ciqDate) {
        this.ciqDate = ciqDate;
        this.nodifySetFiled("ciqDate", ciqDate);
        return this;
    }

    public Date getCiqDate() {
        this.nodifyGetFiled("ciqDate");
        return ciqDate;
    }

    public OmsOrderFwxmBgbjEntity setCiqDateStart(Date ciqDateStart) {
        this.ciqDateStart = ciqDateStart;
        this.nodifySetFiled("ciqDateStart", ciqDateStart);
        return this;
    }

    public Date getCiqDateStart() {
        this.nodifyGetFiled("ciqDateStart");
        return ciqDateStart;
    }

    public OmsOrderFwxmBgbjEntity setCiqDateEnd(Date ciqDateEnd) {
        this.ciqDateEnd = ciqDateEnd;
        this.nodifySetFiled("ciqDateEnd", ciqDateEnd);
        return this;
    }

    public Date getCiqDateEnd() {
        this.nodifyGetFiled("ciqDateEnd");
        return ciqDateEnd;
    }
    public OmsOrderFwxmBgbjEntity setCiqLinkMan(String ciqLinkMan) {
        this.ciqLinkMan = ciqLinkMan;
        this.nodifySetFiled("ciqLinkMan", ciqLinkMan);
        return this;
    }

    public String getCiqLinkMan() {
        this.nodifyGetFiled("ciqLinkMan");
        return ciqLinkMan;
    }

    public OmsOrderFwxmBgbjEntity setCiqTel(String ciqTel) {
        this.ciqTel = ciqTel;
        this.nodifySetFiled("ciqTel", ciqTel);
        return this;
    }

    public String getCiqTel() {
        this.nodifyGetFiled("ciqTel");
        return ciqTel;
    }

    public OmsOrderFwxmBgbjEntity setDjmc(String djmc) {
        this.djmc = djmc;
        this.nodifySetFiled("djmc", djmc);
        return this;
    }

    public String getDjmc() {
        this.nodifyGetFiled("djmc");
        return djmc;
    }

    public OmsOrderFwxmBgbjEntity setZcAddress(String zcAddress) {
        this.zcAddress = zcAddress;
        this.nodifySetFiled("zcAddress", zcAddress);
        return this;
    }

    public String getZcAddress() {
        this.nodifyGetFiled("zcAddress");
        return zcAddress;
    }

    public OmsOrderFwxmBgbjEntity setZrAddress(String zrAddress) {
        this.zrAddress = zrAddress;
        this.nodifySetFiled("zrAddress", zrAddress);
        return this;
    }

    public String getZrAddress() {
        this.nodifyGetFiled("zrAddress");
        return zrAddress;
    }

    public OmsOrderFwxmBgbjEntity setHxDateEnd(Date hxDateEnd) {
        this.hxDateEnd = hxDateEnd;
        this.nodifySetFiled("hxDateEnd", hxDateEnd);
        return this;
    }

    public Date getHxDateEnd() {
        this.nodifyGetFiled("hxDateEnd");
        return hxDateEnd;
    }

    public OmsOrderFwxmBgbjEntity setHxDateEndStart(Date hxDateEndStart) {
        this.hxDateEndStart = hxDateEndStart;
        this.nodifySetFiled("hxDateEndStart", hxDateEndStart);
        return this;
    }

    public Date getHxDateEndStart() {
        this.nodifyGetFiled("hxDateEndStart");
        return hxDateEndStart;
    }

    public OmsOrderFwxmBgbjEntity setHxDateEndEnd(Date hxDateEndEnd) {
        this.hxDateEndEnd = hxDateEndEnd;
        this.nodifySetFiled("hxDateEndEnd", hxDateEndEnd);
        return this;
    }

    public Date getHxDateEndEnd() {
        this.nodifyGetFiled("hxDateEndEnd");
        return hxDateEndEnd;
    }
    public OmsOrderFwxmBgbjEntity setZcCustom(String zcCustom) {
        this.zcCustom = zcCustom;
        this.nodifySetFiled("zcCustom", zcCustom);
        return this;
    }

    public String getZcCustom() {
        this.nodifyGetFiled("zcCustom");
        return zcCustom;
    }

    public OmsOrderFwxmBgbjEntity setZrCustom(String zrCustom) {
        this.zrCustom = zrCustom;
        this.nodifySetFiled("zrCustom", zrCustom);
        return this;
    }

    public String getZrCustom() {
        this.nodifyGetFiled("zrCustom");
        return zrCustom;
    }

    public OmsOrderFwxmBgbjEntity setYsfs(String ysfs) {
        this.ysfs = ysfs;
        this.nodifySetFiled("ysfs", ysfs);
        return this;
    }

    public String getYsfs() {
        this.nodifyGetFiled("ysfs");
        return ysfs;
    }

    public OmsOrderFwxmBgbjEntity setCustomCode(String customCode) {
        this.customCode = customCode;
        this.nodifySetFiled("customCode", customCode);
        return this;
    }

    public String getCustomCode() {
        this.nodifyGetFiled("customCode");
        return customCode;
    }

    public OmsOrderFwxmBgbjEntity setXdh(String xdh) {
        this.xdh = xdh;
        this.nodifySetFiled("xdh", xdh);
        return this;
    }

    public String getXdh() {
        this.nodifyGetFiled("xdh");
        return xdh;
    }

    public OmsOrderFwxmBgbjEntity setQtckh(String qtckh) {
        this.qtckh = qtckh;
        this.nodifySetFiled("qtckh", qtckh);
        return this;
    }

    public String getQtckh() {
        this.nodifyGetFiled("qtckh");
        return qtckh;
    }

    public OmsOrderFwxmBgbjEntity setBjType(String bjType) {
        this.bjType = bjType;
        this.nodifySetFiled("bjType", bjType);
        return this;
    }

    public String getBjType() {
        this.nodifyGetFiled("bjType");
        return bjType;
    }

    public OmsOrderFwxmBgbjEntity setYsfsCompany(String ysfsCompany) {
        this.ysfsCompany = ysfsCompany;
        this.nodifySetFiled("ysfsCompany", ysfsCompany);
        return this;
    }

    public String getYsfsCompany() {
        this.nodifyGetFiled("ysfsCompany");
        return ysfsCompany;
    }

    public OmsOrderFwxmBgbjEntity setHdcs(String hdcs) {
        this.hdcs = hdcs;
        this.nodifySetFiled("hdcs", hdcs);
        return this;
    }

    public String getHdcs() {
        this.nodifyGetFiled("hdcs");
        return hdcs;
    }

    public OmsOrderFwxmBgbjEntity setBgTypeCompany(String bgTypeCompany) {
        this.bgTypeCompany = bgTypeCompany;
        this.nodifySetFiled("bgTypeCompany", bgTypeCompany);
        return this;
    }

    public String getBgTypeCompany() {
        this.nodifyGetFiled("bgTypeCompany");
        return bgTypeCompany;
    }

    public OmsOrderFwxmBgbjEntity setQwCompany(String qwCompany) {
        this.qwCompany = qwCompany;
        this.nodifySetFiled("qwCompany", qwCompany);
        return this;
    }

    public String getQwCompany() {
        this.nodifyGetFiled("qwCompany");
        return qwCompany;
    }

    public OmsOrderFwxmBgbjEntity setBgdCustomCode(String bgdCustomCode) {
        this.bgdCustomCode = bgdCustomCode;
        this.nodifySetFiled("bgdCustomCode", bgdCustomCode);
        return this;
    }

    public String getBgdCustomCode() {
        this.nodifyGetFiled("bgdCustomCode");
        return bgdCustomCode;
    }

    public OmsOrderFwxmBgbjEntity setIsPtsb(Integer isPtsb) {
        this.isPtsb = isPtsb;
        this.nodifySetFiled("isPtsb", isPtsb);
        return this;
    }

    public Integer getIsPtsb() {
        this.nodifyGetFiled("isPtsb");
        return isPtsb;
    }

    public Integer getIsPtsbHzqd() {
        this.nodifyGetFiled("isPtsbHzqd");
        return isPtsbHzqd;
    }

    public OmsOrderFwxmBgbjEntity setIsPtsbHzqd(Integer isPtsbHzqd) {
        this.isPtsbHzqd = isPtsbHzqd;
        this.nodifySetFiled("isPtsbHzqd", isPtsbHzqd);
        return this;
    }

    public Integer getIsPtsbCrkd() {
        this.nodifyGetFiled("isPtsbCrkd");
        return isPtsbCrkd;
    }

    public OmsOrderFwxmBgbjEntity setIsPtsbCrkd(Integer isPtsbCrkd) {
        this.isPtsbCrkd = isPtsbCrkd;
        this.nodifySetFiled("isPtsbCrkd", isPtsbCrkd);
        return this;
    }
}
