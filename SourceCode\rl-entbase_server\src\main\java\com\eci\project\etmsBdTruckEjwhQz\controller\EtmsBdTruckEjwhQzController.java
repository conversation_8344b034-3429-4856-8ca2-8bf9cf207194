package com.eci.project.etmsBdTruckEjwhQz.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.etmsBdTruckEjwhQz.service.EtmsBdTruckEjwhQzService;
import com.eci.project.etmsBdTruckEjwhQz.entity.EtmsBdTruckEjwhQzEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 车辆二级维护历史Controller
*
* @<NAME_EMAIL>
* @date 2025-04-16
*/
@Api(tags = "车辆二级维护历史")
@RestController
@RequestMapping("/etmsBdTruckEjwhQz")
public class EtmsBdTruckEjwhQzController extends EciBaseController {

    @Autowired
    private EtmsBdTruckEjwhQzService etmsBdTruckEjwhQzService;


    @ApiOperation("车辆二级维护历史:保存")
    @EciLog(title = "车辆二级维护历史:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody EtmsBdTruckEjwhQzEntity entity){
        EtmsBdTruckEjwhQzEntity etmsBdTruckEjwhQzEntity =etmsBdTruckEjwhQzService.save(entity);
        return ResponseMsgUtil.success(10001,etmsBdTruckEjwhQzEntity);
    }


    @ApiOperation("车辆二级维护历史:查询列表")
    @EciLog(title = "车辆二级维护历史:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody EtmsBdTruckEjwhQzEntity entity){
        List<EtmsBdTruckEjwhQzEntity> etmsBdTruckEjwhQzEntities = etmsBdTruckEjwhQzService.selectList(entity);
        return ResponseMsgUtil.success(10001,etmsBdTruckEjwhQzEntities);
    }


    @ApiOperation("车辆二级维护历史:分页查询列表")
    @EciLog(title = "车辆二级维护历史:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody EtmsBdTruckEjwhQzEntity entity){
        TgPageInfo tgPageInfo = etmsBdTruckEjwhQzService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("车辆二级维护历史:根据ID查一条")
    @EciLog(title = "车辆二级维护历史:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody EtmsBdTruckEjwhQzEntity entity){
        EtmsBdTruckEjwhQzEntity  etmsBdTruckEjwhQzEntity = etmsBdTruckEjwhQzService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,etmsBdTruckEjwhQzEntity);
    }


    @ApiOperation("车辆二级维护历史:根据ID删除一条")
    @EciLog(title = "车辆二级维护历史:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody EtmsBdTruckEjwhQzEntity entity){
        int count = etmsBdTruckEjwhQzService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("车辆二级维护历史:根据ID字符串删除多条")
    @EciLog(title = "车辆二级维护历史:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody EtmsBdTruckEjwhQzEntity entity) {
        int count = etmsBdTruckEjwhQzService.deleteByList(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}