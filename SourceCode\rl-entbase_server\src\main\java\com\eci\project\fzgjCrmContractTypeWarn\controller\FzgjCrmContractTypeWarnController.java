package com.eci.project.fzgjCrmContractTypeWarn.controller;

import com.eci.common.ResponseMsgUtilX;
import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjCrmContractTypeWarn.service.FzgjCrmContractTypeWarnService;
import com.eci.project.fzgjCrmContractTypeWarn.entity.FzgjCrmContractTypeWarnEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 合同种类及预警时效Controller
*
* @<NAME_EMAIL>
* @date 2025-04-10
*/
@Api(tags = "合同种类及预警时效")
@RestController
@RequestMapping("/fzgjCrmContractTypeWarn")
public class FzgjCrmContractTypeWarnController extends EciBaseController {

    @Autowired
    private FzgjCrmContractTypeWarnService fzgjCrmContractTypeWarnService;


    @ApiOperation("合同种类及预警时效:保存")
    @EciLog(title = "合同种类及预警时效:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody FzgjCrmContractTypeWarnEntity entity){
        FzgjCrmContractTypeWarnEntity fzgjCrmContractTypeWarnEntity =fzgjCrmContractTypeWarnService.save(entity);
        return ResponseMsgUtil.success(10001,fzgjCrmContractTypeWarnEntity);
    }


    @ApiOperation("合同种类及预警时效:查询列表")
    @EciLog(title = "合同种类及预警时效:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjCrmContractTypeWarnEntity entity){
        List<FzgjCrmContractTypeWarnEntity> fzgjCrmContractTypeWarnEntities = fzgjCrmContractTypeWarnService.selectList(entity);
        return ResponseMsgUtil.success(10001,fzgjCrmContractTypeWarnEntities);
    }


    @ApiOperation("合同种类及预警时效:分页查询列表")
    @EciLog(title = "合同种类及预警时效:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjCrmContractTypeWarnEntity entity){
        TgPageInfo tgPageInfo = fzgjCrmContractTypeWarnService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("合同种类及预警时效:根据ID查一条")
    @EciLog(title = "合同种类及预警时效:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjCrmContractTypeWarnEntity entity){
        FzgjCrmContractTypeWarnEntity  fzgjCrmContractTypeWarnEntity = fzgjCrmContractTypeWarnService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,fzgjCrmContractTypeWarnEntity);
    }


    @ApiOperation("合同种类及预警时效:根据ID删除一条")
    @EciLog(title = "合同种类及预警时效:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjCrmContractTypeWarnEntity entity){
        int count = fzgjCrmContractTypeWarnService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("合同种类及预警时效:根据ID字符串删除多条")
    @EciLog(title = "合同种类及预警时效:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjCrmContractTypeWarnEntity entity) {
        int count = fzgjCrmContractTypeWarnService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}