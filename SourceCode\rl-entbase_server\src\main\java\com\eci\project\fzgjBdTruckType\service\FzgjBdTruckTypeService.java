package com.eci.project.fzgjBdTruckType.service;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjBdTruckType.dao.FzgjBdTruckTypeDao;
import com.eci.project.fzgjBdTruckType.entity.FzgjBdTruckTypeEntity;
import com.eci.project.fzgjBdTruckType.validate.FzgjBdTruckTypeVal;

import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 车辆类型Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-03-14
*/
@Service
@Slf4j
public class FzgjBdTruckTypeService implements EciBaseService<FzgjBdTruckTypeEntity> {

    @Autowired
    private FzgjBdTruckTypeDao fzgjBdTruckTypeDao;

    @Autowired
    private FzgjBdTruckTypeVal fzgjBdTruckTypeVal;


    @Override
    public TgPageInfo queryPageList(FzgjBdTruckTypeEntity entity) {
        EciQuery<FzgjBdTruckTypeEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjBdTruckTypeEntity> entities = fzgjBdTruckTypeDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjBdTruckTypeEntity save(FzgjBdTruckTypeEntity entity) {
        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setGuid(IdWorker.getIdStr());
            entity.setCreateDate(new java.util.Date());
            entity.setUpdateDate(new java.util.Date());
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            entity.setCompanyName(UserContext.getUserInfo().getCompanyName());
            entity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
        } else {
            entity.setUpdateDate(new java.util.Date());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        }
        // 返回实体对象
        FzgjBdTruckTypeEntity fzgjBdTruckTypeEntity = null;
        fzgjBdTruckTypeVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjBdTruckTypeEntity = fzgjBdTruckTypeDao.insertOne(entity);

        }else{

            fzgjBdTruckTypeEntity = fzgjBdTruckTypeDao.updateByEntityId(entity);

        }
        return fzgjBdTruckTypeEntity;
    }

    @Override
    public List<FzgjBdTruckTypeEntity> selectList(FzgjBdTruckTypeEntity entity) {
        return fzgjBdTruckTypeDao.selectList(entity);
    }

    @Override
    public FzgjBdTruckTypeEntity selectOneById(Serializable id) {
        return fzgjBdTruckTypeDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjBdTruckTypeEntity> list) {
        fzgjBdTruckTypeDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjBdTruckTypeDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjBdTruckTypeDao.deleteById(id);
    }

}