package com.eci.project.fzgjBdOmsPages.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjBdOmsPages.entity.FzgjBdOmsPagesEntity;

import org.springframework.stereotype.Service;


/**
* 录入订单编辑页面Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-03-19
*/
@Service
public class FzgjBdOmsPagesVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjBdOmsPagesEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjBdOmsPagesEntity entity, BusinessType businessType) {

    }

}
