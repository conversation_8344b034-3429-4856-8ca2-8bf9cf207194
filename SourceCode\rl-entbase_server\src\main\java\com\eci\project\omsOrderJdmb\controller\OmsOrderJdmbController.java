package com.eci.project.omsOrderJdmb.controller;

import com.eci.common.ResponseMsgUtilX;
import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.omsOrderJdmb.service.OmsOrderJdmbService;
import com.eci.project.omsOrderJdmb.entity.OmsOrderJdmbEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 接单模板Controller
*
* @<NAME_EMAIL>
* @date 2025-04-24
*/
@Api(tags = "接单模板")
@RestController
@RequestMapping("/omsOrderJdmb")
public class OmsOrderJdmbController extends EciBaseController {

    @Autowired
    private OmsOrderJdmbService omsOrderJdmbService;


    @ApiOperation("接单模板:保存")
    @EciLog(title = "接单模板:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody OmsOrderJdmbEntity entity){
        OmsOrderJdmbEntity omsOrderJdmbEntity =omsOrderJdmbService.save(entity);
        return ResponseMsgUtil.success(10001,omsOrderJdmbEntity);
    }


    @ApiOperation("接单模板:查询列表")
    @EciLog(title = "接单模板:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody OmsOrderJdmbEntity entity){
        List<OmsOrderJdmbEntity> omsOrderJdmbEntities = omsOrderJdmbService.selectList(entity);
        return ResponseMsgUtil.success(10001,omsOrderJdmbEntities);
    }


    @ApiOperation("接单模板:分页查询列表")
    @EciLog(title = "接单模板:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody OmsOrderJdmbEntity entity){
        TgPageInfo tgPageInfo = omsOrderJdmbService.queryPageList(entity);
        return ResponseMsgUtilX.success(10001,tgPageInfo);
    }


    @ApiOperation("接单模板:根据ID查一条")
    @EciLog(title = "接单模板:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody OmsOrderJdmbEntity entity){
        OmsOrderJdmbEntity  omsOrderJdmbEntity = omsOrderJdmbService.selectOneById(entity.getJdmbNo());
        return ResponseMsgUtil.success(10001,omsOrderJdmbEntity);
    }


    @ApiOperation("接单模板:根据ID删除一条")
    @EciLog(title = "接单模板:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody OmsOrderJdmbEntity entity){
        int count = omsOrderJdmbService.deleteById(entity.getJdmbNo());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("接单模板:根据ID字符串删除多条")
    @EciLog(title = "接单模板:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody OmsOrderJdmbEntity entity) {
        int count = omsOrderJdmbService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}