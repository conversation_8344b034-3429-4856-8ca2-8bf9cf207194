package com.eci.project.omsOrder.controller;

import com.eci.common.ResponseMsgUtilX;
import com.eci.common.ZsrJson;
import com.eci.common.enums.OrderEnum;
import com.eci.common.util.ResponseMsg;
import com.eci.common.util.ResponseMsgUtil;
import com.eci.crud.controller.EciBaseController;
import com.eci.log.annotation.EciAction;
import com.eci.log.annotation.EciLog;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjFileType.entity.FzgjFileTypeEntity;
import com.eci.project.fzgjFileType.service.FzgjFileTypeService;
import com.eci.project.omsOrder.entity.OmsOrderEntity;
import com.eci.project.omsOrder.entity.RequestOmsOrderTracePageEntity;
import com.eci.project.omsOrder.service.OmsOrderCustomService;
import com.eci.project.omsOrder.service.OmsOrderService;
import com.eci.project.omsOrder.service.OmsOrderTraceService;
import com.eci.project.omsOrder.service.SendOrderService;
import com.eci.project.omsOrderFwxmWork.entity.OmsOrderFwxmWorkEntity;
import com.eci.project.omsOrderFwxmWork.service.XieZuoFangAnService;
import com.eci.project.omsOrderFwxmWorkTrace.entity.OmsOrderFwxmWorkInfoEntity;
import com.eci.project.omsOrderPre.entity.OmsOrderPreEntity;
import com.eci.wu.core.EntityBase;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 订单表Controller
 *
 * @<NAME_EMAIL>
 * @date 2025-04-14
 */
@Api(tags = "订单表")
@RestController
@RequestMapping("/omsOrder")
public class OmsOrderController extends EciBaseController {

    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private OmsOrderCustomService omsOrderCustomService;

    @Autowired
    private OmsOrderTraceService omsOrderTraceService;
    @Autowired
    private SendOrderService sendOrderService;

    /**
     * 协作方案
     */
    @Autowired
    private XieZuoFangAnService xieZuoFangAnService;

    @ApiOperation("订单表:保存")
    @EciLog(title = "订单表:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody OmsOrderEntity entity) {
        OmsOrderEntity omsOrderEntity = omsOrderService.save(entity);
        return ResponseMsgUtil.success(10001, omsOrderEntity);
    }

    @ApiOperation("订单表:订单模板")
    @EciLog(title = "订单表:订单模板", businessType = BusinessType.INSERT)
    @PostMapping("/saveTemplate")
    @EciAction()
    public ResponseMsg saveTemplate(@RequestBody OmsOrderEntity entity) {
        OmsOrderEntity omsOrderEntity = omsOrderService.saveTemplate(entity);
        return ResponseMsgUtil.success(10001, omsOrderEntity);
    }

    @ApiOperation("订单表:确认接单")
    @EciLog(title = "订单表:新增", businessType = BusinessType.INSERT)
    @PostMapping("/orderStatusSave")
    @EciAction()
    public ResponseMsg OrderStatusSave(@RequestBody String jsonString) {
        ZsrJson zsrJson = ZsrJson.parse(jsonString);
        List<String> listOrderNo = zsrJson.check("orderNos").getStringList("orderNos");
        String cancelRemark = zsrJson.getString("cancelRemark");

        omsOrderCustomService.OrderStatusSave(OrderEnum.OrderOpType.QRJD, listOrderNo, cancelRemark);
        OmsOrderEntity omsOrderEntity = omsOrderService.selectOneById(listOrderNo.get(0));
//        OmsOrderEntity omsOrderEntity = omsOrderService.save(entity);
        return ResponseMsgUtilX.success(10001, omsOrderEntity);
    }

    @ApiOperation("订单表:分发")
    @EciLog(title = "订单表:分发", businessType = BusinessType.INSERT)
    @PostMapping("/orderFenFa")
    @EciAction()
    public ResponseMsg orderFenFa(@RequestBody String jsonString) {
        OmsOrderEntity omsOrderEntity = sendOrderService.sendOrder(jsonString);

        return ResponseMsgUtilX.success(10001, omsOrderService.selectOneById(omsOrderEntity.getOrderNo()));
    }


    @ApiOperation("订单表:协作方案")
    @EciLog(title = "订单表:协作方案", businessType = BusinessType.INSERT)
    @PostMapping("/xieZuoFangAn")
    @EciAction()
    public ResponseMsg xieZuoFangAn(@RequestBody OmsOrderFwxmWorkEntity jsonString) {
        TgPageInfo tgPageInfo = xieZuoFangAnService.queryPageList(jsonString);
        return ResponseMsgUtilX.success(10001, tgPageInfo);
    }


    @ApiOperation("订单表:查询列表")
    @EciLog(title = "订单表:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody OmsOrderEntity entity) {
        List<OmsOrderEntity> omsOrderEntities = omsOrderService.selectList(entity);
        return ResponseMsgUtilX.success(10001, omsOrderEntities);
    }


    @ApiOperation("订单表:分页查询列表")
    @EciLog(title = "订单表:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody OmsOrderEntity entity) {
        TgPageInfo tgPageInfo = omsOrderService.queryPageList(entity);
        return ResponseMsgUtilX.success(10001, tgPageInfo);
    }


    @ApiOperation("订单表:根据ID查一条")
    @EciLog(title = "订单表:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody OmsOrderEntity entity) {
        OmsOrderEntity omsOrderEntity = omsOrderService.selectOneById(entity.getOrderNo());
        return ResponseMsgUtilX.success(10001, omsOrderEntity);
    }

    @ApiOperation("订单:获取客户自助下单数据")
    @EciLog(title = "订单:获取客户自助下单数据", businessType = BusinessType.SELECT)
    @PostMapping("/getOrderPre")
    @EciAction()
    public ResponseMsg getOrderPre(@RequestBody OmsOrderEntity entity) {
        OmsOrderPreEntity omsOrderEntity = omsOrderService.getOrderPre(entity.getOrderNo());
        return ResponseMsgUtilX.success(10001, omsOrderEntity);
    }


    @ApiOperation("订单表:根据ID删除一条")
    @EciLog(title = "订单表:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody OmsOrderEntity entity) {
        int count = omsOrderService.deleteById(entity.getOrderNo());
        return ResponseMsgUtilX.success(10001, count);
    }


    @ApiOperation("订单表:根据ID字符串删除多条")
    @EciLog(title = "订单表:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody OmsOrderEntity entity) {
        int count = omsOrderService.deleteByIds(entity.getIds());
        return ResponseMsgUtilX.success(10001, count);
    }


    @ApiOperation("作业跟踪及反馈:分页查询列表")
    @EciLog(title = "作业跟踪及反馈:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectTracePageList")
    @EciAction()
    public ResponseMsg selectTracePageList(@RequestBody RequestOmsOrderTracePageEntity entity) {
        TgPageInfo tgPageInfo = omsOrderTraceService.selectTracePageList(entity);
        return ResponseMsgUtilX.success(10001, tgPageInfo);
    }

    @ApiOperation("作业跟踪及反馈:跟踪及反馈详情")
    @EciLog(title = "作业跟踪及反馈:跟踪及反馈详情", businessType = BusinessType.SELECT)
    @PostMapping("/selectTraceLinkList")
    @EciAction()
    public ResponseMsg selectTraceLinkList(@RequestBody RequestOmsOrderTracePageEntity entity) {
        List<OmsOrderFwxmWorkInfoEntity> list = omsOrderTraceService.selectTraceLinkList(entity);
        TgPageInfo tgPageInfo = new TgPageInfo();
        tgPageInfo.setList(list);
        return ResponseMsgUtilX.success(10001, tgPageInfo);
    }

    @Autowired
    private FzgjFileTypeService fzgjFileTypeService;

    @ApiOperation("订单操作:获取附件类型")
    @EciLog(title = "订单操作:获取附件类型", businessType = BusinessType.SELECT)
    @PostMapping("/getFileTypeList")
    @EciAction()
    public ResponseMsg getFileTypeList(@RequestBody RequestOmsOrderTracePageEntity entity) {
        List<FzgjFileTypeEntity> list = fzgjFileTypeService.getFileTypeList();
        return ResponseMsgUtilX.success(10001, list);
    }

}