package com.eci.project.etmsOpHead.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.etmsOpHead.dao.EtmsOpHeadDao;
import com.eci.project.etmsOpHead.entity.EtmsOpHeadEntity;
import com.eci.project.etmsOpHead.validate.EtmsOpHeadVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 平台业务主表Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-06-13
*/
@Service
@Slf4j
public class EtmsOpHeadService implements EciBaseService<EtmsOpHeadEntity> {

    @Autowired
    private EtmsOpHeadDao etmsOpHeadDao;

    @Autowired
    private EtmsOpHeadVal etmsOpHeadVal;


    @Override
    public TgPageInfo queryPageList(EtmsOpHeadEntity entity) {
        EciQuery<EtmsOpHeadEntity> eciQuery = EciQuery.buildQuery(entity);
        List<EtmsOpHeadEntity> entities = etmsOpHeadDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public EtmsOpHeadEntity save(EtmsOpHeadEntity entity) {
        // 返回实体对象
        EtmsOpHeadEntity etmsOpHeadEntity = null;
        etmsOpHeadVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            etmsOpHeadEntity = etmsOpHeadDao.insertOne(entity);

        }else{

            etmsOpHeadEntity = etmsOpHeadDao.updateByEntityId(entity);

        }
        return etmsOpHeadEntity;
    }

    @Override
    public List<EtmsOpHeadEntity> selectList(EtmsOpHeadEntity entity) {
        return etmsOpHeadDao.selectList(entity);
    }

    @Override
    public EtmsOpHeadEntity selectOneById(Serializable id) {
        return etmsOpHeadDao.selectById(id);
    }


    @Override
    public void insertBatch(List<EtmsOpHeadEntity> list) {
        etmsOpHeadDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return etmsOpHeadDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return etmsOpHeadDao.deleteById(id);
    }

}