package com.eci.project.omsReceiveHistory.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 报文接收记录对象 OMS_RECEIVE_HISTORY
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-06-04
*/
@ApiModel("报文接收记录")
@TableName("OMS_RECEIVE_HISTORY")
@FieldNameConstants
public class OmsReceiveHistoryEntity extends EciBaseEntity{
    /**
    * 主键
    */
    @ApiModelProperty("主键(36)")
    @TableId("GUID")
    private String guid;

    /**
    * 报文内容
    */
    @ApiModelProperty("报文内容(3,000)")
    @TableField("JSONBODY")
    private String jsonbody;

    /**
    * 订单号
    */
    @ApiModelProperty("订单号(100)")
    @TableField("ORDERNO")
    private String orderno;

    /**
    * 协作单号
    */
    @ApiModelProperty("协作单号(100)")
    @TableField("WORKNO")
    private String workno;

    /**
    * 数据创建时间
    */
    @ApiModelProperty("数据创建时间(7)")
    @TableField("CREATEDATE")
    private Date createdate;

    @ApiModelProperty("数据创建时间开始")
    @TableField(exist=false)
    private Date createdateStart;

    @ApiModelProperty("数据创建时间结束")
    @TableField(exist=false)
    private Date createdateEnd;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public OmsReceiveHistoryEntity() {
        this.setSubClazz(OmsReceiveHistoryEntity.class);
    }

    public OmsReceiveHistoryEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public OmsReceiveHistoryEntity setJsonbody(String jsonbody) {
        this.jsonbody = jsonbody;
        this.nodifySetFiled("jsonbody", jsonbody);
        return this;
    }

    public String getJsonbody() {
        this.nodifyGetFiled("jsonbody");
        return jsonbody;
    }

    public OmsReceiveHistoryEntity setOrderno(String orderno) {
        this.orderno = orderno;
        this.nodifySetFiled("orderno", orderno);
        return this;
    }

    public String getOrderno() {
        this.nodifyGetFiled("orderno");
        return orderno;
    }

    public OmsReceiveHistoryEntity setWorkno(String workno) {
        this.workno = workno;
        this.nodifySetFiled("workno", workno);
        return this;
    }

    public String getWorkno() {
        this.nodifyGetFiled("workno");
        return workno;
    }

    public OmsReceiveHistoryEntity setCreatedate(Date createdate) {
        this.createdate = createdate;
        this.nodifySetFiled("createdate", createdate);
        return this;
    }

    public Date getCreatedate() {
        this.nodifyGetFiled("createdate");
        return createdate;
    }

    public OmsReceiveHistoryEntity setCreatedateStart(Date createdateStart) {
        this.createdateStart = createdateStart;
        this.nodifySetFiled("createdateStart", createdateStart);
        return this;
    }

    public Date getCreatedateStart() {
        this.nodifyGetFiled("createdateStart");
        return createdateStart;
    }

    public OmsReceiveHistoryEntity setCreatedateEnd(Date createdateEnd) {
        this.createdateEnd = createdateEnd;
        this.nodifySetFiled("createdateEnd", createdateEnd);
        return this;
    }

    public Date getCreatedateEnd() {
        this.nodifyGetFiled("createdateEnd");
        return createdateEnd;
    }
}
