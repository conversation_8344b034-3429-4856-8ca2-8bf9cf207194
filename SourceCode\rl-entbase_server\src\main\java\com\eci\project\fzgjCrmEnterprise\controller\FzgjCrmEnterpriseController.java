package com.eci.project.fzgjCrmEnterprise.controller;

import com.eci.common.ResponseMsgUtilX;
import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjCrmEnterprise.service.FzgjCrmEnterpriseService;
import com.eci.project.fzgjCrmEnterprise.entity.FzgjCrmEnterpriseEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 * 注册企业Controller
 *
 * @<NAME_EMAIL>
 * @date 2025-03-13
 */
@Api(tags = "注册企业")
@RestController
@RequestMapping("/fzgjCrmEnterprise")
public class FzgjCrmEnterpriseController extends EciBaseController {

    @Autowired
    private FzgjCrmEnterpriseService fzgjCrmEnterpriseService;


    @ApiOperation("注册企业:保存")
    @EciLog(title = "注册企业:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody FzgjCrmEnterpriseEntity entity) {
        FzgjCrmEnterpriseEntity fzgjCrmEnterpriseEntity = fzgjCrmEnterpriseService.save(entity);
        return ResponseMsgUtil.success(10001, fzgjCrmEnterpriseEntity);
    }

    @ApiOperation("注册企业:保存")
    @EciLog(title = "注册企业:新增", businessType = BusinessType.INSERT)
    @PostMapping("/selectAdd")
    @EciAction()
    public ResponseMsg selectAdd(@RequestBody String jsonString) {
        fzgjCrmEnterpriseService.selectAddBase(jsonString);
        return ResponseMsgUtil.success(10001, null);
    }

    @ApiOperation("注册企业:保存")
    @EciLog(title = "注册企业:新增", businessType = BusinessType.INSERT)
    @PostMapping("/selectRemove")
    @EciAction()
    public ResponseMsg selectRemove(@RequestBody String jsonString) {
        fzgjCrmEnterpriseService.selectRemoveBase(jsonString);
        return ResponseMsgUtil.success(10001, null);
    }


    @ApiOperation("注册企业:查询列表")
    @EciLog(title = "注册企业:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjCrmEnterpriseEntity entity) {
        List<FzgjCrmEnterpriseEntity> fzgjCrmEnterpriseEntities = fzgjCrmEnterpriseService.selectList(entity);
        return ResponseMsgUtil.success(10001, fzgjCrmEnterpriseEntities);
    }


    @ApiOperation("注册企业:分页查询列表")
    @EciLog(title = "注册企业:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjCrmEnterpriseEntity entity) {
        TgPageInfo tgPageInfo = fzgjCrmEnterpriseService.queryPageList(entity);
        return ResponseMsgUtilX.success(10001, tgPageInfo);
    }

    @ApiOperation("注册企业:分页查询列表")
    @EciLog(title = "注册企业:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageLeftTable")
    @EciAction()
    public ResponseMsg selectPageLeftTable(@RequestBody FzgjCrmEnterpriseEntity entity) {
        TgPageInfo tgPageInfo = fzgjCrmEnterpriseService.queryPageLeftTable(entity);
        return ResponseMsgUtilX.success(10001, tgPageInfo);
    }

    @ApiOperation("注册企业:分页查询列表")
    @EciLog(title = "注册企业:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageRightTable")
    @EciAction()
    public ResponseMsg selectPageRightTable(@RequestBody FzgjCrmEnterpriseEntity entity) {
        TgPageInfo tgPageInfo = fzgjCrmEnterpriseService.queryPageRightTable(entity);
        return ResponseMsgUtilX.success(10001, tgPageInfo);
    }


    @ApiOperation("注册企业:根据ID查一条")
    @EciLog(title = "注册企业:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjCrmEnterpriseEntity entity) {
        FzgjCrmEnterpriseEntity fzgjCrmEnterpriseEntity = fzgjCrmEnterpriseService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001, fzgjCrmEnterpriseEntity);
    }


    @ApiOperation("注册企业:根据ID删除一条")
    @EciLog(title = "注册企业:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjCrmEnterpriseEntity entity) {
        int count = fzgjCrmEnterpriseService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001, count);
    }


    @ApiOperation("注册企业:根据ID字符串删除多条")
    @EciLog(title = "注册企业:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjCrmEnterpriseEntity entity) {
        int count = fzgjCrmEnterpriseService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001, count);
    }


}