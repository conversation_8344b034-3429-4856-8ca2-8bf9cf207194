package com.eci.project.etmsCrmPartner.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.etmsCrmPartner.dao.EtmsCrmPartnerDao;
import com.eci.project.etmsCrmPartner.entity.EtmsCrmPartnerEntity;
import com.eci.project.etmsCrmPartner.validate.EtmsCrmPartnerVal;

import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-16
*/
@Service
@Slf4j
public class EtmsCrmPartnerService implements EciBaseService<EtmsCrmPartnerEntity> {

    @Autowired
    private EtmsCrmPartnerDao etmsCrmPartnerDao;

    @Autowired
    private EtmsCrmPartnerVal etmsCrmPartnerVal;


    @Override
    public TgPageInfo queryPageList(EtmsCrmPartnerEntity entity) {
        QueryWrapper eciQuery=new QueryWrapper();
        if(entity.getName()!=null&&!entity.getName().isEmpty())
            eciQuery.eq("A.NAME",entity.getName());
        if(entity.getCode()!=null&&!entity.getCode().isEmpty())
            eciQuery.eq("A.CODE",entity.getCode());
        eciQuery.eq("A.STATUS","Y");
        eciQuery.eq("A.GROUP_CODE", UserContext.getUserInfo().getCompanyCode());
        eciQuery.apply("(COMPANY_CODE={0} OR COMPANY_CODE='-')",UserContext.getUserInfo().getCompanyCode());
        eciQuery.apply("(NVL(A.IS_USER_CONTROL, 'N') = 'N' OR\n" +
                "(NVL(A.IS_USER_CONTROL, 'N') = 'Y'\n" +
                "AND EXISTS (SELECT GUID FROM ETMS_CRM_PARTNER_USER T WHERE T.USER_CODE = {0} \n" +
                "AND A.GUID = T.CRM_PARTNER_GUID)))",UserContext.getUserInfo().getUserLoginNo());
//        EciQuery<EtmsCrmPartnerEntity> eciQuery = EciQuery.buildQuery(entity);
        List<EtmsCrmPartnerEntity> entities = etmsCrmPartnerDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public EtmsCrmPartnerEntity save(EtmsCrmPartnerEntity entity) {
        // 返回实体对象
        EtmsCrmPartnerEntity etmsCrmPartnerEntity = null;
        etmsCrmPartnerVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            etmsCrmPartnerEntity = etmsCrmPartnerDao.insertOne(entity);

        }else{

            etmsCrmPartnerEntity = etmsCrmPartnerDao.updateByEntityId(entity);

        }
        return etmsCrmPartnerEntity;
    }

    @Override
    public List<EtmsCrmPartnerEntity> selectList(EtmsCrmPartnerEntity entity) {
        return etmsCrmPartnerDao.selectList(entity);
    }

    @Override
    public EtmsCrmPartnerEntity selectOneById(Serializable id) {
        return etmsCrmPartnerDao.selectById(id);
    }


    @Override
    public void insertBatch(List<EtmsCrmPartnerEntity> list) {
        etmsCrmPartnerDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return etmsCrmPartnerDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return etmsCrmPartnerDao.deleteById(id);
    }

}