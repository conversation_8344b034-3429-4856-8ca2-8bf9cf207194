package com.eci.project.fzgjScoreCompany.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjScoreCar.entity.FzgjScoreCarEntity;
import com.eci.project.fzgjScoreCompany.dao.FzgjScoreCompanyDao;
import com.eci.project.fzgjScoreCompany.entity.FzgjScoreCompanyEntity;
import com.eci.project.fzgjScoreCompany.validate.FzgjScoreCompanyVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 企业评分Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-06-16
*/
@Service
@Slf4j
public class FzgjScoreCompanyService implements EciBaseService<FzgjScoreCompanyEntity> {

    @Autowired
    private FzgjScoreCompanyDao fzgjScoreCompanyDao;

    @Autowired
    private FzgjScoreCompanyVal fzgjScoreCompanyVal;


    @Override
    public TgPageInfo queryPageList(FzgjScoreCompanyEntity entity) {
        EciQuery<FzgjScoreCompanyEntity> eciQuery = EciQuery.buildQuery(entity);
        if(entity.getScore()!=null)
            eciQuery.apply(" A.score>={0}",entity.getScore());
        if(entity.getScore1()!=null)
            eciQuery.apply(" A.score<={0}",entity.getScore1());
        List<FzgjScoreCompanyEntity> entities = fzgjScoreCompanyDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjScoreCompanyEntity save(FzgjScoreCompanyEntity entity) {
        // 返回实体对象
        FzgjScoreCompanyEntity fzgjScoreCompanyEntity = null;
        fzgjScoreCompanyVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjScoreCompanyEntity = fzgjScoreCompanyDao.insertOne(entity);

        }else{

            fzgjScoreCompanyEntity = fzgjScoreCompanyDao.updateByEntityId(entity);

        }
        return fzgjScoreCompanyEntity;
    }

    @Override
    public List<FzgjScoreCompanyEntity> selectList(FzgjScoreCompanyEntity entity) {
        return fzgjScoreCompanyDao.selectList(entity);
    }

    @Override
    public FzgjScoreCompanyEntity selectOneById(Serializable id) {
        return fzgjScoreCompanyDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjScoreCompanyEntity> list) {
        fzgjScoreCompanyDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjScoreCompanyDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjScoreCompanyDao.deleteById(id);
    }

}