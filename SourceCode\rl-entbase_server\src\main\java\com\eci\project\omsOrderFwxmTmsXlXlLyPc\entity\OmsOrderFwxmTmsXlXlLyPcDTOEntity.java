package com.eci.project.omsOrderFwxmTmsXlXlLyPc.entity;


import com.eci.common.Zsr;

public class OmsOrderFwxmTmsXlXlLyPcDTOEntity extends OmsOrderFwxmTmsXlXlLyPcEntity{
    @Override
    protected void addConvertMap() {
        convertMap.put(this.opType, () -> "OMS_BD_OP_TYPE_ORDER");
        convertMap.put(this.productCode, () -> "OMS_BD_PRODUCT");
        convertMap.put(this.consigneeCode, () -> "CRM_CUSTOMER_KH_NAMECOM");
        convertMap.put(this.shipper, () -> "CRM_CUSTOMER_SFHF");
        convertMap.put(this.receiver, () -> "CRM_CUSTOMER_SFHF");
    }
    private String opType;
    private String productCode;
    private String customerOrderNo;
    private String goodsName;
    private String weightTotal;
    private String consigneeCode;
    private String shipper;
    private String receiver;
    private String requestOkDate;

    private boolean isYpc;

    private boolean isSearchZp;

    private String zpNum;
    public String getOpType() {
        return opType;
    }

    public void setOpType(String opType) {
        this.opType = opType;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getCustomerOrderNo() {
        return customerOrderNo;
    }

    public void setCustomerOrderNo(String customerOrderNo) {
        this.customerOrderNo = customerOrderNo;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getWeightTotal() {
        return weightTotal;
    }

    public void setWeightTotal(String weightTotal) {
        this.weightTotal = weightTotal;
    }

    public String getConsigneeCode() {
        return consigneeCode;
    }

    public void setConsigneeCode(String consigneeCode) {
        this.consigneeCode = consigneeCode;
    }

    public String getShipper() {
        return shipper;
    }

    public void setShipper(String shipper) {
        this.shipper = shipper;
    }

    public String getReceiver() {
        return receiver;
    }

    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }

    public String getRequestOkDate() {
        return requestOkDate;
    }

    public void setRequestOkDate(String requestOkDate) {
        this.requestOkDate = requestOkDate;
    }

    public boolean getIsYpc() {
        return isYpc;
    }

    public void setYpc(boolean ypc) {
        isYpc = ypc;
    }

    public boolean getIsSearchZp() {
        return isSearchZp;
    }

    public void setSearchZp(boolean searchZp) {
        isSearchZp = searchZp;
    }

    public String getZpNum() {
        return zpNum;
    }

    public void setZpNum(String zpNum) {
        this.zpNum = zpNum;
    }
}
