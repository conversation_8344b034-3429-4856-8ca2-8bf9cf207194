<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.omsOrderGoods.dao.OmsOrderGoodsDao">
    <resultMap type="OmsOrderGoodsEntity" id="OmsOrderGoodsResult">
        <result property="guid" column="GUID"/>
        <result property="orderNo" column="ORDER_NO"/>
        <result property="preNo" column="PRE_NO"/>
        <result property="weightTotal" column="WEIGHT_TOTAL"/>
        <result property="weightCalc" column="WEIGHT_CALC"/>
        <result property="volumeTotal" column="VOLUME_TOTAL"/>
        <result property="goodsName" column="GOODS_NAME"/>
        <result property="goodsProtety" column="GOODS_PROTETY"/>
        <result property="contractNo" column="CONTRACT_NO"/>
        <result property="invoiceNo" column="INVOICE_NO"/>
        <result property="customerOrderNo" column="CUSTOMER_ORDER_NO"/>
        <result property="isGoodsFz" column="IS_GOODS_FZ"/>
        <result property="isGoodsFqx" column="IS_GOODS_FQX"/>
        <result property="wdMin" column="WD_MIN"/>
        <result property="wdMax" column="WD_MAX"/>
        <result property="sdMin" column="SD_MIN"/>
        <result property="sdMax" column="SD_MAX"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="hyd" column="HYD"/>
        <result property="netTotal" column="NET_TOTAL"/>
        <result property="jgfs" column="JGFS"/>
        <result property="warehouseInNo" column="WAREHOUSE_IN_NO"/>
        <result property="mbNo" column="MB_NO"/>
        <result property="hbNo" column="HB_NO"/>
        <result property="pieceTotal" column="PIECE_TOTAL"/>
        <result property="thdh" column="THDH"/>
        <result property="jxsCode" column="JXS_CODE"/>
        <result property="unNum" column="UN_NUM"/>
        <result property="qtyDt" column="QTY_DT"/>
        <result property="zts" column="ZTS"/>
        <result property="zfcsd" column="ZFCSD"/>
        <result property="qtyXt" column="QTY_XT"/>
        <result property="cjfs" column="CJFS"/>
        <result property="packType" column="PACK_TYPE"/>
    </resultMap>

    <sql id="selectOmsOrderGoodsEntityVo">
        select
            GUID,
            ORDER_NO,
            PRE_NO,
            WEIGHT_TOTAL,
            WEIGHT_CALC,
            VOLUME_TOTAL,
            GOODS_NAME,
            GOODS_PROTETY,
            CONTRACT_NO,
            INVOICE_NO,
            CUSTOMER_ORDER_NO,
            IS_GOODS_FZ,
            IS_GOODS_FQX,
            WD_MIN,
            WD_MAX,
            SD_MIN,
            SD_MAX,
            CREATE_USER,
            CREATE_USER_NAME,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_USER_NAME,
            UPDATE_DATE,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            HYD,
            NET_TOTAL,
            JGFS,
            WAREHOUSE_IN_NO,
            MB_NO,
            HB_NO,
            PIECE_TOTAL,
            THDH,
            JXS_CODE,
            UN_NUM,
            QTY_DT,
            ZTS,
            ZFCSD,
            QTY_XT,
            CJFS,
            PACK_TYPE
        from OMS_ORDER_GOODS
    </sql>
</mapper>