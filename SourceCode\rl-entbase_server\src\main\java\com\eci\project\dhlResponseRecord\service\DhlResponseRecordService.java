package com.eci.project.dhlResponseRecord.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eci.common.enums.Enums;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.dhlResponseRecord.dao.DhlResponseRecordDao;
import com.eci.project.dhlResponseRecord.entity.DhlResponseRecordEntity;
import com.eci.project.dhlResponseRecord.validate.DhlResponseRecordVal;
import com.eci.project.dhlWorkBasic.dao.DhlWorkBasicDao;
import com.eci.project.dhlWorkBasic.entity.DhlWorkBasicEntity;
import com.eci.project.dhlWorkBasic.validate.DhlWorkBasicVal;
import com.eci.project.omsOrder.entity.OmsOrderEntity;
import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.List;


/**
 * 反馈DHL跟踪Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-04-22
 */
@Service
@Slf4j
public class DhlResponseRecordService implements EciBaseService<DhlResponseRecordEntity> {

    @Autowired
    private DhlResponseRecordDao dhlResponseRecordDao;

    @Autowired
    private DhlResponseRecordVal dhlResponseRecordVal;

    @Autowired
    private DhlWorkBasicVal dhlWorkBasicVal;

    @Autowired
    private DhlWorkBasicDao dhlWorkBasicDao;

    @Override
    public TgPageInfo queryPageList(DhlResponseRecordEntity entity) {
        EciQuery<DhlResponseRecordEntity> eciQuery = EciQuery.buildQuery(entity);
        List<DhlResponseRecordEntity> entities = dhlResponseRecordDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public DhlResponseRecordEntity save(DhlResponseRecordEntity entity) {
        // 返回实体对象
        DhlResponseRecordEntity dhlResponseRecordEntity = null;
        dhlResponseRecordVal.saveValidate(entity, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            dhlResponseRecordEntity = dhlResponseRecordDao.insertOne(entity);

        } else {

            dhlResponseRecordEntity = dhlResponseRecordDao.updateByEntityId(entity);

        }
        return dhlResponseRecordEntity;
    }

    @Override
    public List<DhlResponseRecordEntity> selectList(DhlResponseRecordEntity entity) {
        return dhlResponseRecordDao.selectList(entity);
    }

    @Override
    public DhlResponseRecordEntity selectOneById(Serializable id) {
        return dhlResponseRecordDao.selectById(id);
    }


    @Override
    public void insertBatch(List<DhlResponseRecordEntity> list) {
        dhlResponseRecordDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return dhlResponseRecordDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return dhlResponseRecordDao.deleteById(id);
    }


    /// <summary>
    /// Dhl VendorToDos
    /// </summary>
    /// <param name="context"></param>
    /// <param name="entity">最主要的传ORDER_NO过来</param>
    /// <param name="status">OrderEnum.DHLOrderStatus</param>
    public void DhlVendorToDosByOrder(OmsOrderEntity order, String status) {

        if (order == null) {
            return;
        }

        QueryWrapper querWrapper = new QueryWrapper();
        querWrapper.apply("ORDER_NO={0}", order.getPreNo());
        querWrapper.apply("GROUP_CODE={0}", UserContext.getUserInfo().getCompanyCode());
        List<DhlWorkBasicEntity> listDhlBasic = dhlWorkBasicDao.selectList(querWrapper); //不加事务，审核发送的时候会查不出数据。

        for(DhlWorkBasicEntity dhlWorkBasicDb : listDhlBasic){
            DhlVendorToDos(dhlWorkBasicDb, status);
        }
    }

    /// <summary>
    /// Dhl VendorToDos
    /// 1、修改 DHL_WORK_BASIC.TO_STATUS
    /// 2、调用 SP_OMS_SEND存储过程  执行VendorToDos
    /// </summary>
    /// <param name="context"></param>
    /// <param name="dhlWorkBasicUpdate">DHL_WORK_BASIC</param>
    /// <param name="status">OrderEnum.DHLOrderStatus</param>
    private void DhlVendorToDos(DhlWorkBasicEntity dhlWorkBasicUpdate, String status) {
        dhlWorkBasicVal.DhlVendorToDosValidate(dhlWorkBasicUpdate, status);

//        dhlWorkBasicUpdate.KeyField = DHL_WORK_BASIC.Fields.GUID;
//        dhlWorkBasicUpdate.Begin();
//        dhlWorkBasicUpdate.AddUserInfo(context, false);
//        dhlWorkBasicUpdate.End();
        dhlWorkBasicUpdate.setToStatus(status);
        dhlWorkBasicDao.updateByEntityId(dhlWorkBasicUpdate);

//        PO po = new PO("SP_OMS_SEND");
//        po["P_FGUID"] = dhlWorkBasicUpdate.TO_NO;
//        po["P_TYPE"] = "DHL_UPDATE";
//        po["P_SYS_CODE"] = "DHL-TOS";
//        po.ExecuteProcedure(context.Transaction, "");
    }
}