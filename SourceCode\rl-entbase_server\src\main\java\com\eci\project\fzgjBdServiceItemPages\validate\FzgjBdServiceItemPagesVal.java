package com.eci.project.fzgjBdServiceItemPages.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjBdServiceItemPages.entity.FzgjBdServiceItemPagesEntity;

import org.springframework.stereotype.Service;


/**
* 平台服务项目对应页面编辑区Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-07
*/
@Service
public class FzgjBdServiceItemPagesVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjBdServiceItemPagesEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjBdServiceItemPagesEntity entity, BusinessType businessType) {

    }

}
