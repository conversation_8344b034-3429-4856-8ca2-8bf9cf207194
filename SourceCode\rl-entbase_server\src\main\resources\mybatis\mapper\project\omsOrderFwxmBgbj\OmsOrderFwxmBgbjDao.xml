<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.omsOrderFwxmBgbj.dao.OmsOrderFwxmBgbjDao">
    <resultMap type="OmsOrderFwxmBgbjEntity" id="OmsOrderFwxmBgbjResult">
        <result property="preNo" column="PRE_NO"/>
        <result property="orderNo" column="ORDER_NO"/>
        <result property="bgbjNo" column="BGBJ_NO"/>
        <result property="fwlxCode" column="FWLX_CODE"/>
        <result property="fwxmCode" column="FWXM_CODE"/>
        <result property="iEType" column="I_E_TYPE"/>
        <result property="tradeMode" column="TRADE_MODE"/>
        <result property="cjtk" column="CJTK"/>
        <result property="workNo" column="WORK_NO"/>
        <result property="jnshr" column="JNSHR"/>
        <result property="jnfhr" column="JNFHR"/>
        <result property="iEPort" column="I_E_PORT"/>
        <result property="tradeCode" column="TRADE_CODE"/>
        <result property="enrolno" column="ENROLNO"/>
        <result property="entyportCode" column="ENTYPORT_CODE"/>
        <result property="cjfs" column="CJFS"/>
        <result property="originCode" column="ORIGIN_CODE"/>
        <result property="destinationCountry" column="DESTINATION_COUNTRY"/>
        <result property="cyMemo" column="CY_MEMO"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="supervision" column="SUPERVISION"/>
        <result property="xqdh" column="XQDH"/>
        <result property="decNo" column="DEC_NO"/>
        <result property="bhType" column="BH_TYPE"/>
        <result property="hxDateBegin" column="HX_DATE_BEGIN"/>
        <result property="sgdlx" column="SGDLX"/>
        <result property="glOrderNo" column="GL_ORDER_NO"/>
        <result property="sgdyy" column="SGDYY"/>
        <result property="unit" column="UNIT"/>
        <result property="qty" column="QTY"/>
        <result property="yjyDate" column="YJY_DATE"/>
        <result property="yjyAddress" column="YJY_ADDRESS"/>
        <result property="khLinkMan" column="KH_LINK_MAN"/>
        <result property="khTel" column="KH_TEL"/>
        <result property="ciqDate" column="CIQ_DATE"/>
        <result property="ciqLinkMan" column="CIQ_LINK_MAN"/>
        <result property="ciqTel" column="CIQ_TEL"/>
        <result property="djmc" column="DJMC"/>
        <result property="zcAddress" column="ZC_ADDRESS"/>
        <result property="zrAddress" column="ZR_ADDRESS"/>
        <result property="hxDateEnd" column="HX_DATE_END"/>
        <result property="zcCustom" column="ZC_CUSTOM"/>
        <result property="zrCustom" column="ZR_CUSTOM"/>
        <result property="ysfs" column="YSFS"/>
        <result property="customCode" column="CUSTOM_CODE"/>
        <result property="xdh" column="XDH"/>
        <result property="qtckh" column="QTCKH"/>
        <result property="bjType" column="BJ_TYPE"/>
        <result property="ysfsCompany" column="YSFS_COMPANY"/>
        <result property="hdcs" column="HDCS"/>
        <result property="bgTypeCompany" column="BG_TYPE_COMPANY"/>
        <result property="qwCompany" column="QW_COMPANY"/>
        <result property="bgdCustomCode" column="BGD_CUSTOM_CODE"/>
        <result property="isPtsb" column="IS_PTSB"/>
        <result property="isPtsbHzqd" column="IS_PTSB_HZQD"/>
        <result property="isPtsbCrkd" column="IS_PTSB_CRKD"/>
    </resultMap>

    <sql id="selectOmsOrderFwxmBgbjEntityVo">
        select
            PRE_NO,
            ORDER_NO,
            BGBJ_NO,
            FWLX_CODE,
            FWXM_CODE,
            I_E_TYPE,
            TRADE_MODE,
            CJTK,
            WORK_NO,
            JNSHR,
            JNFHR,
            I_E_PORT,
            TRADE_CODE,
            ENROLNO,
            ENTYPORT_CODE,
            CJFS,
            ORIGIN_CODE,
            DESTINATION_COUNTRY,
            CY_MEMO,
            CREATE_USER,
            CREATE_USER_NAME,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_USER_NAME,
            UPDATE_DATE,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            SUPERVISION,
            XQDH,
            DEC_NO,
            BH_TYPE,
            HX_DATE_BEGIN,
            SGDLX,
            GL_ORDER_NO,
            SGDYY,
            UNIT,
            QTY,
            YJY_DATE,
            YJY_ADDRESS,
            KH_LINK_MAN,
            KH_TEL,
            CIQ_DATE,
            CIQ_LINK_MAN,
            CIQ_TEL,
            DJMC,
            ZC_ADDRESS,
            ZR_ADDRESS,
            HX_DATE_END,
            ZC_CUSTOM,
            ZR_CUSTOM,
            YSFS,
            CUSTOM_CODE,
            XDH,
            QTCKH,
            BJ_TYPE,
            YSFS_COMPANY,
            HDCS,
            BG_TYPE_COMPANY,
            QW_COMPANY,
            BGD_CUSTOM_CODE,
            IS_PTSB,
            IS_PTSB_HZQD,
            IS_PTSB_CRKD
        from OMS_ORDER_FWXM_BGBJ
    </sql>
</mapper>