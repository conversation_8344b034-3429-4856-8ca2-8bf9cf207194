package com.eci.project.omsOrderFwxmTmsXlXl.controller;

import com.eci.common.ResponseMsgUtilX;
import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.omsOrderFwxmTmsXlXl.entity.OmsOrderFwxmTmsXlXlDTOEntity;
import com.eci.project.omsOrderFwxmTmsXlXl.service.OmsOrderFwxmTmsXlXlService;
import com.eci.project.omsOrderFwxmTmsXlXl.entity.OmsOrderFwxmTmsXlXlEntity;
import com.eci.project.omsOrderFwxmTmsXlXlLyXl.entity.OmsOrderFwxmTmsXlXlLyXlEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 * 委托内容-程运序列Controller
 *
 * @<NAME_EMAIL>
 * @date 2025-05-28
 */
@Api(tags = "委托内容-程运序列")
@RestController
@RequestMapping("/omsOrderFwxmTmsXlXl")
public class OmsOrderFwxmTmsXlXlController extends EciBaseController {

    @Autowired
    private OmsOrderFwxmTmsXlXlService omsOrderFwxmTmsXlXlService;


    @ApiOperation("委托内容-程运序列:保存")
    @EciLog(title = "委托内容-程运序列:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody OmsOrderFwxmTmsXlXlEntity entity) {
        OmsOrderFwxmTmsXlXlEntity omsOrderFwxmTmsXlXlEntity = omsOrderFwxmTmsXlXlService.save(entity);
        return ResponseMsgUtil.success(10001, omsOrderFwxmTmsXlXlEntity);
    }


    @ApiOperation("委托内容-程运序列:查询列表")
    @EciLog(title = "委托内容-程运序列:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody OmsOrderFwxmTmsXlXlEntity entity) {
        List<OmsOrderFwxmTmsXlXlEntity> omsOrderFwxmTmsXlXlEntities = omsOrderFwxmTmsXlXlService.selectList(entity);
        return ResponseMsgUtil.success(10001, omsOrderFwxmTmsXlXlEntities);
    }


    @ApiOperation("委托内容-程运序列:分页查询列表")
    @EciLog(title = "委托内容-程运序列:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody OmsOrderFwxmTmsXlXlEntity entity) {
        TgPageInfo tgPageInfo = omsOrderFwxmTmsXlXlService.queryPageList(entity);
        return ResponseMsgUtil.success(10001, tgPageInfo);
    }


    @ApiOperation("委托内容-程运序列:根据ID查一条")
    @EciLog(title = "委托内容-程运序列:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody OmsOrderFwxmTmsXlXlEntity entity) {
        OmsOrderFwxmTmsXlXlEntity omsOrderFwxmTmsXlXlEntity = omsOrderFwxmTmsXlXlService.selectOneById(entity.getSeqNo());
        return ResponseMsgUtil.success(10001, omsOrderFwxmTmsXlXlEntity);
    }


    @ApiOperation("委托内容-程运序列:根据ID删除一条")
    @EciLog(title = "委托内容-程运序列:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody OmsOrderFwxmTmsXlXlEntity entity) {
        int count = omsOrderFwxmTmsXlXlService.deleteById(entity.getSeqNo());
        return ResponseMsgUtil.success(10001, count);
    }


    @ApiOperation("委托内容-程运序列:根据ID字符串删除多条")
    @EciLog(title = "委托内容-程运序列:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody OmsOrderFwxmTmsXlXlEntity entity) {
        int count = omsOrderFwxmTmsXlXlService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001, count);
    }

    @ApiOperation("委托内容-程运序列:查询路线")
    @EciLog(title = "委托内容-程运序列:查询路线", businessType = BusinessType.SELECT)
    @PostMapping("/getGetXlRow")
    @EciAction()
    public ResponseMsg getGetXlRow(@RequestBody OmsOrderFwxmTmsXlXlLyXlEntity entity) {
        return ResponseMsgUtilX.success(10001, omsOrderFwxmTmsXlXlService.getGetXlRow(entity));
    }
}