package com.eci.project.fzgjBdOpTypeUser.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjBdOpTypeUser.service.FzgjBdOpTypeUserService;
import com.eci.project.fzgjBdOpTypeUser.entity.FzgjBdOpTypeUserEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 用户-业务伙伴授权Controller
*
* @<NAME_EMAIL>
* @date 2025-03-27
*/
@Api(tags = "用户-业务伙伴授权")
@RestController
@RequestMapping("/fzgjBdOpTypeUser")
public class FzgjBdOpTypeUserController extends EciBaseController {

    @Autowired
    private FzgjBdOpTypeUserService fzgjBdOpTypeUserService;


    @ApiOperation("用户-业务伙伴授权:保存")
    @EciLog(title = "用户-业务伙伴授权:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody FzgjBdOpTypeUserEntity entity){
        FzgjBdOpTypeUserEntity fzgjBdOpTypeUserEntity =fzgjBdOpTypeUserService.save(entity);
        return ResponseMsgUtil.success(10001,fzgjBdOpTypeUserEntity);
    }


    @ApiOperation("用户-业务伙伴授权:查询列表")
    @EciLog(title = "用户-业务伙伴授权:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjBdOpTypeUserEntity entity){
        List<FzgjBdOpTypeUserEntity> fzgjBdOpTypeUserEntities = fzgjBdOpTypeUserService.selectList(entity);
        return ResponseMsgUtil.success(10001,fzgjBdOpTypeUserEntities);
    }


    @ApiOperation("用户-业务伙伴授权:分页查询列表")
    @EciLog(title = "用户-业务伙伴授权:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjBdOpTypeUserEntity entity){
        TgPageInfo tgPageInfo = fzgjBdOpTypeUserService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("用户-业务伙伴授权:根据ID查一条")
    @EciLog(title = "用户-业务伙伴授权:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjBdOpTypeUserEntity entity){
        FzgjBdOpTypeUserEntity  fzgjBdOpTypeUserEntity = fzgjBdOpTypeUserService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,fzgjBdOpTypeUserEntity);
    }


    @ApiOperation("用户-业务伙伴授权:根据ID删除一条")
    @EciLog(title = "用户-业务伙伴授权:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjBdOpTypeUserEntity entity){
        int count = fzgjBdOpTypeUserService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("用户-业务伙伴授权:根据ID字符串删除多条")
    @EciLog(title = "用户-业务伙伴授权:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjBdOpTypeUserEntity entity) {
        int count = fzgjBdOpTypeUserService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}