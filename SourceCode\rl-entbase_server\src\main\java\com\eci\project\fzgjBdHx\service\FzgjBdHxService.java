package com.eci.project.fzgjBdHx.service;

import com.eci.common.utils.ExportUtils.ExcelProcess;
import com.eci.common.web.BllContext;
import com.eci.crud.entity.EciExcelExportAo;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjBdHx.dao.FzgjBdHxDao;
import com.eci.project.fzgjBdHx.entity.FzgjBdHxEntity;
import com.eci.project.fzgjBdHx.validate.FzgjBdHxVal;

import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 航线信息Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-03-13
*/
@Service
@Slf4j
public class FzgjBdHxService implements EciBaseService<FzgjBdHxEntity> {

    @Autowired
    private FzgjBdHxDao fzgjBdHxDao;

    @Autowired
    private FzgjBdHxVal fzgjBdHxVal;


    @Override
    public TgPageInfo queryPageList(FzgjBdHxEntity entity) {
       // Export(entity);
        EciQuery<FzgjBdHxEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjBdHxEntity> entities = fzgjBdHxDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }

    public void Export(FzgjBdHxEntity entity){
        EciQuery<FzgjBdHxEntity> eciQuery = EciQuery.buildQuery(entity);
        fzgjBdHxDao.asyncExportDefaultExcel(()->{
            List<FzgjBdHxEntity> list=fzgjBdHxDao.selectList(eciQuery);
            list.forEach(p->{
                if(p.getStatus()=="Y")
                    p.setStatus("是");
                else
                    p.setStatus("否");
            });
            return list;
        }, ExcelProcess.BuildConfig("航线代码",FzgjBdHxEntity.class));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjBdHxEntity save(FzgjBdHxEntity entity) {
        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setCreateDate(new java.util.Date());
            entity.setUpdateDate(new java.util.Date());
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        } else {
            entity.setUpdateDate(new java.util.Date());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        }
        // 返回实体对象
        FzgjBdHxEntity fzgjBdHxEntity = null;
        fzgjBdHxVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjBdHxEntity = fzgjBdHxDao.insertOne(entity);

        }else{

            fzgjBdHxEntity = fzgjBdHxDao.updateByEntityId(entity);

        }
        return fzgjBdHxEntity;
    }

    @Override
    public List<FzgjBdHxEntity> selectList(FzgjBdHxEntity entity) {
        return fzgjBdHxDao.selectList(entity);
    }

    @Override
    public FzgjBdHxEntity selectOneById(Serializable id) {
        return fzgjBdHxDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjBdHxEntity> list) {
        fzgjBdHxDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjBdHxDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjBdHxDao.deleteById(id);
    }

}