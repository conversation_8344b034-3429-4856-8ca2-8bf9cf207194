<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.omsOrderFwxmTmsXlXlLyXl.dao.OmsOrderFwxmTmsXlXlLyXlDao">
    <resultMap type="OmsOrderFwxmTmsXlXlLyXlEntity" id="OmsOrderFwxmTmsXlXlLyXlResult">
        <result property="guid" column="GUID"/>
        <result property="preNo" column="PRE_NO"/>
        <result property="orderNo" column="ORDER_NO"/>
        <result property="tmsNo" column="TMS_NO"/>
        <result property="lineNo" column="LINE_NO"/>
        <result property="lyNo" column="LY_NO"/>
        <result property="seqNo" column="SEQ_NO"/>
        <result property="xlSeq" column="XL_SEQ"/>
        <result property="xlType" column="XL_TYPE"/>
        <result property="county" column="COUNTY"/>
        <result property="province" column="PROVINCE"/>
        <result property="city" column="CITY"/>
        <result property="region" column="REGION"/>
        <result property="town" column="TOWN"/>
        <result property="address" column="ADDRESS"/>
        <result property="jjf" column="JJF"/>
        <result property="linkMan" column="LINK_MAN"/>
        <result property="linkTel" column="LINK_TEL"/>
        <result property="jjDate" column="JJ_DATE"/>
        <result property="isDhjc" column="IS_DHJC"/>
        <result property="isAzcx" column="IS_AZCX"/>
        <result property="isThdz" column="IS_THDZ"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="note" column="NOTE"/>
        <result property="memo" column="MEMO"/>
        <result property="xlMode" column="XL_MODE"/>
    </resultMap>

    <sql id="selectOmsOrderFwxmTmsXlXlLyXlEntityVo">
        select
            GUID,
            PRE_NO,
            ORDER_NO,
            TMS_NO,
            LINE_NO,
            LY_NO,
            SEQ_NO,
            XL_SEQ,
            XL_TYPE,
            COUNTY,
            PROVINCE,
            CITY,
            REGION,
            TOWN,
            ADDRESS,
            JJF,
            LINK_MAN,
            LINK_TEL,
            JJ_DATE,
            IS_DHJC,
            IS_AZCX,
            IS_THDZ,
            CREATE_USER,
            CREATE_USER_NAME,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_USER_NAME,
            UPDATE_DATE,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            NOTE,
            MEMO,
            XL_MODE
        from OMS_ORDER_FWXM_TMS_XL_XL_LY_XL
    </sql>
</mapper>