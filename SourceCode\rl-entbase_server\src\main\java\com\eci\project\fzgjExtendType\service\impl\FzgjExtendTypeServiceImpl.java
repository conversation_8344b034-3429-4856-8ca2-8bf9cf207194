package com.eci.project.fzgjExtendType.service.impl;

import com.eci.common.web.BllContext;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjExtendType.dao.FzgjExtendTypeDao;
import com.eci.project.fzgjExtendType.entity.FzgjExtendTypeEntity;
import com.eci.project.fzgjExtendType.service.IFzgjExtendTypeService;
import com.eci.project.fzgjExtendType.validate.FzgjExtendTypeVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 扩展基础资料类型Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-03-19
*/
@Service
@Slf4j
public class FzgjExtendTypeServiceImpl implements IFzgjExtendTypeService
{
    @Autowired
    private FzgjExtendTypeDao fzgjExtendTypeDao;

    @Autowired
    private FzgjExtendTypeVal fzgjExtendTypeVal;


    @Override
    public TgPageInfo queryPageList(FzgjExtendTypeEntity entity) {
        EciQuery<FzgjExtendTypeEntity> eciQuery = EciQuery.doQuery(entity);
        List<FzgjExtendTypeEntity> entities = fzgjExtendTypeDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjExtendTypeEntity save(FzgjExtendTypeEntity entity) {
        // 返回实体对象
        FzgjExtendTypeEntity fzgjExtendTypeEntity = null;
        fzgjExtendTypeVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjExtendTypeEntity = fzgjExtendTypeDao.insertOne(entity);

        }else{

            fzgjExtendTypeEntity = fzgjExtendTypeDao.updateByEntityId(entity);

        }
        return fzgjExtendTypeEntity;
    }

    @Override
    public List<FzgjExtendTypeEntity> selectList(FzgjExtendTypeEntity entity) {
        return fzgjExtendTypeDao.selectList(entity);
    }

    @Override
    public FzgjExtendTypeEntity selectOneById(Serializable id) {
        return fzgjExtendTypeDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjExtendTypeEntity> list) {
        fzgjExtendTypeDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjExtendTypeDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjExtendTypeDao.deleteById(id);
    }

}