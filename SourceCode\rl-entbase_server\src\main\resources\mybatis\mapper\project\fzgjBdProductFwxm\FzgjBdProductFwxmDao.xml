<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.fzgjBdProductFwxm.dao.FzgjBdProductFwxmDao">
    <resultMap type="FzgjBdProductFwxmEntity" id="FzgjBdProductFwxmResult">
        <result property="guid" column="GUID"/>
        <result property="opType" column="OP_TYPE"/>
        <result property="productCode" column="PRODUCT_CODE"/>
        <result property="serviceNo" column="SERVICE_NO"/>
        <result property="fwxmCode" column="FWXM_CODE"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
    </resultMap>

    <sql id="selectFzgjBdProductFwxmEntityVo">
        select
            GUID,
            OP_TYPE,
            PRODUCT_CODE,
            SERVICE_NO,
            FWXM_CODE,
            CREATE_USER,
            CREATE_USER_NAME,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_USER_NAME,
            UPDATE_DATE,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME
        from FZGJ_BD_PRODUCT_FWXM
    </sql>
</mapper>