package com.eci.project.fzgjTaskLimitation.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjTaskLimitation.dao.FzgjTaskLimitationDao;
import com.eci.project.fzgjTaskLimitation.entity.FzgjTaskLimitationEntity;
import com.eci.project.fzgjTaskLimitation.validate.FzgjTaskLimitationVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 作业环节及标准时效Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-22
*/
@Service
@Slf4j
public class FzgjTaskLimitationService implements EciBaseService<FzgjTaskLimitationEntity> {

    @Autowired
    private FzgjTaskLimitationDao fzgjTaskLimitationDao;

    @Autowired
    private FzgjTaskLimitationVal fzgjTaskLimitationVal;


    @Override
    public TgPageInfo queryPageList(FzgjTaskLimitationEntity entity) {
        EciQuery<FzgjTaskLimitationEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjTaskLimitationEntity> entities = fzgjTaskLimitationDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjTaskLimitationEntity save(FzgjTaskLimitationEntity entity) {
        // 返回实体对象
        FzgjTaskLimitationEntity fzgjTaskLimitationEntity = null;
        fzgjTaskLimitationVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjTaskLimitationEntity = fzgjTaskLimitationDao.insertOne(entity);

        }else{

            fzgjTaskLimitationEntity = fzgjTaskLimitationDao.updateByEntityId(entity);

        }
        return fzgjTaskLimitationEntity;
    }

    @Override
    public List<FzgjTaskLimitationEntity> selectList(FzgjTaskLimitationEntity entity) {
        return fzgjTaskLimitationDao.selectList(entity);
    }

    @Override
    public FzgjTaskLimitationEntity selectOneById(Serializable id) {
        return fzgjTaskLimitationDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjTaskLimitationEntity> list) {
        fzgjTaskLimitationDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjTaskLimitationDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjTaskLimitationDao.deleteById(id);
    }

}