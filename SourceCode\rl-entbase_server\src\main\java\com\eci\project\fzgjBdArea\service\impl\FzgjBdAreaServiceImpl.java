package com.eci.project.fzgjBdArea.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.db.DBHelper;
import com.eci.common.util.DateUtils;
import com.eci.common.web.BllContext;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjBaseDataDetail.entity.FzgjBaseDataDetailEntity;
import com.eci.project.fzgjBdArea.dao.FzgjBdAreaBaseDao;
import com.eci.project.fzgjBdArea.dao.FzgjBdAreaDao;
import com.eci.project.fzgjBdArea.entity.FzgjBdAreaBaseEntity;
import com.eci.project.fzgjBdArea.entity.FzgjBdAreaEntity;
import com.eci.project.fzgjBdArea.service.IFzgjBdAreaService;
import com.eci.project.fzgjBdArea.validate.FzgjBdAreaVal;

import com.eci.sso.role.entity.UserContext;
import com.eci.wu.core.EntityBase;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 公路乡镇地区Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-03-26
*/
@Service
@Slf4j
public class FzgjBdAreaServiceImpl implements IFzgjBdAreaService
{
    @Autowired
    private FzgjBdAreaDao fzgjBdAreaDao;
    @Autowired
    private FzgjBdAreaBaseDao fzgjBdAreaBaseDao;

    @Autowired
    private FzgjBdAreaVal fzgjBdAreaVal;


    @Override
    public TgPageInfo queryPageList(FzgjBdAreaEntity entity) {
        startPage();
        entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
        //entity.setGroupCode("G01");
        List<FzgjBdAreaEntity> entities = fzgjBdAreaDao.selectListInfo(entity);
        return EciQuery.getPageInfo(entities);
    }


    @Transactional(rollbackFor = Exception.class)
    public FzgjBdAreaBaseEntity saveBase(FzgjBdAreaBaseEntity entity) {
        entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
        entity.setGroupName(UserContext.getUserInfo().getCompanyName());
        // 返回实体对象
        FzgjBdAreaBaseEntity fzgjBdAreaEntity = null;
        fzgjBdAreaVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setGuid(IdWorker.get32UUID());
            entity.setCreateDate(DateUtils.getNowDate());
            entity.setCreateUser(UserContext.getUserInfo().getUserId());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            entity.setCompanyName(UserContext.getUserInfo().getCompanyName());
            entity.setNodeCode(UserContext.getUserInfo().getDeptCode());
            entity.setNodeName(UserContext.getUserInfo().getDeptName());
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
            fzgjBdAreaEntity = fzgjBdAreaBaseDao.insertOne(entity);
        }else{
            entity.setUpdateDate(DateUtils.getNowDate());
            entity.setUpdateUser(UserContext.getUserInfo().getUserId());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            fzgjBdAreaEntity = fzgjBdAreaBaseDao.updateByEntityId(entity);
        }
        return fzgjBdAreaEntity;
    }

    @Override
    public List<FzgjBdAreaEntity> selectList(FzgjBdAreaEntity entity) {
        return fzgjBdAreaDao.selectList(entity);
    }

    @Override
    public FzgjBdAreaEntity selectOneById(Serializable id) {
        return fzgjBdAreaDao.selectInfo(String.valueOf(id));
    }


    @Override
    public void insertBatch(List<FzgjBdAreaEntity> list) {
        fzgjBdAreaDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjBdAreaBaseDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjBdAreaBaseDao.deleteById(id);
    }

}