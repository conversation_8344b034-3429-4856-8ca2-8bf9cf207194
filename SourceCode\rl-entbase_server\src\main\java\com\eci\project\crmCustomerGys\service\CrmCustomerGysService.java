package com.eci.project.crmCustomerGys.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.crmCustomerGys.dao.CrmCustomerGysDao;
import com.eci.project.crmCustomerGys.entity.CrmCustomerGysEntity;
import com.eci.project.crmCustomerGys.validate.CrmCustomerGysVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 业务伙伴-供应商Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-29
*/
@Service
@Slf4j
public class CrmCustomerGysService implements EciBaseService<CrmCustomerGysEntity> {

    @Autowired
    private CrmCustomerGysDao crmCustomerGysDao;

    @Autowired
    private CrmCustomerGysVal crmCustomerGysVal;


    @Override
    public TgPageInfo queryPageList(CrmCustomerGysEntity entity) {
        EciQuery<CrmCustomerGysEntity> eciQuery = EciQuery.buildQuery(entity);
        List<CrmCustomerGysEntity> entities = crmCustomerGysDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public CrmCustomerGysEntity save(CrmCustomerGysEntity entity) {
        // 返回实体对象
        CrmCustomerGysEntity crmCustomerGysEntity = null;
        crmCustomerGysVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            crmCustomerGysEntity = crmCustomerGysDao.insertOne(entity);

        }else{

            crmCustomerGysEntity = crmCustomerGysDao.updateByEntityId(entity);

        }
        return crmCustomerGysEntity;
    }

    @Override
    public List<CrmCustomerGysEntity> selectList(CrmCustomerGysEntity entity) {
        return crmCustomerGysDao.selectList(entity);
    }

    @Override
    public CrmCustomerGysEntity selectOneById(Serializable id) {
        return crmCustomerGysDao.selectById(id);
    }

    public CrmCustomerGysEntity selectOneByCustomerCode(CrmCustomerGysEntity entity){
        QueryWrapper query=new QueryWrapper();
        query.eq("CUSTOMER_CODE",entity.getCustomerCode());
        return crmCustomerGysDao.selectOne(query);
    }


    @Override
    public void insertBatch(List<CrmCustomerGysEntity> list) {
        crmCustomerGysDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return crmCustomerGysDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return crmCustomerGysDao.deleteById(id);
    }

}