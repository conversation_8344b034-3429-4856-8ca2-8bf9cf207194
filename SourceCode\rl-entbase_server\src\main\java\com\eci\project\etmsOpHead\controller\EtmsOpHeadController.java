package com.eci.project.etmsOpHead.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.etmsOpHead.service.EtmsOpHeadService;
import com.eci.project.etmsOpHead.entity.EtmsOpHeadEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 平台业务主表Controller
*
* @<NAME_EMAIL>
* @date 2025-06-13
*/
@Api(tags = "平台业务主表")
@RestController
@RequestMapping("/etmsOpHead")
public class EtmsOpHeadController extends EciBaseController {

    @Autowired
    private EtmsOpHeadService etmsOpHeadService;


    @ApiOperation("平台业务主表:保存")
    @EciLog(title = "平台业务主表:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody EtmsOpHeadEntity entity){
        EtmsOpHeadEntity etmsOpHeadEntity =etmsOpHeadService.save(entity);
        return ResponseMsgUtil.success(10001,etmsOpHeadEntity);
    }


    @ApiOperation("平台业务主表:查询列表")
    @EciLog(title = "平台业务主表:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody EtmsOpHeadEntity entity){
        List<EtmsOpHeadEntity> etmsOpHeadEntities = etmsOpHeadService.selectList(entity);
        return ResponseMsgUtil.success(10001,etmsOpHeadEntities);
    }


    @ApiOperation("平台业务主表:分页查询列表")
    @EciLog(title = "平台业务主表:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody EtmsOpHeadEntity entity){
        TgPageInfo tgPageInfo = etmsOpHeadService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("平台业务主表:根据ID查一条")
    @EciLog(title = "平台业务主表:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody EtmsOpHeadEntity entity){
        EtmsOpHeadEntity  etmsOpHeadEntity = etmsOpHeadService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,etmsOpHeadEntity);
    }


    @ApiOperation("平台业务主表:根据ID删除一条")
    @EciLog(title = "平台业务主表:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody EtmsOpHeadEntity entity){
        int count = etmsOpHeadService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("平台业务主表:根据ID字符串删除多条")
    @EciLog(title = "平台业务主表:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody EtmsOpHeadEntity entity) {
        int count = etmsOpHeadService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}