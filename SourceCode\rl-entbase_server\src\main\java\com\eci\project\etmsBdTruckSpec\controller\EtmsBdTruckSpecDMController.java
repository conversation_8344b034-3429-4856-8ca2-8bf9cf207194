package com.eci.project.etmsBdTruckSpec.controller;

import com.eci.crud.controller.EciBaseController;
import com.eci.project.etmsBdTruckSpec.service.EtmsBdTruckSpecService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "车辆管理-东盟")
@RestController
@RequestMapping("/etmsBdTruckSpecDM")
public class EtmsBdTruckSpecDMController extends EciBaseController {
    @Autowired
    private EtmsBdTruckSpecService etmsBdTruckSpecService;

}
