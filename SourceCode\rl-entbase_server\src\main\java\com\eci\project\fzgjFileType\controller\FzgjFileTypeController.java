package com.eci.project.fzgjFileType.controller;

import com.eci.common.ResponseMsgUtilX;
import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjFileType.service.FzgjFileTypeService;
import com.eci.project.fzgjFileType.entity.FzgjFileTypeEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 附件类型Controller
*
* @<NAME_EMAIL>
* @date 2025-04-11
*/
@Api(tags = "附件类型")
@RestController
@RequestMapping("/fzgjFileType")
public class FzgjFileTypeController extends EciBaseController {

    @Autowired
    private FzgjFileTypeService fzgjFileTypeService;


    @ApiOperation("附件类型:保存")
    @EciLog(title = "附件类型:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody String jsonString){
        FzgjFileTypeEntity fzgjFileTypeEntity =fzgjFileTypeService.save(jsonString);
        return ResponseMsgUtil.success(10001,fzgjFileTypeEntity);
    }


    @ApiOperation("附件类型:查询列表")
    @EciLog(title = "附件类型:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjFileTypeEntity entity){
        List<FzgjFileTypeEntity> fzgjFileTypeEntities = fzgjFileTypeService.selectList(entity);
        return ResponseMsgUtil.success(10001,fzgjFileTypeEntities);
    }


    @ApiOperation("附件类型:分页查询列表")
    @EciLog(title = "附件类型:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjFileTypeEntity entity){
        TgPageInfo tgPageInfo = fzgjFileTypeService.queryPageList(entity);
        return ResponseMsgUtilX.success(10001,tgPageInfo);
    }


    @ApiOperation("附件类型:根据ID查一条")
    @EciLog(title = "附件类型:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjFileTypeEntity entity){
        FzgjFileTypeEntity  fzgjFileTypeEntity = fzgjFileTypeService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,fzgjFileTypeEntity);
    }


    @ApiOperation("附件类型:根据ID删除一条")
    @EciLog(title = "附件类型:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjFileTypeEntity entity){
        int count = fzgjFileTypeService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("附件类型:根据ID字符串删除多条")
    @EciLog(title = "附件类型:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjFileTypeEntity entity) {
        int count = fzgjFileTypeService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}