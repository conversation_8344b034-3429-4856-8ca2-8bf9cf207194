package com.eci.project.crmCustomerHzfw.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.crmCustomerHzfw.service.CrmCustomerHzfwService;
import com.eci.project.crmCustomerHzfw.entity.CrmCustomerHzfwEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 客户合作服务Controller
*
* @<NAME_EMAIL>
* @date 2025-04-29
*/
@Api(tags = "客户合作服务")
@RestController
@RequestMapping("/crmCustomerHzfw")
public class CrmCustomerHzfwController extends EciBaseController {

    @Autowired
    private CrmCustomerHzfwService crmCustomerHzfwService;


    @ApiOperation("客户合作服务:保存")
    @EciLog(title = "客户合作服务:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody CrmCustomerHzfwEntity entity){
        CrmCustomerHzfwEntity crmCustomerHzfwEntity =crmCustomerHzfwService.save(entity);
        return ResponseMsgUtil.success(10001,crmCustomerHzfwEntity);
    }


    @ApiOperation("客户合作服务:查询列表")
    @EciLog(title = "客户合作服务:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody CrmCustomerHzfwEntity entity){
        List<CrmCustomerHzfwEntity> crmCustomerHzfwEntities = crmCustomerHzfwService.selectList(entity);
        return ResponseMsgUtil.success(10001,crmCustomerHzfwEntities);
    }


    @ApiOperation("客户合作服务:分页查询列表")
    @EciLog(title = "客户合作服务:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody CrmCustomerHzfwEntity entity){
        TgPageInfo tgPageInfo = crmCustomerHzfwService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("客户合作服务:根据ID查一条")
    @EciLog(title = "客户合作服务:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody CrmCustomerHzfwEntity entity){
        CrmCustomerHzfwEntity  crmCustomerHzfwEntity = crmCustomerHzfwService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,crmCustomerHzfwEntity);
    }


    @ApiOperation("客户合作服务:根据ID删除一条")
    @EciLog(title = "客户合作服务:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody CrmCustomerHzfwEntity entity){
        int count = crmCustomerHzfwService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("客户合作服务:根据ID字符串删除多条")
    @EciLog(title = "客户合作服务:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody CrmCustomerHzfwEntity entity) {
        int count = crmCustomerHzfwService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}