package com.eci.project.omsOrderGoodsPack.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.omsOrderGoodsPack.entity.OmsOrderGoodsPackEntity;

import org.springframework.stereotype.Service;


/**
* 货物包装表Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-06-10
*/
@Service
public class OmsOrderGoodsPackVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(OmsOrderGoodsPackEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(OmsOrderGoodsPackEntity entity, BusinessType businessType) {

    }

}
