package com.eci.project.omsOrderFwxmWorkTrace.validate;

import com.eci.exception.BaseException;
import com.eci.log.enums.BusinessType;
import com.eci.project.omsOrderFwxmWorkTrace.entity.OmsOrderFwxmWorkTraceEntity;
import org.springframework.stereotype.Service;


/**
 * 作业跟踪Val接口
 * 验证接口
 *
 * @<NAME_EMAIL>
 * @date 2025-04-22
 */
@Service
public class OmsOrderFwxmWorkTraceVal {

    /**
     * 删除验证
     *
     * @param entity
     */
    public void deleteValidate(OmsOrderFwxmWorkTraceEntity entity) {

    }

    /**
     * 保存验证
     *
     * @param entity
     */
    public void saveValidate(OmsOrderFwxmWorkTraceEntity entity, BusinessType businessType) {

        if (entity.getActualOkDate() == null) {
            throw new BaseException("实际完成时间不可为空");
        }

    }

}
