package com.eci.project.fzgjBdProductService.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;



/**
* 产品服务类型对象 FZGJ_BD_PRODUCT_SERVICE
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-04-01
*/
@ApiModel("产品服务类型")
@TableName("FZGJ_BD_PRODUCT_SERVICE")
@FieldNameConstants
public class FzgjBdProductServiceEntity extends EciBaseEntity{
    /**
    * GUID
    */
    @ApiModelProperty("GUID(36)")
    @TableId("GUID")
    private String guid;

    /**
    * PRODUCT_CODE
    */
    @ApiModelProperty("PRODUCT_CODE(20)")
    @TableField("PRODUCT_CODE")
    private String productCode;

    /**
    * SERVICE_NO
    */
    @ApiModelProperty("SERVICE_NO(20)")
    @TableField("SERVICE_NO")
    private String serviceNo;

    /**
    * COMPANY_CODE
    */
    @ApiModelProperty("COMPANY_CODE(36)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * COMPANY_NAME
    */
    @ApiModelProperty("COMPANY_NAME(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 集团代码
    */
    @ApiModelProperty("集团代码(36)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 集团名称
    */
    @ApiModelProperty("集团名称(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 业务类型
    */
    @ApiModelProperty("业务类型(20)")
    @TableField("OP_TYPE")
    private String opType;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public FzgjBdProductServiceEntity() {
        this.setSubClazz(FzgjBdProductServiceEntity.class);
    }

    public FzgjBdProductServiceEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public FzgjBdProductServiceEntity setProductCode(String productCode) {
        this.productCode = productCode;
        this.nodifySetFiled("productCode", productCode);
        return this;
    }

    public String getProductCode() {
        this.nodifyGetFiled("productCode");
        return productCode;
    }

    public FzgjBdProductServiceEntity setServiceNo(String serviceNo) {
        this.serviceNo = serviceNo;
        this.nodifySetFiled("serviceNo", serviceNo);
        return this;
    }

    public String getServiceNo() {
        this.nodifyGetFiled("serviceNo");
        return serviceNo;
    }

    public FzgjBdProductServiceEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public FzgjBdProductServiceEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public FzgjBdProductServiceEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public FzgjBdProductServiceEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public FzgjBdProductServiceEntity setOpType(String opType) {
        this.opType = opType;
        this.nodifySetFiled("opType", opType);
        return this;
    }

    public String getOpType() {
        this.nodifyGetFiled("opType");
        return opType;
    }

}
