package com.eci.project.omsOrderPre.controller;

import com.eci.common.ResponseMsgUtilX;
import com.eci.common.util.ResponseMsg;
import com.eci.common.util.ResponseMsgUtil;
import com.eci.crud.controller.EciBaseController;
import com.eci.log.annotation.EciAction;
import com.eci.log.annotation.EciLog;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.omsOrder.entity.RequestOmsOrderTracePageEntity;
import com.eci.project.omsOrderPre.entity.OmsOrderPreEntity;
import com.eci.project.omsOrderPre.entity.ReqOmsOrderPrePageEntity;
import com.eci.project.omsOrderPre.entity.ReqOmsOrderPreStatusEntity;
import com.eci.project.omsOrderPre.entity.ResOmsOrderPrePageEntity;
import com.eci.project.omsOrderPre.service.OmsOrderPreService;
import com.eci.wu.core.EntityBase;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 客户委托单Controller
 *
 * @<NAME_EMAIL>
 * @date 2025-04-15
 */
@Api(tags = "客户委托单")
@RestController
@RequestMapping("/omsOrderPre")
public class OmsOrderPreController extends EciBaseController {

    @Autowired
    private OmsOrderPreService omsOrderPreService;


    @ApiOperation("客户委托单:保存")
    @EciLog(title = "客户委托单:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody OmsOrderPreEntity entity) {
        OmsOrderPreEntity omsOrderPreEntity = omsOrderPreService.save(entity);
        return ResponseMsgUtil.success(10001, omsOrderPreEntity);
    }

    @ApiOperation("客户委托单-自助下单:保存")
    @EciLog(title = "客户委托单-自助下单:新增", businessType = BusinessType.INSERT)
    @PostMapping("/saveZiZhuXiaDan")
    @EciAction()
    public ResponseMsg saveZiZhuXiaDan(@RequestBody OmsOrderPreEntity entity) {
        OmsOrderPreEntity omsOrderPreEntity = omsOrderPreService.saveZiZhuXiaDan(entity);
        return ResponseMsgUtil.success(10001, omsOrderPreEntity);
    }


    @ApiOperation("客户委托单:查询列表")
    @EciLog(title = "客户委托单:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody OmsOrderPreEntity entity) {
        return ResponseMsgUtil.success(10001, omsOrderPreService.selectList(entity));
    }


    @ApiOperation("客户委托单:分页查询列表")
    @EciLog(title = "客户委托单:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody ReqOmsOrderPrePageEntity entity) {
        TgPageInfo tgPageInfo = omsOrderPreService.selectOmsPageList(entity);
        return ResponseMsgUtil.successPlus(10001, tgPageInfo);
    }

    @ApiOperation("客户委托单:待提交分页查询列表")
    @EciLog(title = "客户委托单:待提交分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageListZiZhu")
    @EciAction()
    public ResponseMsg selectPageListZiZhu(@RequestBody ReqOmsOrderPrePageEntity entity) {
        TgPageInfo tgPageInfo = omsOrderPreService.selectPageListZiZhu(entity);
        return ResponseMsgUtilX.success(10001, tgPageInfo);
    }

    @ApiOperation("客户委托单:待审核分页查询列表")
    @EciLog(title = "客户委托单:待审核分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageListZiZhuDaiShenHe")
    @EciAction()
    public ResponseMsg selectPageListZiZhuDaiShenHe(@RequestBody ReqOmsOrderPrePageEntity entity) {
        TgPageInfo tgPageInfo = omsOrderPreService.selectPageListZiZhuDaiShen(entity);
        return ResponseMsgUtilX.success(10001, tgPageInfo);
    }

    @ApiOperation("客户委托单:作业最新状态查询列表")
    @EciLog(title = "客户委托单:作业最新状态查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/getTraceLinkStatus")
    @EciAction()
    public ResponseMsg getTraceLinkStatus(@RequestBody ReqOmsOrderPrePageEntity entity) {
        TgPageInfo tgPageInfo = omsOrderPreService.selectPageListZuoYeZhuangTai(entity);
        return ResponseMsgUtilX.success(10001, tgPageInfo);
    }


    @ApiOperation("客户委托单:根据ID查一条")
    @EciLog(title = "客户委托单:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody OmsOrderPreEntity entity) {
        return ResponseMsgUtilX.success(10001, omsOrderPreService.selectOneZiZhu(entity.getPreNo()));
    }

    @ApiOperation("协同订单审核:根据ID查一条")
    @EciLog(title = "协同订单审核:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneAudit")
    @EciAction()
    public ResponseMsg selectOneAudit(@RequestBody OmsOrderPreEntity entity) {
        return ResponseMsgUtilX.success(10001, omsOrderPreService.selectOneAudit(entity.getPreNo()));
    }


    @ApiOperation("客户委托单:根据ID删除一条")
    @EciLog(title = "客户委托单:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody OmsOrderPreEntity entity) {
        int count = omsOrderPreService.deleteById(entity.getPreNo());
        return ResponseMsgUtil.success(10001, count);
    }


    @ApiOperation("客户委托单:根据ID字符串删除多条")
    @EciLog(title = "客户委托单:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody OmsOrderPreEntity entity) {
        int count = omsOrderPreService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001, count);
    }

    @ApiOperation("客户委托单:根据ID字符串删除多条")
    @EciLog(title = "客户委托单:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/updateSHStatus")
    @EciAction()
    public ResponseMsg updateSHStatus(@RequestBody ReqOmsOrderPreStatusEntity entity) {
        return ResponseMsgUtil.success(10001, omsOrderPreService.updateSHStatus(entity));
    }

    @ApiOperation("客户委托单:确认下单")
    @EciLog(title = "客户委托单:确认下单", businessType = BusinessType.DELETE)
    @PostMapping("/xiaDan")
    @EciAction()
    public ResponseMsg xiaDan(@RequestBody String jsonString) {
        return ResponseMsgUtil.success(10001, omsOrderPreService.xiaDan(jsonString));
    }


}