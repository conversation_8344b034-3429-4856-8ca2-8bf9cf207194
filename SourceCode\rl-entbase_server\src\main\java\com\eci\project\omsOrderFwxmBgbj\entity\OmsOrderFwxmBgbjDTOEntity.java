package com.eci.project.omsOrderFwxmBgbj.entity;

import com.eci.common.DictField;

public class OmsOrderFwxmBgbjDTOEntity extends OmsOrderFwxmBgbjEntity{

    @DictField(queryKey = "CRM_CUSTOMER_GYS_HZFW")
    private String gysCode;
    @DictField(queryKey = "OMS_SSO_NODE")
    private String nodeCodeNb;
    private String isWb;
    private String otherMemo;

    public String getGysCode() {
        return gysCode;
    }

    public void setGysCode(String gysCode) {
        this.gysCode = gysCode;
    }

    public String getNodeCodeNb() {
        return nodeCodeNb;
    }

    public void setNodeCodeNb(String nodeCodeNb) {
        this.nodeCodeNb = nodeCodeNb;
    }
    public String getIsWb() {
        return isWb;
    }
    public void setIsWb(String isWb) {
        this.isWb = isWb;
    }
    public String getOtherMemo() {
        return otherMemo;
    }
    public void setOtherMemo(String otherMemo) {
        this.otherMemo = otherMemo;
    }
}
