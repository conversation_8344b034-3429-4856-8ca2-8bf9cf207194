package com.eci.project.omsOrderFwxmZhys.controller;

import com.eci.common.ResponseMsgUtilX;
import com.eci.common.util.ResponseMsg;
import com.eci.common.util.ResponseMsgUtil;
import com.eci.crud.controller.EciBaseController;
import com.eci.log.annotation.EciAction;
import com.eci.log.annotation.EciLog;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.omsOrderFwxmZhys.entity.OmsOrderFwxmZhysEntity;
import com.eci.project.omsOrderFwxmZhys.service.OmsOrderFwxmZhysService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 服务项目-综合运输Controller
 *
 * @<NAME_EMAIL>
 * @date 2025-06-13
 */
@Api(tags = "服务项目-综合运输")
@RestController
@RequestMapping("/omsOrderFwxmZhys")
public class OmsOrderFwxmZhysController extends EciBaseController {

    @Autowired
    private OmsOrderFwxmZhysService omsOrderFwxmZhysService;


    @ApiOperation("服务项目-综合运输:保存")
    @EciLog(title = "服务项目-综合运输:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody OmsOrderFwxmZhysEntity entity) {
        OmsOrderFwxmZhysEntity omsOrderFwxmZhysEntity = omsOrderFwxmZhysService.save(entity);
        return ResponseMsgUtil.success(10001, omsOrderFwxmZhysEntity);
    }


    @ApiOperation("服务项目-综合运输:查询列表")
    @EciLog(title = "服务项目-综合运输:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody OmsOrderFwxmZhysEntity entity) {
        List<OmsOrderFwxmZhysEntity> omsOrderFwxmZhysEntities = omsOrderFwxmZhysService.selectList(entity);
        return ResponseMsgUtil.success(10001, omsOrderFwxmZhysEntities);
    }


    @ApiOperation("服务项目-综合运输:分页查询列表")
    @EciLog(title = "服务项目-综合运输:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody OmsOrderFwxmZhysEntity entity) {
        TgPageInfo tgPageInfo = omsOrderFwxmZhysService.queryPageList(entity);
        return ResponseMsgUtil.success(10001, tgPageInfo);
    }


    @ApiOperation("服务项目-综合运输:根据ID字符串删除多条")
    @EciLog(title = "服务项目-综合运输:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody OmsOrderFwxmZhysEntity entity) {
        int count = omsOrderFwxmZhysService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001, count);
    }

    @ApiOperation("服务项目-综合运输:保存|编辑")
    @EciLog(title = "服务项目-综合运输:保存|编辑", businessType = BusinessType.SELECT)
    @PostMapping("/saveOmsOrderZHYS")
    @EciAction()
    public ResponseMsg saveOmsOrderZHYS(@RequestBody String jsonString) {
        return ResponseMsgUtil.success(10001, omsOrderFwxmZhysService.saveOmsOrderZHYS(jsonString));
    }

    @ApiOperation("货物信息表:货物信息加载")
    @EciLog(title = "货物信息表:货物信息加载", businessType = BusinessType.SELECT)
    @PostMapping("/loadOmsOrderZHYS")
    @EciAction()
    public ResponseMsg loadOmsOrderZHYS(@RequestBody OmsOrderFwxmZhysEntity entity) {
        return ResponseMsgUtilX.success(10001, omsOrderFwxmZhysService.loadOmsOrderZHYS(entity));
    }


}