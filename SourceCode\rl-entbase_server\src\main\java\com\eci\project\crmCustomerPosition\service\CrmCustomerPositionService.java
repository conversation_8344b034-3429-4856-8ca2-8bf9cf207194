package com.eci.project.crmCustomerPosition.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.crmCustomerPosition.dao.CrmCustomerPositionDao;
import com.eci.project.crmCustomerPosition.entity.CrmCustomerPositionEntity;
import com.eci.project.crmCustomerPosition.validate.CrmCustomerPositionVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 合作伙伴职务Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-05-26
*/
@Service
@Slf4j
public class CrmCustomerPositionService implements EciBaseService<CrmCustomerPositionEntity> {

    @Autowired
    private CrmCustomerPositionDao crmCustomerPositionDao;

    @Autowired
    private CrmCustomerPositionVal crmCustomerPositionVal;


    @Override
    public TgPageInfo queryPageList(CrmCustomerPositionEntity entity) {
        EciQuery<CrmCustomerPositionEntity> eciQuery = EciQuery.buildQuery(entity);
        List<CrmCustomerPositionEntity> entities = crmCustomerPositionDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public CrmCustomerPositionEntity save(CrmCustomerPositionEntity entity) {
        // 返回实体对象
        CrmCustomerPositionEntity crmCustomerPositionEntity = null;
        crmCustomerPositionVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            crmCustomerPositionEntity = crmCustomerPositionDao.insertOne(entity);

        }else{

            crmCustomerPositionEntity = crmCustomerPositionDao.updateByEntityId(entity);

        }
        return crmCustomerPositionEntity;
    }

    @Override
    public List<CrmCustomerPositionEntity> selectList(CrmCustomerPositionEntity entity) {
        return crmCustomerPositionDao.selectList(entity);
    }

    @Override
    public CrmCustomerPositionEntity selectOneById(Serializable id) {
        return crmCustomerPositionDao.selectById(id);
    }


    @Override
    public void insertBatch(List<CrmCustomerPositionEntity> list) {
        crmCustomerPositionDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return crmCustomerPositionDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return crmCustomerPositionDao.deleteById(id);
    }

}