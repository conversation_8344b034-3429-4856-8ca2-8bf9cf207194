package com.eci.project.etmsBdTruckQualificateQz.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.eci.common.utils.ExportUtils.Excel;
import com.eci.crud.entity.EciBaseEntity;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

public class EtmsBdTruckQualificateQzSearchEntity extends EciBaseEntity {
    private String guid;
    @Excel(value = "车牌号",order = 1)
    private String truckNo;

    private String carColor;

    @Excel(value = "资质类型",order = 2)
    private String qualificateType;

    @Excel(value = "资质名称",order = 3)
    private String qualificateName;

    @Excel(value = "资质编号",order = 4)
    private String qualificateNo;

    @Excel(value = "资质状态",order = 11)
    private String qualificateStatus;

    @Excel(value = "发证日期",order = 9)
    private String issueDate;

    @Excel(value = "有效期",order = 10)
    private String validityDate;

    @Excel(value = "车辆类型",order = 5)
    private String cllx;

    @Excel(value = "车辆规格",order = 6)
    private String clcc;

    @Excel(value = "车主业务伙伴",order = 8)
    private String partnerName;

    @Excel(value = "驾驶人员",order = 12)
    private String driverName;

    @Excel(value = "是否送审",order = 13)
    private String modMark;

    @Excel(value = "审核通过",order = 14)
    private String checkMark;
    @Excel(value = "创建企业",order = 15)
    private String companyName;
    @Excel(value = "创建时间",order = 16)
    private Date createDate;
    @Excel(value = "自有",order = 7)
    private String isGk;

    private Date issueDateStart;

    private Date issueDateEnd;

    private Date validityDateStart;

    private Date validityDateEnd;

    private Date createDateStart;

    private Date createDateEnd;

    public String getTruckNo() {
        return truckNo;
    }

    public void setTruckNo(String truckNo) {
        this.truckNo = truckNo;
    }

    public String getCarColor() {
        return carColor;
    }

    public void setCarColor(String carColor) {
        this.carColor = carColor;
    }

    public String getQualificateType() {
        return qualificateType;
    }

    public void setQualificateType(String qualificateType) {
        this.qualificateType = qualificateType;
    }

    public String getQualificateName() {
        return qualificateName;
    }

    public void setQualificateName(String qualificateName) {
        this.qualificateName = qualificateName;
    }

    public String getQualificateNo() {
        return qualificateNo;
    }

    public void setQualificateNo(String qualificateNo) {
        this.qualificateNo = qualificateNo;
    }

    public String getQualificateStatus() {
        return qualificateStatus;
    }

    public void setQualificateStatus(String qualificateStatus) {
        this.qualificateStatus = qualificateStatus;
    }

    public String getIssueDate() {
        return issueDate;
    }

    public void setIssueDate(String issueDate) {
        this.issueDate = issueDate;
    }

    public String getValidityDate() {
        return validityDate;
    }

    public void setValidityDate(String validityDate) {
        this.validityDate = validityDate;
    }

    public String getCllx() {
        return cllx;
    }

    public void setCllx(String cllx) {
        this.cllx = cllx;
    }

    public String getClcc() {
        return clcc;
    }

    public void setClcc(String clcc) {
        this.clcc = clcc;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public String getDriverName() {
        return driverName;
    }

    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }
    public Date getCreateDate() {
        return createDate;
    }
    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getModMark() {
        return modMark;
    }

    public void setModMark(String modMark) {
        this.modMark = modMark;
    }

    public String getCheckMark() {
        return checkMark;
    }

    public void setCheckMark(String checkMark) {
        this.checkMark = checkMark;
    }

    public String getIsGk() {
        return isGk;
    }

    public void setIsGk(String isGk) {
        this.isGk = isGk;
    }

    public Date getIssueDateStart() {
        return issueDateStart;
    }

    public void setIssueDateStart(Date issueDateStart) {
        this.issueDateStart = issueDateStart;
    }

    public Date getIssueDateEnd() {
        return issueDateEnd;
    }

    public void setIssueDateEnd(Date issueDateEnd) {
        this.issueDateEnd = issueDateEnd;
    }

    public Date getValidityDateStart() {
        return validityDateStart;
    }

    public void setValidityDateStart(Date validityDateStart) {
        this.validityDateStart = validityDateStart;
    }

    public Date getValidityDateEnd() {
        return validityDateEnd;
    }

    public void setValidityDateEnd(Date validityDateEnd) {
        this.validityDateEnd = validityDateEnd;
    }

    public Date getCreateDateStart() {
        return createDateStart;
    }

    public void setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
    }

    public Date getCreateDateEnd() {
        return createDateEnd;
    }

    public void setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
    }
}
