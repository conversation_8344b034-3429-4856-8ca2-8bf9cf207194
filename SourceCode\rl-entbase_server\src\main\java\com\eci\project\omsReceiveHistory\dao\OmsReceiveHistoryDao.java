package com.eci.project.omsReceiveHistory.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.omsReceiveHistory.entity.OmsReceiveHistoryEntity;


/**
* 报文接收记录Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-06-04
*/
public interface OmsReceiveHistoryDao extends EciBaseDao<OmsReceiveHistoryEntity> {

}