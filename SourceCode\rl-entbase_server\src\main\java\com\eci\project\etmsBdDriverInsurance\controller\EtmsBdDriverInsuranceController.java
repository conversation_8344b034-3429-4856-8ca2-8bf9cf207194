package com.eci.project.etmsBdDriverInsurance.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.etmsBdDriverInsurance.entity.EtmsBdDriverInsuranceSearchEntity;
import com.eci.project.etmsBdDriverInsurance.service.EtmsBdDriverInsuranceService;
import com.eci.project.etmsBdDriverInsurance.entity.EtmsBdDriverInsuranceEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 司机保险管理Controller
*
* @<NAME_EMAIL>
* @date 2025-04-27
*/
@Api(tags = "司机保险管理")
@RestController
@RequestMapping("/etmsBdDriverInsurance")
public class EtmsBdDriverInsuranceController extends EciBaseController {

    @Autowired
    private EtmsBdDriverInsuranceService etmsBdDriverInsuranceService;


    @ApiOperation("司机保险管理:保存")
    @EciLog(title = "司机保险管理:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody EtmsBdDriverInsuranceEntity entity){
        EtmsBdDriverInsuranceEntity etmsBdDriverInsuranceEntity =etmsBdDriverInsuranceService.save(entity);
        return ResponseMsgUtil.success(10001,etmsBdDriverInsuranceEntity);
    }


    @ApiOperation("司机保险管理:查询列表")
    @EciLog(title = "司机保险管理:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody EtmsBdDriverInsuranceEntity entity){
        List<EtmsBdDriverInsuranceEntity> etmsBdDriverInsuranceEntities = etmsBdDriverInsuranceService.selectList(entity);
        return ResponseMsgUtil.success(10001,etmsBdDriverInsuranceEntities);
    }


    @ApiOperation("司机保险管理:分页查询列表")
    @EciLog(title = "司机保险管理:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody EtmsBdDriverInsuranceEntity entity){
        TgPageInfo tgPageInfo = etmsBdDriverInsuranceService.queryPageList(entity);
        return ResponseMsgUtil.successPlus(10001,tgPageInfo);
    }


    @ApiOperation("司机保险管理:根据ID查一条")
    @EciLog(title = "司机保险管理:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody EtmsBdDriverInsuranceEntity entity){
        EtmsBdDriverInsuranceEntity  etmsBdDriverInsuranceEntity = etmsBdDriverInsuranceService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,etmsBdDriverInsuranceEntity);
    }


    @ApiOperation("司机保险管理:根据ID删除一条")
    @EciLog(title = "司机保险管理:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody EtmsBdDriverInsuranceEntity entity){
        int count = etmsBdDriverInsuranceService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("司机保险管理:根据ID字符串删除多条")
    @EciLog(title = "司机保险管理:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody EtmsBdDriverInsuranceEntity entity) {
        int count = etmsBdDriverInsuranceService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }

    @ApiOperation("保险资质审核:司机保险管理分页查询列表")
    @EciLog(title = "保险资质审核:司机保险管理分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectInsurancePageList")
    @EciAction()
    public ResponseMsg selectInsurancePageList(@RequestBody EtmsBdDriverInsuranceSearchEntity entity){
        TgPageInfo tgPageInfo = etmsBdDriverInsuranceService.queryInsurancePageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }
}