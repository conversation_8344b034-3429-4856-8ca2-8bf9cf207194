package com.eci.project.omsFile.service;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.Zsr;
import com.eci.common.ZsrGlobaVarl;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.omsFile.dao.OmsFileDao;
import com.eci.project.omsFile.entity.OmsFileEntity;
import com.eci.project.omsFile.validate.OmsFileVal;

import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * OMS对接数据其他业务系统附件Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-06-04
 */
@Service
@Slf4j
public class OmsFileService implements EciBaseService<OmsFileEntity> {

    @Autowired
    private OmsFileDao omsFileDao;

    @Autowired
    private OmsFileVal omsFileVal;


    @Override
    public TgPageInfo queryPageList(OmsFileEntity entity) {
        if (Zsr.String.IsNullOrWhiteSpace(entity.getOrderNo())) {
            return new TgPageInfo();
        }
        EciQuery<OmsFileEntity> eciQuery = EciQuery.buildQuery(entity);
        eciQuery.eq(OmsFileEntity::getOrderNo, entity.getOrderNo());
        eciQuery.eq(OmsFileEntity::getSysCode, ZsrGlobaVarl.Sys_Code_Attr);
        List<OmsFileEntity> entities = omsFileDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }

    /**
     * 查询不是本系统的附件数据
     * @param entity
     * @return
     */
    public TgPageInfo selectPageListForOther(OmsFileEntity entity) {
        if (Zsr.String.IsNullOrWhiteSpace(entity.getOrderNo())) {
            return new TgPageInfo();
        }
        EciQuery<OmsFileEntity> eciQuery = EciQuery.buildQuery(entity);
        eciQuery.eq(OmsFileEntity::getOrderNo, entity.getOrderNo());
        eciQuery.ne(OmsFileEntity::getSysCode, ZsrGlobaVarl.Sys_Code_Attr);
        List<OmsFileEntity> entities = omsFileDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }

    @Transactional(rollbackFor = Exception.class)
    public OmsFileEntity save(OmsFileEntity entity, boolean isAdd) {
        // 返回实体对象
        OmsFileEntity omsFileEntity = null;

        if (isAdd) {

            omsFileEntity = omsFileDao.insertOne(entity);

        } else {

            omsFileEntity = omsFileDao.updateByEntityId(entity);

        }
        return omsFileEntity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OmsFileEntity save(OmsFileEntity entity) {
        // 返回实体对象
        OmsFileEntity omsFileEntity = null;
        omsFileVal.saveValidate(entity, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setGuid(IdWorker.getIdStr());
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setCreateDate(new Date());
            entity.setSource("人工上传");
            entity.setSysCode(ZsrGlobaVarl.Sys_Code_Attr);
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
            entity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            entity.setCompanyName(UserContext.getUserInfo().getCompanyName());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            entity.setUpdateDate(new Date());

            omsFileEntity = omsFileDao.insertOne(entity);

        } else {
            entity.setUpdateDate(new Date());
            entity.setSource("人工上传");
            entity.setSysCode(ZsrGlobaVarl.Sys_Code_Attr);
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            omsFileEntity = omsFileDao.updateByEntityId(entity);

        }
        return omsFileEntity;
    }

    @Override
    public List<OmsFileEntity> selectList(OmsFileEntity entity) {
        return omsFileDao.selectList(entity);
    }

    public OmsFileEntity selectOneById(String id) {
        return omsFileDao.select().eq(OmsFileEntity::getGuid, id).one();
    }


    @Override
    public void insertBatch(List<OmsFileEntity> list) {
        omsFileDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        List<String> stringIds = Zsr.String.string2List(ids);
        stringIds.forEach(item -> {
            omsFileDao.delete().eq(OmsFileEntity::getGuid, item).execute();
        });
        return 1;
    }

    @Override
    public int deleteById(Serializable id) {
        return omsFileDao.deleteById(id);
    }

}