package com.eci.project.etmsBdGpsType.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.etmsBdGpsType.entity.EtmsBdGpsTypeEntity;


/**
* 定位方式Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-03-21
*/
public interface EtmsBdGpsTypeDao extends EciBaseDao<EtmsBdGpsTypeEntity> {

}