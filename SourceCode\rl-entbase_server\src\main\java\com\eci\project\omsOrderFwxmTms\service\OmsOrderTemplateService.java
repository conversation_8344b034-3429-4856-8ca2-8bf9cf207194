package com.eci.project.omsOrderFwxmTms.service;

import com.eci.common.CodeNameEntity;
import com.eci.common.Zsr;
import com.eci.common.ZsrDateUtils;
import com.eci.common.db.DBHelper;
import com.eci.common.utils.CommonLib;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.common.SQLGenerator;
import com.eci.project.omsOrderFwxmTms.dao.OmsOrderFwxmTmsDao;
import com.eci.project.omsOrderFwxmTms.entity.OmsOrderFwxmTmsEntity;
import com.eci.project.omsOrderFwxmTms.entity.OrderInfoDto;
import com.eci.project.omsOrderFwxmTms.entity.QueryOrderInfoDto;
import com.eci.project.omsOrderFwxmTms.validate.OmsOrderFwxmTmsVal;
import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 委托内容 - 运输 Service 业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-04-14
 */
@Service
@Slf4j
public class OmsOrderTemplateService implements EciBaseService<OmsOrderFwxmTmsEntity> {

    @Autowired
    private OmsOrderFwxmTmsDao omsOrderFwxmTmsDao;

    @Autowired
    private OmsOrderFwxmTmsVal omsOrderFwxmTmsVal;


    @Override
    public TgPageInfo queryPageList(OmsOrderFwxmTmsEntity entity) {
        EciQuery<OmsOrderFwxmTmsEntity> eciQuery = EciQuery.buildQuery(entity);
        List<OmsOrderFwxmTmsEntity> entities = omsOrderFwxmTmsDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }

    CommonLib cmn = CommonLib.getInstance();

    public TgPageInfo queryPageListByOrderDto(QueryOrderInfoDto queryOrderInfoDto) throws InterruptedException {

        String sql = "SELECT A.IS_JJH, " + //--2急货
                "       A.ORDER_NO, " + //--3订单号
                "       A.CONSIGNEE_CODE, " + //--4委托方
                "       A.CUSTOMER_BU, " + //--5客户事业部
                "       A.CUSTOMER_ORDER_NO, " + //--6客户单据编号
                "       A.SHIPPER, " + //--7实际发货方
                "       A.RECEIVER, " + //--8实际收货方
                "       A.OP_DATE, " +
                "       A.OP_TYPE, " + //--10业务类型
                "       A.PRODUCT_CODE, " + //--11业务产品/项目
                "       A.FWLX_NAME, " + //--12服务类型
                "       A.FKFA_CODE, " + //--13客户付款方案
                "       A.STATUS,\n" +
                "       A.COMPANY_CODE,\n" +
                "       A.XZFA_NO, " + //--14协作方案
                "       A.BIZ_MEMO, " + //--15业务备注
                "       A.CREATE_DATE, " + //--16创建时间
                "       A.JD_NODE, " + //--17接单组织
                "       A.PRE_NO, " + //--18协同编号 (pre)
                "       A.CANCEL_USER, " + //--作废人
                "       A.CANCEL_USER_NAME, " + //--作废人名称
                "       A.CANCEL_DATE, " + //--作废时间
                "       A.CANCEL_REMARK, " + //--作废原因
                "       A.CREATE_USER_NAME, " + //--创建人
                "       A.STAGE, " + //--执行阶段
                "       A.JD_USER, " + //--接单员
                "       A.GROUP_CODE,\n" +
                "       A.BIZ_REG_ID,\n" +
                "       A.CONFIRM_DATE, " + //--接单日期
                "       CASE\n" +
                "         WHEN A.OP_COMPLETE_OK = 'Y' THEN\n" +
                "          '√'\n" +
                "         ELSE\n" +
                "          ' '\n" +
                "       END OP_COMPLETE_OK, " + //--作业完成
                "       CASE\n" +
                "         WHEN A.DATA_OK = 'Y' THEN\n" +
                "          '√'\n" +
                "         ELSE\n" +
                "          ' '\n" +
                "       END DATA_OK, " + //--作业数据齐全
                "       CASE\n" +
                "         WHEN A.ARAP_OK = 'Y' THEN\n" +
                "          '√'\n" +
                "         ELSE\n" +
                "          ' '\n" +
                "       END ARAP_OK, " + //--结算完成日期（结案日期)
                "       A.ARAP_OK_DATE, " + //--结算完成日期（结案日期，
                "       A.CROSS_ITEM, " + //--项目
                "       A.IS_QRJD, " + //--确认接单必填项
                "       A.IS_XZFF, " + //--协作分发必填项
                "       (SELECT MIN(X.SYS_DOC_NO) FROM OMS_ORDER_PRE X WHERE X.PRE_NO = A.PRE_NO) AS SYS_DOC_NO, " + //--来源系统编号
                "       CASE\n" +
                "         WHEN A.AP_OK = 'Y' THEN\n" +
                "          '√'\n" +
                "         ELSE\n" +
                "          NULL\n" +
                "       END AS AP_OK,\n" +
//                "       NVL((OMS_NAMECOM(A.SHIPPER, A.GROUP_CODE, 'CRM_CUSTOMER_SFHF')),\n" +
//                "            A.SHIPPER) AS SHIPPER_NAME, " + //-- --9实际收货方
//                "       NVL((OMS_NAMECOM(A.RECEIVER, A.GROUP_CODE, 'CRM_CUSTOMER_SFHF')),\n" +
//                "            A.RECEIVER) AS RECEIVER_NAME, " + //-- --9实际收货方
//                "       OMS_NAMECOM(A.JD_USER, A.JD_COMPANY, 'OMS_SSO_USER') JD_USER_NAME,\n" +
//                "       NVL((BMC_NAME(A.FKFA_CODE, 'BMC_MMS_FKFA')), A.FKFA_CODE) AS FKFA_CODE_NAME,\n" +
                "       (SELECT MIN(X.FWXM_CODE) " +
                "          FROM OMS_ORDER_FWXM X\n" +
                "         WHERE X.ORDER_NO = A.ORDER_NO) AS FWXM_CODE,\n" +
                "       CASE\n" +
                "         WHEN EXISTS (SELECT DD.PRE_NO\n" +
                "                      FROM OMS_ORDER_PRE DD\n" +
                "                     WHERE DD.PRE_NO = A.PRE_NO\n" +
                "                       AND DD.SYS_CODE = 'OMS') THEN\n" +
                "          'Y'\n" +
                "         ELSE\n" +
                "          'N'\n" +
                "       END AS IS_OMS,\n" +
                "       B.MB_NO,\n" +
                "       B.HB_NO,\n" +
                "       B.WAREHOUSE_IN_NO,\n" +
                "       B.PIECE_TOTAL,\n" +
                "       A.UDF9,\n" +
                "       C.SYS_ORDER_BATCH,\n" +
                "       C.CUSTOMER_ORDER_BATCH,\n" +
                "  (SELECT MIN(XL.QHC)\n" +
                "          FROM OMS_ORDER_FWXM_TMS X\n" +
                "          LEFT JOIN OMS_ORDER_FWXM_TMS_XL XL\n" +
                "            ON XL.TMS_NO = X.TMS_NO\n" +
                "         WHERE X.ORDER_NO = A.ORDER_NO \n";
        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getFwxmCode())) {
            sql += Zsr.String.format(" AND X.FWXM_CODE = {0} ", cmn.SQLQ(queryOrderInfoDto.getFwxmCode()));
        }
        sql += "        ) AS QHC " +
                "  FROM (SELECT OO.*,\n" +
                "               (SELECT MIN(XL.CROSS_ITEM)\n" +
                "                  FROM OMS_ORDER_FWXM_TMS X\n" +
                "                  LEFT JOIN OMS_ORDER_FWXM_TMS_XL XL\n" +
                "                   ON XL.TMS_NO = X.TMS_NO\n" +
                "                 WHERE X.ORDER_NO = OO.ORDER_NO\n";

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getFwxmCode())) {
            sql += Zsr.String.format(" AND X.FWXM_CODE = {0} ", cmn.SQLQ(queryOrderInfoDto.getFwxmCode()));
        }

        sql += " ) AS CROSS_ITEM\n" +
                "          FROM OMS_ORDER OO) A\n" +
                "  LEFT JOIN OMS_ORDER_GOODS B\n" +
                "    ON A.ORDER_NO = B.ORDER_NO\n" +
                "  LEFT JOIN OMS_ORDER_BATCH C\n" +
                "    ON C.SYS_ORDER_BATCH = A.SYS_ORDER_BATCH\n" +
                " WHERE A.GROUP_CODE   = " + cmn.SQLQ(UserContext.getUserInfo().getCompanyCode());

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getFwlxCode())) {
            sql += " AND EXISTS(SELECT 1 FROM OMS_ORDER_FW FW WHERE FW.ORDER_NO = A.ORDER_NO AND A.GROUP_CODE = FW.GROUP_CODE AND FW.FWLX_CODE=" + cmn.SQLQ(queryOrderInfoDto.getFwlxCode()) + ")";
        }

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getOrderNo())) {
            sql += Zsr.String.format("   AND (A.ORDER_NO LIKE {0}) ", cmn.SQLQL(queryOrderInfoDto.getOrderNo()));
        }

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getCustomerOrderNo())) {
            sql += Zsr.String.format("   AND (A.CUSTOMER_ORDER_NO LIKE {0} ) ", cmn.SQLQL(queryOrderInfoDto.getCustomerOrderNo()));
        }

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getConsigneeCode())) {
            sql += Zsr.String.format("   AND A.CONSIGNEE_CODE = {0} ", cmn.SQLQ(queryOrderInfoDto.getConsigneeCode()));
        }

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getOpType())) {
            sql += Zsr.String.format("   AND A.OP_TYPE = {0} ", cmn.SQLQ(queryOrderInfoDto.getOpType()));
        }

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getIsJjh())) {
            sql += Zsr.String.format("   AND A.IS_JJH = {0} ", cmn.SQLQ(queryOrderInfoDto.getIsJjh()));
        }

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getJdUser())) {
            sql += Zsr.String.format("   AND A.JD_USER = {0} ", cmn.SQLQ(queryOrderInfoDto.getJdUser()));
        }

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getCreateDateStart())) {
            sql += Zsr.String.format(" AND A.CREATE_DATE >= to_date( {0} , 'yyyy-MM-dd HH24:mi:ss') ", cmn.SQLQ(
                    ZsrDateUtils.stringToDate(queryOrderInfoDto.getCreateDateStart())));
        }

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getCreateDateEnd())) {
            sql += Zsr.String.format(" AND A.CREATE_DATE <  to_date( {0} , 'yyyy-MM-dd HH24:mi:ss')", cmn.SQLQ(
                    ZsrDateUtils.stringToDate(queryOrderInfoDto.getCreateDateEnd())));
        }

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getShipper())) {
            sql += Zsr.String.format("   AND (A.SHIPPER LIKE {0} ) ", cmn.SQLQL(queryOrderInfoDto.getShipper()));
        }

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getReceiver())) {
            sql += Zsr.String.format(" AND (A.RECEIVER LIKE {0} ) ", cmn.SQLQL(queryOrderInfoDto.getReceiver()));
        }

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getPreNo())) {
            sql += Zsr.String.format(" AND (A.PRE_NO LIKE {0} ) ", cmn.SQLQL(queryOrderInfoDto.getPreNo()));
        }


        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getFwxmCode())) {
            sql += "  AND EXISTS (SELECT 1 FROM OMS_ORDER_FWXM X  WHERE X.ORDER_NO = A.ORDER_NO AND X.FWXM_CODE=" + cmn.SQLQ(queryOrderInfoDto.getFwxmCode()) + ")  ";
        }

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getSysDocNo())) {
            sql += " AND EXISTS(SELECT 1 FROM OMS_ORDER_PRE X WHERE SYS_DOC_NO LIKE " + cmn.SQLQL(queryOrderInfoDto.getSysDocNo()) + " AND A.PRE_NO=X.PRE_NO) ";
        }

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getStage())) {
            sql += Zsr.String.format(" AND A.STAGE = {0} ", cmn.SQLQ(queryOrderInfoDto.getStage()));
        }

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getRequestOkDateStart())) {
            sql += Zsr.String.format(" AND A.REQUEST_OK_DATE >= to_date( {0} , 'yyyy-MM-dd HH24:mi:ss') ", cmn.SQLQ(
                    ZsrDateUtils.stringToDate(queryOrderInfoDto.getRequestOkDateStart())
            ));
        }
        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getRequestOkDateEnd())) {
            sql += Zsr.String.format(" AND A.REQUEST_OK_DATE <  to_date( {0} , 'yyyy-MM-dd HH24:mi:ss')", cmn.SQLQ(
                    ZsrDateUtils.stringToDate(queryOrderInfoDto.getRequestOkDateEnd())
            ));
        }

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getOpDateStart())) {
            sql += Zsr.String.format(" AND A.OP_DATE >= to_date( {0} , 'yyyy-MM-dd HH24:mi:ss') ", cmn.SQLQ(
                    ZsrDateUtils.stringToDate(queryOrderInfoDto.getOpDateStart())
            ));
        }
        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getOpDateEnd())) {
            sql += Zsr.String.format(" AND A.OP_DATE <  to_date( {0} , 'yyyy-MM-dd HH24:mi:ss')", cmn.SQLQ(
                    ZsrDateUtils.stringToDate(queryOrderInfoDto.getOpDateEnd())
            ));
        }
        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getOpCompleteOk())) {
            sql += Zsr.String.format(" AND A.OP_COMPLETE_OK = {0} ", cmn.SQLQ(queryOrderInfoDto.getOpCompleteOk()));
        }
        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getDataOk())) {
            sql += Zsr.String.format(" AND A.DATA_OK = {0} ", cmn.SQLQ(queryOrderInfoDto.getDataOk()));
        }
        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getArapOk())) {
            sql += Zsr.String.format(" AND A.ARAP_OK = {0} ", cmn.SQLQ(queryOrderInfoDto.getArapOk()));
        }
        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getApOkQuery())) {
            sql += Zsr.String.format(" AND A.AP_OK_QUERY = {0} ", cmn.SQLQ(queryOrderInfoDto.getApOkQuery()));
        }


        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getOpTypeUser()))//控制业务类型显示
        {
            sql += " AND EXISTS(SELECT 1 FROM FZGJ_BD_OP_TYPE_USER U WHERE U.USER_CODE=" + cmn.SQLQ(UserContext.getUserInfo().getUserLoginNo()) + " AND A.OP_TYPE=U.OP_TYPE_CODE)  ";
        }
        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getMbNo())) {
            sql += Zsr.String.format(" AND A.MB_NO LIKE {0} ", cmn.SQLQL(queryOrderInfoDto.getMbNo()));
        }
        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getHbNo())) {
            sql += Zsr.String.format(" AND A.HB_NO LIKE {0} ", cmn.SQLQL(queryOrderInfoDto.getHbNo()));
        }

        String WAREHOUSE_IN_NO = queryOrderInfoDto.getWarehouseInNo();
        String condition = "";
        if (!Zsr.String.IsNullOrWhiteSpace(WAREHOUSE_IN_NO)) {
            if (WAREHOUSE_IN_NO.contains(",")) {
                String[] listWare = WAREHOUSE_IN_NO.split(",");
                StringBuilder sb = new StringBuilder(" AND (");
                for (String s : listWare) {
                    sb.append(" B.WAREHOUSE_IN_NO LIKE '%").append(s.trim()).append("%' OR");
                }
                // 删除最后多余的 " OR"
                sb.delete(sb.length() - 3, sb.length());
                sb.append(")");
                condition = sb.toString();
            } else {
                condition = " AND B.WAREHOUSE_IN_NO LIKE '%" + WAREHOUSE_IN_NO + "%'";
            }
            sql += condition;
        }

        // 添加订单状态查询，默认查询所有订单
        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getOrderStatus())) {
            sql += Zsr.String.format(" AND A.STATUS = {0} ", cmn.SQLQ(queryOrderInfoDto.getOrderStatus()));
        }else{
            sql += Zsr.String.format(" AND A.STATUS IS NOT NULL AND  A.STATUS <> '' ");
        }

        sql += " AND A.ORDER_NO NOT LIKE 'temp%' ";

        // 添加去回程查询
        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getQhc())) {
            sql += Zsr.String.format("    AND EXISTS \n" +
                    " (SELECT 1 FROM OMS_ORDER_FWXM_TMS X\n" +
                    "          LEFT JOIN OMS_ORDER_FWXM_TMS_XL XL\n" +
                    "            ON XL.TMS_NO = X.TMS_NO\n" +
                    "         WHERE X.ORDER_NO = A.ORDER_NO\n" +
                    "           AND XL.QHC =  {0} " +
                    "        ) ", cmn.SQLQ(queryOrderInfoDto.getQhc()));
        }
        try {
            TgPageInfo<OrderInfoDto> pageInfo = DBHelper.selectPageList(sql, OrderInfoDto.class);
            return pageInfoCode2Name(pageInfo);
        } catch (Exception e) {
            return new TgPageInfo();
        }

    }


    public TgPageInfo<OrderInfoDto> pageInfoCode2Name(TgPageInfo<OrderInfoDto> pageInfo) throws InterruptedException {
        if (pageInfo == null || pageInfo.getList() == null || pageInfo.getList().isEmpty()) {
            return pageInfo;
        }

        // 定义需要查询的字段映射关系
        Map<String, String> fieldTypeMap = new HashMap<>();
        fieldTypeMap.put("consigneeCode", "CRM_CUSTOMER_SFHF");
        fieldTypeMap.put("customerBu", "CRM_CUSTOMER_KHSYB");
        fieldTypeMap.put("opType", "OMS_BD_OP_TYPE");
        fieldTypeMap.put("xzfaNo", "OMS_XZFA");
        fieldTypeMap.put("crossItem", "OMS_BD_XM");

        Map<String, String> fieldTypeMap2 = new HashMap<>();
        fieldTypeMap.put("stage", "STAGE");
        fieldTypeMap.put("jdNode", "JD_NODE");

        // 收集所有需要查询的字段值
        Map<String, List<String>> typeValuesMap = new HashMap<>();
        Map<String, List<String>> typeValuesMap2 = new HashMap<>();
        pageInfo.getList().forEach(item -> {
            if (item != null) {
                fieldTypeMap.forEach((field, type) -> {
                    String value = item.getFieldValue(field);
                    if (value != null && !value.isEmpty()) {
                        typeValuesMap.computeIfAbsent(type, k -> new ArrayList<>()).add(value);
                    }
                });
                fieldTypeMap2.forEach((field, type) -> {
                    String value = item.getFieldValue(field);
                    if (value != null && !value.isEmpty()) {
                        typeValuesMap2.computeIfAbsent(type, k -> new ArrayList<>()).add(value);
                    }
                });
            }
        });

        try {
            // 批量查询所有类型的 CodeNameEntity
            Map<String, Map<String, String>> codeNameMaps = new HashMap<>();
            for (Map.Entry<String, List<String>> entry : typeValuesMap.entrySet()) {
                String type = entry.getKey();
                List<String> codes = entry.getValue();
                if (!codes.isEmpty()) {
                    List<CodeNameEntity> entities = getCodeNameEntities(type, codes);
                    codeNameMaps.put(type, buildCodeNameMap(entities));
                }
            }
            for (Map.Entry<String, List<String>> entry : typeValuesMap2.entrySet()) {
                String type = entry.getKey();
                List<String> codes = entry.getValue();
                if (!codes.isEmpty()) {
                    List<CodeNameEntity> entities = getCodeNameEntities2(type, codes);
                    codeNameMaps.put(type, buildCodeNameMap(entities));
                }
            }
            // 使用并行流更新 DTO
            pageInfo.getList().parallelStream().forEach(item -> {
                if (item != null) {
                    fieldTypeMap.forEach((field, type) -> {
                        Map<String, String> codeNameMap = codeNameMaps.get(type);
                        if (codeNameMap != null) {
                            String codeValue = item.getFieldValue(field);
                            if (codeValue != null) {
                                String name = codeNameMap.get(codeValue);
                                if (name != null) {
                                    item.push(field + "Name", name);
                                }
                            }
                        }
                    });
                }
            });
        } catch (Exception e) {
            log.error("Failed to process pageInfoCode2Name", e);
            throw new RuntimeException("Failed to process pageInfoCode2Name", e);
        }

        return pageInfo;
    }

    // 批量查询 CodeNameEntity
    private List<CodeNameEntity> getCodeNameEntities(String tableName, List<String> queryList) {
        String sql = SQLGenerator.OMS_NAMECOM(tableName, queryList);
        if (Zsr.String.IsNullOrWhiteSpace(sql)) {
            return new ArrayList<>();
        }
        List<CodeNameEntity> codeNameEntities = DBHelper.selectList(
                sql,
                CodeNameEntity.class);

        // Ensure list is not null before streaming
        if (codeNameEntities == null) {
            return new ArrayList<>();
        }

        // Apply distinct and return
        return codeNameEntities.stream()
                .distinct()
                .collect(Collectors.toList());
    }

    // 批量查询 CodeNameEntity
    private List<CodeNameEntity> getCodeNameEntities2(String tableName, List<String> queryList) {
        String sql = SQLGenerator.OMS_NAME(tableName, queryList);
        if (Zsr.String.IsNullOrWhiteSpace(sql)) {
            return new ArrayList<>();
        }
        List<CodeNameEntity> codeNameEntities = DBHelper.selectList(
                sql,
                CodeNameEntity.class);

        // Ensure list is not null before streaming
        if (codeNameEntities == null) {
            return new ArrayList<>();
        }

        // Apply distinct and return
        return codeNameEntities.stream()
                .distinct()
                .collect(Collectors.toList());
    }


    // 构建 Code-Name 映射
    private Map<String, String> buildCodeNameMap(List<CodeNameEntity> entities) {
        return entities != null ? entities.stream()
                .collect(Collectors.toMap(CodeNameEntity::getCode, CodeNameEntity::getName, (v1, v2) -> v1, HashMap::new))
                : Collections.emptyMap();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderFwxmTmsEntity save(OmsOrderFwxmTmsEntity entity) {
        // 返回实体对象
        OmsOrderFwxmTmsEntity omsOrderFwxmTmsEntity = null;
        omsOrderFwxmTmsVal.saveValidate(entity, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            omsOrderFwxmTmsEntity = omsOrderFwxmTmsDao.insertOne(entity);

        } else {

            omsOrderFwxmTmsEntity = omsOrderFwxmTmsDao.updateByEntityId(entity);

        }
        return omsOrderFwxmTmsEntity;
    }

    @Override
    public List<OmsOrderFwxmTmsEntity> selectList(OmsOrderFwxmTmsEntity entity) {
        return omsOrderFwxmTmsDao.selectList(entity);
    }

    @Override
    public OmsOrderFwxmTmsEntity selectOneById(Serializable id) {
        return omsOrderFwxmTmsDao.selectById(id);
    }


    @Override
    public void insertBatch(List<OmsOrderFwxmTmsEntity> list) {
        omsOrderFwxmTmsDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return omsOrderFwxmTmsDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return omsOrderFwxmTmsDao.deleteById(id);
    }

}