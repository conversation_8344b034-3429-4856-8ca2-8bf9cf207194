package com.eci.project.crmCustomerPosition.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.crmCustomerPosition.service.CrmCustomerPositionService;
import com.eci.project.crmCustomerPosition.entity.CrmCustomerPositionEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 合作伙伴职务Controller
*
* @<NAME_EMAIL>
* @date 2025-05-26
*/
@Api(tags = "合作伙伴职务")
@RestController
@RequestMapping("/crmCustomerPosition")
public class CrmCustomerPositionController extends EciBaseController {

    @Autowired
    private CrmCustomerPositionService crmCustomerPositionService;


    @ApiOperation("合作伙伴职务:保存")
    @EciLog(title = "合作伙伴职务:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody CrmCustomerPositionEntity entity){
        CrmCustomerPositionEntity crmCustomerPositionEntity =crmCustomerPositionService.save(entity);
        return ResponseMsgUtil.success(10001,crmCustomerPositionEntity);
    }


    @ApiOperation("合作伙伴职务:查询列表")
    @EciLog(title = "合作伙伴职务:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody CrmCustomerPositionEntity entity){
        List<CrmCustomerPositionEntity> crmCustomerPositionEntities = crmCustomerPositionService.selectList(entity);
        return ResponseMsgUtil.success(10001,crmCustomerPositionEntities);
    }


    @ApiOperation("合作伙伴职务:分页查询列表")
    @EciLog(title = "合作伙伴职务:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody CrmCustomerPositionEntity entity){
        TgPageInfo tgPageInfo = crmCustomerPositionService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("合作伙伴职务:根据ID查一条")
    @EciLog(title = "合作伙伴职务:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody CrmCustomerPositionEntity entity){
        CrmCustomerPositionEntity  crmCustomerPositionEntity = crmCustomerPositionService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,crmCustomerPositionEntity);
    }


    @ApiOperation("合作伙伴职务:根据ID删除一条")
    @EciLog(title = "合作伙伴职务:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody CrmCustomerPositionEntity entity){
        int count = crmCustomerPositionService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("合作伙伴职务:根据ID字符串删除多条")
    @EciLog(title = "合作伙伴职务:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody CrmCustomerPositionEntity entity) {
        int count = crmCustomerPositionService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}