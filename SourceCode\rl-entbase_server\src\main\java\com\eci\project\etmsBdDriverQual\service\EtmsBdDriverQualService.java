package com.eci.project.etmsBdDriverQual.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eci.common.db.DBHelper;
import com.eci.common.util.DateUtils;
import com.eci.common.util.StringUtils;
import com.eci.common.utils.ExportUtils.ExcelProcess;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.ActionType;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.etmsBdDriverQual.dao.EtmsBdDriverQualDao;
import com.eci.project.etmsBdDriverQual.entity.EtmsBdDriverQualEntity;
import com.eci.project.etmsBdDriverQual.entity.EtmsBdDriverQualSearchEntity;
import com.eci.project.etmsBdDriverQual.validate.EtmsBdDriverQualVal;

import com.eci.wu.core.DataTable;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
* 司机从业资格证Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-17
*/
@Service
@Slf4j
public class EtmsBdDriverQualService implements EciBaseService<EtmsBdDriverQualEntity> {

    @Autowired
    private EtmsBdDriverQualDao etmsBdDriverQualDao;

    @Autowired
    private EtmsBdDriverQualVal etmsBdDriverQualVal;


    @Override
    public TgPageInfo queryPageList(EtmsBdDriverQualEntity entity) {
        EciQuery<EtmsBdDriverQualEntity> eciQuery = EciQuery.buildQuery(entity);
        List<EtmsBdDriverQualEntity> entities = etmsBdDriverQualDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }
    public TgPageInfo queryPageList1(EtmsBdDriverQualEntity entity) {
        QueryWrapper query=new QueryWrapper();
        if(entity.getGuid()!=null&&!entity.getGuid().isEmpty())
            query.apply("DRIVER_GUID={0}",entity.getGuid());
        if(entity.getStatus()!=null&&!entity.getStatus().isEmpty())
            query.apply(" STATUS ={0}",entity.getStatus());
        if(entity.getCardStatus()!=null&&!entity.getCardStatus().isEmpty())
            query.apply(" cardStatus={0}",entity.getCardStatus());
        Integer pageSize = BllContext.getPaging().getPageSize();
        Integer pageNum = BllContext.getPaging().getPageNum();
        PageHelper.startPage(pageNum, pageSize == -1 ? 0 : pageSize);
        List<EtmsBdDriverQualEntity> entities = etmsBdDriverQualDao.queryPages(query);
        return EciQuery.getPageInfo(entities);
    }

    public TgPageInfo queryQualPageList(EtmsBdDriverQualSearchEntity entity) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("SELECT  A.NAME,A.IS_GK,(SELECT X.MAPPING_ABBREVIATION FROM ETMS_CRM_PARTNER X WHERE X.GUID=A.PARTNER_GUID AND X.GROUP_CODE=A.GROUP_CODE) PARTNER_NAME, B.START_DATE,B.END_DATE,B.CREATE_COMPANY,B.CREATE_DATE\n" +
                ",(SELECT T.NAME FROM ETMS_CRM_ENTERPRISE T  WHERE T.STATUS='Y' AND T.CODE=B.CREATE_COMPANY) AS CREATE_COMPANY_NAME\n" +
                "FROM ETMS_BD_DRIVER A INNER JOIN ETMS_BD_DRIVER_QUAL B ON A.GUID=B.DRIVER_GUID where 1=1 \n");
        if(StringUtils.hasValue(entity.getName())){
            stringBuilder.append(" AND A.NAME like '%"+entity.getName()+"%'");
        }
        if(StringUtils.hasValue(entity.getPartnerName())){
            stringBuilder.append(" AND (SELECT X.MAPPING_ABBREVIATION FROM ETMS_CRM_PARTNER X WHERE X.GUID=A.PARTNER_GUID AND X.GROUP_CODE=A.GROUP_CODE) = '"+entity.getPartnerName()+"'");
        }
        if(StringUtils.hasValue(entity.getIsGk())){
            stringBuilder.append(" AND A.IS_GK = '"+entity.getIsGk()+"'");
        }
        if(StringUtils.hasValue(entity.getCreateCompany())){
            stringBuilder.append(" AND B.CREATE_COMPANY like '%"+entity.getCreateCompany()+"%'");
        }
        if (entity.getStartDateStart()!=null) {
            stringBuilder.append(" AND B.START_DATE >= TO_DATE('" + DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss",entity.getStartDateStart()) + "', 'yyyy-mm-dd hh24:mi:ss')");
        }
        if (entity.getStartDateEnd()!=null) {
            stringBuilder.append(" AND B.START_DATE <= TO_DATE('" + DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss",entity.getStartDateEnd()) + "', 'yyyy-mm-dd hh24:mi:ss')");
        }
        if (entity.getEndDateStart()!=null) {
            stringBuilder.append(" AND B.END_DATE >= TO_DATE('" + DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss",entity.getEndDateStart()) + "', 'yyyy-mm-dd hh24:mi:ss')");
        }
        if (entity.getEndDateEnd()!=null) {
            stringBuilder.append(" AND B.END_DATE <= TO_DATE('" + DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss",entity.getEndDateEnd()) + "', 'yyyy-mm-dd hh24:mi:ss')");
        }
        if (entity.getCreateDateStart()!=null) {
            stringBuilder.append(" AND B.CREATE_DATE >= TO_DATE('" + DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss",entity.getCreateDateStart()) + "', 'yyyy-mm-dd hh24:mi:ss')");
        }
        if (entity.getCreateDateEnd()!=null) {
            stringBuilder.append(" AND B.CREATE_DATE <= TO_DATE('" + DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss",entity.getCreateDateEnd()) + "', 'yyyy-mm-dd hh24:mi:ss')");
        }
        if (BllContext.getBusinessType() == BusinessType.EXPORT) {
            if (BllContext.getRequestEntity().getActionType() == ActionType.ASYNC) {
                etmsBdDriverQualDao.asyncExportDefaultExcel(()->{
                    List<EtmsBdDriverQualSearchEntity> entities = DBHelper.selectList(stringBuilder.toString(), EtmsBdDriverQualSearchEntity.class);
                    entities.forEach(qzSearchEntity -> {
                        if (StringUtils.hasValue(qzSearchEntity.getEndDate()) && DateUtils.parseDate(qzSearchEntity.getEndDate().substring(0,19)).compareTo(DateUtils.parseDate(DateUtils.getDate())) >= 0) {
                            qzSearchEntity.setStatus("有效");
                        } else {
                            qzSearchEntity.setStatus("无效");
                        }
                        if (qzSearchEntity.getIsGk().equals("Y")) {
                            qzSearchEntity.setIsGk("是");
                        } else {
                            qzSearchEntity.setIsGk("否");
                        }
                        qzSearchEntity.setStartDate(qzSearchEntity.getStartDate().substring(0, 10));
                        qzSearchEntity.setEndDate(qzSearchEntity.getEndDate().substring(0, 10));
                        qzSearchEntity.setModMark("否");
                    });
                    return entities;
                }, ExcelProcess.BuildConfig("司机资质管理", EtmsBdDriverQualSearchEntity.class));
            } else {
                etmsBdDriverQualDao.exportDefaultExcel(() -> {
                    List<EtmsBdDriverQualSearchEntity> entities = DBHelper.selectList(stringBuilder.toString(), EtmsBdDriverQualSearchEntity.class);
                    entities.forEach(qzSearchEntity -> {
                        if (StringUtils.hasValue(qzSearchEntity.getEndDate()) && DateUtils.parseDate(qzSearchEntity.getEndDate().substring(0,19)).compareTo(DateUtils.parseDate(DateUtils.getDate())) >= 0) {
                            qzSearchEntity.setStatus("有效");
                        } else {
                            qzSearchEntity.setStatus("无效");
                        }
                        if (qzSearchEntity.getIsGk().equals("Y")) {
                            qzSearchEntity.setIsGk("是");
                        } else {
                            qzSearchEntity.setIsGk("否");
                        }
                        qzSearchEntity.setStartDate(qzSearchEntity.getStartDate().substring(0, 10));
                        qzSearchEntity.setEndDate(qzSearchEntity.getEndDate().substring(0, 10));
                        qzSearchEntity.setModMark("否");
                    });
                    return entities;
                }, ExcelProcess.BuildConfig("司机资质管理", EtmsBdDriverQualSearchEntity.class));
            }
            return new TgPageInfo<>();
        }else{
            startPage();
            List<EtmsBdDriverQualSearchEntity> entities = DBHelper.selectList(stringBuilder.toString(), EtmsBdDriverQualSearchEntity.class);
            entities.forEach(qzSearchEntity -> {
                Date now=DateUtils.parseDate(DateUtils.getDate());
                if (StringUtils.hasValue(qzSearchEntity.getEndDate()) && DateUtils.parseDate(qzSearchEntity.getEndDate().substring(0, 19)).compareTo(now) >= 0) {
                    qzSearchEntity.setStatus("有效");
                } else {
                    qzSearchEntity.setStatus("无效");
                }
                if(qzSearchEntity.getIsGk().equals("Y")){
                    qzSearchEntity.setIsGk("是");
                }else{
                    qzSearchEntity.setIsGk("否");
                }
                qzSearchEntity.setModMark("否");
            });
            return EciQuery.getPageInfo(entities);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public EtmsBdDriverQualEntity save(EtmsBdDriverQualEntity entity) {
        // 返回实体对象
        EtmsBdDriverQualEntity etmsBdDriverQualEntity = null;
        etmsBdDriverQualVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            etmsBdDriverQualEntity = etmsBdDriverQualDao.insertOne(entity);

        }else{

            etmsBdDriverQualEntity = etmsBdDriverQualDao.updateByEntityId(entity);

        }
        return etmsBdDriverQualEntity;
    }

    @Override
    public List<EtmsBdDriverQualEntity> selectList(EtmsBdDriverQualEntity entity) {
        return etmsBdDriverQualDao.selectList(entity);
    }

    @Override
    public EtmsBdDriverQualEntity selectOneById(Serializable id) {
        return etmsBdDriverQualDao.selectById(id);
    }


    @Override
    public void insertBatch(List<EtmsBdDriverQualEntity> list) {
        etmsBdDriverQualDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return etmsBdDriverQualDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return etmsBdDriverQualDao.deleteById(id);
    }

}