package com.eci.project.fzgjFile.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjFile.entity.FzgjFileEntity;

import org.springframework.stereotype.Service;


/**
* 附件Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-06-04
*/
@Service
public class FzgjFileVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjFileEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjFileEntity entity, BusinessType businessType) {

    }

}
