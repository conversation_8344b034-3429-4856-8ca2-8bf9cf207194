package com.eci.project.fzgjBaseInformation.service;

import cn.hutool.json.JSONConverter;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.Zsr;
import com.eci.common.ZsrDBHelper;
import com.eci.common.db.DBHelper;
import com.eci.common.util.DateUtils;
import com.eci.common.util.EciSqlUtl;
import com.eci.common.util.StringUtils;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjBaseInformation.dao.FzgjBaseInformationDao;
import com.eci.project.fzgjBaseInformation.entity.FzgjBaseInformationEntity;
import com.eci.project.fzgjBaseInformation.validate.FzgjBaseInformationVal;

import com.eci.sso.role.entity.UserContext;
import com.eci.wu.core.DataTable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.Reader;
import java.io.Serializable;
import java.sql.*;
import java.util.List;


/**
 * 新闻通知公告信息表Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-06-18
 */
@Service
@Slf4j
public class FzgjBaseInformationService implements EciBaseService<FzgjBaseInformationEntity> {

    @Autowired
    private FzgjBaseInformationDao fzgjBaseInformationDao;

    @Autowired
    private FzgjBaseInformationVal fzgjBaseInformationVal;

    private static String clobToString(Clob clob) {
        StringBuilder sb = new StringBuilder();
        if (clob != null) {
            try (Reader reader = clob.getCharacterStream()) {
                char[] buffer = new char[1024];
                int bytesRead;
                while ((bytesRead = reader.read(buffer)) != -1) {
                    sb.append(buffer, 0, bytesRead);
                }
            } catch (SQLException | IOException e) {
                throw new RuntimeException(e);
            }
        }
        return sb.toString();
    }

    @Override
    public TgPageInfo queryPageList(FzgjBaseInformationEntity entity) {
        EciQuery<FzgjBaseInformationEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjBaseInformationEntity> entities = fzgjBaseInformationDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjBaseInformationEntity save(FzgjBaseInformationEntity entity) {
        // 返回实体对象
        FzgjBaseInformationEntity fzgjBaseInformationEntity = null;
        fzgjBaseInformationVal.saveValidate(entity, BllContext.getBusinessType());

        if (entity.getStatus() == 1) {
            entity.setPublishdate(DateUtils.getNowDate());
        }
        if(entity.getSort()==null){
            entity.setSort(999);
        }
        if (!StringUtils.hasValue(entity.getGuid())) {
            entity.setGuid(IdWorker.get32UUID());
            entity.setCreateDate(DateUtils.getNowDate());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            entity.setCompanyName(UserContext.getUserInfo().getCompanyName());
            entity.setNodeCode(UserContext.getUserInfo().getDeptCode());
            entity.setNodeName(UserContext.getUserInfo().getDeptName());
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
            fzgjBaseInformationEntity = fzgjBaseInformationDao.insertOne(entity);
        } else {
            entity.setUpdateDate(DateUtils.getNowDate());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            fzgjBaseInformationEntity = fzgjBaseInformationDao.updateByEntityId(entity);
        }
        return fzgjBaseInformationEntity;
    }

    @Override
    public List<FzgjBaseInformationEntity> selectList(FzgjBaseInformationEntity entity) {
        return fzgjBaseInformationDao.selectList(entity);
    }

    @Override
    public FzgjBaseInformationEntity selectOneById(Serializable id) {
        FzgjBaseInformationEntity entity = new FzgjBaseInformationEntity();
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;

        try {
            conn = new ZsrDBHelper().getCurrentConnection();
            stmt = conn.createStatement();
            rs = stmt.executeQuery(String.format("SELECT * FROM FZGJ_BASE_INFORMATION WHERE GUID='%s'", id));

            if (rs.next()) { // 使用 if 而非 while，确保仅处理第一条记录

                // 处理 CLOB 或普通文本
                String content = DBHelper.isOracle() ?
                        clobToString(rs.getClob("content")) :
                        rs.getString("content");
                entity.setContent(content);
                // 设置其他字段
                entity.setGuid(rs.getString("guid"));
                entity.setTitle(rs.getString("title"));
                entity.setStatus(rs.getInt("status"));
                entity.setInfoType(rs.getInt("info_type"));
                entity.setIsRecommend(rs.getInt("is_recommend"));
                entity.setSort(rs.getInt("sort"));
                entity.setAuthor(rs.getString("author"));
                entity.setPublishdate(rs.getDate("publishdate"));
                entity.setAttachmentUrl(rs.getString("attachment_url"));
                entity.setRemark(rs.getString("remark"));
                entity.setCreateDate(rs.getDate("create_date"));
                entity.setUpdateDate(rs.getDate("update_date"));
                entity.setCreateUserName(rs.getString("create_user_name"));
                entity.setUpdateUserName(rs.getString("update_user_name"));
                entity.setCompanyCode(rs.getString("company_code"));
                entity.setCompanyName(rs.getString("company_name"));
                entity.setNodeCode(rs.getString("node_code"));
                entity.setNodeName(rs.getString("node_name"));
                entity.setGroupCode(rs.getString("group_code"));
                entity.setGroupName(rs.getString("group_name"));
            } else {
                log.warn("未找到 GUID={} 的记录", id);
            }
        } catch (SQLException e) {
            throw new RuntimeException("数据库查询失败", e);
        } finally {
            // 关闭资源
            try {
                if (rs != null) {
                    rs.close();
                }
                if (stmt != null) {
                    stmt.close();
                }
                if (conn != null) {
                    conn.close();
                }
            } catch (SQLException e) {
                log.error("资源关闭异常", e);
            }
        }
        return entity;
    }

    @Override
    public void insertBatch(List<FzgjBaseInformationEntity> list) {
        fzgjBaseInformationDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjBaseInformationDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjBaseInformationDao.deleteById(id);
    }
}