<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.crmCustomerSfhfAddress.dao.CrmCustomerSfhfAddressDao">
    <resultMap type="CrmCustomerSfhfAddressEntity" id="CrmCustomerSfhfAddressResult">
        <result property="guid" column="GUID"/>
        <result property="customerGuid" column="CUSTOMER_GUID"/>
        <result property="opArea" column="OP_AREA"/>
        <result property="opAddress" column="OP_ADDRESS"/>
        <result property="opWh" column="OP_WH"/>
        <result property="opLink" column="OP_LINK"/>
        <result property="opTel" column="OP_TEL"/>
        <result property="opRequest" column="OP_REQUEST"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="isMr" column="IS_MR"/>
        <result property="status" column="STATUS"/>
        <result property="opAbbreviation" column="OP_ABBREVIATION"/>
        <result property="seq" column="SEQ"/>
        <result property="memo" column="MEMO"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="customerCode" column="CUSTOMER_CODE"/>
        <result property="distance" column="DISTANCE"/>
    </resultMap>

    <sql id="selectCrmCustomerSfhfAddressEntityVo">
        select
            GUID,
            CUSTOMER_GUID,
            OP_AREA,
            OP_ADDRESS,
            OP_WH,
            OP_LINK,
            OP_TEL,
            OP_REQUEST,
            CREATE_USER,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_DATE,
            IS_MR,
            STATUS,
            OP_ABBREVIATION,
            SEQ,
            MEMO,
            CREATE_USER_NAME,
            UPDATE_USER_NAME,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            CUSTOMER_CODE,
            DISTANCE
        from CRM_CUSTOMER_SFHF_ADDRESS
    </sql>
</mapper>