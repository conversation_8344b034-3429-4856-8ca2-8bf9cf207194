package com.eci.project.crmCustomerQual.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 资质管理对象 CRM_CUSTOMER_QUAL
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-05-26
*/
@ApiModel("资质管理")
@TableName("CRM_CUSTOMER_QUAL")
@FieldNameConstants
public class CrmCustomerQualEntity extends EciBaseEntity{
    /**
    * GUID
    */
    @ApiModelProperty("GUID(50)")
    @TableId("GUID")
    private String guid;

    /**
    * 司机GUID
    */
    @ApiModelProperty("司机GUID(50)")
    @TableField("CUSTOMER_GUID")
    private String customerGuid;

    /**
    * 证件编号
    */
    @ApiModelProperty("证件编号(30)")
    @TableField("QUAL_NO")
    private String qualNo;

    /**
    * 证件类型
    */
    @ApiModelProperty("证件类型(40)")
    @TableField("QUAL_TYPE")
    private String qualType;

    @ApiModelProperty("(100)")
    @TableField("QUAL_NAME")
    private String qualName;

    /**
    * 发证日期
    */
    @ApiModelProperty("发证日期(7)")
    @TableField("START_DATE")
    private Date startDate;

    @ApiModelProperty("发证日期开始")
    @TableField(exist=false)
    private Date startDateStart;

    @ApiModelProperty("发证日期结束")
    @TableField(exist=false)
    private Date startDateEnd;

    /**
    * 有效期
    */
    @ApiModelProperty("有效期(7)")
    @TableField("END_DATE")
    private Date endDate;

    @ApiModelProperty("有效期开始")
    @TableField(exist=false)
    private Date endDateStart;

    @ApiModelProperty("有效期结束")
    @TableField(exist=false)
    private Date endDateEnd;

    /**
    * 状态
    */
    @ApiModelProperty("状态(20)")
    @TableField("STATUS")
    private String status;
    @TableField(exist = false)
    private String statusName;
    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 备注
    */
    @ApiModelProperty("备注(20)")
    @TableField("MEMO")
    private String memo;

    @ApiModelProperty("(20)")
    @TableField("CREATE_USER")
    private String createUser;

    @ApiModelProperty("(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    @ApiModelProperty("(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("结束")
    @TableField(exist=false)
    private Date createDateEnd;

    @ApiModelProperty("(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 资质等级
    */
    @ApiModelProperty("资质等级(20)")
    @TableField("QUAL_LEVEL")
    private String qualLevel;

    /**
    * 颁发机构
    */
    @ApiModelProperty("颁发机构(50)")
    @TableField("ISSUING_AUTHORITY")
    private String issuingAuthority;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public CrmCustomerQualEntity() {
        this.setSubClazz(CrmCustomerQualEntity.class);
    }

    public CrmCustomerQualEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public CrmCustomerQualEntity setCustomerGuid(String customerGuid) {
        this.customerGuid = customerGuid;
        this.nodifySetFiled("customerGuid", customerGuid);
        return this;
    }

    public String getCustomerGuid() {
        this.nodifyGetFiled("customerGuid");
        return customerGuid;
    }

    public CrmCustomerQualEntity setQualNo(String qualNo) {
        this.qualNo = qualNo;
        this.nodifySetFiled("qualNo", qualNo);
        return this;
    }

    public String getQualNo() {
        this.nodifyGetFiled("qualNo");
        return qualNo;
    }

    public CrmCustomerQualEntity setQualType(String qualType) {
        this.qualType = qualType;
        this.nodifySetFiled("qualType", qualType);
        return this;
    }

    public String getQualType() {
        this.nodifyGetFiled("qualType");
        return qualType;
    }

    public CrmCustomerQualEntity setQualName(String qualName) {
        this.qualName = qualName;
        this.nodifySetFiled("qualName", qualName);
        return this;
    }

    public String getQualName() {
        this.nodifyGetFiled("qualName");
        return qualName;
    }

    public CrmCustomerQualEntity setStartDate(Date startDate) {
        this.startDate = startDate;
        this.nodifySetFiled("startDate", startDate);
        return this;
    }

    public Date getStartDate() {
        this.nodifyGetFiled("startDate");
        return startDate;
    }

    public CrmCustomerQualEntity setStartDateStart(Date startDateStart) {
        this.startDateStart = startDateStart;
        this.nodifySetFiled("startDateStart", startDateStart);
        return this;
    }

    public Date getStartDateStart() {
        this.nodifyGetFiled("startDateStart");
        return startDateStart;
    }

    public CrmCustomerQualEntity setStartDateEnd(Date startDateEnd) {
        this.startDateEnd = startDateEnd;
        this.nodifySetFiled("startDateEnd", startDateEnd);
        return this;
    }

    public Date getStartDateEnd() {
        this.nodifyGetFiled("startDateEnd");
        return startDateEnd;
    }
    public CrmCustomerQualEntity setEndDate(Date endDate) {
        this.endDate = endDate;
        this.nodifySetFiled("endDate", endDate);
        return this;
    }

    public Date getEndDate() {
        this.nodifyGetFiled("endDate");
        return endDate;
    }

    public CrmCustomerQualEntity setEndDateStart(Date endDateStart) {
        this.endDateStart = endDateStart;
        this.nodifySetFiled("endDateStart", endDateStart);
        return this;
    }

    public Date getEndDateStart() {
        this.nodifyGetFiled("endDateStart");
        return endDateStart;
    }

    public CrmCustomerQualEntity setEndDateEnd(Date endDateEnd) {
        this.endDateEnd = endDateEnd;
        this.nodifySetFiled("endDateEnd", endDateEnd);
        return this;
    }

    public Date getEndDateEnd() {
        this.nodifyGetFiled("endDateEnd");
        return endDateEnd;
    }
    public CrmCustomerQualEntity setStatus(String status) {
        this.status = status;
        this.nodifySetFiled("status", status);
        return this;
    }

    public String getStatus() {
        this.nodifyGetFiled("status");
        return status;
    }

    public String getStatusName() {
        this.nodifyGetFiled("status");
        if(new Date().after(this.endDate)){
            return "无效";
        }else{
            return "有效";
        }

    }

    public CrmCustomerQualEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public CrmCustomerQualEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public CrmCustomerQualEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public CrmCustomerQualEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public CrmCustomerQualEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public CrmCustomerQualEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public CrmCustomerQualEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public CrmCustomerQualEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public CrmCustomerQualEntity setMemo(String memo) {
        this.memo = memo;
        this.nodifySetFiled("memo", memo);
        return this;
    }

    public String getMemo() {
        this.nodifyGetFiled("memo");
        return memo;
    }

    public CrmCustomerQualEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public CrmCustomerQualEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public CrmCustomerQualEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public CrmCustomerQualEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public CrmCustomerQualEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public CrmCustomerQualEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public CrmCustomerQualEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public CrmCustomerQualEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public CrmCustomerQualEntity setQualLevel(String qualLevel) {
        this.qualLevel = qualLevel;
        this.nodifySetFiled("qualLevel", qualLevel);
        return this;
    }

    public String getQualLevel() {
        this.nodifyGetFiled("qualLevel");
        return qualLevel;
    }

    public CrmCustomerQualEntity setIssuingAuthority(String issuingAuthority) {
        this.issuingAuthority = issuingAuthority;
        this.nodifySetFiled("issuingAuthority", issuingAuthority);
        return this;
    }

    public String getIssuingAuthority() {
        this.nodifyGetFiled("issuingAuthority");
        return issuingAuthority;
    }

}
