package com.eci.project.fzgjCrmContractTypeWarn.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjCrmContractTypeWarn.entity.FzgjCrmContractTypeWarnEntity;

import org.springframework.stereotype.Service;


/**
* 合同种类及预警时效Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-10
*/
@Service
public class FzgjCrmContractTypeWarnVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjCrmContractTypeWarnEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjCrmContractTypeWarnEntity entity, BusinessType businessType) {

    }

}
