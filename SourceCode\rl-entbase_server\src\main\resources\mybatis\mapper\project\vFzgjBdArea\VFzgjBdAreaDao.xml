<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.vFzgjBdArea.dao.VFzgjBdAreaDao">
    <resultMap type="VFzgjBdAreaEntity" id="VFzgjBdAreaResult">
        <result property="countryChName" column="COUNTRY_CH_NAME"/>
        <result property="countryEnName" column="COUNTRY_EN_NAME"/>
        <result property="countryCode" column="COUNTRY_CODE"/>
        <result property="provinceName" column="PROVINCE_NAME"/>
        <result property="provinceCode" column="PROVINCE_CODE"/>
        <result property="cityName" column="CITY_NAME"/>
        <result property="cityCode" column="CITY_CODE"/>
        <result property="districtName" column="DISTRICT_NAME"/>
        <result property="districtCode" column="DISTRICT_CODE"/>
        <result property="areaName" column="AREA_NAME"/>
        <result property="areaCode" column="AREA_CODE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="status" column="STATUS"/>
    </resultMap>

    <sql id="selectVFzgjBdAreaEntityVo">
        select
            COUNTRY_CH_NAME,
            COUNTRY_EN_NAME,
            COUNTRY_CODE,
            PROVINCE_NAME,
            PROVINCE_CODE,
            CITY_NAME,
            CITY_CODE,
            DISTRICT_NAME,
            DISTRICT_CODE,
            AREA_NAME,
            AREA_CODE,
            COMPANY_CODE,
            GROUP_CODE,
            STATUS
        from V_FZGJ_BD_AREA
    </sql>
</mapper>