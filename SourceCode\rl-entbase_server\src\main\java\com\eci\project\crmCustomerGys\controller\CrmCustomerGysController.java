package com.eci.project.crmCustomerGys.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.crmCustomerGys.service.CrmCustomerGysService;
import com.eci.project.crmCustomerGys.entity.CrmCustomerGysEntity;
import com.eci.project.crmCustomerGysType.entity.CrmCustomerGysTypeEntity;
import com.eci.project.crmCustomerGysType.service.CrmCustomerGysTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 业务伙伴-供应商Controller
*
* @<NAME_EMAIL>
* @date 2025-04-29
*/
@Api(tags = "业务伙伴-供应商")
@RestController
@RequestMapping("/crmCustomerGys")
public class CrmCustomerGysController extends EciBaseController {

    @Autowired
    private CrmCustomerGysService crmCustomerGysService;
    @Autowired
    private CrmCustomerGysTypeService crmCustomerGysTypeService;

    @ApiOperation("业务伙伴-供应商:保存")
    @EciLog(title = "业务伙伴-供应商:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody CrmCustomerGysEntity entity){
        CrmCustomerGysEntity crmCustomerGysEntity =crmCustomerGysService.save(entity);
        CrmCustomerGysTypeEntity typeEntity=new CrmCustomerGysTypeEntity();
        typeEntity.setGysType(entity.getGysType());
        typeEntity.setGysTypeName(entity.getGysTypeName());
        typeEntity.setCustomerCode(entity.getCustomerCode());
        typeEntity.setCustomerGuid(entity.getRequestParams().get("CustomerGuid").toString());
        //根据客户GUID删除类型
        crmCustomerGysTypeService.deleteByCustomerGuid(entity.getRequestParams().get("CustomerGuid").toString());
        //保存供应商类型
        crmCustomerGysTypeService.save(typeEntity);
        return ResponseMsgUtil.success(10001,crmCustomerGysEntity);
    }


    @ApiOperation("业务伙伴-供应商:查询列表")
    @EciLog(title = "业务伙伴-供应商:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody CrmCustomerGysEntity entity){
        List<CrmCustomerGysEntity> crmCustomerGysEntities = crmCustomerGysService.selectList(entity);
        return ResponseMsgUtil.success(10001,crmCustomerGysEntities);
    }


    @ApiOperation("业务伙伴-供应商:分页查询列表")
    @EciLog(title = "业务伙伴-供应商:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody CrmCustomerGysEntity entity){
        TgPageInfo tgPageInfo = crmCustomerGysService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("业务伙伴-供应商:根据ID查一条")
    @EciLog(title = "业务伙伴-供应商:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody CrmCustomerGysEntity entity){
        CrmCustomerGysEntity crmCustomerGysEntity = crmCustomerGysService.selectOneByCustomerCode(entity);
        if(crmCustomerGysEntity==null) crmCustomerGysEntity=new CrmCustomerGysEntity();
        return ResponseMsgUtil.success(10001,crmCustomerGysEntity);
    }


    @ApiOperation("业务伙伴-供应商:根据ID删除一条")
    @EciLog(title = "业务伙伴-供应商:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody CrmCustomerGysEntity entity){
        int count = crmCustomerGysService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("业务伙伴-供应商:根据ID字符串删除多条")
    @EciLog(title = "业务伙伴-供应商:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody CrmCustomerGysEntity entity) {
        int count = crmCustomerGysService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}