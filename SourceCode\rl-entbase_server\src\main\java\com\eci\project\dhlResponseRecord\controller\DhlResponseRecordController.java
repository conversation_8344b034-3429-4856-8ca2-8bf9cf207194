package com.eci.project.dhlResponseRecord.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.project.dhlResponseRecord.service.DhlResponseRecordService;
import com.eci.project.dhlResponseRecord.entity.DhlResponseRecordEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.EciLog;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


/**
* 反馈DHL跟踪Controller
*
* @<NAME_EMAIL>
* @date 2025-04-22
*/
@Api(tags = "反馈DHL跟踪")
@RestController
@RequestMapping("/dhlResponseRecord")
public class DhlResponseRecordController extends EciBaseController {

    @Autowired
    private DhlResponseRecordService dhlResponseRecordService;


    @ApiOperation("反馈DHL跟踪:保存")
    @EciLog(title = "反馈DHL跟踪:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    //@EciAction() //框架core,sso,component 需升级到2.2.5
    public ResponseMsg save(@RequestBody DhlResponseRecordEntity entity){
        return ResponseMsgUtil.success(10001,dhlResponseRecordService.save(entity));
    }


    @ApiOperation("反馈DHL跟踪:查询列表")
    @EciLog(title = "反馈DHL跟踪:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    //@EciAction() //框架core,sso,component 需升级到2.2.5
    public ResponseMsg selectList(@RequestBody DhlResponseRecordEntity entity){
        return ResponseMsgUtil.success(10001,dhlResponseRecordService.selectList(entity));
    }


    @ApiOperation("反馈DHL跟踪:分页查询列表")
    @EciLog(title = "反馈DHL跟踪:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    //@EciAction() //框架core,sso,component 需升级到2.2.5
    public ResponseMsg selectPageList(@RequestBody DhlResponseRecordEntity entity){
        return ResponseMsgUtil.success(10001,dhlResponseRecordService.queryPageList(entity));
    }


    @ApiOperation("反馈DHL跟踪:根据ID查一条")
    @EciLog(title = "反馈DHL跟踪:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    //@EciAction() //框架core,sso,component 需升级到2.2.5
    public ResponseMsg selectOneById(@RequestBody DhlResponseRecordEntity entity){
        return ResponseMsgUtil.success(10001,dhlResponseRecordService.selectOneById(entity.getGuid()));
    }


    @ApiOperation("反馈DHL跟踪:根据ID删除一条")
    @EciLog(title = "反馈DHL跟踪:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    //@EciAction() //框架core,sso,component 需升级到2.2.5
    public ResponseMsg deleteById(@RequestBody DhlResponseRecordEntity entity){
        return ResponseMsgUtil.success(10001,dhlResponseRecordService.deleteById(entity.getGuid()));
    }


    @ApiOperation("反馈DHL跟踪:根据ID字符串删除多条")
    @EciLog(title = "反馈DHL跟踪:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    //@EciAction() //框架core,sso,component 需升级到2.2.5
    public ResponseMsg deleteByIds(@RequestBody DhlResponseRecordEntity entity) {
        return ResponseMsgUtil.success(10001, dhlResponseRecordService.deleteByIds(entity.getIds()));
    }


}