package com.eci.project.fzgjBdProvince.service.impl;

import com.eci.common.web.BllContext;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjBdProvince.dao.FzgjBdProvinceDao;
import com.eci.project.fzgjBdProvince.entity.FzgjBdProvinceEntity;
import com.eci.project.fzgjBdProvince.service.IFzgjBdProvinceService;
import com.eci.project.fzgjBdProvince.validate.FzgjBdProvinceVal;

import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 省Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-03-17
*/
@Service
@Slf4j
public class FzgjBdProvinceServiceImpl implements IFzgjBdProvinceService
{
    @Autowired
    private FzgjBdProvinceDao fzgjBdProvinceDao;

    @Autowired
    private FzgjBdProvinceVal fzgjBdProvinceVal;


    @Override
    public TgPageInfo queryPageList(FzgjBdProvinceEntity entity) {
        EciQuery<FzgjBdProvinceEntity> eciQuery = EciQuery.doQuery(entity);
        List<FzgjBdProvinceEntity> entities = fzgjBdProvinceDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjBdProvinceEntity save(FzgjBdProvinceEntity entity) {
        entity.setUpdateDate(new java.util.Date());
        entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
        entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setCreateDate(new java.util.Date());
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
        }

        // 返回实体对象
        FzgjBdProvinceEntity fzgjBdProvinceEntity = null;
        fzgjBdProvinceVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjBdProvinceEntity = fzgjBdProvinceDao.insertOne(entity);

        }else{

            fzgjBdProvinceEntity = fzgjBdProvinceDao.updateByEntityId(entity);

        }
        return fzgjBdProvinceEntity;
    }

    @Override
    public List<FzgjBdProvinceEntity> selectList(FzgjBdProvinceEntity entity) {
        return fzgjBdProvinceDao.selectList(entity);
    }

    @Override
    public FzgjBdProvinceEntity selectOneById(Serializable id) {
        return fzgjBdProvinceDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjBdProvinceEntity> list) {
        fzgjBdProvinceDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjBdProvinceDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjBdProvinceDao.deleteById(id);
    }

}