<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.fzgjBdTruckSpecCom.dao.FzgjBdTruckSpecComDao">
    <resultMap type="FzgjBdTruckSpecComEntity" id="FzgjBdTruckSpecComResult">
        <result property="guid" column="GUID"/>
        <result property="code" column="CODE"/>
        <result property="name" column="NAME"/>
        <result property="truncType" column="TRUNC_TYPE"/>
        <result property="memo" column="MEMO"/>
        <result property="status" column="STATUS"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="xzt" column="XZT"/>
        <result property="bx" column="BX"/>
        <result property="carLong" column="CAR_LONG"/>
        <result property="carLongType" column="CAR_LONG_TYPE"/>
    </resultMap>

    <sql id="selectFzgjBdTruckSpecComEntityVo">
        select
            GUID,
            CODE,
            NAME,
            TRUNC_TYPE,
            MEMO,
            STATUS,
            CREATE_USER_NAME,
            CREATE_USER,
            CREATE_DATE,
            UPDATE_USER_NAME,
            UPDATE_USER,
            UPDATE_DATE,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            XZT,
            BX,
            CAR_LONG,
            CAR_LONG_TYPE
        from FZGJ_BD_TRUCK_SPEC_COM
    </sql>
</mapper>