package com.eci.project.etmsBdDriverQual.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.etmsBdDriverQual.entity.EtmsBdDriverQualSearchEntity;
import com.eci.project.etmsBdDriverQual.service.EtmsBdDriverQualService;
import com.eci.project.etmsBdDriverQual.entity.EtmsBdDriverQualEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 司机从业资格证Controller
*
* @<NAME_EMAIL>
* @date 2025-04-17
*/
@Api(tags = "司机从业资格证")
@RestController
@RequestMapping("/etmsBdDriverQual")
public class EtmsBdDriverQualController extends EciBaseController {

    @Autowired
    private EtmsBdDriverQualService etmsBdDriverQualService;


    @ApiOperation("司机从业资格证:保存")
    @EciLog(title = "司机从业资格证:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody EtmsBdDriverQualEntity entity){
        EtmsBdDriverQualEntity etmsBdDriverQualEntity =etmsBdDriverQualService.save(entity);
        return ResponseMsgUtil.success(10001,etmsBdDriverQualEntity);
    }


    @ApiOperation("司机从业资格证:查询列表")
    @EciLog(title = "司机从业资格证:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody EtmsBdDriverQualEntity entity){
        List<EtmsBdDriverQualEntity> etmsBdDriverQualEntities = etmsBdDriverQualService.selectList(entity);
        return ResponseMsgUtil.success(10001,etmsBdDriverQualEntities);
    }


    @ApiOperation("司机从业资格证:分页查询列表")
    @EciLog(title = "司机从业资格证:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody EtmsBdDriverQualEntity entity){
        TgPageInfo tgPageInfo = etmsBdDriverQualService.queryPageList1(entity);
        return ResponseMsgUtil.successPlus(10001,tgPageInfo);
    }


    @ApiOperation("司机从业资格证:根据ID查一条")
    @EciLog(title = "司机从业资格证:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody EtmsBdDriverQualEntity entity){
        EtmsBdDriverQualEntity  etmsBdDriverQualEntity = etmsBdDriverQualService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.successPlus(10001,etmsBdDriverQualEntity);
    }


    @ApiOperation("司机从业资格证:根据ID删除一条")
    @EciLog(title = "司机从业资格证:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody EtmsBdDriverQualEntity entity){
        int count = etmsBdDriverQualService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("司机从业资格证:根据ID字符串删除多条")
    @EciLog(title = "司机从业资格证:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody EtmsBdDriverQualEntity entity) {
        int count = etmsBdDriverQualService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }

    @ApiOperation("司机资质审核:司机资质管理分页查询列表")
    @EciLog(title = "司机资质审核:司机资质管理分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectQualPageList")
    @EciAction()
    public ResponseMsg selectQualPageList(@RequestBody EtmsBdDriverQualSearchEntity entity){
        TgPageInfo tgPageInfo = etmsBdDriverQualService.queryQualPageList(entity);
        return ResponseMsgUtil.successPlus(10001,tgPageInfo);
    }
}