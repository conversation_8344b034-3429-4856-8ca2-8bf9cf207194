package com.eci.project.omsOrderGoodsCost.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.omsOrderGoodsCost.entity.OmsOrderGoodsCostEntity;

import org.springframework.stereotype.Service;


/**
* 货值币制表Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-06-10
*/
@Service
public class OmsOrderGoodsCostVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(OmsOrderGoodsCostEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(OmsOrderGoodsCostEntity entity, BusinessType businessType) {

    }

}
