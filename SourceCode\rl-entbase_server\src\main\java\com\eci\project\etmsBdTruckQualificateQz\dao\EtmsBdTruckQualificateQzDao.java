package com.eci.project.etmsBdTruckQualificateQz.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.etmsBdTruckQualificateQz.entity.EtmsBdTruckQualificateQzEntity;


/**
* 资质管理Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-04-25
*/
public interface EtmsBdTruckQualificateQzDao extends EciBaseDao<EtmsBdTruckQualificateQzEntity> {

}