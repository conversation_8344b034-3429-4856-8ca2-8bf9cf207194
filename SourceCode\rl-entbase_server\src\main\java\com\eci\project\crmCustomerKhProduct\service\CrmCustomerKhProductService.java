package com.eci.project.crmCustomerKhProduct.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.crmCustomerKhProduct.dao.CrmCustomerKhProductDao;
import com.eci.project.crmCustomerKhProduct.entity.CrmCustomerKhProductEntity;
import com.eci.project.crmCustomerKhProduct.validate.CrmCustomerKhProductVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 客户信息-关联业务产品Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-05-12
*/
@Service
@Slf4j
public class CrmCustomerKhProductService implements EciBaseService<CrmCustomerKhProductEntity> {

    @Autowired
    private CrmCustomerKhProductDao crmCustomerKhProductDao;

    @Autowired
    private CrmCustomerKhProductVal crmCustomerKhProductVal;


    @Override
    public TgPageInfo queryPageList(CrmCustomerKhProductEntity entity) {
        EciQuery<CrmCustomerKhProductEntity> eciQuery = EciQuery.buildQuery(entity);
        eciQuery.select("(SELECT max(X.NAME) FROM FZGJ_BD_PRODUCT X WHERE X.STATUS='Y' " +
                        "AND X.GROUP_CODE=A.group_code " +
                        "AND X.CODE = A.PRODUCT_CODE " +
                        "and x.op_type=A.OP_TYPE) as PRODUCT_CODE_NAME"
                ,"(SELECT C.NAME  FROM FZGJ_BD_OP_TYPE C WHERE C.STATUS='Y' AND C.GROUP_CODE=A.GROUP_CODE AND C.CODE = A.OP_TYPE AND ROWNUM=1) opTypeName"
                ,"A.*");
        List<CrmCustomerKhProductEntity> entities = crmCustomerKhProductDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public CrmCustomerKhProductEntity save(CrmCustomerKhProductEntity entity) {
        // 返回实体对象
        CrmCustomerKhProductEntity crmCustomerKhProductEntity = null;
        crmCustomerKhProductVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            crmCustomerKhProductEntity = crmCustomerKhProductDao.insertOne(entity);

        }else{

            crmCustomerKhProductEntity = crmCustomerKhProductDao.updateByEntityId(entity);

        }
        return crmCustomerKhProductEntity;
    }

    @Override
    public List<CrmCustomerKhProductEntity> selectList(CrmCustomerKhProductEntity entity) {
        return crmCustomerKhProductDao.selectList(entity);
    }

    @Override
    public CrmCustomerKhProductEntity selectOneById(Serializable id) {
        return crmCustomerKhProductDao.selectById(id);
    }


    @Override
    public void insertBatch(List<CrmCustomerKhProductEntity> list) {
        crmCustomerKhProductDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return crmCustomerKhProductDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return crmCustomerKhProductDao.deleteById(id);
    }

}