package com.eci.project.etmsBdTruckSpec.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eci.common.utils.ExportUtils.ExcelProcess;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.etmsBdTruckSpec.dao.EtmsBdTruckSpecDao;
import com.eci.project.etmsBdTruckSpec.entity.EtmsBdTruckSpecEntity;
import com.eci.project.etmsBdTruckSpec.validate.EtmsBdTruckSpecVal;

import com.eci.project.fzgjBdHx.entity.FzgjBdHxEntity;
import com.eci.sso.role.entity.UserContext;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;

import static com.eci.common.util.ResponseMsgUtil.listCodeToName;


/**
* 车辆规则Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-03-21
*/
@Service
@Slf4j
public class EtmsBdTruckSpecService implements EciBaseService<EtmsBdTruckSpecEntity> {

    @Autowired
    private EtmsBdTruckSpecDao etmsBdTruckSpecDao;

    @Autowired
    private EtmsBdTruckSpecVal etmsBdTruckSpecVal;


    @Override
    public TgPageInfo queryPageList(EtmsBdTruckSpecEntity entity) {
        EciQuery<EtmsBdTruckSpecEntity> eciQuery = EciQuery.buildQuery(entity);
        List<EtmsBdTruckSpecEntity> entities = etmsBdTruckSpecDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }
    public void Export(EtmsBdTruckSpecEntity entity){
        EciQuery<EtmsBdTruckSpecEntity> eciQuery = EciQuery.buildQuery(entity);
        etmsBdTruckSpecDao.asyncExportDefaultExcel(()->{
            List<EtmsBdTruckSpecEntity> list=etmsBdTruckSpecDao.selectList(eciQuery);
            List convertList = listCodeToName(list);
            return list;
        }, ExcelProcess.BuildConfig("航线代码",FzgjBdHxEntity.class));
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public EtmsBdTruckSpecEntity save(EtmsBdTruckSpecEntity entity) {
        // 返回实体对象
        EtmsBdTruckSpecEntity etmsBdTruckSpecEntity = null;
        etmsBdTruckSpecVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            if(exist(entity.getCode(),entity.getTruckType()))
                throw new RuntimeException("当前维护的车辆规格+车辆类型已存在当前登陆企业中,请检查!");
            entity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            entity.setCompanyName(UserContext.getUserInfo().getCompanyName());
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
            etmsBdTruckSpecEntity = etmsBdTruckSpecDao.insertOne(entity);

        }else{

            etmsBdTruckSpecEntity = etmsBdTruckSpecDao.updateByEntityId(entity);

        }
        return etmsBdTruckSpecEntity;
    }

    @Override
    public List<EtmsBdTruckSpecEntity> selectList(EtmsBdTruckSpecEntity entity) {
        return etmsBdTruckSpecDao.selectList(entity);
    }

    @Override
    public EtmsBdTruckSpecEntity selectOneById(Serializable id) {
        return etmsBdTruckSpecDao.selectById(id);
    }


    @Override
    public void insertBatch(List<EtmsBdTruckSpecEntity> list) {
        etmsBdTruckSpecDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return etmsBdTruckSpecDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return etmsBdTruckSpecDao.deleteById(id);
    }

    public boolean exist(String code,String truckType){
        QueryWrapper<EtmsBdTruckSpecEntity> wrapper=new QueryWrapper<>();
        wrapper.eq("COMPANY_CODE", UserContext.getUserInfo().getCompanyCode())
                .eq("CODE",code)
                .eq("TRUCK_TYPE",truckType);
        return etmsBdTruckSpecDao.exists(wrapper);
    }
}