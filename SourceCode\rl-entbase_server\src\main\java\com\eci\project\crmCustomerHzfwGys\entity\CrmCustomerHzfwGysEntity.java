package com.eci.project.crmCustomerHzfwGys.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;
import java.util.List;


/**
* 供应商合作服务对象 CRM_CUSTOMER_HZFW_GYS
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-05-13
*/
@ApiModel("供应商合作服务")
@TableName("CRM_CUSTOMER_HZFW_GYS")
@FieldNameConstants
public class CrmCustomerHzfwGysEntity extends EciBaseEntity{
    /**
    * GUID
    */
    @ApiModelProperty("GUID(36)")
    @TableId("GUID")
    private String guid;

    /**
    * 业务伙伴代码
    */
    @ApiModelProperty("业务伙伴代码(36)")
    @TableField("CUSTOMER_CODE")
    private String customerCode;
    @ApiModelProperty("名称(36)")
    @TableField(exist = false)
    private String Name;
    @TableField(exist = false)
    private String itemGuid;
    @ApiModelProperty("业务伙伴代码(36)")
    @TableField(exist=false)
    private boolean Checked=false;

    @TableField(exist=false)
    private List<CrmCustomerHzfwGysEntity> children;
    @TableField(exist=false)
    private String parentId;
    /**
    * 服务类型代码
    */
    @ApiModelProperty("服务类型代码(36)")
    @TableField("FWLX_CODE")
    private String fwlxCode;
    @TableField(exist = false)
    private String fwlxName;
    @TableField(exist = false)
    private String typeGuid;
    @TableField(exist = false)
    private String comTypeGuid;

    /**
    * 服务项目代码
    */
    @ApiModelProperty("服务项目代码(36)")
    @TableField("FWXM_CODE")
    private String fwxmCode;
    @TableField(exist = false)
    private String fwxmName;
    @ApiModelProperty("系统代码(36)")
    @TableField(exist = false)
    private String fwxmsyscode;

    @TableField(exist = false)
    private String beLongGroup;

    /**
    * 反馈作业数据方式
    */
    @ApiModelProperty("反馈作业数据方式(20)")
    @TableField("FKZYSJFS")
    private String fkzysjfs;

    /**
    * 自有系统代码
    */
    @ApiModelProperty("自有系统代码(20)")
    @TableField("SYS_CODE")
    private String sysCode;

    /**
    * 备注
    */
    @ApiModelProperty("备注(500)")
    @TableField("MEMO")
    private String memo;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 最后修改日期
    */
    @ApiModelProperty("最后修改日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("最后修改日期开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("最后修改日期结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(200)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 需应付结算YN
    */
    @ApiModelProperty("需应付结算YN(1)")
    @TableField("IS_AP")
    private String isAp;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public CrmCustomerHzfwGysEntity() {
        this.setSubClazz(CrmCustomerHzfwGysEntity.class);
    }

    public CrmCustomerHzfwGysEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public CrmCustomerHzfwGysEntity setFwxmName(String fwxmName) {
        this.fwxmName = fwxmName;
        this.nodifySetFiled("fwxmName", fwxmName);
        return this;
    }

    public String getFwxmName() {
        this.nodifyGetFiled("fwxmName");
        return fwxmName;
    }

    public CrmCustomerHzfwGysEntity setFwlxName(String fwlxName) {
        this.fwlxName = fwlxName;
        this.nodifySetFiled("fwlxName", fwlxName);
        return this;
    }

    public String getFwlxName() {
        this.nodifyGetFiled("fwlxName");
        return fwlxName;
    }

    public CrmCustomerHzfwGysEntity setTypeGuid(String typeGuid) {
        this.typeGuid = typeGuid;
        this.nodifySetFiled("typeGuid", typeGuid);
        return this;
    }

    public String getTypeGuid() {
        this.nodifyGetFiled("typeGuid");
        return typeGuid;
    }
    public CrmCustomerHzfwGysEntity setComTypeGuid(String comTypeGuid) {
        this.comTypeGuid = comTypeGuid;
        this.nodifySetFiled("comTypeGuid", comTypeGuid);
        return this;
    }

    public String getComTypeGuid() {
        this.nodifyGetFiled("comTypeGuid");
        return comTypeGuid;
    }

    public CrmCustomerHzfwGysEntity setItemGuid(String itemGuid) {
        this.itemGuid = itemGuid;
        this.nodifySetFiled("itemGuid", itemGuid);
        return this;
    }

    public String getItemGuid() {
        this.nodifyGetFiled("itemGuid");
        return itemGuid;
    }

    public CrmCustomerHzfwGysEntity setBeLongGroup(String beLongGroup) {
        this.beLongGroup = beLongGroup;
        this.nodifySetFiled("beLongGroup", beLongGroup);
        return this;
    }

    public String getBeLongGroup() {
        this.nodifyGetFiled("beLongGroup");
        return beLongGroup;
    }

    public CrmCustomerHzfwGysEntity setFwxmsyscode(String fwxmsyscode) {
        this.fwxmsyscode = fwxmsyscode;
        this.nodifySetFiled("fwxmsyscode", fwxmsyscode);
        return this;
    }

    public String getFwxmsyscode() {
        this.nodifyGetFiled("fwxmsyscode");
        return fwxmsyscode;
    }

    public CrmCustomerHzfwGysEntity setParentId(String parentId) {
        this.parentId = parentId;
        this.nodifySetFiled("parentId", parentId);
        return this;
    }

    public String getParentId() {
        this.nodifyGetFiled("parentId");
        return parentId;
    }

    public CrmCustomerHzfwGysEntity setChildren(List<CrmCustomerHzfwGysEntity> children) {
        this.children = children;
        this.nodifySetFiled("children", children);
        return this;
    }

    public List<CrmCustomerHzfwGysEntity> getChildren() {
        this.nodifyGetFiled("children");
        return children;
    }

    public CrmCustomerHzfwGysEntity setName(String Name) {
        this.Name = Name;
        this.nodifySetFiled("Name", Name);
        return this;
    }

    public String getName() {
        this.nodifyGetFiled("Name");
        return Name;
    }

    public CrmCustomerHzfwGysEntity setChecked(boolean Checked) {
        this.Checked = Checked;
        this.nodifySetFiled("Checked", Checked);
        return this;
    }

    public boolean getChecked() {
        this.nodifyGetFiled("Checked");
        return Checked;
    }



    public CrmCustomerHzfwGysEntity setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
        this.nodifySetFiled("customerCode", customerCode);
        return this;
    }

    public String getCustomerCode() {
        this.nodifyGetFiled("customerCode");
        return customerCode;
    }

    public CrmCustomerHzfwGysEntity setFwlxCode(String fwlxCode) {
        this.fwlxCode = fwlxCode;
        this.nodifySetFiled("fwlxCode", fwlxCode);
        return this;
    }

    public String getFwlxCode() {
        this.nodifyGetFiled("fwlxCode");
        return fwlxCode;
    }

    public CrmCustomerHzfwGysEntity setFwxmCode(String fwxmCode) {
        this.fwxmCode = fwxmCode;
        this.nodifySetFiled("fwxmCode", fwxmCode);
        return this;
    }

    public String getFwxmCode() {
        this.nodifyGetFiled("fwxmCode");
        return fwxmCode;
    }

    public CrmCustomerHzfwGysEntity setFkzysjfs(String fkzysjfs) {
        this.fkzysjfs = fkzysjfs;
        this.nodifySetFiled("fkzysjfs", fkzysjfs);
        return this;
    }

    public String getFkzysjfs() {
        this.nodifyGetFiled("fkzysjfs");
        return fkzysjfs;
    }

    public CrmCustomerHzfwGysEntity setSysCode(String sysCode) {
        this.sysCode = sysCode;
        this.nodifySetFiled("sysCode", sysCode);
        return this;
    }

    public String getSysCode() {
        this.nodifyGetFiled("sysCode");
        return sysCode;
    }

    public CrmCustomerHzfwGysEntity setMemo(String memo) {
        this.memo = memo;
        this.nodifySetFiled("memo", memo);
        return this;
    }

    public String getMemo() {
        this.nodifyGetFiled("memo");
        return memo;
    }

    public CrmCustomerHzfwGysEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public CrmCustomerHzfwGysEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public CrmCustomerHzfwGysEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public CrmCustomerHzfwGysEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public CrmCustomerHzfwGysEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public CrmCustomerHzfwGysEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public CrmCustomerHzfwGysEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public CrmCustomerHzfwGysEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public CrmCustomerHzfwGysEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public CrmCustomerHzfwGysEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public CrmCustomerHzfwGysEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public CrmCustomerHzfwGysEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public CrmCustomerHzfwGysEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public CrmCustomerHzfwGysEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public CrmCustomerHzfwGysEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public CrmCustomerHzfwGysEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public CrmCustomerHzfwGysEntity setIsAp(String isAp) {
        this.isAp = isAp;
        this.nodifySetFiled("isAp", isAp);
        return this;
    }

    public String getIsAp() {
        this.nodifyGetFiled("isAp");
        return isAp;
    }

}
