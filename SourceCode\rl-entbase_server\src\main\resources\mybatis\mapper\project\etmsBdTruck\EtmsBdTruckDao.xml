<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.etmsBdTruck.dao.EtmsBdTruckDao">
    <resultMap type="EtmsBdTruckEntity" id="EtmsBdTruckResult">
        <result property="guid" column="GUID"/>
        <result property="truckSpceGuid" column="TRUCK_SPCE_GUID"/>
        <result property="truckNo" column="TRUCK_NO"/>
        <result property="isGk" column="IS_GK"/>
        <result property="partnerGuid" column="PARTNER_GUID"/>
        <result property="gpsMode" column="GPS_MODE"/>
        <result property="gpsNo" column="GPS_NO"/>
        <result property="driverGuid" column="DRIVER_GUID"/>
        <result property="status" column="STATUS"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createCompany" column="CREATE_COMPANY"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="memo" column="MEMO"/>
        <result property="isUser" column="IS_USER"/>
        <result property="llOil" column="LL_OIL"/>
        <result property="licenseDate" column="LICENSE_DATE"/>
        <result property="ratingDate" column="RATING_DATE"/>
        <result property="operationDate" column="OPERATION_DATE"/>
        <result property="carType" column="CAR_TYPE"/>
        <result property="reside" column="RESIDE"/>
        <result property="link" column="LINK"/>
        <result property="tel" column="TEL"/>
        <result property="truckVin" column="TRUCK_VIN"/>
        <result property="tlength" column="TLENGTH"/>
        <result property="tweight" column="TWEIGHT"/>
        <result property="trailerNo" column="TRAILER_NO"/>
        <result property="attributeCode" column="ATTRIBUTE_CODE"/>
        <result property="orgDepId" column="ORG_DEP_ID"/>
        <result property="orgDepCode" column="ORG_DEP_CODE"/>
        <result property="orgDepName" column="ORG_DEP_NAME"/>
        <result property="ssoCompanyGuid" column="SSO_COMPANY_GUID"/>
        <result property="orgCode" column="ORG_CODE"/>
        <result property="orgName" column="ORG_NAME"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="zbzlKg" column="ZBZL_KG"/>
        <result property="cllx" column="CLLX"/>
        <result property="clcc" column="CLCC"/>
        <result property="carColor" column="CAR_COLOR"/>
        <result property="checkStatus" column="CHECK_STATUS"/>
        <result property="checkUser" column="CHECK_USER"/>
        <result property="checkRmk" column="CHECK_RMK"/>
        <result property="truckType" column="TRUCK_TYPE"/>
        <result property="carLong" column="CAR_LONG"/>
        <result property="carLongType" column="CAR_LONG_TYPE"/>
        <result property="isBt" column="IS_BT"/>
    </resultMap>

    <sql id="selectEtmsBdTruckEntityVo">
        select
            GUID,
            TRUCK_SPCE_GUID,
            TRUCK_NO,
            IS_GK,
            PARTNER_GUID,
            GPS_MODE,
            GPS_NO,
            DRIVER_GUID,
            STATUS,
            CREATE_DATE,
            CREATE_USER,
            CREATE_COMPANY,
            UPDATE_DATE,
            UPDATE_USER,
            MEMO,
            IS_USER,
            LL_OIL,
            LICENSE_DATE,
            RATING_DATE,
            OPERATION_DATE,
            CAR_TYPE,
            RESIDE,
            LINK,
            TEL,
            TRUCK_VIN,
            TLENGTH,
            TWEIGHT,
            TRAILER_NO,
            ATTRIBUTE_CODE,
            ORG_DEP_ID,
            ORG_DEP_CODE,
            ORG_DEP_NAME,
            SSO_COMPANY_GUID,
            ORG_CODE,
            ORG_NAME,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            CREATE_USER_NAME,
            UPDATE_USER_NAME,
            ZBZL_KG,
            CLLX,
            CLCC,
            CAR_COLOR,
            CHECK_STATUS,
            CHECK_USER,
            CHECK_RMK,
            TRUCK_TYPE,
            CAR_LONG,
            CAR_LONG_TYPE,
            IS_BT
        from ETMS_BD_TRUCK
    </sql>

    <select id="queryPages" parameterType="com.baomidou.mybatisplus.core.conditions.Wrapper"
            resultType="com.eci.project.etmsBdTruck.entity.EtmsBdTruckDTO">
      SELECT * FROM (
                        SELECT A.GUID,A.DRIVER_GUID,
                               A.ATTRIBUTE_CODE,
                               A.TRUCK_NO AS TRUCK_NO,
                               (SELECT NAME FROM (SELECT NAME,CODE FROM ETMS_BD_TRUCK_TYPE_CUSTOM) WHERE CODE = A.CLLX AND ROWNUM = 1) NEW_CLLX,
                               (SELECT NAME FROM (SELECT NAME,CODE FROM ETMS_BD_TRUCK_SPEC_CUSTOM) WHERE CODE = A.CLCC AND ROWNUM = 1) NEW_CLCC,
                               (CASE A.IS_GK
                                    WHEN 'Y' THEN
                                        '否'
                                    WHEN 'N' THEN
                                        '是'
                                   END) AS IS_GK_NAME,
                               (SELECT X.NAME
                                FROM ETMS_CRM_PARTNER X
                                WHERE X.GUID = A.PARTNER_GUID
                                  AND X.GROUP_CODE = A.GROUP_CODE) PARTNER_GUID,
                               (SELECT X.NAME FROM ETMS_BD_DRIVER X WHERE X.GUID = A.DRIVER_GUID) DRIVER_NAME,
                               A.IS_GK,
                               A.GPS_MODE,
                               A.TRAILER_NO,
                               (select max(Name) from ETMS_BD_GPS_TYPE where code=A.GPS_MODE) GPS_MODE_NAME,

                               A.GPS_NO AS GPS_NO,
                               A.STATUS,
                               (SELECT MAX(T.KEY_NAME)  FROM ECI_DATA_CODE T  WHERE T.GROUP_CODE ='CAR_STATUS' AND T.KEY_VALUE=A.STATUS) STATUS_NAME,

                               A.IS_USER,
                               (SELECT MAX(T.KEY_NAME)  FROM ECI_DATA_CODE T  WHERE T.GROUP_CODE ='00001' AND T.KEY_VALUE=A.IS_USER) IS_USER_NAME,
                               A.MEMO AS MEMO,
                               A.LL_OIL,
                               A.CREATE_DATE,
                               (SELECT MAX(T.Truename) FROM ETMS_SSO_USER T  WHERE T.Username=A.CREATE_USER) CREATE_USER_NAME,
                               NVL((SELECT MAX(T.NAME)  FROM ETMS_BD_XT_ENTERPRISE T WHERE T.CLIENT_COMPANY_CODE = A.CREATE_COMPANY),
                                   (SELECT MAX(NAME)  FROM ETMS_CRM_ENTERPRISE WHERE CODE = A.CREATE_COMPANY))  as CREATE_COMPANY_NAME,
                               UPDATE_DATE,
                               (SELECT MAX(T.Truename) FROM ETMS_SSO_USER T  WHERE T.Username=A.UPDATE_USER) UPDATE_USER_NAME,
                               (SELECT X.CODE || '|' ||
                                       (SELECT MAX(NAME) FROM ETMS_BD_TRUCK_TYPE WHERE CODE=X.TRUCK_TYPE)
                                FROM ETMS_BD_TRUCK_SPEC X
                                WHERE X.GUID = A.TRUCK_SPCE_GUID) TRUCK_SPCE_GUID,
                               A.LICENSE_DATE,
                               A.RATING_DATE,
                               A.OPERATION_DATE,
                               A.ORG_DEP_NAME,
                               A.ORG_DEP_CODE,
                               NVL((SELECT MAX(A.CODE)  FROM ETMS_BD_XT_ENTERPRISE A WHERE A.CLIENT_COMPANY_CODE = A.CREATE_COMPANY),
                                   A.CREATE_COMPANY) CREATE_COMPANY,
                               (CASE
                                    WHEN (SELECT COUNT(*) FROM ETMS_OP_FILE T WHERE T.OP_NO = A.GUID) > 0 THEN
                                        '是'
                                    ELSE
                                        '否'
                                   END) DRIVER_ATT
                        FROM ETMS_BD_TRUCK A
                        WHERE 1 = 1
                    ) A
          ${ew.customSqlSegment}

    </select>
</mapper>