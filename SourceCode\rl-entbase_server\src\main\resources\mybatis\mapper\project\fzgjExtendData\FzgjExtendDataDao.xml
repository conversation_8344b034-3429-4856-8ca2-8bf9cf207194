<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.fzgjExtendData.dao.FzgjExtendDataDao">
    <resultMap type="FzgjExtendDataEntity" id="FzgjExtendDataResult">
        <result property="guid" column="GUID"/>
        <result property="dataType" column="DATA_TYPE"/>
        <result property="code" column="CODE"/>
        <result property="name" column="NAME"/>
        <result property="status" column="STATUS"/>
        <result property="memo" column="MEMO"/>
        <result property="seq" column="SEQ"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="relCountryCode" column="REL_COUNTRY_CODE"/>
        <result property="relCountryName" column="REL_COUNTRY_NAME"/>
    </resultMap>

    <resultMap type="FzgjExtendDataPageEntity" id="FzgjExtendDataPageResult">
        <result property="guid" column="GUID"/>
        <result property="dataType" column="DATA_TYPE"/>
        <result property="code" column="CODE"/>
        <result property="name" column="NAME"/>
        <result property="status" column="STATUS"/>
        <result property="memo" column="MEMO"/>
        <result property="seq" column="SEQ"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="relCountryCode" column="REL_COUNTRY_CODE"/>
        <result property="relCountryName" column="REL_COUNTRY_NAME"/>
        <result property="typeName" column="TYPE_NAME"/>
    </resultMap>

    <sql id="selectFzgjExtendDataEntityVo">
        select
            GUID,
            DATA_TYPE,
            CODE,
            NAME,
            STATUS,
            MEMO,
            SEQ,
            CREATE_DATE,
            CREATE_USER,
            CREATE_USER_NAME,
            UPDATE_DATE,
            UPDATE_USER,
            UPDATE_USER_NAME,
            NODE_CODE,
            NODE_NAME,
            COMPANY_CODE,
            COMPANY_NAME,
            GROUP_CODE,
            GROUP_NAME,
            REL_COUNTRY_CODE,
            REL_COUNTRY_NAME
        from FZGJ_EXTEND_DATA
    </sql>

    <select id="selectListInfo" parameterType="FzgjExtendDataEntity" resultMap="FzgjExtendDataPageResult">
        SELECT
        A.GUID ,
        A.DATA_TYPE ,
        A.CODE ,
        A.NAME ,
        A.STATUS ,
        A.MEMO ,
        A.SEQ ,
        A.CREATE_DATE ,
        A.CREATE_USER ,
        A.CREATE_USER_NAME ,
        A.UPDATE_DATE ,
        A.UPDATE_USER ,
        A.UPDATE_USER_NAME ,
        A.NODE_CODE ,
        A.NODE_NAME ,
        A.COMPANY_CODE ,
        A.COMPANY_NAME ,
        A.GROUP_CODE ,
        A.GROUP_NAME,
        A.REL_COUNTRY_CODE,
        A.REL_COUNTRY_NAME,
        B.TYPE_NAME,
        B.STATUS AS TYPESTATUS,
        B.SYS_JSD
        FROM FZGJ_EXTEND_DATA A LEFT JOIN FZGJ_EXTEND_TYPE B ON A.DATA_TYPE=B.TYPE_CODE
        WHERE B.STATUS = 'Y' AND A.GROUP_CODE = #{groupCode}
        <if test="code != null and code != ''">
            AND A.CODE like '%' || #{code} || '%'
        </if>
        <if test="name != null and name != ''">
            AND A.NAME like '%' || #{name} || '%'
        </if>
        <if test="status != null and status != '' ">
            AND A.STATUS = #{status}
        </if>
        <if test=" dataType != null and dataType != ''">
            AND DATA_TYPE = #{dataType}
        </if>
        <choose>
            <when test="memo == 'SYS'">
                AND B.SYS_JSD='N'
            </when>
            <otherwise>
                AND B.SYS_JSD='Y'
            </otherwise>
        </choose>
    </select>
</mapper>