package com.eci.project.omsOrderFwxmWorkFkHzqd.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.omsOrderFwxmWorkFkHzqd.entity.OmsOrderFwxmWorkFkHzqdEntity;


/**
* 反馈内容-核注清单Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-06-03
*/
public interface OmsOrderFwxmWorkFkHzqdDao extends EciBaseDao<OmsOrderFwxmWorkFkHzqdEntity> {

}