<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.fzgjBdCountry.dao.FzgjBdCountryDao">
    <resultMap type="FzgjBdCountryEntity" id="FzgjBdCountryResult">
        <result property="guid" column="GUID"/>
        <result property="code" column="CODE"/>
        <result property="enName" column="EN_NAME"/>
        <result property="chName" column="CH_NAME"/>
        <result property="memo" column="MEMO"/>
        <result property="status" column="STATUS"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="numCode" column="NUM_CODE"/>
        <result property="threeCode" column="THREE_CODE"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
    </resultMap>

    <sql id="selectFzgjBdCountryEntityVo">
        select
            GUID,
            CODE,
            EN_NAME,
            CH_NAME,
            MEMO,
            STATUS,
            CREATE_USER,
            CREATE_DATE,
            NUM_CODE,
            THREE_CODE,
            UPDATE_DATE,
            UPDATE_USER,
            CREATE_USER_NAME,
            UPDATE_USER_NAME
        from FZGJ_BD_COUNTRY
    </sql>
</mapper>