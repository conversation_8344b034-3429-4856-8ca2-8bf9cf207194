package com.eci.project.fzgjTaskLimitationTime.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjTaskLimitationTime.dao.FzgjTaskLimitationTimeDao;
import com.eci.project.fzgjTaskLimitationTime.entity.FzgjTaskLimitationTimeEntity;
import com.eci.project.fzgjTaskLimitationTime.validate.FzgjTaskLimitationTimeVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 作业环节基准时效Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-05-22
*/
@Service
@Slf4j
public class FzgjTaskLimitationTimeService implements EciBaseService<FzgjTaskLimitationTimeEntity> {

    @Autowired
    private FzgjTaskLimitationTimeDao fzgjTaskLimitationTimeDao;

    @Autowired
    private FzgjTaskLimitationTimeVal fzgjTaskLimitationTimeVal;


    @Override
    public TgPageInfo queryPageList(FzgjTaskLimitationTimeEntity entity) {
        EciQuery<FzgjTaskLimitationTimeEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjTaskLimitationTimeEntity> entities = fzgjTaskLimitationTimeDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjTaskLimitationTimeEntity save(FzgjTaskLimitationTimeEntity entity) {
        // 返回实体对象
        FzgjTaskLimitationTimeEntity fzgjTaskLimitationTimeEntity = null;
        fzgjTaskLimitationTimeVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjTaskLimitationTimeEntity = fzgjTaskLimitationTimeDao.insertOne(entity);

        }else{

            fzgjTaskLimitationTimeEntity = fzgjTaskLimitationTimeDao.updateByEntityId(entity);

        }
        return fzgjTaskLimitationTimeEntity;
    }

    @Override
    public List<FzgjTaskLimitationTimeEntity> selectList(FzgjTaskLimitationTimeEntity entity) {
        return fzgjTaskLimitationTimeDao.selectList(entity);
    }

    @Override
    public FzgjTaskLimitationTimeEntity selectOneById(Serializable id) {
        return fzgjTaskLimitationTimeDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjTaskLimitationTimeEntity> list) {
        fzgjTaskLimitationTimeDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjTaskLimitationTimeDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjTaskLimitationTimeDao.deleteById(id);
    }

}