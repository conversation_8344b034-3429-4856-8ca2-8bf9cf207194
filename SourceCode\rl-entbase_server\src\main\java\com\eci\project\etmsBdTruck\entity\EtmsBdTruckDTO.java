package com.eci.project.etmsBdTruck.entity;

import lombok.Data;

/**
 * <Description>TODO <br>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/15$
 */
@Data
public class EtmsBdTruckDTO {
    public String guid;                    // 主键
    public String driverGuid;              // 司机GUID
    public String attributeCode;           // 属性代码
    public String truckNo;                 // 车牌号
    public String newCllx;                 // 车辆类型名称
    public String newClcc;                 // 车辆尺寸名称
    public String isGkName;                // 是否挂靠名称
    public String partnerGuid;             // 合作伙伴名称
    public String driverName;              // 司机姓名
    public String isGk;                    // 是否挂靠
    public String gpsMode;                 // GPS模式
    public String trailerNo;               // 挂车号
    public String gpsModeName;             // GPS模式名称
    public String gpsNo;                   // GPS编号
    public String status;                  // 状态
    public String statusName;              // 状态名称
    public String isUser;                  // 是否使用
    public String isUserName;              // 是否使用名称
    public String memo;                    // 备注
    public String llOil;                   // 油耗
    public String createDate;              // 创建日期
    public String createUserName;          // 创建人姓名
    public String createCompanyName;       // 创建公司名称
    public String updateDate;              // 更新日期
    public String updateUserName;          // 更新人姓名
    public String truckSpceGuid;           // 车辆规格GUID
    public String licenseDate;             // 许可证日期
    public String ratingDate;              // 评级日期
    public String operationDate;           // 运营日期
    public String orgDepName;              // 组织部门名称
    public String orgDepCode;              // 组织部门代码
    public String createCompany;           // 创建公司
    public String driverAtt;
}
