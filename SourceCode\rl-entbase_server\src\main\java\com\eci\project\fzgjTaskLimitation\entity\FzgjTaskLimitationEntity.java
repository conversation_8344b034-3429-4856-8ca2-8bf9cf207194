package com.eci.project.fzgjTaskLimitation.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;


/**
* 作业环节及标准时效对象 FZGJ_TASK_LIMITATION
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-03-28
*/
@ApiModel("作业环节及标准时效")
@TableName("FZGJ_TASK_LIMITATION")
public class FzgjTaskLimitationEntity extends FzgjTaskLimitationBaseEntity{

}
