package com.eci.project.crmCustomerBank.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.crmCustomerBank.service.CrmCustomerBankService;
import com.eci.project.crmCustomerBank.entity.CrmCustomerBankEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 供应商开户行信息Controller
*
* @<NAME_EMAIL>
* @date 2025-05-13
*/
@Api(tags = "供应商开户行信息")
@RestController
@RequestMapping("/crmCustomerBank")
public class CrmCustomerBankController extends EciBaseController {

    @Autowired
    private CrmCustomerBankService crmCustomerBankService;


    @ApiOperation("供应商开户行信息:保存")
    @EciLog(title = "供应商开户行信息:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody CrmCustomerBankEntity entity){
        CrmCustomerBankEntity crmCustomerBankEntity =crmCustomerBankService.save(entity);
        return ResponseMsgUtil.success(10001,crmCustomerBankEntity);
    }


    @ApiOperation("供应商开户行信息:查询列表")
    @EciLog(title = "供应商开户行信息:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody CrmCustomerBankEntity entity){
        List<CrmCustomerBankEntity> crmCustomerBankEntities = crmCustomerBankService.selectList(entity);
        return ResponseMsgUtil.success(10001,crmCustomerBankEntities);
    }


    @ApiOperation("供应商开户行信息:分页查询列表")
    @EciLog(title = "供应商开户行信息:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody CrmCustomerBankEntity entity){
        TgPageInfo tgPageInfo = crmCustomerBankService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("供应商开户行信息:根据ID查一条")
    @EciLog(title = "供应商开户行信息:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody CrmCustomerBankEntity entity){
        CrmCustomerBankEntity  crmCustomerBankEntity = crmCustomerBankService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,crmCustomerBankEntity);
    }


    @ApiOperation("供应商开户行信息:根据ID删除一条")
    @EciLog(title = "供应商开户行信息:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody CrmCustomerBankEntity entity){
        int count = crmCustomerBankService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("供应商开户行信息:根据ID字符串删除多条")
    @EciLog(title = "供应商开户行信息:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody CrmCustomerBankEntity entity) {
        int count = crmCustomerBankService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}