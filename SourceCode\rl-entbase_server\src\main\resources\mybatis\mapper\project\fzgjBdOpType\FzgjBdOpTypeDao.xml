<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.fzgjBdOpType.dao.FzgjBdOpTypeDao">
    <resultMap type="FzgjBdOpTypeEntity" id="FzgjBdOpTypeResult">
        <result property="guid" column="GUID"/>
        <result property="code" column="CODE"/>
        <result property="name" column="NAME"/>
        <result property="memo" column="MEMO"/>
        <result property="status" column="STATUS"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="seq" column="SEQ"/>
        <result property="parentid" column="PARENTID"/>
        <result property="apGk" column="AP_GK"/>
        <result property="arGk" column="AR_GK"/>
        <result property="classGuid" column="CLASS_GUID"/>
        <result property="classCode" column="CLASS_CODE"/>
        <result property="sysCode" column="SYS_CODE"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="enName" column="EN_NAME"/>
        <result property="trnDate" column="TRN_DATE"/>
        <result property="shareConfig" column="SHARE_CONFIG"/>
        <result property="externalName" column="EXTERNAL_NAME"/>
    </resultMap>

    <sql id="selectFzgjBdOpTypeEntityVo">
        select
            GUID,
            CODE,
            NAME,
            MEMO,
            STATUS,
            CREATE_USER,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_DATE,
            SEQ,
            PARENTID,
            AP_GK,
            AR_GK,
            CLASS_GUID,
            CLASS_CODE,
            SYS_CODE,
            CREATE_USER_NAME,
            UPDATE_USER_NAME,
            GROUP_CODE,
            GROUP_NAME,
            EN_NAME,
            TRN_DATE,
            SHARE_CONFIG,
            EXTERNAL_NAME
        from FZGJ_BD_OP_TYPE
    </sql>
</mapper>