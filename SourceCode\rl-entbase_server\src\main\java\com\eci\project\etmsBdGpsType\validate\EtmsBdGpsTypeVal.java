package com.eci.project.etmsBdGpsType.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.etmsBdGpsType.entity.EtmsBdGpsTypeEntity;

import com.eci.sso.role.entity.UserContext;
import org.springframework.stereotype.Service;

import java.util.Date;


/**
* 定位方式Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-03-21
*/
@Service
public class EtmsBdGpsTypeVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(EtmsBdGpsTypeEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(EtmsBdGpsTypeEntity entity, BusinessType businessType) {
        entity.setUpdateUser(UserContext.getUserInfo().getUserId());
        entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        entity.setUpdateDate(new Date());
        if(businessType==BusinessType.INSERT){
            entity.setCreateUser(UserContext.getUserInfo().getUserId());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setCreateDate(new Date());
        }
    }

}
