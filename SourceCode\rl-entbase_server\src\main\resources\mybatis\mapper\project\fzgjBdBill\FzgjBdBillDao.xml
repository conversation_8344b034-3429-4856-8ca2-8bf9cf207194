<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.fzgjBdBill.dao.FzgjBdBillDao">
    <resultMap type="FzgjBdBillEntity" id="FzgjBdBillResult">
        <result property="guid" column="GUID"/>
        <result property="code" column="CODE"/>
        <result property="name" column="NAME"/>
        <result property="preFix" column="PRE_FIX"/>
        <result property="status" column="STATUS"/>
        <result property="seq" column="SEQ"/>
        <result property="memo" column="MEMO"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="parentid" column="PARENTID"/>
        <result property="classGuid" column="CLASS_GUID"/>
        <result property="classCode" column="CLASS_CODE"/>
        <result property="isJd" column="IS_JD"/>
        <result property="isCz" column="IS_CZ"/>
        <result property="pageView" column="PAGE_VIEW"/>
        <result property="enName" column="EN_NAME"/>
    </resultMap>

    <sql id="selectFzgjBdBillEntityVo">
        select
            GUID,
            CODE,
            NAME,
            PRE_FIX,
            STATUS,
            SEQ,
            MEMO,
            CREATE_DATE,
            CREATE_USER,
            UPDATE_DATE,
            UPDATE_USER,
            CREATE_USER_NAME,
            UPDATE_USER_NAME,
            PARENTID,
            CLASS_GUID,
            CLASS_CODE,
            IS_JD,
            IS_CZ,
            PAGE_VIEW,
            EN_NAME
        from FZGJ_BD_BILL
    </sql>
</mapper>