package com.eci.project.omsOrderGoodsPack.controller;

import com.eci.common.util.ResponseMsg;
import com.eci.common.util.ResponseMsgUtil;
import com.eci.crud.controller.EciBaseController;
import com.eci.log.annotation.EciAction;
import com.eci.log.annotation.EciLog;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.omsOrderGoodsPack.entity.OmsOrderGoodsPackEntity;
import com.eci.project.omsOrderGoodsPack.entity.ResOmsOrderGoodsPackEntity;
import com.eci.project.omsOrderGoodsPack.service.OmsOrderGoodsPackService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 货物包装表Controller
 *
 * @<NAME_EMAIL>
 * @date 2025-06-10
 */
@Api(tags = "货物包装表")
@RestController
@RequestMapping("/omsOrderGoodsPack")
public class OmsOrderGoodsPackController extends EciBaseController {

    @Autowired
    private OmsOrderGoodsPackService omsOrderGoodsPackService;


    @ApiOperation("货物包装表:保存")
    @EciLog(title = "货物包装表:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody OmsOrderGoodsPackEntity entity) {
        OmsOrderGoodsPackEntity omsOrderGoodsPackEntity = omsOrderGoodsPackService.save(entity);
        return ResponseMsgUtil.success(10001, omsOrderGoodsPackEntity);
    }


    @ApiOperation("货物包装表:查询列表")
    @EciLog(title = "货物包装表:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody OmsOrderGoodsPackEntity entity) {
        List<OmsOrderGoodsPackEntity> omsOrderGoodsPackEntities = omsOrderGoodsPackService.selectList(entity);
        return ResponseMsgUtil.success(10001, omsOrderGoodsPackEntities);
    }


    @ApiOperation("货物包装表:分页查询列表")
    @EciLog(title = "货物包装表:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody OmsOrderGoodsPackEntity entity) {
        TgPageInfo tgPageInfo = omsOrderGoodsPackService.queryPageList(entity);
        return ResponseMsgUtil.success(10001, tgPageInfo);
    }


    @ApiOperation("货物包装表:根据ID查一条")
    @EciLog(title = "货物包装表:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody OmsOrderGoodsPackEntity entity) {
        OmsOrderGoodsPackEntity omsOrderGoodsPackEntity = omsOrderGoodsPackService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001, omsOrderGoodsPackEntity);
    }


    @ApiOperation("货物包装表:根据ID删除一条")
    @EciLog(title = "货物包装表:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody OmsOrderGoodsPackEntity entity) {
        int count = omsOrderGoodsPackService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001, count);
    }


    @ApiOperation("货物包装表:根据ID字符串删除多条")
    @EciLog(title = "货物包装表:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody OmsOrderGoodsPackEntity entity) {
        int count = omsOrderGoodsPackService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001, count);
    }


    @ApiOperation("货物包装表:加载货物明细")
    @EciLog(title = "货物包装表:加载货物明细", businessType = BusinessType.SELECT)
    @PostMapping("/loadOrderGoodsPack")
    @EciAction()
    public ResponseMsg loadOrderGoodsPack(@RequestBody OmsOrderGoodsPackEntity entity) {
        List<ResOmsOrderGoodsPackEntity> omsOrderGoodsPackEntities = omsOrderGoodsPackService.loadOrderGoodsPack(entity);
        return ResponseMsgUtil.success(10001, omsOrderGoodsPackEntities);
    }


}