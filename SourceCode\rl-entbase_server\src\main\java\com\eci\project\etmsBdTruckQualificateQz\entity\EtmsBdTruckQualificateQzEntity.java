package com.eci.project.etmsBdTruckQualificateQz.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 资质管理对象 ETMS_BD_TRUCK_QUALIFICATE_QZ
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-04-25
*/
@ApiModel("资质管理")
@TableName("ETMS_BD_TRUCK_QUALIFICATE_QZ")
@FieldNameConstants
public class EtmsBdTruckQualificateQzEntity extends EciBaseEntity{
    /**
    * 主键
    */
    @ApiModelProperty("主键(50)")
    @TableField("GUID")
    private String guid;

    @ApiModelProperty("(50)")
    @TableField("TRUCK_GUID")
    private String truckGuid;

    /**
    * 资质类型
    */
    @ApiModelProperty("资质类型(100)")
    @TableField("QUALIFICATE_TYPE")
    private String qualificateType;

    /**
    * 资质名称
    */
    @ApiModelProperty("资质名称(200)")
    @TableField("QUALIFICATE_NAME")
    private String qualificateName;

    /**
    * 资质编号
    */
    @ApiModelProperty("资质编号(50)")
    @TableField("QUALIFICATE_NO")
    private String qualificateNo;

    /**
    * 资质状态（0：无效，1：有效）
    */
    @ApiModelProperty("资质状态（0：无效，1：有效）(22)")
    @TableField("QUALIFICATE_STATUS")
    private Integer qualificateStatus;

    /**
    * 发证日期
    */
    @ApiModelProperty("发证日期(7)")
    @TableField("ISSUE_DATE")
    private Date issueDate;

    @ApiModelProperty("发证日期开始")
    @TableField(exist=false)
    private Date issueDateStart;

    @ApiModelProperty("发证日期结束")
    @TableField(exist=false)
    private Date issueDateEnd;

    /**
    * 有效期
    */
    @ApiModelProperty("有效期(7)")
    @TableField("VALIDITY_DATE")
    private Date validityDate;

    @ApiModelProperty("有效期开始")
    @TableField(exist=false)
    private Date validityDateStart;

    @ApiModelProperty("有效期结束")
    @TableField(exist=false)
    private Date validityDateEnd;

    /**
    * 备注
    */
    @ApiModelProperty("备注(500)")
    @TableField("REMARK")
    private String remark;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    @ApiModelProperty("最后修改日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("最后修改日期")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("最后修改日期")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 修改标志（0：不变，1：修改，2：删除，3：新增）
    */
    @ApiModelProperty("修改标志（0：不变，1：修改，2：删除，3：新增）(50)")
    @TableField("MOD_MARK")
    private String modMark;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public EtmsBdTruckQualificateQzEntity() {
        this.setSubClazz(EtmsBdTruckQualificateQzEntity.class);
    }

    public EtmsBdTruckQualificateQzEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public EtmsBdTruckQualificateQzEntity setTruckGuid(String truckGuid) {
        this.truckGuid = truckGuid;
        this.nodifySetFiled("truckGuid", truckGuid);
        return this;
    }

    public String getTruckGuid() {
        this.nodifyGetFiled("truckGuid");
        return truckGuid;
    }

    public EtmsBdTruckQualificateQzEntity setQualificateType(String qualificateType) {
        this.qualificateType = qualificateType;
        this.nodifySetFiled("qualificateType", qualificateType);
        return this;
    }

    public String getQualificateType() {
        this.nodifyGetFiled("qualificateType");
        return qualificateType;
    }

    public EtmsBdTruckQualificateQzEntity setQualificateName(String qualificateName) {
        this.qualificateName = qualificateName;
        this.nodifySetFiled("qualificateName", qualificateName);
        return this;
    }

    public String getQualificateName() {
        this.nodifyGetFiled("qualificateName");
        return qualificateName;
    }

    public EtmsBdTruckQualificateQzEntity setQualificateNo(String qualificateNo) {
        this.qualificateNo = qualificateNo;
        this.nodifySetFiled("qualificateNo", qualificateNo);
        return this;
    }

    public String getQualificateNo() {
        this.nodifyGetFiled("qualificateNo");
        return qualificateNo;
    }

    public EtmsBdTruckQualificateQzEntity setQualificateStatus(Integer qualificateStatus) {
        this.qualificateStatus = qualificateStatus;
        this.nodifySetFiled("qualificateStatus", qualificateStatus);
        return this;
    }

    public Integer getQualificateStatus() {
        this.nodifyGetFiled("qualificateStatus");
        return qualificateStatus;
    }

    public EtmsBdTruckQualificateQzEntity setIssueDate(Date issueDate) {
        this.issueDate = issueDate;
        this.nodifySetFiled("issueDate", issueDate);
        return this;
    }

    public Date getIssueDate() {
        this.nodifyGetFiled("issueDate");
        return issueDate;
    }

    public EtmsBdTruckQualificateQzEntity setIssueDateStart(Date issueDateStart) {
        this.issueDateStart = issueDateStart;
        this.nodifySetFiled("issueDateStart", issueDateStart);
        return this;
    }

    public Date getIssueDateStart() {
        this.nodifyGetFiled("issueDateStart");
        return issueDateStart;
    }

    public EtmsBdTruckQualificateQzEntity setIssueDateEnd(Date issueDateEnd) {
        this.issueDateEnd = issueDateEnd;
        this.nodifySetFiled("issueDateEnd", issueDateEnd);
        return this;
    }

    public Date getIssueDateEnd() {
        this.nodifyGetFiled("issueDateEnd");
        return issueDateEnd;
    }
    public EtmsBdTruckQualificateQzEntity setValidityDate(Date validityDate) {
        this.validityDate = validityDate;
        this.nodifySetFiled("validityDate", validityDate);
        return this;
    }

    public Date getValidityDate() {
        this.nodifyGetFiled("validityDate");
        return validityDate;
    }

    public EtmsBdTruckQualificateQzEntity setValidityDateStart(Date validityDateStart) {
        this.validityDateStart = validityDateStart;
        this.nodifySetFiled("validityDateStart", validityDateStart);
        return this;
    }

    public Date getValidityDateStart() {
        this.nodifyGetFiled("validityDateStart");
        return validityDateStart;
    }

    public EtmsBdTruckQualificateQzEntity setValidityDateEnd(Date validityDateEnd) {
        this.validityDateEnd = validityDateEnd;
        this.nodifySetFiled("validityDateEnd", validityDateEnd);
        return this;
    }

    public Date getValidityDateEnd() {
        this.nodifyGetFiled("validityDateEnd");
        return validityDateEnd;
    }
    public EtmsBdTruckQualificateQzEntity setRemark(String remark) {
        this.remark = remark;
        this.nodifySetFiled("remark", remark);
        return this;
    }

    public String getRemark() {
        this.nodifyGetFiled("remark");
        return remark;
    }

    public EtmsBdTruckQualificateQzEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public EtmsBdTruckQualificateQzEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public EtmsBdTruckQualificateQzEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public EtmsBdTruckQualificateQzEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public EtmsBdTruckQualificateQzEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public EtmsBdTruckQualificateQzEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public EtmsBdTruckQualificateQzEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public EtmsBdTruckQualificateQzEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public EtmsBdTruckQualificateQzEntity setModMark(String modMark) {
        this.modMark = modMark;
        this.nodifySetFiled("modMark", modMark);
        return this;
    }

    public String getModMark() {
        this.nodifyGetFiled("modMark");
        return modMark;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public EtmsBdTruckQualificateQzEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDateStart() {
        return createDateStart;
    }

    public EtmsBdTruckQualificateQzEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateEnd() {
        return createDateEnd;
    }

    public EtmsBdTruckQualificateQzEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public EtmsBdTruckQualificateQzEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDateStart() {
        return updateDateStart;
    }

    public EtmsBdTruckQualificateQzEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateEnd() {
        return updateDateEnd;
    }

    public EtmsBdTruckQualificateQzEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }
}
