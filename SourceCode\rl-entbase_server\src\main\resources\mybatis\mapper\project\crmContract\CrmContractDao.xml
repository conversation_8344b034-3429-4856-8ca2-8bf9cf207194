<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.crmContract.dao.CrmContractDao">
    <resultMap type="CrmContractEntity" id="CrmContractResult">
        <result property="guid" column="GUID"/>
        <result property="customerCode" column="CUSTOMER_CODE"/>
        <result property="customerName" column="CUSTOMER_NAME"/>
        <result property="contractNum" column="CONTRACT_NUM"/>
        <result property="contractType" column="CONTRACT_TYPE"/>
        <result property="signDateFirst" column="SIGN_DATE_FIRST"/>
        <result property="effectiveDate" column="EFFECTIVE_DATE"/>
        <result property="expirationDate" column="EXPIRATION_DATE"/>
        <result property="terminationDate" column="TERMINATION_DATE"/>
        <result property="renewalDate" column="RENEWAL_DATE"/>
        <result property="renewalExpirationDate" column="RENEWAL_EXPIRATION_DATE"/>
        <result property="latestExpirationDate" column="LATEST_EXPIRATION_DATE"/>
        <result property="status" column="STATUS"/>
        <result property="memo" column="MEMO"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="customerShortName" column="CUSTOMER_SHORT_NAME"/>
        <result property="customerGuid" column="CUSTOMER_GUID"/>
        <result property="contractName" column="CONTRACT_NAME"/>
        <result property="htzt" column="HTZT"/>
        <result property="skDateline" column="SK_DATELINE"/>
        <result property="serviceAsk" column="SERVICE_ASK"/>
        <result property="carefulItem" column="CAREFUL_ITEM"/>
        <result property="isDeposit" column="IS_DEPOSIT"/>
        <result property="amountYj" column="AMOUNT_YJ"/>
        <result property="isTh" column="IS_TH"/>
        <result property="memoTh" column="MEMO_TH"/>
        <result property="oaTkdNo" column="OA_TKD_NO"/>
        <result property="taxStatus" column="TAX_STATUS"/>
        <result property="invoiceType" column="INVOICE_TYPE"/>
        <result property="taxRate" column="TAX_RATE"/>
    </resultMap>

    <sql id="selectCrmContractEntityVo">
        select
            GUID,
            CUSTOMER_CODE,
            CUSTOMER_NAME,
            CONTRACT_NUM,
            CONTRACT_TYPE,
            SIGN_DATE_FIRST,
            EFFECTIVE_DATE,
            EXPIRATION_DATE,
            TERMINATION_DATE,
            RENEWAL_DATE,
            RENEWAL_EXPIRATION_DATE,
            LATEST_EXPIRATION_DATE,
            STATUS,
            MEMO,
            CREATE_USER,
            CREATE_USER_NAME,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_USER_NAME,
            UPDATE_DATE,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            CUSTOMER_SHORT_NAME,
            CUSTOMER_GUID,
            CONTRACT_NAME,
            HTZT,
            SK_DATELINE,
            SERVICE_ASK,
            CAREFUL_ITEM,
            IS_DEPOSIT,
            AMOUNT_YJ,
            IS_TH,
            MEMO_TH,
            OA_TKD_NO,
            TAX_STATUS,
            INVOICE_TYPE,
            TAX_RATE
        from CRM_CONTRACT
    </sql>
</mapper>