package com.eci.project.fzgjBaseDataDetail.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjBaseDataDetail.entity.FzgjBaseDataDetailEntity;

import org.springframework.stereotype.Service;


/**
* 扩展基础资料明细Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-03-18
*/
@Service
public class FzgjBaseDataDetailVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjBaseDataDetailEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjBaseDataDetailEntity entity, BusinessType businessType) {

    }

}
