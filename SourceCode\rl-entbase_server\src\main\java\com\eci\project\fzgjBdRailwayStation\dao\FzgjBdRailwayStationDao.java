package com.eci.project.fzgjBdRailwayStation.dao;


import com.eci.crud.dao.EciBaseDao;
import com.eci.project.fzgjBdRailwayStation.entity.FzgjBdRailwayStationEntity;
import com.eci.project.fzgjBdRailwayStation.entity.FzgjBdRailwayStationPageEntity;

import java.util.List;


/**
 * 铁路站点Dao层
 * 接口层, 直接查询数据库使用
 *
 * @<NAME_EMAIL>
 * @date 2025-03-25
 */
public interface FzgjBdRailwayStationDao extends EciBaseDao<FzgjBdRailwayStationEntity> {

    /**
     * 分页列表查询
     */
    List<FzgjBdRailwayStationPageEntity> selectStationPageList(FzgjBdRailwayStationEntity entity);


    /**
     * 详情加载
     ***/
    FzgjBdRailwayStationPageEntity selectOneByID(String guid);
}