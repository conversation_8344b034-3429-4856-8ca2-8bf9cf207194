<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.crmCustomerBank.dao.CrmCustomerBankDao">
    <resultMap type="CrmCustomerBankEntity" id="CrmCustomerBankResult">
        <result property="guid" column="GUID"/>
        <result property="customerCode" column="CUSTOMER_CODE"/>
        <result property="account" column="ACCOUNT"/>
        <result property="bank" column="BANK"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="bankMemo" column="BANK_MEMO"/>
        <result property="taxNo" column="TAX_NO"/>
        <result property="isDefault" column="IS_DEFAULT"/>
    </resultMap>

    <sql id="selectCrmCustomerBankEntityVo">
        select
            GUID,
            CUSTOMER_CODE,
            ACCOUNT,
            BANK,
            CREATE_USER,
            CREATE_USER_NAME,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_USER_NAME,
            UPDATE_DATE,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            BANK_MEMO,
            TAX_NO,
            IS_DEFAULT
        from CRM_CUSTOMER_BANK
    </sql>
</mapper>