package com.eci.project.fzgjBdArea.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.project.fzgjBdArea.service.IFzgjBdAreaService;
import com.eci.project.fzgjBdArea.entity.FzgjBdAreaEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.EciLog;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


/**
* 公路乡镇地区Controller
*
* @<NAME_EMAIL>
* @date 2025-03-26
*/
@Api(tags = "公路乡镇地区")
@RestController
@RequestMapping("/fzgjBdArea")
public class FzgjBdAreaController extends EciBaseController {

    @Autowired
    private IFzgjBdAreaService fzgjBdAreaService;

    /**
    * 根据实体插入数据库
    * @param entity
    * @return
    */
    @ApiOperation("公路乡镇地区:保存")
    @EciLog(title = "公路乡镇地区:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public ResponseMsg save(@RequestBody FzgjBdAreaEntity entity){
        return ResponseMsgUtil.success(10001,fzgjBdAreaService.saveBase(entity));
    }

    /**
    * 根据条件查询一个集合
    * @param entity
    * @return
    */
    @ApiOperation("公路乡镇地区:查询列表")
    @EciLog(title = "公路乡镇地区:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    public ResponseMsg selectList(@RequestBody FzgjBdAreaEntity entity){
        return ResponseMsgUtil.successPlus(10001,fzgjBdAreaService.selectList(entity));
    }

    /**
    * 分页查询
    * @param entity
    * @return
    */
    @ApiOperation("公路乡镇地区:分页查询列表")
    @EciLog(title = "公路乡镇地区:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    public ResponseMsg selectPageList(@RequestBody FzgjBdAreaEntity entity){
        return ResponseMsgUtil.successPlus(10001,fzgjBdAreaService.queryPageList(entity));
    }

    /**
    * 根据ID查一条记录
    * @param entity
    * @return
    */
    @ApiOperation("公路乡镇地区:根据ID查一条")
    @EciLog(title = "公路乡镇地区:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    public ResponseMsg selectOneById(@RequestBody FzgjBdAreaEntity entity){
        return ResponseMsgUtil.successPlus(10001,fzgjBdAreaService.selectOneById(entity.getGuid()));
    }


    /**
    * 根据ID删除
    * @param entity
    * @return
    */
    @ApiOperation("公路乡镇地区:根据ID删除一条")
    @EciLog(title = "公路乡镇地区:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    public ResponseMsg deleteById(@RequestBody FzgjBdAreaEntity entity){
        return ResponseMsgUtil.success(10001,fzgjBdAreaService.deleteById(entity.getGuid()));
    }

    /**
    * 根据IDS删除
    *
    * @param entity
    * @return
    */
    @ApiOperation("公路乡镇地区:根据ID字符串删除多条")
    @EciLog(title = "公路乡镇地区:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    public ResponseMsg deleteByIds(@RequestBody FzgjBdAreaEntity entity) {
        return ResponseMsgUtil.success(10001, fzgjBdAreaService.deleteByIds(entity.getIds()));
    }


}