package com.eci.project.fzgjBdArea.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;

import java.util.Date;


/**
* 公路乡镇地区对象 FZGJ_BD_AREA
*
* @<NAME_EMAIL>
* @date 2025-03-26
*/
@FieldNameConstants
@TableName("FZGJ_BD_AREA")
public class FzgjBdAreaBaseEntity extends EciBaseEntity
{
	/**
	* GUID
	*/
	@ApiModelProperty("GUID(50)")
	@TableId("GUID")
	private String guid;

	/**
	* 代码
	*/
	@ApiModelProperty("代码(50)")
	@TableField("CODE")
	private String code;

	/**
	* 名称
	*/
	@ApiModelProperty("名称(50)")
	@TableField("NAME")
	private String name;

	/**
	* 简称
	*/
	@ApiModelProperty("简称(50)")
	@TableField("SHORT_NAME")
	private String shortName;

	/**
	* 地区id
	*/
	@ApiModelProperty("地区id(50)")
	@TableField("DISTRICT_ID")
	private String districtId;

	/**
	* 备注
	*/
	@ApiModelProperty("备注(50)")
	@TableField("MEMO")
	private String memo;

	/**
	* 状态
	*/
	@ApiModelProperty("状态(1)")
	@TableField("STATUS")
	private String status;

	/**
	* CREATE_USER
	*/
	@ApiModelProperty("CREATE_USER(200)")
	@TableField("CREATE_USER_NAME")
	private String createUserName;

	/**
	* CREATE_USER
	*/
	@ApiModelProperty("CREATE_USER(50)")
	@TableField("CREATE_USER")
	private String createUser;

	/**
	* CREATE_DATE
	*/
	@ApiModelProperty("CREATE_DATE(7)")
	@TableField("CREATE_DATE")
	private Date createDate;

	@ApiModelProperty("CREATE_DATE开始")
	@TableField(exist=false)
	private Date createDateStart;

	@ApiModelProperty("CREATE_DATE结束")
	@TableField(exist=false)
	private Date createDateEnd;

	/**
	* UPDATE_USER
	*/
	@ApiModelProperty("UPDATE_USER(200)")
	@TableField("UPDATE_USER_NAME")
	private String updateUserName;

	/**
	* UPDATE_USER
	*/
	@ApiModelProperty("UPDATE_USER(50)")
	@TableField("UPDATE_USER")
	private String updateUser;

	/**
	* UPDATE_DATE
	*/
	@ApiModelProperty("UPDATE_DATE(7)")
	@TableField("UPDATE_DATE")
	private Date updateDate;

	@ApiModelProperty("UPDATE_DATE开始")
	@TableField(exist=false)
	private Date updateDateStart;

	@ApiModelProperty("UPDATE_DATE结束")
	@TableField(exist=false)
	private Date updateDateEnd;

	/**
	* 顺序
	*/
	@ApiModelProperty("顺序(22)")
	@TableField("SEQ")
	private Integer seq;

	/**
	* 组织代码
	*/
	@ApiModelProperty("组织代码(50)")
	@TableField("NODE_CODE")
	private String nodeCode;

	/**
	* 组织名称
	*/
	@ApiModelProperty("组织名称(200)")
	@TableField("NODE_NAME")
	private String nodeName;

	/**
	* 公司代码
	*/
	@ApiModelProperty("公司代码(50)")
	@TableField("COMPANY_CODE")
	private String companyCode;

	/**
	* 公司名称
	*/
	@ApiModelProperty("公司名称(200)")
	@TableField("COMPANY_NAME")
	private String companyName;

	/**
	* 集团代码
	*/
	@ApiModelProperty("集团代码(50)")
	@TableField("GROUP_CODE")
	private String groupCode;

	/**
	* 集团名称
	*/
	@ApiModelProperty("集团名称(200)")
	@TableField("GROUP_NAME")
	private String groupName;

	@JsonIgnore
	@TableField(exist = false)
	private Class<?> clazz = this.getClass();

	public Class<?> getClazz() {
		return clazz;
	}

	public void setClazz(Class<?> clazz) {
		this.clazz = clazz;
	}

	public FzgjBdAreaBaseEntity setGuid(String guid) {
		this.guid = guid;
		return this;
	}

	public String getGuid() {
		return guid;
	}

	public FzgjBdAreaBaseEntity setCode(String code) {
		this.code = code;
		return this;
	}

	public String getCode() {
		return code;
	}

	public FzgjBdAreaBaseEntity setName(String name) {
		this.name = name;
		return this;
	}

	public String getName() {
		return name;
	}

	public FzgjBdAreaBaseEntity setShortName(String shortName) {
		this.shortName = shortName;
		return this;
	}

	public String getShortName() {
		return shortName;
	}

	public FzgjBdAreaBaseEntity setDistrictId(String districtId) {
		this.districtId = districtId;
		return this;
	}

	public String getDistrictId() {
		return districtId;
	}

	public FzgjBdAreaBaseEntity setMemo(String memo) {
		this.memo = memo;
		return this;
	}

	public String getMemo() {
		return memo;
	}

	public FzgjBdAreaBaseEntity setStatus(String status) {
		this.status = status;
		return this;
	}

	public String getStatus() {
		return status;
	}

	public FzgjBdAreaBaseEntity setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
		return this;
	}

	public String getCreateUserName() {
		return createUserName;
	}

	public FzgjBdAreaBaseEntity setCreateUser(String createUser) {
		this.createUser = createUser;
		return this;
	}

	public String getCreateUser() {
		return createUser;
	}

	public FzgjBdAreaBaseEntity setCreateDate(Date createDate) {
		this.createDate = createDate;
		return this;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public FzgjBdAreaBaseEntity setCreateDateStart(Date createDateStart) {
		this.createDateStart = createDateStart;
		return this;
	}

	public Date getCreateDateStart() {
		return createDateStart;
	}

	public FzgjBdAreaBaseEntity setCreateDateEnd(Date createDateEnd) {
		this.createDateEnd = createDateEnd;
		return this;
	}

	public Date getCreateDateEnd() {
		return createDateEnd;
	}
	public FzgjBdAreaBaseEntity setUpdateUserName(String updateUserName) {
		this.updateUserName = updateUserName;
		return this;
	}

	public String getUpdateUserName() {
		return updateUserName;
	}

	public FzgjBdAreaBaseEntity setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
		return this;
	}

	public String getUpdateUser() {
		return updateUser;
	}

	public FzgjBdAreaBaseEntity setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
		return this;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public FzgjBdAreaBaseEntity setUpdateDateStart(Date updateDateStart) {
		this.updateDateStart = updateDateStart;
		return this;
	}

	public Date getUpdateDateStart() {
		return updateDateStart;
	}

	public FzgjBdAreaBaseEntity setUpdateDateEnd(Date updateDateEnd) {
		this.updateDateEnd = updateDateEnd;
		return this;
	}

	public Date getUpdateDateEnd() {
		return updateDateEnd;
	}
	public FzgjBdAreaBaseEntity setSeq(Integer seq) {
		this.seq = seq;
		return this;
	}

	public Integer getSeq() {
		return seq;
	}

	public FzgjBdAreaBaseEntity setNodeCode(String nodeCode) {
		this.nodeCode = nodeCode;
		return this;
	}

	public String getNodeCode() {
		return nodeCode;
	}

	public FzgjBdAreaBaseEntity setNodeName(String nodeName) {
		this.nodeName = nodeName;
		return this;
	}

	public String getNodeName() {
		return nodeName;
	}

	public FzgjBdAreaBaseEntity setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
		return this;
	}

	public String getCompanyCode() {
		return companyCode;
	}

	public FzgjBdAreaBaseEntity setCompanyName(String companyName) {
		this.companyName = companyName;
		return this;
	}

	public String getCompanyName() {
		return companyName;
	}

	public FzgjBdAreaBaseEntity setGroupCode(String groupCode) {
		this.groupCode = groupCode;
		return this;
	}

	public String getGroupCode() {
		return groupCode;
	}

	public FzgjBdAreaBaseEntity setGroupName(String groupName) {
		this.groupName = groupName;
		return this;
	}

	public String getGroupName() {
		return groupName;
	}

}
