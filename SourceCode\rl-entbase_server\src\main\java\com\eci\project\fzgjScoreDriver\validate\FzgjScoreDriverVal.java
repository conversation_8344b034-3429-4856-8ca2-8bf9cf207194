package com.eci.project.fzgjScoreDriver.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjScoreDriver.entity.FzgjScoreDriverEntity;

import org.springframework.stereotype.Service;


/**
* 企业评分Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-06-16
*/
@Service
public class FzgjScoreDriverVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjScoreDriverEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjScoreDriverEntity entity, BusinessType businessType) {

    }

}
