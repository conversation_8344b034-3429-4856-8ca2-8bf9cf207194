package com.eci.project.omsOrderFwxmZhys.validate;

import com.eci.common.util.StringUtils;
import com.eci.exception.BaseException;
import com.eci.log.enums.BusinessType;
import com.eci.project.omsOrderFwxmZhys.entity.OmsOrderFwxmZhysEntity;
import com.eci.project.omsOrderFwxmZhysXl.entity.OmsOrderFwxmZhysXlEntity;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 服务项目-综合运输Val接口
 * 验证接口
 *
 * @<NAME_EMAIL>
 * @date 2025-06-13
 */
@Service
public class OmsOrderFwxmZhysVal {

    /**
     * 删除验证
     *
     * @param entity
     */
    public void deleteValidate(OmsOrderFwxmZhysEntity entity) {

    }

    /**
     * 保存验证
     *
     * @param entity
     */
    public void saveValidate(OmsOrderFwxmZhysEntity entity, BusinessType businessType) {

    }


    /**
     * 保存验证
     */
    public void saveOmsOrderZHYS(OmsOrderFwxmZhysEntity entity, List<OmsOrderFwxmZhysXlEntity> xlList) {

        if (StringUtils.isEmpty(entity.getOrderNo()) && StringUtils.isEmpty(entity.getPreNo())) {
            throw new BaseException("orderNo为空和preNo为空");
        }

        if (StringUtils.isEmpty(entity.getCrossItem())) {
            throw new BaseException("项目为空");
        }

        if (StringUtils.isEmpty(entity.getQhc())) {
            throw new BaseException("去回程为空");
        }

        // 线路-单程运输服务代码
        if (xlList.size() > 0) {
            boolean hasEmptyXlName = xlList.stream().anyMatch(item -> StringUtils.isEmpty(item.getDcysxl()));
            if (hasEmptyXlName) {
                throw new BaseException("单程运输服务值缺失");
            }
        }
    }
}
