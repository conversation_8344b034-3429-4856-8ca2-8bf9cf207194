package com.eci.project.etmsBdTruckSpec.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.etmsBdTruckSpec.entity.EtmsBdTruckSpecEntity;

import com.eci.sso.role.entity.UserContext;
import org.springframework.stereotype.Service;

import java.util.Date;


/**
* 车辆规则Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-03-21
*/
@Service
public class EtmsBdTruckSpecVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(EtmsBdTruckSpecEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(EtmsBdTruckSpecEntity entity, BusinessType businessType) {
        entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        entity.setUpdateDate(new Date());
        if(businessType==BusinessType.INSERT){
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
        }
    }

}
