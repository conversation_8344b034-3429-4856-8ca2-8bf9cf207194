package com.eci.project.omsOrderFwxmZhys.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.common.DictField;
import com.eci.common.ZsrBaseEntity;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
 * 服务项目-综合运输对象 OMS_ORDER_FWXM_ZHYS
 * 可以自己扩展字段
 *
 * @<NAME_EMAIL>
 * @date 2025-06-23
 */
@ApiModel("服务项目-综合运输")
@TableName("OMS_ORDER_FWXM_ZHYS")
@FieldNameConstants
public class OmsOrderFwxmZhysEntity extends ZsrBaseEntity {
    /**
     * 主键
     */
    @ApiModelProperty("主键(36)")
    @TableId("ZHYS_NO")
    private String zhysNo;

    /**
     * 订单号
     */
    @ApiModelProperty("订单号(36)")
    @TableField("ORDER_NO")
    private String orderNo;

    /**
     * 服务项目代码
     */
    @ApiModelProperty("服务项目代码(20)")
    @TableField("FWXM_CODE")
    private String fwxmCode;

    /**
     * MYDL:贸易代理，ZJDB：证件待办，BXFW：保险服务
     */
    @ApiModelProperty("MYDL:贸易代理，ZJDB：证件待办，BXFW：保险服务(20)")
    @TableField("ZZFW_MYDL")
    private String zzfwMydl;

    /**
     * 去回程
     */
    @ApiModelProperty("去回程(20)")
    @TableField("QHC")
    private String qhc;

    /**
     * 跨境班车线路
     */
    @ApiModelProperty("跨境班车线路(20)")
    @TableField("CROSS_LINE")
    @DictField(queryKey = "OMS_BD_KJBCXL")
    private String crossLine;

    /**
     * 项目
     */
    @ApiModelProperty("项目(20)")
    @TableField("CROSS_ITEM")
    @DictField(queryKey = "OMS_BD_XM")
    private String crossItem;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
     * 创建日期
     */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist = false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist = false)
    private Date createDateEnd;

    /**
     * 最后修改人
     */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
     * 最后修改人
     */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
     * 最后修改日期
     */
    @ApiModelProperty("最后修改日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("最后修改日期开始")
    @TableField(exist = false)
    private Date updateDateStart;

    @ApiModelProperty("最后修改日期结束")
    @TableField(exist = false)
    private Date updateDateEnd;

    /**
     * 归属公司
     */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
     * 归属公司
     */
    @ApiModelProperty("归属公司(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
     * 归属组织
     */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
     * 归属组织
     */
    @ApiModelProperty("归属组织(200)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
     * 归属集团
     */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
     * 归属集团
     */
    @ApiModelProperty("归属集团(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    @ApiModelProperty("(20)")
    @TableField("ZZFW_ZJDB")
    private String zzfwZjdb;

    @ApiModelProperty("(20)")
    @TableField("ZZFW_BXFW")
    private String zzfwBxfw;

    @ApiModelProperty("(36)")
    @TableField("PRE_NO")
    private String preNo;


    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public OmsOrderFwxmZhysEntity() {
        this.setSubClazz(OmsOrderFwxmZhysEntity.class);
    }

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public String getZhysNo() {
        this.nodifyGetFiled("zhysNo");
        return zhysNo;
    }

    public OmsOrderFwxmZhysEntity setZhysNo(String zhysNo) {
        this.zhysNo = zhysNo;
        this.nodifySetFiled("zhysNo", zhysNo);
        return this;
    }

    public String getOrderNo() {
        this.nodifyGetFiled("orderNo");
        return orderNo;
    }

    public OmsOrderFwxmZhysEntity setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        this.nodifySetFiled("orderNo", orderNo);
        return this;
    }

    public String getFwxmCode() {
        this.nodifyGetFiled("fwxmCode");
        return fwxmCode;
    }

    public OmsOrderFwxmZhysEntity setFwxmCode(String fwxmCode) {
        this.fwxmCode = fwxmCode;
        this.nodifySetFiled("fwxmCode", fwxmCode);
        return this;
    }

    public String getZzfwMydl() {
        this.nodifyGetFiled("zzfwMydl");
        return zzfwMydl;
    }

    public OmsOrderFwxmZhysEntity setZzfwMydl(String zzfwMydl) {
        this.zzfwMydl = zzfwMydl;
        this.nodifySetFiled("zzfwMydl", zzfwMydl);
        return this;
    }

    public String getQhc() {
        this.nodifyGetFiled("qhc");
        return qhc;
    }

    public OmsOrderFwxmZhysEntity setQhc(String qhc) {
        this.qhc = qhc;
        this.nodifySetFiled("qhc", qhc);
        return this;
    }

    public String getCrossLine() {
        this.nodifyGetFiled("crossLine");
        return crossLine;
    }

    public OmsOrderFwxmZhysEntity setCrossLine(String crossLine) {
        this.crossLine = crossLine;
        this.nodifySetFiled("crossLine", crossLine);
        return this;
    }

    public String getCrossItem() {
        this.nodifyGetFiled("crossItem");
        return crossItem;
    }

    public OmsOrderFwxmZhysEntity setCrossItem(String crossItem) {
        this.crossItem = crossItem;
        this.nodifySetFiled("crossItem", crossItem);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public OmsOrderFwxmZhysEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public OmsOrderFwxmZhysEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public OmsOrderFwxmZhysEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public OmsOrderFwxmZhysEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }

    public OmsOrderFwxmZhysEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public OmsOrderFwxmZhysEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public OmsOrderFwxmZhysEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public OmsOrderFwxmZhysEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public OmsOrderFwxmZhysEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }

    public OmsOrderFwxmZhysEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public OmsOrderFwxmZhysEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public OmsOrderFwxmZhysEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public OmsOrderFwxmZhysEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public OmsOrderFwxmZhysEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public OmsOrderFwxmZhysEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public OmsOrderFwxmZhysEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getZzfwZjdb() {
        this.nodifyGetFiled("zzfwZjdb");
        return zzfwZjdb;
    }

    public OmsOrderFwxmZhysEntity setZzfwZjdb(String zzfwZjdb) {
        this.zzfwZjdb = zzfwZjdb;
        this.nodifySetFiled("zzfwZjdb", zzfwZjdb);
        return this;
    }

    public String getZzfwBxfw() {
        this.nodifyGetFiled("zzfwBxfw");
        return zzfwBxfw;
    }

    public OmsOrderFwxmZhysEntity setZzfwBxfw(String zzfwBxfw) {
        this.zzfwBxfw = zzfwBxfw;
        this.nodifySetFiled("zzfwBxfw", zzfwBxfw);
        return this;
    }

    public OmsOrderFwxmZhysEntity setPreNo(String preNo) {
        this.preNo = preNo;
        this.nodifySetFiled("preNo", preNo);
        return this;
    }

    public String getPreNo() {
        this.nodifyGetFiled("preNo");
        return preNo;
    }

}
