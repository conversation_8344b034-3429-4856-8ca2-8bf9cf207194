package com.eci.project.fzgjCrmFileType.service;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjCrmFileType.dao.FzgjCrmFileTypeDao;
import com.eci.project.fzgjCrmFileType.entity.FzgjCrmFileTypeEntity;
import com.eci.project.fzgjCrmFileType.validate.FzgjCrmFileTypeVal;

import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* CRM附件类型Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-10
*/
@Service
@Slf4j
public class FzgjCrmFileTypeService implements EciBaseService<FzgjCrmFileTypeEntity> {

    @Autowired
    private FzgjCrmFileTypeDao fzgjCrmFileTypeDao;

    @Autowired
    private FzgjCrmFileTypeVal fzgjCrmFileTypeVal;


    @Override
    public TgPageInfo queryPageList(FzgjCrmFileTypeEntity entity) {
        entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
        EciQuery<FzgjCrmFileTypeEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjCrmFileTypeEntity> entities = fzgjCrmFileTypeDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjCrmFileTypeEntity save(FzgjCrmFileTypeEntity entity) {
        // 返回实体对象
        FzgjCrmFileTypeEntity fzgjCrmFileTypeEntity = null;
        fzgjCrmFileTypeVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setGuid(IdWorker.get32UUID());
            entity.setCreateDate(new java.util.Date());
            entity.setUpdateDate(new java.util.Date());
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
            entity.setNodeCode(UserContext.getUserInfo().getDeptCode());
            entity.setNodeName(UserContext.getUserInfo().getDeptName());
            entity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            entity.setCompanyName(UserContext.getUserInfo().getCompanyName());
            fzgjCrmFileTypeEntity = fzgjCrmFileTypeDao.insertOne(entity);

        }else{
            entity.setUpdateDate(new java.util.Date());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            fzgjCrmFileTypeEntity = fzgjCrmFileTypeDao.updateByEntityId(entity);

        }
        return fzgjCrmFileTypeEntity;
    }

    @Override
    public List<FzgjCrmFileTypeEntity> selectList(FzgjCrmFileTypeEntity entity) {
        return fzgjCrmFileTypeDao.selectList(entity);
    }

    @Override
    public FzgjCrmFileTypeEntity selectOneById(Serializable id) {
        return fzgjCrmFileTypeDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjCrmFileTypeEntity> list) {
        fzgjCrmFileTypeDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjCrmFileTypeDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjCrmFileTypeDao.deleteById(id);
    }

}