<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.fzgjBdTruckType.dao.FzgjBdTruckTypeDao">
    <resultMap type="FzgjBdTruckTypeEntity" id="FzgjBdTruckTypeResult">
        <result property="guid" column="GUID"/>
        <result property="code" column="CODE"/>
        <result property="name" column="NAME"/>
        <result property="memo" column="MEMO"/>
        <result property="status" column="STATUS"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="dtype" column="DTYPE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
    </resultMap>

    <sql id="selectFzgjBdTruckTypeEntityVo">
        select
            GUID,
            CODE,
            NAME,
            MEMO,
            STATUS,
            CREATE_USER_NAME,
            CREATE_USER,
            CREATE_DATE,
            UPDATE_USER_NAME,
            UPDATE_USER,
            UPDATE_DATE,
            DTYPE,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME
        from FZGJ_BD_TRUCK_TYPE
    </sql>
</mapper>