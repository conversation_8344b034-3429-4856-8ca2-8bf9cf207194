package com.eci.project.crmCustomerHzTrade.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.crmCustomerHzTrade.service.CrmCustomerHzTradeService;
import com.eci.project.crmCustomerHzTrade.entity.CrmCustomerHzTradeEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 业务伙伴货主贸易关系Controller
*
* @<NAME_EMAIL>
* @date 2025-05-20
*/
@Api(tags = "业务伙伴货主贸易关系")
@RestController
@RequestMapping("/crmCustomerHzTrade")
public class CrmCustomerHzTradeController extends EciBaseController {

    @Autowired
    private CrmCustomerHzTradeService crmCustomerHzTradeService;


    @ApiOperation("业务伙伴货主贸易关系:保存")
    @EciLog(title = "业务伙伴货主贸易关系:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody CrmCustomerHzTradeEntity entity){
        CrmCustomerHzTradeEntity crmCustomerHzTradeEntity =crmCustomerHzTradeService.save(entity);
        return ResponseMsgUtil.success(10001,crmCustomerHzTradeEntity);
    }


    @ApiOperation("业务伙伴货主贸易关系:查询列表")
    @EciLog(title = "业务伙伴货主贸易关系:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody CrmCustomerHzTradeEntity entity){
        List<CrmCustomerHzTradeEntity> crmCustomerHzTradeEntities = crmCustomerHzTradeService.selectList(entity);
        return ResponseMsgUtil.success(10001,crmCustomerHzTradeEntities);
    }


    @ApiOperation("业务伙伴货主贸易关系:分页查询列表")
    @EciLog(title = "业务伙伴货主贸易关系:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody CrmCustomerHzTradeEntity entity){
        TgPageInfo tgPageInfo = crmCustomerHzTradeService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("业务伙伴货主贸易关系:根据ID查一条")
    @EciLog(title = "业务伙伴货主贸易关系:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody CrmCustomerHzTradeEntity entity){
        CrmCustomerHzTradeEntity  crmCustomerHzTradeEntity = crmCustomerHzTradeService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,crmCustomerHzTradeEntity);
    }


    @ApiOperation("业务伙伴货主贸易关系:根据ID删除一条")
    @EciLog(title = "业务伙伴货主贸易关系:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody CrmCustomerHzTradeEntity entity){
        int count = crmCustomerHzTradeService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("业务伙伴货主贸易关系:根据ID字符串删除多条")
    @EciLog(title = "业务伙伴货主贸易关系:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody CrmCustomerHzTradeEntity entity) {
        int count = crmCustomerHzTradeService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}