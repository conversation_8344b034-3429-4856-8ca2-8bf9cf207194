package com.eci.project.omsOrderFwxmTms.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.eci.common.DictField;
import com.eci.common.ZsrBaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

@Data
public class QueryOrderInfoDto extends ZsrBaseEntity {
    @DictField(queryKey = "YNKey01")
    private String isJjh;       // 急货‌:ml-citation{ref="1,6" data="citationList"}
    private String orderNo;         // 订单号‌:ml-citation{ref="1,6" data="citationList"}
    private String sysDocNo;        // 来源系统业务编号‌:ml-citation{ref="3,4" data="citationList"}
    private String consigneeCodeName; // 委托方‌:ml-citation{ref="5,7" data="citationList"}
    private String fwxmName;        // 服务项目‌:ml-citation{ref="4,7" data="citationList"}
    private String customerBuName;  // 客户事业部‌:ml-citation{ref="2,6" data="citationList"}
    private String customerOrderNo; // 客户单据编号‌:ml-citation{ref="3,6" data="citationList"}
    private String shipperName;     // 实际发货方‌:ml-citation{ref="5,7" data="citationList"}
    private String receiverName;    // 实际收货方‌:ml-citation{ref="5,7" data="citationList"}
    private String opDate;          // 业务日期‌:ml-citation{ref="1,6" data="citationList"}
    private String opTypeName;      // 业务类型‌:ml-citation{ref="1,4" data="citationList"}
    private String productCodeName; // 业务产品/项目‌:ml-citation{ref="1,6" data="citationList"}
    private String fwlxName;        // 服务类型‌:ml-citation{ref="4,7" data="citationList"}
    private String fkfaCodeName;    // 客户付款方案‌:ml-citation{ref="2,3" data="citationList"}
    private String xzfaNoName;      // 协作方案‌:ml-citation{ref="2,3" data="citationList"}
    private String bizMemo;         // 业务备注‌:ml-citation{ref="3,6" data="citationList"}
    private String createUserName;  // 创建人‌:ml-citation{ref="3,6" data="citationList"}
    private String createDate;      // 创建时间‌:ml-citation{ref="3,6" data="citationList"}
    private String jdNodeName;      // 接单组织‌:ml-citation{ref="4,7" data="citationList"}
    private String preNo;           // 协同编号‌:ml-citation{ref="3,4" data="citationList"}
    private String isQrjd;          // 确认接单必填项‌:ml-citation{ref="3,6" data="citationList"}
    private String fwxmCode;
    private String receiver;
    private String shipper;
    private String jdUser;
    private String opType;
    private String consigneeCode;
    private String fwlxCode;

    private String createDateStart;

    private String  createDateEnd;


    /**
     * 订单阶段
     */
    private String stage;

    /**
     * 要求完成开始日期
     */
    private String requestOkDateStart;

    /**
     * 要求完成结束日期
     */
    private String requestOkDateEnd;

    /**
     * 业务开始日期
     */
    private String opDateStart;

    /**
     * 业务结束日期
     */
    private String opDateEnd;

    /**
     * 作业完成
     */
    private String opCompleteOk;

    /**
     * 作业数据齐全
     */
    private String dataOk;

    /**
     * 应收费用齐全
     */
    private String arapOk;

    /**
     * 应付费用齐全
     */
    private String apOkQuery;

    /**
     * 控制业务类型显示
     */
    private String opTypeUser;

    /**
     * 控制业务类型显示
     */
    private String WarehouseInNo;
    /**
     * 主单号
     */
    private String mbNo;
    /**
     * 分单号
     */
    private String hbNo;
    /**
     * 订单状态
     */
    private String orderStatus;
    /**
     * 去回程
     */
    private String qhc;

}
