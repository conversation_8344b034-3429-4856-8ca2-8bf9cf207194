package com.eci.project.omsOrderFwxmWorkFkHzqd.service;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.DataExtend;
import com.eci.common.Extensions;
import com.eci.common.ZsrJson;
import com.eci.common.db.DBHelper;
import com.eci.common.util.StringUtils;
import com.eci.common.utils.CommonLib;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.exception.BaseException;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.omsOrder.service.OmsOrderService;
import com.eci.project.omsOrderFwxmWorkFk.dao.OmsOrderFwxmWorkFkDao;
import com.eci.project.omsOrderFwxmWorkFk.entity.OmsOrderFwxmWorkFkEntity;
import com.eci.project.omsOrderFwxmWorkFk.validate.OmsOrderFwxmWorkFkVal;
import com.eci.project.omsOrderFwxmWorkFkHfd.dao.OmsOrderFwxmWorkFkHfdDao;
import com.eci.project.omsOrderFwxmWorkFkHfd.entity.OmsOrderFwxmWorkFkHfdEntity;
import com.eci.project.omsOrderFwxmWorkFkHzqd.dao.OmsOrderFwxmWorkFkHzqdDao;
import com.eci.project.omsOrderFwxmWorkFkHzqd.entity.OmsOrderFwxmWorkFkHzqdEntity;
import com.eci.project.omsOrderFwxmWorkFkHzqd.entity.ResOmsOrderFwxmWorkFkHzqdEntity;
import com.eci.project.omsOrderFwxmWorkFkHzqd.validate.OmsOrderFwxmWorkFkHzqdVal;
import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.jackson.map.MapperConfig;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.List;


/**
 * 反馈内容-核注清单Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-06-03
 */
@Service
@Slf4j
public class OmsOrderFwxmWorkFkHzqdService implements EciBaseService<OmsOrderFwxmWorkFkHzqdEntity> {

    CommonLib cmn = CommonLib.getInstance();

    @Autowired
    private OmsOrderFwxmWorkFkHzqdDao omsOrderFwxmWorkFkHzqdDao;

    @Autowired
    private OmsOrderFwxmWorkFkHzqdVal omsOrderFwxmWorkFkHzqdVal;

    @Autowired
    private OmsOrderFwxmWorkFkVal omsOrderFwxmWorkFkVal;

    @Autowired
    private OmsOrderFwxmWorkFkDao omsOrderFwxmWorkFkDao;

    @Autowired
    private OmsOrderFwxmWorkFkHfdDao omsOrderFwxmWorkFkHfdDao;

    @Autowired
    private OmsOrderService omsOrderService;

    /**
     * 扩展类
     **/
    @Autowired
    private Extensions extensions;

    @Override
    public TgPageInfo queryPageList(OmsOrderFwxmWorkFkHzqdEntity entity) {
        EciQuery<OmsOrderFwxmWorkFkHzqdEntity> eciQuery = EciQuery.buildQuery(entity);
        List<OmsOrderFwxmWorkFkHzqdEntity> entities = omsOrderFwxmWorkFkHzqdDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderFwxmWorkFkHzqdEntity save(OmsOrderFwxmWorkFkHzqdEntity entity) {
        // 返回实体对象
        OmsOrderFwxmWorkFkHzqdEntity omsOrderFwxmWorkFkHzqdEntity = null;
        omsOrderFwxmWorkFkHzqdVal.saveValidate(entity, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            omsOrderFwxmWorkFkHzqdEntity = omsOrderFwxmWorkFkHzqdDao.insertOne(entity);

        } else {

            omsOrderFwxmWorkFkHzqdEntity = omsOrderFwxmWorkFkHzqdDao.updateByEntityId(entity);

        }
        return omsOrderFwxmWorkFkHzqdEntity;
    }

    @Override
    public List<OmsOrderFwxmWorkFkHzqdEntity> selectList(OmsOrderFwxmWorkFkHzqdEntity entity) {
        return omsOrderFwxmWorkFkHzqdDao.selectList(entity);
    }

    @Override
    public OmsOrderFwxmWorkFkHzqdEntity selectOneById(Serializable id) {
        return omsOrderFwxmWorkFkHzqdDao.selectById(id);
    }


    @Override
    public void insertBatch(List<OmsOrderFwxmWorkFkHzqdEntity> list) {
        omsOrderFwxmWorkFkHzqdDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return omsOrderFwxmWorkFkHzqdDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return omsOrderFwxmWorkFkHzqdDao.deleteById(id);
    }


    /**
     * 核注清单-反馈保存
     **/
    public boolean orderFwxmWorkFkHzqdSave(String jsonString) {

        boolean resFlag = true;

        ZsrJson jsonStr = ZsrJson.parse(jsonString);
        String fieldName = jsonStr.check("decFiledName").getString("decFiledName");
        OmsOrderFwxmWorkFkEntity saveEntity = jsonStr.check("entity").getObject("entity", OmsOrderFwxmWorkFkEntity.class);
        List<OmsOrderFwxmWorkFkHzqdEntity> dtDataHZQD = jsonStr.check("dtDataHZQD").getList("dtDataHZQD", OmsOrderFwxmWorkFkHzqdEntity.class);
        if (saveEntity == null) {
            throw new BaseException("entity 解析失败");
        }

        try {
            String workNo = saveEntity.getWorkNo();
            String xzwtNo = saveEntity.getXzwtNo();
            String orderNo = saveEntity.getOrderNo();
            String bizRegId = saveEntity.getBizRegId();
            String fwxmCode = saveEntity.getFwxmCode();

            // 反馈验证
            omsOrderFwxmWorkFkVal.feedBackValidate(workNo, "");

            // 反馈
            saveOrderWorkFK(saveEntity);
            // 核注清单
            saveOrderWorkInfoHZQD(workNo, orderNo, fwxmCode, bizRegId, fieldName, dtDataHZQD);
            // 调用存储过程固话
            extensions.addOmsGh(orderNo);
        } catch (Exception ex) {
            throw new BaseException(ex.getMessage());
        }

        return resFlag;
    }

    /**
     * 保存反馈信息
     */
    public void saveOrderWorkFK(OmsOrderFwxmWorkFkEntity saveEntity) {
        boolean isAdd = false;

        List<OmsOrderFwxmWorkFkEntity> workFkList = omsOrderFwxmWorkFkDao.select()
                .eq(OmsOrderFwxmWorkFkEntity::getBizRegId, saveEntity.getBizRegId())
                .list();
        if (workFkList.size() <= 0) {
            isAdd = true;
        }

        OmsOrderFwxmWorkFkEntity cbsInfo = new OmsOrderFwxmWorkFkEntity();

        cbsInfo.setOrderNo(saveEntity.getOrderNo());
        cbsInfo.setWorkNo(saveEntity.getWorkNo());
        cbsInfo.setFwxmCode(saveEntity.getFwxmCode());
        cbsInfo.setBizRegId(saveEntity.getBizRegId());
        cbsInfo.setHgcyNum(saveEntity.getHgcyNum());
        cbsInfo.setSjcyNum(saveEntity.getSjcyNum());
        cbsInfo.setOpMemo(saveEntity.getOpMemo());
        cbsInfo.setSjcyMemo(saveEntity.getSjcyMemo());
        cbsInfo.setHgcyMemo(saveEntity.getHgcyMemo());

        if (isAdd) {
            cbsInfo.setGuid(IdWorker.get32UUID());
            cbsInfo.setCreateDate(new java.util.Date());
            cbsInfo.setUpdateDate(new java.util.Date());
            cbsInfo.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            cbsInfo.setCreateUserName(UserContext.getUserInfo().getTrueName());
            cbsInfo.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            cbsInfo.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            omsOrderFwxmWorkFkDao.insertOne(cbsInfo);
        } else {
            cbsInfo.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            cbsInfo.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            omsOrderFwxmWorkFkDao.updateByEntityId(cbsInfo);
        }
    }

    /***
     * 核注清单
     * */
    @Transactional
    public void saveOrderWorkInfoHZQD(String workNo, String orderNo, String fwxmCode, String bizRegId, String filedName, List<OmsOrderFwxmWorkFkHzqdEntity> dtDateHZQD) {

        if (StringUtils.hasValue(workNo)) {
            // 先删
            String delSql = "DELETE FROM OMS_ORDER_FWXM_WORK_FK_HZQD WHERE BIZ_REG_ID=" + cmn.SQLQ(bizRegId);
            DBHelper.execute(delSql);
        }

        // 报关单 ：DEC_NO
        // 核注清单：CHECKBILL_NO
        omsOrderFwxmWorkFkVal.validateOrderOnly(filedName, dtDateHZQD);

        if (dtDateHZQD.size() > 0) {
            // 新增
            for (OmsOrderFwxmWorkFkHzqdEntity rows : dtDateHZQD) {
                OmsOrderFwxmWorkFkHzqdEntity cbsCheckInfo = new OmsOrderFwxmWorkFkHzqdEntity();
                rows.setIsCheck(StringUtils.hasValue(rows.getIsCheck()) && rows.getIsCheck().equals("true") ? "Y" : "N");
                rows.setIsGd(StringUtils.hasValue(rows.getIsGd()) && rows.getIsGd().equals("true") ? "Y" : "N");
                rows.setIsSd(StringUtils.hasValue(rows.getIsSd()) && rows.getIsSd().equals("true") ? "Y" : "N");
                rows.setIsJyjy(StringUtils.hasValue(rows.getIsJyjy()) && rows.getIsJyjy().equals("true") ? "Y" : "N");
                rows.setIsBj(StringUtils.hasValue(rows.getIsBj()) && rows.getIsBj().equals("true") ? "Y" : "N");

                BeanUtils.copyProperties(rows, cbsCheckInfo);

                cbsCheckInfo.setGuid(IdWorker.get32UUID());
                cbsCheckInfo.setOrderNo(orderNo);
                cbsCheckInfo.setWorkNo(workNo);
                cbsCheckInfo.setBizRegId(bizRegId);
                cbsCheckInfo.setFwxmCode(fwxmCode);
                cbsCheckInfo.setiEType(DataExtend.toCode(rows.getiEType()));
                cbsCheckInfo.setiEPort(DataExtend.toCode(rows.getiEPort()));
                cbsCheckInfo.setCustomCode(DataExtend.toCode(rows.getCustomCode()));
                cbsCheckInfo.setTradeCode(DataExtend.toCode(rows.getTradeCode()));
                cbsCheckInfo.setYsfs(DataExtend.toCode(rows.getYsfs()));
                cbsCheckInfo.setCurrency(DataExtend.toCode(rows.getCurrency()));
                cbsCheckInfo.setSupervisionMode(DataExtend.toCode(rows.getSupervisionMode()));
                cbsCheckInfo.setOriginArrivalCountry(DataExtend.toCode(rows.getOriginArrivalCountry()));
                cbsCheckInfo.setHzqdBgType(DataExtend.toCode(rows.getHzqdBgType()));
                cbsCheckInfo.setBgFlag(DataExtend.toCode(rows.getBgFlag()));

                cbsCheckInfo.setCreateDate(new java.util.Date());
                cbsCheckInfo.setUpdateDate(new java.util.Date());
                cbsCheckInfo.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
                cbsCheckInfo.setCreateUserName(UserContext.getUserInfo().getTrueName());
                cbsCheckInfo.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
                cbsCheckInfo.setUpdateUserName(UserContext.getUserInfo().getTrueName());
                cbsCheckInfo.setGroupCode(UserContext.getUserInfo().getCompanyCode());
                cbsCheckInfo.setGroupName(UserContext.getUserInfo().getCompanyName());
                cbsCheckInfo.setNodeCode(UserContext.getUserInfo().getDeptCode());
                cbsCheckInfo.setNodeName(UserContext.getUserInfo().getDeptName());
                cbsCheckInfo.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
                cbsCheckInfo.setCompanyName(UserContext.getUserInfo().getCompanyName());
                omsOrderFwxmWorkFkHzqdDao.insertOne(cbsCheckInfo);

                // T032189 保存 核注清单，    报关单， 出入库单 时  关联 查验类型
                // CHECKBILL_NO  DEC_NO  CRKDH
                String itm = cbsCheckInfo.getCheckbillNo();
                if (StringUtils.hasValue(itm)) {
                    omsOrderService.orderCheckType(itm, "FK_PAGE");
                }
            }
        }
    }

    /***
     * 核注清单-反馈加载
     * **/
    public ResOmsOrderFwxmWorkFkHzqdEntity feedBackHzqdLoad(OmsOrderFwxmWorkFkHzqdEntity entity) {


        String bizRegId = entity.getBizRegId();

        if (StringUtils.isEmpty(bizRegId)) {
            throw new BaseException("bizRegId为空");
        }

        ResOmsOrderFwxmWorkFkHzqdEntity response = new ResOmsOrderFwxmWorkFkHzqdEntity();

        // 核注清单作业
        response.setDtDateHZQD(feedBackHzqdInfoLoad(bizRegId));

        return response;
    }


    public List<OmsOrderFwxmWorkFkHzqdEntity> feedBackHzqdInfoLoad(String bizRegId) {

        String groupCode = UserContext.getUserInfo().getCompanyCode();

        String sql = "SELECT A.GUID,A.CHECKBILL_NO,A.CHECKBILL_TABLE_NUM,A.HK_DATE,A.PIECE,A.WEIGHT,A.QTY,A.PRODUCT_CODE\n" +
                "              ,(I_E_TYPE||'|'||DECODE(A.I_E_TYPE,'I','进口','E','出口',''))AS I_E_TYPE\n" +
                "              ,(I_E_PORT || '|' ||(SELECT NAME  FROM FZGJ_BD_CUSTOMS CUSTOMS WHERE 1 = 1  AND CUSTOMS.CODE = A.I_E_PORT )) AS I_E_PORT\n" +
                "              ,(CUSTOM_CODE|| '|'||(SELECT NAME  FROM FZGJ_BD_CUSTOMS CUSTOMS WHERE 1 = 1  AND CUSTOMS.CODE = A.CUSTOM_CODE )) AS CUSTOM_CODE\n" +
                "              ,(TRADE_CODE||'|'|| (SELECT MAX(CUSTOMER.NAME)  FROM CRM_CUSTOMER CUSTOMER WHERE CUSTOMER.STATUS='Y' AND CUSTOMER.CODE = A.TRADE_CODE AND CUSTOMER.GROUP_CODE = " + cmn.SQLQ(groupCode) + "))AS TRADE_CODE\n" +
                "              ,(YSFS||'|'||( SELECT BDYSFS.NAME FROM FZGJ_BD_YSFS BDYSFS WHERE BDYSFS.CODE = A.YSFS)) AS YSFS\n" +
                "              ,NET_WEIGHT,AMOUNT\n" +
                "              ,AMOUNT_CURRENCY\n" +
                "              ,OMS_CODE_NAME(A.SUPERVISION_MODE,'OMS_BD_TRADE') SUPERVISION_MODE\n" +
                "              ,OMS_CODE_NAME(A.CURRENCY, 'OMS_BD_CURRENCY') CURRENCY\n" +
                "              ,HS_CODE\n" +
                "              ,IS_CHECK\n" +
                "              ,IS_SD\n" +
                "              ,IS_GD\n" +
                "              ,OMS_CODE_NAME(A.ORIGIN_ARRIVAL_COUNTRY,'OMS_BD_COUNTRY_TG') ORIGIN_ARRIVAL_COUNTRY\n" +
                "              ,(BILL_MAN||'|'||OMS_NAMECOM(A.BILL_MAN,A.GROUP_CODE,'OMS_SSO_USER'))AS BILL_MAN\n" +
                "              ,BGD_MEMO\n" +
                "              ,TO_CHAR(A.HZQD_DATE,'yyyy-mm-dd') AS HZQD_DATE\n" +
                "              ,OMS_CODE_NAME(A.BG_FLAG, 'OMS_BD_HZQD_BG_FLAG') BG_FLAG\n" +
                "              ,OMS_CODE_NAME(A.HZQD_BG_TYPE, 'OMS_BD_HZQD_BG_TYPE') HZQD_BG_TYPE\n" +
                "              ,TO_CHAR(A.BILL_DATE,'yyyy-mm-dd') AS BILL_DATE\n" +
                "             FROM OMS_ORDER_FWXM_WORK_FK_HZQD A";

        sql += " WHERE 1=1 ";
        sql += " AND A.BIZ_REG_ID=" + cmn.SQLQ(bizRegId);

        return DBHelper.selectList(sql, OmsOrderFwxmWorkFkHzqdEntity.class);
    }

//   核放单暂时不做
//    public List<OmsOrderFwxmWorkFkHfdEntity> feedBackJehfdInfoLoad(String bizRegId) {
//
//        String sql = "SELECT A.GUID,A.HF_NO,A.CHECKBILL_NO,A.FX_DATE,A.REMARK,A.HF_NO_LY";
//
//        sql += " FROM OMS_ORDER_FWXM_WORK_FK_HFD A ";
//        sql += " WHERE A.BIZ_REG_ID=" + cmn.SQLQ(bizRegId);
//
//        List<OmsOrderFwxmWorkFkHfdEntity> data = DBHelper.selectList(sql, OmsOrderFwxmWorkFkHfdEntity.class);
//
//        return data;
//    }
//
//
//    /// <summary>
//    /// 核放单信息保存方法
//    /// </summary>
//    /// <param name="context"></param>
//    /// <param name="workNo"></param>
//    /// <param name="dtDataH"></param>
//    /// <param name="orderNo"></param>
//    /// <param name="fwxmCode"></param>
//    @Transactional
//    public void saveOrderWorkInfoHFD(String workNo, String orderNo, String fwxmCode, String bizRegId, List<OmsOrderFwxmWorkFkHfdEntity> dtDataJEHFD) {
//
//        if (StringUtils.hasValue(workNo)) {
//            // 先删
//            omsOrderFwxmWorkFkHfdDao.delete().eq(OmsOrderFwxmWorkFkHfdEntity::getBizRegId, bizRegId);
//        }
//
//        if (dtDataJEHFD.size() > 0) {
//            // 再新增
//            for (OmsOrderFwxmWorkFkHfdEntity rows : dtDataJEHFD) {
//                OmsOrderFwxmWorkFkHfdEntity cbsCheckInfo = new OmsOrderFwxmWorkFkHfdEntity();
//                cbsCheckInfo.setGuid(IdWorker.get32UUID());
//                cbsCheckInfo.setOrderNo(orderNo);
//                cbsCheckInfo.setWorkNo(workNo);
//                cbsCheckInfo.setBizRegId(bizRegId);
//                cbsCheckInfo.setFwxmCode(fwxmCode);
//
//                if (StringUtils.hasValue(rows.getHfNo())) {
//                    cbsCheckInfo.setHfNo(rows.getHfNo());
//                }
//
//                if (StringUtils.hasValue(rows.getCheckbillNo())) {
//                    cbsCheckInfo.setCheckbillNo(rows.getCheckbillNo());
//                }
//
//                cbsCheckInfo.setFxDate(rows.getFxDate());
//
//                if (StringUtils.hasValue(rows.getRemark())) {
//                    cbsCheckInfo.setRemark(rows.getRemark());
//                }
//
//                if (StringUtils.hasValue(rows.getHfNoLy())) {
//                    cbsCheckInfo.setHfNoLy(rows.getHfNoLy());
//                }
//
//                cbsCheckInfo.setCreateDate(new java.util.Date());
//                cbsCheckInfo.setUpdateDate(new java.util.Date());
//                cbsCheckInfo.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
//                cbsCheckInfo.setCreateUserName(UserContext.getUserInfo().getTrueName());
//                cbsCheckInfo.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
//                cbsCheckInfo.setUpdateUserName(UserContext.getUserInfo().getTrueName());
//                cbsCheckInfo.setGroupCode(UserContext.getUserInfo().getCompanyCode());
//                cbsCheckInfo.setGroupName(UserContext.getUserInfo().getCompanyName());
//                cbsCheckInfo.setNodeCode(UserContext.getUserInfo().getDeptCode());
//                cbsCheckInfo.setNodeName(UserContext.getUserInfo().getDeptName());
//                cbsCheckInfo.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
//                cbsCheckInfo.setCompanyName(UserContext.getUserInfo().getCompanyName());
//                omsOrderFwxmWorkFkHfdDao.insertOne(cbsCheckInfo);
//            }
//        }
//    }
//

}