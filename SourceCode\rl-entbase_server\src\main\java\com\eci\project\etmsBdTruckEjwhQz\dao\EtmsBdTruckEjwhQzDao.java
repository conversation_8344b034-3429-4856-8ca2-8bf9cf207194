package com.eci.project.etmsBdTruckEjwhQz.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.etmsBdTruckEjwhQz.entity.EtmsBdTruckEjwhQzEntity;


/**
* 车辆二级维护历史Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-04-16
*/
public interface EtmsBdTruckEjwhQzDao extends EciBaseDao<EtmsBdTruckEjwhQzEntity> {

}