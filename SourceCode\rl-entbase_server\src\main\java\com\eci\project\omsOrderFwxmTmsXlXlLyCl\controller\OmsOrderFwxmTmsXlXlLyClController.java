package com.eci.project.omsOrderFwxmTmsXlXlLyCl.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.omsOrderFwxmTmsXlXlLyCl.service.OmsOrderFwxmTmsXlXlLyClService;
import com.eci.project.omsOrderFwxmTmsXlXlLyCl.entity.OmsOrderFwxmTmsXlXlLyClEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 委托内容-程运序列-陆运-车辆信息Controller
*
* @<NAME_EMAIL>
* @date 2025-06-04
*/
@Api(tags = "委托内容-程运序列-陆运-车辆信息")
@RestController
@RequestMapping("/omsOrderFwxmTmsXlXlLyCl")
public class OmsOrderFwxmTmsXlXlLyClController extends EciBaseController {

    @Autowired
    private OmsOrderFwxmTmsXlXlLyClService omsOrderFwxmTmsXlXlLyClService;


    @ApiOperation("委托内容-程运序列-陆运-车辆信息:保存")
    @EciLog(title = "委托内容-程运序列-陆运-车辆信息:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody OmsOrderFwxmTmsXlXlLyClEntity entity){
        OmsOrderFwxmTmsXlXlLyClEntity omsOrderFwxmTmsXlXlLyClEntity =omsOrderFwxmTmsXlXlLyClService.save(entity);
        return ResponseMsgUtil.success(10001,omsOrderFwxmTmsXlXlLyClEntity);
    }


    @ApiOperation("委托内容-程运序列-陆运-车辆信息:查询列表")
    @EciLog(title = "委托内容-程运序列-陆运-车辆信息:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody OmsOrderFwxmTmsXlXlLyClEntity entity){
        List<OmsOrderFwxmTmsXlXlLyClEntity> omsOrderFwxmTmsXlXlLyClEntities = omsOrderFwxmTmsXlXlLyClService.selectList(entity);
        return ResponseMsgUtil.success(10001,omsOrderFwxmTmsXlXlLyClEntities);
    }


    @ApiOperation("委托内容-程运序列-陆运-车辆信息:分页查询列表")
    @EciLog(title = "委托内容-程运序列-陆运-车辆信息:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody OmsOrderFwxmTmsXlXlLyClEntity entity){
        TgPageInfo tgPageInfo = omsOrderFwxmTmsXlXlLyClService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("委托内容-程运序列-陆运-车辆信息:根据ID查一条")
    @EciLog(title = "委托内容-程运序列-陆运-车辆信息:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody OmsOrderFwxmTmsXlXlLyClEntity entity){
        OmsOrderFwxmTmsXlXlLyClEntity  omsOrderFwxmTmsXlXlLyClEntity = omsOrderFwxmTmsXlXlLyClService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,omsOrderFwxmTmsXlXlLyClEntity);
    }


    @ApiOperation("委托内容-程运序列-陆运-车辆信息:根据ID删除一条")
    @EciLog(title = "委托内容-程运序列-陆运-车辆信息:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody OmsOrderFwxmTmsXlXlLyClEntity entity){
        int count = omsOrderFwxmTmsXlXlLyClService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("委托内容-程运序列-陆运-车辆信息:根据ID字符串删除多条")
    @EciLog(title = "委托内容-程运序列-陆运-车辆信息:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody OmsOrderFwxmTmsXlXlLyClEntity entity) {
        int count = omsOrderFwxmTmsXlXlLyClService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}