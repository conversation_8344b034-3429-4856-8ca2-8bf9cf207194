package com.eci.project.fzgjBdProductFwxmFf.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjBdProductFwxmFf.entity.FzgjBdProductFwxmFfEntity;

import org.springframework.stereotype.Service;


/**
* 不分发的服务项目Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-01
*/
@Service
public class FzgjBdProductFwxmFfVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjBdProductFwxmFfEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjBdProductFwxmFfEntity entity, BusinessType businessType) {

    }

}
