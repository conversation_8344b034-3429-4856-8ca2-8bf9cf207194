package com.eci.project.fzgjBdProductService.service;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.ZsrJson;
import com.eci.common.ZsrPair;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjBdProductService.dao.FzgjBdProductServiceDao;
import com.eci.project.fzgjBdProductService.entity.FzgjBdProductServiceEntity;
import com.eci.project.fzgjBdProductService.validate.FzgjBdProductServiceVal;

import com.eci.project.fzgjBdServiceType.service.FzgjBdServiceTypeService;
import com.eci.sso.role.entity.UserContext;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 产品服务类型Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-04-01
 */
@Service
@Slf4j
public class FzgjBdProductServiceService implements EciBaseService<FzgjBdProductServiceEntity> {

    @Autowired
    private FzgjBdProductServiceDao fzgjBdProductServiceDao;

    @Autowired
    private FzgjBdProductServiceVal fzgjBdProductServiceVal;

    @Autowired
    private FzgjBdServiceTypeService fzgjBdServiceTypeService;

    /**
     * 根据产品code列表和操作类型列表获取服务类型名称 (优化后)
     * @param productCodes 产品编号列表
     * @param opTypes 操作类型列表
     * @return
     */
    public Map<String, String> getServiceTypeNamesBatch(List<String> productCodes, List<String> opTypes) {
        if (CollectionUtils.isEmpty(productCodes) || CollectionUtils.isEmpty(opTypes)) {
            return new HashMap<>();
        }

        // 构建查询条件
        List<FzgjBdProductServiceEntity> productServiceRelations = fzgjBdProductServiceDao.select()
                .in(FzgjBdProductServiceEntity::getProductCode, productCodes)
                .in(FzgjBdProductServiceEntity::getOpType, opTypes)
                .list();

        // 处理查询结果
        Map<String, List<FzgjBdProductServiceEntity>> groupedRelations = productServiceRelations.stream()
                .collect(Collectors.groupingBy(relation -> relation.getProductCode() + "-" + relation.getOpType()));

        Map<String, String> resultMap = new HashMap<>();
        for (Map.Entry<String, List<FzgjBdProductServiceEntity>> entry : groupedRelations.entrySet()) {
            String productCodeAndOpType = entry.getKey();
            List<FzgjBdProductServiceEntity> relations = entry.getValue();
            List<String> serviceTypeNames = relations.stream()
                    .map(FzgjBdProductServiceEntity::getServiceNo)
                    .map(this::getServiceTypeNameFromDB)
                    .filter(StringUtils::hasText)
                    .distinct()
                    .collect(Collectors.toList());
            resultMap.put(productCodeAndOpType, String.join(",", serviceTypeNames));
        }

        return resultMap;
    }

    /**
     * 根据产品code、操作类型获取服务类型名称
     * @param productCode 产品编号 KF-01ZL
     * @param opType 操作类型：如 KF-01
     * @return
     */
    public String getServiceTypeNames(String productCode, String opType) {
        List<FzgjBdProductServiceEntity> productServiceRelations = fzgjBdProductServiceDao.select()
                .eq(FzgjBdProductServiceEntity::getProductCode, productCode)
                .eq(FzgjBdProductServiceEntity::getOpType, opType)
                .list();

        if (productServiceRelations.isEmpty()) {
            return null;
        }

        List<String> serviceTypeNames = productServiceRelations.stream()
                .map(FzgjBdProductServiceEntity::getServiceNo)
                .map(this::getServiceTypeNameFromDB) // 替换为你的获取名称的逻辑
                .filter(StringUtils::hasText)
                .distinct()
                .collect(Collectors.toList());

        return String.join(",", serviceTypeNames);
    }

    /**
     * 根据服务类型编号获取服务类型名称
     * @param serviceNo 服务类型编号
     * @return
     */
    private String getServiceTypeNameFromDB(String serviceNo) {
        return fzgjBdServiceTypeService.getServiceTypeNameFromDB(serviceNo);
    }


    @Override
    public TgPageInfo queryPageList(FzgjBdProductServiceEntity entity) {
        EciQuery<FzgjBdProductServiceEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjBdProductServiceEntity> entities = fzgjBdProductServiceDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjBdProductServiceEntity save(FzgjBdProductServiceEntity entity) {
        // 返回实体对象
        FzgjBdProductServiceEntity fzgjBdProductServiceEntity = null;
        fzgjBdProductServiceVal.saveValidate(entity, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjBdProductServiceEntity = fzgjBdProductServiceDao.insertOne(entity);

        } else {

            fzgjBdProductServiceEntity = fzgjBdProductServiceDao.updateByEntityId(entity);

        }
        return fzgjBdProductServiceEntity;
    }

    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public FzgjBdProductServiceEntity save(String jsonString) {

        ZsrJson zsrJson = ZsrJson.parse(jsonString).check("opType").check("productCode");
        String opType = zsrJson.getString("opType");
        String productCode = zsrJson.getString("productCode");
        List<String> listKey = zsrJson.getStringList("listKey");

        fzgjBdProductServiceDao.delete()
                .eq(FzgjBdProductServiceEntity::getProductCode, productCode)
                .eq(FzgjBdProductServiceEntity::getOpType, opType)
                .eq(FzgjBdProductServiceEntity::getGroupCode, UserContext.getUserInfo().getCompanyCode())
                .execute();


        listKey.forEach(listKeyItem -> {
            // 返回实体对象
            FzgjBdProductServiceEntity entity = new FzgjBdProductServiceEntity();
            entity.setGuid(IdWorker.getIdStr());
            entity.setProductCode(productCode);
            entity.setOpType(opType);
            entity.setServiceNo(listKeyItem);
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
            entity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            entity.setCompanyName(UserContext.getUserInfo().getCompanyName());

            fzgjBdProductServiceDao.insertOne(entity);
        });
        return null;
    }

    @Override
    public List<FzgjBdProductServiceEntity> selectList(FzgjBdProductServiceEntity entity) {
        return fzgjBdProductServiceDao.selectList(entity);
    }

    @Override
    public FzgjBdProductServiceEntity selectOneById(Serializable id) {
        return fzgjBdProductServiceDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjBdProductServiceEntity> list) {
        fzgjBdProductServiceDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjBdProductServiceDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjBdProductServiceDao.deleteById(id);
    }


}