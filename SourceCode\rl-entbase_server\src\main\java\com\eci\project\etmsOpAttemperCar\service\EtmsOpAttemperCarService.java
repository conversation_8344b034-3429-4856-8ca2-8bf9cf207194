package com.eci.project.etmsOpAttemperCar.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.etmsOpAttemperCar.dao.EtmsOpAttemperCarDao;
import com.eci.project.etmsOpAttemperCar.entity.EtmsOpAttemperCarEntity;
import com.eci.project.etmsOpAttemperCar.validate.EtmsOpAttemperCarVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 用车需求信息Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-06-13
*/
@Service
@Slf4j
public class EtmsOpAttemperCarService implements EciBaseService<EtmsOpAttemperCarEntity> {

    @Autowired
    private EtmsOpAttemperCarDao etmsOpAttemperCarDao;

    @Autowired
    private EtmsOpAttemperCarVal etmsOpAttemperCarVal;


    @Override
    public TgPageInfo queryPageList(EtmsOpAttemperCarEntity entity) {
        EciQuery<EtmsOpAttemperCarEntity> eciQuery = EciQuery.buildQuery(entity);
        List<EtmsOpAttemperCarEntity> entities = etmsOpAttemperCarDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public EtmsOpAttemperCarEntity save(EtmsOpAttemperCarEntity entity) {
        // 返回实体对象
        EtmsOpAttemperCarEntity etmsOpAttemperCarEntity = null;
        etmsOpAttemperCarVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            etmsOpAttemperCarEntity = etmsOpAttemperCarDao.insertOne(entity);

        }else{

            etmsOpAttemperCarEntity = etmsOpAttemperCarDao.updateByEntityId(entity);

        }
        return etmsOpAttemperCarEntity;
    }

    @Override
    public List<EtmsOpAttemperCarEntity> selectList(EtmsOpAttemperCarEntity entity) {
        return etmsOpAttemperCarDao.selectList(entity);
    }

    @Override
    public EtmsOpAttemperCarEntity selectOneById(Serializable id) {
        return etmsOpAttemperCarDao.selectById(id);
    }


    @Override
    public void insertBatch(List<EtmsOpAttemperCarEntity> list) {
        etmsOpAttemperCarDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return etmsOpAttemperCarDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return etmsOpAttemperCarDao.deleteById(id);
    }

}