package com.eci.project.crmCustomerKh.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.codeConvert.EciCode;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 业务伙伴-客户信息对象 CRM_CUSTOMER_KH
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-04-29
*/
@ApiModel("业务伙伴-客户信息")
@TableName("CRM_CUSTOMER_KH")
@FieldNameConstants
public class CrmCustomerKhEntity extends EciBaseEntity{
    /**
    * GUID
    */
    @ApiModelProperty("GUID(36)")
    @TableId("GUID")
    private String guid;

    /**
    * 业务伙伴代码
    */
    @ApiModelProperty("业务伙伴代码(36)")
    @TableField("CUSTOMER_CODE")
    private String customerCode;

    /**
    * 客户来源
    */
    @ApiModelProperty("客户来源(20)")
    @TableField("KHLY")
    private String khly;

    /**
    * 客户级别
    */
    @ApiModelProperty("客户级别(20)")
    @TableField("YWHBJB")
    private String ywhbjb;

    /**
    * 销售人员
    */
    @ApiModelProperty("销售人员(20)")
    @TableField("SALE_USER")
    private String saleUser;

    /**
    * 收款期限(天)
    */
    @ApiModelProperty("收款期限(天)(22)")
    @TableField("SK_DATELINE")
    private Integer skDateline;

    /**
    * 付款期限(天)
    */
    @ApiModelProperty("付款期限(天)(22)")
    @TableField("PAY_DATELINE")
    private Integer payDateline;

    /**
    * 信用额度
    */
    @ApiModelProperty("信用额度(22)")
    @TableField("CREDIT_LIMIT")
    private Integer creditLimit;

    /**
    * 默认结算方式
    */
    @ApiModelProperty("默认结算方式(20)")
    @TableField("ACCOUNT_MODE")
    private String accountMode;

    /**
    * 默认付款方式
    */
    @ApiModelProperty("默认付款方式(20)")
    @TableField("PAY_MODE")
    private String payMode;

    /**
    * 接单冻结标志(Y/N)
    */
    @ApiModelProperty("接单冻结标志(Y/N)(1)")
    @TableField("JD_FREEZE")
    private String jdFreeze;

    /**
    * 开票周期(天)
    */
    @ApiModelProperty("开票周期(天)(22)")
    @TableField("KP_CYCLE")
    private Integer kpCycle;

    /**
    * 开票期限(天)
    */
    @ApiModelProperty("开票期限(天)(22)")
    @TableField("KP_DATELINE")
    private Integer kpDateline;

    /**
    * 开户行账号-客户开票信息
    */
    @ApiModelProperty("开户行账号-客户开票信息(50)")
    @TableField("ACCOUNT_KH")
    private String accountKh;

    /**
    * 开户行名称-客户开票信息
    */
    @ApiModelProperty("开户行名称-客户开票信息(80)")
    @TableField("BANK_KH")
    private String bankKh;

    /**
    * 税号-客户开票信息
    */
    @ApiModelProperty("税号-客户开票信息(30)")
    @TableField("TAX_NO_KH")
    private String taxNoKh;

    /**
    * 默认开票税率-客户开票信息
    */
    @ApiModelProperty("默认开票税率-客户开票信息(22)")
    @TableField("TAX_VAL_KH")
    private Integer taxValKh;

    /**
    * 默认开票发票类型-客户开票信息
    */
    @ApiModelProperty("默认开票发票类型-客户开票信息(20)")
    @TableField("INV_TYPE_KH")
    @EciCode("FZGJ_BD_INVOICE_TYPE")
    private String invTypeKh;

    /**
    * 结款周期(天)
    */
    @ApiModelProperty("结款周期(天)(22)")
    @TableField("JK_CYCLE")
    private Integer jkCycle;

    /**
    * 付款冻结标志(Y/N)
    */
    @ApiModelProperty("付款冻结标志(Y/N)(1)")
    @TableField("PAY_FREEZE")
    private String payFreeze;

    /**
    * 企业电话
    */
    @ApiModelProperty("企业电话(50)")
    @TableField("TEL")
    private String tel;

    /**
    * 企业地址
    */
    @ApiModelProperty("企业地址(80)")
    @TableField("ADDRESS")
    private String address;

    /**
    * 客户合作服务代码，多个逗号分隔
    */
    @ApiModelProperty("客户合作服务代码，多个逗号分隔(500)")
    @TableField("HZFW_CODE_KH")
    private String hzfwCodeKh;

    /**
    * 客户合作服务名称，多个逗号分隔
    */
    @ApiModelProperty("客户合作服务名称，多个逗号分隔(500)")
    @TableField("HZFW_NAME_KH")
    private String hzfwNameKh;

    /**
    * 备注-客户开票信息
    */
    @ApiModelProperty("备注-客户开票信息(500)")
    @TableField("MEMO_KH")
    private String memoKh;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 最后修改日期
    */
    @ApiModelProperty("最后修改日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("最后修改日期开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("最后修改日期结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(200)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 结算备注
    */
    @ApiModelProperty("结算备注(100)")
    @TableField("ACCOUNT_MEMO")
    private String accountMemo;

    /**
    * 受票人资质
    */
    @ApiModelProperty("受票人资质(20)")
    @TableField("SPRZZ")
    private String sprzz;

    /**
    * 客户订单类型
    */
    @ApiModelProperty("客户订单类型(20)")
    @TableField("ORDER_TYPE")
    private String orderType;

    /**
    * 合作开始日期
    */
    @ApiModelProperty("合作开始日期(7)")
    @TableField("BEGIN_DATE")
    private Date beginDate;

    @ApiModelProperty("合作开始日期开始")
    @TableField(exist=false)
    private Date beginDateStart;

    @ApiModelProperty("合作开始日期结束")
    @TableField(exist=false)
    private Date beginDateEnd;

    /**
    * 合作结束日期
    */
    @ApiModelProperty("合作结束日期(7)")
    @TableField("END_DATE")
    private Date endDate;

    @ApiModelProperty("合作结束日期开始")
    @TableField(exist=false)
    private Date endDateStart;

    @ApiModelProperty("合作结束日期结束")
    @TableField(exist=false)
    private Date endDateEnd;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public CrmCustomerKhEntity() {
        this.setSubClazz(CrmCustomerKhEntity.class);
    }

    public CrmCustomerKhEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public CrmCustomerKhEntity setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
        this.nodifySetFiled("customerCode", customerCode);
        return this;
    }

    public String getCustomerCode() {
        this.nodifyGetFiled("customerCode");
        return customerCode;
    }

    public CrmCustomerKhEntity setKhly(String khly) {
        this.khly = khly;
        this.nodifySetFiled("khly", khly);
        return this;
    }

    public String getKhly() {
        this.nodifyGetFiled("khly");
        return khly;
    }

    public CrmCustomerKhEntity setYwhbjb(String ywhbjb) {
        this.ywhbjb = ywhbjb;
        this.nodifySetFiled("ywhbjb", ywhbjb);
        return this;
    }

    public String getYwhbjb() {
        this.nodifyGetFiled("ywhbjb");
        return ywhbjb;
    }

    public CrmCustomerKhEntity setSaleUser(String saleUser) {
        this.saleUser = saleUser;
        this.nodifySetFiled("saleUser", saleUser);
        return this;
    }

    public String getSaleUser() {
        this.nodifyGetFiled("saleUser");
        return saleUser;
    }

    public CrmCustomerKhEntity setSkDateline(Integer skDateline) {
        this.skDateline = skDateline;
        this.nodifySetFiled("skDateline", skDateline);
        return this;
    }

    public Integer getSkDateline() {
        this.nodifyGetFiled("skDateline");
        return skDateline;
    }

    public CrmCustomerKhEntity setPayDateline(Integer payDateline) {
        this.payDateline = payDateline;
        this.nodifySetFiled("payDateline", payDateline);
        return this;
    }

    public Integer getPayDateline() {
        this.nodifyGetFiled("payDateline");
        return payDateline;
    }

    public CrmCustomerKhEntity setCreditLimit(Integer creditLimit) {
        this.creditLimit = creditLimit;
        this.nodifySetFiled("creditLimit", creditLimit);
        return this;
    }

    public Integer getCreditLimit() {
        this.nodifyGetFiled("creditLimit");
        return creditLimit;
    }

    public CrmCustomerKhEntity setAccountMode(String accountMode) {
        this.accountMode = accountMode;
        this.nodifySetFiled("accountMode", accountMode);
        return this;
    }

    public String getAccountMode() {
        this.nodifyGetFiled("accountMode");
        return accountMode;
    }

    public CrmCustomerKhEntity setPayMode(String payMode) {
        this.payMode = payMode;
        this.nodifySetFiled("payMode", payMode);
        return this;
    }

    public String getPayMode() {
        this.nodifyGetFiled("payMode");
        return payMode;
    }

    public CrmCustomerKhEntity setJdFreeze(String jdFreeze) {
        this.jdFreeze = jdFreeze;
        this.nodifySetFiled("jdFreeze", jdFreeze);
        return this;
    }

    public String getJdFreeze() {
        this.nodifyGetFiled("jdFreeze");
        return jdFreeze;
    }

    public CrmCustomerKhEntity setKpCycle(Integer kpCycle) {
        this.kpCycle = kpCycle;
        this.nodifySetFiled("kpCycle", kpCycle);
        return this;
    }

    public Integer getKpCycle() {
        this.nodifyGetFiled("kpCycle");
        return kpCycle;
    }

    public CrmCustomerKhEntity setKpDateline(Integer kpDateline) {
        this.kpDateline = kpDateline;
        this.nodifySetFiled("kpDateline", kpDateline);
        return this;
    }

    public Integer getKpDateline() {
        this.nodifyGetFiled("kpDateline");
        return kpDateline;
    }

    public CrmCustomerKhEntity setAccountKh(String accountKh) {
        this.accountKh = accountKh;
        this.nodifySetFiled("accountKh", accountKh);
        return this;
    }

    public String getAccountKh() {
        this.nodifyGetFiled("accountKh");
        return accountKh;
    }

    public CrmCustomerKhEntity setBankKh(String bankKh) {
        this.bankKh = bankKh;
        this.nodifySetFiled("bankKh", bankKh);
        return this;
    }

    public String getBankKh() {
        this.nodifyGetFiled("bankKh");
        return bankKh;
    }

    public CrmCustomerKhEntity setTaxNoKh(String taxNoKh) {
        this.taxNoKh = taxNoKh;
        this.nodifySetFiled("taxNoKh", taxNoKh);
        return this;
    }

    public String getTaxNoKh() {
        this.nodifyGetFiled("taxNoKh");
        return taxNoKh;
    }

    public CrmCustomerKhEntity setTaxValKh(Integer taxValKh) {
        this.taxValKh = taxValKh;
        this.nodifySetFiled("taxValKh", taxValKh);
        return this;
    }

    public Integer getTaxValKh() {
        this.nodifyGetFiled("taxValKh");
        return taxValKh;
    }

    public CrmCustomerKhEntity setInvTypeKh(String invTypeKh) {
        this.invTypeKh = invTypeKh;
        this.nodifySetFiled("invTypeKh", invTypeKh);
        return this;
    }

    public String getInvTypeKh() {
        this.nodifyGetFiled("invTypeKh");
        return invTypeKh;
    }

    public CrmCustomerKhEntity setJkCycle(Integer jkCycle) {
        this.jkCycle = jkCycle;
        this.nodifySetFiled("jkCycle", jkCycle);
        return this;
    }

    public Integer getJkCycle() {
        this.nodifyGetFiled("jkCycle");
        return jkCycle;
    }

    public CrmCustomerKhEntity setPayFreeze(String payFreeze) {
        this.payFreeze = payFreeze;
        this.nodifySetFiled("payFreeze", payFreeze);
        return this;
    }

    public String getPayFreeze() {
        this.nodifyGetFiled("payFreeze");
        return payFreeze;
    }

    public CrmCustomerKhEntity setTel(String tel) {
        this.tel = tel;
        this.nodifySetFiled("tel", tel);
        return this;
    }

    public String getTel() {
        this.nodifyGetFiled("tel");
        return tel;
    }

    public CrmCustomerKhEntity setAddress(String address) {
        this.address = address;
        this.nodifySetFiled("address", address);
        return this;
    }

    public String getAddress() {
        this.nodifyGetFiled("address");
        return address;
    }

    public CrmCustomerKhEntity setHzfwCodeKh(String hzfwCodeKh) {
        this.hzfwCodeKh = hzfwCodeKh;
        this.nodifySetFiled("hzfwCodeKh", hzfwCodeKh);
        return this;
    }

    public String getHzfwCodeKh() {
        this.nodifyGetFiled("hzfwCodeKh");
        return hzfwCodeKh;
    }

    public CrmCustomerKhEntity setHzfwNameKh(String hzfwNameKh) {
        this.hzfwNameKh = hzfwNameKh;
        this.nodifySetFiled("hzfwNameKh", hzfwNameKh);
        return this;
    }

    public String getHzfwNameKh() {
        this.nodifyGetFiled("hzfwNameKh");
        return hzfwNameKh;
    }

    public CrmCustomerKhEntity setMemoKh(String memoKh) {
        this.memoKh = memoKh;
        this.nodifySetFiled("memoKh", memoKh);
        return this;
    }

    public String getMemoKh() {
        this.nodifyGetFiled("memoKh");
        return memoKh;
    }

    public CrmCustomerKhEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public CrmCustomerKhEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public CrmCustomerKhEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public CrmCustomerKhEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public CrmCustomerKhEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public CrmCustomerKhEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public CrmCustomerKhEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public CrmCustomerKhEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public CrmCustomerKhEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public CrmCustomerKhEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public CrmCustomerKhEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public CrmCustomerKhEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public CrmCustomerKhEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public CrmCustomerKhEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public CrmCustomerKhEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public CrmCustomerKhEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public CrmCustomerKhEntity setAccountMemo(String accountMemo) {
        this.accountMemo = accountMemo;
        this.nodifySetFiled("accountMemo", accountMemo);
        return this;
    }

    public String getAccountMemo() {
        this.nodifyGetFiled("accountMemo");
        return accountMemo;
    }

    public CrmCustomerKhEntity setSprzz(String sprzz) {
        this.sprzz = sprzz;
        this.nodifySetFiled("sprzz", sprzz);
        return this;
    }

    public String getSprzz() {
        this.nodifyGetFiled("sprzz");
        return sprzz;
    }

    public CrmCustomerKhEntity setOrderType(String orderType) {
        this.orderType = orderType;
        this.nodifySetFiled("orderType", orderType);
        return this;
    }

    public String getOrderType() {
        this.nodifyGetFiled("orderType");
        return orderType;
    }

    public CrmCustomerKhEntity setBeginDate(Date beginDate) {
        this.beginDate = beginDate;
        this.nodifySetFiled("beginDate", beginDate);
        return this;
    }

    public Date getBeginDate() {
        this.nodifyGetFiled("beginDate");
        return beginDate;
    }

    public CrmCustomerKhEntity setBeginDateStart(Date beginDateStart) {
        this.beginDateStart = beginDateStart;
        this.nodifySetFiled("beginDateStart", beginDateStart);
        return this;
    }

    public Date getBeginDateStart() {
        this.nodifyGetFiled("beginDateStart");
        return beginDateStart;
    }

    public CrmCustomerKhEntity setBeginDateEnd(Date beginDateEnd) {
        this.beginDateEnd = beginDateEnd;
        this.nodifySetFiled("beginDateEnd", beginDateEnd);
        return this;
    }

    public Date getBeginDateEnd() {
        this.nodifyGetFiled("beginDateEnd");
        return beginDateEnd;
    }
    public CrmCustomerKhEntity setEndDate(Date endDate) {
        this.endDate = endDate;
        this.nodifySetFiled("endDate", endDate);
        return this;
    }

    public Date getEndDate() {
        this.nodifyGetFiled("endDate");
        return endDate;
    }

    public CrmCustomerKhEntity setEndDateStart(Date endDateStart) {
        this.endDateStart = endDateStart;
        this.nodifySetFiled("endDateStart", endDateStart);
        return this;
    }

    public Date getEndDateStart() {
        this.nodifyGetFiled("endDateStart");
        return endDateStart;
    }

    public CrmCustomerKhEntity setEndDateEnd(Date endDateEnd) {
        this.endDateEnd = endDateEnd;
        this.nodifySetFiled("endDateEnd", endDateEnd);
        return this;
    }

    public Date getEndDateEnd() {
        this.nodifyGetFiled("endDateEnd");
        return endDateEnd;
    }
}
