package com.eci.project.fzgjBdServiceItemPagesPt.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjBdServiceItemPagesPt.dao.FzgjBdServiceItemPagesPtDao;
import com.eci.project.fzgjBdServiceItemPagesPt.entity.FzgjBdServiceItemPagesPtEntity;
import com.eci.project.fzgjBdServiceItemPagesPt.validate.FzgjBdServiceItemPagesPtVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 企业服务项目对应页面编辑区Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-01
*/
@Service
@Slf4j
public class FzgjBdServiceItemPagesPtService implements EciBaseService<FzgjBdServiceItemPagesPtEntity> {

    @Autowired
    private FzgjBdServiceItemPagesPtDao fzgjBdServiceItemPagesPtDao;

    @Autowired
    private FzgjBdServiceItemPagesPtVal fzgjBdServiceItemPagesPtVal;


    @Override
    public TgPageInfo queryPageList(FzgjBdServiceItemPagesPtEntity entity) {
        EciQuery<FzgjBdServiceItemPagesPtEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjBdServiceItemPagesPtEntity> entities = fzgjBdServiceItemPagesPtDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjBdServiceItemPagesPtEntity save(FzgjBdServiceItemPagesPtEntity entity) {
        // 返回实体对象
        FzgjBdServiceItemPagesPtEntity fzgjBdServiceItemPagesPtEntity = null;
        fzgjBdServiceItemPagesPtVal.saveValidate(entity,BllContext.getBusinessType());
        entity.setStatus("Y");

            fzgjBdServiceItemPagesPtEntity = fzgjBdServiceItemPagesPtDao.insertOne(entity);


        return fzgjBdServiceItemPagesPtEntity;
    }

    @Override
    public List<FzgjBdServiceItemPagesPtEntity> selectList(FzgjBdServiceItemPagesPtEntity entity) {
        return fzgjBdServiceItemPagesPtDao.selectList(entity);
    }

    @Override
    public FzgjBdServiceItemPagesPtEntity selectOneById(Serializable id) {
        return fzgjBdServiceItemPagesPtDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjBdServiceItemPagesPtEntity> list) {
        fzgjBdServiceItemPagesPtDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjBdServiceItemPagesPtDao.deleteByIds(ids);
    }

    public void deleteByCode(String code){
       fzgjBdServiceItemPagesPtDao.delete().eq("SERVICE_ITEM_CODE",code);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjBdServiceItemPagesPtDao.deleteById(id);
    }

}