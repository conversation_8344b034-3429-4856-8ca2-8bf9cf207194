package com.eci.project.fzgjBaseInformation.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.sql.Clob;
import java.util.Date;


/**
* 新闻通知公告信息表对象 FZGJ_BASE_INFORMATION
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-06-18
*/
@ApiModel("新闻通知公告信息表")
@TableName("FZGJ_BASE_INFORMATION")
@FieldNameConstants
public class FzgjBaseInformationEntity extends EciBaseEntity{
    /**
    * 主键
    */
    @ApiModelProperty("主键(50)")
    @TableId("GUID")
    private String guid;

    /**
    * 标题
    */
    @ApiModelProperty("标题(100)")
    @TableField("TITLE")
    private String title;

    /**
    * 发布状态(0:未发布,1:已发布)
    */
    @ApiModelProperty("发布状态(0:未发布,1:已发布)(22)")
    @TableField("STATUS")
    private Integer status;

    /**
    * 信息类型(1:新闻,2:公告)
    */
    @ApiModelProperty("信息类型(1:新闻,2:公告)(22)")
    @TableField("INFO_TYPE")
    private Integer infoType;

    /**
    * 是否推荐显示(0:不推荐,1:推荐显示)
    */
    @ApiModelProperty("是否推荐显示(0:不推荐,1:推荐显示)(22)")
    @TableField("IS_RECOMMEND")
    private Integer isRecommend;

    /**
    * 作者
    */
    @ApiModelProperty("作者(50)")
    @TableField("AUTHOR")
    private String author;

    /**
    * 发布时间
    */
    @ApiModelProperty("发布时间(7)")
    @TableField("PUBLISHDATE")
    private Date publishdate;

    @ApiModelProperty("发布时间开始")
    @TableField(exist=false)
    private Date publishdateStart;

    @ApiModelProperty("发布时间结束")
    @TableField(exist=false)
    private Date publishdateEnd;

    /**
    * 内容
    */
    @ApiModelProperty("内容(4,000)")
    @TableField("CONTENT")
    private String content;

    /**
    * 显示顺序
    */
    @ApiModelProperty("显示顺序(22)")
    @TableField("SORT")
    private Integer sort;

    /**
    * 显示图片路径
    */
    @ApiModelProperty("显示图片路径(200)")
    @TableField("ATTACHMENT_URL")
    private String attachmentUrl;

    /**
    * 备注
    */
    @ApiModelProperty("备注(500)")
    @TableField("REMARK")
    private String remark;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 创建时间
    */
    @ApiModelProperty("创建时间(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建时间开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建时间结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 修改时间
    */
    @ApiModelProperty("修改时间(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("修改时间开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("修改时间结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public FzgjBaseInformationEntity() {
        this.setSubClazz(FzgjBaseInformationEntity.class);
    }

    public FzgjBaseInformationEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public FzgjBaseInformationEntity setTitle(String title) {
        this.title = title;
        this.nodifySetFiled("title", title);
        return this;
    }

    public String getTitle() {
        this.nodifyGetFiled("title");
        return title;
    }

    public FzgjBaseInformationEntity setStatus(Integer status) {
        this.status = status;
        this.nodifySetFiled("status", status);
        return this;
    }

    public Integer getStatus() {
        this.nodifyGetFiled("status");
        return status;
    }

    public FzgjBaseInformationEntity setInfoType(Integer infoType) {
        this.infoType = infoType;
        this.nodifySetFiled("infoType", infoType);
        return this;
    }

    public Integer getInfoType() {
        this.nodifyGetFiled("infoType");
        return infoType;
    }

    public FzgjBaseInformationEntity setIsRecommend(Integer isRecommend) {
        this.isRecommend = isRecommend;
        this.nodifySetFiled("isRecommend", isRecommend);
        return this;
    }

    public Integer getIsRecommend() {
        this.nodifyGetFiled("isRecommend");
        return isRecommend;
    }

    public FzgjBaseInformationEntity setAuthor(String author) {
        this.author = author;
        this.nodifySetFiled("author", author);
        return this;
    }

    public String getAuthor() {
        this.nodifyGetFiled("author");
        return author;
    }

    public FzgjBaseInformationEntity setPublishdate(Date publishdate) {
        this.publishdate = publishdate;
        this.nodifySetFiled("publishdate", publishdate);
        return this;
    }

    public Date getPublishdate() {
        this.nodifyGetFiled("publishdate");
        return publishdate;
    }

    public FzgjBaseInformationEntity setPublishdateStart(Date publishdateStart) {
        this.publishdateStart = publishdateStart;
        this.nodifySetFiled("publishdateStart", publishdateStart);
        return this;
    }

    public Date getPublishdateStart() {
        this.nodifyGetFiled("publishdateStart");
        return publishdateStart;
    }

    public FzgjBaseInformationEntity setPublishdateEnd(Date publishdateEnd) {
        this.publishdateEnd = publishdateEnd;
        this.nodifySetFiled("publishdateEnd", publishdateEnd);
        return this;
    }

    public Date getPublishdateEnd() {
        this.nodifyGetFiled("publishdateEnd");
        return publishdateEnd;
    }
    public FzgjBaseInformationEntity setContent(String content) {
        this.content = content;
        this.nodifySetFiled("content", content);
        return this;
    }

    public String getContent() {
        this.nodifyGetFiled("content");
        return content;
    }

    public FzgjBaseInformationEntity setSort(Integer sort) {
        this.sort = sort;
        this.nodifySetFiled("sort", sort);
        return this;
    }

    public Integer getSort() {
        this.nodifyGetFiled("sort");
        return sort;
    }

    public FzgjBaseInformationEntity setAttachmentUrl(String attachmentUrl) {
        this.attachmentUrl = attachmentUrl;
        this.nodifySetFiled("attachmentUrl", attachmentUrl);
        return this;
    }

    public String getAttachmentUrl() {
        this.nodifyGetFiled("attachmentUrl");
        return attachmentUrl;
    }

    public FzgjBaseInformationEntity setRemark(String remark) {
        this.remark = remark;
        this.nodifySetFiled("remark", remark);
        return this;
    }

    public String getRemark() {
        this.nodifyGetFiled("remark");
        return remark;
    }

    public FzgjBaseInformationEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public FzgjBaseInformationEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public FzgjBaseInformationEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public FzgjBaseInformationEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public FzgjBaseInformationEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public FzgjBaseInformationEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public FzgjBaseInformationEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public FzgjBaseInformationEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public FzgjBaseInformationEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public FzgjBaseInformationEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public FzgjBaseInformationEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public FzgjBaseInformationEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public FzgjBaseInformationEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public FzgjBaseInformationEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
}
