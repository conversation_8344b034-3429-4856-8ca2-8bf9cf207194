package com.eci.project.fzgjCrmFileType.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjCrmFileType.entity.FzgjCrmFileTypeEntity;

import org.springframework.stereotype.Service;


/**
* CRM附件类型Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-10
*/
@Service
public class FzgjCrmFileTypeVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjCrmFileTypeEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjCrmFileTypeEntity entity, BusinessType businessType) {

    }

}
