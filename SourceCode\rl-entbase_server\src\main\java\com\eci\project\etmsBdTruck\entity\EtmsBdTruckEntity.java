package com.eci.project.etmsBdTruck.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;


/**
* 车辆信息对象 ETMS_BD_TRUCK
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-04-11
*/
@ApiModel("车辆信息")
@TableName("ETMS_BD_TRUCK")
public class EtmsBdTruckEntity extends EtmsBdTruckBaseEntity{

}
