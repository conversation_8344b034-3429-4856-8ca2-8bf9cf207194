package com.eci.project.fzgjBdHx.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjBdHx.service.FzgjBdHxService;
import com.eci.project.fzgjBdHx.entity.FzgjBdHxEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 航线信息Controller
*
* @<NAME_EMAIL>
* @date 2025-03-13
*/
@Api(tags = "航线信息")
@RestController
@RequestMapping("/fzgjBdHx")
public class FzgjBdHxController extends EciBaseController {

    @Autowired
    private FzgjBdHxService fzgjBdHxService;


    @ApiOperation("航线信息:保存")
    @EciLog(title = "航线信息:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody FzgjBdHxEntity entity){
        FzgjBdHxEntity fzgjBdHxEntity =fzgjBdHxService.save(entity);
        return ResponseMsgUtil.success(10001,fzgjBdHxEntity);
    }


    @ApiOperation("航线信息:查询列表")
    @EciLog(title = "航线信息:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjBdHxEntity entity){
        List<FzgjBdHxEntity> fzgjBdHxEntities = fzgjBdHxService.selectList(entity);
        return ResponseMsgUtil.success(10001,fzgjBdHxEntities);
    }


    @ApiOperation("航线信息:分页查询列表")
    @EciLog(title = "航线信息:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjBdHxEntity entity){
        TgPageInfo tgPageInfo = fzgjBdHxService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }

    @ApiOperation("航线信息:导出")
    @EciLog(title = "航线信息:导出", businessType = BusinessType.SELECT)
    @PostMapping("/ExportExcel")
    @EciAction()
    public ResponseMsg ExportExcel(@RequestBody FzgjBdHxEntity entity){
        fzgjBdHxService.Export(entity);
        return ResponseMsgUtil.success(10001,null);
    }


    @ApiOperation("航线信息:根据ID查一条")
    @EciLog(title = "航线信息:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjBdHxEntity entity){
        FzgjBdHxEntity  fzgjBdHxEntity = fzgjBdHxService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,fzgjBdHxEntity);
    }


    @ApiOperation("航线信息:根据ID删除一条")
    @EciLog(title = "航线信息:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjBdHxEntity entity){
        int count = fzgjBdHxService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("航线信息:根据ID字符串删除多条")
    @EciLog(title = "航线信息:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjBdHxEntity entity) {
        int count = fzgjBdHxService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}