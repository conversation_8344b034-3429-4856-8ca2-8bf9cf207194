<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.etmsBdDriverCheckHis.dao.EtmsBdDriverCheckHisDao">
    <resultMap type="EtmsBdDriverCheckHisEntity" id="EtmsBdDriverCheckHisResult">
        <result property="guid" column="GUID"/>
        <result property="driverGuid" column="DRIVER_GUID"/>
        <result property="checkDate" column="CHECK_DATE"/>
        <result property="nextDate" column="NEXT_DATE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
    </resultMap>

    <sql id="selectEtmsBdDriverCheckHisEntityVo">
        select
            GUID,
            DRIVER_GUID,
            CHECK_DATE,
            NEXT_DATE,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            CREATE_USER_NAME,
            UPDATE_USER_NAME
        from ETMS_BD_DRIVER_CHECK_HIS
    </sql>
</mapper>