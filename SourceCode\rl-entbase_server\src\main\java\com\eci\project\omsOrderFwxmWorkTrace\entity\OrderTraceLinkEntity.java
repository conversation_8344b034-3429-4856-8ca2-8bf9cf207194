package com.eci.project.omsOrderFwxmWorkTrace.entity;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * @ClassName: OrderTraceLinkEntity
 * @Author: guangyan.mei
 * @Date: 2025/5/9 10:08
 * @Description: TODO
 */
public class OrderTraceLinkEntity {

    public String orderNo;
    public String workNo;
    public String fwxmCode;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getWorkNo() {
        return workNo;
    }

    public void setWorkNo(String workNo) {
        this.workNo = workNo;
    }

    public String getFwxmCode() {
        return fwxmCode;
    }

    public void setFwxmCode(String fwxmCode) {
        this.fwxmCode = fwxmCode;
    }

    public String getFwxmName() {
        return fwxmName;
    }

    public void setFwxmName(String fwxmName) {
        this.fwxmName = fwxmName;
    }

    public String getFwlxCode() {
        return fwlxCode;
    }

    public void setFwlxCode(String fwlxCode) {
        this.fwlxCode = fwlxCode;
    }

    public String getFwlxName() {
        return fwlxName;
    }

    public void setFwlxName(String fwlxName) {
        this.fwlxName = fwlxName;
    }

    public String getLinkCode() {
        return linkCode;
    }

    public void setLinkCode(String linkCode) {
        this.linkCode = linkCode;
    }

    public String getLinkCodeName() {
        return linkCodeName;
    }

    public void setLinkCodeName(String linkCodeName) {
        this.linkCodeName = linkCodeName;
    }

    public String getIsException() {
        return isException;
    }

    public void setIsException(String isException) {
        this.isException = isException;
    }

    public String getLinkColor() {
        return linkColor;
    }

    public void setLinkColor(String linkColor) {
        this.linkColor = linkColor;
    }



    public String fwxmName;
    public String fwlxCode;
    public String fwlxName;
    public String linkCode;
    public String linkCodeName;
    public String isException;
    public String linkColor;
    public LocalDateTime planOkDate;

    public LocalDateTime getPlanOkDate() {
        return planOkDate;
    }

    public void setPlanOkDate(LocalDateTime planOkDate) {
        this.planOkDate = planOkDate;
    }

    public LocalDateTime getActualOkDate() {
        return actualOkDate;
    }

    public void setActualOkDate(LocalDateTime actualOkDate) {
        this.actualOkDate = actualOkDate;
    }

    public LocalDateTime  actualOkDate;


}
