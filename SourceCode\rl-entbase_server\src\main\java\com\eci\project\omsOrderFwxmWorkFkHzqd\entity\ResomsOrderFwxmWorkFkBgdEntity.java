package com.eci.project.omsOrderFwxmWorkFkHzqd.entity;

import com.eci.project.omsOrderFwxmWorkFkHfd.entity.OmsOrderFwxmWorkFkHfdEntity;
import com.eci.project.omsOrderFwxmWorkFkHzqd.entity.OmsOrderFwxmWorkFkHzqdEntity;

import java.util.List;

/**
 * @ClassName: ResomsOrderFwxmWorkFkBgdEntity
 * @Author: guangyan.mei
 * @Date: 2025/6/4 10:39
 * @Description: TODO
 */
public class ResomsOrderFwxmWorkFkBgdEntity {

    // 核注清单
    public List<OmsOrderFwxmWorkFkHzqdEntity> dtDateBGD;

    // 金二核放单
    public List<OmsOrderFwxmWorkFkHfdEntity> dtDateLYHFD;
}
