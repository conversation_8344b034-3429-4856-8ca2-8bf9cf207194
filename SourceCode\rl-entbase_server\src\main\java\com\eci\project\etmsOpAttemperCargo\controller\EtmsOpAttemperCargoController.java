package com.eci.project.etmsOpAttemperCargo.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.etmsOpAttemperCargo.service.EtmsOpAttemperCargoService;
import com.eci.project.etmsOpAttemperCargo.entity.EtmsOpAttemperCargoEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 托运货物信息Controller
*
* @<NAME_EMAIL>
* @date 2025-06-13
*/
@Api(tags = "托运货物信息")
@RestController
@RequestMapping("/etmsOpAttemperCargo")
public class EtmsOpAttemperCargoController extends EciBaseController {

    @Autowired
    private EtmsOpAttemperCargoService etmsOpAttemperCargoService;


    @ApiOperation("托运货物信息:保存")
    @EciLog(title = "托运货物信息:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody EtmsOpAttemperCargoEntity entity){
        EtmsOpAttemperCargoEntity etmsOpAttemperCargoEntity =etmsOpAttemperCargoService.save(entity);
        return ResponseMsgUtil.success(10001,etmsOpAttemperCargoEntity);
    }


    @ApiOperation("托运货物信息:查询列表")
    @EciLog(title = "托运货物信息:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody EtmsOpAttemperCargoEntity entity){
        List<EtmsOpAttemperCargoEntity> etmsOpAttemperCargoEntities = etmsOpAttemperCargoService.selectList(entity);
        return ResponseMsgUtil.success(10001,etmsOpAttemperCargoEntities);
    }


    @ApiOperation("托运货物信息:分页查询列表")
    @EciLog(title = "托运货物信息:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody EtmsOpAttemperCargoEntity entity){
        TgPageInfo tgPageInfo = etmsOpAttemperCargoService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("托运货物信息:根据ID查一条")
    @EciLog(title = "托运货物信息:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody EtmsOpAttemperCargoEntity entity){
        EtmsOpAttemperCargoEntity  etmsOpAttemperCargoEntity = etmsOpAttemperCargoService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,etmsOpAttemperCargoEntity);
    }


    @ApiOperation("托运货物信息:根据ID删除一条")
    @EciLog(title = "托运货物信息:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody EtmsOpAttemperCargoEntity entity){
        int count = etmsOpAttemperCargoService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("托运货物信息:根据ID字符串删除多条")
    @EciLog(title = "托运货物信息:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody EtmsOpAttemperCargoEntity entity) {
        int count = etmsOpAttemperCargoService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}