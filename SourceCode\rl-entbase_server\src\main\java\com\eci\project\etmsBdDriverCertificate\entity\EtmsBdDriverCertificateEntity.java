package com.eci.project.etmsBdDriverCertificate.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 司机证件管理对象 ETMS_BD_DRIVER_CERTIFICATE
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-04-28
*/
@ApiModel("司机证件管理")
@TableName("ETMS_BD_DRIVER_CERTIFICATE")
@FieldNameConstants
public class EtmsBdDriverCertificateEntity extends EciBaseEntity{
    /**
    * GUID
    */
    @ApiModelProperty("GUID(50)")
    @TableId("GUID")
    private String guid;

    /**
    * 司机GUID
    */
    @ApiModelProperty("司机GUID(50)")
    @TableField("DRIVER_GUID")
    private String driverGuid;

    /**
    * 证件编号
    */
    @ApiModelProperty("证件编号(30)")
    @TableField("CERTIFICATE_NO")
    private String certificateNo;

    /**
    * 证件类型
    */
    @ApiModelProperty("证件类型(40)")
    @TableField("CERTIFICATE_TYPE")
    private String certificateType;

    /**
    * 驾照编号
    */
    @ApiModelProperty("驾照编号(30)")
    @TableField("DRIVER_LICENSE_TYPE")
    private String driverLicenseType;

    /**
    * 驾龄
    */
    @ApiModelProperty("驾龄(2)")
    @TableField("DRIVERING_YEARS")
    private String driveringYears;

    /**
    * 发证日期
    */
    @ApiModelProperty("发证日期(7)")
    @TableField("START_DATE")
    private Date startDate;

    @ApiModelProperty("发证日期开始")
    @TableField(exist=false)
    private Date startDateStart;

    @ApiModelProperty("发证日期结束")
    @TableField(exist=false)
    private Date startDateEnd;

    /**
     * 发证日期
     */
    @ApiModelProperty("有效期(7)")
    @TableField("END_DATE")
    private Date endDate;

    @ApiModelProperty("有效期开始")
    @TableField(exist=false)
    private Date endDateStart;

    @ApiModelProperty("有效期结束")
    @TableField(exist=false)
    private Date endDateEnd;
    @ApiModelProperty("证件状态")
    @TableField(exist=false)
    private String statusName;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 备注
    */
    @ApiModelProperty("备注(20)")
    @TableField("MEMO")
    private String memo;

    @ApiModelProperty("(20)")
    @TableField("CREATE_USER")
    private String createUser;

    @ApiModelProperty("(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    @ApiModelProperty("(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("结束")
    @TableField(exist=false)
    private Date createDateEnd;

    @ApiModelProperty("(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public EtmsBdDriverCertificateEntity() {
        this.setSubClazz(EtmsBdDriverCertificateEntity.class);
    }

    public EtmsBdDriverCertificateEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public EtmsBdDriverCertificateEntity setDriverGuid(String driverGuid) {
        this.driverGuid = driverGuid;
        this.nodifySetFiled("driverGuid", driverGuid);
        return this;
    }

    public String getDriverGuid() {
        this.nodifyGetFiled("driverGuid");
        return driverGuid;
    }

    public EtmsBdDriverCertificateEntity setCertificateNo(String certificateNo) {
        this.certificateNo = certificateNo;
        this.nodifySetFiled("certificateNo", certificateNo);
        return this;
    }

    public String getCertificateNo() {
        this.nodifyGetFiled("certificateNo");
        return certificateNo;
    }

    public EtmsBdDriverCertificateEntity setCertificateType(String certificateType) {
        this.certificateType = certificateType;
        this.nodifySetFiled("certificateType", certificateType);
        return this;
    }

    public String getCertificateType() {
        this.nodifyGetFiled("certificateType");
        return certificateType;
    }

    public EtmsBdDriverCertificateEntity setDriverLicenseType(String driverLicenseType) {
        this.driverLicenseType = driverLicenseType;
        this.nodifySetFiled("driverLicenseType", driverLicenseType);
        return this;
    }

    public String getDriverLicenseType() {
        this.nodifyGetFiled("driverLicenseType");
        return driverLicenseType;
    }

    public EtmsBdDriverCertificateEntity setDriveringYears(String driveringYears) {
        this.driveringYears = driveringYears;
        this.nodifySetFiled("driveringYears", driveringYears);
        return this;
    }

    public String getDriveringYears() {
        this.nodifyGetFiled("driveringYears");
        return driveringYears;
    }

    public EtmsBdDriverCertificateEntity setStartDate(Date startDate) {
        this.startDate = startDate;
        this.nodifySetFiled("startDate", startDate);
        return this;
    }

    public Date getStartDate() {
        this.nodifyGetFiled("startDate");
        return startDate;
    }

    public EtmsBdDriverCertificateEntity setStartDateStart(Date startDateStart) {
        this.startDateStart = startDateStart;
        this.nodifySetFiled("startDateStart", startDateStart);
        return this;
    }


    public Date getStartDateStart() {
        this.nodifyGetFiled("startDateStart");
        return startDateStart;
    }

    public EtmsBdDriverCertificateEntity setStartDateEnd(Date startDateEnd) {
        this.startDateEnd = startDateEnd;
        this.nodifySetFiled("startDateEnd", startDateEnd);
        return this;
    }

    public Date getStartDateEnd() {
        this.nodifyGetFiled("startDateEnd");
        return startDateEnd;
    }

    public EtmsBdDriverCertificateEntity setEndDate(Date endDate) {
        this.endDate = endDate;
        this.nodifySetFiled("endDate", endDate);
        return this;
    }

    public Date getEndDate() {
        this.nodifyGetFiled("endDate");
        return endDate;
    }

    public EtmsBdDriverCertificateEntity setEndDateStart(Date endDateStart) {
        this.endDateStart = endDateStart;
        this.nodifySetFiled("endDateStart", endDateStart);
        return this;
    }


    public Date getEndDateStart() {
        this.nodifyGetFiled("endDateStart");
        return endDateStart;
    }

    public EtmsBdDriverCertificateEntity setEndDateEnd(Date endDateEnd) {
        this.endDateEnd = endDateEnd;
        this.nodifySetFiled("endDateEnd", endDateEnd);
        return this;
    }

    public Date getEndDateEnd() {
        this.nodifyGetFiled("endDateEnd");
        return endDateEnd;
    }


    public EtmsBdDriverCertificateEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public EtmsBdDriverCertificateEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public EtmsBdDriverCertificateEntity setStatusName(String statusName) {
        this.statusName = statusName;
        this.nodifySetFiled("statusName", statusName);
        return this;
    }

    public String getStatusName() {
        this.nodifyGetFiled("statusName");
        return statusName;
    }

    public EtmsBdDriverCertificateEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public EtmsBdDriverCertificateEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public EtmsBdDriverCertificateEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public EtmsBdDriverCertificateEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public EtmsBdDriverCertificateEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public EtmsBdDriverCertificateEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public EtmsBdDriverCertificateEntity setMemo(String memo) {
        this.memo = memo;
        this.nodifySetFiled("memo", memo);
        return this;
    }

    public String getMemo() {
        this.nodifyGetFiled("memo");
        return memo;
    }

    public EtmsBdDriverCertificateEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public EtmsBdDriverCertificateEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public EtmsBdDriverCertificateEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public EtmsBdDriverCertificateEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public EtmsBdDriverCertificateEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public EtmsBdDriverCertificateEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public EtmsBdDriverCertificateEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public EtmsBdDriverCertificateEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
}
