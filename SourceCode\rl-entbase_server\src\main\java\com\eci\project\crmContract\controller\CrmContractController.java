package com.eci.project.crmContract.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.crmContract.service.CrmContractService;
import com.eci.project.crmContract.entity.CrmContractEntity;
import com.eci.project.crmCustomer.entity.CrmCustomerEntity;
import com.eci.project.crmCustomer.service.CrmCustomerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.Date;
import java.util.List;

/**
* 合同Controller
*
* @<NAME_EMAIL>
* @date 2025-05-21
*/
@Api(tags = "合同")
@RestController
@RequestMapping("/crmContract")
public class CrmContractController extends EciBaseController {

    @Autowired
    private CrmContractService crmContractService;
    @Autowired
    private CrmCustomerService crmCustomerService;

    @ApiOperation("合同:保存")
    @EciLog(title = "合同:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody CrmContractEntity entity) throws Exception {
        CrmCustomerEntity customer= crmCustomerService.selectOneById(entity.getCustomerGuid());
        if(customer==null) throw new Exception("未查询到业务伙伴信息");
        entity.setCustomerCode(customer.getCode());
        entity.setCustomerName(customer.getName());
        entity.setCustomerShortName(customer.getShortName());
        entity.setLatestExpirationDate(entity.getRenewalExpirationDate()!=null?entity.getRenewalExpirationDate():entity.getExpirationDate());
        //存在终止时间，状态为失效
        if(entity.getTerminationDate()!=null){
            entity.setHtzt("Y");
        }else {
            //首次签约时间大于当前时间  状态为 待生效
            if(entity.getSignDateFirst().after(new Date())){
                entity.setHtzt("D");
            }else if(entity.getSignDateFirst().before(new Date()) //首次签约时间大于当前时间，且最新失效日期大于当前日期，章台为有效
                    &&new Date().before(entity.getLatestExpirationDate())){
                entity.setHtzt("N");
            }else if(entity.getLatestExpirationDate()!=null  //存在最新失效日期，且当前时间大于失效日期时，状态为失效
                    &&new Date().after(entity.getLatestExpirationDate())){
                entity.setHtzt("Y");
            }
        }

        CrmContractEntity crmContractEntity =crmContractService.save(entity);
        return ResponseMsgUtil.success(10001,crmContractEntity);
    }

    @ApiOperation("合同:续签")
    @EciLog(title = "合同:续签", businessType = BusinessType.INSERT)
    @PostMapping("/saveRenewalDate")
    @EciAction()
    public ResponseMsg saveRenewalDate(@RequestBody CrmContractEntity entity) throws Exception {
        if(entity.getRenewalDate().after(entity.getRenewalExpirationDate())){
            throw new Exception("续签日期不得大于等于续签合同失效日期。");
        }
        List<CrmContractEntity> list=crmContractService.selectList(entity.getIds());
        if(list!=null&&list.size()>0){
            list.forEach(model->{
                model.setLatestExpirationDate(entity.getRenewalExpirationDate()!=null?entity.getRenewalExpirationDate():entity.getExpirationDate());
                model.setStatus("N");
                model.setTerminationDate(null);
                model.setRenewalDate(entity.getRenewalDate());
                model.setRenewalExpirationDate(entity.getRenewalExpirationDate());
                //存在终止时间，状态为失效
                if(model.getTerminationDate()!=null){
                    model.setHtzt("Y");
                }else {
                    //首次签约时间大于当前时间  状态为 待生效
                    if(model.getSignDateFirst()!=null&&model.getSignDateFirst().after(new Date())){
                        model.setHtzt("D");
                    }else if(model.getSignDateFirst()!=null&&model.getSignDateFirst().before(new Date()) //首次签约时间大于当前时间，且最新失效日期大于当前日期，章台为有效
                            &&new Date().before(model.getLatestExpirationDate())){
                        model.setHtzt("N");
                    }else if(model.getLatestExpirationDate()!=null  //存在最新失效日期，且当前时间大于失效日期时，状态为失效
                            &&new Date().after(model.getLatestExpirationDate())){
                        model.setHtzt("Y");
                    }
                }
                crmContractService.Update(model);
            });

        }

        return ResponseMsgUtil.success(10001);
    }
    @ApiOperation("合同:终止")
    @EciLog(title = "合同:终止", businessType = BusinessType.INSERT)
    @PostMapping("/saveTerminationDate")
    @EciAction()
    public ResponseMsg saveTerminationDate(@RequestBody CrmContractEntity entity) throws Exception {
        List<CrmContractEntity> list=crmContractService.selectList(entity.getIds());
        if(list!=null&&list.size()>0){
            list.forEach(model->{
                model.setLatestExpirationDate(entity.getRenewalExpirationDate()!=null?entity.getRenewalExpirationDate():entity.getExpirationDate());
                model.setStatus("Y");
                model.setTerminationDate(entity.getTerminationDate());
                model.setHtzt("Y");
                crmContractService.Update(model);
            });
        }

        return ResponseMsgUtil.success(10001);
    }

    @ApiOperation("合同:查询列表")
    @EciLog(title = "合同:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody CrmContractEntity entity){
        List<CrmContractEntity> crmContractEntities = crmContractService.selectList(entity);
        return ResponseMsgUtil.success(10001,crmContractEntities);
    }


    @ApiOperation("合同:分页查询列表")
    @EciLog(title = "合同:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody CrmContractEntity entity){
        TgPageInfo tgPageInfo = crmContractService.queryPageList(entity);
        return ResponseMsgUtil.successPlus(10001,tgPageInfo);
    }


    @ApiOperation("合同:根据ID查一条")
    @EciLog(title = "合同:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody CrmContractEntity entity){
        CrmContractEntity  crmContractEntity = crmContractService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,crmContractEntity);
    }


    @ApiOperation("合同:根据ID删除一条")
    @EciLog(title = "合同:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody CrmContractEntity entity){
        int count = crmContractService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("合同:根据ID字符串删除多条")
    @EciLog(title = "合同:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody CrmContractEntity entity) {
        int count = crmContractService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}