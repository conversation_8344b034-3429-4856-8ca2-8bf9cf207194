package com.eci.common;

/**
 * 通用code name结果集
 *
 * <AUTHOR>
 * @version 1.0
 * <remark>code name 类</remark>
 * @date 2025-1-13 11:25:47
 */
public class CodeNameCommon {
    private final String code;
    private final String name;
    private final SqlTemplate sqlTemplate; // 可能为 null

    public CodeNameCommon(String code, String name) {
        this(code, name, null);
    }

    public CodeNameCommon(String code, String name, SqlTemplate sqlTemplate) {
        this.code = code;
        this.name = name;
        this.sqlTemplate = sqlTemplate;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public SqlTemplate getSqlTemplate() {
        return sqlTemplate;
    }
}
