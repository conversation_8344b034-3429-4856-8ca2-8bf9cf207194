package com.eci.project.etmsBdTruckEjwhQz.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 车辆二级维护历史对象 ETMS_BD_TRUCK_EJWH_QZ
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-04-16
*/
@ApiModel("车辆二级维护历史")
@TableName("ETMS_BD_TRUCK_EJWH_QZ")
@FieldNameConstants
public class EtmsBdTruckEjwhQzEntity extends EciBaseEntity{
    /**
    * GUID
    */
    @ApiModelProperty("GUID(50)")
    @TableField("GUID")
    private String guid;

    /**
    * GUID
    */
    @ApiModelProperty("GUID(50)")
    @TableField("TRUCK_GUID")
    private String truckGuid;

    /**
    * 二级维护日期
    */
    @ApiModelProperty("二级维护日期(7)")
    @TableField("WH_DATE")
    private Date whDate;

    @ApiModelProperty("二级维护日期开始")
    @TableField(exist=false)
    private Date whDateStart;

    @ApiModelProperty("二级维护日期结束")
    @TableField(exist=false)
    private Date whDateEnd;

    /**
    * 下次维护日期
    */
    @ApiModelProperty("下次维护日期(7)")
    @TableField("NEXT_DATE")
    private Date nextDate;

    @ApiModelProperty("下次维护日期开始")
    @TableField(exist=false)
    private Date nextDateStart;

    @ApiModelProperty("下次维护日期结束")
    @TableField(exist=false)
    private Date nextDateEnd;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 修改标志（0：不变，1：修改，2：删除，3：新增）
    */
    @ApiModelProperty("修改标志（0：不变，1：修改，2：删除，3：新增）(50)")
    @TableField("MOD_MARK")
    private String modMark;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public EtmsBdTruckEjwhQzEntity() {
        this.setSubClazz(EtmsBdTruckEjwhQzEntity.class);
    }

    public EtmsBdTruckEjwhQzEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public EtmsBdTruckEjwhQzEntity setTruckGuid(String truckGuid) {
        this.truckGuid = truckGuid;
        this.nodifySetFiled("truckGuid", truckGuid);
        return this;
    }

    public String getTruckGuid() {
        this.nodifyGetFiled("truckGuid");
        return truckGuid;
    }

    public EtmsBdTruckEjwhQzEntity setWhDate(Date whDate) {
        this.whDate = whDate;
        this.nodifySetFiled("whDate", whDate);
        return this;
    }

    public Date getWhDate() {
        this.nodifyGetFiled("whDate");
        return whDate;
    }

    public EtmsBdTruckEjwhQzEntity setWhDateStart(Date whDateStart) {
        this.whDateStart = whDateStart;
        this.nodifySetFiled("whDateStart", whDateStart);
        return this;
    }

    public Date getWhDateStart() {
        this.nodifyGetFiled("whDateStart");
        return whDateStart;
    }

    public EtmsBdTruckEjwhQzEntity setWhDateEnd(Date whDateEnd) {
        this.whDateEnd = whDateEnd;
        this.nodifySetFiled("whDateEnd", whDateEnd);
        return this;
    }

    public Date getWhDateEnd() {
        this.nodifyGetFiled("whDateEnd");
        return whDateEnd;
    }
    public EtmsBdTruckEjwhQzEntity setNextDate(Date nextDate) {
        this.nextDate = nextDate;
        this.nodifySetFiled("nextDate", nextDate);
        return this;
    }

    public Date getNextDate() {
        this.nodifyGetFiled("nextDate");
        return nextDate;
    }

    public EtmsBdTruckEjwhQzEntity setNextDateStart(Date nextDateStart) {
        this.nextDateStart = nextDateStart;
        this.nodifySetFiled("nextDateStart", nextDateStart);
        return this;
    }

    public Date getNextDateStart() {
        this.nodifyGetFiled("nextDateStart");
        return nextDateStart;
    }

    public EtmsBdTruckEjwhQzEntity setNextDateEnd(Date nextDateEnd) {
        this.nextDateEnd = nextDateEnd;
        this.nodifySetFiled("nextDateEnd", nextDateEnd);
        return this;
    }

    public Date getNextDateEnd() {
        this.nodifyGetFiled("nextDateEnd");
        return nextDateEnd;
    }
    public EtmsBdTruckEjwhQzEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public EtmsBdTruckEjwhQzEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public EtmsBdTruckEjwhQzEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public EtmsBdTruckEjwhQzEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public EtmsBdTruckEjwhQzEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public EtmsBdTruckEjwhQzEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public EtmsBdTruckEjwhQzEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public EtmsBdTruckEjwhQzEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public EtmsBdTruckEjwhQzEntity setModMark(String modMark) {
        this.modMark = modMark;
        this.nodifySetFiled("modMark", modMark);
        return this;
    }

    public String getModMark() {
        this.nodifyGetFiled("modMark");
        return modMark;
    }

}
