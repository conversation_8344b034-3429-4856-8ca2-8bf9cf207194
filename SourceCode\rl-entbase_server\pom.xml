<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.4.5</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>com.eci</groupId>
    <artifactId>rl-entbase_server</artifactId>
    <version>1.0.0</version>
    <name>rl-entbase_server</name>
    <packaging>jar</packaging>
    <description>智慧公路项目基础和订单服务</description>
    <properties>
        <java.version>1.8</java.version>
        <tiangong.version>2.2.137</tiangong.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
            <version>2.9.3</version> <!-- 请根据需要选择版本 -->
        </dependency>

        <dependency>
            <groupId>com.eci</groupId>
            <artifactId>tiangong-core</artifactId>
            <version>${tiangong.version}</version>
        </dependency>
        <dependency>
            <groupId>com.eci</groupId>
            <artifactId>tiangong-component</artifactId>
            <version>${tiangong.version}</version>
        </dependency>
        <dependency>
            <groupId>com.eci</groupId>
            <artifactId>tiangong-sso</artifactId>
            <version>${tiangong.version}</version>
        </dependency>

        <dependency>
            <groupId>com.eci</groupId>
            <artifactId>tiangong-system</artifactId>
            <version>${tiangong.version}</version>
        </dependency>
        <dependency>
            <groupId>com.eci</groupId>
            <artifactId>tiangong-third</artifactId>
            <version>${tiangong.version}</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
