package com.eci.project.fzgjBdDistrict.service;

import com.eci.crud.service.EciBaseService;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjBdDistrict.entity.FzgjBdDistrictEntity;
import com.eci.project.fzgjBdDistrict.entity.FzgjBdDistrictRealtionEntity;


/**
* 区县Service接口
* 业务逻辑层, 接口代码, 只需要写自定义的部分, 增删改查部分在父级接口已经实现
* @<NAME_EMAIL>
* @date 2025-03-17
*/
public interface IFzgjBdDistrictService extends EciBaseService<FzgjBdDistrictEntity> {

    /**
     * 区县所属关系分页列表
     * */
    TgPageInfo selectDistrictRealtionPageList(FzgjBdDistrictRealtionEntity entity);

}
