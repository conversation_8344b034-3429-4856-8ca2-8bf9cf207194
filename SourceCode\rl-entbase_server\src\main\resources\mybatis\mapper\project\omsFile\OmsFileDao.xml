<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.omsFile.dao.OmsFileDao">
    <resultMap type="OmsFileEntity" id="OmsFileResult">
        <result property="guid" column="GUID"/>
        <result property="goodsGuid" column="GOODS_GUID"/>
        <result property="memo" column="MEMO"/>
        <result property="fileType" column="FILE_TYPE"/>
        <result property="url" column="URL"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="originFileName" column="ORIGIN_FILE_NAME"/>
        <result property="fileNo" column="FILE_NO"/>
        <result property="dataObject" column="DATA_OBJECT"/>
        <result property="sysCode" column="SYS_CODE"/>
        <result property="orderNo" column="ORDER_NO"/>
        <result property="fileFormat" column="FILE_FORMAT"/>
        <result property="source" column="SOURCE"/>
        <result property="status" column="STATUS"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="longitude" column="LONGITUDE"/>
        <result property="latitude" column="LATITUDE"/>
        <result property="startTime" column="START_TIME"/>
        <result property="startAddress" column="START_ADDRESS"/>
        <result property="sendTime" column="SEND_TIME"/>
        <result property="sendAddress" column="SEND_ADDRESS"/>
    </resultMap>

    <sql id="selectOmsFileEntityVo">
        select
            GUID,
            GOODS_GUID,
            MEMO,
            FILE_TYPE,
            URL,
            CREATE_USER,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_DATE,
            ORIGIN_FILE_NAME,
            FILE_NO,
            DATA_OBJECT,
            SYS_CODE,
            ORDER_NO,
            FILE_FORMAT,
            SOURCE,
            STATUS,
            NODE_CODE,
            NODE_NAME,
            COMPANY_CODE,
            COMPANY_NAME,
            GROUP_CODE,
            GROUP_NAME,
            CREATE_USER_NAME,
            UPDATE_USER_NAME,
            LONGITUDE,
            LATITUDE,
            START_TIME,
            START_ADDRESS,
            SEND_TIME,
            SEND_ADDRESS
        from OMS_FILE
    </sql>
</mapper>