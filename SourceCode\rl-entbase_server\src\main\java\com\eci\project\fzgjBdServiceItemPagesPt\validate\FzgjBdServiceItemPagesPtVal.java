package com.eci.project.fzgjBdServiceItemPagesPt.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjBdServiceItemPagesPt.entity.FzgjBdServiceItemPagesPtEntity;

import com.eci.sso.role.entity.UserContext;
import org.springframework.stereotype.Service;

import java.util.Date;


/**
* 企业服务项目对应页面编辑区Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-01
*/
@Service
public class FzgjBdServiceItemPagesPtVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjBdServiceItemPagesPtEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjBdServiceItemPagesPtEntity entity, BusinessType businessType) {
        entity.setUpdateUser(UserContext.getUserInfo().getTrueName());
        entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        entity.setUpdateDate(new Date());

        entity.setCreateUser(UserContext.getUserInfo().getTrueName());
        entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
        entity.setCreateDate(new Date());

    }

}
