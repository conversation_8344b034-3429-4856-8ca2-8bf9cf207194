package com.eci.project.omsOrder.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eci.common.*;
import com.eci.common.enums.Enums;
import com.eci.common.enums.OrderEnum;
import com.eci.common.utils.CommonLib;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.exception.BaseException;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.etmsOpHead.service.EtmsOpHeadPushService;
import com.eci.project.omsOrder.dao.OmsOrderDao;
import com.eci.project.omsOrder.entity.OmsOrderEntity;
import com.eci.project.omsOrder.entity.OrderEntity;
import com.eci.project.omsOrder.entity.OrderRequest;
import com.eci.project.omsOrder.validate.OmsOrderVal;
import com.eci.project.omsOrderFw.dao.OmsOrderFwDao;
import com.eci.project.omsOrderFw.entity.OmsOrderFwEntity;
import com.eci.project.omsOrderFwxm.dao.OmsOrderFwxmDao;
import com.eci.project.omsOrderFwxm.entity.OmsOrderFwxmEntity;
import com.eci.project.omsOrderFwxmBgbj.dao.OmsOrderFwxmBgbjDao;
import com.eci.project.omsOrderFwxmBgbj.entity.OmsOrderFwxmBgbjEntity;
import com.eci.project.omsOrderFwxmWork.dao.OmsOrderFwxmWorkDao;
import com.eci.project.omsOrderFwxmWork.entity.OmsOrderFwxmWorkEntity;
import com.eci.project.omsOrderLog.entity.OmsOrderLogEntity;
import com.eci.project.omsOrderLog.service.OmsOrderLogService;
import com.eci.sso.login.entity.UserInfo;
import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单表Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-04-14
 */
@Service
@Slf4j
public class SendOrderService implements EciBaseService<OmsOrderEntity> {

    // 推荐从配置文件中注入
    @Value("${api.order.save-url}")
    private String targetUrl;

    @Autowired
    private OmsOrderDao omsOrderDao;

    @Autowired
    private OmsOrderVal omsOrderVal;

    @Autowired
    private OmsOrderFwxmWorkDao omsOrderFwxmWorkDao;

    @Autowired
    private OmsOrderFwxmBgbjDao omsOrderFwxmBgbjDao;

    @Autowired
    private OmsOrderFwxmDao omsOrderFwxmDao;

    @Autowired
    private EtmsOpHeadPushService etmsOpHeadPushService;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    /**
     * 发送订单到外部系统
     */
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderEntity sendOrder(String jsonString) {
        try {
            ZsrJson json = ZsrJson.parse(jsonString);
            String orderNo = json.check("orderNo").getString("orderNo");
            String companyCode = UserContext.getUserInfo().getCompanyCode();

            // 1. 获取订单表头数据
            OmsOrderEntity orderEntity = omsOrderDao.select()
                    .eq(OmsOrderEntity::getOrderNo, orderNo)
                    .one();
            if (orderEntity == null) {
                throw new BaseException("订单不存在，orderNo=" + orderNo);
            }

            // 2. 获取协作任务列表
            List<OmsOrderFwxmWorkEntity> workList = omsOrderFwxmWorkDao.select()
                    .eq(OmsOrderFwxmWorkEntity::getOrderNo, orderNo)
                    .eq(OmsOrderFwxmWorkEntity::getGroupCode, companyCode)
                    .list();
            if (workList.isEmpty()) {
                throw new BaseException("协作任务不存在！");
            }

            // 3. 批量获取报关信息
            Set<String> workNos = workList.stream()
                    .map(OmsOrderFwxmWorkEntity::getWorkNo)
                    .collect(Collectors.toSet());
            List<OmsOrderFwxmBgbjEntity> bgbjList = omsOrderFwxmBgbjDao.select()
                    .in(OmsOrderFwxmBgbjEntity::getWorkNo, workNos)
                    .eq(OmsOrderFwxmBgbjEntity::getGroupCode, companyCode)
                    .list();
            Map<String, OmsOrderFwxmBgbjEntity> bgbjMap = bgbjList.stream()
                    .collect(Collectors.toMap(OmsOrderFwxmBgbjEntity::getWorkNo, b -> b));

            // 4. 初始化 API 客户端
            OrderApiClientService apiClient = new OrderApiClientService();

            // 订单号或者preno  货物信息：omsordergoods


            // 5. 遍历发送每个工作项
            for (OmsOrderFwxmWorkEntity workItem : workList) {
                String workNo = workItem.getWorkNo();
                OmsOrderFwxmBgbjEntity bgbjEntity = bgbjMap.get(workNo);

                if (bgbjEntity == null) {
                    log.warn("缺少报关信息，workNo={}", workNo);
                    continue; // 或者 throw new BaseException
                }

                try {
                    // 新增运输系统数据
                    etmsOpHeadPushService.pushData(orderEntity);
                    // 更新订单状态
                    orderEntity.setStage(OrderEnum.OrderStage.FF.getCode());
                    omsOrderDao.updateByEntityId(orderEntity);

                } catch (Exception e) {
                    log.error("运输系统数据分发失败", e);
                }
                // 给灵境推送数据
                try {
                    // 构造请求体
                    OrderEntity entity = buildOrderEntity(orderEntity, workItem, bgbjEntity);
                    OrderRequest orderRequest = new OrderRequest();
                    orderRequest.setEntity(entity);

                    // 发送请求
                    log.info("正在发送订单数据，orderNo={}, workNo={}", orderNo, workNo);
                    String response = apiClient.postOrder(targetUrl, orderRequest);
                    // 处理响应
                    handleResponse(response, orderEntity);
                } catch (Exception e) {
                    log.error("灵境系统数据分发失败", e);
                }

                // 操作日志记录
                OmsOrderLogEntity logEntity = new OmsOrderLogEntity();
                logEntity.setOrderNo(orderEntity.getOrderNo());
                logEntity.setOperName("订单分发");
                omsOrderLogService.writeLog(logEntity);

            }

            return orderEntity;

        } catch (BaseException e) {
            log.error("订单发送失败: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("未知错误导致订单发送失败", e);
            throw new BaseException("系统异常：" + e.getMessage());
        }
    }

    private OrderEntity buildOrderEntity(OmsOrderEntity orderEntity,
                                         OmsOrderFwxmWorkEntity workItem,
                                         OmsOrderFwxmBgbjEntity bgbjEntity) {
        OrderEntity entity = new OrderEntity();
        entity.setORDER_NO(orderEntity.getOrderNo());
        entity.setWORK_NO(workItem.getWorkNo());

        // 截取前三位作为服务项目 code
        entity.setFWXM_CODE(Zsr.String.safeSubstring(workItem.getFwxmCode(), 3));
        entity.setCUSTOMER_ORDER_NO(orderEntity.getCustomerOrderNo());

        // 根据进出口类型决定字段
        if ("I".equalsIgnoreCase(bgbjEntity.getiEType())) {
            entity.setJNSFHR(bgbjEntity.getJnshr());
        } else {
            entity.setJNSFHR(bgbjEntity.getJnfhr());
        }

        entity.setI_E_TYPE(bgbjEntity.getiEType());
        entity.setHDCS(bgbjEntity.getHdcs());
        entity.setBH_TYPE(bgbjEntity.getBhType());
        entity.setYSFS(bgbjEntity.getYsfs());
        entity.setBGD_CUSTOM_CODE(bgbjEntity.getBgdCustomCode());
        entity.setJD_NODE(orderEntity.getJdNode());
        entity.setJD_USER(orderEntity.getJdUser());
        entity.setCONFIRM_DATE(ZsrDateUtils.dateToString(orderEntity.getConfirmDate()));
        entity.setXDH(bgbjEntity.getXdh());
        entity.setQTCKH(bgbjEntity.getQtckh());
        entity.setBJ_TYPE(bgbjEntity.getBjType());
        entity.setI_E_PORT(bgbjEntity.getiEPort());
        entity.setCUSTOM_CODE(bgbjEntity.getCustomCode());
        entity.setOP_DATE(ZsrDateUtils.dateToString(orderEntity.getOpDate()));

        // 服务项目备注（可为空）
        OmsOrderFwxmEntity fwxmEntity = omsOrderFwxmDao.select()
                .eq(OmsOrderFwxmEntity::getOrderNo, orderEntity.getOrderNo())
                .eq(OmsOrderFwxmEntity::getFwxmCode, workItem.getFwxmCode())
                .eq(OmsOrderFwxmEntity::getGroupCode, UserContext.getUserInfo().getCompanyCode())
                .one();
        entity.setOTHER_MEMO(fwxmEntity != null ? fwxmEntity.getOtherMemo() : null);

        return entity;
    }

    private void handleResponse(String response, OmsOrderEntity orderEntity) throws BaseException {
        if (Zsr.String.IsNullOrWhiteSpace(response)) {
            throw new BaseException("分发数据失败：未获得对接方接口响应");
        }

        try {
            ZsrJson responseJson = ZsrJson.parse(response);
            boolean success = responseJson.getBoolean("success");
            int code = responseJson.getInt("code");

            if (success && code == 10001) {
                log.info("订单数据发送成功，响应：{}", response);

                // 更新订单状态
                orderEntity.setStage(OrderEnum.OrderStage.FF.getCode());
                omsOrderDao.updateByEntityId(orderEntity);


            } else {
                log.warn("订单数据发送失败，响应：{}", response);
                throw new BaseException("订单数据发送失败，服务器响应信息：" + response);
            }
        } catch (Exception e) {
            log.warn("服务器返回数据异常，不是有效的 JSON 字符串：{}", e.getMessage());
            throw new BaseException("分发数据失败：服务器返回数据异常，不是有效的 JSON 字符串");
        }
    }
}