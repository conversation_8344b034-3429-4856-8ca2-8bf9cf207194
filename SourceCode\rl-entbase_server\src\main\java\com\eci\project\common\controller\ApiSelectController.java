package com.eci.project.common.controller;

import com.alibaba.fastjson.JSON;
import com.eci.common.DataDictUtils;
import com.eci.common.Zsr;
import com.eci.common.ZsrJson;
import com.eci.common.util.ResponseMsg;
import com.eci.common.util.ResponseMsgUtil;
import com.eci.common.web.BllContext;
import com.eci.crud.controller.EciBaseController;
import com.eci.log.annotation.EciAction;
import com.eci.log.annotation.EciLog;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.crmCustomer.entity.CrmCustomerEntity;
import com.eci.project.crmCustomer.service.CrmCustomerService;
import com.eci.project.crmCustomerHead.entity.CrmCustomerHeadEntity;
import com.eci.project.crmCustomerHead.service.CrmCustomerHeadService;
import com.eci.project.crmCustomerHzfw.entity.CrmCustomerHzfwEntity;
import com.eci.project.crmCustomerHzfw.service.CrmCustomerHzfwService;
import com.eci.project.crmCustomerRole.entity.CrmCustomerRoleEntity;
import com.eci.project.crmCustomerRole.service.CrmCustomerRoleService;
import com.eci.sso.role.entity.UserContext;
import com.eci.wu.core.DataTable;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 业务伙伴Controller
 *
 * @<NAME_EMAIL>
 * @date 2025-04-29
 */
@Api(tags = "下拉框数据源")
@RestController
@RequestMapping("/apiSelect")
public class ApiSelectController extends EciBaseController {

    @Autowired
    private CrmCustomerService crmCustomerService;
    @Autowired
    private CrmCustomerHzfwService crmCustomerHzfwService;
    @Autowired
    private CrmCustomerHeadService crmCustomerHeadService;

    @Autowired
    private CrmCustomerRoleService crmCustomerRoleService;


    @ApiOperation("下拉框:查询列表")
    @EciLog(title = "下拉框:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody String jsonString) {
        ZsrJson zsrJson = ZsrJson.parse(jsonString);
        String dataType = zsrJson.check("dataType").getString("dataType");

        Map<String, DataTable> dataTable = DataDictUtils.queryKeyForDataTable(dataType);
        DataTable dataTableData = dataTable.get(dataType);
        if (dataTableData != null && dataTableData.rows.size() > 0) {
            return ResponseMsgUtil.success(10001, dataTableData.rows);
        } else {
            return ResponseMsgUtil.success(10001, null);
        }
    }


    @ApiOperation("业务伙伴:分页查询列表")
    @EciLog(title = "业务伙伴:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody CrmCustomerEntity entity) {
        TgPageInfo tgPageInfo = crmCustomerService.queryPageList(entity);
        return ResponseMsgUtil.successPlus(10001, tgPageInfo);
    }


}