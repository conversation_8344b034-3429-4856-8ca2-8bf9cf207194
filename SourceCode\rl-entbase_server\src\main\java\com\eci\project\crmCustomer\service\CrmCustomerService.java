package com.eci.project.crmCustomer.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eci.cache.config.TgCacheHelper;
import com.eci.common.db.DBHelper;
import com.eci.common.utils.CommonLib;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.crmCustomer.dao.CrmCustomerDao;
import com.eci.project.crmCustomer.entity.CrmCustomerEntity;
import com.eci.project.crmCustomer.validate.CrmCustomerVal;

import com.eci.sso.constant.RedisKey;
import com.eci.sso.role.entity.UserContext;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.cache.CacheProperties;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


/**
* 业务伙伴Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-29
*/
@Service
@Slf4j
public class CrmCustomerService implements EciBaseService<CrmCustomerEntity> {

    @Autowired
    private CrmCustomerDao crmCustomerDao;

    @Autowired
    private CrmCustomerVal crmCustomerVal;

    CommonLib cmn = CommonLib.getInstance();
    @Override
    public TgPageInfo queryPageList(CrmCustomerEntity entity) {
        EciQuery<CrmCustomerEntity> eciQuery = EciQuery.buildQuery(entity);
        eciQuery.select("(SELECT name FROM FZGJ_BASE_DATA_DETAIL WHERE GROUP_CODE='IS_CUSTOM' AND CODE=A.IS_CUSTOM) as isCustomName"
                ,"A.CODE,A.GUID,A.NAME,A.SHORT_NAME,A.EN_NAME,A.ROLE_NAME,A.COM_TYPE_NAME,A.HZFW_NAME_KH,A.CUSTOMER_GROUP_NAME," +
                        "A.hzfwNameGys,A.CL_DATE,A.CUSTOM_NO,A.COUNTRY,A.PROVINCE,A.CITY,A.DISTRICT,A.COMPANY_TYPE,A.IS_SSGS,A.STOCK_CODE,A.SK_DATELINE,A.PAY_DATELINE," +
                        "A.KP_CYCLE,A.KP_DATELINE,A.MEMO,A.CREATE_USER_NAME,A.CREATE_DATE,A.COMPANY_NAME,A.STATUS,A.MANAGE_STATUS,A.AUDIT_REMARK");
        if(entity.getRoleCode()!=null&&!entity.getRoleCode().isEmpty()){
            String[] roles= entity.getRoleCode().split(",");
            String sql="instr(A.ROLE_CODE,'%s')>0";
            List<String> sqlList=new ArrayList<>();
            Arrays.stream(roles).forEach(p->{
                sqlList.add(String.format(sql,p));
            });
            eciQuery.apply(String.format(" (%s)",String.join(" or ",sqlList)));
        }
        if(entity.getHzfwCodeKh()!=null&&!entity.getHzfwCodeKh().isEmpty()){
            String[] code= entity.getHzfwCodeKh().split(",");
            String sql="instr(A.HZFW_CODE_KH,'%s')>0";
            List<String> sqlList=new ArrayList<>();
            Arrays.stream(code).forEach(p->{
                sqlList.add(String.format(sql,p));
            });
            eciQuery.apply(String.format(" (%s)",String.join(" or ",sqlList)));
        }

        if(entity.getHzfwCodeGys()!=null&&!entity.getHzfwCodeGys().isEmpty()){
            String[] code= entity.getHzfwCodeGys().split(",");
            String sql="instr(A.HzfwCodeGys,'%s')>0";
            List<String> sqlList=new ArrayList<>();
            Arrays.stream(code).forEach(p->{
                sqlList.add(String.format(sql,p));
            });
            eciQuery.apply(String.format(" (%s)",String.join(" or ",sqlList)));
        }
        //List<CrmCustomerEntity> entities = crmCustomerDao.queryPageList(eciQuery);
        Integer pageSize = BllContext.getPaging().getPageSize();
        Integer pageNum = BllContext.getPaging().getPageNum();
        PageHelper.startPage(pageNum, pageSize == -1 ? 0 : pageSize);
        List<CrmCustomerEntity> entities = crmCustomerDao.selectList1(eciQuery
                ,UserContext.getUserInfo().getCompanyCode()
                ,UserContext.getUserInfo().getCompanyCode());
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public CrmCustomerEntity save(CrmCustomerEntity entity) {
        // 返回实体对象
        CrmCustomerEntity crmCustomerEntity = null;
        crmCustomerVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            crmCustomerEntity = crmCustomerDao.insertOne(entity);

        }else{

            crmCustomerEntity = crmCustomerDao.updateByEntityId(entity);

        }
        return crmCustomerEntity;
    }

    @Override
    public List<CrmCustomerEntity> selectList(CrmCustomerEntity entity) {
        return crmCustomerDao.selectList(entity);
    }

    @Override
    public CrmCustomerEntity selectOneById(Serializable id) {
        return crmCustomerDao.selectById(id);
    }


    @Override
    public void insertBatch(List<CrmCustomerEntity> list) {
        crmCustomerDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {

        return crmCustomerDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return crmCustomerDao.deleteById(id);
    }
    /**
     * <AUTHOR>
     * @Description 生成统一信用代码
     * @Date  2025/5/6 9:37
     * @Param []
     * @return java.lang.String
     **/
    public String AutoBuildCode(String guid){
        String template="AUTO%s";
        Long no= TgCacheHelper.incr("AutoBuildCode",1);//redis自增1
        String code = String.format(template,String.format("%014d",no));//补位
        if(tycodeExist(code,guid)) //信用代码存在则重新生成
            return AutoBuildCode(guid);
        return code;
    }
    /**
     * <AUTHOR>
     * @Description 验证统一信用代码是否存在
     * @Date  2025/5/6 9:40
     * @Param [code]
     * @return boolean
     **/
    public boolean tycodeExist(String code,String guid){
        QueryWrapper queryWrapper=new QueryWrapper();
        queryWrapper.eq("TY_CODE",code);
        if(guid!=null&&!guid.isEmpty())
            queryWrapper.ne("GUID",guid);
        return crmCustomerDao.exists(queryWrapper);
    }

    /**
     * <AUTHOR>
     * @Description 验证代码是否存在
     * @Date  2025/5/6 9:40
     * @Param [code]
     * @return boolean
     **/
    public boolean codeExist(String code,String guid){
        QueryWrapper queryWrapper=new QueryWrapper();
        queryWrapper.eq("CODE",code);
        if(guid!=null&&!guid.isEmpty())
            queryWrapper.ne("GUID",guid);
        return crmCustomerDao.exists(queryWrapper);
    }
    /**
     * <AUTHOR>
     * @Description 验证数据是否存在
     * @Date  2025/5/8 14:12
     * @Param [value, guid, type]
     * @return boolean
     **/
    public boolean Validate(String value,String guid,String type){
        QueryWrapper queryWrapper=new QueryWrapper();
        if(guid!=null&&!guid.isEmpty())
            queryWrapper.ne("GUID",guid);
        queryWrapper.eq("GROUP_CODE", UserContext.getUserInfo().getCompanyCode());
        switch (type){
            case "name":
                queryWrapper.eq("NAME",value);
                break;
            case "shortName":
                queryWrapper.eq("SHORT_NAME",value);
                break;
            case "code":
                queryWrapper.eq("CODE",value);
                break;
            case "tyCode":
                queryWrapper.eq("TY_CODE",value);
                break;
            case "customNo":
                queryWrapper.eq("CUSTOM_NO",value);
                break;
            case "customer_b2b":
                queryWrapper.eq("CUSTOMER_B2B",value);
                break;
        }
        return crmCustomerDao.exists(queryWrapper);
    }


    public void SendAudit(String ids){
        String sqlInClause= Arrays.stream(ids.split(",")).map(value->"'"+value+"'")
                .collect(Collectors.joining(","));
        String sql=String.format("Update CRM_CUSTOMER set MANAGE_STATUS=1 WHERE GUID IN (%s)"
                ,sqlInClause);
        DBHelper.execute(sql);
    }
    /**
     * <AUTHOR>
     * @Description 审批退回
     * @Date  2025/4/28 14:19
     * @Param [ids]
     * @return void
     **/
    public void AuditBack(String ids,String remark){
        String sqlInClause= Arrays.stream(ids.split(",")).map(value->"'"+value+"'")
                .collect(Collectors.joining(","));
        String sql=String.format("Update CRM_CUSTOMER set MANAGE_STATUS=-1,AUDIT_REMARK=%s WHERE GUID IN (%s)"
                ,cmn.SQLQ(remark),sqlInClause);
        DBHelper.execute(sql);
    }

    /**
     * <AUTHOR>
     * @Description 审批通过
     * @Date  2025/4/28 14:19
     * @Param [ids]
     * @return void
     **/
    public void AuditSuccess(String ids){
        String sqlInClause= Arrays.stream(ids.split(",")).map(value->"'"+value+"'")
                .collect(Collectors.joining(","));
        String sql=String.format("Update CRM_CUSTOMER set MANAGE_STATUS=2 WHERE GUID IN (%s)"
                ,sqlInClause);
        DBHelper.execute(sql);
    }
}