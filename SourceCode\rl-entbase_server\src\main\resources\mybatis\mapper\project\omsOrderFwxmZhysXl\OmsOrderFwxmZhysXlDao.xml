<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.omsOrderFwxmZhysXl.dao.OmsOrderFwxmZhysXlDao">
    <resultMap type="OmsOrderFwxmZhysXlEntity" id="OmsOrderFwxmZhysXlResult">
        <result property="lineNo" column="LINE_NO"/>
        <result property="orderNo" column="ORDER_NO"/>
        <result property="lineSeq" column="LINE_SEQ"/>
        <result property="zhysGuid" column="ZHYS_GUID"/>
        <result property="isWb" column="IS_WB"/>
        <result property="dcysxl" column="DCYSXL"/>
        <result property="gys" column="GYS"/>
        <result property="nbzyzz" column="NBZYZZ"/>
        <result property="xzrwbh" column="XZRWBH"/>
        <result property="nationalidCounty" column="NATIONALID_COUNTY"/>
        <result property="nationalidProvince" column="NATIONALID_PROVINCE"/>
        <result property="nationalidCity" column="NATIONALID_CITY"/>
        <result property="nationalidRegion" column="NATIONALID_REGION"/>
        <result property="nationalidTown" column="NATIONALID_TOWN"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="terminusidCountry" column="TERMINUSID_COUNTRY"/>
        <result property="terminusidCity" column="TERMINUSID_CITY"/>
        <result property="terminusidProvince" column="TERMINUSID_PROVINCE"/>
        <result property="terminusidRegion" column="TERMINUSID_REGION"/>
        <result property="terminusidTown" column="TERMINUSID_TOWN"/>
        <result property="seqNo" column="SEQ_NO"/>
    </resultMap>

    <sql id="selectOmsOrderFwxmZhysXlEntityVo">
        select
            LINE_NO,
            ORDER_NO,
            LINE_SEQ,
            SEQ_NO,
            ZHYS_GUID,
            IS_WB,
            DCYSXL,
            GYS,
            NBZYZZ,
            XZRWBH,
            NATIONALID_COUNTY,
            NATIONALID_PROVINCE,
            NATIONALID_CITY,
            NATIONALID_REGION,
            NATIONALID_TOWN,
            CREATE_USER,
            CREATE_USER_NAME,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_USER_NAME,
            UPDATE_DATE,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            TERMINUSID_COUNTRY,
            TERMINUSID_CITY,
            TERMINUSID_PROVINCE,
            TERMINUSID_REGION,
            TERMINUSID_TOWN
        from OMS_ORDER_FWXM_ZHYS_XL
    </sql>
</mapper>