package com.eci.project.fzgjBdOpType.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.fzgjBdOpType.entity.FzgjBdOpTypeEntity;


/**
* 业务类型Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-03-27
*/
public interface FzgjBdOpTypeDao extends EciBaseDao<FzgjBdOpTypeEntity> {

}