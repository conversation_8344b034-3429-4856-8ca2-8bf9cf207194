package com.eci.project.fzgjBdTruckSpecCom.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.db.DBHelper;
import com.eci.common.util.DateUtils;
import com.eci.common.web.BllContext;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjBdTruckSpecCom.dao.FzgjBdTruckSpecComDao;
import com.eci.project.fzgjBdTruckSpecCom.entity.FzgjBdTruckSpecComEntity;
import com.eci.project.fzgjBdTruckSpecCom.service.IFzgjBdTruckSpecComService;
import com.eci.project.fzgjBdTruckSpecCom.validate.FzgjBdTruckSpecComVal;

import com.eci.sso.role.entity.UserContext;
import dm.jdbc.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 计费车辆尺寸Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-03-25
*/
@Service
@Slf4j
public class FzgjBdTruckSpecComServiceImpl implements IFzgjBdTruckSpecComService
{
    @Autowired
    private FzgjBdTruckSpecComDao fzgjBdTruckSpecComDao;

    @Autowired
    private FzgjBdTruckSpecComVal fzgjBdTruckSpecComVal;


    @Override
    public TgPageInfo queryPageList(FzgjBdTruckSpecComEntity entity) {
        EciQuery<FzgjBdTruckSpecComEntity> eciQuery = EciQuery.doQuery(entity);
        List<FzgjBdTruckSpecComEntity> entities = fzgjBdTruckSpecComDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjBdTruckSpecComEntity save(FzgjBdTruckSpecComEntity entity) {
        // 返回实体对象
        FzgjBdTruckSpecComEntity fzgjBdTruckSpecComEntity = null;
        fzgjBdTruckSpecComVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setGuid(IdWorker.get32UUID());
            entity.setCreateDate(DateUtils.getNowDate());
            entity.setCreateUser(UserContext.getUserInfo().getUserId());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            entity.setCompanyName(UserContext.getUserInfo().getCompanyName());
            entity.setNodeCode(UserContext.getUserInfo().getDeptCode());
            entity.setNodeName(UserContext.getUserInfo().getDeptName());
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
            fzgjBdTruckSpecComEntity = fzgjBdTruckSpecComDao.insertOne(entity);
        }else{
            entity.setUpdateDate(DateUtils.getNowDate());
            entity.setUpdateUser(UserContext.getUserInfo().getUserId());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            fzgjBdTruckSpecComEntity = fzgjBdTruckSpecComDao.updateByEntityId(entity);
        }
        return fzgjBdTruckSpecComEntity;
    }

    @Override
    public List<FzgjBdTruckSpecComEntity> selectList(FzgjBdTruckSpecComEntity entity) {
        return fzgjBdTruckSpecComDao.selectList(entity);
    }

    @Override
    public FzgjBdTruckSpecComEntity selectOneById(Serializable id) {
        return fzgjBdTruckSpecComDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjBdTruckSpecComEntity> list) {
        fzgjBdTruckSpecComDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjBdTruckSpecComDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjBdTruckSpecComDao.deleteById(id);
    }

}