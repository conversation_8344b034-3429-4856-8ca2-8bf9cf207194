package com.eci.project.fzgjBdServiceItemPt.entity;

import com.eci.project.fzgjBdServiceItemPagesPt.entity.FzgjBdServiceItemPagesPtEntity;
import lombok.Data;

import java.util.List;

/**
 * <Description>TODO <br>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/1$
 */
@Data
public class ServiceItemSaveDTO {
    public FzgjBdServiceItemPtEntity entity;

    public List<FzgjBdServiceItemPagesPtEntity>  delList;
    public List<FzgjBdServiceItemPagesPtEntity>  addList;
}
