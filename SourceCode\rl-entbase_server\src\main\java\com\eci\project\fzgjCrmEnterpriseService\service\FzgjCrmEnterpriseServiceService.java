package com.eci.project.fzgjCrmEnterpriseService.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjCrmEnterpriseService.dao.FzgjCrmEnterpriseServiceDao;
import com.eci.project.fzgjCrmEnterpriseService.entity.FzgjCrmEnterpriseServiceEntity;
import com.eci.project.fzgjCrmEnterpriseService.validate.FzgjCrmEnterpriseServiceVal;

import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 注册企业服务Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-03-13
*/
@Service
@Slf4j
public class FzgjCrmEnterpriseServiceService implements EciBaseService<FzgjCrmEnterpriseServiceEntity> {

    @Autowired
    private FzgjCrmEnterpriseServiceDao fzgjCrmEnterpriseServiceDao;

    @Autowired
    private FzgjCrmEnterpriseServiceVal fzgjCrmEnterpriseServiceVal;


    @Override
    public TgPageInfo queryPageList(FzgjCrmEnterpriseServiceEntity entity) {
        EciQuery<FzgjCrmEnterpriseServiceEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjCrmEnterpriseServiceEntity> entities = fzgjCrmEnterpriseServiceDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjCrmEnterpriseServiceEntity save(FzgjCrmEnterpriseServiceEntity entity) {
        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setCreateDate(new java.util.Date());
            entity.setUpdateDate(new java.util.Date());
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        } else {
            entity.setUpdateDate(new java.util.Date());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        }
        // 返回实体对象
        FzgjCrmEnterpriseServiceEntity fzgjCrmEnterpriseServiceEntity = null;
        fzgjCrmEnterpriseServiceVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjCrmEnterpriseServiceEntity = fzgjCrmEnterpriseServiceDao.insertOne(entity);

        }else{

            fzgjCrmEnterpriseServiceEntity = fzgjCrmEnterpriseServiceDao.updateByEntityId(entity);

        }
        return fzgjCrmEnterpriseServiceEntity;
    }

    @Override
    public List<FzgjCrmEnterpriseServiceEntity> selectList(FzgjCrmEnterpriseServiceEntity entity) {
        return fzgjCrmEnterpriseServiceDao.selectList(entity);
    }

    @Override
    public FzgjCrmEnterpriseServiceEntity selectOneById(Serializable id) {
        return fzgjCrmEnterpriseServiceDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjCrmEnterpriseServiceEntity> list) {
        fzgjCrmEnterpriseServiceDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjCrmEnterpriseServiceDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjCrmEnterpriseServiceDao.deleteById(id);
    }

}