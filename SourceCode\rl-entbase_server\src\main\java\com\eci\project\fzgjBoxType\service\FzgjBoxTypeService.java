package com.eci.project.fzgjBoxType.service;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjBoxType.dao.FzgjBoxTypeDao;
import com.eci.project.fzgjBoxType.entity.FzgjBoxTypeEntity;
import com.eci.project.fzgjBoxType.validate.FzgjBoxTypeVal;

import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
* 集装箱类型Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-17
*/
@Service
@Slf4j
public class FzgjBoxTypeService implements EciBaseService<FzgjBoxTypeEntity> {

    @Autowired
    private FzgjBoxTypeDao fzgjBoxTypeDao;

    @Autowired
    private FzgjBoxTypeVal fzgjBoxTypeVal;


    @Override
    public TgPageInfo queryPageList(FzgjBoxTypeEntity entity) {
        EciQuery<FzgjBoxTypeEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjBoxTypeEntity> entities = fzgjBoxTypeDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjBoxTypeEntity save(FzgjBoxTypeEntity entity) {
        // 返回实体对象
        FzgjBoxTypeEntity fzgjBoxTypeEntity = null;
        fzgjBoxTypeVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setGuid(IdWorker.getIdStr());
            entity.setCreateDate(new Date());
            entity.setCreateUser(UserContext.getUserInfo().getUserId());
            entity.setCreateUserName(UserContext.getUserInfo().getUserNickname());
            entity.setUpdateDate(new Date());
            entity.setUpdateUser(UserContext.getUserInfo().getUserId());
            entity.setUpdateUserName(UserContext.getUserInfo().getUserNickname());
            entity.setNodeCode(UserContext.getUserInfo().getCompanyCode());
            entity.setNodeName(UserContext.getUserInfo().getCompanyName());
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
            entity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            entity.setCompanyName(UserContext.getUserInfo().getCompanyName());
            fzgjBoxTypeEntity = fzgjBoxTypeDao.insertOne(entity);

        }else{
            entity.setUpdateDate(new Date());
            entity.setUpdateUser(UserContext.getUserInfo().getUserId());
            entity.setUpdateUserName(UserContext.getUserInfo().getUserNickname());
            entity.setNodeCode(UserContext.getUserInfo().getCompanyCode());
            entity.setNodeName(UserContext.getUserInfo().getCompanyName());
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
            entity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            entity.setCompanyName(UserContext.getUserInfo().getCompanyName());
            fzgjBoxTypeEntity = fzgjBoxTypeDao.updateByEntityId(entity);

        }
        return fzgjBoxTypeEntity;
    }

    @Override
    public List<FzgjBoxTypeEntity> selectList(FzgjBoxTypeEntity entity) {
        return fzgjBoxTypeDao.selectList(entity);
    }

    @Override
    public FzgjBoxTypeEntity selectOneById(Serializable id) {
        return fzgjBoxTypeDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjBoxTypeEntity> list) {
        fzgjBoxTypeDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjBoxTypeDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjBoxTypeDao.deleteById(id);
    }

}