package com.eci.project.omsReceiveHistory.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.omsReceiveHistory.entity.OmsReceiveHistoryEntity;

import org.springframework.stereotype.Service;


/**
* 报文接收记录Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-06-04
*/
@Service
public class OmsReceiveHistoryVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(OmsReceiveHistoryEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(OmsReceiveHistoryEntity entity, BusinessType businessType) {

    }

}
