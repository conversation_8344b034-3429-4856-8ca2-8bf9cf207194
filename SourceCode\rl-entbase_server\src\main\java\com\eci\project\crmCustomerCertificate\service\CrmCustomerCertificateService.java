package com.eci.project.crmCustomerCertificate.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.crmCustomerCertificate.dao.CrmCustomerCertificateDao;
import com.eci.project.crmCustomerCertificate.entity.CrmCustomerCertificateEntity;
import com.eci.project.crmCustomerCertificate.validate.CrmCustomerCertificateVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 司机证件管理Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-05-26
*/
@Service
@Slf4j
public class CrmCustomerCertificateService implements EciBaseService<CrmCustomerCertificateEntity> {

    @Autowired
    private CrmCustomerCertificateDao crmCustomerCertificateDao;

    @Autowired
    private CrmCustomerCertificateVal crmCustomerCertificateVal;


    @Override
    public TgPageInfo queryPageList(CrmCustomerCertificateEntity entity) {
        EciQuery<CrmCustomerCertificateEntity> eciQuery = EciQuery.buildQuery(entity);
        List<CrmCustomerCertificateEntity> entities = crmCustomerCertificateDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public CrmCustomerCertificateEntity save(CrmCustomerCertificateEntity entity) {
        // 返回实体对象
        CrmCustomerCertificateEntity crmCustomerCertificateEntity = null;
        crmCustomerCertificateVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            crmCustomerCertificateEntity = crmCustomerCertificateDao.insertOne(entity);

        }else{

            crmCustomerCertificateEntity = crmCustomerCertificateDao.updateByEntityId(entity);

        }
        return crmCustomerCertificateEntity;
    }

    @Override
    public List<CrmCustomerCertificateEntity> selectList(CrmCustomerCertificateEntity entity) {
        return crmCustomerCertificateDao.selectList(entity);
    }

    @Override
    public CrmCustomerCertificateEntity selectOneById(Serializable id) {
        return crmCustomerCertificateDao.selectById(id);
    }


    @Override
    public void insertBatch(List<CrmCustomerCertificateEntity> list) {
        crmCustomerCertificateDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return crmCustomerCertificateDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return crmCustomerCertificateDao.deleteById(id);
    }

}