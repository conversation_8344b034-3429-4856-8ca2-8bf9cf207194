package com.eci.project.etmsBdDriverInsurance.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.etmsBdDriverInsurance.entity.EtmsBdDriverInsuranceEntity;

import com.eci.sso.role.entity.UserContext;
import org.springframework.stereotype.Service;

import java.util.Date;


/**
* 司机保险管理Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-27
*/
@Service
public class EtmsBdDriverInsuranceVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(EtmsBdDriverInsuranceEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(EtmsBdDriverInsuranceEntity entity, BusinessType businessType) {
        entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        entity.setUpdateDate(new Date());
        entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
        if(businessType==BusinessType.INSERT){
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateDate(new Date());
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
            entity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            entity.setCompanyName(UserContext.getUserInfo().getCompanyName());
        }
    }

}
