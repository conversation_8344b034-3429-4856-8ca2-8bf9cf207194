<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.omsOrderFwxmWorkFkCar.dao.OmsOrderFwxmWorkFkCarDao">
    <resultMap type="OmsOrderFwxmWorkFkCarEntity" id="OmsOrderFwxmWorkFkCarResult">
        <result property="guid" column="GUID"/>
        <result property="orderNo" column="ORDER_NO"/>
        <result property="xzwtNo" column="XZWT_NO"/>
        <result property="workNo" column="WORK_NO"/>
        <result property="fwxmCode" column="FWXM_CODE"/>
        <result property="carNo" column="CAR_NO"/>
        <result property="carType" column="CAR_TYPE"/>
        <result property="carSize" column="CAR_SIZE"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="takeDate" column="TAKE_DATE"/>
        <result property="returnDate" column="RETURN_DATE"/>
        <result property="gpsNo" column="GPS_NO"/>
        <result property="sysCode" column="SYS_CODE"/>
        <result property="pcdNo" column="PCD_NO"/>
        <result property="trailerNo" column="TRAILER_NO"/>
        <result property="driverName" column="DRIVER_NAME"/>
        <result property="tel" column="TEL"/>
        <result property="idCard" column="ID_CARD"/>
        <result property="weight" column="WEIGHT"/>
        <result property="boxNo" column="BOX_NO"/>
        <result property="bizRegId" column="BIZ_REG_ID"/>
        <result property="carColor" column="CAR_COLOR"/>
        <result property="lineNum" column="LINE_NUM"/>
    </resultMap>

    <sql id="selectOmsOrderFwxmWorkFkCarEntityVo">
        select
            GUID,
            ORDER_NO,
            XZWT_NO,
            WORK_NO,
            FWXM_CODE,
            CAR_NO,
            CAR_TYPE,
            CAR_SIZE,
            CREATE_USER,
            CREATE_USER_NAME,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_USER_NAME,
            UPDATE_DATE,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            TAKE_DATE,
            RETURN_DATE,
            GPS_NO,
            SYS_CODE,
            PCD_NO,
            TRAILER_NO,
            DRIVER_NAME,
            TEL,
            ID_CARD,
            WEIGHT,
            BOX_NO,
            BIZ_REG_ID,
            CAR_COLOR,
            LINE_NUM
        from OMS_ORDER_FWXM_WORK_FK_CAR
    </sql>
</mapper>