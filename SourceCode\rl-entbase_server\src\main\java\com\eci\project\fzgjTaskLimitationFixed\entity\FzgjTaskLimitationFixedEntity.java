package com.eci.project.fzgjTaskLimitationFixed.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 作业环节及时效标准条件对象 FZGJ_TASK_LIMITATION_FIXED
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-05-22
*/
@ApiModel("作业环节及时效标准条件")
@TableName("FZGJ_TASK_LIMITATION_FIXED")
@FieldNameConstants
public class FzgjTaskLimitationFixedEntity extends EciBaseEntity{
    /**
    * GUID
    */
    @ApiModelProperty("GUID(36)")
    @TableId("GUID")
    private String guid;

    /**
    * LIMITATION.GUID
    */
    @ApiModelProperty("LIMITATION.GUID(50)")
    @TableField("LIMITATION_GUID")
    private String limitationGuid;

    /**
    * 行号
    */
    @ApiModelProperty("行号(20)")
    @TableField("LINE_NO")
    private String lineNo;

    /**
    * 元素（条件名）
    */
    @ApiModelProperty("元素（条件名）(20)")
    @TableField("ELEMENT_CODE")
    private String elementCode;

    /**
    * ELEMENT_CODE_NAME
    */
    @ApiModelProperty("ELEMENT_CODE_NAME(50)")
    @TableField("ELEMENT_CODE_NAME")
    private String elementCodeName;

    /**
    * 运算符
    */
    @ApiModelProperty("运算符(20)")
    @TableField("LOGICAL")
    private String logical;

    /**
    * 元素设定值（锁定范围）
    */
    @ApiModelProperty("元素设定值（锁定范围）(500)")
    @TableField("ELEMENT_VALUE")
    private String elementValue;

    /**
    * ELEMENT_VALUE_NAME
    */
    @ApiModelProperty("ELEMENT_VALUE_NAME(50)")
    @TableField("ELEMENT_VALUE_NAME")
    private String elementValueName;

    /**
    * 备注
    */
    @ApiModelProperty("备注(500)")
    @TableField("MEMO")
    private String memo;

    /**
    * 顺序
    */
    @ApiModelProperty("顺序(22)")
    @TableField("SEQ")
    private Integer seq;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 创建人名称
    */
    @ApiModelProperty("创建人名称(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 创建时间
    */
    @ApiModelProperty("创建时间(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建时间开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建时间结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 修改人
    */
    @ApiModelProperty("修改人(50)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 修改人名称
    */
    @ApiModelProperty("修改人名称(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 修改时间
    */
    @ApiModelProperty("修改时间(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("修改时间开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("修改时间结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 组织代码
    */
    @ApiModelProperty("组织代码(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 组织名称
    */
    @ApiModelProperty("组织名称(200)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 公司代码
    */
    @ApiModelProperty("公司代码(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 公司名称
    */
    @ApiModelProperty("公司名称(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 集团代码
    */
    @ApiModelProperty("集团代码(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 集团名称
    */
    @ApiModelProperty("集团名称(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * LIMITATION_TIME.GUID
    */
    @ApiModelProperty("LIMITATION_TIME.GUID(50)")
    @TableField("LIMITATION_TIME_GUID")
    private String limitationTimeGuid;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public FzgjTaskLimitationFixedEntity() {
        this.setSubClazz(FzgjTaskLimitationFixedEntity.class);
    }

    public FzgjTaskLimitationFixedEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public FzgjTaskLimitationFixedEntity setLimitationGuid(String limitationGuid) {
        this.limitationGuid = limitationGuid;
        this.nodifySetFiled("limitationGuid", limitationGuid);
        return this;
    }

    public String getLimitationGuid() {
        this.nodifyGetFiled("limitationGuid");
        return limitationGuid;
    }

    public FzgjTaskLimitationFixedEntity setLineNo(String lineNo) {
        this.lineNo = lineNo;
        this.nodifySetFiled("lineNo", lineNo);
        return this;
    }

    public String getLineNo() {
        this.nodifyGetFiled("lineNo");
        return lineNo;
    }

    public FzgjTaskLimitationFixedEntity setElementCode(String elementCode) {
        this.elementCode = elementCode;
        this.nodifySetFiled("elementCode", elementCode);
        return this;
    }

    public String getElementCode() {
        this.nodifyGetFiled("elementCode");
        return elementCode;
    }

    public FzgjTaskLimitationFixedEntity setElementCodeName(String elementCodeName) {
        this.elementCodeName = elementCodeName;
        this.nodifySetFiled("elementCodeName", elementCodeName);
        return this;
    }

    public String getElementCodeName() {
        this.nodifyGetFiled("elementCodeName");
        return elementCodeName;
    }

    public FzgjTaskLimitationFixedEntity setLogical(String logical) {
        this.logical = logical;
        this.nodifySetFiled("logical", logical);
        return this;
    }

    public String getLogical() {
        this.nodifyGetFiled("logical");
        return logical;
    }

    public FzgjTaskLimitationFixedEntity setElementValue(String elementValue) {
        this.elementValue = elementValue;
        this.nodifySetFiled("elementValue", elementValue);
        return this;
    }

    public String getElementValue() {
        this.nodifyGetFiled("elementValue");
        return elementValue;
    }

    public FzgjTaskLimitationFixedEntity setElementValueName(String elementValueName) {
        this.elementValueName = elementValueName;
        this.nodifySetFiled("elementValueName", elementValueName);
        return this;
    }

    public String getElementValueName() {
        this.nodifyGetFiled("elementValueName");
        return elementValueName;
    }

    public FzgjTaskLimitationFixedEntity setMemo(String memo) {
        this.memo = memo;
        this.nodifySetFiled("memo", memo);
        return this;
    }

    public String getMemo() {
        this.nodifyGetFiled("memo");
        return memo;
    }

    public FzgjTaskLimitationFixedEntity setSeq(Integer seq) {
        this.seq = seq;
        this.nodifySetFiled("seq", seq);
        return this;
    }

    public Integer getSeq() {
        this.nodifyGetFiled("seq");
        return seq;
    }

    public FzgjTaskLimitationFixedEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public FzgjTaskLimitationFixedEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public FzgjTaskLimitationFixedEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public FzgjTaskLimitationFixedEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public FzgjTaskLimitationFixedEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public FzgjTaskLimitationFixedEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public FzgjTaskLimitationFixedEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public FzgjTaskLimitationFixedEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public FzgjTaskLimitationFixedEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public FzgjTaskLimitationFixedEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public FzgjTaskLimitationFixedEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public FzgjTaskLimitationFixedEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public FzgjTaskLimitationFixedEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public FzgjTaskLimitationFixedEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public FzgjTaskLimitationFixedEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public FzgjTaskLimitationFixedEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public FzgjTaskLimitationFixedEntity setLimitationTimeGuid(String limitationTimeGuid) {
        this.limitationTimeGuid = limitationTimeGuid;
        this.nodifySetFiled("limitationTimeGuid", limitationTimeGuid);
        return this;
    }

    public String getLimitationTimeGuid() {
        this.nodifyGetFiled("limitationTimeGuid");
        return limitationTimeGuid;
    }

}
