<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.fzgjBdHx.dao.FzgjBdHxDao">
    <resultMap type="FzgjBdHxEntity" id="FzgjBdHxResult">
        <result property="guid" column="GUID"/>
        <result property="code" column="CODE"/>
        <result property="name" column="NAME"/>
        <result property="enName" column="EN_NAME"/>
        <result property="status" column="STATUS"/>
        <result property="seq" column="SEQ"/>
        <result property="memo" column="MEMO"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
    </resultMap>

    <sql id="selectFzgjBdHxEntityVo">
        select
            GUID,
            CODE,
            NAME,
            EN_NAME,
            STATUS,
            SEQ,
            MEMO,
            CREATE_DATE,
            CREATE_USER,
            CREATE_USER_NAME,
            UPDATE_DATE,
            UPDATE_USER,
            UPDATE_USER_NAME
        from FZGJ_BD_HX
    </sql>
</mapper>