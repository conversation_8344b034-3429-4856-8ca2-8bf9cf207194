package com.eci.project.omsOrderFwxmWorkFkCar.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;

/**
 * @ClassName: OmsOrderFwxmWorkFkCarExdEntity
 * @Author: guangyan.mei
 * @Date: 2025/6/6 15:29
 * @Description: TODO
 */
public class OmsOrderFwxmWorkFkCarExdEntity extends OmsOrderFwxmWorkFkCarEntity {

    public Integer getTrainQty() {
        return trainQty;
    }

    public void setTrainQty(Integer trainQty) {
        this.trainQty = trainQty;
    }

    /**
     * 需求车次数
     */
    @ApiModelProperty("需求车次数(22)")
    @TableField("TRAIN_QTY")
    private Integer trainQty;



}
