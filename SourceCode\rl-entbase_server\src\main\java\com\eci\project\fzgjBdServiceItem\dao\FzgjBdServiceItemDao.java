package com.eci.project.fzgjBdServiceItem.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.eci.project.fzgjBdServiceItemPages.entity.FzgjBdServiceItemPagesEntity;
import com.eci.project.fzgjBdServiceItemPt.entity.TreeModel;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.fzgjBdServiceItem.entity.FzgjBdServiceItemEntity;
import org.apache.ibatis.annotations.Select;

import java.util.List;


/**
* 服务项目Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-04-07
*/
public interface FzgjBdServiceItemDao extends EciBaseDao<FzgjBdServiceItemEntity> {
    List<TreeModel> selectTree(@Param(Constants.WRAPPER) Wrapper queryWrapper);
    List<String> selectCheckedNodes(@Param(Constants.WRAPPER) Wrapper queryWrapper);

    @Select("select P.* from fzgj_bd_service_item A inner join fzgj_bd_service_item_PT B on A.code=b.code\n" +
            "left join fzgj_bd_service_item_pages P on p.service_item_code=B.guid")
    List<FzgjBdServiceItemPagesEntity >  getServiceItemPages();


}