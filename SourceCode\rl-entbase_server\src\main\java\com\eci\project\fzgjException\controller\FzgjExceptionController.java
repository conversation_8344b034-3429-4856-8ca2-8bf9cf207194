package com.eci.project.fzgjException.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjException.entity.ReqFzgjExceptionPageEntity;
import com.eci.project.fzgjException.service.FzgjExceptionService;
import com.eci.project.fzgjException.entity.FzgjExceptionEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 订单作业异常Controller
*
* @<NAME_EMAIL>
* @date 2025-04-25
*/
@Api(tags = "订单作业异常")
@RestController
@RequestMapping("/fzgjException")
public class FzgjExceptionController extends EciBaseController {

    @Autowired
    private FzgjExceptionService fzgjExceptionService;


    @ApiOperation("订单作业异常:保存")
    @EciLog(title = "订单作业异常:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody FzgjExceptionEntity entity){
        FzgjExceptionEntity fzgjExceptionEntity =fzgjExceptionService.save(entity);
        return ResponseMsgUtil.success(10001,fzgjExceptionEntity);
    }


    @ApiOperation("订单作业异常:查询列表")
    @EciLog(title = "订单作业异常:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjExceptionEntity entity){
        List<FzgjExceptionEntity> fzgjExceptionEntities = fzgjExceptionService.selectList(entity);
        return ResponseMsgUtil.success(10001,fzgjExceptionEntities);
    }


    @ApiOperation("订单作业异常:分页查询列表")
    @EciLog(title = "订单作业异常:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody ReqFzgjExceptionPageEntity entity){
        TgPageInfo tgPageInfo = fzgjExceptionService.selectExceptionPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("订单作业异常:根据ID查一条")
    @EciLog(title = "订单作业异常:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjExceptionEntity entity){
        FzgjExceptionEntity  fzgjExceptionEntity = fzgjExceptionService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,fzgjExceptionEntity);
    }


    @ApiOperation("订单作业异常:根据ID删除一条")
    @EciLog(title = "订单作业异常:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjExceptionEntity entity){
        int count = fzgjExceptionService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("订单作业异常:根据ID字符串删除多条")
    @EciLog(title = "订单作业异常:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjExceptionEntity entity) {
        int count = fzgjExceptionService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}