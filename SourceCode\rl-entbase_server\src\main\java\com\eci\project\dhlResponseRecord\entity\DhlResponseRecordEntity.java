package com.eci.project.dhlResponseRecord.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;

import java.math.BigDecimal;
import java.util.Date;


/**
* 反馈DHL跟踪对象 DHL_RESPONSE_RECORD
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-04-22
*/
@ApiModel("反馈DHL跟踪")
@TableName("DHL_RESPONSE_RECORD")
@FieldNameConstants
public class DhlResponseRecordEntity extends EciBaseEntity{
    /**
    * 主键
    */
    @ApiModelProperty("主键(36)")
    @TableId("GUID")
    private String guid;

    /**
    * 状态
    */
    @ApiModelProperty("状态(5)")
    @TableField("STATE")
    private String state;

    /**
    * 回执内容
    */
    @ApiModelProperty("回执内容(4,000)")
    @TableField("MSG")
    private String msg;

    /**
    * request.guid
    */
    @ApiModelProperty("request.guid(50)")
    @TableField("REQUEST_GUID")
    private String requestGuid;

    /**
    * 是否发送成功
    */
    @ApiModelProperty("是否发送成功(1)")
    @TableField("IS_SUCESS_SEND")
    private String isSucessSend;

    /**
    * 对方是否接收成功
    */
    @ApiModelProperty("对方是否接收成功(1)")
    @TableField("IS_SUCESS_RECEIVE")
    private String isSucessReceive;

    /**
    * 发送的方式:人工RG,自动ZD
    */
    @ApiModelProperty("发送的方式:人工RG,自动ZD(10)")
    @TableField("FSFS")
    private String fsfs;

    /**
    * 发送时间
    */
    @ApiModelProperty("发送时间(7)")
    @TableField("OP_DATE")
    private Date opDate;

    @ApiModelProperty("发送时间开始")
    @TableField(exist=false)
    private Date opDateStart;

    @ApiModelProperty("发送时间结束")
    @TableField(exist=false)
    private Date opDateEnd;

    /**
    * 失败回执内容
    */
    @ApiModelProperty("失败回执内容(4,000)")
    @TableField("MEMO")
    private String memo;

    /**
    * 发送次数
    */
    @ApiModelProperty("发送次数(22)")
    @TableField("SEND_NUM")
    private BigDecimal sendNum;

    /**
    * 发送ETF标记
    */
    @ApiModelProperty("发送ETF标记(1)")
    @TableField("SEND_FLAG")
    private String sendFlag;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 最后修改日期
    */
    @ApiModelProperty("最后修改日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("最后修改日期开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("最后修改日期结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(200)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public DhlResponseRecordEntity setGuid(String guid) {
        this.guid = guid;
        return this;
    }

    public String getGuid() {
        return guid;
    }

    public DhlResponseRecordEntity setState(String state) {
        this.state = state;
        return this;
    }

    public String getState() {
        return state;
    }

    public DhlResponseRecordEntity setMsg(String msg) {
        this.msg = msg;
        return this;
    }

    public String getMsg() {
        return msg;
    }

    public DhlResponseRecordEntity setRequestGuid(String requestGuid) {
        this.requestGuid = requestGuid;
        return this;
    }

    public String getRequestGuid() {
        return requestGuid;
    }

    public DhlResponseRecordEntity setIsSucessSend(String isSucessSend) {
        this.isSucessSend = isSucessSend;
        return this;
    }

    public String getIsSucessSend() {
        return isSucessSend;
    }

    public DhlResponseRecordEntity setIsSucessReceive(String isSucessReceive) {
        this.isSucessReceive = isSucessReceive;
        return this;
    }

    public String getIsSucessReceive() {
        return isSucessReceive;
    }

    public DhlResponseRecordEntity setFsfs(String fsfs) {
        this.fsfs = fsfs;
        return this;
    }

    public String getFsfs() {
        return fsfs;
    }

    public DhlResponseRecordEntity setOpDate(Date opDate) {
        this.opDate = opDate;
        return this;
    }

    public Date getOpDate() {
        return opDate;
    }

    public DhlResponseRecordEntity setOpDateStart(Date opDateStart) {
        this.opDateStart = opDateStart;
        return this;
    }

    public Date getOpDateStart() {
        return opDateStart;
    }

    public DhlResponseRecordEntity setOpDateEnd(Date opDateEnd) {
        this.opDateEnd = opDateEnd;
        return this;
    }

    public Date getOpDateEnd() {
        return opDateEnd;
    }
    public DhlResponseRecordEntity setMemo(String memo) {
        this.memo = memo;
        return this;
    }

    public String getMemo() {
        return memo;
    }

    public DhlResponseRecordEntity setSendNum(BigDecimal sendNum) {
        this.sendNum = sendNum;
        return this;
    }

    public BigDecimal getSendNum() {
        return sendNum;
    }

    public DhlResponseRecordEntity setSendFlag(String sendFlag) {
        this.sendFlag = sendFlag;
        return this;
    }

    public String getSendFlag() {
        return sendFlag;
    }

    public DhlResponseRecordEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        return this;
    }

    public String getCreateUser() {
        return createUser;
    }

    public DhlResponseRecordEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        return this;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public DhlResponseRecordEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        return this;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public DhlResponseRecordEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        return this;
    }

    public Date getCreateDateStart() {
        return createDateStart;
    }

    public DhlResponseRecordEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        return this;
    }

    public Date getCreateDateEnd() {
        return createDateEnd;
    }
    public DhlResponseRecordEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        return this;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public DhlResponseRecordEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        return this;
    }

    public String getUpdateUserName() {
        return updateUserName;
    }

    public DhlResponseRecordEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        return this;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public DhlResponseRecordEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        return this;
    }

    public Date getUpdateDateStart() {
        return updateDateStart;
    }

    public DhlResponseRecordEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        return this;
    }

    public Date getUpdateDateEnd() {
        return updateDateEnd;
    }
    public DhlResponseRecordEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        return this;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public DhlResponseRecordEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        return this;
    }

    public String getCompanyName() {
        return companyName;
    }

    public DhlResponseRecordEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        return this;
    }

    public String getNodeCode() {
        return nodeCode;
    }

    public DhlResponseRecordEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        return this;
    }

    public String getNodeName() {
        return nodeName;
    }

    public DhlResponseRecordEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        return this;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public DhlResponseRecordEntity setGroupName(String groupName) {
        this.groupName = groupName;
        return this;
    }

    public String getGroupName() {
        return groupName;
    }

}
