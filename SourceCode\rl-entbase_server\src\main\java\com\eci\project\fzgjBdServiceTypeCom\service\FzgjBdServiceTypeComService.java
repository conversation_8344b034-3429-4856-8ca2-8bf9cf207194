package com.eci.project.fzgjBdServiceTypeCom.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjBdServiceTypeCom.dao.FzgjBdServiceTypeComDao;
import com.eci.project.fzgjBdServiceTypeCom.entity.FzgjBdServiceTypeComEntity;
import com.eci.project.fzgjBdServiceTypeCom.validate.FzgjBdServiceTypeComVal;

import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 企业级服务类型Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-03-13
*/
@Service
@Slf4j
public class FzgjBdServiceTypeComService implements EciBaseService<FzgjBdServiceTypeComEntity> {

    @Autowired
    private FzgjBdServiceTypeComDao fzgjBdServiceTypeComDao;

    @Autowired
    private FzgjBdServiceTypeComVal fzgjBdServiceTypeComVal;


    @Override
    public TgPageInfo queryPageList(FzgjBdServiceTypeComEntity entity) {
        EciQuery<FzgjBdServiceTypeComEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjBdServiceTypeComEntity> entities = fzgjBdServiceTypeComDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjBdServiceTypeComEntity save(FzgjBdServiceTypeComEntity entity) {
        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setGuid(IdWorker.getIdStr());
            entity.setCreateDate(new java.util.Date());
            entity.setUpdateDate(new java.util.Date());
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        } else {
            entity.setUpdateDate(new java.util.Date());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        }
        entity.setParentid("0");
        // 返回实体对象
        FzgjBdServiceTypeComEntity fzgjBdServiceTypeComEntity = null;
        fzgjBdServiceTypeComVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjBdServiceTypeComEntity = fzgjBdServiceTypeComDao.insertOne(entity);

        }else{

            fzgjBdServiceTypeComEntity = fzgjBdServiceTypeComDao.updateByEntityId(entity);

        }
        return fzgjBdServiceTypeComEntity;
    }

    @Override
    public List<FzgjBdServiceTypeComEntity> selectList(FzgjBdServiceTypeComEntity entity) {
        return fzgjBdServiceTypeComDao.selectList(entity);
    }

    @Override
    public FzgjBdServiceTypeComEntity selectOneById(Serializable id) {
        return fzgjBdServiceTypeComDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjBdServiceTypeComEntity> list) {
        fzgjBdServiceTypeComDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjBdServiceTypeComDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjBdServiceTypeComDao.deleteById(id);
    }
    /**
     * <AUTHOR>
     * @Description 根据groupcode删除所有数据
     * @Date  2025/5/15 12:46
     * @Param []
     * @return int
     **/
    public int deleteByGroupCode()
    {
        QueryWrapper query=new QueryWrapper();
        query.eq("GROUP_CODE",UserContext.getUserInfo().getCompanyCode());
        return fzgjBdServiceTypeComDao.delete(query);
    }
}