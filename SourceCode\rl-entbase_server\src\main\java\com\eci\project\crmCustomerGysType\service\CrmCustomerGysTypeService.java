package com.eci.project.crmCustomerGysType.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.crmCustomerGysType.dao.CrmCustomerGysTypeDao;
import com.eci.project.crmCustomerGysType.entity.CrmCustomerGysTypeEntity;
import com.eci.project.crmCustomerGysType.validate.CrmCustomerGysTypeVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 供应商类型Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-05-14
*/
@Service
@Slf4j
public class CrmCustomerGysTypeService implements EciBaseService<CrmCustomerGysTypeEntity> {

    @Autowired
    private CrmCustomerGysTypeDao crmCustomerGysTypeDao;

    @Autowired
    private CrmCustomerGysTypeVal crmCustomerGysTypeVal;


    @Override
    public TgPageInfo queryPageList(CrmCustomerGysTypeEntity entity) {
        EciQuery<CrmCustomerGysTypeEntity> eciQuery = EciQuery.buildQuery(entity);
        List<CrmCustomerGysTypeEntity> entities = crmCustomerGysTypeDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public CrmCustomerGysTypeEntity save(CrmCustomerGysTypeEntity entity) {
        // 返回实体对象
        CrmCustomerGysTypeEntity crmCustomerGysTypeEntity = null;
        crmCustomerGysTypeVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            crmCustomerGysTypeEntity = crmCustomerGysTypeDao.insertOne(entity);

        }else{

            crmCustomerGysTypeEntity = crmCustomerGysTypeDao.updateByEntityId(entity);

        }
        return crmCustomerGysTypeEntity;
    }

    @Override
    public List<CrmCustomerGysTypeEntity> selectList(CrmCustomerGysTypeEntity entity) {
        return crmCustomerGysTypeDao.selectList(entity);
    }

    @Override
    public CrmCustomerGysTypeEntity selectOneById(Serializable id) {
        return crmCustomerGysTypeDao.selectById(id);
    }


    @Override
    public void insertBatch(List<CrmCustomerGysTypeEntity> list) {
        crmCustomerGysTypeDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return crmCustomerGysTypeDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return crmCustomerGysTypeDao.deleteById(id);
    }
    /**
     * <AUTHOR>
     * @Description 根据客户GUID删除
     * @Date  2025/5/14 15:25
     * @Param [customerGuid]
     * @return int
     **/
    @Transactional(rollbackFor = Exception.class)
    public int deleteByCustomerGuid(String customerGuid){
        QueryWrapper query=new QueryWrapper();
        query.eq("CUSTOMER_GUID",customerGuid);
        return crmCustomerGysTypeDao.delete(query);
    }
}