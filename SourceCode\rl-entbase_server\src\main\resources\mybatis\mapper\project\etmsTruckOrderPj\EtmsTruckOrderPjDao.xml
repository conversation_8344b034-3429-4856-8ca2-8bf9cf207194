<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.etmsTruckOrderPj.dao.EtmsTruckOrderPjDao">
    <resultMap type="EtmsTruckOrderPjEntity" id="EtmsTruckOrderPjResult">
        <result property="guid" column="GUID"/>
        <result property="truckOrderGuid" column="TRUCK_ORDER_GUID"/>
        <result property="loginNo" column="LOGIN_NO"/>
        <result property="fwtddf" column="FWTDDF"/>
        <result property="yssxdf" column="YSSXDF"/>
        <result property="xsaqdf" column="XSAQDF"/>
        <result property="hdsxdf" column="HDSXDF"/>
        <result property="remark" column="REMARK"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="bczhpf" column="BCZHPF"/>
    </resultMap>

    <sql id="selectEtmsTruckOrderPjEntityVo">
        select
            GUID,
            TRUCK_ORDER_GUID,
            LOGIN_NO,
            FWTDDF,
            YSSXDF,
            XSAQDF,
            HDSXDF,
            REMARK,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            CREATE_USER_NAME,
            UPDATE_USER_NAME,
            CREATE_USER,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_DATE,
            BCZHPF
        from ETMS_TRUCK_ORDER_PJ
    </sql>

    <select id="queryPages" parameterType="com.baomidou.mybatisplus.core.conditions.Wrapper"
            resultType="com.eci.project.etmsTruckOrderPj.entity.orderPj">
select * from (SELECT
                   B.DRIVER_NAME,
                   A.UPDATE_DATE,
                   A.BCZHPF,
                   B.ORDER_NO,
                   A.FWTDDF,
                   A.YSSXDF,
                   A.XSAQDF,
                   A.HDSXDF,
                   ( '服务态度：'
                       || A.FWTDDF
                       || '星，运输时效：'
                       || A.YSSXDF
                       || '星，货物安全：'
                       || A.XSAQDF
                       || '星，回单时效：'
                       || A.HDSXDF
                       || '星' ) AS PFXQ,
                   A.REMARK,
                   (SELECT MAX(T.NAME)
                    FROM ETMS_BD_DRIVER T
                    WHERE T.USER_ID = A.LOGIN_NO
                      AND T.COMPANY_CODE = B.GROUP_CODE) as LOGIN_NO_NAME1,

                   A.LOGIN_NO

               FROM
                   ETMS_TRUCK_ORDER_PJ A
                       LEFT JOIN ETMS_TRUCK_ORDER B ON A.TRUCK_ORDER_GUID = B.GUID) A



                             ${ew.customSqlSegment}
    </select>
</mapper>