package com.eci.project.omsOrderFwxmTmsXlXlLyXl.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.omsOrderFwxmTmsXlXlLyXl.entity.OmsOrderFwxmTmsXlXlLyXlEntity;

import org.springframework.stereotype.Service;


/**
* 委托内容-程运序列-陆运-线路Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-06-04
*/
@Service
public class OmsOrderFwxmTmsXlXlLyXlVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(OmsOrderFwxmTmsXlXlLyXlEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(OmsOrderFwxmTmsXlXlLyXlEntity entity, BusinessType businessType) {

    }

}
