package com.eci.project.omsOrderFwxmZhysXl.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.omsOrderFwxmZhysXl.entity.OmsOrderFwxmZhysXlEntity;

import org.springframework.stereotype.Service;


/**
* 综合运输-线路Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-06-13
*/
@Service
public class OmsOrderFwxmZhysXlVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(OmsOrderFwxmZhysXlEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(OmsOrderFwxmZhysXlEntity entity, BusinessType businessType) {

    }

}
