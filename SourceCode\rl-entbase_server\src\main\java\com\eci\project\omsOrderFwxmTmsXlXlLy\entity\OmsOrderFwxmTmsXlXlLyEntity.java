package com.eci.project.omsOrderFwxmTmsXlXlLy.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.common.DictField;
import com.eci.common.ZsrBaseEntity;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 委托内容-程运序列-陆运对象 OMS_ORDER_FWXM_TMS_XL_XL_LY
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-06-04
*/
@ApiModel("委托内容-程运序列-陆运")
@TableName("OMS_ORDER_FWXM_TMS_XL_XL_LY")
@FieldNameConstants
public class OmsOrderFwxmTmsXlXlLyEntity extends ZsrBaseEntity {
    /**
    * 委托单编号
    */
    @ApiModelProperty("委托单编号(36)")
    @TableField("PRE_NO")
    private String preNo;

    /**
    * 订单号
    */
    @ApiModelProperty("订单号(36)")
    @TableField("ORDER_NO")
    private String orderNo;

    /**
    * 需求唯一编号
    */
    @ApiModelProperty("需求唯一编号(36)")
    @TableField("TMS_NO")
    private String tmsNo;

    /**
    * 结算线路唯一编号
    */
    @ApiModelProperty("结算线路唯一编号(36)")
    @TableField("LINE_NO")
    private String lineNo;

    /**
    * 程运序列唯一编号
    */
    @ApiModelProperty("程运序列唯一编号(36)")
    @TableField("SEQ_NO")
    private String seqNo;

    /**
    * 明细唯一编号
    */
    @ApiModelProperty("明细唯一编号(36)")
    @TableId("LY_NO")
    private String lyNo;

    /**
    * 公路承运方式(整车/整车自拼/零担)，运输业务类型
    */
    @ApiModelProperty("公路承运方式(整车/整车自拼/零担)，运输业务类型(10)")
    @TableField("CYFS")
    private String cyfs;

    /**
    * 是否主拼
    */
    @ApiModelProperty("是否主拼(1)")
    @TableField("IS_ZP")
    private String isZp;

    /**
    * 外部应收加点数/车辆尺寸
    */
    @ApiModelProperty("外部应收加点数/车辆尺寸(10)")
    @TableField("WBYSJDS")
    private String wbysjds;

    /**
    * 是否需要理货换托
    */
    @ApiModelProperty("是否需要理货换托(1)")
    @TableField("IS_LHWT")
    private String isLhwt;

    /**
    * 是否需要负责装货
    */
    @ApiModelProperty("是否需要负责装货(1)")
    @TableField("IS_FZZH")
    private String isFzzh;

    /**
    * 是否需要负责卸货
    */
    @ApiModelProperty("是否需要负责卸货(1)")
    @TableField("IS_FZXH")
    private String isFzxh;

    /**
    * 是否需要有押车员
    */
    @ApiModelProperty("是否需要有押车员(1)")
    @TableField("IS_YCY")
    private String isYcy;

    /**
    * 是否需要带液压车
    */
    @ApiModelProperty("是否需要带液压车(1)")
    @TableField("IS_DYYC")
    private String isDyyc;

    /**
    * 是否需要拆托分料
    */
    @ApiModelProperty("是否需要拆托分料(1)")
    @TableField("IS_CTFL")
    private String isCtfl;

    /**
    * 是否要求车载GPS
    */
    @ApiModelProperty("是否要求车载GPS(1)")
    @TableField("IS_GPS")
    private String isGps;

    /**
    * 是否要求高速
    */
    @ApiModelProperty("是否要求高速(1)")
    @TableField("IS_EXPRESSWAY")
    private String isExpressway;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 最后修改日期
    */
    @ApiModelProperty("最后修改日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("最后修改日期开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("最后修改日期结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(200)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 零担计费依据/车辆类型、零担运输按整车价计费(应收)
    */
    @ApiModelProperty("零担计费依据/车辆类型、零担运输按整车价计费(应收)(20)")
    @DictField(queryKey = "OMS_BD_LDJFYJ")
    @TableField("LDJFYJ")
    private String ldjfyj;

    /**
    * 结算送达日期(应收)
    */
    @ApiModelProperty("结算送达日期(应收)(7)")
    @TableField("JSSD_DATE")
    private Date jssdDate;

    @ApiModelProperty("结算送达日期(应收)开始")
    @TableField(exist=false)
    private Date jssdDateStart;

    @ApiModelProperty("结算送达日期(应收)结束")
    @TableField(exist=false)
    private Date jssdDateEnd;

    @ApiModelProperty("服务项目编码")
    @TableField("FWXM_CODE")
    private String fwxmCode;

    @ApiModelProperty("其他特殊要求")
    @TableField("OTHER_MEMO")
    private String otherMemo;

    @ApiModelProperty("协作任务编码")
    @TableField("WORK_NO")
    private String workNo;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public OmsOrderFwxmTmsXlXlLyEntity() {
        this.setSubClazz(OmsOrderFwxmTmsXlXlLyEntity.class);
    }

    public OmsOrderFwxmTmsXlXlLyEntity setPreNo(String preNo) {
        this.preNo = preNo;
        this.nodifySetFiled("preNo", preNo);
        return this;
    }

    public String getPreNo() {
        this.nodifyGetFiled("preNo");
        return preNo;
    }

    public OmsOrderFwxmTmsXlXlLyEntity setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        this.nodifySetFiled("orderNo", orderNo);
        return this;
    }

    public String getOrderNo() {
        this.nodifyGetFiled("orderNo");
        return orderNo;
    }

    public OmsOrderFwxmTmsXlXlLyEntity setTmsNo(String tmsNo) {
        this.tmsNo = tmsNo;
        this.nodifySetFiled("tmsNo", tmsNo);
        return this;
    }

    public String getTmsNo() {
        this.nodifyGetFiled("tmsNo");
        return tmsNo;
    }

    public OmsOrderFwxmTmsXlXlLyEntity setLineNo(String lineNo) {
        this.lineNo = lineNo;
        this.nodifySetFiled("lineNo", lineNo);
        return this;
    }

    public String getLineNo() {
        this.nodifyGetFiled("lineNo");
        return lineNo;
    }

    public OmsOrderFwxmTmsXlXlLyEntity setSeqNo(String seqNo) {
        this.seqNo = seqNo;
        this.nodifySetFiled("seqNo", seqNo);
        return this;
    }

    public String getSeqNo() {
        this.nodifyGetFiled("seqNo");
        return seqNo;
    }

    public OmsOrderFwxmTmsXlXlLyEntity setLyNo(String lyNo) {
        this.lyNo = lyNo;
        this.nodifySetFiled("lyNo", lyNo);
        return this;
    }

    public String getLyNo() {
        this.nodifyGetFiled("lyNo");
        return lyNo;
    }

    public OmsOrderFwxmTmsXlXlLyEntity setCyfs(String cyfs) {
        this.cyfs = cyfs;
        this.nodifySetFiled("cyfs", cyfs);
        return this;
    }

    public String getCyfs() {
        this.nodifyGetFiled("cyfs");
        return cyfs;
    }

    public OmsOrderFwxmTmsXlXlLyEntity setIsZp(String isZp) {
        this.isZp = isZp;
        this.nodifySetFiled("isZp", isZp);
        return this;
    }

    public String getIsZp() {
        this.nodifyGetFiled("isZp");
        return isZp;
    }

    public OmsOrderFwxmTmsXlXlLyEntity setWbysjds(String wbysjds) {
        this.wbysjds = wbysjds;
        this.nodifySetFiled("wbysjds", wbysjds);
        return this;
    }

    public String getWbysjds() {
        this.nodifyGetFiled("wbysjds");
        return wbysjds;
    }

    public OmsOrderFwxmTmsXlXlLyEntity setIsLhwt(String isLhwt) {
        this.isLhwt = isLhwt;
        this.nodifySetFiled("isLhwt", isLhwt);
        return this;
    }

    public String getIsLhwt() {
        this.nodifyGetFiled("isLhwt");
        return isLhwt;
    }

    public OmsOrderFwxmTmsXlXlLyEntity setIsFzzh(String isFzzh) {
        this.isFzzh = isFzzh;
        this.nodifySetFiled("isFzzh", isFzzh);
        return this;
    }

    public String getIsFzzh() {
        this.nodifyGetFiled("isFzzh");
        return isFzzh;
    }

    public OmsOrderFwxmTmsXlXlLyEntity setIsFzxh(String isFzxh) {
        this.isFzxh = isFzxh;
        this.nodifySetFiled("isFzxh", isFzxh);
        return this;
    }

    public String getIsFzxh() {
        this.nodifyGetFiled("isFzxh");
        return isFzxh;
    }

    public OmsOrderFwxmTmsXlXlLyEntity setIsYcy(String isYcy) {
        this.isYcy = isYcy;
        this.nodifySetFiled("isYcy", isYcy);
        return this;
    }

    public String getIsYcy() {
        this.nodifyGetFiled("isYcy");
        return isYcy;
    }

    public OmsOrderFwxmTmsXlXlLyEntity setIsDyyc(String isDyyc) {
        this.isDyyc = isDyyc;
        this.nodifySetFiled("isDyyc", isDyyc);
        return this;
    }

    public String getIsDyyc() {
        this.nodifyGetFiled("isDyyc");
        return isDyyc;
    }

    public OmsOrderFwxmTmsXlXlLyEntity setIsCtfl(String isCtfl) {
        this.isCtfl = isCtfl;
        this.nodifySetFiled("isCtfl", isCtfl);
        return this;
    }

    public String getIsCtfl() {
        this.nodifyGetFiled("isCtfl");
        return isCtfl;
    }

    public OmsOrderFwxmTmsXlXlLyEntity setIsGps(String isGps) {
        this.isGps = isGps;
        this.nodifySetFiled("isGps", isGps);
        return this;
    }

    public String getIsGps() {
        this.nodifyGetFiled("isGps");
        return isGps;
    }

    public OmsOrderFwxmTmsXlXlLyEntity setIsExpressway(String isExpressway) {
        this.isExpressway = isExpressway;
        this.nodifySetFiled("isExpressway", isExpressway);
        return this;
    }

    public String getIsExpressway() {
        this.nodifyGetFiled("isExpressway");
        return isExpressway;
    }

    public OmsOrderFwxmTmsXlXlLyEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public OmsOrderFwxmTmsXlXlLyEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public OmsOrderFwxmTmsXlXlLyEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public OmsOrderFwxmTmsXlXlLyEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public OmsOrderFwxmTmsXlXlLyEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public OmsOrderFwxmTmsXlXlLyEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public OmsOrderFwxmTmsXlXlLyEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public OmsOrderFwxmTmsXlXlLyEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public OmsOrderFwxmTmsXlXlLyEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public OmsOrderFwxmTmsXlXlLyEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public OmsOrderFwxmTmsXlXlLyEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public OmsOrderFwxmTmsXlXlLyEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public OmsOrderFwxmTmsXlXlLyEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public OmsOrderFwxmTmsXlXlLyEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public OmsOrderFwxmTmsXlXlLyEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public OmsOrderFwxmTmsXlXlLyEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public OmsOrderFwxmTmsXlXlLyEntity setLdjfyj(String ldjfyj) {
        this.ldjfyj = ldjfyj;
        this.nodifySetFiled("ldjfyj", ldjfyj);
        return this;
    }

    public String getLdjfyj() {
        this.nodifyGetFiled("ldjfyj");
        return ldjfyj;
    }

    public OmsOrderFwxmTmsXlXlLyEntity setJssdDate(Date jssdDate) {
        this.jssdDate = jssdDate;
        this.nodifySetFiled("jssdDate", jssdDate);
        return this;
    }

    public Date getJssdDate() {
        this.nodifyGetFiled("jssdDate");
        return jssdDate;
    }

    public OmsOrderFwxmTmsXlXlLyEntity setJssdDateStart(Date jssdDateStart) {
        this.jssdDateStart = jssdDateStart;
        this.nodifySetFiled("jssdDateStart", jssdDateStart);
        return this;
    }

    public Date getJssdDateStart() {
        this.nodifyGetFiled("jssdDateStart");
        return jssdDateStart;
    }

    public OmsOrderFwxmTmsXlXlLyEntity setJssdDateEnd(Date jssdDateEnd) {
        this.jssdDateEnd = jssdDateEnd;
        this.nodifySetFiled("jssdDateEnd", jssdDateEnd);
        return this;
    }

    public Date getJssdDateEnd() {
        this.nodifyGetFiled("jssdDateEnd");
        return jssdDateEnd;
    }

    public String getFwxmCode() {
        this.nodifyGetFiled("fwxmCode");
        return fwxmCode;
    }

    public OmsOrderFwxmTmsXlXlLyEntity setFwxmCode(String fwxmCode) {
        this.fwxmCode = fwxmCode;
        this.nodifySetFiled("fwxmCode", fwxmCode);
        return this;
    }

    public String getOtherMemo() {
        this.nodifyGetFiled("otherMemo");
        return otherMemo;
    }

    public OmsOrderFwxmTmsXlXlLyEntity setOtherMemo(String otherMemo) {
        this.otherMemo = otherMemo;
        this.nodifySetFiled("otherMemo", otherMemo);
        return this;
    }
    public OmsOrderFwxmTmsXlXlLyEntity setWorkNo(String workNo) {
        this.workNo = workNo;
        this.nodifySetFiled("workNo", workNo);
        return this;
    }
    public String getWorkNo() {
        this.nodifyGetFiled("workNo");
        return workNo;
    }
}
