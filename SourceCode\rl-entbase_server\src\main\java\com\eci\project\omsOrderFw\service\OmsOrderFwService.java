package com.eci.project.omsOrderFw.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eci.common.DataExtend;
import com.eci.common.util.StringUtils;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjBdServiceType.dao.FzgjBdServiceTypeDao;
import com.eci.project.fzgjBdServiceType.entity.FzgjBdServiceTypeEntity;
import com.eci.project.omsOrderFw.dao.OmsOrderFwDao;
import com.eci.project.omsOrderFw.entity.OmsOrderFwEntity;
import com.eci.project.omsOrderFw.validate.OmsOrderFwVal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 订单服务类型Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-04-24
 */
@Service
@Slf4j
public class OmsOrderFwService implements EciBaseService<OmsOrderFwEntity> {

    @Autowired
    private OmsOrderFwDao omsOrderFwDao;

    @Autowired
    private OmsOrderFwVal omsOrderFwVal;

    @Autowired
    private FzgjBdServiceTypeDao fzgjBdServiceTypeDao;


    @Override
    public TgPageInfo queryPageList(OmsOrderFwEntity entity) {
        EciQuery<OmsOrderFwEntity> eciQuery = EciQuery.buildQuery(entity);
        List<OmsOrderFwEntity> entities = omsOrderFwDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderFwEntity save(OmsOrderFwEntity entity) {
        // 返回实体对象
        OmsOrderFwEntity omsOrderFwEntity = null;
        omsOrderFwVal.saveValidate(entity, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            omsOrderFwEntity = omsOrderFwDao.insertOne(entity);

        } else {

            omsOrderFwEntity = omsOrderFwDao.updateByEntityId(entity);

        }
        return omsOrderFwEntity;
    }

    @Override
    public List<OmsOrderFwEntity> selectList(OmsOrderFwEntity entity) {
        return omsOrderFwDao.selectList(entity);
    }

    @Override
    public OmsOrderFwEntity selectOneById(Serializable id) {
        return omsOrderFwDao.selectById(id);
    }


    @Override
    public void insertBatch(List<OmsOrderFwEntity> list) {
        omsOrderFwDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return omsOrderFwDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return omsOrderFwDao.deleteById(id);
    }


    public String GetName(String orderFwlxCode) {

        if (StringUtils.hasValue(orderFwlxCode)) {
            QueryWrapper query = new QueryWrapper();
            query.in("CODE", DataExtend.convertList(orderFwlxCode));
            List<FzgjBdServiceTypeEntity> list = fzgjBdServiceTypeDao.selectList(query);
            List<String> nameList = list.stream().map(FzgjBdServiceTypeEntity::getName).collect(Collectors.toList());
            return DataExtend.toOneString(nameList);
        }

        return "";
    }
}