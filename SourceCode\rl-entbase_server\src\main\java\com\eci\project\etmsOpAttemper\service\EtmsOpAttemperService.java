package com.eci.project.etmsOpAttemper.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.etmsOpAttemper.dao.EtmsOpAttemperDao;
import com.eci.project.etmsOpAttemper.entity.EtmsOpAttemperEntity;
import com.eci.project.etmsOpAttemper.validate.EtmsOpAttemperVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 运输委托信息Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-06-13
*/
@Service
@Slf4j
public class EtmsOpAttemperService implements EciBaseService<EtmsOpAttemperEntity> {

    @Autowired
    private EtmsOpAttemperDao etmsOpAttemperDao;

    @Autowired
    private EtmsOpAttemperVal etmsOpAttemperVal;


    @Override
    public TgPageInfo queryPageList(EtmsOpAttemperEntity entity) {
        EciQuery<EtmsOpAttemperEntity> eciQuery = EciQuery.buildQuery(entity);
        List<EtmsOpAttemperEntity> entities = etmsOpAttemperDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public EtmsOpAttemperEntity save(EtmsOpAttemperEntity entity) {
        // 返回实体对象
        EtmsOpAttemperEntity etmsOpAttemperEntity = null;
        etmsOpAttemperVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            etmsOpAttemperEntity = etmsOpAttemperDao.insertOne(entity);

        }else{

            etmsOpAttemperEntity = etmsOpAttemperDao.updateByEntityId(entity);

        }
        return etmsOpAttemperEntity;
    }

    @Override
    public List<EtmsOpAttemperEntity> selectList(EtmsOpAttemperEntity entity) {
        return etmsOpAttemperDao.selectList(entity);
    }

    @Override
    public EtmsOpAttemperEntity selectOneById(Serializable id) {
        return etmsOpAttemperDao.selectById(id);
    }


    @Override
    public void insertBatch(List<EtmsOpAttemperEntity> list) {
        etmsOpAttemperDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return etmsOpAttemperDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return etmsOpAttemperDao.deleteById(id);
    }

}