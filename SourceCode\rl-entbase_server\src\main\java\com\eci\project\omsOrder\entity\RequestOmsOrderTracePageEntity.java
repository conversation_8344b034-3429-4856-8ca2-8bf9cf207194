package com.eci.project.omsOrder.entity;

/**
 * @ClassName: RequestOmsOrderTracePageEntity
 * @Author: guangyan.mei
 * @Date: 2025/5/7 14:34
 * @Description: TODO
 */
public class RequestOmsOrderTracePageEntity extends OmsOrderEntity {


    // 包含服务项目
    public String fwxmCode;

    // 进仓编号
    public String  warehouseInNo;

    public String getDecNo() {
        return decNo;
    }

    public void setDecNo(String decNo) {
        this.decNo = decNo;
    }

    // 报关单号
    public String  decNo;
    // 核注清单号
    public  String  checkBillNo;
    // 金二核放单号
    public  String  hfNo;

    public String getCheckBillNo() {
        return checkBillNo;
    }

    public void setCheckBillNo(String checkBillNo) {
        this.checkBillNo = checkBillNo;
    }

    public String getHfNo() {
        return hfNo;
    }

    public void setHfNo(String hfNo) {
        this.hfNo = hfNo;
    }

    public String getHfNoLy() {
        return hfNoLy;
    }

    public void setHfNoLy(String hfNoLy) {
        this.hfNoLy = hfNoLy;
    }

    public String getMbNo() {
        return mbNo;
    }

    public void setMbNo(String mbNo) {
        this.mbNo = mbNo;
    }

    public String getHbNo() {
        return hbNo;
    }

    public void setHbNo(String hbNo) {
        this.hbNo = hbNo;
    }

    // 陆运核放单号
    public  String  hfNoLy;
    // 主单号
    public  String  mbNo;
    // 分单号
    public  String  hbNo;

    public String getIsException() {
        return isException;
    }

    public void setIsException(String isException) {
        this.isException = isException;
    }

    // 是否异常
    public String isException;

    public String getIsDelay() {
        return isDelay;
    }

    public void setIsDelay(String isDelay) {
        this.isDelay = isDelay;
    }

    // 是否延迟
    public String isDelay;

    public String getFwxmCode() {
        return fwxmCode;
    }

    public void setFwxmCode(String fwxmCode) {
        this.fwxmCode = fwxmCode;
    }

    public String getWarehouseInNo() {
        return warehouseInNo;
    }

    public void setWarehouseInNo(String warehouseInNo) {
        this.warehouseInNo = warehouseInNo;
    }

    public String getIsJa() {
        return isJa;
    }

    public void setIsJa(String isJa) {
        this.isJa = isJa;
    }

    // 结案
    public String isJa;
    // 应收费用齐全
    public String arapOk;
    // 应付费用齐全
    public String apOk;

}
