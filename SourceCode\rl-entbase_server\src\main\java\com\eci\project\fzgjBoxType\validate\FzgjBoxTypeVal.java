package com.eci.project.fzgjBoxType.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjBoxType.entity.FzgjBoxTypeEntity;

import org.springframework.stereotype.Service;


/**
* 集装箱类型Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-17
*/
@Service
public class FzgjBoxTypeVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjBoxTypeEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjBoxTypeEntity entity, BusinessType businessType) {

    }

}
