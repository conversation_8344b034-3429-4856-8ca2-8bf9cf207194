package com.eci.project.fzgjBdCountry.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjBdCountry.service.IFzgjBdCountryService;
import com.eci.project.fzgjBdCountry.entity.FzgjBdCountryEntity;
import com.eci.sso.role.entity.UserContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.EciLog;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


/**
* 国家Controller
*
* @<NAME_EMAIL>
* @date 2025-03-14
*/
@Api(tags = "国家")
@RestController
@RequestMapping("/fzgjBdCountry")
public class FzgjBdCountryController extends EciBaseController {

    @Autowired
    private IFzgjBdCountryService fzgjBdCountryService;

    /**
    * 根据实体插入数据库
    * @param entity
    * @return
    */
    @ApiOperation("国家:保存")
    @EciLog(title = "国家:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public ResponseMsg save(@RequestBody FzgjBdCountryEntity entity){


        return ResponseMsgUtil.success(10001,fzgjBdCountryService.save(entity));
    }

    /**
    * 根据条件查询一个集合
    * @param entity
    * @return
    */
    @ApiOperation("国家:查询列表")
    @EciLog(title = "国家:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    public ResponseMsg selectList(@RequestBody FzgjBdCountryEntity entity){
        return ResponseMsgUtil.success(10001,fzgjBdCountryService.selectList(entity));
    }

    /**
    * 分页查询
    * @param entity
    * @return
    */
    @ApiOperation("国家:分页查询列表")
    @EciLog(title = "国家:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    public ResponseMsg selectPageList(@RequestBody FzgjBdCountryEntity entity){
        return ResponseMsgUtil.success(10001,fzgjBdCountryService.queryPageList(entity));
    }

    /**
    * 根据ID查一条记录
    * @param entity
    * @return
    */
    @ApiOperation("国家:根据ID查一条")
    @EciLog(title = "国家:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    public ResponseMsg selectOneById(@RequestBody FzgjBdCountryEntity entity){
        return ResponseMsgUtil.success(10001,fzgjBdCountryService.selectOneById(entity.getGuid()));
    }


    /**
    * 根据ID删除
    * @param entity
    * @return
    */
    @ApiOperation("国家:根据ID删除一条")
    @EciLog(title = "国家:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    public ResponseMsg deleteById(@RequestBody FzgjBdCountryEntity entity){
        return ResponseMsgUtil.success(10001,fzgjBdCountryService.deleteById(entity.getGuid()));
    }

    /**
    * 根据IDS删除
    *
    * @param entity
    * @return
    */
    @ApiOperation("国家:根据ID字符串删除多条")
    @EciLog(title = "国家:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    public ResponseMsg deleteByIds(@RequestBody FzgjBdCountryEntity entity) {
        return ResponseMsgUtil.success(10001, fzgjBdCountryService.deleteByIds(entity.getIds()));
    }


}