package com.eci.project.crmCustomerHead.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.crmCustomerHead.service.CrmCustomerHeadService;
import com.eci.project.crmCustomerHead.entity.CrmCustomerHeadEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 业务伙伴表头Controller
*
* @<NAME_EMAIL>
* @date 2025-04-29
*/
@Api(tags = "业务伙伴表头")
@RestController
@RequestMapping("/crmCustomerHead")
public class CrmCustomerHeadController extends EciBaseController {

    @Autowired
    private CrmCustomerHeadService crmCustomerHeadService;


    @ApiOperation("业务伙伴表头:保存")
    @EciLog(title = "业务伙伴表头:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody CrmCustomerHeadEntity entity){
        CrmCustomerHeadEntity crmCustomerHeadEntity =crmCustomerHeadService.save(entity);
        return ResponseMsgUtil.success(10001,crmCustomerHeadEntity);
    }


    @ApiOperation("业务伙伴表头:查询列表")
    @EciLog(title = "业务伙伴表头:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody CrmCustomerHeadEntity entity){
        List<CrmCustomerHeadEntity> crmCustomerHeadEntities = crmCustomerHeadService.selectList(entity);
        return ResponseMsgUtil.success(10001,crmCustomerHeadEntities);
    }


    @ApiOperation("业务伙伴表头:分页查询列表")
    @EciLog(title = "业务伙伴表头:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody CrmCustomerHeadEntity entity){
        TgPageInfo tgPageInfo = crmCustomerHeadService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("业务伙伴表头:根据ID查一条")
    @EciLog(title = "业务伙伴表头:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody CrmCustomerHeadEntity entity){
        CrmCustomerHeadEntity  crmCustomerHeadEntity = crmCustomerHeadService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,crmCustomerHeadEntity);
    }


    @ApiOperation("业务伙伴表头:根据ID删除一条")
    @EciLog(title = "业务伙伴表头:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody CrmCustomerHeadEntity entity){
        int count = crmCustomerHeadService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("业务伙伴表头:根据ID字符串删除多条")
    @EciLog(title = "业务伙伴表头:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody CrmCustomerHeadEntity entity) {
        int count = crmCustomerHeadService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}