package com.eci.project.etmsCrmEnterprise.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.etmsCrmEnterprise.service.EtmsCrmEnterpriseService;
import com.eci.project.etmsCrmEnterprise.entity.EtmsCrmEnterpriseEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* Controller
*
* @<NAME_EMAIL>
* @date 2025-06-13
*/
@Api(tags = "")
@RestController
@RequestMapping("/etmsCrmEnterprise")
public class EtmsCrmEnterpriseController extends EciBaseController {

    @Autowired
    private EtmsCrmEnterpriseService etmsCrmEnterpriseService;


    @ApiOperation(":保存")
    @EciLog(title = ":新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody EtmsCrmEnterpriseEntity entity){
        EtmsCrmEnterpriseEntity etmsCrmEnterpriseEntity =etmsCrmEnterpriseService.save(entity);
        return ResponseMsgUtil.success(10001,etmsCrmEnterpriseEntity);
    }


    @ApiOperation(":查询列表")
    @EciLog(title = ":查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody EtmsCrmEnterpriseEntity entity){
        List<EtmsCrmEnterpriseEntity> etmsCrmEnterpriseEntities = etmsCrmEnterpriseService.selectList(entity);
        return ResponseMsgUtil.success(10001,etmsCrmEnterpriseEntities);
    }


    @ApiOperation(":分页查询列表")
    @EciLog(title = ":分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody EtmsCrmEnterpriseEntity entity){
        TgPageInfo tgPageInfo = etmsCrmEnterpriseService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation(":根据ID查一条")
    @EciLog(title = ":根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody EtmsCrmEnterpriseEntity entity){
        EtmsCrmEnterpriseEntity  etmsCrmEnterpriseEntity = etmsCrmEnterpriseService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,etmsCrmEnterpriseEntity);
    }


    @ApiOperation(":根据ID删除一条")
    @EciLog(title = ":根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody EtmsCrmEnterpriseEntity entity){
        int count = etmsCrmEnterpriseService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation(":根据ID字符串删除多条")
    @EciLog(title = ":根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody EtmsCrmEnterpriseEntity entity) {
        int count = etmsCrmEnterpriseService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}