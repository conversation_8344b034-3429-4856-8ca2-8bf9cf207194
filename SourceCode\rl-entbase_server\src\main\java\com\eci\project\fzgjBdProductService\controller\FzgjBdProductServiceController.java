package com.eci.project.fzgjBdProductService.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjBdProductService.service.FzgjBdProductServiceService;
import com.eci.project.fzgjBdProductService.entity.FzgjBdProductServiceEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 产品服务类型Controller
*
* @<NAME_EMAIL>
* @date 2025-04-01
*/
@Api(tags = "产品服务类型")
@RestController
@RequestMapping("/fzgjBdProductService")
public class FzgjBdProductServiceController extends EciBaseController {

    @Autowired
    private FzgjBdProductServiceService fzgjBdProductServiceService;


    @ApiOperation("产品服务类型:保存")
    @EciLog(title = "产品服务类型:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody String jsonString){
        FzgjBdProductServiceEntity fzgjBdProductServiceEntity =fzgjBdProductServiceService.save(jsonString);
        return ResponseMsgUtil.success(10001,fzgjBdProductServiceEntity);
    }


    @ApiOperation("产品服务类型:查询列表")
    @EciLog(title = "产品服务类型:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjBdProductServiceEntity entity){
        List<FzgjBdProductServiceEntity> fzgjBdProductServiceEntities = fzgjBdProductServiceService.selectList(entity);
        return ResponseMsgUtil.success(10001,fzgjBdProductServiceEntities);
    }


    @ApiOperation("产品服务类型:分页查询列表")
    @EciLog(title = "产品服务类型:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjBdProductServiceEntity entity){
        TgPageInfo tgPageInfo = fzgjBdProductServiceService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("产品服务类型:根据ID查一条")
    @EciLog(title = "产品服务类型:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjBdProductServiceEntity entity){
        FzgjBdProductServiceEntity  fzgjBdProductServiceEntity = fzgjBdProductServiceService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,fzgjBdProductServiceEntity);
    }


    @ApiOperation("产品服务类型:根据ID删除一条")
    @EciLog(title = "产品服务类型:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjBdProductServiceEntity entity){
        int count = fzgjBdProductServiceService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("产品服务类型:根据ID字符串删除多条")
    @EciLog(title = "产品服务类型:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjBdProductServiceEntity entity) {
        int count = fzgjBdProductServiceService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}