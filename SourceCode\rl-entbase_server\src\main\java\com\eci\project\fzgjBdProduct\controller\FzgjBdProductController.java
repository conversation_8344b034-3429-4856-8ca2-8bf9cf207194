package com.eci.project.fzgjBdProduct.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjBdProduct.service.FzgjBdProductService;
import com.eci.project.fzgjBdProduct.entity.FzgjBdProductEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 业务产品Controller
*
* @<NAME_EMAIL>
* @date 2025-04-01
*/
@Api(tags = "业务产品")
@RestController
@RequestMapping("/fzgjBdProduct")
public class FzgjBdProductController extends EciBaseController {

    @Autowired
    private FzgjBdProductService fzgjBdProductService;


    @ApiOperation("业务产品:保存")
    @EciLog(title = "业务产品:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody FzgjBdProductEntity entity){
        FzgjBdProductEntity fzgjBdProductEntity =fzgjBdProductService.save(entity);
        return ResponseMsgUtil.success(10001,fzgjBdProductEntity);
    }


    @ApiOperation("业务产品:查询列表")
    @EciLog(title = "业务产品:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjBdProductEntity entity){
        List<FzgjBdProductEntity> fzgjBdProductEntities = fzgjBdProductService.selectList(entity);
        return ResponseMsgUtil.success(10001,fzgjBdProductEntities);
    }


    @ApiOperation("业务产品:分页查询列表")
    @EciLog(title = "业务产品:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjBdProductEntity entity){
        TgPageInfo tgPageInfo = fzgjBdProductService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("业务产品:根据ID查一条")
    @EciLog(title = "业务产品:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjBdProductEntity entity){
        FzgjBdProductEntity  fzgjBdProductEntity = fzgjBdProductService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,fzgjBdProductEntity);
    }


    @ApiOperation("业务产品:根据ID删除一条")
    @EciLog(title = "业务产品:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjBdProductEntity entity){
        int count = fzgjBdProductService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("业务产品:根据ID字符串删除多条")
    @EciLog(title = "业务产品:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjBdProductEntity entity) {
        int count = fzgjBdProductService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}