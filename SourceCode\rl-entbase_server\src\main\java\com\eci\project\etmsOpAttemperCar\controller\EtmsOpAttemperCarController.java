package com.eci.project.etmsOpAttemperCar.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.etmsOpAttemperCar.service.EtmsOpAttemperCarService;
import com.eci.project.etmsOpAttemperCar.entity.EtmsOpAttemperCarEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 用车需求信息Controller
*
* @<NAME_EMAIL>
* @date 2025-06-13
*/
@Api(tags = "用车需求信息")
@RestController
@RequestMapping("/etmsOpAttemperCar")
public class EtmsOpAttemperCarController extends EciBaseController {

    @Autowired
    private EtmsOpAttemperCarService etmsOpAttemperCarService;


    @ApiOperation("用车需求信息:保存")
    @EciLog(title = "用车需求信息:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody EtmsOpAttemperCarEntity entity){
        EtmsOpAttemperCarEntity etmsOpAttemperCarEntity =etmsOpAttemperCarService.save(entity);
        return ResponseMsgUtil.success(10001,etmsOpAttemperCarEntity);
    }


    @ApiOperation("用车需求信息:查询列表")
    @EciLog(title = "用车需求信息:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody EtmsOpAttemperCarEntity entity){
        List<EtmsOpAttemperCarEntity> etmsOpAttemperCarEntities = etmsOpAttemperCarService.selectList(entity);
        return ResponseMsgUtil.success(10001,etmsOpAttemperCarEntities);
    }


    @ApiOperation("用车需求信息:分页查询列表")
    @EciLog(title = "用车需求信息:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody EtmsOpAttemperCarEntity entity){
        TgPageInfo tgPageInfo = etmsOpAttemperCarService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("用车需求信息:根据ID查一条")
    @EciLog(title = "用车需求信息:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody EtmsOpAttemperCarEntity entity){
        EtmsOpAttemperCarEntity  etmsOpAttemperCarEntity = etmsOpAttemperCarService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,etmsOpAttemperCarEntity);
    }


    @ApiOperation("用车需求信息:根据ID删除一条")
    @EciLog(title = "用车需求信息:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody EtmsOpAttemperCarEntity entity){
        int count = etmsOpAttemperCarService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("用车需求信息:根据ID字符串删除多条")
    @EciLog(title = "用车需求信息:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody EtmsOpAttemperCarEntity entity) {
        int count = etmsOpAttemperCarService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}