<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.fzgjBoxSize.dao.FzgjBoxSizeDao">
    <resultMap type="FzgjBoxSizeEntity" id="FzgjBoxSizeResult">
        <result property="guid" column="GUID"/>
        <result property="boxSize" column="BOX_SIZE"/>
        <result property="targetCode" column="TARGET_CODE"/>
        <result property="length" column="LENGTH"/>
        <result property="width" column="WIDTH"/>
        <result property="hight" column="HIGHT"/>
        <result property="weight" column="WEIGHT"/>
        <result property="status" column="STATUS"/>
        <result property="seq" column="SEQ"/>
        <result property="memo" column="MEMO"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
    </resultMap>

    <sql id="selectFzgjBoxSizeEntityVo">
        select
            GUID,
            BOX_SIZE,
            TARGET_CODE,
            LENGTH,
            WIDTH,
            HIGHT,
            WEIGHT,
            STATUS,
            SEQ,
            MEMO,
            CREATE_USER,
            CREATE_USER_NAME,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_USER_NAME,
            UPDATE_DATE,
            NODE_CODE,
            NODE_NAME,
            COMPANY_CODE,
            COMPANY_NAME,
            GROUP_CODE,
            GROUP_NAME
        from FZGJ_BOX_SIZE
    </sql>
</mapper>