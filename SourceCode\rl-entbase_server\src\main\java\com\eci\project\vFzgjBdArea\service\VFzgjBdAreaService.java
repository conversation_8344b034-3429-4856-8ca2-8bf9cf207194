package com.eci.project.vFzgjBdArea.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.vFzgjBdArea.dao.VFzgjBdAreaDao;
import com.eci.project.vFzgjBdArea.entity.VFzgjBdAreaEntity;
import com.eci.project.vFzgjBdArea.validate.VFzgjBdAreaVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-05-20
*/
@Service
@Slf4j
public class VFzgjBdAreaService implements EciBaseService<VFzgjBdAreaEntity> {

    @Autowired
    private VFzgjBdAreaDao vFzgjBdAreaDao;

    @Autowired
    private VFzgjBdAreaVal vFzgjBdAreaVal;


    @Override
    public TgPageInfo queryPageList(VFzgjBdAreaEntity entity) {
        EciQuery<VFzgjBdAreaEntity> eciQuery = EciQuery.buildQuery(entity);
        List<VFzgjBdAreaEntity> entities = vFzgjBdAreaDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public VFzgjBdAreaEntity save(VFzgjBdAreaEntity entity) {
        // 返回实体对象
        VFzgjBdAreaEntity vFzgjBdAreaEntity = null;
        vFzgjBdAreaVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            vFzgjBdAreaEntity = vFzgjBdAreaDao.insertOne(entity);

        }else{

            vFzgjBdAreaEntity = vFzgjBdAreaDao.updateByEntityId(entity);

        }
        return vFzgjBdAreaEntity;
    }

    @Override
    public List<VFzgjBdAreaEntity> selectList(VFzgjBdAreaEntity entity) {
        return vFzgjBdAreaDao.selectList(entity);
    }

    @Override
    public VFzgjBdAreaEntity selectOneById(Serializable id) {
        return vFzgjBdAreaDao.selectById(id);
    }


    @Override
    public void insertBatch(List<VFzgjBdAreaEntity> list) {
        vFzgjBdAreaDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return vFzgjBdAreaDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return vFzgjBdAreaDao.deleteById(id);
    }

}