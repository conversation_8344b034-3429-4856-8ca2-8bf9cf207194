package com.eci.project.fzgjBdOmsPagesFk.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjBdOmsPagesFk.service.FzgjBdOmsPagesFkService;
import com.eci.project.fzgjBdOmsPagesFk.entity.FzgjBdOmsPagesFkEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 订单反馈页面Controller
*
* @<NAME_EMAIL>
* @date 2025-04-14
*/
@Api(tags = "订单反馈页面")
@RestController
@RequestMapping("/fzgjBdOmsPagesFk")
public class FzgjBdOmsPagesFkController extends EciBaseController {

    @Autowired
    private FzgjBdOmsPagesFkService fzgjBdOmsPagesFkService;


    @ApiOperation("订单反馈页面:保存")
    @EciLog(title = "订单反馈页面:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody FzgjBdOmsPagesFkEntity entity){
        FzgjBdOmsPagesFkEntity fzgjBdOmsPagesFkEntity =fzgjBdOmsPagesFkService.save(entity);
        return ResponseMsgUtil.success(10001,fzgjBdOmsPagesFkEntity);
    }


    @ApiOperation("订单反馈页面:查询列表")
    @EciLog(title = "订单反馈页面:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjBdOmsPagesFkEntity entity){
        List<FzgjBdOmsPagesFkEntity> fzgjBdOmsPagesFkEntities = fzgjBdOmsPagesFkService.selectList(entity);
        return ResponseMsgUtil.success(10001,fzgjBdOmsPagesFkEntities);
    }


    @ApiOperation("订单反馈页面:分页查询列表")
    @EciLog(title = "订单反馈页面:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjBdOmsPagesFkEntity entity){
        TgPageInfo tgPageInfo = fzgjBdOmsPagesFkService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("订单反馈页面:根据ID查一条")
    @EciLog(title = "订单反馈页面:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjBdOmsPagesFkEntity entity){
        FzgjBdOmsPagesFkEntity  fzgjBdOmsPagesFkEntity = fzgjBdOmsPagesFkService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,fzgjBdOmsPagesFkEntity);
    }


    @ApiOperation("订单反馈页面:根据ID删除一条")
    @EciLog(title = "订单反馈页面:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjBdOmsPagesFkEntity entity){
        int count = fzgjBdOmsPagesFkService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("订单反馈页面:根据ID字符串删除多条")
    @EciLog(title = "订单反馈页面:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjBdOmsPagesFkEntity entity) {
        int count = fzgjBdOmsPagesFkService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}