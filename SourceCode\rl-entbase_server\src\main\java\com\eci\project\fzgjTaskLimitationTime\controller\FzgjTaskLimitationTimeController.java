package com.eci.project.fzgjTaskLimitationTime.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjTaskLimitationTime.service.FzgjTaskLimitationTimeService;
import com.eci.project.fzgjTaskLimitationTime.entity.FzgjTaskLimitationTimeEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 作业环节基准时效Controller
*
* @<NAME_EMAIL>
* @date 2025-05-22
*/
@Api(tags = "作业环节基准时效")
@RestController
@RequestMapping("/fzgjTaskLimitationTime")
public class FzgjTaskLimitationTimeController extends EciBaseController {

    @Autowired
    private FzgjTaskLimitationTimeService fzgjTaskLimitationTimeService;


    @ApiOperation("作业环节基准时效:保存")
    @EciLog(title = "作业环节基准时效:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody FzgjTaskLimitationTimeEntity entity){
        FzgjTaskLimitationTimeEntity fzgjTaskLimitationTimeEntity =fzgjTaskLimitationTimeService.save(entity);
        return ResponseMsgUtil.success(10001,fzgjTaskLimitationTimeEntity);
    }


    @ApiOperation("作业环节基准时效:查询列表")
    @EciLog(title = "作业环节基准时效:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjTaskLimitationTimeEntity entity){
        List<FzgjTaskLimitationTimeEntity> fzgjTaskLimitationTimeEntities = fzgjTaskLimitationTimeService.selectList(entity);
        return ResponseMsgUtil.success(10001,fzgjTaskLimitationTimeEntities);
    }


    @ApiOperation("作业环节基准时效:分页查询列表")
    @EciLog(title = "作业环节基准时效:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjTaskLimitationTimeEntity entity){
        TgPageInfo tgPageInfo = fzgjTaskLimitationTimeService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("作业环节基准时效:根据ID查一条")
    @EciLog(title = "作业环节基准时效:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjTaskLimitationTimeEntity entity){
        FzgjTaskLimitationTimeEntity  fzgjTaskLimitationTimeEntity = fzgjTaskLimitationTimeService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,fzgjTaskLimitationTimeEntity);
    }


    @ApiOperation("作业环节基准时效:根据ID删除一条")
    @EciLog(title = "作业环节基准时效:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjTaskLimitationTimeEntity entity){
        int count = fzgjTaskLimitationTimeService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("作业环节基准时效:根据ID字符串删除多条")
    @EciLog(title = "作业环节基准时效:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjTaskLimitationTimeEntity entity) {
        int count = fzgjTaskLimitationTimeService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}