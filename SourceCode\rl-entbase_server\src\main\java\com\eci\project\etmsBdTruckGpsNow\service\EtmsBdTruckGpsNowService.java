package com.eci.project.etmsBdTruckGpsNow.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.etmsBdTruckGpsNow.dao.EtmsBdTruckGpsNowDao;
import com.eci.project.etmsBdTruckGpsNow.entity.EtmsBdTruckGpsNowEntity;
import com.eci.project.etmsBdTruckGpsNow.validate.EtmsBdTruckGpsNowVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-14
*/
@Service
@Slf4j
public class EtmsBdTruckGpsNowService implements EciBaseService<EtmsBdTruckGpsNowEntity> {

    @Autowired
    private EtmsBdTruckGpsNowDao etmsBdTruckGpsNowDao;

    @Autowired
    private EtmsBdTruckGpsNowVal etmsBdTruckGpsNowVal;


    @Override
    public TgPageInfo queryPageList(EtmsBdTruckGpsNowEntity entity) {
        EciQuery<EtmsBdTruckGpsNowEntity> eciQuery = EciQuery.buildQuery(entity);
        List<EtmsBdTruckGpsNowEntity> entities = etmsBdTruckGpsNowDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public EtmsBdTruckGpsNowEntity save(EtmsBdTruckGpsNowEntity entity) {
        // 返回实体对象
        EtmsBdTruckGpsNowEntity etmsBdTruckGpsNowEntity = null;
        etmsBdTruckGpsNowVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            etmsBdTruckGpsNowEntity = etmsBdTruckGpsNowDao.insertOne(entity);

        }else{

            etmsBdTruckGpsNowEntity = etmsBdTruckGpsNowDao.updateByEntityId(entity);

        }
        return etmsBdTruckGpsNowEntity;
    }

    @Override
    public List<EtmsBdTruckGpsNowEntity> selectList(EtmsBdTruckGpsNowEntity entity) {
        return etmsBdTruckGpsNowDao.selectList(entity);
    }

    @Override
    public EtmsBdTruckGpsNowEntity selectOneById(Serializable id) {
        return etmsBdTruckGpsNowDao.selectById(id);
    }


    @Override
    public void insertBatch(List<EtmsBdTruckGpsNowEntity> list) {
        etmsBdTruckGpsNowDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return etmsBdTruckGpsNowDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return etmsBdTruckGpsNowDao.deleteById(id);
    }

}