package com.eci.project.omsOrderGoods.validate;

import com.eci.common.util.StringUtils;
import com.eci.exception.BaseException;
import com.eci.log.enums.BusinessType;
import com.eci.project.omsOrderGoods.entity.OmsOrderGoodsEntity;
import com.eci.project.omsOrderGoodsCost.entity.OmsOrderGoodsCostEntity;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 货物信息表Val接口
 * 验证接口
 *
 * @<NAME_EMAIL>
 * @date 2025-06-10
 */
@Service
public class OmsOrderGoodsVal {

    /**
     * 删除验证
     *
     * @param entity
     */
    public void deleteValidate(OmsOrderGoodsEntity entity) {

    }

    /**
     * 保存验证
     *
     * @param entity
     */
    public void saveValidate(OmsOrderGoodsEntity entity, BusinessType businessType) {

    }

    /**
     * 货物信息-保存验证
     *
     * @param entity
     */
    public void saveOmsOrderValidate(OmsOrderGoodsEntity entity, List<OmsOrderGoodsCostEntity> costList) {
        if (StringUtils.isEmpty(entity.getOrderNo()) && StringUtils.isEmpty(entity.getPreNo())) {
            throw new BaseException("OrderNo 为空 && preNo为空");
        }
        if (StringUtils.isEmpty(entity.getGoodsName())) {
            throw new BaseException("货品名称 为空");
        }
        if (entity.getWeightTotal() == null) {
            throw new BaseException("预估总毛重 为空");
        }
        if (entity.getWeightCalc() == null) {
            throw new BaseException("计费重量 为空");
        }
        if (costList.size() <= 0) {
            throw new BaseException("预估货值*币制1 为空");
        }
        if (entity.getGoodsProtety() == null) {
            throw new BaseException("货物品类 为空");
        }
    }
}
