package com.eci.project.omsOrderFwxmWorkFkCar.controller;

import com.eci.common.ResponseMsgUtilX;
import com.eci.common.util.ResponseMsg;
import com.eci.common.util.ResponseMsgUtil;
import com.eci.crud.controller.EciBaseController;
import com.eci.log.annotation.EciAction;
import com.eci.log.annotation.EciLog;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.omsOrderFwxmWorkFk.entity.OmsOrderFwxmWorkFkEntity;
import com.eci.project.omsOrderFwxmWorkFkCar.entity.OmsOrderFwxmWorkFkCarEntity;
import com.eci.project.omsOrderFwxmWorkFkCar.entity.ReqOmsOrderFwxmWorkFkCarEntity;
import com.eci.project.omsOrderFwxmWorkFkCar.service.OmsOrderFwxmWorkFkCarService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 反馈内容-车辆Controller
 *
 * @<NAME_EMAIL>
 * @date 2025-05-21
 */
@Api(tags = "反馈内容-车辆")
@RestController
@RequestMapping("/omsOrderFwxmWorkFkCar")
public class OmsOrderFwxmWorkFkCarController extends EciBaseController {

    @Autowired
    private OmsOrderFwxmWorkFkCarService omsOrderFwxmWorkFkCarService;


    @ApiOperation("反馈内容-车辆:保存")
    @EciLog(title = "反馈内容-车辆:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody OmsOrderFwxmWorkFkCarEntity entity) {
        OmsOrderFwxmWorkFkCarEntity omsOrderFwxmWorkFkCarEntity = omsOrderFwxmWorkFkCarService.save(entity);
        return ResponseMsgUtil.success(10001, omsOrderFwxmWorkFkCarEntity);
    }


    @ApiOperation("反馈内容-车辆:查询列表")
    @EciLog(title = "反馈内容-车辆:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody OmsOrderFwxmWorkFkCarEntity entity) {
        List<OmsOrderFwxmWorkFkCarEntity> omsOrderFwxmWorkFkCarEntities = omsOrderFwxmWorkFkCarService.selectList(entity);
        return ResponseMsgUtil.success(10001, omsOrderFwxmWorkFkCarEntities);
    }


    @ApiOperation("反馈内容-车辆:分页查询列表")
    @EciLog(title = "反馈内容-车辆:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody OmsOrderFwxmWorkFkCarEntity entity) {
        TgPageInfo tgPageInfo = omsOrderFwxmWorkFkCarService.queryPageList(entity);
        return ResponseMsgUtil.success(10001, tgPageInfo);
    }


    @ApiOperation("反馈内容-车辆:根据ID查一条")
    @EciLog(title = "反馈内容-车辆:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody OmsOrderFwxmWorkFkCarEntity entity) {
        OmsOrderFwxmWorkFkCarEntity omsOrderFwxmWorkFkCarEntity = omsOrderFwxmWorkFkCarService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001, omsOrderFwxmWorkFkCarEntity);
    }


    @ApiOperation("反馈内容-车辆:根据ID删除一条")
    @EciLog(title = "反馈内容-车辆:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody OmsOrderFwxmWorkFkCarEntity entity) {
        int count = omsOrderFwxmWorkFkCarService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001, count);
    }


    @ApiOperation("反馈内容-车辆:根据ID字符串删除多条")
    @EciLog(title = "反馈内容-车辆:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody OmsOrderFwxmWorkFkCarEntity entity) {
        int count = omsOrderFwxmWorkFkCarService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001, count);
    }

    @ApiOperation("反馈内容-车辆:保存")
    @EciLog(title = "反馈内容-车辆:保存", businessType = BusinessType.INSERT)
    @PostMapping("/saveOrderFwxmWorkFkCar")
    @EciAction()
    public ResponseMsg saveOrderFwxmWorkFkCar(@RequestBody String jsonString) {
        return ResponseMsgUtil.success(10001, omsOrderFwxmWorkFkCarService.saveOrderFwxmWorkFkCar(jsonString));
    }

    @ApiOperation("反馈内容表头:国外公路运输&国内公路加载")
    @EciLog(title = "反馈内容表头:国外公路运输&国内公路加载", businessType = BusinessType.DELETE)
    @PostMapping("/loadFeedBackCar")
    @EciAction()
    public ResponseMsg loadFeedBackCar(@RequestBody OmsOrderFwxmWorkFkEntity entity) {
        return ResponseMsgUtilX.success(10001, omsOrderFwxmWorkFkCarService.fkCarEditLoad(entity));
    }
}