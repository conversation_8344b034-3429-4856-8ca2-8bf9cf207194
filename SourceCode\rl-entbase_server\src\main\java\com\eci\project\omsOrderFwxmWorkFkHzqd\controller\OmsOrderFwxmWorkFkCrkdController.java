package com.eci.project.omsOrderFwxmWorkFkHzqd.controller;

import com.eci.common.ResponseMsgUtilX;
import com.eci.common.util.ResponseMsg;
import com.eci.common.util.ResponseMsgUtil;
import com.eci.crud.controller.EciBaseController;
import com.eci.log.annotation.EciAction;
import com.eci.log.annotation.EciLog;
import com.eci.log.enums.BusinessType;
import com.eci.project.omsOrderFwxmWorkFk.entity.OmsOrderFwxmWorkFkEntity;
import com.eci.project.omsOrderFwxmWorkFkHzqd.service.OmsOrderFwxmWorkFkCrkdService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "出入库单")
@RestController
@RequestMapping("/omsOrderFwxmWorkFkCrkd")
public class OmsOrderFwxmWorkFkCrkdController extends EciBaseController {

    @Autowired
    private OmsOrderFwxmWorkFkCrkdService omsOrderFwxmWorkFkCrkdService;

    @ApiOperation("反馈内容-出入库单:加载")
    @EciLog(title = "反馈内容-出入库单:加载", businessType = BusinessType.SELECT)
    @PostMapping("/loadFeedBackCrkd")
    @EciAction()
    public ResponseMsg loadFeedBackCrkd(@RequestBody OmsOrderFwxmWorkFkEntity entity){
        return ResponseMsgUtilX.success(10001,omsOrderFwxmWorkFkCrkdService.loadOrderFwxmWorkFkCrkd(entity));
    }

    @ApiOperation("反馈内容-出入库单:保存")
    @EciLog(title = "反馈内容-出入库单:保存", businessType = BusinessType.SELECT)
    @PostMapping("/saveOrderFwxmWorkFkCrkd")
    @EciAction()
    public ResponseMsg saveOrderFwxmWorkFkCrkd(@RequestBody String jsonString) {
        omsOrderFwxmWorkFkCrkdService.saveOrderFwxmWorkFkCrkd(jsonString);
        return ResponseMsgUtil.success(10001,  "保存完成");
    }

}
