package com.eci.project.omsOrderFwxmTmsXlXlLyPc.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.eci.common.Zsr;
import com.eci.common.db.DBHelper;
import com.eci.common.util.StringUtils;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.exception.BaseException;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.omsOrder.dao.OmsOrderDao;
import com.eci.project.omsOrder.entity.OmsOrderEntity;
import com.eci.project.omsOrderFwxmTmsXlXlLyPc.dao.OmsOrderFwxmTmsXlXlLyPcDao;
import com.eci.project.omsOrderFwxmTmsXlXlLyPc.entity.OmsOrderFwxmTmsXlXlLyPcDTOEntity;
import com.eci.project.omsOrderFwxmTmsXlXlLyPc.entity.OmsOrderFwxmTmsXlXlLyPcEntity;
import com.eci.project.omsOrderFwxmTmsXlXlLyPc.validate.OmsOrderFwxmTmsXlXlLyPcVal;

import com.eci.project.omsOrderFwxmWork.dao.OmsOrderFwxmWorkDao;
import com.eci.project.omsOrderFwxmWork.entity.OmsOrderFwxmWorkEntity;
import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import java.util.StringJoiner;
import java.util.stream.Collectors;


/**
* 委托内容-程运序列-陆运-拼车Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-06-05
*/
@Service
@Slf4j
public class OmsOrderFwxmTmsXlXlLyPcService implements EciBaseService<OmsOrderFwxmTmsXlXlLyPcEntity> {

    @Autowired
    private OmsOrderFwxmTmsXlXlLyPcDao omsOrderFwxmTmsXlXlLyPcDao;
    @Autowired
    private OmsOrderDao omsOrderDao;
    @Autowired
    private OmsOrderFwxmWorkDao omsOrderFwxmWorkDao;

    @Autowired
    private OmsOrderFwxmTmsXlXlLyPcVal omsOrderFwxmTmsXlXlLyPcVal;


    public TgPageInfo searchPageList(OmsOrderFwxmTmsXlXlLyPcDTOEntity entity) {
        String sql="SELECT PC.GUID,NVL( O.FWLX_CODE,P.FWLX_CODE) FWLX_CODE,\n" +
                "                          NVL( O.OP_TYPE,P.OP_TYPE) OP_TYPE,\n" +
                "                          NVL( O.PRODUCT_CODE,P.PRODUCT_CODE) PRODUCT_CODE,\n" +
                "                          NVL( O.ORDER_NO,P.PRE_NO) ORDER_NO,\n" +
                "                          PC.BP_SEQ_NO,\n" +
                "                          O.CUSTOMER_ORDER_NO,\n" +
                "                          G.GOODS_NAME  ,\n" +
                "                          G.WEIGHT_TOTAL ,\n" +
                "                          O.CONSIGNEE_CODE,\n" +
                "                          O.SHIPPER,\n" +
                "                          O.RECEIVER,\n" +
                "                          O.REQUEST_OK_DATE,\n" +
                "    O.CREATE_DATE,\n" +
                "    O.NODE_NAME,\n" +
                "    O.GROUP_CODE,\n" +
                "    O.CREATE_USER_NAME,\n" +
                "    O.COMPANY_NAME,\n" +
                "    (SELECT COUNT(1) FROM OMS_ORDER_FWXM_TMS_XL_XL_LY_PC P WHERE P.ZP_SEQ_NO  = PC.ZP_SEQ_NO AND P.GROUP_CODE = PC.GROUP_CODE ) ZP_NUM\n" +
                "FROM OMS_ORDER_FWXM_TMS_XL_XL_LY_PC PC\n" +
                "LEFT JOIN (SELECT OOO.ORDER_NO,\n" +
                "               OOO.FWLX_CODE,\n" +
                "               OOO.OP_TYPE,\n" +
                "               OOO.STATUS,\n" +
                "               OOO.PRODUCT_CODE,\n" +
                "               OOO.CUSTOMER_ORDER_NO,\n" +
                "               OOO.CONSIGNEE_CODE,\n" +
                "               OOO.SHIPPER,\n" +
                "               OOO.RECEIVER,\n" +
                "               OOO.REQUEST_OK_DATE,\n" +
                "               OOO.CREATE_DATE,\n" +
                "               OOO.NODE_NAME,\n" +
                "               OOO.GROUP_CODE,\n" +
                "               OOO.CREATE_USER_NAME,\n" +
                "               OOO.COMPANY_NAME\n" +
                "          FROM OMS_ORDER OOO\n" +
                "         WHERE EXISTS (SELECT 1\n" +
                "                  FROM OMS_ORDER_FWXM_TMS_XL_XL_LY_PC OPC\n" +
                "                 WHERE OPC.ORDER_NO = OOO.ORDER_NO AND\n" +
                "                       OPC.ORDER_NO IS NOT NULL)\n" +
                "        UNION ALL\n" +
                "        SELECT PO.PRE_NO AS ORDER_NO,\n" +
                "               PO.FWLX_CODE,\n" +
                "               PO.OP_TYPE,\n" +
                "               PO.STATUS,\n" +
                "               PO.PRODUCT_CODE,\n" +
                "               PO.CUSTOMER_ORDER_NO,\n" +
                "               PO.CONSIGNEE_CODE,\n" +
                "               PO.SHIPPER,\n" +
                "               PO.RECEIVER,\n" +
                "               PO.REQUEST_OK_DATE,\n" +
                "               PO.CREATE_DATE,\n" +
                "               PO.NODE_NAME,\n" +
                "               PO.GROUP_CODE,\n" +
                "               PO.CREATE_USER_NAME,\n" +
                "               PO.COMPANY_NAME\n" +
                "          FROM OMS_ORDER_PRE PO\n" +
                "         WHERE EXISTS\n" +
                "         (SELECT 1\n" +
                "                  FROM OMS_ORDER_FWXM_TMS_XL_XL_LY_PC OPC\n" +
                "                 WHERE OPC.PRE_NO = PO.PRE_NO AND NVL(OPC.ORDER_NO,' ')=' ')) O ON (PC.ORDER_NO = O.ORDER_NO OR PC.PRE_NO = O.ORDER_NO) AND PC.GROUP_CODE = O.GROUP_CODE\n" +
                "LEFT JOIN OMS_ORDER_FWXM_ZHYS_XL XL  ON PC.SEQ_NO = XL.SEQ_NO\n" +
                "LEFT JOIN OMS_ORDER_FWXM_TMS_XL_XL_LY LY  ON PC.LY_NO = LY.LY_NO\n" +
                "LEFT JOIN OMS_ORDER_PRE P ON P.PRE_NO=PC.PRE_NO\n" +
                "LEFT JOIN OMS_ORDER_GOODS G ON (G.PRE_NO=P.PRE_NO OR G.ORDER_NO=PC.ORDER_NO) AND G.GROUP_CODE=PC.GROUP_CODE\n" +
                "WHERE PC.GROUP_CODE ='"+ UserContext.getUserInfo().getCompanyCode() +"' AND LY.CYFS ='ZCZP'";
        if(StringUtils.hasValue(entity.getZpSeqNo())){
            sql+=" AND PC.ZP_SEQ_NO = '"+entity.getZpSeqNo()+"'";
        }
        if(entity.getIsYpc()){
            if (entity.getIsSearchZp())
            {
                sql += " AND  PC.ZP_SEQ_NO = PC.BP_SEQ_NO";
            }
            else
            {
                sql += " AND  PC.ZP_SEQ_NO != PC.BP_SEQ_NO";
            }
            if (StringUtils.hasValue(entity.getOrderNo()))
            {
                sql += " AND  EXISTS(SELECT 1 FROM OMS_ORDER_FWXM_TMS_XL_XL_LY_PC C WHERE (C.ORDER_NO = '"+entity.getOrderNo()+"' OR C.PRE_NO='"+entity.getOrderNo()+"') AND C.BP_SEQ_NO = PC.ZP_SEQ_NO AND C.GROUP_CODE =PC.GROUP_CODE) ";
            }
        }else{
            sql += " AND LY.IS_ZP = 'N' AND O.STATUS != 'ZF' AND NVL(PC.ZP_SEQ_NO,' ')=' '";
            sql += " AND EXISTS(SELECT 1 FROM OMS_ORDER_FWXM_WORK W WHERE W.WORK_NO = XL.XZRWBH AND W.STATUS = 'ZC') ";
        }
        if(entity.getCreateDateStart()!=null){
            sql+=" AND TO_CHAR(O.CREATE_DATE,'yyyy-MM-dd') >= '"+entity.getCreateDateStart()+"'";
        }
        if(entity.getCreateDateEnd()!=null){
            sql+=" AND TO_CHAR(O.CREATE_DATE,'yyyy-MM-dd') <= '"+entity.getCreateDateEnd()+"'";
        }
        if(StringUtils.hasValue(entity.getCreateUser())){
            sql+=" AND O.CREATE_USER = '"+entity.getCreateUser()+"'";
        }
        if(StringUtils.hasValue(entity.getOpType())){
            sql+=" AND O.OP_TYPE = '"+entity.getOpType()+"'";
        }
        if(StringUtils.hasValue(entity.getOrderNo())){
            sql+=" AND O.ORDER_NO LIKE '%"+entity.getOrderNo()+"%'";
        }
        if(StringUtils.hasValue(entity.getCustomerOrderNo())){
            sql+=" AND O.CUSTOMER_ORDER_NO LIKE '%"+entity.getCustomerOrderNo()+"%'";
        }
        startPage();
        List<OmsOrderFwxmTmsXlXlLyPcDTOEntity> entities =DBHelper.selectList(sql, OmsOrderFwxmTmsXlXlLyPcDTOEntity.class);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderFwxmTmsXlXlLyPcEntity save(OmsOrderFwxmTmsXlXlLyPcEntity entity) {
        // 返回实体对象
        OmsOrderFwxmTmsXlXlLyPcEntity omsOrderFwxmTmsXlXlLyPcEntity = null;
        omsOrderFwxmTmsXlXlLyPcVal.saveValidate(entity,BllContext.getBusinessType());
        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            omsOrderFwxmTmsXlXlLyPcEntity = omsOrderFwxmTmsXlXlLyPcDao.insertOne(entity);
        }else{
            omsOrderFwxmTmsXlXlLyPcEntity = omsOrderFwxmTmsXlXlLyPcDao.updateByEntityId(entity);
        }
        return omsOrderFwxmTmsXlXlLyPcEntity;
    }

    @Override
    public List<OmsOrderFwxmTmsXlXlLyPcEntity> selectList(OmsOrderFwxmTmsXlXlLyPcEntity entity) {
        return omsOrderFwxmTmsXlXlLyPcDao.selectList(entity);
    }

    @Override
    public OmsOrderFwxmTmsXlXlLyPcEntity selectOneById(Serializable id) {
        return omsOrderFwxmTmsXlXlLyPcDao.selectById(id);
    }


    @Override
    public void insertBatch(List<OmsOrderFwxmTmsXlXlLyPcEntity> list) {
        omsOrderFwxmTmsXlXlLyPcDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return omsOrderFwxmTmsXlXlLyPcDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return omsOrderFwxmTmsXlXlLyPcDao.deleteById(id);
    }

    /**
     * 拼入可拼承运序列
     * @param entity
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int addDpc(OmsOrderFwxmTmsXlXlLyPcDTOEntity entity){
        QueryWrapper<OmsOrderFwxmTmsXlXlLyPcEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("SEQ_NO",entity.getSeqNo());
        queryWrapper.eq("GROUP_CODE",UserContext.getUserInfo().getCompanyCode());
        OmsOrderFwxmTmsXlXlLyPcEntity pcEntity=omsOrderFwxmTmsXlXlLyPcDao.selectOne(queryWrapper);
        if(pcEntity==null){
            throw new BaseException("未找到相应拼车的数据");
        }
        QueryWrapper<OmsOrderEntity> queryWrapper_order = new QueryWrapper<>();
        queryWrapper_order.eq("ORDER_NO",pcEntity.getOrderNo());
        if(omsOrderDao.selectOne(queryWrapper_order)==null){
            throw new BaseException("未找到相应订单的数据");
        }
        List<String> listBzSeqNo = Arrays.asList(entity.getBpSeqNo().split(","));
        if (listBzSeqNo.size()==0)
        {
            throw new BaseException("请选择可拼承运序列");
        }
        StringJoiner joiner = new StringJoiner(", ", "", "");
        for (String element : listBzSeqNo) {
            joiner.add("'" + element + "'");
        }
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(" SELECT A.WORK_NO FROM OMS_ORDER_FWXM_ZHYS_XL A WHERE A.SEQ_NO IN ("+joiner.toString()+") AND EXISTS");
        stringBuilder.append(" (SELECT 1 FROM OMS_ORDER_FWXM_WORK W WHERE W.WORK_NO = A.XZRWBH AND W.STATUS != 'ZC') ");
        List<OmsOrderFwxmWorkEntity> listWork=DBHelper.selectList(stringBuilder.toString(), OmsOrderFwxmWorkEntity.class);
        if (listWork.size()>0)
        {
            List<String> workNoList = listWork.stream().map(OmsOrderFwxmWorkEntity::getWorkNo).collect(Collectors.toList());
            throw new BaseException("存在被拼承运序列【" + StringUtils.join(workNoList.toArray(),",") + "】不为暂存状态，操作失败！");
        }
        List<String> idList = Arrays.asList(entity.getIds().split(","));
        int row=0;
        for (int i=0;i<idList.size();i++){
            UpdateWrapper<OmsOrderFwxmTmsXlXlLyPcEntity> updateWrapper = new UpdateWrapper<>();
            updateWrapper.set("BP_SEQ_NO", listBzSeqNo.get(i));
            updateWrapper.set("ZP_SEQ_NO",pcEntity.getZpSeqNo());
            updateWrapper.eq("GUID",idList.get(i));
            row+=omsOrderFwxmTmsXlXlLyPcDao.update(null,updateWrapper);
        }
        return row;
    }

    public int cancelPc(OmsOrderFwxmTmsXlXlLyPcDTOEntity entity){
        List<String> listBzSeqNo = Arrays.asList(entity.getBpSeqNo().split(","));
        if (listBzSeqNo.size()==0)
        {
            throw new BaseException("请选择被拼承运序列");
        }
        List<String> idList = Arrays.asList(entity.getIds().split(","));
        int row=0;
        for (int i=0;i<idList.size();i++){
            UpdateWrapper<OmsOrderFwxmTmsXlXlLyPcEntity> updateWrapper = new UpdateWrapper<>();
            updateWrapper.set("BP_SEQ_NO", listBzSeqNo.get(i));
            updateWrapper.set("ZP_SEQ_NO","");
            updateWrapper.eq("GUID",idList.get(i));
            row+=omsOrderFwxmTmsXlXlLyPcDao.update(null,updateWrapper);
        }
        return row;
    }

    /**
     * 获取自拼明细数量
     * @return
     */
    public Long getZpNum(String zpSeqNo){
        QueryWrapper<OmsOrderFwxmTmsXlXlLyPcEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ZP_SEQ_NO",zpSeqNo);
        return omsOrderFwxmTmsXlXlLyPcDao.selectCount(queryWrapper);
    }

}