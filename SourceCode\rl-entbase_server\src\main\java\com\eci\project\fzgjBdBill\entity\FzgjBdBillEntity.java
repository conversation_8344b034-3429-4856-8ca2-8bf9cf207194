package com.eci.project.fzgjBdBill.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.common.DictField;
import com.eci.common.ZsrBaseEntity;
import com.eci.common.validations.ZsrValidation;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 单据类型对象 FZGJ_BD_BILL
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-03-19
*/
@ApiModel("单据类型")
@TableName("FZGJ_BD_BILL")
@FieldNameConstants
public class FzgjBdBillEntity extends ZsrBaseEntity{
    /**
    * GUID
    */
    @ApiModelProperty("GUID(72)")
    @TableId("GUID")
    private String guid;

    /**
    * 代码
    */
    @ApiModelProperty("代码(100)")
    @TableField("CODE")
    @ZsrValidation(name = "代码", required = true, length = 100)
    private String code;

    /**
    * 名称
    */
    @ApiModelProperty("名称(200)")
    @TableField("NAME")
    @ZsrValidation(name = "名称", required = true, length = 200)
    private String name;

    /**
    * 1位简码
    */
    @ApiModelProperty("1位简码(1)")
    @TableField("PRE_FIX")
    @ZsrValidation(name = "简码", required = true, length = 1)
    private String preFix;

    /**
    * 状态
    */
    @ApiModelProperty("状态(1)")
    @TableField("STATUS")
    @DictField(queryKey = "YNKey")
    @ZsrValidation(name = "是否启用", required = true, length = 1)
    private String status;

    /**
    * 顺序
    */
    @ApiModelProperty("顺序(22)")
    @TableField("SEQ")
    private Float seq;

    /**
    * 备注
    */
    @ApiModelProperty("备注(1,000)")
    @TableField("MEMO")
    private String memo;

    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(72)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 修改日期
    */
    @ApiModelProperty("修改日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("修改日期开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("修改日期结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 修改人
    */
    @ApiModelProperty("修改人(72)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * CREATE_USER
    */
    @ApiModelProperty("CREATE_USER(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * UPDATE_USER
    */
    @ApiModelProperty("UPDATE_USER(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 父级ID
    */
    @ApiModelProperty("父级ID(50)")
    @TableField("PARENTID")
    private String parentid;

    /**
    * 计算对象GUID
    */
    @ApiModelProperty("计算对象GUID(50)")
    @TableField("CLASS_GUID")
    private String classGuid;

    /**
    * 计算对象
    */
    @ApiModelProperty("计算对象(50)")
    @TableField("CLASS_CODE")
    private String classCode;

    /**
    * 接单单据
    */
    @ApiModelProperty("接单单据(1)")
    @TableField("IS_JD")
    @DictField(queryKey = "YNKey")
    @ZsrValidation(name = "接单单据", required = true, length = 1)
    private String isJd;

    /**
    * 操作单据
    */
    @ApiModelProperty("操作单据(1)")
    @TableField("IS_CZ")
    @DictField(queryKey = "YNKey")
    @ZsrValidation(name = "操作单据", required = true, length = 1)
    private String isCz;

    /**
    * 查看页面
    */
    @ApiModelProperty("查看页面(200)")
    @TableField("PAGE_VIEW")
    private String pageView;

    /**
    * 英文名称
    */
    @ApiModelProperty("英文名称(50)")
    @TableField("EN_NAME")
    private String enName;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public FzgjBdBillEntity() {
        this.setSubClazz(FzgjBdBillEntity.class);
    }

    public FzgjBdBillEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public FzgjBdBillEntity setCode(String code) {
        this.code = code;
        this.nodifySetFiled("code", code);
        return this;
    }

    public String getCode() {
        this.nodifyGetFiled("code");
        return code;
    }

    public FzgjBdBillEntity setName(String name) {
        this.name = name;
        this.nodifySetFiled("name", name);
        return this;
    }

    public String getName() {
        this.nodifyGetFiled("name");
        return name;
    }

    public FzgjBdBillEntity setPreFix(String preFix) {
        this.preFix = preFix;
        this.nodifySetFiled("preFix", preFix);
        return this;
    }

    public String getPreFix() {
        this.nodifyGetFiled("preFix");
        return preFix;
    }

    public FzgjBdBillEntity setStatus(String status) {
        this.status = status;
        this.nodifySetFiled("status", status);
        return this;
    }

    public String getStatus() {
        this.nodifyGetFiled("status");
        return status;
    }

    public FzgjBdBillEntity setSeq(Float seq) {
        this.seq = seq;
        this.nodifySetFiled("seq", seq);
        return this;
    }

    public Float getSeq() {
        this.nodifyGetFiled("seq");
        return seq;
    }

    public FzgjBdBillEntity setMemo(String memo) {
        this.memo = memo;
        this.nodifySetFiled("memo", memo);
        return this;
    }

    public String getMemo() {
        this.nodifyGetFiled("memo");
        return memo;
    }

    public FzgjBdBillEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public FzgjBdBillEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public FzgjBdBillEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public FzgjBdBillEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public FzgjBdBillEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public FzgjBdBillEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public FzgjBdBillEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public FzgjBdBillEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public FzgjBdBillEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public FzgjBdBillEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public FzgjBdBillEntity setParentid(String parentid) {
        this.parentid = parentid;
        this.nodifySetFiled("parentid", parentid);
        return this;
    }

    public String getParentid() {
        this.nodifyGetFiled("parentid");
        return parentid;
    }

    public FzgjBdBillEntity setClassGuid(String classGuid) {
        this.classGuid = classGuid;
        this.nodifySetFiled("classGuid", classGuid);
        return this;
    }

    public String getClassGuid() {
        this.nodifyGetFiled("classGuid");
        return classGuid;
    }

    public FzgjBdBillEntity setClassCode(String classCode) {
        this.classCode = classCode;
        this.nodifySetFiled("classCode", classCode);
        return this;
    }

    public String getClassCode() {
        this.nodifyGetFiled("classCode");
        return classCode;
    }

    public FzgjBdBillEntity setIsJd(String isJd) {
        this.isJd = isJd;
        this.nodifySetFiled("isJd", isJd);
        return this;
    }

    public String getIsJd() {
        this.nodifyGetFiled("isJd");
        return isJd;
    }

    public FzgjBdBillEntity setIsCz(String isCz) {
        this.isCz = isCz;
        this.nodifySetFiled("isCz", isCz);
        return this;
    }

    public String getIsCz() {
        this.nodifyGetFiled("isCz");
        return isCz;
    }

    public FzgjBdBillEntity setPageView(String pageView) {
        this.pageView = pageView;
        this.nodifySetFiled("pageView", pageView);
        return this;
    }

    public String getPageView() {
        this.nodifyGetFiled("pageView");
        return pageView;
    }

    public FzgjBdBillEntity setEnName(String enName) {
        this.enName = enName;
        this.nodifySetFiled("enName", enName);
        return this;
    }

    public String getEnName() {
        this.nodifyGetFiled("enName");
        return enName;
    }

}
