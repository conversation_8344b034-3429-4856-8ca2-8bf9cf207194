package com.eci;

import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.common.Zsr;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import com.eci.common.util.ServerStarter;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;

@SpringBootApplication
public class MainApplication {

    public static void main(String[] args) throws IOException {
        SpringApplication.run(MainApplication.class, args);
// 在项目启动时，扫描com.eci.project包下所有类，并输出到table_names.txt文件中，适合在项目开发后，检查表名，拿到表名，去对应的数据库查询，是否已经导入
//        Zsr.EntityScanner.scanAndWriteTableNames("com.eci.project", "table_names.txt");
        ServerStarter.printInfo();
    }

}
