package com.eci.project.fzgjBdProductFwxmFf.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjBdProductFwxmFf.service.FzgjBdProductFwxmFfService;
import com.eci.project.fzgjBdProductFwxmFf.entity.FzgjBdProductFwxmFfEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 不分发的服务项目Controller
*
* @<NAME_EMAIL>
* @date 2025-04-01
*/
@Api(tags = "不分发的服务项目")
@RestController
@RequestMapping("/fzgjBdProductFwxmFf")
public class FzgjBdProductFwxmFfController extends EciBaseController {

    @Autowired
    private FzgjBdProductFwxmFfService fzgjBdProductFwxmFfService;


    @ApiOperation("不分发的服务项目:保存")
    @EciLog(title = "不分发的服务项目:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody FzgjBdProductFwxmFfEntity entity){
        FzgjBdProductFwxmFfEntity fzgjBdProductFwxmFfEntity =fzgjBdProductFwxmFfService.save(entity);
        return ResponseMsgUtil.success(10001,fzgjBdProductFwxmFfEntity);
    }


    @ApiOperation("不分发的服务项目:查询列表")
    @EciLog(title = "不分发的服务项目:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjBdProductFwxmFfEntity entity){
        List<FzgjBdProductFwxmFfEntity> fzgjBdProductFwxmFfEntities = fzgjBdProductFwxmFfService.selectList(entity);
        return ResponseMsgUtil.success(10001,fzgjBdProductFwxmFfEntities);
    }


    @ApiOperation("不分发的服务项目:分页查询列表")
    @EciLog(title = "不分发的服务项目:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjBdProductFwxmFfEntity entity){
        TgPageInfo tgPageInfo = fzgjBdProductFwxmFfService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("不分发的服务项目:根据ID查一条")
    @EciLog(title = "不分发的服务项目:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjBdProductFwxmFfEntity entity){
        FzgjBdProductFwxmFfEntity  fzgjBdProductFwxmFfEntity = fzgjBdProductFwxmFfService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,fzgjBdProductFwxmFfEntity);
    }


    @ApiOperation("不分发的服务项目:根据ID删除一条")
    @EciLog(title = "不分发的服务项目:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjBdProductFwxmFfEntity entity){
        int count = fzgjBdProductFwxmFfService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("不分发的服务项目:根据ID字符串删除多条")
    @EciLog(title = "不分发的服务项目:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjBdProductFwxmFfEntity entity) {
        int count = fzgjBdProductFwxmFfService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}