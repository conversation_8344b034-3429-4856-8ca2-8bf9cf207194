package com.eci.project.fzgjCrmContractFileType.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.web.BllContext;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjCrmContractFileType.dao.FzgjCrmContractFileTypeDao;
import com.eci.project.fzgjCrmContractFileType.entity.FzgjCrmContractFileTypeEntity;
import com.eci.project.fzgjCrmContractFileType.service.IFzgjCrmContractFileTypeService;
import com.eci.project.fzgjCrmContractFileType.validate.FzgjCrmContractFileTypeVal;
import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.List;


/**
 * 合同附件类型Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-03-19
 */
@Service
@Slf4j
public class FzgjCrmContractFileTypeServiceImpl implements IFzgjCrmContractFileTypeService {
    @Autowired
    private FzgjCrmContractFileTypeDao fzgjCrmContractFileTypeDao;

    @Autowired
    private FzgjCrmContractFileTypeVal fzgjCrmContractFileTypeVal;


    @Override
    public TgPageInfo queryPageList(FzgjCrmContractFileTypeEntity entity) {
        entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
        EciQuery<FzgjCrmContractFileTypeEntity> eciQuery = EciQuery.doQuery(entity);
        List<FzgjCrmContractFileTypeEntity> entities = fzgjCrmContractFileTypeDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjCrmContractFileTypeEntity save(FzgjCrmContractFileTypeEntity entity) {
        // 返回实体对象
        FzgjCrmContractFileTypeEntity fzgjCrmContractFileTypeEntity = null;
        fzgjCrmContractFileTypeVal.saveValidate(entity, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setGuid(IdWorker.get32UUID());
            entity.setCreateDate(new java.util.Date());
            entity.setUpdateDate(new java.util.Date());
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
            entity.setNodeCode(UserContext.getUserInfo().getDeptCode());
            entity.setNodeName(UserContext.getUserInfo().getDeptName());
            entity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            entity.setCompanyName(UserContext.getUserInfo().getCompanyName());
            fzgjCrmContractFileTypeEntity = fzgjCrmContractFileTypeDao.insertOne(entity);

        } else {
            entity.setUpdateDate(new java.util.Date());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            fzgjCrmContractFileTypeEntity = fzgjCrmContractFileTypeDao.updateByEntityId(entity);

        }
        return fzgjCrmContractFileTypeEntity;
    }

    @Override
    public List<FzgjCrmContractFileTypeEntity> selectList(FzgjCrmContractFileTypeEntity entity) {
        return fzgjCrmContractFileTypeDao.selectList(entity);
    }

    @Override
    public FzgjCrmContractFileTypeEntity selectOneById(Serializable id) {
        return fzgjCrmContractFileTypeDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjCrmContractFileTypeEntity> list) {
        fzgjCrmContractFileTypeDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjCrmContractFileTypeDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjCrmContractFileTypeDao.deleteById(id);
    }

}