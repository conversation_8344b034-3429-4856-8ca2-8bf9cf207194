package com.eci.project.fzgjBdOmsPages.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;

import java.util.Date;


/**
* 录入订单编辑页面对象 FZGJ_BD_OMS_PAGES
*
* @<NAME_EMAIL>
* @date 2025-03-19
*/
@FieldNameConstants
public class FzgjBdOmsPagesBaseEntity extends EciBaseEntity
{
	/**
	* GUID
	*/
	@ApiModelProperty("GUID(36)")
	@TableId("GUID")
	private String guid;

	/**
	* 代码
	*/
	@ApiModelProperty("代码(20)")
	@TableField("CODE")
	private String code;

	/**
	* 名称
	*/
	@ApiModelProperty("名称(100)")
	@TableField("NAME")
	private String name;

	/**
	* 状态
	*/
	@ApiModelProperty("状态(1)")
	@TableField("STATUS")
	private String status;

	/**
	* 顺序
	*/
	@ApiModelProperty("顺序(22)")
	@TableField("SEQ")
	private Integer seq;

	/**
	* 备注
	*/
	@ApiModelProperty("备注(500)")
	@TableField("MEMO")
	private String memo;

	/**
	* 服务项目页面URL
	*/
	@ApiModelProperty("服务项目页面URL(50)")
	@TableField("PAGE_URL")
	private String pageUrl;

	/**
	* 集团名称
	*/
	@ApiModelProperty("集团名称(50)")
	@TableField("GROUP_NAME")
	private String groupName;

	/**
	* 集团代码
	*/
	@ApiModelProperty("集团代码(50)")
	@TableField("GROUP_CODE")
	private String groupCode;

	/**
	* 创建人
	*/
	@ApiModelProperty("创建人(20)")
	@TableField("CREATE_USER")
	private String createUser;

	/**
	* 创建日期
	*/
	@ApiModelProperty("创建日期(7)")
	@TableField("CREATE_DATE")
	private Date createDate;

	@ApiModelProperty("创建日期开始")
	@TableField(exist=false)
	private Date createDateStart;

	@ApiModelProperty("创建日期结束")
	@TableField(exist=false)
	private Date createDateEnd;

	/**
	* 修改人
	*/
	@ApiModelProperty("修改人(20)")
	@TableField("UPDATE_USER")
	private String updateUser;

	/**
	* 修改日期
	*/
	@ApiModelProperty("修改日期(7)")
	@TableField("UPDATE_DATE")
	private Date updateDate;

	@ApiModelProperty("修改日期开始")
	@TableField(exist=false)
	private Date updateDateStart;

	@ApiModelProperty("修改日期结束")
	@TableField(exist=false)
	private Date updateDateEnd;

	/**
	* 创建人
	*/
	@ApiModelProperty("创建人(50)")
	@TableField("CREATE_USER_NAME")
	private String createUserName;

	/**
	* 修改人
	*/
	@ApiModelProperty("修改人(50)")
	@TableField("UPDATE_USER_NAME")
	private String updateUserName;

	@JsonIgnore
	@TableField(exist = false)
	private Class<?> clazz = this.getClass();

	public Class<?> getClazz() {
		return clazz;
	}

	public void setClazz(Class<?> clazz) {
		this.clazz = clazz;
	}

	public FzgjBdOmsPagesBaseEntity setGuid(String guid) {
		this.guid = guid;
		return this;
	}

	public String getGuid() {
		return guid;
	}

	public FzgjBdOmsPagesBaseEntity setCode(String code) {
		this.code = code;
		return this;
	}

	public String getCode() {
		return code;
	}

	public FzgjBdOmsPagesBaseEntity setName(String name) {
		this.name = name;
		return this;
	}

	public String getName() {
		return name;
	}

	public FzgjBdOmsPagesBaseEntity setStatus(String status) {
		this.status = status;
		return this;
	}

	public String getStatus() {
		return status;
	}

	public FzgjBdOmsPagesBaseEntity setSeq(Integer seq) {
		this.seq = seq;
		return this;
	}

	public Integer getSeq() {
		return seq;
	}

	public FzgjBdOmsPagesBaseEntity setMemo(String memo) {
		this.memo = memo;
		return this;
	}

	public String getMemo() {
		return memo;
	}

	public FzgjBdOmsPagesBaseEntity setPageUrl(String pageUrl) {
		this.pageUrl = pageUrl;
		return this;
	}

	public String getPageUrl() {
		return pageUrl;
	}

	public FzgjBdOmsPagesBaseEntity setGroupName(String groupName) {
		this.groupName = groupName;
		return this;
	}

	public String getGroupName() {
		return groupName;
	}

	public FzgjBdOmsPagesBaseEntity setGroupCode(String groupCode) {
		this.groupCode = groupCode;
		return this;
	}

	public String getGroupCode() {
		return groupCode;
	}

	public FzgjBdOmsPagesBaseEntity setCreateUser(String createUser) {
		this.createUser = createUser;
		return this;
	}

	public String getCreateUser() {
		return createUser;
	}

	public FzgjBdOmsPagesBaseEntity setCreateDate(Date createDate) {
		this.createDate = createDate;
		return this;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public FzgjBdOmsPagesBaseEntity setCreateDateStart(Date createDateStart) {
		this.createDateStart = createDateStart;
		return this;
	}

	public Date getCreateDateStart() {
		return createDateStart;
	}

	public FzgjBdOmsPagesBaseEntity setCreateDateEnd(Date createDateEnd) {
		this.createDateEnd = createDateEnd;
		return this;
	}

	public Date getCreateDateEnd() {
		return createDateEnd;
	}
	public FzgjBdOmsPagesBaseEntity setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
		return this;
	}

	public String getUpdateUser() {
		return updateUser;
	}

	public FzgjBdOmsPagesBaseEntity setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
		return this;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public FzgjBdOmsPagesBaseEntity setUpdateDateStart(Date updateDateStart) {
		this.updateDateStart = updateDateStart;
		return this;
	}

	public Date getUpdateDateStart() {
		return updateDateStart;
	}

	public FzgjBdOmsPagesBaseEntity setUpdateDateEnd(Date updateDateEnd) {
		this.updateDateEnd = updateDateEnd;
		return this;
	}

	public Date getUpdateDateEnd() {
		return updateDateEnd;
	}
	public FzgjBdOmsPagesBaseEntity setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
		return this;
	}

	public String getCreateUserName() {
		return createUserName;
	}

	public FzgjBdOmsPagesBaseEntity setUpdateUserName(String updateUserName) {
		this.updateUserName = updateUserName;
		return this;
	}

	public String getUpdateUserName() {
		return updateUserName;
	}

}
