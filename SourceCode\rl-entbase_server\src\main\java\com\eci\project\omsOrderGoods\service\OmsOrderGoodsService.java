package com.eci.project.omsOrderGoods.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.DictFieldUtils;
import com.eci.common.ZsrJson;
import com.eci.common.db.DBHelper;
import com.eci.common.util.DateUtils;
import com.eci.common.util.StringUtils;
import com.eci.common.utils.CommonLib;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.exception.BaseException;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.omsOrderFwxmWorkTrace.service.OmsOrderFwxmWorkTraceZsrService;
import com.eci.project.omsOrderGoods.dao.OmsOrderGoodsDao;
import com.eci.project.omsOrderGoods.entity.OmsOrderGoodsEntity;
import com.eci.project.omsOrderGoods.entity.ResOmsOrderGoodsEntity;
import com.eci.project.omsOrderGoods.validate.OmsOrderGoodsVal;
import com.eci.project.omsOrderGoodsCost.dao.OmsOrderGoodsCostDao;
import com.eci.project.omsOrderGoodsCost.entity.OmsOrderGoodsCostEntity;
import com.eci.project.omsOrderGoodsPack.dao.OmsOrderGoodsPackDao;
import com.eci.project.omsOrderGoodsPack.entity.OmsOrderGoodsPackEntity;
import com.eci.project.omsOrderPre.dao.OmsOrderPreDao;
import com.eci.project.omsOrderPre.entity.OmsOrderPreEntity;
import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * 货物信息表Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-06-10
 */
@Service
@Slf4j
public class OmsOrderGoodsService implements EciBaseService<OmsOrderGoodsEntity> {

    CommonLib cmn = CommonLib.getInstance();

    @Autowired
    private OmsOrderGoodsDao omsOrderGoodsDao;

    @Autowired
    private OmsOrderPreDao omsOrderPreDao;

    @Autowired
    private OmsOrderGoodsVal omsOrderGoodsVal;

    @Autowired
    private OmsOrderGoodsCostDao omsOrderGoodsCostDao;
    @Autowired
    private OmsOrderGoodsPackDao omsOrderGoodsPackDao;

    @Autowired
    private OmsOrderFwxmWorkTraceZsrService omsOrderFwxmWorkTraceZsrService;


    @Override
    public TgPageInfo queryPageList(OmsOrderGoodsEntity entity) {
        EciQuery<OmsOrderGoodsEntity> eciQuery = EciQuery.buildQuery(entity);
        List<OmsOrderGoodsEntity> entities = omsOrderGoodsDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderGoodsEntity save(OmsOrderGoodsEntity entity) {
        // 返回实体对象
        OmsOrderGoodsEntity omsOrderGoodsEntity = null;
        omsOrderGoodsVal.saveValidate(entity, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            omsOrderGoodsEntity = omsOrderGoodsDao.insertOne(entity);

        } else {

            omsOrderGoodsEntity = omsOrderGoodsDao.updateByEntityId(entity);

        }
        return omsOrderGoodsEntity;
    }

    @Override
    public List<OmsOrderGoodsEntity> selectList(OmsOrderGoodsEntity entity) {
        return omsOrderGoodsDao.selectList(entity);
    }

    @Override
    public OmsOrderGoodsEntity selectOneById(Serializable id) {
        return omsOrderGoodsDao.selectById(id);
    }


    @Override
    public void insertBatch(List<OmsOrderGoodsEntity> list) {
        omsOrderGoodsDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return omsOrderGoodsDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return omsOrderGoodsDao.deleteById(id);
    }


    /**
     * 货物信息加载
     */
    public List<ResOmsOrderGoodsEntity> loadOrderGoods(OmsOrderGoodsEntity entity) {

        String sql = "SELECT\n" +
                "                             A.GUID,\n" +
                "                             A.ORDER_NO,\n" +
                "                             A.PRE_NO,\n" +
                "                             A.WEIGHT_TOTAL,\n" +
                "                             A.WEIGHT_CALC,\n" +
                "                             A.VOLUME_TOTAL,\n" +
                "                             A.GOODS_NAME,\n" +
                "                             A.GOODS_NAME,--货物属性\n" +
                "                             A.CONTRACT_NO,\n" +
                "                             A.INVOICE_NO,\n" +
                "                             A.CUSTOMER_ORDER_NO,\n" +
                "                             A.IS_GOODS_FZ,\n" +
                "                             A.IS_GOODS_FQX,\n" +
                "                             A.WD_MIN,\n" +
                "                             A.WD_MAX,\n" +
                "                             A.SD_MIN,\n" +
                "                             A.SD_MAX,\n" +
                "                             A.CREATE_USER,\n" +
                "                             A.CREATE_USER_NAME,\n" +
                "                             A.CREATE_DATE,\n" +
                "                             A.UPDATE_USER,\n" +
                "                             A.UPDATE_USER_NAME,\n" +
                "                             A.UPDATE_DATE,\n" +
                "                             A.COMPANY_CODE,\n" +
                "                             A.COMPANY_NAME ,\n" +
                "                             A.NODE_CODE,\n" +
                "                             A.NODE_NAME,\n" +
                "                             A.GROUP_CODE,\n" +
                "                             A.GROUP_NAME,\n" +
                "                             A.NET_TOTAL,A.THDH,A.JXS_CODE,A.UN_NUM,A.QTY_DT,A.QTY_XT,A.ZTS,ZFCSD,\n" +
                "                             A.HYD,\n" +
                "                             A.GOODS_PROTETY,\n" +
                "                             (SELECT GOODSATTR.NAME  FROM FZGJ_GOODS_ATTR GOODSATTR WHERE  GOODSATTR.STATUS='Y' AND GOODSATTR.GROUP_CODE=A.GROUP_CODE  AND GOODSATTR.CODE = A.GOODS_PROTETY AND ROWNUM=1) GOODS_PROTETY_NAME,\n" +
                "                             A.JGFS,\n" +
                "                            (SELECT AREA_NAME FROM V_FZGJ_BD_AREA AREA WHERE AREA.GROUP_CODE=A.GROUP_CODE AND AREA.STATUS='Y' AND AREA.AREA_CODE=A.HYD) HYD_NAME,\n" +
                "                             A.CJFS,\n" +
                "                          MB_NO,HB_NO,WAREHOUSE_IN_NO,PIECE_TOTAL\n" +
                "                         FROM OMS_ORDER_GOODS A";
        sql += "   WHERE A.GROUP_CODE =" + cmn.SQLQ(UserContext.getUserInfo().getCompanyCode());

        if (StringUtils.hasValue(entity.getOrderNo())) {
            sql += " AND  A.ORDER_NO=" + cmn.SQLQ(entity.getOrderNo());
            return DBHelper.selectList(sql, ResOmsOrderGoodsEntity.class);
        }

        if (StringUtils.hasValue(entity.getPreNo())) {
            sql += " AND  A.PRE_NO=" + cmn.SQLQ(entity.getPreNo());
            return DBHelper.selectList(sql, ResOmsOrderGoodsEntity.class);
        }

        return new ArrayList<>();
    }


    /**
     * 货物信息保存
     **/
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrderGoods(String jsonString) {
        try {
            // 解析
            ZsrJson jsonStr = ZsrJson.parse(jsonString);
            // 货物信息
            OmsOrderGoodsEntity goodsEntity = jsonStr.check("goodsEntity").getObject("goodsEntity", OmsOrderGoodsEntity.class);
            // 货值&币制信息
            List<OmsOrderGoodsCostEntity> costList = jsonStr.check("goodsList").getList("goodsList", OmsOrderGoodsCostEntity.class);
            // 货物明细信息
            List<OmsOrderGoodsPackEntity> packList = jsonStr.check("packList").getList("packList", OmsOrderGoodsPackEntity.class);

            omsOrderGoodsVal.saveOmsOrderValidate(goodsEntity, costList);

            boolean isAdd = true;
            List<OmsOrderGoodsEntity> oldGoodList = new ArrayList<>();
            if (StringUtils.hasValue(goodsEntity.getOrderNo())) {
                oldGoodList = omsOrderGoodsDao.select()
                        .eq(OmsOrderGoodsEntity::getOrderNo, goodsEntity.getOrderNo())
                        .eq(OmsOrderGoodsEntity::getCompanyCode, UserContext.getUserInfo().getCompanyCode())
                        .list();
            }
            if (StringUtils.hasValue(goodsEntity.getPreNo())) {
                oldGoodList = omsOrderGoodsDao.select()
                        .eq(OmsOrderGoodsEntity::getPreNo, goodsEntity.getPreNo())
                        .eq(OmsOrderGoodsEntity::getCompanyCode, UserContext.getUserInfo().getCompanyCode())
                        .list();
            }

            String goodsGuid = null;
            OmsOrderGoodsEntity updateGoodsEntity = new OmsOrderGoodsEntity();
            if (oldGoodList.size() > 0) {
                updateGoodsEntity = oldGoodList.get(0);
                goodsGuid = updateGoodsEntity.getGuid();
                isAdd = false;
            }

            // 转换复选框参数(防震&防倾斜)
            goodsEntity.setIsGoodsFz(StringUtils.hasValue(goodsEntity.getIsGoodsFz()) && goodsEntity.getIsGoodsFz().equals("true") ? "Y" : "N");
            goodsEntity.setIsGoodsFqx(StringUtils.hasValue(goodsEntity.getIsGoodsFqx()) && goodsEntity.getIsGoodsFqx().equals("true") ? "Y" : "N");

            if (isAdd) {
                // 新增货物信息
                goodsGuid = IdWorker.get32UUID();
                goodsEntity.setGuid(goodsGuid);
                goodsEntity.setCreateDate(DateUtils.getNowDate());
                goodsEntity.setCreateUser(UserContext.getUserInfo().getUserId());
                goodsEntity.setCreateUserName(UserContext.getUserInfo().getTrueName());
                goodsEntity.setUpdateDate(DateUtils.getNowDate());
                goodsEntity.setUpdateUser(UserContext.getUserInfo().getUserId());
                goodsEntity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
                goodsEntity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
                goodsEntity.setCompanyName(UserContext.getUserInfo().getCompanyName());
                goodsEntity.setNodeCode(UserContext.getUserInfo().getDeptCode());
                goodsEntity.setNodeName(UserContext.getUserInfo().getDeptName());
                goodsEntity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
                goodsEntity.setGroupName(UserContext.getUserInfo().getCompanyName());
                omsOrderGoodsDao.insertOne(goodsEntity);
            } else {
                // 修改货物信息
                BeanUtils.copyProperties(goodsEntity, updateGoodsEntity);
                updateGoodsEntity.setUpdateDate(DateUtils.getNowDate());
                updateGoodsEntity.setUpdateUser(UserContext.getUserInfo().getUserId());
                updateGoodsEntity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
                omsOrderGoodsDao.updateByEntityId(updateGoodsEntity);
            }


            // 货值&币制信息
            // 1-删除
            if (StringUtils.hasValue(updateGoodsEntity.getGuid())) {
                String delCosSql = "DELETE FROM OMS_ORDER_GOODS_COST WHERE GOODS_GUID=" + cmn.SQLQ(updateGoodsEntity.getGuid());
                DBHelper.execute(delCosSql);
            }

            // 2-新增
            if (costList.size() > 0) {
                for (OmsOrderGoodsCostEntity item : costList) {
                    OmsOrderGoodsCostEntity omsOrderGoodsCostEntity = new OmsOrderGoodsCostEntity();
                    omsOrderGoodsCostEntity.setGuid(IdWorker.get32UUID());
                    omsOrderGoodsCostEntity.setGoodsGuid(goodsGuid);
                    omsOrderGoodsCostEntity.setCost(item.getCost());
                    omsOrderGoodsCostEntity.setCurrency(item.getCurrency());
                    omsOrderGoodsCostEntity.setOrderNo(goodsEntity.getOrderNo());
                    omsOrderGoodsCostEntity.setPreNo(goodsEntity.getPreNo());
                    omsOrderGoodsCostEntity.setCreateDate(DateUtils.getNowDate());
                    omsOrderGoodsCostEntity.setCreateUser(UserContext.getUserInfo().getUserId());
                    omsOrderGoodsCostEntity.setCreateUserName(UserContext.getUserInfo().getTrueName());
                    omsOrderGoodsCostEntity.setUpdateDate(DateUtils.getNowDate());
                    omsOrderGoodsCostEntity.setUpdateUser(UserContext.getUserInfo().getUserId());
                    omsOrderGoodsCostEntity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
                    omsOrderGoodsCostEntity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
                    omsOrderGoodsCostEntity.setCompanyName(UserContext.getUserInfo().getCompanyName());
                    omsOrderGoodsCostEntity.setNodeCode(UserContext.getUserInfo().getDeptCode());
                    omsOrderGoodsCostEntity.setNodeName(UserContext.getUserInfo().getDeptName());
                    omsOrderGoodsCostEntity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
                    omsOrderGoodsCostEntity.setGroupName(UserContext.getUserInfo().getCompanyName());
                    omsOrderGoodsCostDao.insertOne(omsOrderGoodsCostEntity);
                }
            }

            // 货物明细信息
            // 1-删除
            if (StringUtils.hasValue(updateGoodsEntity.getGuid())) {
                String delCosSql = "DELETE FROM OMS_ORDER_GOODS_PACK WHERE GOODS_GUID=" + cmn.SQLQ(updateGoodsEntity.getGuid());
                DBHelper.execute(delCosSql);
            }
            // 2-新增
            if (packList.size() > 0) {
                for (OmsOrderGoodsPackEntity item : packList) {
                    OmsOrderGoodsPackEntity packEntity = new OmsOrderGoodsPackEntity();
                    BeanUtils.copyProperties(item, packEntity);
                    packEntity.setGuid(IdWorker.get32UUID());
                    packEntity.setGoodsGuid(goodsGuid);
                    packEntity.setOrderNo(goodsEntity.getOrderNo());
                    packEntity.setPreNo(goodsEntity.getPreNo());
                    packEntity.setCreateDate(DateUtils.getNowDate());
                    packEntity.setCreateUser(UserContext.getUserInfo().getUserId());
                    packEntity.setCreateUserName(UserContext.getUserInfo().getTrueName());
                    packEntity.setUpdateDate(DateUtils.getNowDate());
                    packEntity.setUpdateUser(UserContext.getUserInfo().getUserId());
                    packEntity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
                    packEntity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
                    packEntity.setCompanyName(UserContext.getUserInfo().getCompanyName());
                    packEntity.setNodeCode(UserContext.getUserInfo().getDeptCode());
                    packEntity.setNodeName(UserContext.getUserInfo().getDeptName());
                    packEntity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
                    packEntity.setGroupName(UserContext.getUserInfo().getCompanyName());
                    omsOrderGoodsPackDao.insertOne(packEntity);
                }
            }

//            // 协作任务
//            if(StringUtils.hasValue(goodsEntity.getOrderNo())){
//                omsOrderFwxmWorkTraceZsrService.WorkTraceAllSave(goodsEntity.getOrderNo());
//            }else if(StringUtils.hasValue(goodsEntity.getPreNo())){
//                omsOrderFwxmWorkTraceZsrService.WorkTraceAllSaveZiZhuXiaDan(goodsEntity.getPreNo());
//            }

        } catch (Exception ex) {
            log.info("货物信息新增|编辑错误：" + ex.getMessage());
            throw new BaseException(ex.getMessage());
        }

        return true;
    }
}