package com.eci.project.etmsBdDriverCertificate.entity;

import com.eci.common.Zsr;
import com.eci.common.utils.ExportUtils.Excel;
import com.eci.crud.entity.EciBaseEntity;

import java.util.Date;

public class EtmsBdDriverCertificateSearchEntity extends EciBaseEntity {
    private String guid;
    @Excel(value = "司机姓名",order = 1)
    private String name;
    @Excel(value = "联系方式",order = 2)
    private String phone;
    @Excel(value = "证件类型",order = 3)
    private String certificateType;
    @Excel(value = "证件号码",order = 4)
    private String certificateNo;
    @Excel(value = "驾照类型",order = 5)
    private String driverLicenseType;
    @Excel(value = "驾龄",order = 6)
    private String driveringYears;
    @Excel(value = "发证日期",order = 7)
    private String startDate;
    private Date startDateStart;
    private Date startDateEnd;
    @Excel(value = "有效期",order = 8)
    private String endDate;
    private Date endDateEnd;
    private Date endDateStart;
    @Excel(value = "证件状态",order = 9)
    private String status;
    @Excel(value = "自有",order = 10)
    private String isGk;
    @Excel(value = "所属业务伙伴",order = 11)
    private String partnerName;
    @Excel(value = "是否送审",order = 12)
    private String modMark;
    @Excel(value = "审核通过",order = 13)
    private String checkMark;
    @Excel(value = "创建企业",order = 14)
    private String companyName;
    @Excel(value = "创建时间",order = 15)
    private Date createDate;
    private Date createDateStart;
    private Date createDateEnd;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getCertificateType() {
        return certificateType;
    }

    public void setCertificateType(String certificateType) {
        this.certificateType = certificateType;
    }

    public String getCertificateNo() {
        return certificateNo;
    }

    public void setCertificateNo(String certificateNo) {
        this.certificateNo = certificateNo;
    }

    public String getDriverLicenseType() {
        return driverLicenseType;
    }

    public void setDriverLicenseType(String driverLicenseType) {
        this.driverLicenseType = driverLicenseType;
    }

    public String getDriveringYears() {
        return driveringYears;
    }

    public void setDriveringYears(String driveringYears) {
        this.driveringYears = driveringYears;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public Date getStartDateStart() {
        return startDateStart;
    }

    public void setStartDateStart(Date startDateStart) {
        this.startDateStart = startDateStart;
    }

    public Date getStartDateEnd() {
        return startDateEnd;
    }

    public void setStartDateEnd(Date startDateEnd) {
        this.startDateEnd = startDateEnd;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public Date getEndDateEnd() {
        return endDateEnd;
    }

    public void setEndDateEnd(Date endDateEnd) {
        this.endDateEnd = endDateEnd;
    }

    public Date getEndDateStart() {
        return endDateStart;
    }

    public void setEndDateStart(Date endDateStart) {
        this.endDateStart = endDateStart;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getIsGk() {
        return isGk;
    }

    public void setIsGk(String isGk) {
        this.isGk = isGk;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public String getModMark() {
        return modMark;
    }

    public void setModMark(String modMark) {
        this.modMark = modMark;
    }

    public String getCheckMark() {
        return checkMark;
    }

    public void setCheckMark(String checkMark) {
        this.checkMark = checkMark;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getCreateDateStart() {
        return createDateStart;
    }

    public void setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
    }

    public Date getCreateDateEnd() {
        return createDateEnd;
    }

    public void setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
    }
}
