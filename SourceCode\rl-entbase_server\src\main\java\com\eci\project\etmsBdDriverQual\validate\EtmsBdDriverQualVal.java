package com.eci.project.etmsBdDriverQual.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.etmsBdDriverQual.entity.EtmsBdDriverQualEntity;

import com.eci.sso.role.entity.UserContext;
import org.springframework.stereotype.Service;

import java.util.Date;


/**
* 司机从业资格证Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-17
*/
@Service
public class EtmsBdDriverQualVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(EtmsBdDriverQualEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(EtmsBdDriverQualEntity entity, BusinessType businessType) {
        entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        entity.setUpdateDate(new Date());
        entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
        if(businessType==BusinessType.INSERT){
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateDate(new Date());
        }
    }

}
