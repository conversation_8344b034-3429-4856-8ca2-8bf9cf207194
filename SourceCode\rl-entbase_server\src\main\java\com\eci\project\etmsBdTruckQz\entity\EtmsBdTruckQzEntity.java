package com.eci.project.etmsBdTruckQz.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.common.utils.ExportUtils.Excel;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.math.BigDecimal;
import java.util.Date;


/**
* 车辆信息对象 ETMS_BD_TRUCK_QZ
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-04-09
*/
@ApiModel("车辆信息")
@TableName("ETMS_BD_TRUCK_QZ")
@FieldNameConstants
public class EtmsBdTruckQzEntity extends EciBaseEntity{
    /**
    * GUID
    */
    @ApiModelProperty("GUID(50)")
    @TableId("GUID")
    private String guid;

    /**
    * 车辆规格GUID
    */
    @ApiModelProperty("车辆规格GUID(50)")
    @TableField("TRUCK_SPCE_GUID")
    private String truckSpceGuid;

    /**
    * 车牌号
    */
    @ApiModelProperty("车牌号(20)")
    @TableField("TRUCK_NO")
    @Excel(value = "车牌号",order = 1)
    private String truckNo;

    /**
    * 是否挂靠
    */
    @ApiModelProperty("是否挂靠(1)")
    @TableField("IS_GK")
    @Excel(value = "自有",order = 4)
    private String isGk;

    /**
    * 车主业务伙伴GUID
    */
    @ApiModelProperty("车主业务伙伴GUID(50)")
    @TableField("PARTNER_GUID")
    @Excel(value = "车主业务伙伴",order = 5)
    private String partnerGuid;

    /**
    * 定位方式
    */
    @ApiModelProperty("定位方式(20)")
    @TableField("GPS_MODE")
    @Excel(value = "定位方式",order = 6)
    private String gpsMode;

    /**
    * 定位设备编号
    */
    @ApiModelProperty("定位设备编号(40)")
    @TableField("GPS_NO")
    @Excel(value = "定位设备编号",order = 7)
    private String gpsNo;

    /**
    * 默认驾驶人GUID
    */
    @ApiModelProperty("默认驾驶人GUID(50)")
    @TableField("DRIVER_GUID")
    private String driverGuid;

    /**
    * 状态  默认空闲
    */
    @ApiModelProperty("状态  默认空闲(1)")
    @TableField("STATUS")
    private String status;

    /**
    * 创建时间
    */
    @ApiModelProperty("创建时间(7)")
    @TableField("CREATE_DATE")
    @Excel(value = "创建时间",order = 14)
    private Date createDate;

    @ApiModelProperty("创建时间开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建时间结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 创建人企业
    */
    @ApiModelProperty("创建人企业(50)")
    @TableField("CREATE_COMPANY")
    private String createCompany;

    /**
    * 修改时间
    */
    @ApiModelProperty("修改时间(7)")
    @TableField("UPDATE_DATE")
    @Excel(value = "最后修改时间",order = 17)
    private Date updateDate;

    @ApiModelProperty("修改时间开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("修改时间结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 修改人
    */
    @ApiModelProperty("修改人(50)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 备注
    */
    @ApiModelProperty("备注(200)")
    @TableField("MEMO")
    private String memo;

    /**
    * 状态启用
    */
    @ApiModelProperty("状态启用(1)")
    @TableField("IS_USER")
    @Excel(value = "启用",order = 13)
    private String isUser;

    /**
    * 理论油耗
    */
    @ApiModelProperty("理论油耗(22)")
    @TableField("LL_OIL")
    @JsonSerialize(using = ToStringSerializer.class)
    @Excel(value = "油耗系数",order = 11)
    private BigDecimal llOil;

    /**
    * 行驶证日期
    */
    @ApiModelProperty("行驶证日期(7)")
    @TableField("LICENSE_DATE")
    private Date licenseDate;

    @ApiModelProperty("行驶证日期开始")
    @TableField(exist=false)
    private Date licenseDateStart;

    @ApiModelProperty("行驶证日期结束")
    @TableField(exist=false)
    private Date licenseDateEnd;

    /**
    * 车辆定级日期
    */
    @ApiModelProperty("车辆定级日期(7)")
    @TableField("RATING_DATE")
    private Date ratingDate;

    @ApiModelProperty("车辆定级日期开始")
    @TableField(exist=false)
    private Date ratingDateStart;

    @ApiModelProperty("车辆定级日期结束")
    @TableField(exist=false)
    private Date ratingDateEnd;

    /**
    * 营运证日期
    */
    @ApiModelProperty("营运证日期(7)")
    @TableField("OPERATION_DATE")
    private Date operationDate;

    @ApiModelProperty("营运证日期开始")
    @TableField(exist=false)
    private Date operationDateStart;

    @ApiModelProperty("营运证日期结束")
    @TableField(exist=false)
    private Date operationDateEnd;

    /**
    * 货车类型
    */
    @ApiModelProperty("货车类型(20)")
    @TableField("CAR_TYPE")
    private String carType;

    /**
    * 常住地
    */
    @ApiModelProperty("常住地(50)")
    @TableField("RESIDE")
    private String reside;

    /**
    * 车源联系人
    */
    @ApiModelProperty("车源联系人(30)")
    @TableField("LINK")
    private String link;

    /**
    * 车源联系电话
    */
    @ApiModelProperty("车源联系电话(30)")
    @TableField("TEL")
    private String tel;

    /**
    * 车辆识别代号
    */
    @ApiModelProperty("车辆识别代号(30)")
    @TableField("TRUCK_VIN")
    private String truckVin;

    /**
    * 车长(米)
    */
    @ApiModelProperty("车长(米)(22)")
    @TableField("TLENGTH")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal tlength;

    /**
    * 车重（吨）
    */
    @ApiModelProperty("车重（吨）(22)")
    @TableField("TWEIGHT")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal tweight;

    @ApiModelProperty("(50)")
    @TableField("TRAILER_NO")
    private String trailerNo;

    /**
    * 车辆性质
    */
    @ApiModelProperty("车辆性质(20)")
    @TableField("ATTRIBUTE_CODE")
    private String attributeCode;

    /**
    * 修改人所属部门ID
    */
    @ApiModelProperty("修改人所属部门ID(50)")
    @TableField("ORG_DEP_ID")
    private String orgDepId;

    /**
    * 修改人所属部门CODE
    */
    @ApiModelProperty("修改人所属部门CODE(50)")
    @TableField("ORG_DEP_CODE")
    private String orgDepCode;

    /**
    * 修改人所属部门名称
    */
    @ApiModelProperty("修改人所属部门名称(50)")
    @TableField("ORG_DEP_NAME")
    @Excel(value = "最后修改人部门",order = 19)
    private String orgDepName;

    @ApiModelProperty("(50)")
    @TableField("SSO_COMPANY_GUID")
    private String ssoCompanyGuid;

    /**
    * 所属企业代码
    */
    @ApiModelProperty("所属企业代码(20)")
    @TableField("ORG_CODE")
    private String orgCode;

    /**
    * 所属企业名称
    */
    @ApiModelProperty("所属企业名称(200)")
    @TableField("ORG_NAME")
    private String orgName;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    @Excel(value = "创建人",order = 15)
    private String createUserName;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    @Excel(value = "修改人",order = 18)
    private String updateUserName;

    /**
    * 整备质量(KG)
    */
    @ApiModelProperty("整备质量(KG)(22)")
    @TableField("ZBZL_KG")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal zbzlKg;

    /**
    * 计费车辆类型
    */
    @ApiModelProperty("计费车辆类型(50)")
    @TableField("CLLX")
    private String cllx;

    /**
    * 计费车辆规格
    */
    @ApiModelProperty("计费车辆规格(50)")
    @TableField("CLCC")
    private String clcc;

    @ApiModelProperty("(10)")
    @TableField("CAR_COLOR")
    private String carColor;

    /**
    * 审批状态（ZC：暂存，SS：送审，TH：退回，SX：已生效）
    */
    @ApiModelProperty("审批状态（ZC：暂存，SS：送审，TH：退回，SX：已生效）(10)")
    @TableField("CHECK_STATUS")
    @Excel(value = "审批状态",order = 9)
    private String checkStatus;

    /**
    * 审批人
    */
    @ApiModelProperty("审批人(50)")
    @TableField("CHECK_USER")
    private String checkUser;

    /**
    * 审批意见
    */
    @ApiModelProperty("审批意见(4,000)")
    @TableField("CHECK_RMK")
    @Excel(value = "退回原因",order = 10)
    private String checkRmk;

    /**
    * 行驶证车辆类型
    */
    @ApiModelProperty("行驶证车辆类型(50)")
    @TableField("TRUCK_TYPE")
    private String truckType;

    /**
    * 车辆尺寸
    */
    @ApiModelProperty("车辆尺寸(22)")
    @TableField("CAR_LONG")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal carLong;

    /**
    * 车辆尺寸类型
    */
    @ApiModelProperty("车辆尺寸类型(50)")
    @TableField("CAR_LONG_TYPE")
    private String carLongType;

    /**
    * 是否满足补贴
    */
    @ApiModelProperty("是否满足补贴(50)")
    @TableField("IS_BT")
    private String isBt;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public EtmsBdTruckQzEntity() {
        this.setSubClazz(EtmsBdTruckQzEntity.class);
    }

    public EtmsBdTruckQzEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public EtmsBdTruckQzEntity setTruckSpceGuid(String truckSpceGuid) {
        this.truckSpceGuid = truckSpceGuid;
        this.nodifySetFiled("truckSpceGuid", truckSpceGuid);
        return this;
    }

    public String getTruckSpceGuid() {
        this.nodifyGetFiled("truckSpceGuid");
        return truckSpceGuid;
    }

    public EtmsBdTruckQzEntity setTruckNo(String truckNo) {
        this.truckNo = truckNo;
        this.nodifySetFiled("truckNo", truckNo);
        return this;
    }

    public String getTruckNo() {
        this.nodifyGetFiled("truckNo");
        return truckNo;
    }

    public EtmsBdTruckQzEntity setIsGk(String isGk) {
        this.isGk = isGk;
        this.nodifySetFiled("isGk", isGk);
        return this;
    }

    public String getIsGk() {
        this.nodifyGetFiled("isGk");
        return isGk;
    }

    public EtmsBdTruckQzEntity setPartnerGuid(String partnerGuid) {
        this.partnerGuid = partnerGuid;
        this.nodifySetFiled("partnerGuid", partnerGuid);
        return this;
    }

    public String getPartnerGuid() {
        this.nodifyGetFiled("partnerGuid");
        return partnerGuid;
    }

    public EtmsBdTruckQzEntity setGpsMode(String gpsMode) {
        this.gpsMode = gpsMode;
        this.nodifySetFiled("gpsMode", gpsMode);
        return this;
    }

    public String getGpsMode() {
        this.nodifyGetFiled("gpsMode");
        return gpsMode;
    }

    public EtmsBdTruckQzEntity setGpsNo(String gpsNo) {
        this.gpsNo = gpsNo;
        this.nodifySetFiled("gpsNo", gpsNo);
        return this;
    }

    public String getGpsNo() {
        this.nodifyGetFiled("gpsNo");
        return gpsNo;
    }

    public EtmsBdTruckQzEntity setDriverGuid(String driverGuid) {
        this.driverGuid = driverGuid;
        this.nodifySetFiled("driverGuid", driverGuid);
        return this;
    }

    public String getDriverGuid() {
        this.nodifyGetFiled("driverGuid");
        return driverGuid;
    }

    public EtmsBdTruckQzEntity setStatus(String status) {
        this.status = status;
        this.nodifySetFiled("status", status);
        return this;
    }

    public String getStatus() {
        this.nodifyGetFiled("status");
        return status;
    }

    public EtmsBdTruckQzEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public EtmsBdTruckQzEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public EtmsBdTruckQzEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public EtmsBdTruckQzEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public EtmsBdTruckQzEntity setCreateCompany(String createCompany) {
        this.createCompany = createCompany;
        this.nodifySetFiled("createCompany", createCompany);
        return this;
    }

    public String getCreateCompany() {
        this.nodifyGetFiled("createCompany");
        return createCompany;
    }

    public EtmsBdTruckQzEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public EtmsBdTruckQzEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public EtmsBdTruckQzEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public EtmsBdTruckQzEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public EtmsBdTruckQzEntity setMemo(String memo) {
        this.memo = memo;
        this.nodifySetFiled("memo", memo);
        return this;
    }

    public String getMemo() {
        this.nodifyGetFiled("memo");
        return memo;
    }

    public EtmsBdTruckQzEntity setIsUser(String isUser) {
        this.isUser = isUser;
        this.nodifySetFiled("isUser", isUser);
        return this;
    }

    public String getIsUser() {
        this.nodifyGetFiled("isUser");
        return isUser;
    }

    public EtmsBdTruckQzEntity setLlOil(BigDecimal llOil) {
        this.llOil = llOil;
        this.nodifySetFiled("llOil", llOil);
        return this;
    }

    public BigDecimal getLlOil() {
        this.nodifyGetFiled("llOil");
        return llOil;
    }

    public EtmsBdTruckQzEntity setLicenseDate(Date licenseDate) {
        this.licenseDate = licenseDate;
        this.nodifySetFiled("licenseDate", licenseDate);
        return this;
    }

    public Date getLicenseDate() {
        this.nodifyGetFiled("licenseDate");
        return licenseDate;
    }

    public EtmsBdTruckQzEntity setLicenseDateStart(Date licenseDateStart) {
        this.licenseDateStart = licenseDateStart;
        this.nodifySetFiled("licenseDateStart", licenseDateStart);
        return this;
    }

    public Date getLicenseDateStart() {
        this.nodifyGetFiled("licenseDateStart");
        return licenseDateStart;
    }

    public EtmsBdTruckQzEntity setLicenseDateEnd(Date licenseDateEnd) {
        this.licenseDateEnd = licenseDateEnd;
        this.nodifySetFiled("licenseDateEnd", licenseDateEnd);
        return this;
    }

    public Date getLicenseDateEnd() {
        this.nodifyGetFiled("licenseDateEnd");
        return licenseDateEnd;
    }
    public EtmsBdTruckQzEntity setRatingDate(Date ratingDate) {
        this.ratingDate = ratingDate;
        this.nodifySetFiled("ratingDate", ratingDate);
        return this;
    }

    public Date getRatingDate() {
        this.nodifyGetFiled("ratingDate");
        return ratingDate;
    }

    public EtmsBdTruckQzEntity setRatingDateStart(Date ratingDateStart) {
        this.ratingDateStart = ratingDateStart;
        this.nodifySetFiled("ratingDateStart", ratingDateStart);
        return this;
    }

    public Date getRatingDateStart() {
        this.nodifyGetFiled("ratingDateStart");
        return ratingDateStart;
    }

    public EtmsBdTruckQzEntity setRatingDateEnd(Date ratingDateEnd) {
        this.ratingDateEnd = ratingDateEnd;
        this.nodifySetFiled("ratingDateEnd", ratingDateEnd);
        return this;
    }

    public Date getRatingDateEnd() {
        this.nodifyGetFiled("ratingDateEnd");
        return ratingDateEnd;
    }
    public EtmsBdTruckQzEntity setOperationDate(Date operationDate) {
        this.operationDate = operationDate;
        this.nodifySetFiled("operationDate", operationDate);
        return this;
    }

    public Date getOperationDate() {
        this.nodifyGetFiled("operationDate");
        return operationDate;
    }

    public EtmsBdTruckQzEntity setOperationDateStart(Date operationDateStart) {
        this.operationDateStart = operationDateStart;
        this.nodifySetFiled("operationDateStart", operationDateStart);
        return this;
    }

    public Date getOperationDateStart() {
        this.nodifyGetFiled("operationDateStart");
        return operationDateStart;
    }

    public EtmsBdTruckQzEntity setOperationDateEnd(Date operationDateEnd) {
        this.operationDateEnd = operationDateEnd;
        this.nodifySetFiled("operationDateEnd", operationDateEnd);
        return this;
    }

    public Date getOperationDateEnd() {
        this.nodifyGetFiled("operationDateEnd");
        return operationDateEnd;
    }
    public EtmsBdTruckQzEntity setCarType(String carType) {
        this.carType = carType;
        this.nodifySetFiled("carType", carType);
        return this;
    }

    public String getCarType() {
        this.nodifyGetFiled("carType");
        return carType;
    }

    public EtmsBdTruckQzEntity setReside(String reside) {
        this.reside = reside;
        this.nodifySetFiled("reside", reside);
        return this;
    }

    public String getReside() {
        this.nodifyGetFiled("reside");
        return reside;
    }

    public EtmsBdTruckQzEntity setLink(String link) {
        this.link = link;
        this.nodifySetFiled("link", link);
        return this;
    }

    public String getLink() {
        this.nodifyGetFiled("link");
        return link;
    }

    public EtmsBdTruckQzEntity setTel(String tel) {
        this.tel = tel;
        this.nodifySetFiled("tel", tel);
        return this;
    }

    public String getTel() {
        this.nodifyGetFiled("tel");
        return tel;
    }

    public EtmsBdTruckQzEntity setTruckVin(String truckVin) {
        this.truckVin = truckVin;
        this.nodifySetFiled("truckVin", truckVin);
        return this;
    }

    public String getTruckVin() {
        this.nodifyGetFiled("truckVin");
        return truckVin;
    }

    public EtmsBdTruckQzEntity setTlength(BigDecimal tlength) {
        this.tlength = tlength;
        this.nodifySetFiled("tlength", tlength);
        return this;
    }

    public BigDecimal getTlength() {
        this.nodifyGetFiled("tlength");
        return tlength;
    }

    public EtmsBdTruckQzEntity setTweight(BigDecimal tweight) {
        this.tweight = tweight;
        this.nodifySetFiled("tweight", tweight);
        return this;
    }

    public BigDecimal getTweight() {
        this.nodifyGetFiled("tweight");
        return tweight;
    }

    public EtmsBdTruckQzEntity setTrailerNo(String trailerNo) {
        this.trailerNo = trailerNo;
        this.nodifySetFiled("trailerNo", trailerNo);
        return this;
    }

    public String getTrailerNo() {
        this.nodifyGetFiled("trailerNo");
        return trailerNo;
    }

    public EtmsBdTruckQzEntity setAttributeCode(String attributeCode) {
        this.attributeCode = attributeCode;
        this.nodifySetFiled("attributeCode", attributeCode);
        return this;
    }

    public String getAttributeCode() {
        this.nodifyGetFiled("attributeCode");
        return attributeCode;
    }

    public EtmsBdTruckQzEntity setOrgDepId(String orgDepId) {
        this.orgDepId = orgDepId;
        this.nodifySetFiled("orgDepId", orgDepId);
        return this;
    }

    public String getOrgDepId() {
        this.nodifyGetFiled("orgDepId");
        return orgDepId;
    }

    public EtmsBdTruckQzEntity setOrgDepCode(String orgDepCode) {
        this.orgDepCode = orgDepCode;
        this.nodifySetFiled("orgDepCode", orgDepCode);
        return this;
    }

    public String getOrgDepCode() {
        this.nodifyGetFiled("orgDepCode");
        return orgDepCode;
    }

    public EtmsBdTruckQzEntity setOrgDepName(String orgDepName) {
        this.orgDepName = orgDepName;
        this.nodifySetFiled("orgDepName", orgDepName);
        return this;
    }

    public String getOrgDepName() {
        this.nodifyGetFiled("orgDepName");
        return orgDepName;
    }

    public EtmsBdTruckQzEntity setSsoCompanyGuid(String ssoCompanyGuid) {
        this.ssoCompanyGuid = ssoCompanyGuid;
        this.nodifySetFiled("ssoCompanyGuid", ssoCompanyGuid);
        return this;
    }

    public String getSsoCompanyGuid() {
        this.nodifyGetFiled("ssoCompanyGuid");
        return ssoCompanyGuid;
    }

    public EtmsBdTruckQzEntity setOrgCode(String orgCode) {
        this.orgCode = orgCode;
        this.nodifySetFiled("orgCode", orgCode);
        return this;
    }

    public String getOrgCode() {
        this.nodifyGetFiled("orgCode");
        return orgCode;
    }

    public EtmsBdTruckQzEntity setOrgName(String orgName) {
        this.orgName = orgName;
        this.nodifySetFiled("orgName", orgName);
        return this;
    }

    public String getOrgName() {
        this.nodifyGetFiled("orgName");
        return orgName;
    }

    public EtmsBdTruckQzEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public EtmsBdTruckQzEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public EtmsBdTruckQzEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public EtmsBdTruckQzEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public EtmsBdTruckQzEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public EtmsBdTruckQzEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public EtmsBdTruckQzEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public EtmsBdTruckQzEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public EtmsBdTruckQzEntity setZbzlKg(BigDecimal zbzlKg) {
        this.zbzlKg = zbzlKg;
        this.nodifySetFiled("zbzlKg", zbzlKg);
        return this;
    }

    public BigDecimal getZbzlKg() {
        this.nodifyGetFiled("zbzlKg");
        return zbzlKg;
    }

    public EtmsBdTruckQzEntity setCllx(String cllx) {
        this.cllx = cllx;
        this.nodifySetFiled("cllx", cllx);
        return this;
    }

    public String getCllx() {
        this.nodifyGetFiled("cllx");
        return cllx;
    }

    public EtmsBdTruckQzEntity setClcc(String clcc) {
        this.clcc = clcc;
        this.nodifySetFiled("clcc", clcc);
        return this;
    }

    public String getClcc() {
        this.nodifyGetFiled("clcc");
        return clcc;
    }

    public EtmsBdTruckQzEntity setCarColor(String carColor) {
        this.carColor = carColor;
        this.nodifySetFiled("carColor", carColor);
        return this;
    }

    public String getCarColor() {
        this.nodifyGetFiled("carColor");
        return carColor;
    }

    public EtmsBdTruckQzEntity setCheckStatus(String checkStatus) {
        this.checkStatus = checkStatus;
        this.nodifySetFiled("checkStatus", checkStatus);
        return this;
    }

    public String getCheckStatus() {
        this.nodifyGetFiled("checkStatus");
        return checkStatus;
    }

    public EtmsBdTruckQzEntity setCheckUser(String checkUser) {
        this.checkUser = checkUser;
        this.nodifySetFiled("checkUser", checkUser);
        return this;
    }

    public String getCheckUser() {
        this.nodifyGetFiled("checkUser");
        return checkUser;
    }

    public EtmsBdTruckQzEntity setCheckRmk(String checkRmk) {
        this.checkRmk = checkRmk;
        this.nodifySetFiled("checkRmk", checkRmk);
        return this;
    }

    public String getCheckRmk() {
        this.nodifyGetFiled("checkRmk");
        return checkRmk;
    }

    public EtmsBdTruckQzEntity setTruckType(String truckType) {
        this.truckType = truckType;
        this.nodifySetFiled("truckType", truckType);
        return this;
    }

    public String getTruckType() {
        this.nodifyGetFiled("truckType");
        return truckType;
    }

    public EtmsBdTruckQzEntity setCarLong(BigDecimal carLong) {
        this.carLong = carLong;
        this.nodifySetFiled("carLong", carLong);
        return this;
    }

    public BigDecimal getCarLong() {
        this.nodifyGetFiled("carLong");
        return carLong;
    }

    public EtmsBdTruckQzEntity setCarLongType(String carLongType) {
        this.carLongType = carLongType;
        this.nodifySetFiled("carLongType", carLongType);
        return this;
    }

    public String getCarLongType() {
        this.nodifyGetFiled("carLongType");
        return carLongType;
    }

    public EtmsBdTruckQzEntity setIsBt(String isBt) {
        this.isBt = isBt;
        this.nodifySetFiled("isBt", isBt);
        return this;
    }

    public String getIsBt() {
        this.nodifyGetFiled("isBt");
        return isBt;
    }

}
