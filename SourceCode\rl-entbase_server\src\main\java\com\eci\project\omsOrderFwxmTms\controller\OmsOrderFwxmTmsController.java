package com.eci.project.omsOrderFwxmTms.controller;

import com.eci.common.ResponseMsgUtilX;
import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.omsOrderFwxmTms.entity.OrderInfoDto;
import com.eci.project.omsOrderFwxmTms.entity.QueryOrderInfoDto;
import com.eci.project.omsOrderFwxmTms.service.OmsOrderFwxmTmsService;
import com.eci.project.omsOrderFwxmTms.entity.OmsOrderFwxmTmsEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 委托内容-运输Controller
*
* @<NAME_EMAIL>
* @date 2025-04-14
*/
@Api(tags = "委托内容-运输")
@RestController
@RequestMapping("/omsOrderFwxmTms")
public class OmsOrderFwxmTmsController extends EciBaseController {

    @Autowired
    private OmsOrderFwxmTmsService omsOrderFwxmTmsService;


    @ApiOperation("委托内容-运输:保存")
    @EciLog(title = "委托内容-运输:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody OmsOrderFwxmTmsEntity entity){
        OmsOrderFwxmTmsEntity omsOrderFwxmTmsEntity =omsOrderFwxmTmsService.save(entity);
        return ResponseMsgUtil.success(10001,omsOrderFwxmTmsEntity);
    }


    @ApiOperation("委托内容-运输:查询列表")
    @EciLog(title = "委托内容-运输:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody OmsOrderFwxmTmsEntity entity){
        List<OmsOrderFwxmTmsEntity> omsOrderFwxmTmsEntities = omsOrderFwxmTmsService.selectList(entity);
        return ResponseMsgUtil.success(10001,omsOrderFwxmTmsEntities);
    }


    @ApiOperation("委托内容-运输:分页查询列表")
    @EciLog(title = "委托内容-运输:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody QueryOrderInfoDto entity) throws InterruptedException {
        TgPageInfo tgPageInfo = omsOrderFwxmTmsService.queryPageListByOrderDto(entity);
        return ResponseMsgUtilX.success(10001,tgPageInfo);
    }


    @ApiOperation("委托内容-运输:根据ID查一条")
    @EciLog(title = "委托内容-运输:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody OmsOrderFwxmTmsEntity entity){
        OmsOrderFwxmTmsEntity  omsOrderFwxmTmsEntity = omsOrderFwxmTmsService.selectOneById(entity.getTmsNo());
        return ResponseMsgUtil.success(10001,omsOrderFwxmTmsEntity);
    }


    @ApiOperation("委托内容-运输:根据ID删除一条")
    @EciLog(title = "委托内容-运输:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody OmsOrderFwxmTmsEntity entity){
        int count = omsOrderFwxmTmsService.deleteById(entity.getTmsNo());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("委托内容-运输:根据ID字符串删除多条")
    @EciLog(title = "委托内容-运输:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody OmsOrderFwxmTmsEntity entity) {
        int count = omsOrderFwxmTmsService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}