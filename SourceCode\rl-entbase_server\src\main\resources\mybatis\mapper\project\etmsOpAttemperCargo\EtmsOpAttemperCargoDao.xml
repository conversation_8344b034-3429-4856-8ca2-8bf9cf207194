<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.etmsOpAttemperCargo.dao.EtmsOpAttemperCargoDao">
    <resultMap type="EtmsOpAttemperCargoEntity" id="EtmsOpAttemperCargoResult">
        <result property="guid" column="GUID"/>
        <result property="opNo" column="OP_NO"/>
        <result property="attGuid" column="ATT_GUID"/>
        <result property="carGuid" column="CAR_GUID"/>
        <result property="startLineGuid" column="START_LINE_GUID"/>
        <result property="opSeq" column="OP_SEQ"/>
        <result property="opTask" column="OP_TASK"/>
        <result property="cargoType" column="CARGO_TYPE"/>
        <result property="cargo" column="CARGO"/>
        <result property="sku" column="SKU"/>
        <result property="deliveryWay" column="DELIVERY_WAY"/>
        <result property="boxNo" column="BOX_NO"/>
        <result property="boxType" column="BOX_TYPE"/>
        <result property="sfNo" column="SF_NO"/>
        <result property="pieces" column="PIECES"/>
        <result property="piecesUnit" column="PIECES_UNIT"/>
        <result property="weight" column="WEIGHT"/>
        <result property="weightUnit" column="WEIGHT_UNIT"/>
        <result property="volume" column="VOLUME"/>
        <result property="volumeUnit" column="VOLUME_UNIT"/>
        <result property="memo" column="MEMO"/>
        <result property="createCompany" column="CREATE_COMPANY"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="endLineGuid" column="END_LINE_GUID"/>
        <result property="startRequestDate" column="START_REQUEST_DATE"/>
        <result property="startOpRequest" column="START_OP_REQUEST"/>
        <result property="endRequestDate" column="END_REQUEST_DATE"/>
        <result property="endOpRequest" column="END_OP_REQUEST"/>
        <result property="opcargoNo" column="OPCARGO_NO"/>
        <result property="senderCode" column="SENDER_CODE"/>
        <result property="consigneeCode" column="CONSIGNEE_CODE"/>
        <result property="orgDepId" column="ORG_DEP_ID"/>
        <result property="orgDepCode" column="ORG_DEP_CODE"/>
        <result property="orgDepName" column="ORG_DEP_NAME"/>
        <result property="attNo" column="ATT_NO"/>
        <result property="opcarNo" column="OPCAR_NO"/>
        <result property="parentAttGuid" column="PARENT_ATT_GUID"/>
        <result property="parentLoadGuid" column="PARENT_LOAD_GUID"/>
        <result property="endOpTel" column="END_OP_TEL"/>
        <result property="endOpLink" column="END_OP_LINK"/>
        <result property="endOpWh" column="END_OP_WH"/>
        <result property="startOpTel" column="START_OP_TEL"/>
        <result property="startOpLink" column="START_OP_LINK"/>
        <result property="startOpWh" column="START_OP_WH"/>
        <result property="boxSize" column="BOX_SIZE"/>
        <result property="boxTypeLy" column="BOX_TYPE_LY"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="omsOrderNo" column="OMS_ORDER_NO"/>
        <result property="omsWorkNo" column="OMS_WORK_NO"/>
        <result property="bizRegId" column="BIZ_REG_ID"/>
        <result property="cargoUdf1" column="CARGO_UDF1"/>
        <result property="cargoUdf2" column="CARGO_UDF2"/>
        <result property="cargoUdf3" column="CARGO_UDF3"/>
        <result property="cargoUdf4" column="CARGO_UDF4"/>
        <result property="cargoUdf5" column="CARGO_UDF5"/>
        <result property="cargoUdf6" column="CARGO_UDF6"/>
        <result property="cargoUdf7" column="CARGO_UDF7"/>
        <result property="cargoUdf8" column="CARGO_UDF8"/>
        <result property="cargoUdf9" column="CARGO_UDF9"/>
        <result property="cargoUdf10" column="CARGO_UDF10"/>
    </resultMap>

    <sql id="selectEtmsOpAttemperCargoEntityVo">
        select
            GUID,
            OP_NO,
            ATT_GUID,
            CAR_GUID,
            START_LINE_GUID,
            OP_SEQ,
            OP_TASK,
            CARGO_TYPE,
            CARGO,
            SKU,
            DELIVERY_WAY,
            BOX_NO,
            BOX_TYPE,
            SF_NO,
            PIECES,
            PIECES_UNIT,
            WEIGHT,
            WEIGHT_UNIT,
            VOLUME,
            VOLUME_UNIT,
            MEMO,
            CREATE_COMPANY,
            CREATE_USER,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_DATE,
            END_LINE_GUID,
            START_REQUEST_DATE,
            START_OP_REQUEST,
            END_REQUEST_DATE,
            END_OP_REQUEST,
            OPCARGO_NO,
            SENDER_CODE,
            CONSIGNEE_CODE,
            ORG_DEP_ID,
            ORG_DEP_CODE,
            ORG_DEP_NAME,
            ATT_NO,
            OPCAR_NO,
            PARENT_ATT_GUID,
            PARENT_LOAD_GUID,
            END_OP_TEL,
            END_OP_LINK,
            END_OP_WH,
            START_OP_TEL,
            START_OP_LINK,
            START_OP_WH,
            BOX_SIZE,
            BOX_TYPE_LY,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            CREATE_USER_NAME,
            UPDATE_USER_NAME,
            OMS_ORDER_NO,
            OMS_WORK_NO,
            BIZ_REG_ID,
            CARGO_UDF1,
            CARGO_UDF2,
            CARGO_UDF3,
            CARGO_UDF4,
            CARGO_UDF5,
            CARGO_UDF6,
            CARGO_UDF7,
            CARGO_UDF8,
            CARGO_UDF9,
            CARGO_UDF10
        from ETMS_OP_ATTEMPER_CARGO
    </sql>
</mapper>