package com.eci.project.etmsBdTruckQz.controller;

import com.eci.common.BaseProperties;
import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.etmsBdTruckQz.entity.EtmsBdTruckQzExtendEntity;
import com.eci.project.etmsBdTruckQz.service.EtmsBdTruckQzService;
import com.eci.project.etmsBdTruckQz.entity.EtmsBdTruckQzEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.List;

/**
* 车辆信息Controller
*
* @<NAME_EMAIL>
* @date 2025-04-09
*/
@Api(tags = "车辆信息-供应商")
@RestController
@RequestMapping("/etmsBdTruckQz")
public class EtmsBdTruckQzController extends EciBaseController {

    @Autowired
    private EtmsBdTruckQzService etmsBdTruckQzService;


    @ApiOperation("车辆信息:保存")
    @EciLog(title = "车辆信息:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody EtmsBdTruckQzEntity entity){
        EtmsBdTruckQzExtendEntity etmsBdTruckQzExtendEntity =etmsBdTruckQzService.save(entity);
        return ResponseMsgUtil.successPlus(10001,etmsBdTruckQzExtendEntity);
    }


    @ApiOperation("车辆信息:查询列表")
    @EciLog(title = "车辆信息:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody EtmsBdTruckQzEntity entity){
        List<EtmsBdTruckQzEntity> etmsBdTruckQzEntities = etmsBdTruckQzService.selectList(entity);
        return ResponseMsgUtil.successPlus(10001,etmsBdTruckQzEntities);
    }


    @ApiOperation("车辆信息:分页查询列表")
    @EciLog(title = "车辆信息:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody EtmsBdTruckQzExtendEntity entity){
        TgPageInfo tgPageInfo = etmsBdTruckQzService.queryPageList(entity);
        return ResponseMsgUtil.successPlus(10001,tgPageInfo);
    }


    @ApiOperation("车辆信息:根据ID查一条")
    @EciLog(title = "车辆信息:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody EtmsBdTruckQzEntity entity){
        EtmsBdTruckQzExtendEntity  etmsBdTruckQzEntity = etmsBdTruckQzService.selectByOneId(entity.getGuid());
        return ResponseMsgUtil.successPlus(10001,etmsBdTruckQzEntity);
    }


    @ApiOperation("车辆信息:根据ID删除一条")
    @EciLog(title = "车辆信息:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody EtmsBdTruckQzEntity entity){
        int count = etmsBdTruckQzService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("车辆信息:根据ID字符串删除多条")
    @EciLog(title = "车辆信息:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody EtmsBdTruckQzEntity entity) {
        int count = etmsBdTruckQzService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }

    @ApiOperation("车辆信息:批量送审")
    @EciLog(title = "车辆信息:批量送审", businessType = BusinessType.DELETE)
    @PostMapping("/batchSubmitByIds")
    @EciAction()
    public ResponseMsg batchSubmitByIds(@RequestBody EtmsBdTruckQzEntity entity) {
        return ResponseMsgUtil.success(10001,etmsBdTruckQzService.batchSubmitByIds(entity.getIds()));
    }
}