package com.eci.project.omsOrderFwxmWorkFkHzqd.controller;

import com.eci.common.util.ResponseMsg;
import com.eci.common.util.ResponseMsgUtil;
import com.eci.crud.controller.EciBaseController;
import com.eci.log.annotation.EciAction;
import com.eci.log.annotation.EciLog;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.omsOrderFwxmWorkFkHzqd.entity.OmsOrderFwxmWorkFkHzqdEntity;
import com.eci.project.omsOrderFwxmWorkFkHzqd.entity.ReqOmsOrderFwxmWorkFkHzqdEntity;
import com.eci.project.omsOrderFwxmWorkFkHzqd.service.OmsOrderFwxmWorkFkHzqdService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 反馈内容-核注清单Controller
 *
 * @<NAME_EMAIL>
 * @date 2025-06-03
 */
@Api(tags = "反馈内容-核注清单")
@RestController
@RequestMapping("/omsOrderFwxmWorkFkHzqd")
public class OmsOrderFwxmWorkFkHzqdController extends EciBaseController {

    @Autowired
    private OmsOrderFwxmWorkFkHzqdService omsOrderFwxmWorkFkHzqdService;


    @ApiOperation("反馈内容-核注清单:保存")
    @EciLog(title = "反馈内容-核注清单:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody OmsOrderFwxmWorkFkHzqdEntity entity) {
        OmsOrderFwxmWorkFkHzqdEntity omsOrderFwxmWorkFkHzqdEntity = omsOrderFwxmWorkFkHzqdService.save(entity);
        return ResponseMsgUtil.success(10001, omsOrderFwxmWorkFkHzqdEntity);
    }


    @ApiOperation("反馈内容-核注清单:查询列表")
    @EciLog(title = "反馈内容-核注清单:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody OmsOrderFwxmWorkFkHzqdEntity entity) {
        List<OmsOrderFwxmWorkFkHzqdEntity> omsOrderFwxmWorkFkHzqdEntities = omsOrderFwxmWorkFkHzqdService.selectList(entity);
        return ResponseMsgUtil.success(10001, omsOrderFwxmWorkFkHzqdEntities);
    }


    @ApiOperation("反馈内容-核注清单:分页查询列表")
    @EciLog(title = "反馈内容-核注清单:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody OmsOrderFwxmWorkFkHzqdEntity entity) {
        TgPageInfo tgPageInfo = omsOrderFwxmWorkFkHzqdService.queryPageList(entity);
        return ResponseMsgUtil.success(10001, tgPageInfo);
    }


    @ApiOperation("反馈内容-核注清单:根据ID查一条")
    @EciLog(title = "反馈内容-核注清单:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody OmsOrderFwxmWorkFkHzqdEntity entity) {
        OmsOrderFwxmWorkFkHzqdEntity omsOrderFwxmWorkFkHzqdEntity = omsOrderFwxmWorkFkHzqdService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001, omsOrderFwxmWorkFkHzqdEntity);
    }


    @ApiOperation("反馈内容-核注清单:根据ID删除一条")
    @EciLog(title = "反馈内容-核注清单:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody OmsOrderFwxmWorkFkHzqdEntity entity) {
        int count = omsOrderFwxmWorkFkHzqdService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001, count);
    }


    @ApiOperation("反馈内容-核注清单:根据ID字符串删除多条")
    @EciLog(title = "反馈内容-核注清单:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody OmsOrderFwxmWorkFkHzqdEntity entity) {
        int count = omsOrderFwxmWorkFkHzqdService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001, count);
    }


    @ApiOperation("反馈内容-核注清单:加载")
    @EciLog(title = "反馈内容-核注清单:加载", businessType = BusinessType.SELECT)
    @PostMapping("/loadfeedBackHzqd")
    @EciAction()
    public ResponseMsg loadfeedBackHzqd(@RequestBody OmsOrderFwxmWorkFkHzqdEntity entity) {
        return ResponseMsgUtil.success(10001, omsOrderFwxmWorkFkHzqdService.feedBackHzqdLoad(entity));
    }

    @ApiOperation("反馈内容-核注清单:保存")
    @EciLog(title = "反馈内容-核注清单:保存", businessType = BusinessType.SELECT)
    @PostMapping("/saveOrderFwxmWorkFkHzqd")
    @EciAction()
    public ResponseMsg saveOrderFwxmWorkFkHzqd(@RequestBody String jsonString) {
        boolean flag = omsOrderFwxmWorkFkHzqdService.orderFwxmWorkFkHzqdSave(jsonString);
        return ResponseMsgUtil.success(10001, flag ? "保存成功" : "保存失败");
    }

}