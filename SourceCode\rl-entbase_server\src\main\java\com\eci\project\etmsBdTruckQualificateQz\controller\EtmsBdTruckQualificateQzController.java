package com.eci.project.etmsBdTruckQualificateQz.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.etmsBdTruckQualificateQz.entity.EtmsBdTruckQualificateQzSearchEntity;
import com.eci.project.etmsBdTruckQualificateQz.service.EtmsBdTruckQualificateQzService;
import com.eci.project.etmsBdTruckQualificateQz.entity.EtmsBdTruckQualificateQzEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 资质管理Controller
*
* @<NAME_EMAIL>
* @date 2025-04-25
*/
@Api(tags = "资质管理")
@RestController
@RequestMapping("/etmsBdTruckQualificateQz")
public class EtmsBdTruckQualificateQzController extends EciBaseController {

    @Autowired
    private EtmsBdTruckQualificateQzService etmsBdTruckQualificateQzService;


    @ApiOperation("资质管理:保存")
    @EciLog(title = "资质管理:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody EtmsBdTruckQualificateQzEntity entity){
        EtmsBdTruckQualificateQzEntity etmsBdTruckQualificateQzEntity =etmsBdTruckQualificateQzService.save(entity);
        return ResponseMsgUtil.success(10001,etmsBdTruckQualificateQzEntity);
    }


    @ApiOperation("资质管理:查询列表")
    @EciLog(title = "资质管理:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody EtmsBdTruckQualificateQzEntity entity){
        List<EtmsBdTruckQualificateQzEntity> etmsBdTruckQualificateQzEntities = etmsBdTruckQualificateQzService.selectList(entity);
        return ResponseMsgUtil.success(10001,etmsBdTruckQualificateQzEntities);
    }


    @ApiOperation("资质管理:分页查询列表")
    @EciLog(title = "资质管理:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody EtmsBdTruckQualificateQzEntity entity){
        TgPageInfo tgPageInfo = etmsBdTruckQualificateQzService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("资质管理:根据ID查一条")
    @EciLog(title = "资质管理:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody EtmsBdTruckQualificateQzEntity entity){
        EtmsBdTruckQualificateQzEntity  etmsBdTruckQualificateQzEntity = etmsBdTruckQualificateQzService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,etmsBdTruckQualificateQzEntity);
    }


    @ApiOperation("资质管理:根据ID删除一条")
    @EciLog(title = "资质管理:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody EtmsBdTruckQualificateQzEntity entity){
        int count = etmsBdTruckQualificateQzService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("资质管理:根据ID字符串删除多条")
    @EciLog(title = "资质管理:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody EtmsBdTruckQualificateQzEntity entity) {
        int count = etmsBdTruckQualificateQzService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }

    @ApiOperation("车辆资质管理:分页查询列表")
    @EciLog(title = "车辆资质管理:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectQualificatePageList")
    @EciAction()
    public ResponseMsg selectQualificatePageList(@RequestBody EtmsBdTruckQualificateQzSearchEntity entity){
        TgPageInfo tgPageInfo = etmsBdTruckQualificateQzService.selectQualificatePageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }
}