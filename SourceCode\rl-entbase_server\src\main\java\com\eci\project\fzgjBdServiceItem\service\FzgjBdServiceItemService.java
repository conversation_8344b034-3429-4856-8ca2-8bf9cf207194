package com.eci.project.fzgjBdServiceItem.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.eci.common.db.DBHelper;
import com.eci.common.utils.CommonLib;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjBdServiceItem.dao.FzgjBdServiceItemDao;
import com.eci.project.fzgjBdServiceItem.entity.FzgjBdServiceItemEntity;
import com.eci.project.fzgjBdServiceItem.validate.FzgjBdServiceItemVal;
import com.eci.project.fzgjBdServiceItemPt.entity.TreeModel;
import com.eci.sso.role.entity.UserContext;
import com.eci.wu.core.DataTable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;


/**
* 服务项目Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-07
*/
@Service
@Slf4j
public class FzgjBdServiceItemService implements EciBaseService<FzgjBdServiceItemEntity> {

    @Autowired
    private FzgjBdServiceItemDao fzgjBdServiceItemDao;

    @Autowired
    private FzgjBdServiceItemVal fzgjBdServiceItemVal;

    CommonLib cmn = CommonLib.getInstance();
    @Override
    public TgPageInfo queryPageList(FzgjBdServiceItemEntity entity) {
        EciQuery<FzgjBdServiceItemEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjBdServiceItemEntity> entities = fzgjBdServiceItemDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjBdServiceItemEntity save(FzgjBdServiceItemEntity entity) {
        // 返回实体对象
        FzgjBdServiceItemEntity fzgjBdServiceItemEntity = null;
        fzgjBdServiceItemVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjBdServiceItemEntity = fzgjBdServiceItemDao.insertOne(entity);

        }else{

            fzgjBdServiceItemEntity = fzgjBdServiceItemDao.updateByEntityId(entity);

        }
        return fzgjBdServiceItemEntity;
    }

    public FzgjBdServiceItemEntity saveContent(FzgjBdServiceItemEntity entity){
        entity= fzgjBdServiceItemDao.updateByEntityId(entity,"isCsc","isWb","isZyNoJd","isSave","jdFwxm","ieType","bgType","isQn");
        return entity;
    }

    public DataTable LoadRecord(String guid){
        String sql="SELECT A.GUID as \"GUID\",A.CODE AS \"CODE\",A.NAME AS \"NAME\",A.STATUS AS \"STATUS\"," +
                "A.SEQ AS \"SEQ\",C.GUID AS \"FWXM_GUID\",C.JD_FWXM AS \"JD_FWXM\",C.IE_TYPE AS \"IE_TYPE\"" +
                ",C.BG_TYPE AS \"BG_TYPE\",C.IS_QN AS \"IS_QN\"" +
                ",A.MEMO AS \"MEMO\",A.CREATE_DATE AS \"CREATE_DATE\",A.CREATE_USER AS \"CREATE_USER\"," +
                "A.UPDATE_DATE AS \"UPDATE_DATE\",A.UPDATE_USER as \"UPDATE_USER\"" +
                ",A.OWNED_COMPANY AS \"OWNED_COMPANY\",A.PARENTID AS \"PARENTID\",A.OWNED_SERVICE AS \"OWNED_SERVICE\"" +
                ",A.CREATE_USER_NAME AS \"CREATE_USER_NAME\",A.UPDATE_USER_NAME AS \"UPDATE_USER_NAME\"," +
                "A.PAGE_URL AS \"PAGE_URL\",A.SELECT_TYPE AS \"SELECT_TYPE\",A.YSFS AS \"YSFS\"" +
                ",A.SYS_CODE AS \"SYS_CODE\",A.JD_BILL AS \"JD_BILL\",A.CZ_BILL AS \"CZ_BILL\"," +
                "A.OWNED_SERVICE AS \"OWNERID\"" +
                ",(SELECT NAME FROM FZGJ_BD_SERVICE_TYPE X WHERE X.GUID=A.OWNED_SERVICE) AS \"OWNERNAME\"" +
                ",NVL((SELECT B.IS_CSC FROM FZGJ_BD_SERVICE_ITEM B WHERE B.CODE = A.CODE AND B.GROUP_CODE = %s ),'Y')  AS \"IS_CSC\"" +
                ",NVL((SELECT B.IS_WB FROM FZGJ_BD_SERVICE_ITEM B WHERE B.CODE = A.CODE AND B.GROUP_CODE = %s ),'Y' ) as \"IS_WB\"" +
                ",NVL((SELECT B.IS_ZY_NO_JD FROM FZGJ_BD_SERVICE_ITEM B WHERE B.CODE = A.CODE AND B.GROUP_CODE = %s ),'Y') AS \"IS_ZY_NO_JD\"" +
                ",NVL((SELECT B.IS_SAVE FROM FZGJ_BD_SERVICE_ITEM B WHERE B.CODE = A.CODE AND B.GROUP_CODE = %s ),'Y') AS \"IS_SAVE\"" +
                ",(SELECT NAME FROM FZGJ_BD_SERVICE_ITEM_PT X WHERE X.GUID=A.PARENTID) AS \"PARENTNAME\",A.SELECT_TYPE as \"SELECT_TYPE\"" +
                " FROM FZGJ_BD_SERVICE_ITEM_PT A " +
                " LEFT JOIN FZGJ_BD_SERVICE_ITEM C ON A.CODE = C.CODE AND C.GROUP_CODE = %s" +
                " WHERE 1=1";
        String companyCode=cmn.SQLQ(UserContext.getUserInfo().getCompanyCode());
        String temp=String.format("{0}","a");
        sql=String.format(sql,companyCode,companyCode,companyCode,companyCode,companyCode);
        sql+=String.format(" AND A.GUID=%s",cmn.SQLQ(guid));
        return DBHelper.getDataTable(sql);
    }

    /**
     * <AUTHOR>
     * @Description 用于查询服务类型的顶层数据
     * @Date  2025/3/25 11:30
     * @Param [entity]
     * @return java.util.List<com.eci.project.fzgjBdServiceItemPt.entity.TreeList>
     **/
    public List<TreeModel> selectTree(String groupCode){
        QueryWrapper query=new QueryWrapper();
        query.eq("GROUP_CODE", groupCode);
        List<TreeModel> list=fzgjBdServiceItemDao.selectTree(query);
        return list;
    }



    public List<String> getDefaultCheck(List<TreeModel> types,List<TreeModel> services){
        List<String> defaultChecked= services.stream().filter(p->p.orgChecked==1)
                .map(p->p.id).collect(Collectors.toList());
        return defaultChecked;
    }

    public void Build(List<TreeModel> types,List<TreeModel> services){
        if(types!=null&&types.size()>0){
            types.forEach(p->{
                BuildTreeData(p,services);
            });
        }
    }
    /**
     * <AUTHOR>
     * @Description 从服务项目中获取服务类型
     * @Date  2025/5/15 11:54
     * @Param []
     * @return java.util.List<com.eci.project.fzgjBdServiceItem.entity.FzgjBdServiceItemEntity>
     **/
    public List<FzgjBdServiceItemEntity> selectServiceItem(){
        QueryWrapper query=new QueryWrapper();
        query.eq("GROUP_CODE",UserContext.getUserInfo().getCompanyCode());
        query.eq("STATUS","Y");
        query.select("OWNED_SERVICE");
        query.groupBy("OWNED_SERVICE");
        return fzgjBdServiceItemDao.selectList(query);
    }

    public void BuildTreeData(TreeModel model,List<TreeModel> services){
        model.children= services.stream().filter(p->p.parentid.equals(model.id)).collect(Collectors.toList());
        if(model.children!=null&&model.children.size()>0){
            model.children.forEach(p->{
                p.parentid=model.code;
                BuildTreeData(p,services);
            });
        }
    }

    @Override
    public List<FzgjBdServiceItemEntity> selectList(FzgjBdServiceItemEntity entity) {
        return fzgjBdServiceItemDao.selectList(entity);
    }

    public List<String> selectNodes(){
        QueryWrapper query = new QueryWrapper();
        query.eq("GROUP_CODE", UserContext.getUserInfo().getCompanyCode());
        List<String> result = fzgjBdServiceItemDao.selectCheckedNodes(query);
        return result;
    }
    /**
     * <AUTHOR>
     * @Description 删除所有节点
     * @Date  2025/4/8 17:21
     * @Param []
     * @return void
     **/
    public void delNodesByCompanyCode(){
        fzgjBdServiceItemDao.delete().eq(FzgjBdServiceItemEntity::getGroupCode,UserContext.getUserInfo().getCompanyCode());
    }

    @Override
    public FzgjBdServiceItemEntity selectOneById(Serializable id) {
        return fzgjBdServiceItemDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjBdServiceItemEntity> list) {
        fzgjBdServiceItemDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjBdServiceItemDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjBdServiceItemDao.deleteById(id);
    }

    public int deleteByGroupCode(){
        QueryWrapper query=new QueryWrapper();
        query.eq("GROUP_CODE",UserContext.getUserInfo().getCompanyCode());
        return fzgjBdServiceItemDao.delete(query);
    }

    public void UpdateParentId(){
        String sql=" update FZGJ_BD_SERVICE_ITEM A \n" +
                " set A.Parentid=nvl(\n" +
                " (select GUID from FZGJ_BD_SERVICE_ITEM where GROUP_CODE=A.GROUP_CODE AND (CODE=A.ParentId or guid=A.Parentid) AND STATUS='Y'),\n" +
                " (SELECT GUID FROM FZGJ_BD_SERVICE_TYPE_COM WHERE (CODE=A.ParentId or guid=A.Parentid) and group_code=A.group_code and status='Y' )\n" +
                " ) where A.GROUP_CODE =  %s  ";
        sql=String.format(sql,cmn.SQLQ(UserContext.getUserInfo().getCompanyCode()));
        DBHelper.execute(sql);
    }
}