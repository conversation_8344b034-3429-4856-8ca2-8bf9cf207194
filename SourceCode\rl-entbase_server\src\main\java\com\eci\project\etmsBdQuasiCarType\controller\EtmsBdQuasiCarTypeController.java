package com.eci.project.etmsBdQuasiCarType.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.etmsBdQuasiCarType.service.EtmsBdQuasiCarTypeService;
import com.eci.project.etmsBdQuasiCarType.entity.EtmsBdQuasiCarTypeEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 准驾车型Controller
*
* @<NAME_EMAIL>
* @date 2025-04-16
*/
@Api(tags = "准驾车型")
@RestController
@RequestMapping("/etmsBdQuasiCarType")
public class EtmsBdQuasiCarTypeController extends EciBaseController {

    @Autowired
    private EtmsBdQuasiCarTypeService etmsBdQuasiCarTypeService;


    @ApiOperation("准驾车型:保存")
    @EciLog(title = "准驾车型:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody EtmsBdQuasiCarTypeEntity entity){
        EtmsBdQuasiCarTypeEntity etmsBdQuasiCarTypeEntity =etmsBdQuasiCarTypeService.save(entity);
        return ResponseMsgUtil.success(10001,etmsBdQuasiCarTypeEntity);
    }


    @ApiOperation("准驾车型:查询列表")
    @EciLog(title = "准驾车型:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody EtmsBdQuasiCarTypeEntity entity){
        List<EtmsBdQuasiCarTypeEntity> etmsBdQuasiCarTypeEntities = etmsBdQuasiCarTypeService.selectList(entity);
        return ResponseMsgUtil.success(10001,etmsBdQuasiCarTypeEntities);
    }


    @ApiOperation("准驾车型:分页查询列表")
    @EciLog(title = "准驾车型:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody EtmsBdQuasiCarTypeEntity entity){
        TgPageInfo tgPageInfo = etmsBdQuasiCarTypeService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("准驾车型:根据ID查一条")
    @EciLog(title = "准驾车型:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody EtmsBdQuasiCarTypeEntity entity){
        EtmsBdQuasiCarTypeEntity  etmsBdQuasiCarTypeEntity = etmsBdQuasiCarTypeService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,etmsBdQuasiCarTypeEntity);
    }


    @ApiOperation("准驾车型:根据ID删除一条")
    @EciLog(title = "准驾车型:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody EtmsBdQuasiCarTypeEntity entity){
        int count = etmsBdQuasiCarTypeService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("准驾车型:根据ID字符串删除多条")
    @EciLog(title = "准驾车型:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody EtmsBdQuasiCarTypeEntity entity) {
        int count = etmsBdQuasiCarTypeService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}