package com.eci.project.dhlWorkBasic.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.project.dhlWorkBasic.service.DhlWorkBasicService;
import com.eci.project.dhlWorkBasic.entity.DhlWorkBasicEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.EciLog;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


/**
* 运单信息Controller
*
* @<NAME_EMAIL>
* @date 2025-04-22
*/
@Api(tags = "运单信息")
@RestController
@RequestMapping("/dhlWorkBasic")
public class DhlWorkBasicController extends EciBaseController {

    @Autowired
    private DhlWorkBasicService dhlWorkBasicService;


    @ApiOperation("运单信息:保存")
    @EciLog(title = "运单信息:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    //@EciAction() //框架core,sso,component 需升级到2.2.5
    public ResponseMsg save(@RequestBody DhlWorkBasicEntity entity){
        return ResponseMsgUtil.success(10001,dhlWorkBasicService.save(entity));
    }


    @ApiOperation("运单信息:查询列表")
    @EciLog(title = "运单信息:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    //@EciAction() //框架core,sso,component 需升级到2.2.5
    public ResponseMsg selectList(@RequestBody DhlWorkBasicEntity entity){
        return ResponseMsgUtil.success(10001,dhlWorkBasicService.selectList(entity));
    }


    @ApiOperation("运单信息:分页查询列表")
    @EciLog(title = "运单信息:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    //@EciAction() //框架core,sso,component 需升级到2.2.5
    public ResponseMsg selectPageList(@RequestBody DhlWorkBasicEntity entity){
        return ResponseMsgUtil.success(10001,dhlWorkBasicService.queryPageList(entity));
    }


    @ApiOperation("运单信息:根据ID查一条")
    @EciLog(title = "运单信息:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    //@EciAction() //框架core,sso,component 需升级到2.2.5
    public ResponseMsg selectOneById(@RequestBody DhlWorkBasicEntity entity){
        return ResponseMsgUtil.success(10001,dhlWorkBasicService.selectOneById(entity.getGuid()));
    }


    @ApiOperation("运单信息:根据ID删除一条")
    @EciLog(title = "运单信息:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    //@EciAction() //框架core,sso,component 需升级到2.2.5
    public ResponseMsg deleteById(@RequestBody DhlWorkBasicEntity entity){
        return ResponseMsgUtil.success(10001,dhlWorkBasicService.deleteById(entity.getGuid()));
    }


    @ApiOperation("运单信息:根据ID字符串删除多条")
    @EciLog(title = "运单信息:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    //@EciAction() //框架core,sso,component 需升级到2.2.5
    public ResponseMsg deleteByIds(@RequestBody DhlWorkBasicEntity entity) {
        return ResponseMsgUtil.success(10001, dhlWorkBasicService.deleteByIds(entity.getIds()));
    }


}