package com.eci.project.omsOrderFwxmTmsXl.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.omsOrderFwxmTmsXl.service.OmsOrderFwxmTmsXlService;
import com.eci.project.omsOrderFwxmTmsXl.entity.OmsOrderFwxmTmsXlEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 委托内容-委托线路Controller
*
* @<NAME_EMAIL>
* @date 2025-04-14
*/
@Api(tags = "委托内容-委托线路")
@RestController
@RequestMapping("/omsOrderFwxmTmsXl")
public class OmsOrderFwxmTmsXlController extends EciBaseController {

    @Autowired
    private OmsOrderFwxmTmsXlService omsOrderFwxmTmsXlService;


    @ApiOperation("委托内容-委托线路:保存")
    @EciLog(title = "委托内容-委托线路:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody OmsOrderFwxmTmsXlEntity entity){
        OmsOrderFwxmTmsXlEntity omsOrderFwxmTmsXlEntity =omsOrderFwxmTmsXlService.save(entity);
        return ResponseMsgUtil.success(10001,omsOrderFwxmTmsXlEntity);
    }


    @ApiOperation("委托内容-委托线路:查询列表")
    @EciLog(title = "委托内容-委托线路:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody OmsOrderFwxmTmsXlEntity entity){
        List<OmsOrderFwxmTmsXlEntity> omsOrderFwxmTmsXlEntities = omsOrderFwxmTmsXlService.selectList(entity);
        return ResponseMsgUtil.success(10001,omsOrderFwxmTmsXlEntities);
    }


    @ApiOperation("委托内容-委托线路:分页查询列表")
    @EciLog(title = "委托内容-委托线路:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody OmsOrderFwxmTmsXlEntity entity){
        TgPageInfo tgPageInfo = omsOrderFwxmTmsXlService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("委托内容-委托线路:根据ID查一条")
    @EciLog(title = "委托内容-委托线路:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody OmsOrderFwxmTmsXlEntity entity){
        OmsOrderFwxmTmsXlEntity  omsOrderFwxmTmsXlEntity = omsOrderFwxmTmsXlService.selectOneById(entity.getLineNo());
        return ResponseMsgUtil.success(10001,omsOrderFwxmTmsXlEntity);
    }


    @ApiOperation("委托内容-委托线路:根据ID删除一条")
    @EciLog(title = "委托内容-委托线路:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody OmsOrderFwxmTmsXlEntity entity){
        int count = omsOrderFwxmTmsXlService.deleteById(entity.getLineNo());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("委托内容-委托线路:根据ID字符串删除多条")
    @EciLog(title = "委托内容-委托线路:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody OmsOrderFwxmTmsXlEntity entity) {
        int count = omsOrderFwxmTmsXlService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}