package com.eci.project.etmsBdTruckQz.dao;

import com.eci.crud.dao.EciBaseDao;
import com.eci.project.etmsBdTruckQz.entity.EtmsBdTruckQzEntity;
import com.eci.project.etmsBdTruckQz.entity.EtmsBdTruckQzExtendEntity;

import java.util.List;

public interface EtmsBdTruckQzExtendDao extends EciBaseDao<EtmsBdTruckQzExtendEntity> {
    List<EtmsBdTruckQzExtendEntity> selectListInfo(EtmsBdTruckQzExtendEntity entity);
    EtmsBdTruckQzExtendEntity selectByOneId(String id);
}
