package com.eci.project.etmsTruckOrderPj.entity;

import lombok.Data;

/**
 * <Description>TODO <br>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/22$
 */
@Data
public class orderPj {
    /**
     * <AUTHOR>
     * @Description 考评时间
     * @Date  2025/4/22 15:37
     * @Param 
     * @return 
     **/
    public String updatedate;
    /**
     * <AUTHOR>
     * @Description 本次综合评分
     * @Date  2025/4/22 15:37
     * @Param
     * @return
     **/
    public String bczhpf;
    /**
     * <AUTHOR>
     * @Description 评分详情
     * @Date  2025/4/22 15:38
     * @Param
     * @return
     **/
    public String pfxq;
    /**
     * <AUTHOR>
     * @Description 派车单号
     * @Date  2025/4/22 15:38
     * @Param
     * @return
     **/
    public String orderno;
    /**
     * <AUTHOR>
     * @Description 备注
     * @Date  2025/4/22 15:38
     * @Param
     * @return
     **/
    public String remark;
    /**
     * <AUTHOR>
     * @Description 考评人
     * @Date  2025/4/22 15:38
     * @Param
     * @return
     **/
    public String loginno;
}
