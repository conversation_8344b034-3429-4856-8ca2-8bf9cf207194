package com.eci.project.omsIBillStatus.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.omsIBillStatus.service.OmsIBillStatusService;
import com.eci.project.omsIBillStatus.entity.OmsIBillStatusEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 单据状态Controller
*
* @<NAME_EMAIL>
* @date 2025-05-16
*/
@Api(tags = "单据状态")
@RestController
@RequestMapping("/omsIBillStatus")
public class OmsIBillStatusController extends EciBaseController {

    @Autowired
    private OmsIBillStatusService omsIBillStatusService;


    @ApiOperation("单据状态:保存")
    @EciLog(title = "单据状态:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody OmsIBillStatusEntity entity){
        OmsIBillStatusEntity omsIBillStatusEntity =omsIBillStatusService.save(entity);
        return ResponseMsgUtil.success(10001,omsIBillStatusEntity);
    }


    @ApiOperation("单据状态:查询列表")
    @EciLog(title = "单据状态:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody OmsIBillStatusEntity entity){
        List<OmsIBillStatusEntity> omsIBillStatusEntities = omsIBillStatusService.selectList(entity);
        return ResponseMsgUtil.success(10001,omsIBillStatusEntities);
    }


    @ApiOperation("单据状态:分页查询列表")
    @EciLog(title = "单据状态:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody OmsIBillStatusEntity entity){
        TgPageInfo tgPageInfo = omsIBillStatusService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("单据状态:根据ID查一条")
    @EciLog(title = "单据状态:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody OmsIBillStatusEntity entity){
        OmsIBillStatusEntity  omsIBillStatusEntity = omsIBillStatusService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,omsIBillStatusEntity);
    }


    @ApiOperation("单据状态:根据ID删除一条")
    @EciLog(title = "单据状态:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody OmsIBillStatusEntity entity){
        int count = omsIBillStatusService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("单据状态:根据ID字符串删除多条")
    @EciLog(title = "单据状态:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody OmsIBillStatusEntity entity) {
        int count = omsIBillStatusService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}