package com.eci.project.fzgjException.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.fzgjException.entity.FzgjExceptionEntity;


/**
* 订单作业异常Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-04-25
*/
public interface FzgjExceptionDao extends EciBaseDao<FzgjExceptionEntity> {

}