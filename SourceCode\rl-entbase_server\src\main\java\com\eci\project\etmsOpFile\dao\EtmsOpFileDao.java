package com.eci.project.etmsOpFile.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.etmsOpFile.entity.EtmsOpFileEntity;


/**
* 业务附件Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-04-22
*/
public interface EtmsOpFileDao extends EciBaseDao<EtmsOpFileEntity> {

}