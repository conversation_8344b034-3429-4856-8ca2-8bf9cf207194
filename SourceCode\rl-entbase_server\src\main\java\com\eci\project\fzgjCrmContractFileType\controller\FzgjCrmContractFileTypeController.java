package com.eci.project.fzgjCrmContractFileType.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.project.fzgjCrmContractFileType.service.IFzgjCrmContractFileTypeService;
import com.eci.project.fzgjCrmContractFileType.entity.FzgjCrmContractFileTypeEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.EciLog;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


/**
* 合同附件类型Controller
*
* @<NAME_EMAIL>
* @date 2025-03-19
*/
@Api(tags = "合同附件类型")
@RestController
@RequestMapping("/fzgjCrmContractFileType")
public class FzgjCrmContractFileTypeController extends EciBaseController {

    @Autowired
    private IFzgjCrmContractFileTypeService fzgjCrmContractFileTypeService;

    /**
    * 根据实体插入数据库
    * @param entity
    * @return
    */
    @ApiOperation("合同附件类型:保存")
    @EciLog(title = "合同附件类型:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public ResponseMsg save(@RequestBody FzgjCrmContractFileTypeEntity entity){
        return ResponseMsgUtil.success(10001,fzgjCrmContractFileTypeService.save(entity));
    }

    /**
    * 根据条件查询一个集合
    * @param entity
    * @return
    */
    @ApiOperation("合同附件类型:查询列表")
    @EciLog(title = "合同附件类型:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    public ResponseMsg selectList(@RequestBody FzgjCrmContractFileTypeEntity entity){
        return ResponseMsgUtil.success(10001,fzgjCrmContractFileTypeService.selectList(entity));
    }

    /**
    * 分页查询
    * @param entity
    * @return
    */
    @ApiOperation("合同附件类型:分页查询列表")
    @EciLog(title = "合同附件类型:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    public ResponseMsg selectPageList(@RequestBody FzgjCrmContractFileTypeEntity entity){
        return ResponseMsgUtil.successPlus(10001,fzgjCrmContractFileTypeService.queryPageList(entity));
    }

    /**
    * 根据ID查一条记录
    * @param entity
    * @return
    */
    @ApiOperation("合同附件类型:根据ID查一条")
    @EciLog(title = "合同附件类型:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    public ResponseMsg selectOneById(@RequestBody FzgjCrmContractFileTypeEntity entity){
        return ResponseMsgUtil.success(10001,fzgjCrmContractFileTypeService.selectOneById(entity.getGuid()));
    }


    /**
    * 根据ID删除
    * @param entity
    * @return
    */
    @ApiOperation("合同附件类型:根据ID删除一条")
    @EciLog(title = "合同附件类型:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    public ResponseMsg deleteById(@RequestBody FzgjCrmContractFileTypeEntity entity){
        return ResponseMsgUtil.success(10001,fzgjCrmContractFileTypeService.deleteById(entity.getGuid()));
    }

    /**
    * 根据IDS删除
    *
    * @param entity
    * @return
    */
    @ApiOperation("合同附件类型:根据ID字符串删除多条")
    @EciLog(title = "合同附件类型:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    public ResponseMsg deleteByIds(@RequestBody FzgjCrmContractFileTypeEntity entity) {
        return ResponseMsgUtil.success(10001, fzgjCrmContractFileTypeService.deleteByIds(entity.getIds()));
    }


}