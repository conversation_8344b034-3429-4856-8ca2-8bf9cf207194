package com.eci.project.fzgjBdArea.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.common.Zsr;
import com.eci.crud.entity.EciBaseEntity;
import com.eci.project.fzgjBdTruckSpecCom.entity.FzgjBdTruckSpecComBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;


/**
* 公路乡镇地区对象 FZGJ_BD_AREA
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-03-26
*/
@ApiModel("公路乡镇地区")
public class FzgjBdAreaEntity extends FzgjBdAreaBaseEntity{

    private String districtCode;
    public FzgjBdAreaEntity setDistrictCode(String districtCode) {
        this.districtCode = districtCode;
        return this;
    }
    public String getDistrictCode() {
        return districtCode;
    }
    private  String districtName;
    public FzgjBdAreaEntity setDistrictName(String districtName) {
        this.districtName = districtName;
        return this;
    }
    public String getDistrictName() {
        return districtName;
    }
    private String city;
    public FzgjBdAreaEntity setCiity(String city) {
        this.city = city;
        return this;
    }

    public String getCity() {
        return city;
    }

    private String province;

    public FzgjBdAreaEntity setProvince(String province) {
        this.province = province;
        return this;
    }

    public String getProvince() {
        return province;
    }

    private String country;

    public FzgjBdAreaEntity setCountry(String country) {
        this.country = country;
        return this;
    }

    public String getCountry() {
        return country;
    }

    @Override
    protected void addConvertMap() {
        convertMap.put(FzgjBdAreaBaseEntity.Fields.status, () -> "YNKey");//是否启用
    }
}
