package com.eci.project.crmCustomerKhsyb.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.crmCustomerKhsyb.dao.CrmCustomerKhsybDao;
import com.eci.project.crmCustomerKhsyb.entity.CrmCustomerKhsybEntity;
import com.eci.project.crmCustomerKhsyb.validate.CrmCustomerKhsybVal;

import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 客户事业部Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-05-20
*/
@Service
@Slf4j
public class CrmCustomerKhsybService implements EciBaseService<CrmCustomerKhsybEntity> {

    @Autowired
    private CrmCustomerKhsybDao crmCustomerKhsybDao;

    @Autowired
    private CrmCustomerKhsybVal crmCustomerKhsybVal;


    @Override
    public TgPageInfo queryPageList(CrmCustomerKhsybEntity entity) {
        EciQuery<CrmCustomerKhsybEntity> eciQuery = EciQuery.buildQuery(entity);
        List<CrmCustomerKhsybEntity> entities = crmCustomerKhsybDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public CrmCustomerKhsybEntity save(CrmCustomerKhsybEntity entity) {
        // 返回实体对象
        CrmCustomerKhsybEntity crmCustomerKhsybEntity = null;
        crmCustomerKhsybVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            crmCustomerKhsybEntity = crmCustomerKhsybDao.insertOne(entity);

        }else{

            crmCustomerKhsybEntity = crmCustomerKhsybDao.updateByEntityId(entity);

        }
        return crmCustomerKhsybEntity;
    }

    @Override
    public List<CrmCustomerKhsybEntity> selectList(CrmCustomerKhsybEntity entity) {
        return crmCustomerKhsybDao.selectList(entity);
    }

    @Override
    public CrmCustomerKhsybEntity selectOneById(Serializable id) {
        return crmCustomerKhsybDao.selectById(id);
    }


    @Override
    public void insertBatch(List<CrmCustomerKhsybEntity> list) {
        crmCustomerKhsybDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return crmCustomerKhsybDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return crmCustomerKhsybDao.deleteById(id);
    }

    public boolean Exist(String value,String guid,boolean isCode){
        QueryWrapper query=new QueryWrapper();
        query.eq("GROUP_CODE", UserContext.getUserInfo().getCompanyCode());
        if(isCode)
            query.eq("CODE",value);
        else
            query.eq("NAME",value);
        if(guid!=null&&guid.isEmpty())
            query.ne("GUID",guid);
        return crmCustomerKhsybDao.exists(query);
    }

}