package com.eci.project.fzgjBdServiceItemPt.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.eci.project.fzgjBdServiceItemPt.entity.TreeModel;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.fzgjBdServiceItemPt.entity.FzgjBdServiceItemPtEntity;

import java.util.List;


/**
* 平台级服务项目Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-03-25
*/
public interface FzgjBdServiceItemPtDao extends EciBaseDao<FzgjBdServiceItemPtEntity> {
    List<TreeModel> selectTree(@Param(Constants.WRAPPER) Wrapper queryWrapper);
    FzgjBdServiceItemPtEntity selectwithparent(@Param(Constants.WRAPPER) Wrapper queryWrapper);
}