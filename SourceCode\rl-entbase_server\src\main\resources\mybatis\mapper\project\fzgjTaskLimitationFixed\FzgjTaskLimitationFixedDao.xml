<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.fzgjTaskLimitationFixed.dao.FzgjTaskLimitationFixedDao">
    <resultMap type="FzgjTaskLimitationFixedEntity" id="FzgjTaskLimitationFixedResult">
        <result property="guid" column="GUID"/>
        <result property="limitationGuid" column="LIMITATION_GUID"/>
        <result property="lineNo" column="LINE_NO"/>
        <result property="elementCode" column="ELEMENT_CODE"/>
        <result property="elementCodeName" column="ELEMENT_CODE_NAME"/>
        <result property="logical" column="LOGICAL"/>
        <result property="elementValue" column="ELEMENT_VALUE"/>
        <result property="elementValueName" column="ELEMENT_VALUE_NAME"/>
        <result property="memo" column="MEMO"/>
        <result property="seq" column="SEQ"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="limitationTimeGuid" column="LIMITATION_TIME_GUID"/>
    </resultMap>

    <sql id="selectFzgjTaskLimitationFixedEntityVo">
        select
            GUID,
            LIMITATION_GUID,
            LINE_NO,
            ELEMENT_CODE,
            ELEMENT_CODE_NAME,
            LOGICAL,
            ELEMENT_VALUE,
            ELEMENT_VALUE_NAME,
            MEMO,
            SEQ,
            CREATE_USER,
            CREATE_USER_NAME,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_USER_NAME,
            UPDATE_DATE,
            NODE_CODE,
            NODE_NAME,
            COMPANY_CODE,
            COMPANY_NAME,
            GROUP_CODE,
            GROUP_NAME,
            LIMITATION_TIME_GUID
        from FZGJ_TASK_LIMITATION_FIXED
    </sql>
</mapper>