package com.eci.project.fzgjBdBill.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.fzgjBdBill.entity.FzgjBdBillEntity;
import org.apache.ibatis.annotations.Select;


/**
* 单据类型Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-03-19
*/
public interface FzgjBdBillDao extends EciBaseDao<FzgjBdBillEntity> {


    @Select("SELECT A.GUID,A.CODE,A.NAME,A.PRE_FIX,A.STATUS,A.SEQ,A.MEMO,A.CREATE_DATE,A.CREATE_USER,A.UPDATE_DATE,A.UPDATE_USER,A.CREATE_USER_NAME,A.UPDATE_USER_NAME,A.<PERSON>,A.<PERSON>_GUID,A.CLA<PERSON>_CODE,A.IS_JD,A.IS_CZ,A.PAGE_VIEW  FROM FZGJ_BD_BILL A WHERE 1 = 1   AND A.PARENTID = '-'")
    FzgjBdBillEntity getRoot();



}