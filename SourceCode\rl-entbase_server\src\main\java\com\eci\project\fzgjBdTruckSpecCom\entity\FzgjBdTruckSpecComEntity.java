package com.eci.project.fzgjBdTruckSpecCom.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.Date;


/**
* 计费车辆尺寸对象 FZGJ_BD_TRUCK_SPEC_COM
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-03-25
*/
@ApiModel("计费车辆尺寸")
@TableName("FZGJ_BD_TRUCK_SPEC_COM")
public class FzgjBdTruckSpecComEntity extends FzgjBdTruckSpecComBaseEntity{
    @Override
    protected void addConvertMap() {
        convertMap.put(Fields.truncType, () -> "cllx");// 车辆类型
        convertMap.put(Fields.status, () -> "YNKey");//是否启用
    }
}
