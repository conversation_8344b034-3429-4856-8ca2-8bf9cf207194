package com.eci.project.fzgjCrmContractFileType.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjCrmContractFileType.dao.FzgjCrmContractFileTypeDao;
import com.eci.project.fzgjCrmContractFileType.entity.FzgjCrmContractFileTypeEntity;
import com.eci.project.fzgjCrmContractFileType.validate.FzgjCrmContractFileTypeVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 合同附件类型Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-05-21
*/
@Service
@Slf4j
public class FzgjCrmContractFileTypeService implements EciBaseService<FzgjCrmContractFileTypeEntity> {

    @Autowired
    private FzgjCrmContractFileTypeDao fzgjCrmContractFileTypeDao;

    @Autowired
    private FzgjCrmContractFileTypeVal fzgjCrmContractFileTypeVal;


    @Override
    public TgPageInfo queryPageList(FzgjCrmContractFileTypeEntity entity) {
        EciQuery<FzgjCrmContractFileTypeEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjCrmContractFileTypeEntity> entities = fzgjCrmContractFileTypeDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjCrmContractFileTypeEntity save(FzgjCrmContractFileTypeEntity entity) {
        // 返回实体对象
        FzgjCrmContractFileTypeEntity fzgjCrmContractFileTypeEntity = null;
        fzgjCrmContractFileTypeVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjCrmContractFileTypeEntity = fzgjCrmContractFileTypeDao.insertOne(entity);

        }else{

            fzgjCrmContractFileTypeEntity = fzgjCrmContractFileTypeDao.updateByEntityId(entity);

        }
        return fzgjCrmContractFileTypeEntity;
    }

    @Override
    public List<FzgjCrmContractFileTypeEntity> selectList(FzgjCrmContractFileTypeEntity entity) {
        return fzgjCrmContractFileTypeDao.selectList(entity);
    }

    @Override
    public FzgjCrmContractFileTypeEntity selectOneById(Serializable id) {
        return fzgjCrmContractFileTypeDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjCrmContractFileTypeEntity> list) {
        fzgjCrmContractFileTypeDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjCrmContractFileTypeDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjCrmContractFileTypeDao.deleteById(id);
    }

}