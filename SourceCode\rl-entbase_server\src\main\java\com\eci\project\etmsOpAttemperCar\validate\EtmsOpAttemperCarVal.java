package com.eci.project.etmsOpAttemperCar.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.etmsOpAttemperCar.entity.EtmsOpAttemperCarEntity;

import org.springframework.stereotype.Service;


/**
* 用车需求信息Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-06-13
*/
@Service
public class EtmsOpAttemperCarVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(EtmsOpAttemperCarEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(EtmsOpAttemperCarEntity entity, BusinessType businessType) {

    }

}
