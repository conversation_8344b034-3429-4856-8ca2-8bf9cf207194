package com.eci.project.fzgjBdCity.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjBdCity.entity.FzgjBdCityEntity;

import org.springframework.stereotype.Service;


/**
* 市Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-03-17
*/
@Service
public class FzgjBdCityVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjBdCityEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjBdCityEntity entity, BusinessType businessType) {

    }

}
