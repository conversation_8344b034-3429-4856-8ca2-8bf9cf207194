package com.eci.project.etmsCrmPartner.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.etmsCrmPartner.service.EtmsCrmPartnerService;
import com.eci.project.etmsCrmPartner.entity.EtmsCrmPartnerEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* Controller
*
* @<NAME_EMAIL>
* @date 2025-04-16
*/
@Api(tags = "")
@RestController
@RequestMapping("/etmsCrmPartner")
public class EtmsCrmPartnerController extends EciBaseController {

    @Autowired
    private EtmsCrmPartnerService etmsCrmPartnerService;


    @ApiOperation(":保存")
    @EciLog(title = ":新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody EtmsCrmPartnerEntity entity){
        EtmsCrmPartnerEntity etmsCrmPartnerEntity =etmsCrmPartnerService.save(entity);
        return ResponseMsgUtil.success(10001,etmsCrmPartnerEntity);
    }


    @ApiOperation(":查询列表")
    @EciLog(title = ":查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody EtmsCrmPartnerEntity entity){
        List<EtmsCrmPartnerEntity> etmsCrmPartnerEntities = etmsCrmPartnerService.selectList(entity);
        return ResponseMsgUtil.success(10001,etmsCrmPartnerEntities);
    }


    @ApiOperation(":分页查询列表")
    @EciLog(title = ":分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody EtmsCrmPartnerEntity entity){
        TgPageInfo tgPageInfo = etmsCrmPartnerService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation(":根据ID查一条")
    @EciLog(title = ":根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody EtmsCrmPartnerEntity entity){
        EtmsCrmPartnerEntity  etmsCrmPartnerEntity = etmsCrmPartnerService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,etmsCrmPartnerEntity);
    }


    @ApiOperation(":根据ID删除一条")
    @EciLog(title = ":根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody EtmsCrmPartnerEntity entity){
        int count = etmsCrmPartnerService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation(":根据ID字符串删除多条")
    @EciLog(title = ":根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody EtmsCrmPartnerEntity entity) {
        int count = etmsCrmPartnerService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}