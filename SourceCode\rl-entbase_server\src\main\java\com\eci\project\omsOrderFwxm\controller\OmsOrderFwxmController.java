package com.eci.project.omsOrderFwxm.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.omsOrderFwxm.service.OmsOrderFwxmService;
import com.eci.project.omsOrderFwxm.entity.OmsOrderFwxmEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 订单服务项目Controller
*
* @<NAME_EMAIL>
* @date 2025-04-14
*/
@Api(tags = "订单服务项目")
@RestController
@RequestMapping("/omsOrderFwxm")
public class OmsOrderFwxmController extends EciBaseController {

    @Autowired
    private OmsOrderFwxmService omsOrderFwxmService;


    @ApiOperation("订单服务项目:保存")
    @EciLog(title = "订单服务项目:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody OmsOrderFwxmEntity entity){
        OmsOrderFwxmEntity omsOrderFwxmEntity =omsOrderFwxmService.save(entity);
        return ResponseMsgUtil.success(10001,omsOrderFwxmEntity);
    }


    @ApiOperation("订单服务项目:查询列表")
    @EciLog(title = "订单服务项目:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody OmsOrderFwxmEntity entity){
        List<OmsOrderFwxmEntity> omsOrderFwxmEntities = omsOrderFwxmService.selectList(entity);
        return ResponseMsgUtil.success(10001,omsOrderFwxmEntities);
    }


    @ApiOperation("订单服务项目:分页查询列表")
    @EciLog(title = "订单服务项目:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody OmsOrderFwxmEntity entity){
        TgPageInfo tgPageInfo = omsOrderFwxmService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("订单服务项目:根据ID查一条")
    @EciLog(title = "订单服务项目:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody OmsOrderFwxmEntity entity){
        OmsOrderFwxmEntity  omsOrderFwxmEntity = omsOrderFwxmService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,omsOrderFwxmEntity);
    }


    @ApiOperation("订单服务项目:根据ID删除一条")
    @EciLog(title = "订单服务项目:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody OmsOrderFwxmEntity entity){
        int count = omsOrderFwxmService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("订单服务项目:根据ID字符串删除多条")
    @EciLog(title = "订单服务项目:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody OmsOrderFwxmEntity entity) {
        int count = omsOrderFwxmService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}