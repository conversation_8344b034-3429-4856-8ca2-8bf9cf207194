<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.omsOrderFwxmWorkFkHfd.dao.OmsOrderFwxmWorkFkHfdDao">
    <resultMap type="OmsOrderFwxmWorkFkHfdEntity" id="OmsOrderFwxmWorkFkHfdResult">
        <result property="guid" column="GUID"/>
        <result property="orderNo" column="ORDER_NO"/>
        <result property="workNo" column="WORK_NO"/>
        <result property="fwxmCode" column="FWXM_CODE"/>
        <result property="hfNo" column="HF_NO"/>
        <result property="xzwtNo" column="XZWT_NO"/>
        <result property="checkbillNo" column="CHECKBILL_NO"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="bizRegId" column="BIZ_REG_ID"/>
        <result property="hfNoLy" column="HF_NO_LY"/>
        <result property="remark" column="REMARK"/>
        <result property="fxDate" column="FX_DATE"/>
        <result property="carNo" column="CAR_NO"/>
        <result property="iEFlag" column="I_E_FLAG"/>
        <result property="ieMark" column="IE_MARK"/>
        <result property="gjNo" column="GJ_NO"/>
    </resultMap>

    <sql id="selectOmsOrderFwxmWorkFkHfdEntityVo">
        select
            GUID,
            ORDER_NO,
            WORK_NO,
            FWXM_CODE,
            HF_NO,
            XZWT_NO,
            CHECKBILL_NO,
            CREATE_USER,
            CREATE_USER_NAME,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_USER_NAME,
            UPDATE_DATE,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            BIZ_REG_ID,
            HF_NO_LY,
            REMARK,
            FX_DATE,
            CAR_NO,
            I_E_FLAG,
            IE_MARK,
            GJ_NO
        from OMS_ORDER_FWXM_WORK_FK_HFD
    </sql>
</mapper>