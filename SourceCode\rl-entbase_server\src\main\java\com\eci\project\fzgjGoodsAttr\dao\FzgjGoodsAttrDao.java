package com.eci.project.fzgjGoodsAttr.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.fzgjGoodsAttr.entity.FzgjGoodsAttrEntity;


/**
* 货物属性Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-04-01
*/
public interface FzgjGoodsAttrDao extends EciBaseDao<FzgjGoodsAttrEntity> {

}