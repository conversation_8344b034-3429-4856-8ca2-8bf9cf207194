package com.eci.project.etmsOpHead.service;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.ZsrGlobaVarl;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.crud.tgQuery.queryChain.EciQueryChainWrapper;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;
import com.eci.project.etmsCrmEnterprise.dao.EtmsCrmEnterpriseDao;
import com.eci.project.etmsCrmEnterprise.entity.EtmsCrmEnterpriseEntity;
import com.eci.project.etmsOpAttemper.dao.EtmsOpAttemperDao;
import com.eci.project.etmsOpAttemper.entity.EtmsOpAttemperEntity;
import com.eci.project.etmsOpAttemperCar.dao.EtmsOpAttemperCarDao;
import com.eci.project.etmsOpAttemperCar.entity.EtmsOpAttemperCarEntity;
import com.eci.project.etmsOpAttemperCargo.dao.EtmsOpAttemperCargoDao;
import com.eci.project.etmsOpAttemperCargo.entity.EtmsOpAttemperCargoEntity;
import com.eci.project.etmsOpAttemperLine.dao.EtmsOpAttemperLineDao;
import com.eci.project.etmsOpAttemperLine.entity.EtmsOpAttemperLineEntity;
import com.eci.project.etmsOpHead.dao.EtmsOpHeadDao;
import com.eci.project.etmsOpHead.entity.EtmsOpHeadEntity;
import com.eci.project.etmsOpHead.validate.EtmsOpHeadVal;
import com.eci.project.omsOrder.entity.OmsOrderEntity;
import com.eci.project.omsOrderFwxmTmsXlXlLy.dao.OmsOrderFwxmTmsXlXlLyDao;
import com.eci.project.omsOrderFwxmTmsXlXlLy.entity.OmsOrderFwxmTmsXlXlLyEntity;
import com.eci.project.omsOrderFwxmTmsXlXlLyCl.dao.OmsOrderFwxmTmsXlXlLyClDao;
import com.eci.project.omsOrderFwxmTmsXlXlLyCl.entity.OmsOrderFwxmTmsXlXlLyClEntity;
import com.eci.project.omsOrderFwxmTmsXlXlLyXl.dao.OmsOrderFwxmTmsXlXlLyXlDao;
import com.eci.project.omsOrderFwxmTmsXlXlLyXl.entity.OmsOrderFwxmTmsXlXlLyXlEntity;
import com.eci.project.omsOrderFwxmWork.dao.OmsOrderFwxmWorkDao;
import com.eci.project.omsOrderFwxmWork.entity.OmsOrderFwxmWorkEntity;
import com.eci.project.omsOrderFwxmWorkFkHzqd.dao.OmsOrderFwxmWorkFkHzqdDao;
import com.eci.project.omsOrderFwxmWorkFkHzqd.entity.OmsOrderFwxmWorkFkHzqdEntity;
import com.eci.project.omsOrderFwxmZhysXl.dao.OmsOrderFwxmZhysXlDao;
import com.eci.project.omsOrderFwxmZhysXl.entity.OmsOrderFwxmZhysXlEntity;
import com.eci.project.omsOrderGoods.dao.OmsOrderGoodsDao;
import com.eci.project.omsOrderGoods.entity.OmsOrderGoodsEntity;
import com.eci.project.omsOrderGoodsPack.dao.OmsOrderGoodsPackDao;
import com.eci.project.omsOrderGoodsPack.entity.OmsOrderGoodsPackEntity;
import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;


/**
 * 平台业务主表Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-06-13
 */
@Service
@Slf4j
public class EtmsOpHeadPushService implements EciBaseService<EtmsOpHeadEntity> {

    @Autowired
    private EtmsOpHeadDao etmsOpHeadDao;

    @Autowired
    private EtmsOpHeadVal etmsOpHeadVal;

    @Autowired
    private EtmsCrmEnterpriseDao etmsCrmEnterpriseDao;

    @Autowired
    private OmsOrderFwxmWorkFkHzqdDao omsOrderFwxmWorkFkHzqdDao;

    /**
     * 协作任务DAO
     */
    @Autowired
    private OmsOrderFwxmWorkDao omsOrderFwxmWorkDao;


    @Autowired
    private EtmsOpAttemperDao etmsOpAttemperDao;


    @Autowired
    private EtmsOpAttemperCarDao etmsOpAttemperCarDao;
    @Autowired
    private EtmsOpAttemperCargoDao etmsOpAttemperCargoDao;
    @Autowired
    private EtmsOpAttemperLineDao etmsOpAttemperLineDao;

    /**
     * 货物信息
     */
    @Autowired
    private OmsOrderGoodsDao omsOrderGoodsDao;

    /**
     * 线路陆运线路
     */
    @Autowired
    private OmsOrderFwxmTmsXlXlLyXlDao omsOrderFwxmTmsXlXlLyXlDao;

    /**
     * 货物包装表Dao
     */
    @Autowired
    private OmsOrderGoodsPackDao omsOrderGoodsPackDao;

    /**
     * 综合运输-线路Dao
     */
    @Autowired
    private OmsOrderFwxmZhysXlDao omsOrderFwxmZhysXlDao;

    /**
     * 委托内容-程运序列-陆运Dao
     */
    @Autowired
    private OmsOrderFwxmTmsXlXlLyDao omsOrderFwxmTmsXlXlLyDao;

    /**
     * 委托内容-程运序列-陆运-车辆信息Dao层
     */
    @Autowired
    private OmsOrderFwxmTmsXlXlLyClDao omsOrderFwxmTmsXlXlLyClDao;

    /**
     * 获取核注清单/报关单的数据
     *
     * @param orderEntity
     * @return
     */
    public List<OmsOrderFwxmWorkFkHzqdEntity> getHzQdList(OmsOrderEntity orderEntity) {
        return omsOrderFwxmWorkFkHzqdDao.select().eq(OmsOrderFwxmWorkFkHzqdEntity::getOrderNo, orderEntity.getOrderNo()).list();
    }

    /**
     * 获取运输企业信息
     *
     * @return
     */
    public EtmsCrmEnterpriseEntity getEtmsCrmEnterprise() {
        // TODO: 获取运输企业信息，目前写死，带确定
        List<EtmsCrmEnterpriseEntity> list = etmsCrmEnterpriseDao.select()
                .eq(EtmsCrmEnterpriseEntity::getCode, UserContext.getUserInfo().getCompanyCode())
                .list();
        if (list.size() > 0) {
            return list.get(0);
        }else{
            EtmsCrmEnterpriseEntity  etmsCrmEnterprise=new EtmsCrmEnterpriseEntity();
            etmsCrmEnterprise.setStartNo("B");

            return etmsCrmEnterprise;
        }

    }

    /**
     * 获取货物信息
     *
     * @param omsOrder
     */
    public List<OmsOrderGoodsEntity> getGoodsData(OmsOrderEntity omsOrder) {
        List<OmsOrderGoodsEntity> list = omsOrderGoodsDao.select()
                .eq(OmsOrderGoodsEntity::getOrderNo, omsOrder.getOrderNo())
                .list();
        return list;
    }

    /**
     * 获取协作任务信息
     *
     * @param omsOrder
     */
    public List<OmsOrderFwxmWorkEntity> getWorkData(OmsOrderEntity omsOrder) {
        List<OmsOrderFwxmWorkEntity> list = omsOrderFwxmWorkDao.select()
                .eq(OmsOrderFwxmWorkEntity::getOrderNo, omsOrder.getOrderNo())
                .list();
        return list;
    }

    /**
     * 获取陆运线路信息
     *
     * @param omsOrder
     */
    public List<OmsOrderFwxmTmsXlXlLyXlEntity> getXlData(OmsOrderEntity omsOrder) {
        List<OmsOrderFwxmTmsXlXlLyXlEntity> list = omsOrderFwxmTmsXlXlLyXlDao.select()
                .eq(OmsOrderFwxmTmsXlXlLyXlEntity::getOrderNo, omsOrder.getOrderNo())
                .list();
        return list;
    }

    /**
     * 获取货物保证信息
     *
     * @param omsOrder
     */
    public List<OmsOrderGoodsPackEntity> getGoodsPackData(OmsOrderEntity omsOrder) {
        List<OmsOrderGoodsPackEntity> list = omsOrderGoodsPackDao.select()
                .eq(OmsOrderGoodsPackEntity::getOrderNo, omsOrder.getOrderNo())
                .list();
        return list;
    }

    /**
     * 获取综合运输线路信息
     *
     * @param omsOrder
     */
    public List<OmsOrderFwxmZhysXlEntity> getZhysXlData(OmsOrderEntity omsOrder) {
        List<OmsOrderFwxmZhysXlEntity> list = omsOrderFwxmZhysXlDao.select()
                .eq(OmsOrderFwxmZhysXlEntity::getOrderNo, omsOrder.getOrderNo())
                .list();
        return list;
    }

    /**
     * 委托内容-程运序列-陆运Dao
     *
     * @param omsOrder
     */
    public List<OmsOrderFwxmTmsXlXlLyEntity> getTmsXlXlLyData(OmsOrderEntity omsOrder) {
        List<OmsOrderFwxmTmsXlXlLyEntity> list = omsOrderFwxmTmsXlXlLyDao.select()
                .eq(OmsOrderFwxmTmsXlXlLyEntity::getOrderNo, omsOrder.getOrderNo())
                .list();
        return list;
    }

    /**
     * 委托内容-程运序列-陆运-车辆信息Dao层
     *
     * @param omsOrder
     */
    public List<OmsOrderFwxmTmsXlXlLyClEntity> getTmsXlXlLyClData(OmsOrderEntity omsOrder) {
        List<OmsOrderFwxmTmsXlXlLyClEntity> list = omsOrderFwxmTmsXlXlLyClDao.select()
                .eq(OmsOrderFwxmTmsXlXlLyClEntity::getOrderNo, omsOrder.getOrderNo())
                .list();
        return list;
    }

    /**
     * 委托内容-程运序列-陆运-车辆信息Dao层
     *
     * @param omsOrder
     */
    public List<OmsOrderFwxmTmsXlXlLyXlEntity> getTmsXlXlLyXlData(OmsOrderEntity omsOrder) {
        List<OmsOrderFwxmTmsXlXlLyXlEntity> list = omsOrderFwxmTmsXlXlLyXlDao.select()
                .eq(OmsOrderFwxmTmsXlXlLyXlEntity::getOrderNo, omsOrder.getOrderNo())
                .list();
        return list;
    }

    /**
     * 给运输系统写数据
     * @param omsOrder
     */
    public void pushData(OmsOrderEntity omsOrder) {

        EtmsCrmEnterpriseEntity etmsCrmEnterprise = getEtmsCrmEnterprise();
        if (etmsCrmEnterprise == null) {

        } else {

            /**
             * 获取反馈数据-核注清单数据
             */
            List<OmsOrderFwxmWorkFkHzqdEntity> hzQdList = getHzQdList(omsOrder);
            /**
             * 获取协作任务信息
             */
            List<OmsOrderFwxmWorkEntity> workData = getWorkData(omsOrder);
            /**
             * 获取货物保证信息
             */
            List<OmsOrderGoodsPackEntity> goodsPackData = getGoodsPackData(omsOrder);
            /**
             * 货物信息
             */
            List<OmsOrderGoodsEntity> goodsDatas = getGoodsData(omsOrder);
            /**
             *  获取综合运输线路信息
             */
            List<OmsOrderFwxmZhysXlEntity> zhysXlData = getZhysXlData(omsOrder);
            /**
             *  委托内容-程运序列-陆运Dao
             */
            List<OmsOrderFwxmTmsXlXlLyEntity> tmsXlXlLyData = getTmsXlXlLyData(omsOrder);
            /**
             *  委托内容-程运序列-陆运-车辆信息Dao层
             */
            List<OmsOrderFwxmTmsXlXlLyClEntity> tmsXlXlLyClData = getTmsXlXlLyClData(omsOrder);

            final int[] sequence = {0};
//            hzQdList.forEach(hzQdEntity -> {
//                sequence[0]++;

            Optional<EtmsOpHeadEntity> max = etmsOpHeadDao.select().list().stream().max(Comparator.comparing(EtmsOpHeadEntity::getOpNo));
            if (max.isPresent()){
                sequence[0]= Integer.parseInt(max.get().getOpNo());
            }
            String attNo = SerialNumberGenerator.generateAttNo(etmsCrmEnterprise.getStartNo(), sequence[0]);
            String opNo = SerialNumberGenerator.generateOpNo(  sequence[0]+1);

            EtmsOpHeadEntity etmsOpHeadEntity = new EtmsOpHeadEntity();
            etmsOpHeadEntity.setGuid(IdWorker.getIdStr());
            etmsOpHeadEntity.setOpNo(opNo);
            etmsOpHeadEntity.setCustomerCode(omsOrder.getConsigneeCode());
            etmsOpHeadEntity.setConsigneeCode(omsOrder.getReceiver());

            if (goodsDatas.size() > 0) {
                etmsOpHeadEntity.setInvNo(goodsDatas.get(0).getInvoiceNo());
            }

            /**
             * 取核注清单里面的信息
             */
            if (hzQdList.size() > 0) {
                OmsOrderFwxmWorkFkHzqdEntity hzQdEntity = hzQdList.get(0);
                etmsOpHeadEntity.setMbl(hzQdEntity.getYdh());
                etmsOpHeadEntity.setHbl(hzQdEntity.getFdh());
                etmsOpHeadEntity.setZgNo(hzQdEntity.getZgdh());
            }
            etmsOpHeadEntity.setStatus(ZsrGlobaVarl.EtmsStatus);
//                saveEntity.setAttNo(attNo);
            etmsOpHeadEntity.setCreateCompany(UserContext.getUserInfo().getCompanyCode());
            if (workData.size() > 0) {
                OmsOrderFwxmWorkEntity workEntity = workData.get(0);
                etmsOpHeadEntity.setCreateDate(workEntity.getCreateDate());
                etmsOpHeadEntity.setUpdateDate(workEntity.getUpdateDate());
                etmsOpHeadEntity.setCreateUser(workEntity.getCreateUser());
                etmsOpHeadEntity.setCreateUserName(workEntity.getCreateUserName());
                etmsOpHeadEntity.setUpdateUser(workEntity.getUpdateUser());
                etmsOpHeadEntity.setUpdateUserName(workEntity.getUpdateUserName());
                etmsOpHeadEntity.setGroupCode(workEntity.getGroupCode());
                etmsOpHeadEntity.setGroupName(workEntity.getGroupName());
                etmsOpHeadEntity.setNodeCode(workEntity.getNodeCode());
                etmsOpHeadEntity.setNodeName(workEntity.getNodeName());
                etmsOpHeadEntity.setCompanyCode(workEntity.getCompanyCode());
                etmsOpHeadEntity.setCompanyName(workEntity.getCompanyName());
            }
            etmsOpHeadEntity.setOmsOrderNo(omsOrder.getOrderNo());
            etmsOpHeadDao.insertOne(etmsOpHeadEntity);

            // 插入运输系统：运输委托信息数据  ETMS_OP_ATTEMPER
            EtmsOpAttemperEntity etmsOpAttemperEntity = insertData_EtmsOpAttemper(tmsXlXlLyData, zhysXlData, goodsDatas,
                    goodsPackData, etmsOpHeadEntity, workData, omsOrder, attNo);

            // 运输系统：用车需求信息  ETMS_OP_ATTEMPER_CAR
            EtmsOpAttemperCarEntity etmsOpAttemperCarEntity = insertData_EtmsOpAttemperCar(etmsOpAttemperEntity, tmsXlXlLyClData, tmsXlXlLyData, goodsDatas, goodsPackData,
                    etmsOpHeadEntity, omsOrder, attNo);


            // 运输系统：托运线路站点信息  ETMS_OP_ATTEMPER_LINE
            EtmsOpAttemperLineEntity etmsOpAttemperLineEntity = insertData_EtmsOpAttemperLine(
                    etmsOpAttemperEntity,
                    etmsOpAttemperCarEntity,
                    etmsOpHeadEntity,
                    omsOrder,
                    attNo
            );


            // 运输系统：用车需求信息  ETMS_OP_ATTEMPER_CARGO
            insertData_EtmsOpAttemperCargo(
                    etmsOpAttemperEntity,
                    etmsOpAttemperCarEntity,
                    etmsOpAttemperLineEntity,
                    goodsDatas,
                    goodsPackData,
                    etmsOpHeadEntity,
                    workData,
                    omsOrder,
                    attNo
            );

        }

    }


    /**
     * 插入运输系统：运输委托信息数据  ETMS_OP_ATTEMPER
     */
    public EtmsOpAttemperEntity insertData_EtmsOpAttemper(
            List<OmsOrderFwxmTmsXlXlLyEntity> tmsXlXlLyData,
            List<OmsOrderFwxmZhysXlEntity> zhysXlData,
            List<OmsOrderGoodsEntity> goodsDatas,
            List<OmsOrderGoodsPackEntity> goodsPackData,
            EtmsOpHeadEntity etmsOpHeadEntity,
            List<OmsOrderFwxmWorkEntity> workData,
            OmsOrderEntity omsOrder,
            String attNo) {
        EtmsOpAttemperEntity etmsOpAttemperEntity = new EtmsOpAttemperEntity();
        etmsOpAttemperEntity.setGuid(IdWorker.getIdStr());
        etmsOpAttemperEntity.setOpNo(etmsOpHeadEntity.getOpNo());
        etmsOpAttemperEntity.setAttNo(attNo);
        etmsOpAttemperEntity.setCustomerJobNo(omsOrder.getCustomerOrderNo());
        etmsOpAttemperEntity.setBusinessType(omsOrder.getOpType());
        etmsOpAttemperEntity.setAttMemo(omsOrder.getBizMemo());
        etmsOpAttemperEntity.setBalanceMemo(omsOrder.getAccountMemo());
        if (workData.size() > 0) {
            OmsOrderFwxmWorkEntity workEntity = workData.get(0);
            etmsOpAttemperEntity.setDeliveryMode(workEntity.getIsWb());
        }
        etmsOpAttemperEntity.setAttFrom("OMS");
        etmsOpAttemperEntity.setStatus(ZsrGlobaVarl.EtmsStatus);

        etmsOpAttemperEntity.setCreateDate(etmsOpHeadEntity.getCreateDate());
        etmsOpAttemperEntity.setUpdateDate(etmsOpHeadEntity.getUpdateDate());
        etmsOpAttemperEntity.setCreateUser(etmsOpHeadEntity.getCreateUser());
        etmsOpAttemperEntity.setCreateUserName(etmsOpHeadEntity.getCreateUserName());
        etmsOpAttemperEntity.setUpdateUser(etmsOpHeadEntity.getUpdateUser());
        etmsOpAttemperEntity.setUpdateUserName(etmsOpHeadEntity.getUpdateUserName());
        etmsOpAttemperEntity.setGroupCode(etmsOpHeadEntity.getGroupCode());
        etmsOpAttemperEntity.setGroupName(etmsOpHeadEntity.getGroupName());
        etmsOpAttemperEntity.setNodeCode(etmsOpHeadEntity.getNodeCode());
        etmsOpAttemperEntity.setNodeName(etmsOpHeadEntity.getNodeName());
        etmsOpAttemperEntity.setCompanyCode(etmsOpHeadEntity.getCompanyCode());
        etmsOpAttemperEntity.setCompanyName(etmsOpHeadEntity.getCompanyName());
        etmsOpAttemperEntity.setOmsOrderNo(omsOrder.getOrderNo());
        etmsOpAttemperEntity.setIsKhzz(ZsrGlobaVarl.EtmsIsKhzz);
        etmsOpAttemperEntity.setOpDate(omsOrder.getOpDate());

        etmsOpAttemperEntity.setSenderCode(etmsOpHeadEntity.getCustomerCode());
        etmsOpAttemperEntity.setConsigneeCode(etmsOpHeadEntity.getConsigneeCode());

        List<OmsOrderFwxmTmsXlXlLyXlEntity> xlData = getXlData(omsOrder);
        if (xlData.size() > 0) {
            // 起始要求作业时间 +    // 起始地
            xlData.stream()
                    .filter(xlEntity -> "QS".equals(xlEntity.getXlType()))
                    .findFirst()
                    .ifPresent(qsEntity -> {
                        etmsOpAttemperEntity.setStartRequestDateStart(qsEntity.getJjDate());

                        // 把这些组合起来，形成一个地址
                        etmsOpAttemperEntity.setStartOpArea(buildFullAddress(qsEntity));

                    });
            // 终到要求作业时间 +  // 终到地
            xlData.stream()
                    .filter(xlEntity -> "ZD".equals(xlEntity.getXlType()))
                    .findFirst()
                    .ifPresent(qsEntity -> {
                        etmsOpAttemperEntity.setStartRequestDateEnd(qsEntity.getJjDate());
                        etmsOpAttemperEntity.setEndOpArea(buildFullAddress(qsEntity));
                    });
        }

        if (goodsPackData.size() > 0) {
            OmsOrderGoodsPackEntity omsOrderGoodsPackEntity = goodsPackData.get(0);
            etmsOpAttemperEntity.setPieces(String.valueOf(omsOrderGoodsPackEntity.getQtyPack()));
        }
        if (goodsDatas.size() > 0) {
            OmsOrderGoodsEntity omsOrderGoodsEntity = goodsDatas.get(0);
            etmsOpAttemperEntity.setWeight(String.valueOf(omsOrderGoodsEntity.getWeightCalc()));
            etmsOpAttemperEntity.setVolume(String.valueOf(omsOrderGoodsEntity.getVolumeTotal()));
            etmsOpAttemperEntity.setCargo(omsOrderGoodsEntity.getGoodsName());
        }

        etmsOpAttemperEntity.setOrgDepId(etmsOpHeadEntity.getOrgDepId());
        etmsOpAttemperEntity.setOrgDepCode(etmsOpHeadEntity.getOrgDepCode());
        etmsOpAttemperEntity.setOrgDepName(etmsOpHeadEntity.getOrgDepName());
        etmsOpAttemperEntity.setBatchNo(etmsOpHeadEntity.getBatchNumber());
        etmsOpAttemperEntity.setBatchNumber(etmsOpHeadEntity.getBatchNumber());
        // 是否是急货
        etmsOpAttemperEntity.setIsUrgent(omsOrder.getIsJjh());

        if (zhysXlData.size() > 0) {
            OmsOrderFwxmZhysXlEntity omsOrderFwxmZhysXlEntity = zhysXlData.get(0);
            etmsOpAttemperEntity.setIsWb(omsOrderFwxmZhysXlEntity.getIsWb());
            etmsOpAttemperEntity.setNbzyNode(omsOrderFwxmZhysXlEntity.getNbzyzz());
        }
        if (tmsXlXlLyData.size() > 0) {
            OmsOrderFwxmTmsXlXlLyEntity omsOrderFwxmTmsXlXlLyEntity = tmsXlXlLyData.get(0);
            etmsOpAttemperEntity.setXzwtNo(omsOrderFwxmTmsXlXlLyEntity.getWorkNo());
        }


        etmsOpAttemperDao.insertOne(etmsOpAttemperEntity);
        return etmsOpAttemperEntity;
    }

    /**
     * 运输系统：用车需求信息  ETMS_OP_ATTEMPER_CAR
     */
    public EtmsOpAttemperCarEntity insertData_EtmsOpAttemperCar(
            EtmsOpAttemperEntity etmsOpAttemperEntity,
            List<OmsOrderFwxmTmsXlXlLyClEntity> tmsXlXlLyClData,
            List<OmsOrderFwxmTmsXlXlLyEntity> tmsXlXlLyData,
            List<OmsOrderGoodsEntity> goodsDatas,
            List<OmsOrderGoodsPackEntity> goodsPackData,
            EtmsOpHeadEntity etmsOpHeadEntity,
            OmsOrderEntity omsOrder,
            String attNo) {
        EtmsOpAttemperCarEntity saveEntity = new EtmsOpAttemperCarEntity();
        saveEntity.setAttGuid(etmsOpAttemperEntity.getGuid());
        saveEntity.setGuid(IdWorker.getIdStr());
        saveEntity.setOpNo(etmsOpHeadEntity.getOpNo());
        saveEntity.setAttNo(attNo);
        // 增加创建公司code
        saveEntity.setCreateCompany(UserContext.getUserInfo().getCompanyCode());

        if (tmsXlXlLyClData.size() > 0) {
            OmsOrderFwxmTmsXlXlLyClEntity omsOrderFwxmTmsXlXlLyClEntity = tmsXlXlLyClData.get(0);
            saveEntity.setZcType(omsOrderFwxmTmsXlXlLyClEntity.getVehicleType());
            saveEntity.setZcModel(omsOrderFwxmTmsXlXlLyClEntity.getVehicleSize());
            saveEntity.setZcNum(BigDecimal.valueOf(omsOrderFwxmTmsXlXlLyClEntity.getTrainQty()));
        }

        saveEntity.setStatus(ZsrGlobaVarl.EtmsStatus);
        saveEntity.setCreateDate(etmsOpHeadEntity.getCreateDate());
        saveEntity.setUpdateDate(etmsOpHeadEntity.getUpdateDate());
        saveEntity.setCreateUser(etmsOpHeadEntity.getCreateUser());
        saveEntity.setCreateUserName(etmsOpHeadEntity.getCreateUserName());
        saveEntity.setUpdateUser(etmsOpHeadEntity.getUpdateUser());
        saveEntity.setUpdateUserName(etmsOpHeadEntity.getUpdateUserName());
        saveEntity.setGroupCode(etmsOpHeadEntity.getGroupCode());
        saveEntity.setGroupName(etmsOpHeadEntity.getGroupName());
        saveEntity.setNodeCode(etmsOpHeadEntity.getNodeCode());
        saveEntity.setNodeName(etmsOpHeadEntity.getNodeName());
        saveEntity.setCompanyCode(etmsOpHeadEntity.getCompanyCode());
        saveEntity.setCompanyName(etmsOpHeadEntity.getCompanyName());

        saveEntity.setSenderCode(etmsOpHeadEntity.getCustomerCode());
        saveEntity.setConsigneeCode(etmsOpHeadEntity.getConsigneeCode());

        List<OmsOrderFwxmTmsXlXlLyXlEntity> xlData = getXlData(omsOrder);
        if (xlData.size() > 0) {
            // 起始要求作业时间 +    // 起始地
            xlData.stream()
                    .filter(xlEntity -> "QS".equals(xlEntity.getXlType()))
                    .findFirst()
                    .ifPresent(qsEntity -> {
                        saveEntity.setStartRequestDateStart(qsEntity.getJjDate());

                        // 把这些组合起来，形成一个地址
                        saveEntity.setStartOpArea(buildFullAddress(qsEntity));

                    });
            // 终到要求作业时间 +  // 终到地
            xlData.stream()
                    .filter(xlEntity -> "ZD".equals(xlEntity.getXlType()))
                    .findFirst()
                    .ifPresent(qsEntity -> {
                        saveEntity.setStartRequestDateEnd(qsEntity.getJjDate());
                        saveEntity.setEndOpArea(buildFullAddress(qsEntity));
                    });
        }

        if (goodsPackData.size() > 0) {
            OmsOrderGoodsPackEntity omsOrderGoodsPackEntity = goodsPackData.get(0);
            saveEntity.setPieces(String.valueOf(omsOrderGoodsPackEntity.getQtyPack()));
        }
        if (goodsDatas.size() > 0) {
            OmsOrderGoodsEntity omsOrderGoodsEntity = goodsDatas.get(0);
            saveEntity.setWeight(String.valueOf(omsOrderGoodsEntity.getWeightCalc()));
            saveEntity.setVolume(String.valueOf(omsOrderGoodsEntity.getVolumeTotal()));
            saveEntity.setCargo(omsOrderGoodsEntity.getGoodsName());
        }

        saveEntity.setOrgDepId(etmsOpHeadEntity.getOrgDepId());
        saveEntity.setOrgDepCode(etmsOpHeadEntity.getOrgDepCode());
        saveEntity.setOrgDepName(etmsOpHeadEntity.getOrgDepName());

        if (tmsXlXlLyData.size() > 0) {
            OmsOrderFwxmTmsXlXlLyEntity omsOrderFwxmTmsXlXlLyEntity = tmsXlXlLyData.get(0);
            saveEntity.setDeliveryType(omsOrderFwxmTmsXlXlLyEntity.getCyfs());
            saveEntity.setLdBalance(omsOrderFwxmTmsXlXlLyEntity.getLdjfyj());
        }


        etmsOpAttemperCarDao.insertOne(saveEntity);
        return saveEntity;
    }

    /**
     * 运输系统：用车需求信息  ETMS_OP_ATTEMPER_CARGO
     */
    public EtmsOpAttemperCargoEntity insertData_EtmsOpAttemperCargo(
            EtmsOpAttemperEntity etmsOpAttemperEntity,
            EtmsOpAttemperCarEntity etmsOpAttemperCarEntity,
            EtmsOpAttemperLineEntity etmsOpAttemperLineEntity,
            List<OmsOrderGoodsEntity> goodsDatas,
            List<OmsOrderGoodsPackEntity> goodsPackData,
            EtmsOpHeadEntity etmsOpHeadEntity,
            List<OmsOrderFwxmWorkEntity> workData,
            OmsOrderEntity omsOrder,
            String attNo) {
        EtmsOpAttemperCargoEntity saveEntity = new EtmsOpAttemperCargoEntity();
        saveEntity.setGuid(IdWorker.getIdStr());
        saveEntity.setAttGuid(etmsOpAttemperEntity.getGuid());
        saveEntity.setCarGuid(etmsOpAttemperCarEntity.getGuid());
        saveEntity.setStartLineGuid(etmsOpAttemperLineEntity.getGuid());
        saveEntity.setOpNo(etmsOpHeadEntity.getOpNo());
        saveEntity.setAttNo(attNo);

        saveEntity.setParentAttGuid(etmsOpAttemperEntity.getGuid());
        saveEntity.setEndLineGuid(etmsOpAttemperLineEntity.getGuid());

        if (workData.size() > 0) {
            OmsOrderFwxmWorkEntity omsOrderFwxmWorkEntity = workData.get(0);
            saveEntity.setOmsWorkNo(omsOrderFwxmWorkEntity.getWorkNo());
        }

        saveEntity.setCreateDate(etmsOpHeadEntity.getCreateDate());
        saveEntity.setUpdateDate(etmsOpHeadEntity.getUpdateDate());
        saveEntity.setCreateUser(etmsOpHeadEntity.getCreateUser());
        saveEntity.setCreateUserName(etmsOpHeadEntity.getCreateUserName());
        saveEntity.setUpdateUser(etmsOpHeadEntity.getUpdateUser());
        saveEntity.setUpdateUserName(etmsOpHeadEntity.getUpdateUserName());
        saveEntity.setGroupCode(etmsOpHeadEntity.getGroupCode());
        saveEntity.setGroupName(etmsOpHeadEntity.getGroupName());
        saveEntity.setNodeCode(etmsOpHeadEntity.getNodeCode());
        saveEntity.setNodeName(etmsOpHeadEntity.getNodeName());
        saveEntity.setCompanyCode(etmsOpHeadEntity.getCompanyCode());
        saveEntity.setCompanyName(etmsOpHeadEntity.getCompanyName());
        saveEntity.setOmsOrderNo(omsOrder.getOrderNo());

        saveEntity.setSenderCode(etmsOpHeadEntity.getCustomerCode());
        saveEntity.setConsigneeCode(etmsOpHeadEntity.getConsigneeCode());

        List<OmsOrderFwxmTmsXlXlLyXlEntity> xlData = getXlData(omsOrder);
        if (xlData.size() > 0) {
            // 起始要求作业时间 +    // 起始地
            xlData.stream()
                    .filter(xlEntity -> "QS".equals(xlEntity.getXlType()))
                    .findFirst()
                    .ifPresent(qsEntity -> {
                        saveEntity.setStartRequestDateStart(qsEntity.getJjDate());

                    });
            // 终到要求作业时间 +  // 终到地
            xlData.stream()
                    .filter(xlEntity -> "ZD".equals(xlEntity.getXlType()))
                    .findFirst()
                    .ifPresent(qsEntity -> {
                        saveEntity.setStartRequestDateEnd(qsEntity.getJjDate());
                    });
        }

        if (goodsPackData.size() > 0) {
            OmsOrderGoodsPackEntity omsOrderGoodsPackEntity = goodsPackData.get(0);
            saveEntity.setPieces(BigDecimal.valueOf(omsOrderGoodsPackEntity.getQtyPack()));
            saveEntity.setPiecesUnit("JIA");
        }
        if (goodsDatas.size() > 0) {
            OmsOrderGoodsEntity omsOrderGoodsEntity = goodsDatas.get(0);
            saveEntity.setWeight(omsOrderGoodsEntity.getWeightCalc());
            saveEntity.setWeightUnit("KG");
            saveEntity.setVolume(omsOrderGoodsEntity.getVolumeTotal());
            saveEntity.setVolumeUnit("M3");
            saveEntity.setCargo(omsOrderGoodsEntity.getGoodsName());
        }


        saveEntity.setOrgDepId(etmsOpHeadEntity.getOrgDepId());
        saveEntity.setOrgDepCode(etmsOpHeadEntity.getOrgDepCode());
        saveEntity.setOrgDepName(etmsOpHeadEntity.getOrgDepName());

        etmsOpAttemperCargoDao.insertOne(saveEntity);
        return saveEntity;
    }


    /**
     * 运输系统：托运线路站点信息  ETMS_OP_ATTEMPER_LINE
     */
    public EtmsOpAttemperLineEntity insertData_EtmsOpAttemperLine(
            EtmsOpAttemperEntity etmsOpAttemperEntity,
            EtmsOpAttemperCarEntity etmsOpAttemperCarEntity,
            EtmsOpHeadEntity etmsOpHeadEntity,
            OmsOrderEntity omsOrder,
            String attNo) {
        EtmsOpAttemperLineEntity saveEntity = new EtmsOpAttemperLineEntity();
        saveEntity.setGuid(IdWorker.getIdStr());
        saveEntity.setAttGuid(etmsOpAttemperEntity.getGuid());
        saveEntity.setCarGuid(etmsOpAttemperCarEntity.getGuid());
        saveEntity.setOpNo(etmsOpHeadEntity.getOpNo());
        saveEntity.setAttNo(attNo);

        List<OmsOrderFwxmTmsXlXlLyXlEntity> tmsXlXlLyXlData = getTmsXlXlLyXlData(omsOrder);

        if (tmsXlXlLyXlData.size() > 0) {
            OmsOrderFwxmTmsXlXlLyXlEntity omsOrderFwxmTmsXlXlLyXlEntity = tmsXlXlLyXlData.get(0);
            saveEntity.setStationSeq(BigDecimal.valueOf(omsOrderFwxmTmsXlXlLyXlEntity.getXlSeq()));
            saveEntity.setStationType(omsOrderFwxmTmsXlXlLyXlEntity.getXlType());
            saveEntity.setRequestDate(omsOrderFwxmTmsXlXlLyXlEntity.getJjDate());
            saveEntity.setOpArea(buildFullAddress(omsOrderFwxmTmsXlXlLyXlEntity));
            saveEntity.setOpAddress(omsOrderFwxmTmsXlXlLyXlEntity.getAddress());

            saveEntity.setOpLink(omsOrderFwxmTmsXlXlLyXlEntity.getLinkMan());
            saveEntity.setOpTel(omsOrderFwxmTmsXlXlLyXlEntity.getLinkTel());
            // IS_DHJC+IS_AZCX+IS_THDZ
            saveEntity.setOpRequest(
                    omsOrderFwxmTmsXlXlLyXlEntity.getIsDhjc()
                            + "|" + omsOrderFwxmTmsXlXlLyXlEntity.getIsAzcx()
                            + "|" + omsOrderFwxmTmsXlXlLyXlEntity.getIsThdz()
            );
        }

        saveEntity.setCreateDate(etmsOpHeadEntity.getCreateDate());
        saveEntity.setUpdateDate(etmsOpHeadEntity.getUpdateDate());
        saveEntity.setCreateUser(etmsOpHeadEntity.getCreateUser());
        saveEntity.setCreateUserName(etmsOpHeadEntity.getCreateUserName());
        saveEntity.setUpdateUser(etmsOpHeadEntity.getUpdateUser());
        saveEntity.setUpdateUserName(etmsOpHeadEntity.getUpdateUserName());
        saveEntity.setGroupCode(etmsOpHeadEntity.getGroupCode());
        saveEntity.setGroupName(etmsOpHeadEntity.getGroupName());
        saveEntity.setNodeCode(etmsOpHeadEntity.getNodeCode());
        saveEntity.setNodeName(etmsOpHeadEntity.getNodeName());
        saveEntity.setCompanyCode(etmsOpHeadEntity.getCompanyCode());
        saveEntity.setCompanyName(etmsOpHeadEntity.getCompanyName());

        etmsOpAttemperLineDao.insertOne(saveEntity);
        return saveEntity;
    }


    /**
     * 构建地址
     *
     * @param qs
     * @return // COUNTY N VARCHAR2(20) Y   国家
     * // PROVINCE N VARCHAR2(20) Y   省
     * // CITY N VARCHAR2(20) Y   市
     * // REGION N VARCHAR2(20) Y   区县
     * // TOWN N VARCHAR2(20) Y   乡镇
     * // ADDRESS N VARCHAR2(500) Y   详细地址
     */
    public String buildFullAddress(OmsOrderFwxmTmsXlXlLyXlEntity qs) {
        if (qs == null) {
            return "";
        }

        StringBuilder sb = new StringBuilder();

        appendIfNotNull(sb, qs.getCounty());
        appendIfNotNull(sb, qs.getProvince());
        appendIfNotNull(sb, qs.getCity());
        appendIfNotNull(sb, qs.getRegion());
        appendIfNotNull(sb, qs.getTown());
        appendIfNotNull(sb, qs.getAddress());

        return sb.toString();
    }

    /**
     * 扩展方法
     *
     * @param sb
     * @param part
     */
    private void appendIfNotNull(StringBuilder sb, String part) {
        if (part != null && !part.trim().isEmpty()) {
            sb.append(part);
        }
    }

    @Override
    public TgPageInfo queryPageList(EtmsOpHeadEntity entity) {
        EciQuery<EtmsOpHeadEntity> eciQuery = EciQuery.buildQuery(entity);
        List<EtmsOpHeadEntity> entities = etmsOpHeadDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public EtmsOpHeadEntity save(EtmsOpHeadEntity entity) {
        // 返回实体对象
        EtmsOpHeadEntity etmsOpHeadEntity = null;
        etmsOpHeadVal.saveValidate(entity, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            etmsOpHeadEntity = etmsOpHeadDao.insertOne(entity);

        } else {

            etmsOpHeadEntity = etmsOpHeadDao.updateByEntityId(entity);

        }
        return etmsOpHeadEntity;
    }

    @Override
    public List<EtmsOpHeadEntity> selectList(EtmsOpHeadEntity entity) {
        return etmsOpHeadDao.selectList(entity);
    }

    @Override
    public EtmsOpHeadEntity selectOneById(Serializable id) {
        return etmsOpHeadDao.selectById(id);
    }


    @Override
    public void insertBatch(List<EtmsOpHeadEntity> list) {
        etmsOpHeadDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return etmsOpHeadDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return etmsOpHeadDao.deleteById(id);
    }

}