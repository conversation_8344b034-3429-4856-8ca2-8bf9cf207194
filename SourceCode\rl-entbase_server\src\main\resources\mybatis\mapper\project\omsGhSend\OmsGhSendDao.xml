<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.omsGhSend.dao.OmsGhSendDao">
    <resultMap type="OmsGhSendEntity" id="OmsGhSendResult">
        <result property="guid" column="GUID"/>
        <result property="orderNo" column="ORDER_NO"/>
        <result property="opFlag" column="OP_FLAG"/>
        <result property="opMessage" column="OP_MESSAGE"/>
        <result property="opDate" column="OP_DATE"/>
        <result property="recodeDate" column="RECODE_DATE"/>
        <result property="remark" column="REMARK"/>
        <result property="opNum" column="OP_NUM"/>
    </resultMap>

    <sql id="selectOmsGhSendEntityVo">
        select
            GUID,
            ORDER_NO,
            OP_FLAG,
            OP_MESSAGE,
            OP_DATE,
            RECODE_DATE,
            REMARK,
            OP_NUM
        from OMS_GH_SEND
    </sql>
</mapper>