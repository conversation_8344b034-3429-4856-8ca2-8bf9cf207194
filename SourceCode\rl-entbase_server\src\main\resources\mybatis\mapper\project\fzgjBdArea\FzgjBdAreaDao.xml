<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.fzgjBdArea.dao.FzgjBdAreaDao">
    <resultMap type="FzgjBdAreaEntity" id="FzgjBdAreaResult">
        <result property="guid" column="GUID"/>
        <result property="code" column="CODE"/>
        <result property="name" column="NAME"/>
        <result property="shortName" column="SHORT_NAME"/>
        <result property="districtId" column="DISTRICT_ID"/>
        <result property="memo" column="MEMO"/>
        <result property="status" column="STATUS"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="seq" column="SEQ"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="city" column="CITY"/>
        <result property="province" column="PROVINCE"/>
        <result property="country" column="COUNTRY"/>
        <result property="districtCode" column="DISTRICT_CODE"/>
        <result property="districtName" column="DISTRICT_NAME"/>
    </resultMap>

    <sql id="selectFzgjBdAreaEntityVo">
        select
            GUID,
            CODE,
            NAME,
            SHORT_NAME,
            DISTRICT_ID,
            MEMO,
            STATUS,
            CREATE_USER_NAME,
            CREATE_USER,
            CREATE_DATE,
            UPDATE_USER_NAME,
            UPDATE_USER,
            UPDATE_DATE,
            SEQ,
            NODE_CODE,
            NODE_NAME,
            COMPANY_CODE,
            COMPANY_NAME,
            GROUP_CODE,
            GROUP_NAME
        from FZGJ_BD_AREA
    </sql>

    <select id="selectInfo" parameterType="String" resultMap="FzgjBdAreaResult">
    SELECT
        A.GUID,A.CODE,A.NAME,A.SEQ,A.DISTRICT_ID,A.DISTRICT_ID AS DISTRICT_CODE,B.NAME DISTRICT_NAME,
        C.NAME CITY,D.NAME PROVINCE,E.CH_NAME COUNTRY,A.STATUS,
        A.CREATE_USER_NAME,A.CREATE_DATE,A.UPDATE_USER_NAME,
        A.UPDATE_DATE,A.MEMO
        FROM FZGJ_BD_AREA A
        LEFT JOIN FZGJ_BD_DISTRICT B ON A.DISTRICT_ID = B.GUID
        LEFT JOIN FZGJ_BD_CITY C ON B.CITY_ID = C.GUID
        LEFT JOIN FZGJ_BD_PROVINCE D ON C.PROVINCE_ID =D.GUID
        LEFT JOIN FZGJ_BD_COUNTRY E ON D.COUNTRY_ID = E.CODE
        WHERE 1=1 and A.GUID = #{guid}
    </select>

    <select id="selectListInfo" parameterType="FzgjBdAreaEntity" resultMap="FzgjBdAreaResult">
        SELECT A.GUID,A.CODE,A.NAME,A.SEQ,B.NAME DISTRICT_ID,
        C.NAME CITY,D.NAME PROVINCE,E.CH_NAME COUNTRY,A.STATUS,
        A.CREATE_USER_NAME,A.CREATE_DATE,A.UPDATE_USER_NAME,
        A.UPDATE_DATE,A.MEMO
        FROM FZGJ_BD_AREA A
        LEFT JOIN FZGJ_BD_DISTRICT B ON A.DISTRICT_ID = B.GUID
        LEFT JOIN FZGJ_BD_CITY C ON B.CITY_ID = C.GUID
        LEFT JOIN FZGJ_BD_PROVINCE D ON C.PROVINCE_ID =D.GUID
        LEFT JOIN FZGJ_BD_COUNTRY E ON D.COUNTRY_ID = E.CODE
        WHERE 1=1 and A.GROUP_CODE = #{groupCode}
        <if test="code != null and code != ''">
            AND A.CODE like '%' || #{code} || '%'
        </if>
        <if test="name != null and name != ''">
            AND A.NAME like '%' || #{name} || '%'
        </if>
        <if test="province != null">
            AND C.PROVINCE_ID = #{province}
        </if>
        <if test="city != null">
            AND B.CITY_ID = #{city}
        </if>
        <if test="districtId != null">
            AND A.DISTRICT_ID = #{districtId}
        </if>
        <if test="country != null">
            AND D.COUNTRY_ID = #{country}
        </if>
        <if test="status != null">
            AND A.STATUS = #{status}
        </if>
    </select>
</mapper>