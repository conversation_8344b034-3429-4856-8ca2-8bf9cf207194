package com.eci.project.omsOrderFwxmTms.service;

import com.baomidou.mybatisplus.annotation.TableField;
import com.eci.common.CodeNameEntity;
import com.eci.common.Zsr;
import com.eci.common.ZsrDateUtils;
import com.eci.common.ZsrGloableConfig;
import com.eci.common.db.DBHelper;
import com.eci.common.utils.CommonLib;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.common.SQLGenerator;
import com.eci.project.fzgjBdBill.entity.FzgjBdProductDto;
import com.eci.project.omsOrderFwxmTms.dao.OmsOrderFwxmTmsDao;
import com.eci.project.omsOrderFwxmTms.entity.OmsOrderFwxmTmsEntity;
import com.eci.project.omsOrderFwxmTms.entity.OrderInfoDto;
import com.eci.project.omsOrderFwxmTms.entity.QueryOrderInfoDto;
import com.eci.project.omsOrderFwxmTms.validate.OmsOrderFwxmTmsVal;

import com.eci.sso.role.entity.UserContext;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * 委托内容 - 运输 Service 业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-04-14
 */
@Service
@Slf4j
public class OmsOrderFwxmTmsService implements EciBaseService<OmsOrderFwxmTmsEntity> {

    @Autowired
    private OmsOrderFwxmTmsDao omsOrderFwxmTmsDao;

    @Autowired
    private OmsOrderFwxmTmsVal omsOrderFwxmTmsVal;


    @Override
    public TgPageInfo queryPageList(OmsOrderFwxmTmsEntity entity) {
        EciQuery<OmsOrderFwxmTmsEntity> eciQuery = EciQuery.buildQuery(entity);
        List<OmsOrderFwxmTmsEntity> entities = omsOrderFwxmTmsDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }

    CommonLib cmn = CommonLib.getInstance();

    public TgPageInfo queryPageListByOrderDto(QueryOrderInfoDto queryOrderInfoDto) throws InterruptedException {

        /*
        int pageSize = 10;
        int pageNum = 1;
        try{
            pageNum = BllContext.getPaging().getPageNum();
            pageSize = BllContext.getPaging().getPageSize();
        }catch (Exception ignored){}
        String finalSql = "-- 使用CTE（公用表表达式）实现先分页，再关联\n" +
                "WITH PAGINATED_ORDERS AS (\n" +
                "    -- 步骤1: 核心部分。先对主表进行过滤和分页，只找出当前页需要的10个主键(ORDER_NO)\n" +
                "    SELECT\n" +
                "        oo.ORDER_NO\n" +
                "    FROM\n" +
                "        OMS_ORDER oo\n" +
                "    -- 将过滤所需的EXISTS子句和对B表的过滤改为INNER JOIN，以提前过滤数据\n" +
                "    INNER JOIN OMS_ORDER_GOODS b ON oo.ORDER_NO = b.ORDER_NO\n" +
                "    INNER JOIN OMS_ORDER_FW fw ON oo.ORDER_NO = fw.ORDER_NO AND oo.GROUP_CODE = fw.GROUP_CODE\n" +
                "    INNER JOIN OMS_ORDER_FWXM fwxm ON oo.ORDER_NO = fwxm.ORDER_NO\n" +
                "    INNER JOIN OMS_ORDER_PRE op ON oo.PRE_NO = op.PRE_NO\n" +
                "    INNER JOIN (\n" +
                "        SELECT \n" +
                "            X.ORDER_NO, \n" +
                "            MIN(XL.QHC) AS QHC\n" +
                "        FROM \n" +
                "            OMS_ORDER_FWXM_TMS X\n" +
                "        LEFT JOIN \n" +
                "            OMS_ORDER_FWXM_TMS_XL XL ON XL.TMS_NO = X.TMS_NO\n" ;

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getFwxmCode())) {
            finalSql += Zsr.String.format("        WHERE X.FWXM_CODE = {0} \n", cmn.SQLQ(queryOrderInfoDto.getFwxmCode()));
        }

                        finalSql+="        GROUP BY \n" +
                "            X.ORDER_NO\n" +
                "    ) qhc_agg ON oo.ORDER_NO = qhc_agg.ORDER_NO \n "+
                "    WHERE\n" +
                Zsr.String.format("        oo.GROUP_CODE = {0} \n", cmn.SQLQ(UserContext.getUserInfo().getCompanyCode()));

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getFwlxCode())) {
            finalSql += Zsr.String.format("        AND fw.FWLX_CODE = {0} \n", cmn.SQLQ(queryOrderInfoDto.getFwlxCode()));
        }
        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getFwxmCode())) {
            finalSql += Zsr.String.format("        AND fwxm.FWXM_CODE = {0} \n", cmn.SQLQ(queryOrderInfoDto.getFwxmCode()));
        }
        finalSql += "        -- 其他所有过滤条件放在这里\n";

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getOrderNo())) {
            finalSql += Zsr.String.format("        AND (oo.ORDER_NO LIKE {0} ) \n", cmn.sqlQL(queryOrderInfoDto.getOrderNo()));
        }
        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getCustomerOrderNo())) {
            finalSql += Zsr.String.format("        AND (oo.CUSTOMER_ORDER_NO LIKE {0} ) \n", cmn.sqlQL(queryOrderInfoDto.getCustomerOrderNo()));
        }
        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getConsigneeCode())) {
            finalSql += Zsr.String.format("        AND oo.CONSIGNEE_CODE = {0} \n", cmn.SQLQ(queryOrderInfoDto.getConsigneeCode()));
        }
        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getOpType())) {
            finalSql += Zsr.String.format("        AND oo.OP_TYPE = {0} \n", cmn.SQLQ(queryOrderInfoDto.getOpType()));
        }
        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getIsJjh())) {
            finalSql += Zsr.String.format("        AND oo.IS_JJH = {0} \n", cmn.SQLQ(queryOrderInfoDto.getIsJjh()));
        }

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getShipper())) {
            finalSql += Zsr.String.format("        AND (oo.SHIPPER LIKE {0} ) \n", cmn.sqlQL(queryOrderInfoDto.getShipper()));
        }

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getReceiver())) {
            finalSql += Zsr.String.format("        AND (oo.RECEIVER LIKE {0} ) \n", cmn.sqlQL(queryOrderInfoDto.getReceiver()));
        }

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getPreNo())) {
            finalSql += Zsr.String.format("        AND (oo.PRE_NO LIKE {0} ) \n", cmn.sqlQL(queryOrderInfoDto.getPreNo()));
        }

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getSysDocNo())) {
            finalSql += Zsr.String.format("        AND (oo.SYS_DOC_NO LIKE {0} ) \n", cmn.sqlQL(queryOrderInfoDto.getSysDocNo()));
        }

//        finalSql += "        AND oo.CREATE_DATE >= to_date('2025-07-01 00:00:00', 'yyyy-MM-dd HH24:mi:ss')\n";
//        finalSql += "        AND oo.CREATE_DATE < to_date('2025-08-01 00:00:00', 'yyyy-MM-dd HH24:mi:ss')\n";
//        finalSql += "        AND oo.REQUEST_OK_DATE >= to_date('2025-08-01 00:00:00', 'yyyy-MM-dd HH24:mi:ss')\n";
//        finalSql += "        AND oo.REQUEST_OK_DATE < to_date('2025-08-01 00:00:00', 'yyyy-MM-dd HH24:mi:ss')\n";
//        finalSql += "        AND oo.OP_DATE >= to_date('2025-06-01 00:00:00', 'yyyy-MM-dd HH24:mi:ss')\n";
//        finalSql += "        AND oo.OP_DATE < to_date('2025-07-01 00:00:00', 'yyyy-MM-dd HH24:mi:ss')\n";

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getCreateDateStart())) {
            finalSql += Zsr.String.format(" AND oo.CREATE_DATE >= to_date( {0} , 'yyyy-MM-dd HH24:mi:ss') ", cmn.SQLQ(
                    ZsrDateUtils.stringToDate(queryOrderInfoDto.getCreateDateStart())));
        }

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getCreateDateEnd())) {
            finalSql += Zsr.String.format(" AND oo.CREATE_DATE <  to_date( {0} , 'yyyy-MM-dd HH24:mi:ss')", cmn.SQLQ(
                    ZsrDateUtils.stringToDate(queryOrderInfoDto.getCreateDateEnd())));
        }
        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getRequestOkDateStart())) {
            finalSql += Zsr.String.format(" AND oo.REQUEST_OK_DATE >= to_date( {0} , 'yyyy-MM-dd HH24:mi:ss') ", cmn.SQLQ(
                    ZsrDateUtils.stringToDate(queryOrderInfoDto.getRequestOkDateStart())
            ));
        }
        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getRequestOkDateEnd())) {
            finalSql += Zsr.String.format(" AND oo.REQUEST_OK_DATE <  to_date( {0} , 'yyyy-MM-dd HH24:mi:ss')", cmn.SQLQ(
                    ZsrDateUtils.stringToDate(queryOrderInfoDto.getRequestOkDateEnd())
            ));
        }

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getOpDateStart())) {
            finalSql += Zsr.String.format(" AND oo.OP_DATE >= to_date( {0} , 'yyyy-MM-dd HH24:mi:ss') ", cmn.SQLQ(
                    ZsrDateUtils.stringToDate(queryOrderInfoDto.getOpDateStart())
            ));
        }
        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getOpDateEnd())) {
            finalSql += Zsr.String.format(" AND oo.OP_DATE <  to_date( {0} , 'yyyy-MM-dd HH24:mi:ss')", cmn.SQLQ(
                    ZsrDateUtils.stringToDate(queryOrderInfoDto.getOpDateEnd())
            ));
        }


        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getStage())) {
            finalSql += Zsr.String.format("        AND oo.STAGE = {0} \n", cmn.SQLQ(queryOrderInfoDto.getStage()));
        }

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getOpCompleteOk())) {
            finalSql += Zsr.String.format("        AND oo.OP_COMPLETE_OK = {0} \n", cmn.SQLQ(queryOrderInfoDto.getOpCompleteOk()));
        }

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getDataOk())) {
            finalSql += Zsr.String.format("        AND oo.DATA_OK = {0} \n", cmn.SQLQ(queryOrderInfoDto.getDataOk()));
        }

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getMbNo())) {
            finalSql += Zsr.String.format("        AND b.MB_NO LIKE {0} \n", cmn.SQLQL(queryOrderInfoDto.getMbNo()));
        }

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getHbNo())) {
            finalSql += Zsr.String.format("        AND b.HB_NO LIKE {0} \n", cmn.SQLQL(queryOrderInfoDto.getHbNo()));
        }

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getWarehouseInNo())) {
            finalSql += Zsr.String.format("        AND b.WAREHOUSE_IN_NO LIKE {0} \n", cmn.SQLQL(queryOrderInfoDto.getWarehouseInNo()));
        }

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getOrderStatus())) {
            finalSql += Zsr.String.format("        AND oo.STATUS = {0} \n", cmn.SQLQ(queryOrderInfoDto.getOrderStatus()));
        }

        // 添加去回程查询
        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getQhc())) {
            finalSql += Zsr.String.format("    AND qhc_agg.QHC =  {0} ) \n", cmn.SQLQ(queryOrderInfoDto.getQhc()));
        }

        finalSql += Zsr.String.format(" AND AND oo.ORDER_NO NOT LIKE '{0}%' \n", ZsrGloableConfig.jieDanMoBanPrefixCode);

        finalSql += "    -- 必须有一个确定的排序，才能保证分页的稳定性\n" +
                "    ORDER BY\n" +
                "        oo.CREATE_DATE DESC\n" +
                "    -- 这里是分页的关键！替换成你的分页参数\n" +
                Zsr.String.format("    LIMIT {0} OFFSET {1}\n", pageSize, pageNum-1) +
                ")\n" +
                "-- 步骤2: 将分页后的10个ORDER_NO与所有需要的表进行关联，获取最终数据\n" +
                "SELECT\n" +
                "    A.IS_JJH, A.ORDER_NO, A.CONSIGNEE_CODE, A.CUSTOMER_BU, A.CUSTOMER_ORDER_NO, A.SHIPPER, A.RECEIVER,\n" +
                "    A.OP_DATE, A.OP_TYPE, A.PRODUCT_CODE, A.FWLX_NAME, A.FKFA_CODE, A.STATUS, A.COMPANY_CODE, A.XZFA_NO,\n" +
                "    A.BIZ_MEMO, A.CREATE_DATE, A.JD_NODE, A.PRE_NO, A.CANCEL_USER, A.CANCEL_USER_NAME, A.CANCEL_DATE,\n" +
                "    A.CANCEL_REMARK, A.CREATE_USER_NAME, A.STAGE, A.JD_USER, A.GROUP_CODE, A.BIZ_REG_ID, A.CONFIRM_DATE,\n" +
                "    CASE WHEN A.OP_COMPLETE_OK = 'Y' THEN '√' ELSE ' ' END AS OP_COMPLETE_OK,\n" +
                "    CASE WHEN A.DATA_OK = 'Y' THEN '√' ELSE ' ' END AS DATA_OK,\n" +
                "    CASE WHEN A.ARAP_OK = 'Y' THEN '√' ELSE ' ' END AS ARAP_OK,\n" +
                "    A.ARAP_OK_DATE,\n" +
                "    cross_item_agg.CROSS_ITEM, -- 来自聚合JOIN\n" +
                "    A.IS_QRJD, A.IS_XZFF,\n" +
                "    sys_doc_agg.SYS_DOC_NO, -- 来自聚合JOIN\n" +
                "    CASE WHEN A.AP_OK = 'Y' THEN '√' ELSE NULL END AS AP_OK,\n" +
                "    fwxm_agg.FWXM_CODE, -- 来自聚合JOIN\n" +
                "    CASE WHEN oms_check.PRE_NO IS NOT NULL THEN 'Y' ELSE 'N' END AS IS_OMS,\n" +
                "    B.MB_NO, B.HB_NO, B.WAREHOUSE_IN_NO, B.PIECE_TOTAL,\n" +
                "    A.UDF9,\n" +
                "    C.SYS_ORDER_BATCH, C.CUSTOMER_ORDER_BATCH,\n" +
                "    qhc_agg.QHC -- 来自聚合JOIN\n" +
                "FROM\n" +
                "    PAGINATED_ORDERS P\n" +
                "    INNER JOIN OMS_ORDER A ON P.ORDER_NO = A.ORDER_NO\n" +
                "    LEFT JOIN OMS_ORDER_GOODS B ON A.ORDER_NO = B.ORDER_NO\n" +
                "    LEFT JOIN OMS_ORDER_BATCH C ON C.SYS_ORDER_BATCH = A.SYS_ORDER_BATCH\n" +
                "    -- 步骤3: 将原先SELECT中的相关子查询，全部改写成LEFT JOIN + GROUP BY的形式\n" +
                "    LEFT JOIN (\n" +
                "        SELECT PRE_NO, MIN(SYS_DOC_NO) AS SYS_DOC_NO FROM OMS_ORDER_PRE GROUP BY PRE_NO\n" +
                "    ) sys_doc_agg ON sys_doc_agg.PRE_NO = A.PRE_NO\n" +
                "    LEFT JOIN (\n" +
                "        SELECT ORDER_NO, MIN(FWXM_CODE) AS FWXM_CODE FROM OMS_ORDER_FWXM GROUP BY ORDER_NO\n" +
                "    ) fwxm_agg ON fwxm_agg.ORDER_NO = A.ORDER_NO\n" +
                "    LEFT JOIN (\n" +
                "        SELECT X.ORDER_NO, MIN(XL.CROSS_ITEM) AS CROSS_ITEM\n" +
                "        FROM OMS_ORDER_FWXM_TMS X\n" +
                "        LEFT JOIN OMS_ORDER_FWXM_TMS_XL XL ON XL.TMS_NO = X.TMS_NO\n" ;
        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getFwxmCode())) {
            finalSql += Zsr.String.format("        WHERE X.FWXM_CODE = {0} \n", cmn.SQLQ(queryOrderInfoDto.getFwxmCode()));
        }
               finalSql+= "        GROUP BY X.ORDER_NO\n" +
                "    ) cross_item_agg ON cross_item_agg.ORDER_NO = A.ORDER_NO\n" +
                "    LEFT JOIN (\n" +
                "        SELECT X.ORDER_NO, MIN(XL.QHC) AS QHC\n" +
                "        FROM OMS_ORDER_FWXM_TMS X\n" +
                "        LEFT JOIN OMS_ORDER_FWXM_TMS_XL XL ON XL.TMS_NO = X.TMS_NO\n" ;
        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getFwxmCode())) {
            finalSql += Zsr.String.format("        WHERE X.FWXM_CODE = {0} \n", cmn.SQLQ(queryOrderInfoDto.getFwxmCode()));
        }
            finalSql+=    "        GROUP BY X.ORDER_NO\n" +
                "    ) qhc_agg ON qhc_agg.ORDER_NO = A.ORDER_NO\n" +
                "    -- 用于判断 IS_OMS 的 LEFT JOIN\n" +
                "    LEFT JOIN OMS_ORDER_PRE oms_check ON oms_check.PRE_NO = A.PRE_NO AND oms_check.SYS_CODE = 'OMS'\n" +
                "-- 最终的排序，与CTE中的排序保持一致\n" +
                "ORDER BY\n" +
                "    A.CREATE_DATE DESC ";

         */


        String finalSql = "SELECT A.IS_JJH, " + //--2急货
                "       A.ORDER_NO, " + //--3订单号
                "       A.CONSIGNEE_CODE, " + //--4委托方
                "       A.CUSTOMER_BU, " + //--5客户事业部
                "       A.CUSTOMER_ORDER_NO, " + //--6客户单据编号
                "       A.SHIPPER, " + //--7实际发货方
                "       A.RECEIVER, " + //--8实际收货方
                "       A.OP_DATE, " +
                "       A.OP_TYPE, " + //--10业务类型
                "       A.PRODUCT_CODE, " + //--11业务产品/项目
                "       A.FWLX_NAME, " + //--12服务类型
                "       A.FKFA_CODE, " + //--13客户付款方案
                "       A.STATUS,\n" +
                "       A.COMPANY_CODE,\n" +
                "       A.XZFA_NO, " + //--14协作方案
                "       A.BIZ_MEMO, " + //--15业务备注
                "       A.CREATE_DATE, " + //--16创建时间
                "       A.JD_NODE, " + //--17接单组织
                "       A.PRE_NO, " + //--18协同编号 (pre)
                "       A.CANCEL_USER, " + //--作废人
                "       A.CANCEL_USER_NAME, " + //--作废人名称
                "       A.CANCEL_DATE, " + //--作废时间
                "       A.CANCEL_REMARK, " + //--作废原因
                "       A.CREATE_USER_NAME, " + //--创建人
                "       A.STAGE, " + //--执行阶段
                "       A.JD_USER, " + //--接单员
                "       A.GROUP_CODE,\n" +
                "       A.BIZ_REG_ID,\n" +
                "       A.CONFIRM_DATE, " + //--接单日期
                "       CASE\n" +
                "         WHEN A.OP_COMPLETE_OK = 'Y' THEN\n" +
                "          '√'\n" +
                "         ELSE\n" +
                "          ' '\n" +
                "       END OP_COMPLETE_OK, " + //--作业完成
                "       CASE\n" +
                "         WHEN A.DATA_OK = 'Y' THEN\n" +
                "          '√'\n" +
                "         ELSE\n" +
                "          ' '\n" +
                "       END DATA_OK, " + //--作业数据齐全
                "       CASE\n" +
                "         WHEN A.ARAP_OK = 'Y' THEN\n" +
                "          '√'\n" +
                "         ELSE\n" +
                "          ' '\n" +
                "       END ARAP_OK, " + //--结算完成日期（结案日期)
                "       A.ARAP_OK_DATE, " + //--结算完成日期（结案日期，
                "       A.CROSS_ITEM, " + //--项目
                "       A.IS_QRJD, " + //--确认接单必填项
                "       A.IS_XZFF, " + //--协作分发必填项
                "       (SELECT MIN(X.SYS_DOC_NO) FROM OMS_ORDER_PRE X WHERE X.PRE_NO = A.PRE_NO) AS SYS_DOC_NO, " + //--来源系统编号
                "       CASE\n" +
                "         WHEN A.AP_OK = 'Y' THEN\n" +
                "          '√'\n" +
                "         ELSE\n" +
                "          NULL\n" +
                "       END AS AP_OK,\n" +
//                "       NVL((OMS_NAMECOM(A.SHIPPER, A.GROUP_CODE, 'CRM_CUSTOMER_SFHF')),\n" +
//                "            A.SHIPPER) AS SHIPPER_NAME, " + //-- --9实际收货方
//                "       NVL((OMS_NAMECOM(A.RECEIVER, A.GROUP_CODE, 'CRM_CUSTOMER_SFHF')),\n" +
//                "            A.RECEIVER) AS RECEIVER_NAME, " + //-- --9实际收货方
//                "       OMS_NAMECOM(A.JD_USER, A.JD_COMPANY, 'OMS_SSO_USER') JD_USER_NAME,\n" +
//                "       NVL((BMC_NAME(A.FKFA_CODE, 'BMC_MMS_FKFA')), A.FKFA_CODE) AS FKFA_CODE_NAME,\n" +
                "       (SELECT MIN(X.FWXM_CODE) " +
                "          FROM OMS_ORDER_FWXM X\n" +
                "         WHERE X.ORDER_NO = A.ORDER_NO) AS FWXM_CODE,\n" +
                "       CASE\n" +
                "         WHEN EXISTS (SELECT DD.PRE_NO\n" +
                "                      FROM OMS_ORDER_PRE DD\n" +
                "                     WHERE DD.PRE_NO = A.PRE_NO\n" +
                "                       AND DD.SYS_CODE = 'OMS') THEN\n" +
                "          'Y'\n" +
                "         ELSE\n" +
                "          'N'\n" +
                "       END AS IS_OMS,\n" +
                "       B.MB_NO,\n" +
                "       B.HB_NO,\n" +
                "       B.WAREHOUSE_IN_NO,\n" +
                "       B.PIECE_TOTAL,\n" +
                "       A.UDF9,\n" +
                "       C.SYS_ORDER_BATCH,\n" +
                "       C.CUSTOMER_ORDER_BATCH,\n" +
                "  (SELECT MIN(XL.QHC)\n" +
                "          FROM OMS_ORDER_FWXM_TMS X\n" +
                "          LEFT JOIN OMS_ORDER_FWXM_TMS_XL XL\n" +
                "            ON XL.TMS_NO = X.TMS_NO\n" +
                "         WHERE X.ORDER_NO = A.ORDER_NO \n";
        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getFwxmCode())) {
            finalSql += Zsr.String.format(" AND X.FWXM_CODE = {0} ", cmn.SQLQ(queryOrderInfoDto.getFwxmCode()));
        }
        finalSql += "        ) AS QHC " +
                "  FROM (SELECT OO.*,\n" +
                "               (SELECT MIN(XL.CROSS_ITEM)\n" +
                "                  FROM OMS_ORDER_FWXM_TMS X\n" +
                "                  LEFT JOIN OMS_ORDER_FWXM_TMS_XL XL\n" +
                "                   ON XL.TMS_NO = X.TMS_NO\n" +
                "                 WHERE X.ORDER_NO = OO.ORDER_NO\n";

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getFwxmCode())) {
            finalSql += Zsr.String.format(" AND X.FWXM_CODE = {0} ", cmn.SQLQ(queryOrderInfoDto.getFwxmCode()));
        }

        finalSql += " ) AS CROSS_ITEM\n" +
                "          FROM OMS_ORDER OO) A\n" +
                "  LEFT JOIN OMS_ORDER_GOODS B\n" +
                "    ON A.ORDER_NO = B.ORDER_NO\n" +
                "  LEFT JOIN OMS_ORDER_BATCH C\n" +
                "    ON C.SYS_ORDER_BATCH = A.SYS_ORDER_BATCH\n" +
                " WHERE A.GROUP_CODE   = " + cmn.SQLQ(UserContext.getUserInfo().getCompanyCode());

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getFwlxCode())) {
            finalSql += " AND EXISTS(SELECT 1 FROM OMS_ORDER_FW FW WHERE FW.ORDER_NO = A.ORDER_NO AND A.GROUP_CODE = FW.GROUP_CODE AND FW.FWLX_CODE=" + cmn.SQLQ(queryOrderInfoDto.getFwlxCode()) + ")";
        }

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getOrderNo())) {
            finalSql += Zsr.String.format("   AND (A.ORDER_NO LIKE {0}) ", cmn.SQLQL(queryOrderInfoDto.getOrderNo()));
        }

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getCustomerOrderNo())) {
            finalSql += Zsr.String.format("   AND (A.CUSTOMER_ORDER_NO LIKE {0} ) ", cmn.SQLQL(queryOrderInfoDto.getCustomerOrderNo()));
        }

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getConsigneeCode())) {
            finalSql += Zsr.String.format("   AND A.CONSIGNEE_CODE = {0} ", cmn.SQLQ(queryOrderInfoDto.getConsigneeCode()));
        }

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getOpType())) {
            finalSql += Zsr.String.format("   AND A.OP_TYPE = {0} ", cmn.SQLQ(queryOrderInfoDto.getOpType()));
        }

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getIsJjh())) {
            finalSql += Zsr.String.format("   AND A.IS_JJH = {0} ", cmn.SQLQ(queryOrderInfoDto.getIsJjh()));
        }

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getJdUser())) {
            finalSql += Zsr.String.format("   AND A.JD_USER = {0} ", cmn.SQLQ(queryOrderInfoDto.getJdUser()));
        }



        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getShipper())) {
            finalSql += Zsr.String.format("   AND (A.SHIPPER LIKE {0} ) ", cmn.SQLQL(queryOrderInfoDto.getShipper()));
        }

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getReceiver())) {
            finalSql += Zsr.String.format(" AND (A.RECEIVER LIKE {0} ) ", cmn.SQLQL(queryOrderInfoDto.getReceiver()));
        }

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getPreNo())) {
            finalSql += Zsr.String.format(" AND (A.PRE_NO LIKE {0} ) ", cmn.SQLQL(queryOrderInfoDto.getPreNo()));
        }


        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getFwxmCode())) {
            finalSql += "  AND EXISTS (SELECT 1 FROM OMS_ORDER_FWXM X  WHERE X.ORDER_NO = A.ORDER_NO AND X.FWXM_CODE=" + cmn.SQLQ(queryOrderInfoDto.getFwxmCode()) + ")  ";
        }

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getSysDocNo())) {
            finalSql += " AND EXISTS(SELECT 1 FROM OMS_ORDER_PRE X WHERE SYS_DOC_NO LIKE " + cmn.SQLQL(queryOrderInfoDto.getSysDocNo()) + " AND A.PRE_NO=X.PRE_NO) ";
        }

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getStage())) {
            finalSql += Zsr.String.format(" AND A.STAGE = {0} ", cmn.SQLQ(queryOrderInfoDto.getStage()));
        }

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getCreateDateStart())) {
            finalSql += Zsr.String.format(" AND A.CREATE_DATE >= to_date( {0} , 'yyyy-MM-dd HH24:mi:ss') ", cmn.SQLQ(
                    ZsrDateUtils.stringToDate(queryOrderInfoDto.getCreateDateStart())));
        }

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getCreateDateEnd())) {
            finalSql += Zsr.String.format(" AND A.CREATE_DATE <  to_date( {0} , 'yyyy-MM-dd HH24:mi:ss')", cmn.SQLQ(
                    ZsrDateUtils.stringToDate(queryOrderInfoDto.getCreateDateEnd())));
        }
        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getRequestOkDateStart())) {
            finalSql += Zsr.String.format(" AND A.REQUEST_OK_DATE >= to_date( {0} , 'yyyy-MM-dd HH24:mi:ss') ", cmn.SQLQ(
                    ZsrDateUtils.stringToDate(queryOrderInfoDto.getRequestOkDateStart())
            ));
        }
        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getRequestOkDateEnd())) {
            finalSql += Zsr.String.format(" AND A.REQUEST_OK_DATE <  to_date( {0} , 'yyyy-MM-dd HH24:mi:ss')", cmn.SQLQ(
                    ZsrDateUtils.stringToDate(queryOrderInfoDto.getRequestOkDateEnd())
            ));
        }

        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getOpDateStart())) {
            finalSql += Zsr.String.format(" AND A.OP_DATE >= to_date( {0} , 'yyyy-MM-dd HH24:mi:ss') ", cmn.SQLQ(
                    ZsrDateUtils.stringToDate(queryOrderInfoDto.getOpDateStart())
            ));
        }
        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getOpDateEnd())) {
            finalSql += Zsr.String.format(" AND A.OP_DATE <  to_date( {0} , 'yyyy-MM-dd HH24:mi:ss')", cmn.SQLQ(
                    ZsrDateUtils.stringToDate(queryOrderInfoDto.getOpDateEnd())
            ));
        }
        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getOpCompleteOk())) {
            finalSql += Zsr.String.format(" AND A.OP_COMPLETE_OK = {0} ", cmn.SQLQ(queryOrderInfoDto.getOpCompleteOk()));
        }
        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getDataOk())) {
            finalSql += Zsr.String.format(" AND A.DATA_OK = {0} ", cmn.SQLQ(queryOrderInfoDto.getDataOk()));
        }
        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getArapOk())) {
            finalSql += Zsr.String.format(" AND A.ARAP_OK = {0} ", cmn.SQLQ(queryOrderInfoDto.getArapOk()));
        }
        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getApOkQuery())) {
            finalSql += Zsr.String.format(" AND A.AP_OK_QUERY = {0} ", cmn.SQLQ(queryOrderInfoDto.getApOkQuery()));
        }


        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getOpTypeUser()))//控制业务类型显示
        {
            finalSql += " AND EXISTS(SELECT 1 FROM FZGJ_BD_OP_TYPE_USER U WHERE U.USER_CODE=" + cmn.SQLQ(UserContext.getUserInfo().getUserLoginNo()) + " AND A.OP_TYPE=U.OP_TYPE_CODE)  ";
        }
        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getMbNo())) {
            finalSql += Zsr.String.format(" AND A.MB_NO LIKE {0} ", cmn.SQLQL(queryOrderInfoDto.getMbNo()));
        }
        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getHbNo())) {
            finalSql += Zsr.String.format(" AND A.HB_NO LIKE {0} ", cmn.SQLQL(queryOrderInfoDto.getHbNo()));
        }

        String WAREHOUSE_IN_NO = queryOrderInfoDto.getWarehouseInNo();
        String condition = "";
        if (!Zsr.String.IsNullOrWhiteSpace(WAREHOUSE_IN_NO)) {
            if (WAREHOUSE_IN_NO.contains(",")) {
                String[] listWare = WAREHOUSE_IN_NO.split(",");
                StringBuilder sb = new StringBuilder(" AND (");
                for (String s : listWare) {
                    sb.append(" B.WAREHOUSE_IN_NO LIKE '%").append(s.trim()).append("%' OR");
                }
                // 删除最后多余的 " OR"
                sb.delete(sb.length() - 3, sb.length());
                sb.append(")");
                condition = sb.toString();
            } else {
                condition = " AND B.WAREHOUSE_IN_NO LIKE '%" + WAREHOUSE_IN_NO + "%'";
            }
            finalSql += condition;
        }

        // 添加订单状态查询，默认查询所有订单
        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getOrderStatus())) {
            finalSql += Zsr.String.format(" AND A.STATUS = {0} ", cmn.SQLQ(queryOrderInfoDto.getOrderStatus()));
        } else {
            finalSql += Zsr.String.format(" AND A.STATUS IS NOT NULL ");
        }

        finalSql += Zsr.String.format(" AND A.ORDER_NO NOT LIKE '{0}%' ", ZsrGloableConfig.jieDanMoBanPrefixCode);

        // 添加去回程查询
        if (!Zsr.String.IsNullOrWhiteSpace(queryOrderInfoDto.getQhc())) {
            finalSql += Zsr.String.format("    AND EXISTS \n" +
                    " (SELECT 1 FROM OMS_ORDER_FWXM_TMS X\n" +
                    "          LEFT JOIN OMS_ORDER_FWXM_TMS_XL XL\n" +
                    "            ON XL.TMS_NO = X.TMS_NO\n" +
                    "         WHERE X.ORDER_NO = A.ORDER_NO\n" +
                    "           AND XL.QHC =  {0} " +
                    "        ) ", cmn.SQLQ(queryOrderInfoDto.getQhc()));
        }


        try {
            TgPageInfo<OrderInfoDto> pageInfo = DBHelper.selectPageList(finalSql, OrderInfoDto.class);
            return pageInfoCode2Name(pageInfo);
        } catch (Exception e) {
            return new TgPageInfo();
        }

    }


    public TgPageInfo<OrderInfoDto> pageInfoCode2Name(TgPageInfo<OrderInfoDto> pageInfo) throws InterruptedException {
        if (pageInfo == null || pageInfo.getList() == null || pageInfo.getList().isEmpty()) {
            return pageInfo;
        }

        // 定义需要查询的字段映射关系
        Map<String, String> fieldTypeMap = new HashMap<>();
        fieldTypeMap.put("consigneeCode", "CRM_CUSTOMER_SFHF");
        fieldTypeMap.put("customerBu", "CRM_CUSTOMER_KHSYB");
        fieldTypeMap.put("opType", "OMS_BD_OP_TYPE");
        fieldTypeMap.put("xzfaNo", "OMS_XZFA");
        fieldTypeMap.put("crossItem", "OMS_BD_XM");

        Map<String, String> fieldTypeMap2 = new HashMap<>();
        fieldTypeMap.put("stage", "STAGE");
        fieldTypeMap.put("jdNode", "JD_NODE");

        // 收集所有需要查询的字段值
        Map<String, List<String>> typeValuesMap = new HashMap<>();
        Map<String, List<String>> typeValuesMap2 = new HashMap<>();
        pageInfo.getList().forEach(item -> {
            if (item != null) {
                fieldTypeMap.forEach((field, type) -> {
                    String value = item.getFieldValue(field);
                    if (value != null && !value.isEmpty()) {
                        typeValuesMap.computeIfAbsent(type, k -> new ArrayList<>()).add(value);
                    }
                });
                fieldTypeMap2.forEach((field, type) -> {
                    String value = item.getFieldValue(field);
                    if (value != null && !value.isEmpty()) {
                        typeValuesMap2.computeIfAbsent(type, k -> new ArrayList<>()).add(value);
                    }
                });
            }
        });

        try {
            // 批量查询所有类型的 CodeNameEntity
            Map<String, Map<String, String>> codeNameMaps = new HashMap<>();
            for (Map.Entry<String, List<String>> entry : typeValuesMap.entrySet()) {
                String type = entry.getKey();
                List<String> codes = entry.getValue();
                if (!codes.isEmpty()) {
                    List<CodeNameEntity> entities = getCodeNameEntities(type, codes);
                    codeNameMaps.put(type, buildCodeNameMap(entities));
                }
            }
            for (Map.Entry<String, List<String>> entry : typeValuesMap2.entrySet()) {
                String type = entry.getKey();
                List<String> codes = entry.getValue();
                if (!codes.isEmpty()) {
                    List<CodeNameEntity> entities = getCodeNameEntities2(type, codes);
                    codeNameMaps.put(type, buildCodeNameMap(entities));
                }
            }
            // 使用并行流更新 DTO
            pageInfo.getList().parallelStream().forEach(item -> {
                if (item != null) {
                    fieldTypeMap.forEach((field, type) -> {
                        Map<String, String> codeNameMap = codeNameMaps.get(type);
                        if (codeNameMap != null) {
                            String codeValue = item.getFieldValue(field);
                            if (codeValue != null) {
                                String name = codeNameMap.get(codeValue);
                                if (name != null) {
                                    item.push(field + "Name", name);
                                }
                            }
                        }
                    });
                }
            });
        } catch (Exception e) {
            log.error("Failed to process pageInfoCode2Name", e);
            throw new RuntimeException("Failed to process pageInfoCode2Name", e);
        }

        return pageInfo;
    }

    // 批量查询 CodeNameEntity
    private List<CodeNameEntity> getCodeNameEntities(String tableName, List<String> queryList) {
        String sql = SQLGenerator.OMS_NAMECOM(tableName, queryList);
        if (Zsr.String.IsNullOrWhiteSpace(sql)) {
            return new ArrayList<>();
        }
        List<CodeNameEntity> codeNameEntities = DBHelper.selectList(
                sql,
                CodeNameEntity.class);

        // Ensure list is not null before streaming
        if (codeNameEntities == null) {
            return new ArrayList<>();
        }

        // Apply distinct and return
        return codeNameEntities.stream()
                .distinct()
                .collect(Collectors.toList());
    }

    // 批量查询 CodeNameEntity
    private List<CodeNameEntity> getCodeNameEntities2(String tableName, List<String> queryList) {
        String sql = SQLGenerator.OMS_NAME(tableName, queryList);
        if (Zsr.String.IsNullOrWhiteSpace(sql)) {
            return new ArrayList<>();
        }
        List<CodeNameEntity> codeNameEntities = DBHelper.selectList(
                sql,
                CodeNameEntity.class);

        // Ensure list is not null before streaming
        if (codeNameEntities == null) {
            return new ArrayList<>();
        }

        // Apply distinct and return
        return codeNameEntities.stream()
                .distinct()
                .collect(Collectors.toList());
    }


    // 构建 Code-Name 映射
    private Map<String, String> buildCodeNameMap(List<CodeNameEntity> entities) {
        return entities != null ? entities.stream()
                .collect(Collectors.toMap(CodeNameEntity::getCode, CodeNameEntity::getName, (v1, v2) -> v1, HashMap::new))
                : Collections.emptyMap();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderFwxmTmsEntity save(OmsOrderFwxmTmsEntity entity) {
        // 返回实体对象
        OmsOrderFwxmTmsEntity omsOrderFwxmTmsEntity = null;
        omsOrderFwxmTmsVal.saveValidate(entity, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            omsOrderFwxmTmsEntity = omsOrderFwxmTmsDao.insertOne(entity);

        } else {

            omsOrderFwxmTmsEntity = omsOrderFwxmTmsDao.updateByEntityId(entity);

        }
        return omsOrderFwxmTmsEntity;
    }

    @Override
    public List<OmsOrderFwxmTmsEntity> selectList(OmsOrderFwxmTmsEntity entity) {
        return omsOrderFwxmTmsDao.selectList(entity);
    }

    @Override
    public OmsOrderFwxmTmsEntity selectOneById(Serializable id) {
        return omsOrderFwxmTmsDao.selectById(id);
    }


    @Override
    public void insertBatch(List<OmsOrderFwxmTmsEntity> list) {
        omsOrderFwxmTmsDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return omsOrderFwxmTmsDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return omsOrderFwxmTmsDao.deleteById(id);
    }

}