package com.eci.project.omsOrderGoods.entity;

/**
 * @ClassName: ReqOmsOrderGoodsCostEntity 货物信息保存-货值&币制请求实体
 * @Author: guangyan.mei
 * @Date: 2025/6/11 10:21
 * @Description: TODO
 */
public class ReqOmsOrderGoodsCostEntity {

    public String COSTo;

    public String getCOSTo() {
        return COSTo;
    }

    public void setCOSTo(String COSTo) {
        this.COSTo = COSTo;
    }

    public String getCURRENCYo() {
        return CURRENCYo;
    }

    public void setCURRENCYo(String CURRENCYo) {
        this.CURRENCYo = CURRENCYo;
    }

    public String getCOSTt() {
        return COSTt;
    }

    public void setCOSTt(String COSTt) {
        this.COSTt = COSTt;
    }

    public String getCURRENCYt() {
        return CURRENCYt;
    }

    public void setCURRENCYt(String CURRENCYt) {
        this.CURRENCYt = CURRENCYt;
    }

    public String getCOSTth() {
        return COSTth;
    }

    public void setCOSTth(String COSTth) {
        this.COSTth = COSTth;
    }

    public String getCURRENCYth() {
        return CURRENCYth;
    }

    public void setCURRENCYth(String CURRENCYth) {
        this.CURRENCYth = CURRENCYth;
    }

    public String getCOSTf() {
        return COSTf;
    }

    public void setCOSTf(String COSTf) {
        this.COSTf = COSTf;
    }

    public String getCURRENCYf() {
        return CURRENCYf;
    }

    public void setCURRENCYf(String CURRENCYf) {
        this.CURRENCYf = CURRENCYf;
    }

    public String CURRENCYo;
    public String COSTt;
    public String CURRENCYt;
    public String COSTth;
    public String CURRENCYth;
    public String COSTf;
    public String CURRENCYf;
}
