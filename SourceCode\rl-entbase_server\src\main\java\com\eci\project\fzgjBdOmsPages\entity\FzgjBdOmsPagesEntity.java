package com.eci.project.fzgjBdOmsPages.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;


/**
* 录入订单编辑页面对象 FZGJ_BD_OMS_PAGES
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-03-19
*/
@ApiModel("录入订单编辑页面")
@TableName("FZGJ_BD_OMS_PAGES")
public class FzgjBdOmsPagesEntity extends FzgjBdOmsPagesBaseEntity{

}
