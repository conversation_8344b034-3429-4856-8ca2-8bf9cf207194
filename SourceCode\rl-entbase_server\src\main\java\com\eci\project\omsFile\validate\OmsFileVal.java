package com.eci.project.omsFile.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.omsFile.entity.OmsFileEntity;

import org.springframework.stereotype.Service;


/**
* OMS对接数据其他业务系统附件Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-06-04
*/
@Service
public class OmsFileVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(OmsFileEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(OmsFileEntity entity, BusinessType businessType) {

    }

}
