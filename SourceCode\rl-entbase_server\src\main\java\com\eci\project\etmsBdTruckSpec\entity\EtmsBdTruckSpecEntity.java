package com.eci.project.etmsBdTruckSpec.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.codeConvert.EciCode;
import com.eci.common.utils.ExportUtils.Excel;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.math.BigDecimal;
import java.util.Date;


/**
* 车辆规则对象 ETMS_BD_TRUCK_SPEC
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-03-21
*/
@ApiModel("车辆规则")
@TableName("ETMS_BD_TRUCK_SPEC")
@FieldNameConstants
public class EtmsBdTruckSpecEntity extends EciBaseEntity{
    /**
    * GUID
    */
    @ApiModelProperty("GUID(50)")
    @TableId("GUID")
    private String guid;

    /**
    * 车辆类型
    */
    @ApiModelProperty("车辆类型(20)")
    @TableField("TRUCK_TYPE")
    @EciCode("cllx")

    private String truckType;


    /**
    * 代码
    */
    @ApiModelProperty("代码(50)")
    @TableField("CODE")
    @Excel(value = "代码",order = 0)
    private String code;

    /**
    * 限重(T)
    */
    @ApiModelProperty("限重(T)(22)")
    @TableField("XWEIGHT")
    @JsonSerialize(using = ToStringSerializer.class)
    @Excel(value = "限重",order = 2)
    private BigDecimal xweight;

    /**
    * 限长(M)
    */
    @ApiModelProperty("限长(M)(22)")
    @TableField("XLENGTH")
    @JsonSerialize(using = ToStringSerializer.class)
    @Excel(value = "限长",order = 3)
    private BigDecimal xlength;

    /**
    * 限宽(M)
    */
    @ApiModelProperty("限宽(M)(22)")
    @TableField("XWIDTH")
    @JsonSerialize(using = ToStringSerializer.class)
    @Excel(value = "限宽",order = 4)
    private BigDecimal xwidth;

    /**
    * 限高(M)
    */
    @ApiModelProperty("限高(M)(22)")
    @TableField("XHEIGHT")
    @JsonSerialize(using = ToStringSerializer.class)
    @Excel(value = "限高",order = 5)
    private BigDecimal xheight;

    /**
    * 限容(L)
    */
    @ApiModelProperty("限容(L)(22)")
    @TableField("XVOLUME")
    @JsonSerialize(using = ToStringSerializer.class)
    @Excel(value = "限容",order = 6)
    private BigDecimal xvolume;

    /**
    * 限温-下限(摄氏度)
    */
    @ApiModelProperty("限温-下限(摄氏度)(22)")
    @TableField("XX_WD")
    @JsonSerialize(using = ToStringSerializer.class)
    @Excel(value = "限温-下限(摄氏度)",order = 7)
    private BigDecimal xxWd;

    /**
    * 限温-上限(摄氏度)
    */
    @ApiModelProperty("限温-上限(摄氏度)(22)")
    @TableField("SX_WD")
    @JsonSerialize(using = ToStringSerializer.class)
    @Excel(value = "限温-上限(摄氏度)",order = 8)
    private BigDecimal sxWd;

    /**
    * 创建人企业
    */
    @ApiModelProperty("创建人企业(50)")
    @TableField("CREATE_COMPANY")
    @Excel(value = "创建公司",order = 9)
    private String createCompany;

    /**
    * 修改时间
    */
    @ApiModelProperty("修改时间(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("修改时间开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("修改时间结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public EtmsBdTruckSpecEntity() {
        this.setSubClazz(EtmsBdTruckSpecEntity.class);
    }

    public EtmsBdTruckSpecEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public EtmsBdTruckSpecEntity setTruckType(String truckType) {
        this.truckType = truckType;
        this.nodifySetFiled("truckType", truckType);
        return this;
    }

    public String getTruckType() {
        this.nodifyGetFiled("truckType");
        return truckType;
    }

    public EtmsBdTruckSpecEntity setCode(String code) {
        this.code = code;
        this.nodifySetFiled("code", code);
        return this;
    }

    public String getCode() {
        this.nodifyGetFiled("code");
        return code;
    }

    public EtmsBdTruckSpecEntity setXweight(BigDecimal xweight) {
        this.xweight = xweight;
        this.nodifySetFiled("xweight", xweight);
        return this;
    }

    public BigDecimal getXweight() {
        this.nodifyGetFiled("xweight");
        return xweight;
    }

    public EtmsBdTruckSpecEntity setXlength(BigDecimal xlength) {
        this.xlength = xlength;
        this.nodifySetFiled("xlength", xlength);
        return this;
    }

    public BigDecimal getXlength() {
        this.nodifyGetFiled("xlength");
        return xlength;
    }

    public EtmsBdTruckSpecEntity setXwidth(BigDecimal xwidth) {
        this.xwidth = xwidth;
        this.nodifySetFiled("xwidth", xwidth);
        return this;
    }

    public BigDecimal getXwidth() {
        this.nodifyGetFiled("xwidth");
        return xwidth;
    }

    public EtmsBdTruckSpecEntity setXheight(BigDecimal xheight) {
        this.xheight = xheight;
        this.nodifySetFiled("xheight", xheight);
        return this;
    }

    public BigDecimal getXheight() {
        this.nodifyGetFiled("xheight");
        return xheight;
    }

    public EtmsBdTruckSpecEntity setXvolume(BigDecimal xvolume) {
        this.xvolume = xvolume;
        this.nodifySetFiled("xvolume", xvolume);
        return this;
    }

    public BigDecimal getXvolume() {
        this.nodifyGetFiled("xvolume");
        return xvolume;
    }

    public EtmsBdTruckSpecEntity setXxWd(BigDecimal xxWd) {
        this.xxWd = xxWd;
        this.nodifySetFiled("xxWd", xxWd);
        return this;
    }

    public BigDecimal getXxWd() {
        this.nodifyGetFiled("xxWd");
        return xxWd;
    }

    public EtmsBdTruckSpecEntity setSxWd(BigDecimal sxWd) {
        this.sxWd = sxWd;
        this.nodifySetFiled("sxWd", sxWd);
        return this;
    }

    public BigDecimal getSxWd() {
        this.nodifyGetFiled("sxWd");
        return sxWd;
    }

    public EtmsBdTruckSpecEntity setCreateCompany(String createCompany) {
        this.createCompany = createCompany;
        this.nodifySetFiled("createCompany", createCompany);
        return this;
    }

    public String getCreateCompany() {
        this.nodifyGetFiled("createCompany");
        return createCompany;
    }

    public EtmsBdTruckSpecEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public EtmsBdTruckSpecEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public EtmsBdTruckSpecEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public EtmsBdTruckSpecEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public EtmsBdTruckSpecEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public EtmsBdTruckSpecEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public EtmsBdTruckSpecEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public EtmsBdTruckSpecEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public EtmsBdTruckSpecEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public EtmsBdTruckSpecEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public EtmsBdTruckSpecEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

}
