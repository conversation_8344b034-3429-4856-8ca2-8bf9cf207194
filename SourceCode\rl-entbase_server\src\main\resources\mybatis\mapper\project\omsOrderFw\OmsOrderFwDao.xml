<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.omsOrderFw.dao.OmsOrderFwDao">
    <resultMap type="OmsOrderFwEntity" id="OmsOrderFwResult">
        <result property="guid" column="GUID"/>
        <result property="orderNo" column="ORDER_NO"/>
        <result property="preNo" column="PRE_NO"/>
        <result property="fwlxCode" column="FWLX_CODE"/>
        <result property="stage" column="STAGE"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="opCompleteOkDate" column="OP_COMPLETE_OK_DATE"/>
        <result property="opCompleteOk" column="OP_COMPLETE_OK"/>
        <result property="dataOkDate" column="DATA_OK_DATE"/>
        <result property="dataOk" column="DATA_OK"/>
        <result property="arapOk" column="ARAP_OK"/>
        <result property="arapOkDate" column="ARAP_OK_DATE"/>
    </resultMap>

    <sql id="selectOmsOrderFwEntityVo">
        select
            GUID,
            ORDER_NO,
            PRE_NO,
            FWLX_CODE,
            STAGE,
            CREATE_USER,
            CREATE_USER_NAME,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_USER_NAME,
            UPDATE_DATE,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            OP_COMPLETE_OK_DATE,
            OP_COMPLETE_OK,
            DATA_OK_DATE,
            DATA_OK,
            ARAP_OK,
            ARAP_OK_DATE
        from OMS_ORDER_FW
    </sql>
</mapper>