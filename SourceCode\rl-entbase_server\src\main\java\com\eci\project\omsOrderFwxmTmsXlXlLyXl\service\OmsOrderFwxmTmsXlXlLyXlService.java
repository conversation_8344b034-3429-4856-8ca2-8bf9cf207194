package com.eci.project.omsOrderFwxmTmsXlXlLyXl.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.omsOrderFwxmTmsXlXlLyXl.dao.OmsOrderFwxmTmsXlXlLyXlDao;
import com.eci.project.omsOrderFwxmTmsXlXlLyXl.entity.OmsOrderFwxmTmsXlXlLyXlEntity;
import com.eci.project.omsOrderFwxmTmsXlXlLyXl.validate.OmsOrderFwxmTmsXlXlLyXlVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 委托内容-程运序列-陆运-线路Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-06-04
*/
@Service
@Slf4j
public class OmsOrderFwxmTmsXlXlLyXlService implements EciBaseService<OmsOrderFwxmTmsXlXlLyXlEntity> {

    @Autowired
    private OmsOrderFwxmTmsXlXlLyXlDao omsOrderFwxmTmsXlXlLyXlDao;

    @Autowired
    private OmsOrderFwxmTmsXlXlLyXlVal omsOrderFwxmTmsXlXlLyXlVal;


    @Override
    public TgPageInfo queryPageList(OmsOrderFwxmTmsXlXlLyXlEntity entity) {
        EciQuery<OmsOrderFwxmTmsXlXlLyXlEntity> eciQuery = EciQuery.buildQuery(entity);
        List<OmsOrderFwxmTmsXlXlLyXlEntity> entities = omsOrderFwxmTmsXlXlLyXlDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderFwxmTmsXlXlLyXlEntity save(OmsOrderFwxmTmsXlXlLyXlEntity entity) {
        // 返回实体对象
        OmsOrderFwxmTmsXlXlLyXlEntity omsOrderFwxmTmsXlXlLyXlEntity = null;
        omsOrderFwxmTmsXlXlLyXlVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            omsOrderFwxmTmsXlXlLyXlEntity = omsOrderFwxmTmsXlXlLyXlDao.insertOne(entity);

        }else{

            omsOrderFwxmTmsXlXlLyXlEntity = omsOrderFwxmTmsXlXlLyXlDao.updateByEntityId(entity);

        }
        return omsOrderFwxmTmsXlXlLyXlEntity;
    }

    @Override
    public List<OmsOrderFwxmTmsXlXlLyXlEntity> selectList(OmsOrderFwxmTmsXlXlLyXlEntity entity) {
        return omsOrderFwxmTmsXlXlLyXlDao.selectList(entity);
    }

    @Override
    public OmsOrderFwxmTmsXlXlLyXlEntity selectOneById(Serializable id) {
        return omsOrderFwxmTmsXlXlLyXlDao.selectById(id);
    }


    @Override
    public void insertBatch(List<OmsOrderFwxmTmsXlXlLyXlEntity> list) {
        omsOrderFwxmTmsXlXlLyXlDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return omsOrderFwxmTmsXlXlLyXlDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return omsOrderFwxmTmsXlXlLyXlDao.deleteById(id);
    }

}