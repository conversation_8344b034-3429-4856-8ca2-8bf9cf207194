<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.etmsOpAttemper.dao.EtmsOpAttemperDao">
    <resultMap type="EtmsOpAttemperEntity" id="EtmsOpAttemperResult">
        <result property="guid" column="GUID"/>
        <result property="opNo" column="OP_NO"/>
        <result property="parentGuid" column="PARENT_GUID"/>
        <result property="attNo" column="ATT_NO"/>
        <result property="customerJobNo" column="CUSTOMER_JOB_NO"/>
        <result property="shipperCode" column="SHIPPER_CODE"/>
        <result property="deliveryCode" column="DELIVERY_CODE"/>
        <result property="accountCode" column="ACCOUNT_CODE"/>
        <result property="businessType" column="BUSINESS_TYPE"/>
        <result property="attMemo" column="ATT_MEMO"/>
        <result property="balanceMemo" column="BALANCE_MEMO"/>
        <result property="deliveryMode" column="DELIVERY_MODE"/>
        <result property="acceptDate" column="ACCEPT_DATE"/>
        <result property="acceptUser" column="ACCEPT_USER"/>
        <result property="status" column="STATUS"/>
        <result property="attFrom" column="ATT_FROM"/>
        <result property="amountAp" column="AMOUNT_AP"/>
        <result property="amountAr" column="AMOUNT_AR"/>
        <result property="createCompany" column="CREATE_COMPANY"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="zfReason" column="ZF_REASON"/>
        <result property="tdReason" column="TD_REASON"/>
        <result property="distributeDate" column="DISTRIBUTE_DATE"/>
        <result property="exeStatus" column="EXE_STATUS"/>
        <result property="senderCode" column="SENDER_CODE"/>
        <result property="consigneeCode" column="CONSIGNEE_CODE"/>
        <result property="startRequestDate" column="START_REQUEST_DATE"/>
        <result property="endRequestDate" column="END_REQUEST_DATE"/>
        <result property="startOpArea" column="START_OP_AREA"/>
        <result property="endOpArea" column="END_OP_AREA"/>
        <result property="farathestEnd" column="FARATHEST_END"/>
        <result property="pieces" column="PIECES"/>
        <result property="weight" column="WEIGHT"/>
        <result property="volume" column="VOLUME"/>
        <result property="sales" column="SALES"/>
        <result property="oper" column="OPER"/>
        <result property="confirmUser" column="CONFIRM_USER"/>
        <result property="confirmDate" column="CONFIRM_DATE"/>
        <result property="customerSys" column="CUSTOMER_SYS"/>
        <result property="customerSysNo" column="CUSTOMER_SYS_NO"/>
        <result property="accountMode" column="ACCOUNT_MODE"/>
        <result property="payMode" column="PAY_MODE"/>
        <result property="cargo" column="CARGO"/>
        <result property="carNo" column="CAR_NO"/>
        <result property="orgDepId" column="ORG_DEP_ID"/>
        <result property="orgDepCode" column="ORG_DEP_CODE"/>
        <result property="orgDepName" column="ORG_DEP_NAME"/>
        <result property="isUrgent" column="IS_URGENT"/>
        <result property="confirmUserOrgDepId" column="CONFIRM_USER_ORG_DEP_ID"/>
        <result property="confirmUserOrgDepCode" column="CONFIRM_USER_ORG_DEP_CODE"/>
        <result property="confirmUserOrgDepName" column="CONFIRM_USER_ORG_DEP_NAME"/>
        <result property="finishedDate" column="FINISHED_DATE"/>
        <result property="truckSpec" column="TRUCK_SPEC"/>
        <result property="batchNo" column="BATCH_NO"/>
        <result property="sendBms" column="SEND_BMS"/>
        <result property="sendBmsDate" column="SEND_BMS_DATE"/>
        <result property="sendBmsError" column="SEND_BMS_ERROR"/>
        <result property="fmsBusinessType" column="FMS_BUSINESS_TYPE"/>
        <result property="fmsOpNo" column="FMS_OP_NO"/>
        <result property="fmsInvNo" column="FMS_INV_NO"/>
        <result property="fmsContractNo" column="FMS_CONTRACT_NO"/>
        <result property="subordinGuid" column="SUBORDIN_GUID"/>
        <result property="subordinNo" column="SUBORDIN_NO"/>
        <result property="isWb" column="IS_WB"/>
        <result property="payWlf" column="PAY_WLF"/>
        <result property="nbzyNode" column="NBZY_NODE"/>
        <result property="xzwtNo" column="XZWT_NO"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="isKhzz" column="IS_KHZZ"/>
        <result property="bizRegId" column="BIZ_REG_ID"/>
        <result property="opDate" column="OP_DATE"/>
        <result property="khzzXd" column="KHZZ_XD"/>
        <result property="udf1" column="UDF1"/>
        <result property="udf2" column="UDF2"/>
        <result property="udf3" column="UDF3"/>
        <result property="udf4" column="UDF4"/>
        <result property="udf5" column="UDF5"/>
        <result property="udf6" column="UDF6"/>
        <result property="udf7" column="UDF7"/>
        <result property="udf8" column="UDF8"/>
        <result property="udf9" column="UDF9"/>
        <result property="udf10" column="UDF10"/>
        <result property="isWmsOutLoad" column="IS_WMS_OUT_LOAD"/>
        <result property="dataOk" column="DATA_OK"/>
        <result property="dataOkDate" column="DATA_OK_DATE"/>
        <result property="dataOkUser" column="DATA_OK_USER"/>
        <result property="batchNumber" column="BATCH_NUMBER"/>
        <result property="omsOrderNo" column="OMS_ORDER_NO"/>
        <result property="omsPreNo" column="OMS_PRE_NO"/>
    </resultMap>

    <sql id="selectEtmsOpAttemperEntityVo">
        select
            GUID,
            OP_NO,
            PARENT_GUID,
            ATT_NO,
            CUSTOMER_JOB_NO,
            SHIPPER_CODE,
            DELIVERY_CODE,
            ACCOUNT_CODE,
            BUSINESS_TYPE,
            ATT_MEMO,
            BALANCE_MEMO,
            DELIVERY_MODE,
            ACCEPT_DATE,
            ACCEPT_USER,
            STATUS,
            ATT_FROM,
            AMOUNT_AP,
            AMOUNT_AR,
            CREATE_COMPANY,
            CREATE_USER,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_DATE,
            ZF_REASON,
            TD_REASON,
            DISTRIBUTE_DATE,
            EXE_STATUS,
            SENDER_CODE,
            CONSIGNEE_CODE,
            START_REQUEST_DATE,
            END_REQUEST_DATE,
            START_OP_AREA,
            END_OP_AREA,
            FARATHEST_END,
            PIECES,
            WEIGHT,
            VOLUME,
            SALES,
            OPER,
            CONFIRM_USER,
            CONFIRM_DATE,
            CUSTOMER_SYS,
            CUSTOMER_SYS_NO,
            ACCOUNT_MODE,
            PAY_MODE,
            CARGO,
            CAR_NO,
            ORG_DEP_ID,
            ORG_DEP_CODE,
            ORG_DEP_NAME,
            IS_URGENT,
            CONFIRM_USER_ORG_DEP_ID,
            CONFIRM_USER_ORG_DEP_CODE,
            CONFIRM_USER_ORG_DEP_NAME,
            FINISHED_DATE,
            TRUCK_SPEC,
            BATCH_NO,
            SEND_BMS,
            SEND_BMS_DATE,
            SEND_BMS_ERROR,
            FMS_BUSINESS_TYPE,
            FMS_OP_NO,
            FMS_INV_NO,
            FMS_CONTRACT_NO,
            SUBORDIN_GUID,
            SUBORDIN_NO,
            IS_WB,
            PAY_WLF,
            NBZY_NODE,
            XZWT_NO,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            CREATE_USER_NAME,
            UPDATE_USER_NAME,
            IS_KHZZ,
            BIZ_REG_ID,
            OP_DATE,
            KHZZ_XD,
            UDF1,
            UDF2,
            UDF3,
            UDF4,
            UDF5,
            UDF6,
            UDF7,
            UDF8,
            UDF9,
            UDF10,
            IS_WMS_OUT_LOAD,
            DATA_OK,
            DATA_OK_DATE,
            DATA_OK_USER,
            BATCH_NUMBER,
            OMS_ORDER_NO,
            OMS_PRE_NO
        from ETMS_OP_ATTEMPER
    </sql>
</mapper>