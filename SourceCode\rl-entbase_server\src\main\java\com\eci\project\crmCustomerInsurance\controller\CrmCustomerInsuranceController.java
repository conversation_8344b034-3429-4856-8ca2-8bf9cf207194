package com.eci.project.crmCustomerInsurance.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.crmCustomerInsurance.service.CrmCustomerInsuranceService;
import com.eci.project.crmCustomerInsurance.entity.CrmCustomerInsuranceEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 司机保险管理Controller
*
* @<NAME_EMAIL>
* @date 2025-05-26
*/
@Api(tags = "司机保险管理")
@RestController
@RequestMapping("/crmCustomerInsurance")
public class CrmCustomerInsuranceController extends EciBaseController {

    @Autowired
    private CrmCustomerInsuranceService crmCustomerInsuranceService;


    @ApiOperation("司机保险管理:保存")
    @EciLog(title = "司机保险管理:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody CrmCustomerInsuranceEntity entity){
        CrmCustomerInsuranceEntity crmCustomerInsuranceEntity =crmCustomerInsuranceService.save(entity);
        return ResponseMsgUtil.success(10001,crmCustomerInsuranceEntity);
    }


    @ApiOperation("司机保险管理:查询列表")
    @EciLog(title = "司机保险管理:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody CrmCustomerInsuranceEntity entity){
        List<CrmCustomerInsuranceEntity> crmCustomerInsuranceEntities = crmCustomerInsuranceService.selectList(entity);
        return ResponseMsgUtil.success(10001,crmCustomerInsuranceEntities);
    }


    @ApiOperation("司机保险管理:分页查询列表")
    @EciLog(title = "司机保险管理:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody CrmCustomerInsuranceEntity entity){
        TgPageInfo tgPageInfo = crmCustomerInsuranceService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("司机保险管理:根据ID查一条")
    @EciLog(title = "司机保险管理:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody CrmCustomerInsuranceEntity entity){
        CrmCustomerInsuranceEntity  crmCustomerInsuranceEntity = crmCustomerInsuranceService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,crmCustomerInsuranceEntity);
    }


    @ApiOperation("司机保险管理:根据ID删除一条")
    @EciLog(title = "司机保险管理:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody CrmCustomerInsuranceEntity entity){
        int count = crmCustomerInsuranceService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("司机保险管理:根据ID字符串删除多条")
    @EciLog(title = "司机保险管理:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody CrmCustomerInsuranceEntity entity) {
        int count = crmCustomerInsuranceService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}