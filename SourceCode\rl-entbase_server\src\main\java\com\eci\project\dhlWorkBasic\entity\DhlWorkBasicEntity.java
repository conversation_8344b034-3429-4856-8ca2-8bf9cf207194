package com.eci.project.dhlWorkBasic.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;

import java.math.BigDecimal;
import java.util.Date;


/**
* 运单信息对象 DHL_WORK_BASIC
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-04-22
*/
@ApiModel("运单信息")
@TableName("DHL_WORK_BASIC")
@FieldNameConstants
public class DhlWorkBasicEntity extends EciBaseEntity{
    /**
    * GUID
    */
    @ApiModelProperty("GUID(36)")
    @TableId("GUID")
    private String guid;

    /**
    * 运单号码
    */
    @ApiModelProperty("运单号码(20)")
    @TableField("TO_NO")
    private String toNo;

    /**
    * 运输类型
    */
    @ApiModelProperty("运输类型(20)")
    @TableField("TRANSPORTATION_TYPE")
    private String transportationType;

    /**
    * 进出口类型 
    */
    @ApiModelProperty("进出口类型 (20)")
    @TableField("TRANSPORT_MODE")
    private String transportMode;

    /**
    * 业务类型
    */
    @ApiModelProperty("业务类型(20)")
    @TableField("PRODUCT_TYPE")
    private String productType;

    /**
    * 下单人
    */
    @ApiModelProperty("下单人(50)")
    @TableField("TO_CREATOR")
    private String toCreator;

    /**
    * 下单时间
    */
    @ApiModelProperty("下单时间(7)")
    @TableField("ISSUE_TIME")
    private Date issueTime;

    @ApiModelProperty("下单时间开始")
    @TableField(exist=false)
    private Date issueTimeStart;

    @ApiModelProperty("下单时间结束")
    @TableField(exist=false)
    private Date issueTimeEnd;

    /**
    * 联系电话  
    */
    @ApiModelProperty("联系电话  (100)")
    @TableField("TOCREATOR_CONTACT")
    private String tocreatorContact;

    /**
    * 运单状态Submit:待接收Back:退回 Accept:审核通过 Reject:审核退回 Departure:发车 Arrived:到达 Complete:作业完成、数据齐全
    */
    @ApiModelProperty("运单状态Submit:待接收Back:退回 Accept:审核通过 Reject:审核退回 Departure:发车 Arrived:到达 Complete:作业完成、数据齐全(20)")
    @TableField("TO_STATUS")
    private String toStatus;

    /**
    * 卡车类型
    */
    @ApiModelProperty("卡车类型(20)")
    @TableField("TRUCK_TYPE")
    private String truckType;

    /**
    * 卡车尺寸
    */
    @ApiModelProperty("卡车尺寸(20)")
    @TableField("TRUCK_SIZE")
    private String truckSize;

    /**
    * 海关申报类型
    */
    @ApiModelProperty("海关申报类型(20)")
    @TableField("CUSTOMS_DECLARATION_TYPE")
    private String customsDeclarationType;

    /**
    * 货物类型
    */
    @ApiModelProperty("货物类型(20)")
    @TableField("CARGO_TYPE")
    private String cargoType;

    /**
    * 货物名称
    */
    @ApiModelProperty("货物名称(4,000)")
    @TableField("ACTUAL_COMMODITY")
    private String actualCommodity;

    /**
    * 总数量
    */
    @ApiModelProperty("总数量(22)")
    @TableField("TOTAL_QUANTITY")
    private BigDecimal totalQuantity;

    /**
    * 数量单位
    */
    @ApiModelProperty("数量单位(10)")
    @TableField("QUANTITY_UNIT")
    private String quantityUnit;

    /**
    * 总重量（KGM）
    */
    @ApiModelProperty("总重量（KGM）(22)")
    @TableField("TOTAL_WEIGHT")
    private BigDecimal totalWeight;

    /**
    * 总体积(MTQ)
    */
    @ApiModelProperty("总体积(MTQ)(22)")
    @TableField("TOTAL_VOLUME")
    private BigDecimal totalVolume;

    /**
    * 备注（调度）
    */
    @ApiModelProperty("备注（调度）(4,000)")
    @TableField("REMARK_LP")
    private String remarkLp;

    /**
    * 备注(运单)
    */
    @ApiModelProperty("备注(运单)(4,000)")
    @TableField("REMARKS_TO")
    private String remarksTo;

    /**
    * 集装箱箱号
    */
    @ApiModelProperty("集装箱箱号(30)")
    @TableField("CONTAINER_NO")
    private String containerNo;

    /**
    * 集装箱皮重
    */
    @ApiModelProperty("集装箱皮重(22)")
    @TableField("CONTAINER_TARE_WEIGHT")
    private BigDecimal containerTareWeight;

    /**
    * 订舱号
    */
    @ApiModelProperty("订舱号(50)")
    @TableField("SO_NO")
    private String soNo;

    /**
    * 封条号
    */
    @ApiModelProperty("封条号(65)")
    @TableField("SEAL_NO")
    private String sealNo;

    /**
    * 提柜堆场
    */
    @ApiModelProperty("提柜堆场(50)")
    @TableField("PICKUP_CONTAINER_YARD")
    private String pickupContainerYard;

    /**
    * 还柜堆场
    */
    @ApiModelProperty("还柜堆场(50)")
    @TableField("RETURN_CONTAINER_YARD")
    private String returnContainerYard;

    /**
    * 装货地点
    */
    @ApiModelProperty("装货地点(50)")
    @TableField("LOADING_PLACE")
    private String loadingPlace;

    /**
    * 提空箱地点
    */
    @ApiModelProperty("提空箱地点(50)")
    @TableField("EMPTY_CONTAINER_CITY")
    private String emptyContainerCity;

    /**
    * 船公司
    */
    @ApiModelProperty("船公司(100)")
    @TableField("CARRIER")
    private String carrier;

    /**
    * 船名
    */
    @ApiModelProperty("船名(35)")
    @TableField("VESSEL")
    private String vessel;

    /**
    * 航次
    */
    @ApiModelProperty("航次(10)")
    @TableField("VOYAGE")
    private String voyage;

    /**
    * 柜型
    */
    @ApiModelProperty("柜型(50)")
    @TableField("CONTAINER_TYPE")
    private String containerType;

    /**
    * 柜数量
    */
    @ApiModelProperty("柜数量(22)")
    @TableField("CONTAINER_QTY")
    private Integer containerQty;

    /**
    * 要求到达(DHL)仓提货时间
    */
    @ApiModelProperty("要求到达(DHL)仓提货时间(7)")
    @TableField("EXPECTED_ARRIVE_WHS_TIME")
    private Date expectedArriveWhsTime;

    @ApiModelProperty("要求到达(DHL)仓提货时间开始")
    @TableField(exist=false)
    private Date expectedArriveWhsTimeStart;

    @ApiModelProperty("要求到达(DHL)仓提货时间结束")
    @TableField(exist=false)
    private Date expectedArriveWhsTimeEnd;

    /**
    * 要求到达工厂时间
    */
    @ApiModelProperty("要求到达工厂时间(7)")
    @TableField("EXPECTED_ARRIVE_FACTORY_TIME")
    private Date expectedArriveFactoryTime;

    @ApiModelProperty("要求到达工厂时间开始")
    @TableField(exist=false)
    private Date expectedArriveFactoryTimeStart;

    @ApiModelProperty("要求到达工厂时间结束")
    @TableField(exist=false)
    private Date expectedArriveFactoryTimeEnd;

    /**
    * 币别
    */
    @ApiModelProperty("币别(20)")
    @TableField("CURRENCY")
    private String currency;

    /**
    * 税率
    */
    @ApiModelProperty("税率(22)")
    @TableField("TAX_RATE")
    private BigDecimal taxRate;

    /**
    * 参考运费
    */
    @ApiModelProperty("参考运费(22)")
    @TableField("REFERENCE_FREIGHT")
    private BigDecimal referenceFreight;

    /**
    * 总费用
    */
    @ApiModelProperty("总费用(22)")
    @TableField("TOTAL_COST")
    private BigDecimal totalCost;

    /**
    * 参考运费（含税）
    */
    @ApiModelProperty("参考运费（含税）(22)")
    @TableField("TAX_REFERENCE_FREIGHT")
    private BigDecimal taxReferenceFreight;

    /**
    * 总费用（含税）
    */
    @ApiModelProperty("总费用（含税）(22)")
    @TableField("TAX_TOTAL_COST")
    private BigDecimal taxTotalCost;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 最后修改日期
    */
    @ApiModelProperty("最后修改日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("最后修改日期开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("最后修改日期结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(200)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 协作任务编号
    */
    @ApiModelProperty("协作任务编号(50)")
    @TableField("WORK_NO")
    private String workNo;

    /**
    * 订单号
    */
    @ApiModelProperty("订单号(36)")
    @TableField("ORDER_NO")
    private String orderNo;

    /**
    * 委托单编号
    */
    @ApiModelProperty("委托单编号(36)")
    @TableField("PRE_NO")
    private String preNo;

    /**
    * 协作委托单编号
    */
    @ApiModelProperty("协作委托单编号(36)")
    @TableField("XZWT_NO")
    private String xzwtNo;

    /**
    * 业务数据唯一注册编号
    */
    @ApiModelProperty("业务数据唯一注册编号(50)")
    @TableField("BIZ_REG_ID")
    private String bizRegId;

    /**
    * 卡车自重
    */
    @ApiModelProperty("卡车自重(22)")
    @TableField("TRUCK_WEIGHT")
    private BigDecimal truckWeight;

    /**
    * iSee车牌号
    */
    @ApiModelProperty("iSee车牌号(50)")
    @TableField("I_SEE_TRUCK_PLATE_NO")
    private String iSeeTruckPlateNo;

    /**
    * WMS车牌号
    */
    @ApiModelProperty("WMS车牌号(50)")
    @TableField("WMS_TRUCK_PLATE_NO")
    private String wmsTruckPlateNo;

    /**
    * 车辆入闸时间
    */
    @ApiModelProperty("车辆入闸时间(7)")
    @TableField("VEHICLE_ENTRY_TIME")
    private Date vehicleEntryTime;

    @ApiModelProperty("车辆入闸时间开始")
    @TableField(exist=false)
    private Date vehicleEntryTimeStart;

    @ApiModelProperty("车辆入闸时间结束")
    @TableField(exist=false)
    private Date vehicleEntryTimeEnd;

    /**
    * 靠台时间
    */
    @ApiModelProperty("靠台时间(7)")
    @TableField("STANDING_TIME")
    private Date standingTime;

    @ApiModelProperty("靠台时间开始")
    @TableField(exist=false)
    private Date standingTimeStart;

    @ApiModelProperty("靠台时间结束")
    @TableField(exist=false)
    private Date standingTimeEnd;

    /**
    * 开始装货时间
    */
    @ApiModelProperty("开始装货时间(7)")
    @TableField("LOADING_STARTING_TIME")
    private Date loadingStartingTime;

    @ApiModelProperty("开始装货时间开始")
    @TableField(exist=false)
    private Date loadingStartingTimeStart;

    @ApiModelProperty("开始装货时间结束")
    @TableField(exist=false)
    private Date loadingStartingTimeEnd;

    /**
    * 完成装货时间
    */
    @ApiModelProperty("完成装货时间(7)")
    @TableField("LOADING_END_TIME")
    private Date loadingEndTime;

    @ApiModelProperty("完成装货时间开始")
    @TableField(exist=false)
    private Date loadingEndTimeStart;

    @ApiModelProperty("完成装货时间结束")
    @TableField(exist=false)
    private Date loadingEndTimeEnd;

    /**
    * 出闸时间
    */
    @ApiModelProperty("出闸时间(7)")
    @TableField("VEHICLE_EXIT_TIME")
    private Date vehicleExitTime;

    @ApiModelProperty("出闸时间开始")
    @TableField(exist=false)
    private Date vehicleExitTimeStart;

    @ApiModelProperty("出闸时间结束")
    @TableField(exist=false)
    private Date vehicleExitTimeEnd;

    /**
    * 实际运费
    */
    @ApiModelProperty("实际运费(22)")
    @TableField("ACTUAL_FREIGHT")
    private BigDecimal actualFreight;

    /**
    * 实际运费（含税）
    */
    @ApiModelProperty("实际运费（含税）(22)")
    @TableField("TAXACTUAL_FREIGHT")
    private BigDecimal taxactualFreight;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public DhlWorkBasicEntity setGuid(String guid) {
        this.guid = guid;
        return this;
    }

    public String getGuid() {
        return guid;
    }

    public DhlWorkBasicEntity setToNo(String toNo) {
        this.toNo = toNo;
        return this;
    }

    public String getToNo() {
        return toNo;
    }

    public DhlWorkBasicEntity setTransportationType(String transportationType) {
        this.transportationType = transportationType;
        return this;
    }

    public String getTransportationType() {
        return transportationType;
    }

    public DhlWorkBasicEntity setTransportMode(String transportMode) {
        this.transportMode = transportMode;
        return this;
    }

    public String getTransportMode() {
        return transportMode;
    }

    public DhlWorkBasicEntity setProductType(String productType) {
        this.productType = productType;
        return this;
    }

    public String getProductType() {
        return productType;
    }

    public DhlWorkBasicEntity setToCreator(String toCreator) {
        this.toCreator = toCreator;
        return this;
    }

    public String getToCreator() {
        return toCreator;
    }

    public DhlWorkBasicEntity setIssueTime(Date issueTime) {
        this.issueTime = issueTime;
        return this;
    }

    public Date getIssueTime() {
        return issueTime;
    }

    public DhlWorkBasicEntity setIssueTimeStart(Date issueTimeStart) {
        this.issueTimeStart = issueTimeStart;
        return this;
    }

    public Date getIssueTimeStart() {
        return issueTimeStart;
    }

    public DhlWorkBasicEntity setIssueTimeEnd(Date issueTimeEnd) {
        this.issueTimeEnd = issueTimeEnd;
        return this;
    }

    public Date getIssueTimeEnd() {
        return issueTimeEnd;
    }
    public DhlWorkBasicEntity setTocreatorContact(String tocreatorContact) {
        this.tocreatorContact = tocreatorContact;
        return this;
    }

    public String getTocreatorContact() {
        return tocreatorContact;
    }

    public DhlWorkBasicEntity setToStatus(String toStatus) {
        this.toStatus = toStatus;
        return this;
    }

    public String getToStatus() {
        return toStatus;
    }

    public DhlWorkBasicEntity setTruckType(String truckType) {
        this.truckType = truckType;
        return this;
    }

    public String getTruckType() {
        return truckType;
    }

    public DhlWorkBasicEntity setTruckSize(String truckSize) {
        this.truckSize = truckSize;
        return this;
    }

    public String getTruckSize() {
        return truckSize;
    }

    public DhlWorkBasicEntity setCustomsDeclarationType(String customsDeclarationType) {
        this.customsDeclarationType = customsDeclarationType;
        return this;
    }

    public String getCustomsDeclarationType() {
        return customsDeclarationType;
    }

    public DhlWorkBasicEntity setCargoType(String cargoType) {
        this.cargoType = cargoType;
        return this;
    }

    public String getCargoType() {
        return cargoType;
    }

    public DhlWorkBasicEntity setActualCommodity(String actualCommodity) {
        this.actualCommodity = actualCommodity;
        return this;
    }

    public String getActualCommodity() {
        return actualCommodity;
    }

    public DhlWorkBasicEntity setTotalQuantity(BigDecimal totalQuantity) {
        this.totalQuantity = totalQuantity;
        return this;
    }

    public BigDecimal getTotalQuantity() {
        return totalQuantity;
    }

    public DhlWorkBasicEntity setQuantityUnit(String quantityUnit) {
        this.quantityUnit = quantityUnit;
        return this;
    }

    public String getQuantityUnit() {
        return quantityUnit;
    }

    public DhlWorkBasicEntity setTotalWeight(BigDecimal totalWeight) {
        this.totalWeight = totalWeight;
        return this;
    }

    public BigDecimal getTotalWeight() {
        return totalWeight;
    }

    public DhlWorkBasicEntity setTotalVolume(BigDecimal totalVolume) {
        this.totalVolume = totalVolume;
        return this;
    }

    public BigDecimal getTotalVolume() {
        return totalVolume;
    }

    public DhlWorkBasicEntity setRemarkLp(String remarkLp) {
        this.remarkLp = remarkLp;
        return this;
    }

    public String getRemarkLp() {
        return remarkLp;
    }

    public DhlWorkBasicEntity setRemarksTo(String remarksTo) {
        this.remarksTo = remarksTo;
        return this;
    }

    public String getRemarksTo() {
        return remarksTo;
    }

    public DhlWorkBasicEntity setContainerNo(String containerNo) {
        this.containerNo = containerNo;
        return this;
    }

    public String getContainerNo() {
        return containerNo;
    }

    public DhlWorkBasicEntity setContainerTareWeight(BigDecimal containerTareWeight) {
        this.containerTareWeight = containerTareWeight;
        return this;
    }

    public BigDecimal getContainerTareWeight() {
        return containerTareWeight;
    }

    public DhlWorkBasicEntity setSoNo(String soNo) {
        this.soNo = soNo;
        return this;
    }

    public String getSoNo() {
        return soNo;
    }

    public DhlWorkBasicEntity setSealNo(String sealNo) {
        this.sealNo = sealNo;
        return this;
    }

    public String getSealNo() {
        return sealNo;
    }

    public DhlWorkBasicEntity setPickupContainerYard(String pickupContainerYard) {
        this.pickupContainerYard = pickupContainerYard;
        return this;
    }

    public String getPickupContainerYard() {
        return pickupContainerYard;
    }

    public DhlWorkBasicEntity setReturnContainerYard(String returnContainerYard) {
        this.returnContainerYard = returnContainerYard;
        return this;
    }

    public String getReturnContainerYard() {
        return returnContainerYard;
    }

    public DhlWorkBasicEntity setLoadingPlace(String loadingPlace) {
        this.loadingPlace = loadingPlace;
        return this;
    }

    public String getLoadingPlace() {
        return loadingPlace;
    }

    public DhlWorkBasicEntity setEmptyContainerCity(String emptyContainerCity) {
        this.emptyContainerCity = emptyContainerCity;
        return this;
    }

    public String getEmptyContainerCity() {
        return emptyContainerCity;
    }

    public DhlWorkBasicEntity setCarrier(String carrier) {
        this.carrier = carrier;
        return this;
    }

    public String getCarrier() {
        return carrier;
    }

    public DhlWorkBasicEntity setVessel(String vessel) {
        this.vessel = vessel;
        return this;
    }

    public String getVessel() {
        return vessel;
    }

    public DhlWorkBasicEntity setVoyage(String voyage) {
        this.voyage = voyage;
        return this;
    }

    public String getVoyage() {
        return voyage;
    }

    public DhlWorkBasicEntity setContainerType(String containerType) {
        this.containerType = containerType;
        return this;
    }

    public String getContainerType() {
        return containerType;
    }

    public DhlWorkBasicEntity setContainerQty(Integer containerQty) {
        this.containerQty = containerQty;
        return this;
    }

    public Integer getContainerQty() {
        return containerQty;
    }

    public DhlWorkBasicEntity setExpectedArriveWhsTime(Date expectedArriveWhsTime) {
        this.expectedArriveWhsTime = expectedArriveWhsTime;
        return this;
    }

    public Date getExpectedArriveWhsTime() {
        return expectedArriveWhsTime;
    }

    public DhlWorkBasicEntity setExpectedArriveWhsTimeStart(Date expectedArriveWhsTimeStart) {
        this.expectedArriveWhsTimeStart = expectedArriveWhsTimeStart;
        return this;
    }

    public Date getExpectedArriveWhsTimeStart() {
        return expectedArriveWhsTimeStart;
    }

    public DhlWorkBasicEntity setExpectedArriveWhsTimeEnd(Date expectedArriveWhsTimeEnd) {
        this.expectedArriveWhsTimeEnd = expectedArriveWhsTimeEnd;
        return this;
    }

    public Date getExpectedArriveWhsTimeEnd() {
        return expectedArriveWhsTimeEnd;
    }
    public DhlWorkBasicEntity setExpectedArriveFactoryTime(Date expectedArriveFactoryTime) {
        this.expectedArriveFactoryTime = expectedArriveFactoryTime;
        return this;
    }

    public Date getExpectedArriveFactoryTime() {
        return expectedArriveFactoryTime;
    }

    public DhlWorkBasicEntity setExpectedArriveFactoryTimeStart(Date expectedArriveFactoryTimeStart) {
        this.expectedArriveFactoryTimeStart = expectedArriveFactoryTimeStart;
        return this;
    }

    public Date getExpectedArriveFactoryTimeStart() {
        return expectedArriveFactoryTimeStart;
    }

    public DhlWorkBasicEntity setExpectedArriveFactoryTimeEnd(Date expectedArriveFactoryTimeEnd) {
        this.expectedArriveFactoryTimeEnd = expectedArriveFactoryTimeEnd;
        return this;
    }

    public Date getExpectedArriveFactoryTimeEnd() {
        return expectedArriveFactoryTimeEnd;
    }
    public DhlWorkBasicEntity setCurrency(String currency) {
        this.currency = currency;
        return this;
    }

    public String getCurrency() {
        return currency;
    }

    public DhlWorkBasicEntity setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
        return this;
    }

    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public DhlWorkBasicEntity setReferenceFreight(BigDecimal referenceFreight) {
        this.referenceFreight = referenceFreight;
        return this;
    }

    public BigDecimal getReferenceFreight() {
        return referenceFreight;
    }

    public DhlWorkBasicEntity setTotalCost(BigDecimal totalCost) {
        this.totalCost = totalCost;
        return this;
    }

    public BigDecimal getTotalCost() {
        return totalCost;
    }

    public DhlWorkBasicEntity setTaxReferenceFreight(BigDecimal taxReferenceFreight) {
        this.taxReferenceFreight = taxReferenceFreight;
        return this;
    }

    public BigDecimal getTaxReferenceFreight() {
        return taxReferenceFreight;
    }

    public DhlWorkBasicEntity setTaxTotalCost(BigDecimal taxTotalCost) {
        this.taxTotalCost = taxTotalCost;
        return this;
    }

    public BigDecimal getTaxTotalCost() {
        return taxTotalCost;
    }

    public DhlWorkBasicEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        return this;
    }

    public String getCreateUser() {
        return createUser;
    }

    public DhlWorkBasicEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        return this;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public DhlWorkBasicEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        return this;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public DhlWorkBasicEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        return this;
    }

    public Date getCreateDateStart() {
        return createDateStart;
    }

    public DhlWorkBasicEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        return this;
    }

    public Date getCreateDateEnd() {
        return createDateEnd;
    }
    public DhlWorkBasicEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        return this;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public DhlWorkBasicEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        return this;
    }

    public String getUpdateUserName() {
        return updateUserName;
    }

    public DhlWorkBasicEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        return this;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public DhlWorkBasicEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        return this;
    }

    public Date getUpdateDateStart() {
        return updateDateStart;
    }

    public DhlWorkBasicEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        return this;
    }

    public Date getUpdateDateEnd() {
        return updateDateEnd;
    }
    public DhlWorkBasicEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        return this;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public DhlWorkBasicEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        return this;
    }

    public String getCompanyName() {
        return companyName;
    }

    public DhlWorkBasicEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        return this;
    }

    public String getNodeCode() {
        return nodeCode;
    }

    public DhlWorkBasicEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        return this;
    }

    public String getNodeName() {
        return nodeName;
    }

    public DhlWorkBasicEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        return this;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public DhlWorkBasicEntity setGroupName(String groupName) {
        this.groupName = groupName;
        return this;
    }

    public String getGroupName() {
        return groupName;
    }

    public DhlWorkBasicEntity setWorkNo(String workNo) {
        this.workNo = workNo;
        return this;
    }

    public String getWorkNo() {
        return workNo;
    }

    public DhlWorkBasicEntity setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        return this;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public DhlWorkBasicEntity setPreNo(String preNo) {
        this.preNo = preNo;
        return this;
    }

    public String getPreNo() {
        return preNo;
    }

    public DhlWorkBasicEntity setXzwtNo(String xzwtNo) {
        this.xzwtNo = xzwtNo;
        return this;
    }

    public String getXzwtNo() {
        return xzwtNo;
    }

    public DhlWorkBasicEntity setBizRegId(String bizRegId) {
        this.bizRegId = bizRegId;
        return this;
    }

    public String getBizRegId() {
        return bizRegId;
    }

    public DhlWorkBasicEntity setTruckWeight(BigDecimal truckWeight) {
        this.truckWeight = truckWeight;
        return this;
    }

    public BigDecimal getTruckWeight() {
        return truckWeight;
    }

    public DhlWorkBasicEntity setiSeeTruckPlateNo(String iSeeTruckPlateNo) {
        this.iSeeTruckPlateNo = iSeeTruckPlateNo;
        return this;
    }

    public String getiSeeTruckPlateNo() {
        return iSeeTruckPlateNo;
    }

    public DhlWorkBasicEntity setWmsTruckPlateNo(String wmsTruckPlateNo) {
        this.wmsTruckPlateNo = wmsTruckPlateNo;
        return this;
    }

    public String getWmsTruckPlateNo() {
        return wmsTruckPlateNo;
    }

    public DhlWorkBasicEntity setVehicleEntryTime(Date vehicleEntryTime) {
        this.vehicleEntryTime = vehicleEntryTime;
        return this;
    }

    public Date getVehicleEntryTime() {
        return vehicleEntryTime;
    }

    public DhlWorkBasicEntity setVehicleEntryTimeStart(Date vehicleEntryTimeStart) {
        this.vehicleEntryTimeStart = vehicleEntryTimeStart;
        return this;
    }

    public Date getVehicleEntryTimeStart() {
        return vehicleEntryTimeStart;
    }

    public DhlWorkBasicEntity setVehicleEntryTimeEnd(Date vehicleEntryTimeEnd) {
        this.vehicleEntryTimeEnd = vehicleEntryTimeEnd;
        return this;
    }

    public Date getVehicleEntryTimeEnd() {
        return vehicleEntryTimeEnd;
    }
    public DhlWorkBasicEntity setStandingTime(Date standingTime) {
        this.standingTime = standingTime;
        return this;
    }

    public Date getStandingTime() {
        return standingTime;
    }

    public DhlWorkBasicEntity setStandingTimeStart(Date standingTimeStart) {
        this.standingTimeStart = standingTimeStart;
        return this;
    }

    public Date getStandingTimeStart() {
        return standingTimeStart;
    }

    public DhlWorkBasicEntity setStandingTimeEnd(Date standingTimeEnd) {
        this.standingTimeEnd = standingTimeEnd;
        return this;
    }

    public Date getStandingTimeEnd() {
        return standingTimeEnd;
    }
    public DhlWorkBasicEntity setLoadingStartingTime(Date loadingStartingTime) {
        this.loadingStartingTime = loadingStartingTime;
        return this;
    }

    public Date getLoadingStartingTime() {
        return loadingStartingTime;
    }

    public DhlWorkBasicEntity setLoadingStartingTimeStart(Date loadingStartingTimeStart) {
        this.loadingStartingTimeStart = loadingStartingTimeStart;
        return this;
    }

    public Date getLoadingStartingTimeStart() {
        return loadingStartingTimeStart;
    }

    public DhlWorkBasicEntity setLoadingStartingTimeEnd(Date loadingStartingTimeEnd) {
        this.loadingStartingTimeEnd = loadingStartingTimeEnd;
        return this;
    }

    public Date getLoadingStartingTimeEnd() {
        return loadingStartingTimeEnd;
    }
    public DhlWorkBasicEntity setLoadingEndTime(Date loadingEndTime) {
        this.loadingEndTime = loadingEndTime;
        return this;
    }

    public Date getLoadingEndTime() {
        return loadingEndTime;
    }

    public DhlWorkBasicEntity setLoadingEndTimeStart(Date loadingEndTimeStart) {
        this.loadingEndTimeStart = loadingEndTimeStart;
        return this;
    }

    public Date getLoadingEndTimeStart() {
        return loadingEndTimeStart;
    }

    public DhlWorkBasicEntity setLoadingEndTimeEnd(Date loadingEndTimeEnd) {
        this.loadingEndTimeEnd = loadingEndTimeEnd;
        return this;
    }

    public Date getLoadingEndTimeEnd() {
        return loadingEndTimeEnd;
    }
    public DhlWorkBasicEntity setVehicleExitTime(Date vehicleExitTime) {
        this.vehicleExitTime = vehicleExitTime;
        return this;
    }

    public Date getVehicleExitTime() {
        return vehicleExitTime;
    }

    public DhlWorkBasicEntity setVehicleExitTimeStart(Date vehicleExitTimeStart) {
        this.vehicleExitTimeStart = vehicleExitTimeStart;
        return this;
    }

    public Date getVehicleExitTimeStart() {
        return vehicleExitTimeStart;
    }

    public DhlWorkBasicEntity setVehicleExitTimeEnd(Date vehicleExitTimeEnd) {
        this.vehicleExitTimeEnd = vehicleExitTimeEnd;
        return this;
    }

    public Date getVehicleExitTimeEnd() {
        return vehicleExitTimeEnd;
    }
    public DhlWorkBasicEntity setActualFreight(BigDecimal actualFreight) {
        this.actualFreight = actualFreight;
        return this;
    }

    public BigDecimal getActualFreight() {
        return actualFreight;
    }

    public DhlWorkBasicEntity setTaxactualFreight(BigDecimal taxactualFreight) {
        this.taxactualFreight = taxactualFreight;
        return this;
    }

    public BigDecimal getTaxactualFreight() {
        return taxactualFreight;
    }

}
