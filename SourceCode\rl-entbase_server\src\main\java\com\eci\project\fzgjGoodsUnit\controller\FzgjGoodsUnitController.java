package com.eci.project.fzgjGoodsUnit.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjGoodsUnit.service.FzgjGoodsUnitService;
import com.eci.project.fzgjGoodsUnit.entity.FzgjGoodsUnitEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 仓储货品单位Controller
*
* @<NAME_EMAIL>
* @date 2025-04-01
*/
@Api(tags = "仓储货品单位")
@RestController
@RequestMapping("/fzgjGoodsUnit")
public class FzgjGoodsUnitController extends EciBaseController {

    @Autowired
    private FzgjGoodsUnitService fzgjGoodsUnitService;


    @ApiOperation("仓储货品单位:保存")
    @EciLog(title = "仓储货品单位:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody FzgjGoodsUnitEntity entity){
        FzgjGoodsUnitEntity fzgjGoodsUnitEntity =fzgjGoodsUnitService.save(entity);
        return ResponseMsgUtil.success(10001,fzgjGoodsUnitEntity);
    }


    @ApiOperation("仓储货品单位:查询列表")
    @EciLog(title = "仓储货品单位:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjGoodsUnitEntity entity){
        List<FzgjGoodsUnitEntity> fzgjGoodsUnitEntities = fzgjGoodsUnitService.selectList(entity);
        return ResponseMsgUtil.success(10001,fzgjGoodsUnitEntities);
    }


    @ApiOperation("仓储货品单位:分页查询列表")
    @EciLog(title = "仓储货品单位:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjGoodsUnitEntity entity){
        TgPageInfo tgPageInfo = fzgjGoodsUnitService.queryPageList(entity);
        return ResponseMsgUtil.successPlus(10001,tgPageInfo);
    }


    @ApiOperation("仓储货品单位:根据ID查一条")
    @EciLog(title = "仓储货品单位:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjGoodsUnitEntity entity){
        FzgjGoodsUnitEntity  fzgjGoodsUnitEntity = fzgjGoodsUnitService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,fzgjGoodsUnitEntity);
    }


    @ApiOperation("仓储货品单位:根据ID删除一条")
    @EciLog(title = "仓储货品单位:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjGoodsUnitEntity entity){
        int count = fzgjGoodsUnitService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("仓储货品单位:根据ID字符串删除多条")
    @EciLog(title = "仓储货品单位:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjGoodsUnitEntity entity) {
        int count = fzgjGoodsUnitService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}