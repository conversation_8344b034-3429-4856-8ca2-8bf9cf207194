package com.eci.project.fzgjCrmEnterpriseApi.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.fzgjCrmEnterpriseApi.service.FzgjCrmEnterpriseApiService;
import com.eci.project.fzgjCrmEnterpriseApi.entity.FzgjCrmEnterpriseApiEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 注册企业发送接口Controller
*
* @<NAME_EMAIL>
* @date 2025-03-13
*/
@Api(tags = "注册企业发送接口")
@RestController
@RequestMapping("/fzgjCrmEnterpriseApi")
public class FzgjCrmEnterpriseApiController extends EciBaseController {

    @Autowired
    private FzgjCrmEnterpriseApiService fzgjCrmEnterpriseApiService;


    @ApiOperation("注册企业发送接口:保存")
    @EciLog(title = "注册企业发送接口:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody FzgjCrmEnterpriseApiEntity entity){
        FzgjCrmEnterpriseApiEntity fzgjCrmEnterpriseApiEntity =fzgjCrmEnterpriseApiService.save(entity);
        return ResponseMsgUtil.success(10001,fzgjCrmEnterpriseApiEntity);
    }


    @ApiOperation("注册企业发送接口:查询列表")
    @EciLog(title = "注册企业发送接口:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody FzgjCrmEnterpriseApiEntity entity){
        List<FzgjCrmEnterpriseApiEntity> fzgjCrmEnterpriseApiEntities = fzgjCrmEnterpriseApiService.selectList(entity);
        return ResponseMsgUtil.success(10001,fzgjCrmEnterpriseApiEntities);
    }


    @ApiOperation("注册企业发送接口:分页查询列表")
    @EciLog(title = "注册企业发送接口:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody FzgjCrmEnterpriseApiEntity entity){
        TgPageInfo tgPageInfo = fzgjCrmEnterpriseApiService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("注册企业发送接口:根据ID查一条")
    @EciLog(title = "注册企业发送接口:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody FzgjCrmEnterpriseApiEntity entity){
        FzgjCrmEnterpriseApiEntity  fzgjCrmEnterpriseApiEntity = fzgjCrmEnterpriseApiService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,fzgjCrmEnterpriseApiEntity);
    }


    @ApiOperation("注册企业发送接口:根据ID删除一条")
    @EciLog(title = "注册企业发送接口:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody FzgjCrmEnterpriseApiEntity entity){
        int count = fzgjCrmEnterpriseApiService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("注册企业发送接口:根据ID字符串删除多条")
    @EciLog(title = "注册企业发送接口:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody FzgjCrmEnterpriseApiEntity entity) {
        int count = fzgjCrmEnterpriseApiService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}