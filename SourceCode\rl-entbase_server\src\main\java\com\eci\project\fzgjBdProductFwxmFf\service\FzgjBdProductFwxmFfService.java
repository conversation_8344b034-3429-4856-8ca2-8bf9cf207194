package com.eci.project.fzgjBdProductFwxmFf.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjBdProductFwxmFf.dao.FzgjBdProductFwxmFfDao;
import com.eci.project.fzgjBdProductFwxmFf.entity.FzgjBdProductFwxmFfEntity;
import com.eci.project.fzgjBdProductFwxmFf.validate.FzgjBdProductFwxmFfVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 不分发的服务项目Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-01
*/
@Service
@Slf4j
public class FzgjBdProductFwxmFfService implements EciBaseService<FzgjBdProductFwxmFfEntity> {

    @Autowired
    private FzgjBdProductFwxmFfDao fzgjBdProductFwxmFfDao;

    @Autowired
    private FzgjBdProductFwxmFfVal fzgjBdProductFwxmFfVal;


    @Override
    public TgPageInfo queryPageList(FzgjBdProductFwxmFfEntity entity) {
        EciQuery<FzgjBdProductFwxmFfEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjBdProductFwxmFfEntity> entities = fzgjBdProductFwxmFfDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjBdProductFwxmFfEntity save(FzgjBdProductFwxmFfEntity entity) {
        // 返回实体对象
        FzgjBdProductFwxmFfEntity fzgjBdProductFwxmFfEntity = null;
        fzgjBdProductFwxmFfVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjBdProductFwxmFfEntity = fzgjBdProductFwxmFfDao.insertOne(entity);

        }else{

            fzgjBdProductFwxmFfEntity = fzgjBdProductFwxmFfDao.updateByEntityId(entity);

        }
        return fzgjBdProductFwxmFfEntity;
    }

    @Override
    public List<FzgjBdProductFwxmFfEntity> selectList(FzgjBdProductFwxmFfEntity entity) {
        return fzgjBdProductFwxmFfDao.selectList(entity);
    }

    @Override
    public FzgjBdProductFwxmFfEntity selectOneById(Serializable id) {
        return fzgjBdProductFwxmFfDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjBdProductFwxmFfEntity> list) {
        fzgjBdProductFwxmFfDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjBdProductFwxmFfDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjBdProductFwxmFfDao.deleteById(id);
    }

}