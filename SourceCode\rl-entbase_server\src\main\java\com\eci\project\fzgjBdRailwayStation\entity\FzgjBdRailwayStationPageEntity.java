package com.eci.project.fzgjBdRailwayStation.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.eci.project.fzgjBdArea.entity.FzgjBdAreaBaseEntity;
import io.swagger.annotations.ApiModelProperty;

/**
 * @ClassName: FzgjBdRailwayStationPageEntity - 分页列表返回实体
 * @Author: guangyan.mei
 * @Date: 2025/3/25 11:15
 * @Description: TODO
 */
public class FzgjBdRailwayStationPageEntity extends FzgjBdRailwayStationEntity {

    /**
     * countryCodeName
     */
    @TableId("countryName")
    private String countryName;

    /**
     * provinceGuidName
     */
    @TableId("provinceName")
    private String provinceName;

    /**
     * cityGuidName
     */
    @TableId("cityName")
    private String cityName;

    /**
     * districtGuidName
     */
    @TableId("districtName")
    private String districtName;

    /**
     * statusName
     */
    @TableId("statusName")
    private String statusName;

    public String getCountryName() {
        return countryName;
    }

    public void setCountryName(String countryName) {
        this.countryName = countryName;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getDistrictName() {
        return districtName;
    }

    public void setDistrictName(String districtName) {
        this.districtName = districtName;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

}
