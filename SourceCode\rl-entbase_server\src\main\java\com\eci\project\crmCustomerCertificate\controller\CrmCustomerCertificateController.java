package com.eci.project.crmCustomerCertificate.controller;

import com.eci.common.util.*;
import com.eci.crud.controller.EciBaseController;
import com.eci.page.TgPageInfo;
import com.eci.project.crmCustomerCertificate.service.CrmCustomerCertificateService;
import com.eci.project.crmCustomerCertificate.entity.CrmCustomerCertificateEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.eci.log.annotation.*;
import com.eci.log.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
* 司机证件管理Controller
*
* @<NAME_EMAIL>
* @date 2025-05-26
*/
@Api(tags = "司机证件管理")
@RestController
@RequestMapping("/crmCustomerCertificate")
public class CrmCustomerCertificateController extends EciBaseController {

    @Autowired
    private CrmCustomerCertificateService crmCustomerCertificateService;


    @ApiOperation("司机证件管理:保存")
    @EciLog(title = "司机证件管理:新增", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    @EciAction()
    public ResponseMsg save(@RequestBody CrmCustomerCertificateEntity entity){
        CrmCustomerCertificateEntity crmCustomerCertificateEntity =crmCustomerCertificateService.save(entity);
        return ResponseMsgUtil.success(10001,crmCustomerCertificateEntity);
    }


    @ApiOperation("司机证件管理:查询列表")
    @EciLog(title = "司机证件管理:查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectList")
    @EciAction()
    public ResponseMsg selectList(@RequestBody CrmCustomerCertificateEntity entity){
        List<CrmCustomerCertificateEntity> crmCustomerCertificateEntities = crmCustomerCertificateService.selectList(entity);
        return ResponseMsgUtil.success(10001,crmCustomerCertificateEntities);
    }


    @ApiOperation("司机证件管理:分页查询列表")
    @EciLog(title = "司机证件管理:分页查询列表", businessType = BusinessType.SELECT)
    @PostMapping("/selectPageList")
    @EciAction()
    public ResponseMsg selectPageList(@RequestBody CrmCustomerCertificateEntity entity){
        TgPageInfo tgPageInfo = crmCustomerCertificateService.queryPageList(entity);
        return ResponseMsgUtil.success(10001,tgPageInfo);
    }


    @ApiOperation("司机证件管理:根据ID查一条")
    @EciLog(title = "司机证件管理:根据ID查一条", businessType = BusinessType.SELECT)
    @PostMapping("/selectOneById")
    @EciAction()
    public ResponseMsg selectOneById(@RequestBody CrmCustomerCertificateEntity entity){
        CrmCustomerCertificateEntity  crmCustomerCertificateEntity = crmCustomerCertificateService.selectOneById(entity.getGuid());
        return ResponseMsgUtil.success(10001,crmCustomerCertificateEntity);
    }


    @ApiOperation("司机证件管理:根据ID删除一条")
    @EciLog(title = "司机证件管理:根据ID删除一条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteById")
    @EciAction()
    public ResponseMsg deleteById(@RequestBody CrmCustomerCertificateEntity entity){
        int count = crmCustomerCertificateService.deleteById(entity.getGuid());
        return ResponseMsgUtil.success(10001,count);
    }


    @ApiOperation("司机证件管理:根据ID字符串删除多条")
    @EciLog(title = "司机证件管理:根据ID字符串删除多条", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByIds")
    @EciAction()
    public ResponseMsg deleteByIds(@RequestBody CrmCustomerCertificateEntity entity) {
        int count = crmCustomerCertificateService.deleteByIds(entity.getIds());
        return ResponseMsgUtil.success(10001,count);
    }


}