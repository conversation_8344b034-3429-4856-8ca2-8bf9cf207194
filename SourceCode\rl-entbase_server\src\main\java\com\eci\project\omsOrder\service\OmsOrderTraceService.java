package com.eci.project.omsOrder.service;

import com.eci.common.db.DBHelper;
import com.eci.common.util.StringUtils;
import com.eci.common.utils.CommonLib;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.page.TgPageInfo;
import com.eci.project.omsOrder.dao.OmsOrderDao;
import com.eci.project.omsOrder.entity.ExceptionStatusListEntity;
import com.eci.project.omsOrder.entity.OmsOrderEntity;
import com.eci.project.omsOrder.entity.RequestOmsOrderTracePageEntity;
import com.eci.project.omsOrder.entity.ResOmsOrderTracePageEntity;
import com.eci.project.omsOrderFwxmWorkTrace.entity.LinkColorEntity;
import com.eci.project.omsOrderFwxmWorkTrace.entity.OmsOrderFwxmWorkInfoEntity;
import com.eci.project.omsOrderFwxmWorkTrace.entity.OrderTraceLinkEntity;
import com.eci.project.omsOrderFwxmWorkTrace.service.OmsOrderFwxmWorkTraceService;
import com.eci.sso.role.entity.UserContext;
import com.eci.wu.core.EntityBase;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName: OmsOrderTraceService - 订单跟踪-作业跟踪及反馈
 * @Author: guangyan.mei
 * @Date: 2025/5/7 11:02
 * @Description: TODO
 */
@Service
public class OmsOrderTraceService implements EciBaseService<OmsOrderEntity> {

    CommonLib cmn = CommonLib.getInstance();

    @Autowired
    private OmsOrderDao omsOrderDao;

    @Autowired
    private OmsOrderFwxmWorkTraceService omsOrderFwxmWorkTraceService;

    /**
     * 跟踪及反馈
     **/
    public List<OmsOrderFwxmWorkInfoEntity> selectTraceLinkList(RequestOmsOrderTracePageEntity entity) {

        List<OmsOrderFwxmWorkInfoEntity> list = omsOrderFwxmWorkTraceService.traceLinkSearch(entity.getOrderNo());

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S");

        for (OmsOrderFwxmWorkInfoEntity item : list) {

            // 环节名称背景颜色渲染
            if (item.getIsException().equals("Y")) {
                item.setLinkCodeName("#FF3A40");
            } else {
                String planOkDateStr = item.getPlanOkDate(); // 计划完成时间
                String actualOkDateStr = item.getActualOkDate(); // 实际完成时间

                if (StringUtils.hasValue(planOkDateStr)) {   // 计划完成时间有值
                    LocalDateTime planOkDate = LocalDateTime.parse(planOkDateStr, formatter);
                    if (!StringUtils.hasValue(actualOkDateStr)) {
                        if (LocalDateTime.now().isAfter(planOkDate)) {
                            item.setLinkCodeColor("#FFFF66");
                        }
                    } else {
                        LocalDateTime actualDate = LocalDateTime.parse(actualOkDateStr, formatter);

                        if (actualDate.isEqual(planOkDate) || actualDate.isBefore(planOkDate)) {
                            item.setLinkCodeColor("#66FF99");
                        } else {
                            item.setLinkCodeColor("#FFFF66");
                        }
                    }
                } else {
                    if (StringUtils.hasValue(actualOkDateStr)) {
                        item.setLinkCodeColor("#66FF99");
                    }
                }
            }

            // 反馈按钮控制(无作业系统反馈（NO）时启用，其余状态禁用)
            item.setRowFkStatus("Y"); // 默认禁用
            if (StringUtils.hasValue(item.getResponseCode()) && item.getResponseCode().equals("NO")) {
                item.setRowFkStatus("N"); // 启用
            }

            // 行内实际完成时间控制
            item.setRowSaveStatus("Y"); // 默认保存按钮禁用
            item.setActualOkDateStatus("Y"); // 默认实际完成时间可编辑

            if (StringUtils.hasValue(item.getResponseCode()) && item.getResponseCode().equals("NO")) {
                item.setActualOkDateStatus("N");
                item.setRowSaveStatus("N");
            }
        }

        return list;
    }


    /**
     * 重写-分页列表查询
     */
    public TgPageInfo selectTracePageList(RequestOmsOrderTracePageEntity entity) {
        String sql = selectPageListSql(entity);
        startPage();
        List<ResOmsOrderTracePageEntity> entities = DBHelper.selectList(sql, ResOmsOrderTracePageEntity.class);

        entities.forEach(item -> {
            if (entity.getOpCompleteOk().equals("N")) { // 作业中
                List<OrderTraceLinkEntity> traceLink = omsOrderFwxmWorkTraceService.traceLinkAllByOrderNoSearch(item.getOrderNo());
                // 异常类型状态集合
                List<ExceptionStatusListEntity> exceptionStatusList = new ArrayList<>();

                // Step 1: 按 FWXM_CODE 分组，每组取 ACTUAL_OK_DATE 最大的记录
                List<OrderTraceLinkEntity> list = traceLink.stream()
                        .collect(Collectors.groupingBy(
                                OrderTraceLinkEntity::getFwxmCode,
                                Collectors.collectingAndThen(
                                        Collectors.maxBy(Comparator.comparing(OrderTraceLinkEntity::getActualOkDate, Comparator.nullsFirst(Comparator.naturalOrder()))),
                                        Optional::get
                                )
                        ))
                        .values().stream()
                        .collect(Collectors.toList());

                // Step 2: 再按 FWLX_NAME 分组
                Map<String, List<OrderTraceLinkEntity>> dataGroup = list.stream()
                        .collect(Collectors.groupingBy(OrderTraceLinkEntity::getFwlxName));

                for (Map.Entry<String, List<OrderTraceLinkEntity>> entry : dataGroup.entrySet()) {
                    List<OrderTraceLinkEntity> groupItems = entry.getValue();

                    // 找到当前分组中 ACTUAL_OK_DATE 最大的项
                    Optional<OrderTraceLinkEntity> maxOpt = groupItems.stream()
                            .max(Comparator.comparing(OrderTraceLinkEntity::getActualOkDate, Comparator.nullsLast(Comparator.naturalOrder())));

                    if (maxOpt.isPresent()) {
                        OrderTraceLinkEntity data = maxOpt.get();

                        ExceptionStatusListEntity exceptionStatusListEntity = new ExceptionStatusListEntity();
                        String color = "#ffffff";
                        String value = "";

                        boolean hasRed = traceLink.stream()
                                .anyMatch(x -> LinkColorEntity.RED.equals(x.getLinkColor()) && Objects.equals(x.getFwxmCode(), data.getFwxmCode()));

                        boolean hasYellowNoDate = traceLink.stream()
                                .anyMatch(x -> LinkColorEntity.YELLOW.equals(x.getLinkColor()) &&
                                        Objects.equals(x.getFwxmCode(), data.getFwxmCode()) &&
                                        x.getActualOkDate() == null);

                        if (hasRed) {
                            color = "#FF3333";
                        } else if (hasYellowNoDate) {
                            color = "#FFFF66";
                        } else if (LinkColorEntity.YELLOW.equals(data.getLinkColor())) {
                            color = "#FFFF66";
                        } else if (LinkColorEntity.GREEN.equals(data.getLinkColor())) {
                            color = "#66FF99";
                        }

                        if (color.equals(LinkColorEntity.WHITE)) {
                            value = data.getFwlxName() + "(无反馈)";
                        } else {
                            value = data.getFwlxName() + "(" + data.getLinkCodeName() + ")";
                        }

                        exceptionStatusListEntity.setColor(color);
                        exceptionStatusListEntity.setValue(value);
                        exceptionStatusList.add(exceptionStatusListEntity);
                    }
                }

                item.exceptionList = exceptionStatusList;
            }

            if (entity.getOpCompleteOk().equals("Y")) { // 作业完成
                if (StringUtils.hasValue(item.getOpCompleteOk()) && item.getOpCompleteOk().equals("√")) {
                    item.setOpCompleteOknPart("√"); // 部分作业完成状态
                } else {
                    item.setOpCompleteOknPart("×"); // 部分作业完成状态
                }
            }
        });

        return EciQuery.getPageInfo(entities);
    }


    /***
     * 作业中-列表查询sql语句
     * **/
    public String selectPageListSql(RequestOmsOrderTracePageEntity entity) {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        String sql = "SELECT  A.PRE_NO, --1协同编号\n" +
//                "   B.SYS_CODE, --2来源系统\n" +
//                "   B.SYS_DOC_NO, --3来源系统业务编号\n" +
//                "   B.XD_DATE, --4下单时间\n" +
//                "   B.XD_USER, --5下单人\n" +
                "   A.CONSIGNEE_CODE, --6委托方\n" +
                "   (SELECT CUS.NAME  FROM CRM_CUSTOMER CUS WHERE CUS.STATUS='Y' AND CUS.GROUP_CODE = A.GROUP_CODE AND CUS.CODE = A.CONSIGNEE_CODE AND ROWNUM=1)  CONSIGNEE_CODE_NAME, --6委托方中文名称\n" +
                "   A.CUSTOMER_BU, --7客户事业部\n" +
                "   A.SHIPPER, --8实际发货方\n" +
                "   A.RECEIVER, -- --9实际收货方\n" +
                "   A.ACCOUNT_MEMO,\n" +
                "   DECODE(A.IS_JJH,'Y','是','N','否','') IS_JJH, --11急货\n" +
                "   A.OP_TYPE, --12业务类型\n" +
                "   (SELECT  OPTYPE.NAME  FROM FZGJ_BD_OP_TYPE OPTYPE  WHERE OPTYPE.STATUS = 'Y'  AND OPTYPE.SYS_CODE = 'OMS_ORDER' AND OPTYPE.CODE=A.OP_TYPE AND ROWNUM=1) OP_TYPE_NAME ,--业务类型中文名称\n" +
                "   A.PRODUCT_CODE,  --13业务产品/项目\n" +
                "   (SELECT F.NAME FROM FZGJ_BD_PRODUCT F WHERE F.BILL_CODE='OMS_ORDER' AND F.GROUP_CODE = A.GROUP_CODE AND F.CODE = A.PRODUCT_CODE AND F.OP_TYPE = A.OP_TYPE AND F.GROUP_CODE =" + cmn.SQLQ(UserContext.getUserInfo().getCompanyCode()) + " AND ROWNUM=1) AS PRODUCT_CODE_NAME, --13业务产品/项目中文名称\n" +
                "   A.FWLX_NAME, --14服务类型\n" +
                "   A.CUSTOMER_ORDER_NO, --15客户单据号\n" +
                "   A.REQUEST_OK_DATE, --16要求完成时间\n" +
                "   A.OP_COMPLETE_OK_DATE,--实际完成时间\n" +
                "   A.TEL, --17联系电话\n" +
                "   A.E_MAIL, --18电子邮箱\n" +
                "   A.ORDER_NO, --订单号\n" +
                "   A.STATUS, --订单状态\n" +
                "   A.STAGE, --订单阶段\n" +
                "   A.FKFA_CODE, --客户付款方案\n" +
                "   A.XZFA_NO, --协作方案编号\n" +
                "   A.BIZ_MEMO, --业务备注\n" +
                "   A.CREATE_USER, --创建人\n" +
                "   A.CREATE_USER_NAME, --创建人名称\n" +
                "   A.CREATE_DATE, --创建时间\n" +
                "   A.JD_USER, --接单员\n" +
                "   A.JD_NODE, --接单组织\n" +
                "   A.GROUP_CODE,\n" +
                "   A.CONFIRM_DATE, --接单确认时间\n" +
                "   A.SEND_USER, --调度员\n" +
                "   A.SEND_USER_NAME, --调度员名称\n" +
                "   A.SEND_DATE, --确认分发时间\n" +
//                "   A.STAGE,--订单阶段\n" +
                "   A.BIZ_REG_ID,\n" +
                "   G.MB_NO,\n" +
                "   G.HB_NO,\n" +
                "   G.WAREHOUSE_IN_NO,C.SYS_ORDER_BATCH,C.CUSTOMER_ORDER_BATCH,\n" +
                "  DECODE((SELECT CASE WHEN COUNT(1) > 0 THEN 'Y' ELSE 'N' END FROM FZGJ_EXCEPTION FE WHERE FE.GROUP_CODE = A.GROUP_CODE AND FE.ORDER_NO = A.ORDER_NO),'Y','是','1','是','否') ISEXCEPTION, --是否异常\n" +
                "  TO_CHAR(A.OP_DATE, 'YYYY-MM-DD') AS OP_DATE ," +
                "  CASE WHEN A.OP_COMPLETE_OK = 'Y' THEN '√' ELSE NULL END AS OP_COMPLETE_OK, --作业完成\n" +
                "  CASE WHEN A.DATA_OK = 'Y' THEN '√' ELSE NULL END AS DATA_OK,   --作业数据齐全\n" +
                "  CASE WHEN A.ARAP_OK = 'Y' THEN '√' ELSE NULL END AS ARAP_OK,    --结算完成\n" +
                "  CASE WHEN A.AP_OK = 'Y' THEN '√' ELSE NULL END AS AP_OK  --应付费用完成\n" +
                "  FROM OMS_ORDER A " +
                "  LEFT JOIN OMS_ORDER_GOODS G ON A.ORDER_NO=G.ORDER_NO\n" +
                "  LEFT JOIN OMS_ORDER_BATCH C ON C.SYS_ORDER_BATCH = A.SYS_ORDER_BATCH\n";

        sql += " WHERE 1=1 ";
        sql += " AND A.GROUP_CODE=" + cmn.SQLQ(UserContext.getUserInfo().getCompanyCode());
        sql += " AND A.STATUS IS NOT NULL";

        // 包含服务类型
        if (StringUtils.hasValue(entity.getFwlxCode())) {
            sql += " AND  EXISTS( SELECT 1 FROM OMS_ORDER_FW FW WHERE FW.ORDER_NO = A.ORDER_NO AND A.GROUP_CODE = FW.GROUP_CODE AND FW.FWLX_CODE=" + cmn.SQLQ(entity.getFwlxCode()) + ")";
        }

        // 包含服务项目
        if (StringUtils.hasValue(entity.getFwxmCode())) {
            sql += " AND EXISTS(SELECT 1 FROM OMS_ORDER_FWXM X WHERE X.ORDER_NO=A.ORDER_NO AND X.FWXM_CODE=" + cmn.SQLQ(entity.getFwxmCode()) + ")  ";
        }

        // 进仓编号
        if (StringUtils.hasValue(entity.getWarehouseInNo())) {
            StringBuilder sb = new StringBuilder();
            if (entity.getWarehouseInNo().contains(",")) {
                String[] listWare = entity.getWarehouseInNo().split(",");
                sb.append(" AND (");
                for (String s : listWare) {
                    sb.append(" G.WAREHOUSE_IN_NO LIKE '%").append(s).append("%' OR");
                }
                sb.delete(sb.length() - 2, sb.length()); // 删除最后的 " OR"
                sb.append(" )");
            } else {
                sb.append(" AND G.WAREHOUSE_IN_NO LIKE '%").append(entity.getWarehouseInNo()).append("%'");
            }

            sql += sb.toString();
        }

        // 订单号
        if (StringUtils.hasValue(entity.getOrderNo())) {
            sql += " AND A.ORDER_NO LIKE " + cmn.SQLQL(entity.getOrderNo());
        }
        // 客户单据编号
        if (StringUtils.isNotEmpty(entity.getCustomerOrderNo())) {
            sql += " AND A.CUSTOMER_ORDER_NO LIKE " + cmn.SQLQL(entity.getCustomerOrderNo());
        }

        // 业务日期
        if (entity.getOpDateStart() != null) {
            String strStartOPDate = sdf.format(entity.getOpDateStart());
            sql += " AND A.OP_DATE>=TO_DATE(" + cmn.SQLQ(strStartOPDate) + ",'yyyy-MM-dd')";
        }

        if (entity.getOpDateEnd() != null) {
            String strEndOPDate = sdf.format(entity.getOpDateEnd());
            sql += " AND A.OP_DATE<=TO_DATE(" + cmn.SQLQ(strEndOPDate) + ",'yyyy-MM-dd')";
        }
        // 实际发货方
        if (StringUtils.isNotEmpty(entity.getShipper())) {
            sql += " AND A.SHIPPER LIKE " + cmn.SQLQL(entity.getShipper());
        }
        // 实际收货方
        if (StringUtils.isNotEmpty(entity.getReceiver())) {
            sql += " AND A.RECEIVER LIKE " + cmn.SQLQL(entity.getReceiver());
        }
        // 委托方
        if (StringUtils.isNotEmpty(entity.getConsigneeCode())) {
            sql += " AND A.CONSIGNEE_CODE= " + cmn.SQLQ(entity.getConsigneeCode());
        }
        // 业务类型
        if (StringUtils.isNotEmpty(entity.getOpType())) {
            sql += " AND A.OP_TYPE= " + cmn.SQLQ(entity.getOpType());
        }
        // 业务产品/项目
        if (StringUtils.isNotEmpty(entity.getProductCode())) {
            sql += " AND A.PRODUCT_CODE= " + cmn.SQLQ(entity.getProductCode());
        }
        // 急货
        if (StringUtils.isNotEmpty(entity.getIsJjh())) {
            sql += " AND A.IS_JJH= " + cmn.SQLQ(entity.getIsJjh());
        }
        // 调度员
        if (StringUtils.isNotEmpty(entity.getSendUser())) {
            sql += " AND A.SEND_USER= " + cmn.SQLQ(entity.getSendUser());
        }

        // 要求完成日期
        if (entity.getRequestOkDateStart() != null) {
            String strStartRDate = sdf.format(entity.getRequestOkDateStart());
            sql += " AND A.REQUEST_OK_DATE>=TO_DATE(" + cmn.SQLQ(strStartRDate) + ",'yyyy-MM-dd')";
        }
        if (entity.getRequestOkDateEnd() != null) {
            String strEndRDate = sdf.format(entity.getRequestOkDateEnd());
            sql += " AND A.REQUEST_OK_DATE<=TO_DATE(" + cmn.SQLQ(strEndRDate) + ",'yyyy-MM-dd')";
        }

        // 确认分发日期
        if (entity.getSendDateStart() != null) {
            String strStartSendDate = sdf.format(entity.getSendDateStart());
            sql += " AND A.REQUEST_OK_DATE>=TO_DATE(" + cmn.SQLQ(strStartSendDate) + ",'yyyy-MM-dd')";
        }
        if (entity.getSendDateEnd() != null) {
            String strEndSendDate = sdf.format(entity.getSendDateEnd());
            sql += " AND A.REQUEST_OK_DATE<=TO_DATE(" + cmn.SQLQ(strEndSendDate) + ",'yyyy-MM-dd')";
        }

        // 是否异常
        if (StringUtils.isNotEmpty(entity.getIsException())) {
            sql += " AND (SELECT CASE WHEN COUNT(1) > 0 THEN 'Y' ELSE 'N' END FROM FZGJ_EXCEPTION EX WHERE EX.ORDER_NO=A.ORDER_NO AND EX.GROUP_CODE=" + cmn.SQLQ(UserContext.getUserInfo().getCompanyCode()) + ")=" + cmn.SQLQ(entity.getIsException());
        }

        // 是否延迟
        if (StringUtils.isNotEmpty(entity.getIsDelay())) {
            sql += entity.getIsDelay().equals("Y") ? " AND A.OP_COMPLETE_OK_DATE>A.REQUEST_OK_DATE " : " AND A.OP_COMPLETE_OK_DATE<A.REQUEST_OK_DATE ";
        }

        // 报关单号
        if (StringUtils.isNotEmpty(entity.getDecNo())) {
            sql += " AND EXISTS(SELECT 1 FROM OMS_ORDER_FWXM_WORK_FK_HZQD X WHERE X.ORDER_NO=A.ORDER_NO  AND X.GROUP_CODE=A.GROUP_CODE  AND X.DEC_NO LIKE " + cmn.SQLQ(entity.getDecNo()) + ")  ";
        }

        // 核注清单号
        if (StringUtils.isNotEmpty(entity.getCheckBillNo())) {
            sql += " AND EXISTS(SELECT 1 FROM OMS_ORDER_FWXM_WORK_FK_HZQD X WHERE X.ORDER_NO=A.ORDER_NO  AND X.GROUP_CODE=A.GROUP_CODE  AND X.CHECKBILL_NO LIKE " + cmn.SQLQ(entity.getCheckBillNo()) + ")  ";
        }

        // 金二核放单号
        if (StringUtils.isNotEmpty(entity.getHfNo())) {
            sql += " AND EXISTS(SELECT 1 FROM OMS_ORDER_FWXM_WORK_FK_HFD X WHERE X.ORDER_NO=A.ORDER_NO  AND X.GROUP_CODE=A.GROUP_CODE  AND X.HF_NO LIKE " + cmn.SQLQ(entity.getHfNo()) + ")  ";
        }

        //  陆运核放单号 no
        if (StringUtils.isNotEmpty(entity.getHfNoLy())) {
            sql += " AND EXISTS(SELECT 1 FROM OMS_ORDER_FWXM_WORK_FK_HFD X WHERE X.ORDER_NO=A.ORDER_NO  AND X.GROUP_CODE=A.GROUP_CODE  AND X.HF_NO_LY LIKE" + cmn.SQLQ(entity.getHfNoLy()) + ")  ";
        }

        //  主单号
        if (StringUtils.isNotEmpty(entity.getMbNo())) {
            sql += " AND G.MB_NO LIKE " + cmn.SQLQL(entity.getHfNoLy());
        }

        // 分单号
        if (StringUtils.isNotEmpty(entity.getHbNo())) {
            sql += " AND G.HB_NO LIKE " + cmn.SQLQL(entity.getHfNoLy());
        }

        // 结案
        if (StringUtils.isNotEmpty(entity.getIsJa())) {
            if (entity.getIsJa().equals("Y")) {
                sql += " AND A.STATUS = 'JA' ";
            }
            if (entity.getIsJa().equals("N")) {
                sql += " AND A.STATUS <> 'JA' ";
            }
        }

        // 作业数据齐全
        if (StringUtils.isNotEmpty(entity.getDataOk())) {
            sql += " AND A.DATA_OK = " + cmn.SQLQ(entity.getDataOk());
        }

        // 作业完成日期
        if (entity.getOpCompleteOkDateStart() != null) {
            String strStartOpComDate = sdf.format(entity.getOpCompleteOkDateStart());
            sql += " AND A.OP_COMPLETE_OK_DATE_START>=TO_DATE(" + cmn.SQLQ(strStartOpComDate) + ",'yyyy-MM-dd')";
        }
        if (entity.getSendDateEnd() != null) {
            String strEndOpComDate = sdf.format(entity.getOpCompleteOkDateEnd());
            sql += " AND A.OP_COMPLETE_OK_DATE_START<=TO_DATE(" + cmn.SQLQ(strEndOpComDate) + ",'yyyy-MM-dd')";
        }

        // N-作业中 Y-作业完成
        if (StringUtils.hasValue(entity.getOpCompleteOk())) {
            sql += " AND A.OP_COMPLETE_OK = " + cmn.SQLQ(entity.getOpCompleteOk());

            // 作业中
            if (entity.getOpCompleteOk().equals("N")) {
                // 订单状态 A.STATUS|string|=
                if (StringUtils.isNotEmpty(entity.getStatus())) {
                    sql += " AND A.STATUS=" + cmn.SQLQ(entity.getStatus());
                }
                // 订单阶段 A.STAGE|string|=
                if (StringUtils.isNotEmpty(entity.getStage())) {
                    sql += " AND A.STAGE=" + cmn.SQLQ(entity.getStage());
                }
            }
        }

        return sql;
    }

}
