<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.crmCustomer.dao.CrmCustomerDao">
    <resultMap type="CrmCustomerEntity" id="CrmCustomerResult">
        <result property="guid" column="GUID"/>
        <result property="code" column="CODE"/>
        <result property="name" column="NAME"/>
        <result property="shortName" column="SHORT_NAME"/>
        <result property="enName" column="EN_NAME"/>
        <result property="country" column="COUNTRY"/>
        <result property="province" column="PROVINCE"/>
        <result property="city" column="CITY"/>
        <result property="district" column="DISTRICT"/>
        <result property="address" column="ADDRESS"/>
        <result property="addressEn" column="ADDRESS_EN"/>
        <result property="tyCode" column="TY_CODE"/>
        <result property="person" column="PERSON"/>
        <result property="clDate" column="CL_DATE"/>
        <result property="zcCurr" column="ZC_CURR"/>
        <result property="zcCapital" column="ZC_CAPITAL"/>
        <result property="stockCode" column="STOCK_CODE"/>
        <result property="customNo" column="CUSTOM_NO"/>
        <result property="isSsgs" column="IS_SSGS"/>
        <result property="frdb" column="FRDB"/>
        <result property="employeeNum" column="EMPLOYEE_NUM"/>
        <result property="isNb" column="IS_NB"/>
        <result property="status" column="STATUS"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="memoKh" column="MEMO_KH"/>
        <result property="roleCode" column="ROLE_CODE"/>
        <result property="roleName" column="ROLE_NAME"/>
        <result property="companyType" column="COMPANY_TYPE"/>
        <result property="hzfwCodeKh" column="HZFW_CODE_KH"/>
        <result property="hzfwNameKh" column="HZFW_NAME_KH"/>
        <result property="khly" column="KHLY"/>
        <result property="ywhbjb" column="YWHBJB"/>
        <result property="saleUser" column="SALE_USER"/>
        <result property="skDateline" column="SK_DATELINE"/>
        <result property="payDateline" column="PAY_DATELINE"/>
        <result property="creditLimit" column="CREDIT_LIMIT"/>
        <result property="accountMode" column="ACCOUNT_MODE"/>
        <result property="payMode" column="PAY_MODE"/>
        <result property="jdFreeze" column="JD_FREEZE"/>
        <result property="kpCycle" column="KP_CYCLE"/>
        <result property="kpDateline" column="KP_DATELINE"/>
        <result property="accountKh" column="ACCOUNT_KH"/>
        <result property="bankKh" column="BANK_KH"/>
        <result property="taxNoKh" column="TAX_NO_KH"/>
        <result property="taxValKh" column="TAX_VAL_KH"/>
        <result property="invTypeKh" column="INV_TYPE_KH"/>
        <result property="jkCycle" column="JK_CYCLE"/>
        <result property="payFreeze" column="PAY_FREEZE"/>
        <result property="accountFp" column="ACCOUNT_FP"/>
        <result property="bankFp" column="BANK_FP"/>
        <result property="taxNoFp" column="TAX_NO_FP"/>
        <result property="taxValFp" column="TAX_VAL_FP"/>
        <result property="invTypeFp" column="INV_TYPE_FP"/>
        <result property="memoFp" column="MEMO_FP"/>
        <result property="hzfwCodeHz" column="HZFW_CODE_HZ"/>
        <result property="hzfwNameHz" column="HZFW_NAME_HZ"/>
        <result property="tel" column="TEL"/>
        <result property="isUserControl" column="IS_USER_CONTROL"/>
        <result property="customerB2b" column="CUSTOMER_B2B"/>
        <result property="memo" column="MEMO"/>
        <result property="isCustom" column="IS_CUSTOM"/>
        <result property="customerGroupCode" column="CUSTOMER_GROUP_CODE"/>
        <result property="customerGroupName" column="CUSTOMER_GROUP_NAME"/>
        <result property="comType" column="COM_TYPE"/>
        <result property="mail" column="MAIL"/>
        <result property="accountMemo" column="ACCOUNT_MEMO"/>
    </resultMap>

    <sql id="selectCrmCustomerEntityVo">
        select
            GUID,
            CODE,
            NAME,
            SHORT_NAME,
            EN_NAME,
            COUNTRY,
            PROVINCE,
            CITY,
            DISTRICT,
            ADDRESS,
            ADDRESS_EN,
            TY_CODE,
            PERSON,
            CL_DATE,
            ZC_CURR,
            ZC_CAPITAL,
            STOCK_CODE,
            CUSTOM_NO,
            IS_SSGS,
            FRDB,
            EMPLOYEE_NUM,
            IS_NB,
            STATUS,
            CREATE_USER,
            CREATE_USER_NAME,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_USER_NAME,
            UPDATE_DATE,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            MEMO_KH,
            ROLE_CODE,
            ROLE_NAME,
            COMPANY_TYPE,
            HZFW_CODE_KH,
            HZFW_NAME_KH,
            KHLY,
            YWHBJB,
            SALE_USER,
            SK_DATELINE,
            PAY_DATELINE,
            CREDIT_LIMIT,
            ACCOUNT_MODE,
            PAY_MODE,
            JD_FREEZE,
            KP_CYCLE,
            KP_DATELINE,
            ACCOUNT_KH,
            BANK_KH,
            TAX_NO_KH,
            TAX_VAL_KH,
            INV_TYPE_KH,
            JK_CYCLE,
            PAY_FREEZE,
            ACCOUNT_FP,
            BANK_FP,
            TAX_NO_FP,
            TAX_VAL_FP,
            INV_TYPE_FP,
            MEMO_FP,
            HZFW_CODE_HZ,
            HZFW_NAME_HZ,
            TEL,
            IS_USER_CONTROL,
            CUSTOMER_B2B,
            MEMO,
            IS_CUSTOM,
            CUSTOMER_GROUP_CODE,
            CUSTOMER_GROUP_NAME,
            COM_TYPE,
            MAIL,
            ACCOUNT_MEMO
        from CRM_CUSTOMER
    </sql>

    <select id="selectList1" parameterType="com.baomidou.mybatisplus.core.conditions.Wrapper"
            resultType="com.eci.project.crmCustomer.entity.CrmCustomerEntity">
            SELECT * FROM (
                      select
                          (SELECT name FROM FZGJ_BASE_DATA_DETAIL WHERE GROUP_CODE='IS_CUSTOM' AND CODE=A.IS_CUSTOM) as isCustomName,
                          A.COM_TYPE_NAME,
                          A.GUID,
                          A.CODE,
                          A.NAME,
                          A.SHORT_NAME,
                          A.EN_NAME,
                          A.COUNTRY,
                          A.PROVINCE,
                          A.CITY,
                          A.DISTRICT,
                          A.ADDRESS,
                          A.ADDRESS_EN,
                          A.TY_CODE,
                          A.PERSON,
                          A.CL_DATE,
                          A.ZC_CURR,
                          A.ZC_CAPITAL,
                          A.STOCK_CODE,
                          A.CUSTOM_NO,
                          A.IS_SSGS,
                          A.FRDB,
                          A.EMPLOYEE_NUM,
                          A.IS_NB,
                          A.STATUS,
                          A.CREATE_USER,
                          A.CREATE_USER_NAME,
                          A.CREATE_DATE,
                          A.UPDATE_USER,
                          A.UPDATE_USER_NAME,
                          A.UPDATE_DATE,
                          A.COMPANY_CODE,
                          A.COMPANY_NAME,
                          A.NODE_CODE,
                          A.NODE_NAME,
                          A.GROUP_CODE,
                          A.GROUP_NAME,
                          A.MEMO_KH,
                          A.ROLE_CODE,
                          A.ROLE_NAME,
                          A.COMPANY_TYPE,
                          C.HZFW_CODE_KH,
                          C.HZFW_NAME_KH,
                          A.KHLY,
                          A.YWHBJB,
                          A.SALE_USER,
                          C.SK_DATELINE,
                          D.PAY_DATELINE,
                          A.CREDIT_LIMIT,
                          A.ACCOUNT_MODE,
                          A.PAY_MODE,
                          A.JD_FREEZE,
                          C.KP_CYCLE,
                          C.KP_DATELINE,
                          A.ACCOUNT_KH,
                          A.BANK_KH,
                          A.TAX_NO_KH,
                          A.TAX_VAL_KH,
                          A.INV_TYPE_KH,
                          A.JK_CYCLE,
                          A.PAY_FREEZE,
                          A.ACCOUNT_FP,
                          A.BANK_FP,
                          A.TAX_NO_FP,
                          A.TAX_VAL_FP,
                          A.INV_TYPE_FP,
                          A.MEMO_FP,
                          A.HZFW_CODE_HZ,
                          A.HZFW_NAME_HZ,
                          A.TEL,
                          A.IS_USER_CONTROL,
                          A.CUSTOMER_B2B,
                          A.MEMO,
                          A.IS_CUSTOM,
                          A.CUSTOMER_GROUP_CODE,
                          A.CUSTOMER_GROUP_NAME,
                          A.COM_TYPE,
                          A.MAIL,
                          A.ACCOUNT_MEMO,
                          GYS.hzfwCodeGys,
                          GYS.hzfwNameGys,
                          A.MANAGE_STATUS
                      from crm_customer A
                   left join CRM_CUSTOMER_HEAD B ON A.CODE=B.CODE AND A.GROUP_CODE=B.GROUP_CODE
                   left join CRM_CUSTOMER_KH C ON A.CODE=C.CUSTOMER_CODE AND A.GROUP_CODE=C.GROUP_CODE AND C.COMPANY_CODE=A.COMPANY_CODE
                   left join CRM_CUSTOMER_GYS D ON A.CODE=D.CUSTOMER_CODE AND A.GROUP_CODE=D.GROUP_CODE AND D.COMPANY_CODE=A.COMPANY_CODE

                     left join (
                          SELECT
                              WM_CONCAT(DISTINCT X.FWLX_CODE) AS hzfwCodeGys,
                              WM_CONCAT(DISTINCT (SELECT NAME  FROM FZGJ_BD_SERVICE_TYPE WHERE CODE = X.FWLX_CODE)) AS hzfwNameGys,
                              X.CUSTOMER_CODE
                          FROM CRM_CUSTOMER_HZFW_GYS X
                          WHERE
                              X.GROUP_CODE= #{groupcode} AND X.COMPANY_CODE= #{companycode}
                          GROUP BY X.CUSTOMER_CODE ORDER BY X.CUSTOMER_CODE
                      ) GYS on GYS.CUSTOMER_CODE=A.CODE
                      ) A
            ${ew.customSqlSegment}


    </select>
</mapper>