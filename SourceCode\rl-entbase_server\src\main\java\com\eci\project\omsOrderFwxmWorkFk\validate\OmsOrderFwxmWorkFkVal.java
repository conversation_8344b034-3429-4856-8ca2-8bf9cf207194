package com.eci.project.omsOrderFwxmWorkFk.validate;

import com.eci.common.DataExtend;
import com.eci.common.db.DBHelper;
import com.eci.common.enums.Enums;
import com.eci.common.enums.OrderEnum;
import com.eci.common.util.StringUtils;
import com.eci.common.utils.CommonLib;
import com.eci.exception.BaseException;
import com.eci.log.enums.BusinessType;
import com.eci.project.fzgjBdServiceItem.dao.FzgjBdServiceItemDao;
import com.eci.project.fzgjBdServiceItem.entity.FzgjBdServiceItemEntity;
import com.eci.project.omsOrderFwxmWork.dao.OmsOrderFwxmWorkDao;
import com.eci.project.omsOrderFwxmWork.entity.OmsOrderFwxmWorkEntity;
import com.eci.project.omsOrderFwxmWorkFk.entity.OmsOrderFwxmWorkFkEntity;
import com.eci.project.omsOrderFwxmWorkFkHzqd.entity.OmsOrderFwxmWorkFkHzqdEntity;
import com.eci.sso.role.entity.UserContext;
import com.eci.wu.core.EntityBase;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;


/**
 * 反馈内容表头Val接口
 * 验证接口
 *
 * @<NAME_EMAIL>
 * @date 2025-05-21
 */
@Service
public class OmsOrderFwxmWorkFkVal {

    CommonLib cmn = CommonLib.getInstance();

    @Autowired
    private OmsOrderFwxmWorkDao omsOrderFwxmWorkDao;

    @Autowired
    private FzgjBdServiceItemDao fzgjBdServiceItemDao;

    /**
     * 删除验证
     *
     * @param entity
     */
    public void deleteValidate(OmsOrderFwxmWorkFkEntity entity) {

    }

    /**
     * 保存验证
     *
     * @param entity
     */
    public void saveValidate(OmsOrderFwxmWorkFkEntity entity, BusinessType businessType) {

    }

    /// <summary>
    /// 反馈验证
    /// </summary>
    /// <param name="context"></param>
    /// <param name="workNo">协作任务单号</param>
    /// <param name="poDocStatus">OrderEnum.DocStatus</param>
    public void feedBackValidate(String workNo, String poDocStatus) {

        if (StringUtils.isEmpty(workNo)) {
            throw new BaseException("协作任务单号为空");
        }

        List<OmsOrderFwxmWorkEntity> list = omsOrderFwxmWorkDao.select().eq(OmsOrderFwxmWorkEntity::getWorkNo, workNo).list();
        if (list.isEmpty()) {
            throw new BaseException("未查询到协作任务【" + workNo + "】");
        }

        OmsOrderFwxmWorkEntity work = list.get(0);
        boolean blIsSave = getFZGJ_BD_SERVICE_ITEM_IS_SAVE(work);

        if (StringUtils.hasValue(poDocStatus)) {

            OrderEnum.DocStatus fwxmCodeEnum = OrderEnum.DocStatus.fromCode(poDocStatus);

            switch (fwxmCodeEnum) {
                case OP_COMPLETE_OK://作业完成：协作接单且未执行本操作
                    if (!work.getStatus().equals(OrderEnum.WorkStatus.XZJD)) {
                        throw new BaseException("协作任务非【协作接单】状态，不允许此操作");
                    }
                    if (work.getOpCompleteOk().equals(Enums.YNStatus.Y.getCode())) {
                        throw new BaseException("协作任务已作业完成，不允许此操作");
                    }
                    break;

                case DATA_OK: //作业数据齐全：作业完成且未执行本操作
                    if (!work.getOpCompleteOk().equals(Enums.YNStatus.Y.getCode())) {
                        throw new BaseException("协作任务未作业完成，不允许此操作");
                    }

                    if (work.getDataOk().equals(Enums.YNStatus.Y.getCode()) && !blIsSave) {
                        throw new BaseException("协作任务已作业数据齐全，不允许此操作");
                    }
                    break;

                default:// 新增
                    if (!work.getStatus().equals(OrderEnum.WorkStatus.XZJD)) {
                        throw new BaseException("协作任务非【协作接单】状态，不允许此操作");
                    }
                    if (work.getDataOk().equals(Enums.YNStatus.Y.getCode()) && !blIsSave) {
                        throw new BaseException("协作任务已作业数据齐全，不允许此操作");
                    }
                    break;
            }
        }
    }


    /// <summary>
    /// 查询 辅助工具设置， 是否 作业完成也可以修改
    /// </summary>
    public boolean getFZGJ_BD_SERVICE_ITEM_IS_SAVE(OmsOrderFwxmWorkEntity work) {

        List<FzgjBdServiceItemEntity> list = fzgjBdServiceItemDao.select().eq(FzgjBdServiceItemEntity::getCode, work.getFwxmCode())
                .eq(FzgjBdServiceItemEntity::getGroupCode, work.getGroupCode())
                .list();
        if (list.size() <= 0) {
            return false;
        } else {
            return DataExtend.toBool(list.get(0).getIsSave());
        }

    }


    public void validateOrderOnly(String fileName, List<OmsOrderFwxmWorkFkHzqdEntity> dtDataHZQD) {
        String companyCode = UserContext.getUserInfo().getCompanyCode();

        String fileNameCN = "";
        String ysfs = "";

        switch (fileName) {
            case "DEC_NO":
                fileNameCN = "报关单号";
                ysfs = "BGD";
                break;
            case "CHECKBILL_NO":
                fileNameCN = "核注清单号";
                ysfs = "HZQD";
                break;
            case "CRKDH":
                fileNameCN = "出入库单号";
                ysfs = "CRKD";
                break;
            default:
                return;
        }

        for (OmsOrderFwxmWorkFkHzqdEntity entity : dtDataHZQD) {
            String tempData = "";
            if (fileName.equals("CRKDH")) {
                tempData = entity.getCrkdh();
            }
            if(fileName.equals("CHECKBILL_NO")){
                tempData =entity.getCheckbillNo();
            }
            if(fileName.equals("DEC_NO")){
                tempData =entity.getDecNo();
            }

            String fileData = tempData;
            // 1. 检查是否有重复的数据
            if (fileName.equals("CRKDH")) {
                long crkdCount = dtDataHZQD.stream().filter(s -> s.getCrkdh().equals(fileData))
                        .map(OmsOrderFwxmWorkFkHzqdEntity::getCrkdh)
                        .count();
                if (crkdCount > 1) {
                    throw new BaseException("保存数据有重复的" + fileNameCN + ":" + fileData);
                }
            }

            if (fileName.equals("CHECKBILL_NO")) {
                long checkCount = dtDataHZQD.stream().filter(s -> s.getCheckbillNo().equals(fileData))
                        .map(OmsOrderFwxmWorkFkHzqdEntity::getCheckbillNo)
                        .count();
                if (checkCount > 1) {
                    throw new BaseException("保存数据有重复的" + fileNameCN + ":" + fileData);
                }
            }

            if (fileName.equals("DEC_NO")) {
                long decCount = dtDataHZQD.stream().filter(s -> s.getDecNo().equals(fileData))
                        .map(OmsOrderFwxmWorkFkHzqdEntity::getDecNo)
                        .count();
                if (decCount > 1) {
                    throw new BaseException("保存数据有重复的" + fileNameCN + ":" + fileData);
                }
            }

            // 2. 查询数据中是否已存在
            String sql = String.format(
                    "SELECT * FROM OMS_ORDER_FWXM_WORK_FK_HZQD A " +
                            "WHERE A.COMPANY_CODE = %s AND %s = %s " +
                            "AND EXISTS ( " +
                            "SELECT 1 FROM OMS_ORDER_FWXM_WORK_FK_HZQD H " +
                            "WHERE H.%s = %s AND H.COMPANY_CODE = %s AND A.GUID <> H.GUID " +
                            ") " +
                            "AND EXISTS ( " +
                            "SELECT 1 FROM FZGJ_BD_SERVICE_ITEM_PT P " +
                            "WHERE P.CODE = A.FWXM_CODE AND P.YSFS = %s " +
                            ")",
                    cmn.SQLQ(companyCode),
                    fileName,
                    cmn.SQLQ(fileData),
                    fileName,
                    cmn.SQLQ(fileData),
                    cmn.SQLQ(companyCode),
                    cmn.SQLQ(ysfs)
            );

            List<EntityBase> isHasData = DBHelper.selectList(sql, EntityBase.class);
            if (isHasData.size() > 0) {
                throw new BaseException("归属公司:" + companyCode + ",已经存在" + fileNameCN + ":" + fileData);
            }
        }
    }
}
