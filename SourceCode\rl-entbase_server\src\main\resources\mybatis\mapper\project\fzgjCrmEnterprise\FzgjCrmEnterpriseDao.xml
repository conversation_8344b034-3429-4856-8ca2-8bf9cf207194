<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.fzgjCrmEnterprise.dao.FzgjCrmEnterpriseDao">
    <resultMap type="FzgjCrmEnterpriseEntity" id="FzgjCrmEnterpriseResult">
        <result property="guid" column="GUID"/>
        <result property="code" column="CODE"/>
        <result property="name" column="NAME"/>
        <result property="shortName" column="SHORT_NAME"/>
        <result property="enName" column="EN_NAME"/>
        <result property="otherName" column="OTHER_NAME"/>
        <result property="customNo" column="CUSTOM_NO"/>
        <result property="address" column="ADDRESS"/>
        <result property="addressEn" column="ADDRESS_EN"/>
        <result property="person" column="PERSON"/>
        <result property="tel1" column="TEL1"/>
        <result property="tel2" column="TEL2"/>
        <result property="fax" column="FAX"/>
        <result property="email" column="EMAIL"/>
        <result property="zip" column="ZIP"/>
        <result property="country" column="COUNTRY"/>
        <result property="state" column="STATE"/>
        <result property="city" column="CITY"/>
        <result property="companyType" column="COMPANY_TYPE"/>
        <result property="companyTypeName" column="COMPANY_TYPE_NAME"/>
        <result property="customerB2b" column="CUSTOMER_B2B"/>
        <result property="tax" column="TAX"/>
        <result property="logo" column="LOGO"/>
        <result property="appname" column="APPNAME"/>
        <result property="endUserDate" column="END_USER_DATE"/>
        <result property="status" column="STATUS"/>
        <result property="memo" column="MEMO"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="startno" column="STARTNO"/>
        <result property="autoAddToApar" column="AUTO_ADD_TO_APAR"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="taxRate" column="TAX_RATE"/>
        <result property="invType" column="INV_TYPE"/>
        <result property="sysUrl" column="SYS_URL"/>
        <result property="clientCompanyCode" column="CLIENT_COMPANY_CODE"/>
        <result property="ssoCompanyGuid" column="SSO_COMPANY_GUID"/>
        <result property="logoCz" column="LOGO_CZ"/>
        <result property="groudPic" column="GROUD_PIC"/>
        <result property="lsGroupCode" column="LS_GROUP_CODE"/>
        <result property="lsGroupName" column="LS_GROUP_NAME"/>
        <result property="isGroup" column="IS_GROUP"/>
        <result property="tyCode" column="TY_CODE"/>
        <result property="smtpEmail" column="SMTP_EMAIL"/>
        <result property="smtpEmailUser" column="SMTP_EMAIL_USER"/>
        <result property="smtpEmailPwd" column="SMTP_EMAIL_PWD"/>
        <result property="smtpEmailHost" column="SMTP_EMAIL_HOST"/>
        <result property="smtpEmailPort" column="SMTP_EMAIL_PORT"/>
        <result property="whNum" column="WH_NUM"/>
        <result property="fcompanyCode" column="FCOMPANY_CODE"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
    </resultMap>

    <sql id="selectFzgjCrmEnterpriseEntityVo">
        select
            GUID,
            CODE,
            NAME,
            SHORT_NAME,
            EN_NAME,
            OTHER_NAME,
            CUSTOM_NO,
            ADDRESS,
            ADDRESS_EN,
            PERSON,
            TEL1,
            TEL2,
            FAX,
            EMAIL,
            ZIP,
            COUNTRY,
            STATE,
            CITY,
            COMPANY_TYPE,
            COMPANY_TYPE_NAME,
            CUSTOMER_B2B,
            TAX,
            LOGO,
            APPNAME,
            END_USER_DATE,
            STATUS,
            MEMO,
            CREATE_USER,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_DATE,
            STARTNO,
            AUTO_ADD_TO_APAR,
            CREATE_USER_NAME,
            UPDATE_USER_NAME,
            GROUP_CODE,
            GROUP_NAME,
            TAX_RATE,
            INV_TYPE,
            SYS_URL,
            CLIENT_COMPANY_CODE,
            SSO_COMPANY_GUID,
            LOGO_CZ,
            GROUD_PIC,
            LS_GROUP_CODE,
            LS_GROUP_NAME,
            IS_GROUP,
            TY_CODE,
            SMTP_EMAIL,
            SMTP_EMAIL_USER,
            SMTP_EMAIL_PWD,
            SMTP_EMAIL_HOST,
            SMTP_EMAIL_PORT,
            WH_NUM,
            FCOMPANY_CODE,
            NODE_CODE,
            NODE_NAME
        from FZGJ_CRM_ENTERPRISE
    </sql>

    <select  id="selectRightTable" parameterType="com.baomidou.mybatisplus.core.conditions.Wrapper"
         resultType="com.eci.project.fzgjCrmEnterprise.entity.FzgjCrmEnterpriseEntityDto">
        SELECT A.GUID,
               A.SYS_CODE AS CODE,
               (SELECT C.NAME
                FROM FZGJ_BASE_DATA_DETAIL C
                         LEFT JOIN FZGJ_BASE_DATA B
                                   ON C.GROUP_CODE = B.GROUP_CODE
                WHERE B.STATUS = 'Y'
                  AND C.GROUP_CODE = 'SYS'
                  AND C.CODE = A.SYS_CODE) AS NAME
        FROM FZGJ_CRM_ENTERPRISE_SYS A
            ${ew.customSqlSegment}
    </select>

</mapper>