package com.eci.project.fzgjBdDistrict.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.eci.common.DictField;
import com.eci.common.ZsrBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;

import java.util.Date;


/**
 * 区县对象 FZGJ_BD_DISTRICT
 *
 * @<NAME_EMAIL>
 * @date 2025-03-17
 */
@FieldNameConstants
public class FzgjBdDistrictBaseEntity extends ZsrBaseEntity {
    /**
     * GUID
     */
    @ApiModelProperty("GUID(50)")
    @TableId("GUID")
    private String guid;

    /**
     * 名称
     */
    @ApiModelProperty("名称(50)")
    @TableField("NAME")
    private String name;

    /**
     * 城市代码
     */
    @ApiModelProperty("城市代码(50)")
    @TableField("CITY_ID")
    @DictField(queryKey = "BASE_CITY")
    private String cityId;

    /**
     * 备注
     */
    @ApiModelProperty("备注(50)")
    @TableField("MEMO")
    private String memo;

    /**
     * 状态
     */
    @ApiModelProperty("状态(1)")
    @TableField("STATUS")
    private String status;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
     * 创建日期
     */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist = false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist = false)
    private Date createDateEnd;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人(50)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("修改时间开始")
    @TableField(exist = false)
    private Date updateDateStart;

    @ApiModelProperty("修改时间结束")
    @TableField(exist = false)
    private Date updateDateEnd;

    /**
     * 顺序
     */
    @ApiModelProperty("顺序(22)")
    @TableField("SEQ")
    private Integer seq;

    /**
     * CREATE_USER
     */
    @ApiModelProperty("CREATE_USER(200)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
     * UPDATE_USER
     */
    @ApiModelProperty("UPDATE_USER(200)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
     * 英文名称，仅做存储
     */
    @ApiModelProperty("英文名称，仅做存储(200)")
    @TableField("EN_NAME")
    private String enName;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public FzgjBdDistrictBaseEntity setGuid(String guid) {
        this.guid = guid;
        return this;
    }

    public String getGuid() {
        return guid;
    }

    public FzgjBdDistrictBaseEntity setName(String name) {
        this.name = name;
        return this;
    }

    public String getName() {
        return name;
    }

    public FzgjBdDistrictBaseEntity setCityId(String cityId) {
        this.cityId = cityId;
        return this;
    }

    public String getCityId() {
        return cityId;
    }

    public FzgjBdDistrictBaseEntity setMemo(String memo) {
        this.memo = memo;
        return this;
    }

    public String getMemo() {
        return memo;
    }

    public FzgjBdDistrictBaseEntity setStatus(String status) {
        this.status = status;
        return this;
    }

    public String getStatus() {
        return status;
    }

    public FzgjBdDistrictBaseEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        return this;
    }

    public String getCreateUser() {
        return createUser;
    }

    public FzgjBdDistrictBaseEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        return this;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public FzgjBdDistrictBaseEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        return this;
    }

    public Date getCreateDateStart() {
        return createDateStart;
    }

    public FzgjBdDistrictBaseEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        return this;
    }

    public Date getCreateDateEnd() {
        return createDateEnd;
    }

    public FzgjBdDistrictBaseEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        return this;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public FzgjBdDistrictBaseEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        return this;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public FzgjBdDistrictBaseEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        return this;
    }

    public Date getUpdateDateStart() {
        return updateDateStart;
    }

    public FzgjBdDistrictBaseEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        return this;
    }

    public Date getUpdateDateEnd() {
        return updateDateEnd;
    }

    public FzgjBdDistrictBaseEntity setSeq(Integer seq) {
        this.seq = seq;
        return this;
    }

    public Integer getSeq() {
        return seq;
    }

    public FzgjBdDistrictBaseEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        return this;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public FzgjBdDistrictBaseEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        return this;
    }

    public String getUpdateUserName() {
        return updateUserName;
    }

    public FzgjBdDistrictBaseEntity setEnName(String enName) {
        this.enName = enName;
        return this;
    }

    public String getEnName() {
        return enName;
    }

}
