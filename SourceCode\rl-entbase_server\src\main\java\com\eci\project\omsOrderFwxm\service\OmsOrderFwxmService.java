package com.eci.project.omsOrderFwxm.service;

import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.omsOrderFwxm.dao.OmsOrderFwxmDao;
import com.eci.project.omsOrderFwxm.entity.OmsOrderFwxmEntity;
import com.eci.project.omsOrderFwxm.validate.OmsOrderFwxmVal;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;


/**
* 订单服务项目Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-14
*/
@Service
@Slf4j
public class OmsOrderFwxmService implements EciBaseService<OmsOrderFwxmEntity> {

    @Autowired
    private OmsOrderFwxmDao omsOrderFwxmDao;

    @Autowired
    private OmsOrderFwxmVal omsOrderFwxmVal;


    @Override
    public TgPageInfo queryPageList(OmsOrderFwxmEntity entity) {
        EciQuery<OmsOrderFwxmEntity> eciQuery = EciQuery.buildQuery(entity);
        List<OmsOrderFwxmEntity> entities = omsOrderFwxmDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public OmsOrderFwxmEntity save(OmsOrderFwxmEntity entity) {
        // 返回实体对象
        OmsOrderFwxmEntity omsOrderFwxmEntity = null;
        omsOrderFwxmVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            omsOrderFwxmEntity = omsOrderFwxmDao.insertOne(entity);

        }else{

            omsOrderFwxmEntity = omsOrderFwxmDao.updateByEntityId(entity);

        }
        return omsOrderFwxmEntity;
    }

    @Override
    public List<OmsOrderFwxmEntity> selectList(OmsOrderFwxmEntity entity) {
        return omsOrderFwxmDao.selectList(entity);
    }

    @Override
    public OmsOrderFwxmEntity selectOneById(Serializable id) {
        return omsOrderFwxmDao.selectById(id);
    }


    @Override
    public void insertBatch(List<OmsOrderFwxmEntity> list) {
        omsOrderFwxmDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return omsOrderFwxmDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return omsOrderFwxmDao.deleteById(id);
    }

}