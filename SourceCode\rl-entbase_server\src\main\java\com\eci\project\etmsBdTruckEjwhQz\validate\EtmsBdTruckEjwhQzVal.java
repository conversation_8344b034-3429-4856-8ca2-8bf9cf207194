package com.eci.project.etmsBdTruckEjwhQz.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.etmsBdTruckEjwhQz.entity.EtmsBdTruckEjwhQzEntity;

import org.springframework.stereotype.Service;


/**
* 车辆二级维护历史Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-16
*/
@Service
public class EtmsBdTruckEjwhQzVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(EtmsBdTruckEjwhQzEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(EtmsBdTruckEjwhQzEntity entity, BusinessType businessType) {

    }

}
