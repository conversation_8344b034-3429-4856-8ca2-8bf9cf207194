package com.eci.project.crmCustomer.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.codeConvert.EciCode;
import com.eci.crud.entity.EciBaseEntity;
import com.eci.project.etmsBdDriver.entity.EtmsBdDriverEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 业务伙伴对象 CRM_CUSTOMER
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-04-29
*/
@ApiModel("业务伙伴")
@TableName("CRM_CUSTOMER")
@FieldNameConstants
public class CrmCustomerEntity extends EciBaseEntity{
    /**
    * 编号
    */
    @ApiModelProperty("编号(36)")
    @TableId("GUID")
    private String guid;

    /**
    * 企业代码
    */
    @ApiModelProperty("企业代码(36)")
    @TableField("CODE")
    private String code;

    @ApiModelProperty("管理状态(3)")
    @TableField("MANAGE_STATUS")
    @EciCode("Driver_Manage_Status")
    private String manageStatus;
    @ApiModelProperty("审批备注(3)")
    @TableField("AUDIT_REMARK")
    private String auditRemark;
    /**
    * 企业全称
    */
    @ApiModelProperty("企业全称(100)")
    @TableField("NAME")
    private String name;

    /**
    * 企业简称
    */
    @ApiModelProperty("企业简称(50)")
    @TableField("SHORT_NAME")
    private String shortName;

    /**
    * 企业英文名称
    */
    @ApiModelProperty("企业英文名称(100)")
    @TableField("EN_NAME")
    private String enName;

    /**
    * 所属国家
    */
    @ApiModelProperty("所属国家(50)")
    @TableField("COUNTRY")
    @EciCode("BASE_COUNTRY")
    private String country;

    /**
    * 所属省份
    */
    @ApiModelProperty("所属省份(50)")
    @TableField("PROVINCE")
    @EciCode("BASE_PROVINCE")
    private String province;

    /**
    * 所属城市
    */
    @ApiModelProperty("所属城市(50)")
    @TableField("CITY")
    @EciCode("BASE_CITY")
    private String city;

    /**
    * 所属区/县
    */
    @ApiModelProperty("所属区/县(50)")
    @TableField("DISTRICT")
    @EciCode("BASE_BD")
    private String district;

    /**
    * 企业地址
    */
    @ApiModelProperty("企业地址(1,000)")
    @TableField("ADDRESS")
    private String address;

    /**
    * 企业英文地址
    */
    @ApiModelProperty("企业英文地址(510)")
    @TableField("ADDRESS_EN")
    private String addressEn;

    /**
    * 统一社会信用代码
    */
    @ApiModelProperty("统一社会信用代码(50)")
    @TableField("TY_CODE")
    private String tyCode;

    /**
    * 联系人
    */
    @ApiModelProperty("联系人(20)")
    @TableField("PERSON")
    private String person;

    /**
    * 成立日期
    */
    @ApiModelProperty("成立日期(7)")
    @TableField("CL_DATE")
    private Date clDate;

    @ApiModelProperty("成立日期开始")
    @TableField(exist=false)
    private Date clDateStart;

    @ApiModelProperty("成立日期结束")
    @TableField(exist=false)
    private Date clDateEnd;

    /**
    * 注册币制
    */
    @ApiModelProperty("注册币制(20)")
    @TableField("ZC_CURR")
    private String zcCurr;

    /**
    * 注册资本
    */
    @ApiModelProperty("注册资本(50)")
    @TableField("ZC_CAPITAL")
    private String zcCapital;

    /**
    * 股票代码
    */
    @ApiModelProperty("股票代码(50)")
    @TableField("STOCK_CODE")
    private String stockCode;

    /**
    * 企业海关代码
    */
    @ApiModelProperty("企业海关代码(20)")
    @TableField("CUSTOM_NO")
    private String customNo;

    /**
    * 是否上市公司
    */
    @ApiModelProperty("是否上市公司(1)")
    @TableField("IS_SSGS")
    @EciCode("YNKey")
    private String isSsgs;

    /**
    * 法人代表
    */
    @ApiModelProperty("法人代表(50)")
    @TableField("FRDB")
    private String frdb;

    /**
    * 员工人数
    */
    @ApiModelProperty("员工人数(22)")
    @TableField("EMPLOYEE_NUM")
    private Integer employeeNum;

    /**
    * 是否内部公司
    */
    @ApiModelProperty("是否内部公司(1)")
    @TableField("IS_NB")
    @EciCode("YNKey")
    private String isNb;

    /**
    * 是否启用
    */
    @ApiModelProperty("是否启用(1)")
    @TableField("STATUS")
    @EciCode("YNKey")
    private String status;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(20)")
    @TableField("CREATE_USER")
    private String createUser;

    /**
    * 创建人
    */
    @ApiModelProperty("创建人(50)")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    /**
    * 创建日期
    */
    @ApiModelProperty("创建日期(7)")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty("创建日期开始")
    @TableField(exist=false)
    private Date createDateStart;

    @ApiModelProperty("创建日期结束")
    @TableField(exist=false)
    private Date createDateEnd;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(20)")
    @TableField("UPDATE_USER")
    private String updateUser;

    /**
    * 最后修改人
    */
    @ApiModelProperty("最后修改人(50)")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    /**
    * 最后修改日期
    */
    @ApiModelProperty("最后修改日期(7)")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty("最后修改日期开始")
    @TableField(exist=false)
    private Date updateDateStart;

    @ApiModelProperty("最后修改日期结束")
    @TableField(exist=false)
    private Date updateDateEnd;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(200)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 备注-客户开票信息
    */
    @ApiModelProperty("备注-客户开票信息(500)")
    @TableField("MEMO_KH")
    private String memoKh;

    /**
    * 角色代码，多个逗号分隔
    */
    @ApiModelProperty("角色代码，多个逗号分隔(500)")
    @TableField("ROLE_CODE")
    private String roleCode;

    /**
    * 角色名称，多个逗号分隔
    */
    @ApiModelProperty("角色名称，多个逗号分隔(500)")
    @TableField("ROLE_NAME")
    private String roleName;

    /**
    * 公司性质
    */
    @ApiModelProperty("公司性质(20)")
    @TableField("COMPANY_TYPE")
    private String companyType;

    /**
    * 客户合作服务代码，多个逗号分隔
    */
    @ApiModelProperty("客户合作服务代码，多个逗号分隔(500)")
    @TableField("HZFW_CODE_KH")
    private String hzfwCodeKh;

    /**
    * 客户合作服务名称，多个逗号分隔
    */
    @ApiModelProperty("客户合作服务名称，多个逗号分隔(500)")
    @TableField("HZFW_NAME_KH")
    private String hzfwNameKh;

    @ApiModelProperty("供应商合作服务代码，多个逗号分隔(500)")
    @TableField(exist = false)
    private String hzfwCodeGys;

    /**
     * 客户合作服务名称，多个逗号分隔
     */
    @ApiModelProperty("供应商合作服务名称，多个逗号分隔(500)")
    @TableField(exist = false)
    private String hzfwNameGys;

    /**
    * 客户来源
    */
    @ApiModelProperty("客户来源(20)")
    @TableField("KHLY")
    private String khly;

    /**
    * 客户级别
    */
    @ApiModelProperty("客户级别(20)")
    @TableField("YWHBJB")
    private String ywhbjb;

    /**
    * 销售人员
    */
    @ApiModelProperty("销售人员(20)")
    @TableField("SALE_USER")
    private String saleUser;

    /**
    * 收款期限(天)
    */
    @ApiModelProperty("收款期限(天)(22)")
    @TableField("SK_DATELINE")
    private Integer skDateline;

    /**
    * 付款期限(天)
    */
    @ApiModelProperty("付款期限(天)(22)")
    @TableField("PAY_DATELINE")
    private Integer payDateline;

    /**
    * 信用额度
    */
    @ApiModelProperty("信用额度(22)")
    @TableField("CREDIT_LIMIT")
    private Integer creditLimit;

    /**
    * 默认结算方式
    */
    @ApiModelProperty("默认结算方式(20)")
    @TableField("ACCOUNT_MODE")
    private String accountMode;

    /**
    * 默认付款方式
    */
    @ApiModelProperty("默认付款方式(20)")
    @TableField("PAY_MODE")
    private String payMode;

    /**
    * 接单冻结标志(Y/N)
    */
    @ApiModelProperty("接单冻结标志(Y/N)(1)")
    @TableField("JD_FREEZE")
    @EciCode("YNKey")
    private String jdFreeze;

    /**
    * 开票周期(天)
    */
    @ApiModelProperty("开票周期(天)(22)")
    @TableField("KP_CYCLE")
    private Integer kpCycle;

    /**
    * 开票期限(天)
    */
    @ApiModelProperty("开票期限(天)(22)")
    @TableField("KP_DATELINE")
    private Integer kpDateline;

    /**
    * 开户行账号-客户开票信息
    */
    @ApiModelProperty("开户行账号-客户开票信息(50)")
    @TableField("ACCOUNT_KH")
    private String accountKh;

    /**
    * 开户行名称-客户开票信息
    */
    @ApiModelProperty("开户行名称-客户开票信息(80)")
    @TableField("BANK_KH")
    private String bankKh;

    /**
    * 税号-客户开票信息
    */
    @ApiModelProperty("税号-客户开票信息(30)")
    @TableField("TAX_NO_KH")
    private String taxNoKh;

    /**
    * 默认开票税率-客户开票信息
    */
    @ApiModelProperty("默认开票税率-客户开票信息(22)")
    @TableField("TAX_VAL_KH")
    private Integer taxValKh;

    /**
    * 默认开票发票类型-客户开票信息
    */
    @ApiModelProperty("默认开票发票类型-客户开票信息(20)")
    @TableField("INV_TYPE_KH")
    private String invTypeKh;

    /**
    * 结款周期(天)
    */
    @ApiModelProperty("结款周期(天)(22)")
    @TableField("JK_CYCLE")
    private Integer jkCycle;

    /**
    * 付款冻结标志(Y/N)
    */
    @ApiModelProperty("付款冻结标志(Y/N)(1)")
    @TableField("PAY_FREEZE")
    private String payFreeze;

    /**
    * 开户行账号-发票抬头开票信息
    */
    @ApiModelProperty("开户行账号-发票抬头开票信息(50)")
    @TableField("ACCOUNT_FP")
    private String accountFp;

    /**
    * 开户行名称-发票抬头开票信息
    */
    @ApiModelProperty("开户行名称-发票抬头开票信息(80)")
    @TableField("BANK_FP")
    private String bankFp;

    /**
    * 税号-发票抬头开票信息
    */
    @ApiModelProperty("税号-发票抬头开票信息(30)")
    @TableField("TAX_NO_FP")
    private String taxNoFp;

    /**
    * 默认开票税率-发票抬头开票信息
    */
    @ApiModelProperty("默认开票税率-发票抬头开票信息(22)")
    @TableField("TAX_VAL_FP")
    private Integer taxValFp;

    /**
    * 默认开票发票类型-发票抬头开票信息
    */
    @ApiModelProperty("默认开票发票类型-发票抬头开票信息(20)")
    @TableField("INV_TYPE_FP")
    private String invTypeFp;

    /**
    * 备注-发票抬头开票信息
    */
    @ApiModelProperty("备注-发票抬头开票信息(500)")
    @TableField("MEMO_FP")
    private String memoFp;

    /**
    * 货主合作服务代码，多个逗号分隔
    */
    @ApiModelProperty("货主合作服务代码，多个逗号分隔(500)")
    @TableField("HZFW_CODE_HZ")
    private String hzfwCodeHz;

    /**
    * 货主合作服务名称，多个逗号分隔
    */
    @ApiModelProperty("货主合作服务名称，多个逗号分隔(500)")
    @TableField("HZFW_NAME_HZ")
    private String hzfwNameHz;

    /**
    * 企业电话
    */
    @ApiModelProperty("企业电话(50)")
    @TableField("TEL")
    private String tel;

    /**
    * 用户授权控制
    */
    @ApiModelProperty("用户授权控制(1)")
    @TableField("IS_USER_CONTROL")
    @EciCode("YNKey")
    private String isUserControl;

    /**
    * 企业B2B唯一码
    */
    @ApiModelProperty("企业B2B唯一码(50)")
    @TableField("CUSTOMER_B2B")
    private String customerB2b;

    /**
    * 备注
    */
    @ApiModelProperty("备注(500)")
    @TableField("MEMO")
    private String memo;

    /**
    * 企业区域性质
    */
    @ApiModelProperty("企业区域性质(20)")
    @TableField("IS_CUSTOM")
    private String isCustom;
    @ApiModelProperty("企业区域性质(20)")
    @TableField(exist = false)
    private String isCustomName;


    /**
    * 业务伙伴集团代码
    */
    @ApiModelProperty("业务伙伴集团代码(40)")
    @TableField("CUSTOMER_GROUP_CODE")
    private String customerGroupCode;

    /**
    * 业务伙伴集团名称
    */
    @ApiModelProperty("业务伙伴集团名称(200)")
    @TableField("CUSTOMER_GROUP_NAME")
    private String customerGroupName;

    /**
    * 企业类型
    */
    @ApiModelProperty("企业类型(4,000)")
    @TableField("COM_TYPE")
    private String comType;

    @ApiModelProperty("企业类型(4,000)")
    @TableField("COM_TYPE_NAME")
    private String comTypeName;
    @ApiModelProperty("临单客户(4,000)")
    @TableField("LDKH")
    private String ldkh;

    /**
    * 企业邮箱
    */
    @ApiModelProperty("企业邮箱(200)")
    @TableField("MAIL")
    private String mail;

    /**
    * 结算备注
    */
    @ApiModelProperty("结算备注(100)")
    @TableField("ACCOUNT_MEMO")
    private String accountMemo;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public CrmCustomerEntity() {
        this.setSubClazz(CrmCustomerEntity.class);
    }

    public CrmCustomerEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public CrmCustomerEntity setCode(String code) {
        this.code = code;
        this.nodifySetFiled("code", code);
        return this;
    }

    public String getCode() {
        this.nodifyGetFiled("code");
        return code;
    }

    public CrmCustomerEntity setName(String name) {
        this.name = name;
        this.nodifySetFiled("name", name);
        return this;
    }

    public String getName() {
        this.nodifyGetFiled("name");
        return name;
    }

    public CrmCustomerEntity setShortName(String shortName) {
        this.shortName = shortName;
        this.nodifySetFiled("shortName", shortName);
        return this;
    }

    public String getShortName() {
        this.nodifyGetFiled("shortName");
        return shortName;
    }

    public CrmCustomerEntity setEnName(String enName) {
        this.enName = enName;
        this.nodifySetFiled("enName", enName);
        return this;
    }

    public String getEnName() {
        this.nodifyGetFiled("enName");
        return enName;
    }

    public CrmCustomerEntity setCountry(String country) {
        this.country = country;
        this.nodifySetFiled("country", country);
        return this;
    }

    public String getCountry() {
        this.nodifyGetFiled("country");
        return country;
    }

    public CrmCustomerEntity setProvince(String province) {
        this.province = province;
        this.nodifySetFiled("province", province);
        return this;
    }

    public String getProvince() {
        this.nodifyGetFiled("province");
        return province;
    }

    public CrmCustomerEntity setCity(String city) {
        this.city = city;
        this.nodifySetFiled("city", city);
        return this;
    }

    public String getCity() {
        this.nodifyGetFiled("city");
        return city;
    }

    public CrmCustomerEntity setDistrict(String district) {
        this.district = district;
        this.nodifySetFiled("district", district);
        return this;
    }

    public String getDistrict() {
        this.nodifyGetFiled("district");
        return district;
    }

    public CrmCustomerEntity setAddress(String address) {
        this.address = address;
        this.nodifySetFiled("address", address);
        return this;
    }

    public String getAddress() {
        this.nodifyGetFiled("address");
        return address;
    }

    public CrmCustomerEntity setAddressEn(String addressEn) {
        this.addressEn = addressEn;
        this.nodifySetFiled("addressEn", addressEn);
        return this;
    }

    public String getAddressEn() {
        this.nodifyGetFiled("addressEn");
        return addressEn;
    }

    public CrmCustomerEntity setTyCode(String tyCode) {
        this.tyCode = tyCode;
        this.nodifySetFiled("tyCode", tyCode);
        return this;
    }

    public String getTyCode() {
        this.nodifyGetFiled("tyCode");
        return tyCode;
    }

    public CrmCustomerEntity setPerson(String person) {
        this.person = person;
        this.nodifySetFiled("person", person);
        return this;
    }

    public String getPerson() {
        this.nodifyGetFiled("person");
        return person;
    }

    public CrmCustomerEntity setClDate(Date clDate) {
        this.clDate = clDate;
        this.nodifySetFiled("clDate", clDate);
        return this;
    }

    public Date getClDate() {
        this.nodifyGetFiled("clDate");
        return clDate;
    }

    public CrmCustomerEntity setClDateStart(Date clDateStart) {
        this.clDateStart = clDateStart;
        this.nodifySetFiled("clDateStart", clDateStart);
        return this;
    }

    public Date getClDateStart() {
        this.nodifyGetFiled("clDateStart");
        return clDateStart;
    }

    public CrmCustomerEntity setClDateEnd(Date clDateEnd) {
        this.clDateEnd = clDateEnd;
        this.nodifySetFiled("clDateEnd", clDateEnd);
        return this;
    }

    public Date getClDateEnd() {
        this.nodifyGetFiled("clDateEnd");
        return clDateEnd;
    }
    public CrmCustomerEntity setZcCurr(String zcCurr) {
        this.zcCurr = zcCurr;
        this.nodifySetFiled("zcCurr", zcCurr);
        return this;
    }

    public String getZcCurr() {
        this.nodifyGetFiled("zcCurr");
        return zcCurr;
    }

    public CrmCustomerEntity setZcCapital(String zcCapital) {
        this.zcCapital = zcCapital;
        this.nodifySetFiled("zcCapital", zcCapital);
        return this;
    }

    public String getZcCapital() {
        this.nodifyGetFiled("zcCapital");
        return zcCapital;
    }

    public CrmCustomerEntity setStockCode(String stockCode) {
        this.stockCode = stockCode;
        this.nodifySetFiled("stockCode", stockCode);
        return this;
    }

    public String getStockCode() {
        this.nodifyGetFiled("stockCode");
        return stockCode;
    }

    public CrmCustomerEntity setCustomNo(String customNo) {
        this.customNo = customNo;
        this.nodifySetFiled("customNo", customNo);
        return this;
    }

    public String getCustomNo() {
        this.nodifyGetFiled("customNo");
        return customNo;
    }

    public CrmCustomerEntity setIsSsgs(String isSsgs) {
        this.isSsgs = isSsgs;
        this.nodifySetFiled("isSsgs", isSsgs);
        return this;
    }

    public String getIsSsgs() {
        this.nodifyGetFiled("isSsgs");
        return isSsgs;
    }

    public CrmCustomerEntity setFrdb(String frdb) {
        this.frdb = frdb;
        this.nodifySetFiled("frdb", frdb);
        return this;
    }

    public String getFrdb() {
        this.nodifyGetFiled("frdb");
        return frdb;
    }

    public CrmCustomerEntity setEmployeeNum(Integer employeeNum) {
        this.employeeNum = employeeNum;
        this.nodifySetFiled("employeeNum", employeeNum);
        return this;
    }

    public Integer getEmployeeNum() {
        this.nodifyGetFiled("employeeNum");
        return employeeNum;
    }

    public CrmCustomerEntity setIsNb(String isNb) {
        this.isNb = isNb;
        this.nodifySetFiled("isNb", isNb);
        return this;
    }

    public String getIsNb() {
        this.nodifyGetFiled("isNb");
        return isNb;
    }

    public CrmCustomerEntity setStatus(String status) {
        this.status = status;
        this.nodifySetFiled("status", status);
        return this;
    }

    public String getStatus() {
        this.nodifyGetFiled("status");
        return status;
    }

    public CrmCustomerEntity setLdkh(String ldkh) {
        this.ldkh = ldkh;
        this.nodifySetFiled("ldkh", ldkh);
        return this;
    }

    public String getLdkh() {
        this.nodifyGetFiled("ldkh");
        return ldkh;
    }

    public CrmCustomerEntity setCreateUser(String createUser) {
        this.createUser = createUser;
        this.nodifySetFiled("createUser", createUser);
        return this;
    }

    public String getCreateUser() {
        this.nodifyGetFiled("createUser");
        return createUser;
    }

    public CrmCustomerEntity setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        this.nodifySetFiled("createUserName", createUserName);
        return this;
    }

    public String getCreateUserName() {
        this.nodifyGetFiled("createUserName");
        return createUserName;
    }

    public CrmCustomerEntity setCreateDate(Date createDate) {
        this.createDate = createDate;
        this.nodifySetFiled("createDate", createDate);
        return this;
    }

    public Date getCreateDate() {
        this.nodifyGetFiled("createDate");
        return createDate;
    }

    public CrmCustomerEntity setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        this.nodifySetFiled("createDateStart", createDateStart);
        return this;
    }

    public Date getCreateDateStart() {
        this.nodifyGetFiled("createDateStart");
        return createDateStart;
    }

    public CrmCustomerEntity setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        this.nodifySetFiled("createDateEnd", createDateEnd);
        return this;
    }

    public Date getCreateDateEnd() {
        this.nodifyGetFiled("createDateEnd");
        return createDateEnd;
    }
    public CrmCustomerEntity setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
        this.nodifySetFiled("updateUser", updateUser);
        return this;
    }

    public String getUpdateUser() {
        this.nodifyGetFiled("updateUser");
        return updateUser;
    }

    public CrmCustomerEntity setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        this.nodifySetFiled("updateUserName", updateUserName);
        return this;
    }

    public String getUpdateUserName() {
        this.nodifyGetFiled("updateUserName");
        return updateUserName;
    }

    public CrmCustomerEntity setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        this.nodifySetFiled("updateDate", updateDate);
        return this;
    }

    public Date getUpdateDate() {
        this.nodifyGetFiled("updateDate");
        return updateDate;
    }

    public CrmCustomerEntity setUpdateDateStart(Date updateDateStart) {
        this.updateDateStart = updateDateStart;
        this.nodifySetFiled("updateDateStart", updateDateStart);
        return this;
    }

    public Date getUpdateDateStart() {
        this.nodifyGetFiled("updateDateStart");
        return updateDateStart;
    }

    public CrmCustomerEntity setUpdateDateEnd(Date updateDateEnd) {
        this.updateDateEnd = updateDateEnd;
        this.nodifySetFiled("updateDateEnd", updateDateEnd);
        return this;
    }

    public Date getUpdateDateEnd() {
        this.nodifyGetFiled("updateDateEnd");
        return updateDateEnd;
    }
    public CrmCustomerEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public CrmCustomerEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public CrmCustomerEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public CrmCustomerEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public CrmCustomerEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public CrmCustomerEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public CrmCustomerEntity setMemoKh(String memoKh) {
        this.memoKh = memoKh;
        this.nodifySetFiled("memoKh", memoKh);
        return this;
    }

    public String getMemoKh() {
        this.nodifyGetFiled("memoKh");
        return memoKh;
    }

    public CrmCustomerEntity setRoleCode(String roleCode) {
        this.roleCode = roleCode;
        this.nodifySetFiled("roleCode", roleCode);
        return this;
    }

    public String getRoleCode() {
        this.nodifyGetFiled("roleCode");
        return roleCode;
    }

    public CrmCustomerEntity setRoleName(String roleName) {
        this.roleName = roleName;
        this.nodifySetFiled("roleName", roleName);
        return this;
    }

    public String getRoleName() {
        this.nodifyGetFiled("roleName");
        return roleName;
    }

    public CrmCustomerEntity setCompanyType(String companyType) {
        this.companyType = companyType;
        this.nodifySetFiled("companyType", companyType);
        return this;
    }

    public String getCompanyType() {
        this.nodifyGetFiled("companyType");
        return companyType;
    }

    public CrmCustomerEntity setHzfwCodeKh(String hzfwCodeKh) {
        this.hzfwCodeKh = hzfwCodeKh;
        this.nodifySetFiled("hzfwCodeKh", hzfwCodeKh);
        return this;
    }

    public String getHzfwCodeKh() {
        this.nodifyGetFiled("hzfwCodeKh");
        return hzfwCodeKh;
    }

    public CrmCustomerEntity setHzfwNameKh(String hzfwNameKh) {
        this.hzfwNameKh = hzfwNameKh;
        this.nodifySetFiled("hzfwNameKh", hzfwNameKh);
        return this;
    }

    public String getHzfwNameKh() {
        this.nodifyGetFiled("hzfwNameKh");
        return hzfwNameKh;
    }

    public CrmCustomerEntity setKhly(String khly) {
        this.khly = khly;
        this.nodifySetFiled("khly", khly);
        return this;
    }

    public String getKhly() {
        this.nodifyGetFiled("khly");
        return khly;
    }

    public CrmCustomerEntity setYwhbjb(String ywhbjb) {
        this.ywhbjb = ywhbjb;
        this.nodifySetFiled("ywhbjb", ywhbjb);
        return this;
    }

    public String getYwhbjb() {
        this.nodifyGetFiled("ywhbjb");
        return ywhbjb;
    }

    public CrmCustomerEntity setSaleUser(String saleUser) {
        this.saleUser = saleUser;
        this.nodifySetFiled("saleUser", saleUser);
        return this;
    }

    public String getSaleUser() {
        this.nodifyGetFiled("saleUser");
        return saleUser;
    }

    public CrmCustomerEntity setSkDateline(Integer skDateline) {
        this.skDateline = skDateline;
        this.nodifySetFiled("skDateline", skDateline);
        return this;
    }

    public Integer getSkDateline() {
        this.nodifyGetFiled("skDateline");
        return skDateline;
    }

    public CrmCustomerEntity setPayDateline(Integer payDateline) {
        this.payDateline = payDateline;
        this.nodifySetFiled("payDateline", payDateline);
        return this;
    }

    public Integer getPayDateline() {
        this.nodifyGetFiled("payDateline");
        return payDateline;
    }

    public CrmCustomerEntity setCreditLimit(Integer creditLimit) {
        this.creditLimit = creditLimit;
        this.nodifySetFiled("creditLimit", creditLimit);
        return this;
    }

    public Integer getCreditLimit() {
        this.nodifyGetFiled("creditLimit");
        return creditLimit;
    }

    public CrmCustomerEntity setAccountMode(String accountMode) {
        this.accountMode = accountMode;
        this.nodifySetFiled("accountMode", accountMode);
        return this;
    }

    public String getAccountMode() {
        this.nodifyGetFiled("accountMode");
        return accountMode;
    }

    public CrmCustomerEntity setPayMode(String payMode) {
        this.payMode = payMode;
        this.nodifySetFiled("payMode", payMode);
        return this;
    }

    public String getPayMode() {
        this.nodifyGetFiled("payMode");
        return payMode;
    }

    public CrmCustomerEntity setJdFreeze(String jdFreeze) {
        this.jdFreeze = jdFreeze;
        this.nodifySetFiled("jdFreeze", jdFreeze);
        return this;
    }

    public String getJdFreeze() {
        this.nodifyGetFiled("jdFreeze");
        return jdFreeze;
    }

    public CrmCustomerEntity setKpCycle(Integer kpCycle) {
        this.kpCycle = kpCycle;
        this.nodifySetFiled("kpCycle", kpCycle);
        return this;
    }

    public Integer getKpCycle() {
        this.nodifyGetFiled("kpCycle");
        return kpCycle;
    }

    public CrmCustomerEntity setKpDateline(Integer kpDateline) {
        this.kpDateline = kpDateline;
        this.nodifySetFiled("kpDateline", kpDateline);
        return this;
    }

    public Integer getKpDateline() {
        this.nodifyGetFiled("kpDateline");
        return kpDateline;
    }

    public CrmCustomerEntity setAccountKh(String accountKh) {
        this.accountKh = accountKh;
        this.nodifySetFiled("accountKh", accountKh);
        return this;
    }

    public String getAccountKh() {
        this.nodifyGetFiled("accountKh");
        return accountKh;
    }

    public CrmCustomerEntity setBankKh(String bankKh) {
        this.bankKh = bankKh;
        this.nodifySetFiled("bankKh", bankKh);
        return this;
    }

    public String getBankKh() {
        this.nodifyGetFiled("bankKh");
        return bankKh;
    }

    public CrmCustomerEntity setTaxNoKh(String taxNoKh) {
        this.taxNoKh = taxNoKh;
        this.nodifySetFiled("taxNoKh", taxNoKh);
        return this;
    }

    public String getTaxNoKh() {
        this.nodifyGetFiled("taxNoKh");
        return taxNoKh;
    }

    public CrmCustomerEntity setTaxValKh(Integer taxValKh) {
        this.taxValKh = taxValKh;
        this.nodifySetFiled("taxValKh", taxValKh);
        return this;
    }

    public Integer getTaxValKh() {
        this.nodifyGetFiled("taxValKh");
        return taxValKh;
    }

    public CrmCustomerEntity setInvTypeKh(String invTypeKh) {
        this.invTypeKh = invTypeKh;
        this.nodifySetFiled("invTypeKh", invTypeKh);
        return this;
    }

    public String getInvTypeKh() {
        this.nodifyGetFiled("invTypeKh");
        return invTypeKh;
    }

    public CrmCustomerEntity setJkCycle(Integer jkCycle) {
        this.jkCycle = jkCycle;
        this.nodifySetFiled("jkCycle", jkCycle);
        return this;
    }

    public Integer getJkCycle() {
        this.nodifyGetFiled("jkCycle");
        return jkCycle;
    }

    public CrmCustomerEntity setPayFreeze(String payFreeze) {
        this.payFreeze = payFreeze;
        this.nodifySetFiled("payFreeze", payFreeze);
        return this;
    }

    public String getPayFreeze() {
        this.nodifyGetFiled("payFreeze");
        return payFreeze;
    }

    public CrmCustomerEntity setAccountFp(String accountFp) {
        this.accountFp = accountFp;
        this.nodifySetFiled("accountFp", accountFp);
        return this;
    }

    public String getAccountFp() {
        this.nodifyGetFiled("accountFp");
        return accountFp;
    }

    public CrmCustomerEntity setBankFp(String bankFp) {
        this.bankFp = bankFp;
        this.nodifySetFiled("bankFp", bankFp);
        return this;
    }

    public String getBankFp() {
        this.nodifyGetFiled("bankFp");
        return bankFp;
    }

    public CrmCustomerEntity setTaxNoFp(String taxNoFp) {
        this.taxNoFp = taxNoFp;
        this.nodifySetFiled("taxNoFp", taxNoFp);
        return this;
    }

    public String getTaxNoFp() {
        this.nodifyGetFiled("taxNoFp");
        return taxNoFp;
    }

    public CrmCustomerEntity setTaxValFp(Integer taxValFp) {
        this.taxValFp = taxValFp;
        this.nodifySetFiled("taxValFp", taxValFp);
        return this;
    }

    public Integer getTaxValFp() {
        this.nodifyGetFiled("taxValFp");
        return taxValFp;
    }

    public CrmCustomerEntity setInvTypeFp(String invTypeFp) {
        this.invTypeFp = invTypeFp;
        this.nodifySetFiled("invTypeFp", invTypeFp);
        return this;
    }
    public CrmCustomerEntity setManageStatus(String manageStatus) {
        this.manageStatus = manageStatus;
        this.nodifySetFiled("manageStatus", manageStatus);
        return this;
    }
    public String getManageStatus() {
        this.nodifyGetFiled("manageStatus");
        return manageStatus;
    }
    public String getInvTypeFp() {
        this.nodifyGetFiled("invTypeFp");
        return invTypeFp;
    }

    public CrmCustomerEntity setMemoFp(String memoFp) {
        this.memoFp = memoFp;
        this.nodifySetFiled("memoFp", memoFp);
        return this;
    }

    public String getMemoFp() {
        this.nodifyGetFiled("memoFp");
        return memoFp;
    }

    public CrmCustomerEntity setHzfwCodeHz(String hzfwCodeHz) {
        this.hzfwCodeHz = hzfwCodeHz;
        this.nodifySetFiled("hzfwCodeHz", hzfwCodeHz);
        return this;
    }

    public String getHzfwCodeHz() {
        this.nodifyGetFiled("hzfwCodeHz");
        return hzfwCodeHz;
    }

    public CrmCustomerEntity setHzfwNameHz(String hzfwNameHz) {
        this.hzfwNameHz = hzfwNameHz;
        this.nodifySetFiled("hzfwNameHz", hzfwNameHz);
        return this;
    }

    public String getHzfwNameHz() {
        this.nodifyGetFiled("hzfwNameHz");
        return hzfwNameHz;
    }

    public CrmCustomerEntity setTel(String tel) {
        this.tel = tel;
        this.nodifySetFiled("tel", tel);
        return this;
    }

    public String getTel() {
        this.nodifyGetFiled("tel");
        return tel;
    }

    public CrmCustomerEntity setIsUserControl(String isUserControl) {
        this.isUserControl = isUserControl;
        this.nodifySetFiled("isUserControl", isUserControl);
        return this;
    }

    public String getIsUserControl() {
        this.nodifyGetFiled("isUserControl");
        return isUserControl;
    }

    public CrmCustomerEntity setCustomerB2b(String customerB2b) {
        this.customerB2b = customerB2b;
        this.nodifySetFiled("customerB2b", customerB2b);
        return this;
    }

    public String getCustomerB2b() {
        this.nodifyGetFiled("customerB2b");
        return customerB2b;
    }

    public CrmCustomerEntity setMemo(String memo) {
        this.memo = memo;
        this.nodifySetFiled("memo", memo);
        return this;
    }

    public String getMemo() {
        this.nodifyGetFiled("memo");
        return memo;
    }

    public CrmCustomerEntity setIsCustom(String isCustom) {
        this.isCustom = isCustom;
        this.nodifySetFiled("isCustom", isCustom);
        return this;
    }

    public String getIsCustom() {
        this.nodifyGetFiled("isCustom");
        return isCustom;
    }

    public CrmCustomerEntity setIsCustomName(String isCustomName) {
        this.isCustomName = isCustomName;
        this.nodifySetFiled("isCustomName", isCustomName);
        return this;
    }

    public String getIsCustomName() {
        this.nodifyGetFiled("isCustomName");
        return isCustomName;
    }

    public CrmCustomerEntity setCustomerGroupCode(String customerGroupCode) {
        this.customerGroupCode = customerGroupCode;
        this.nodifySetFiled("customerGroupCode", customerGroupCode);
        return this;
    }

    public String getCustomerGroupCode() {
        this.nodifyGetFiled("customerGroupCode");
        return customerGroupCode;
    }

    public CrmCustomerEntity setCustomerGroupName(String customerGroupName) {
        this.customerGroupName = customerGroupName;
        this.nodifySetFiled("customerGroupName", customerGroupName);
        return this;
    }

    public String getCustomerGroupName() {
        this.nodifyGetFiled("customerGroupName");
        return customerGroupName;
    }

    public CrmCustomerEntity setComType(String comType) {
        this.comType = comType;
        this.nodifySetFiled("comType", comType);
        return this;
    }

    public String getComType() {
        this.nodifyGetFiled("comType");
        return comType;
    }

    public CrmCustomerEntity setComTypeName(String comTypeName) {
        this.comTypeName = comTypeName;
        this.nodifySetFiled("comTypeName", comTypeName);
        return this;
    }

    public String getComTypeName() {
        this.nodifyGetFiled("comTypeName");
        return comTypeName;
    }

    public CrmCustomerEntity setMail(String mail) {
        this.mail = mail;
        this.nodifySetFiled("mail", mail);
        return this;
    }

    public String getMail() {
        this.nodifyGetFiled("mail");
        return mail;
    }

    public CrmCustomerEntity setAccountMemo(String accountMemo) {
        this.accountMemo = accountMemo;
        this.nodifySetFiled("accountMemo", accountMemo);
        return this;
    }

    public String getAccountMemo() {
        this.nodifyGetFiled("accountMemo");
        return accountMemo;
    }

    public CrmCustomerEntity setAuditRemark(String AuditRemark) {
        this.auditRemark = AuditRemark;
        this.nodifySetFiled("AuditRemark", AuditRemark);
        return this;
    }

    public String getAuditRemark() {
        this.nodifyGetFiled("AuditRemark");
        return auditRemark;
    }
    public CrmCustomerEntity setHzfwNameGys(String hzfwNameGys) {
        this.hzfwNameGys = hzfwNameGys;
        this.nodifySetFiled("hzfwNameGys", hzfwNameGys);
        return this;
    }

    public String getHzfwNameGys() {
        this.nodifyGetFiled("hzfwNameGys");
        return hzfwNameGys;
    }

    public CrmCustomerEntity setHzfwCodeGys(String hzfwCodeGys) {
        this.hzfwCodeGys = hzfwCodeGys;
        this.nodifySetFiled("hzfwCodeGys", hzfwCodeGys);
        return this;
    }

    public String getHzfwCodeGys() {
        this.nodifyGetFiled("hzfwCodeGys");
        return hzfwCodeGys;
    }
}
