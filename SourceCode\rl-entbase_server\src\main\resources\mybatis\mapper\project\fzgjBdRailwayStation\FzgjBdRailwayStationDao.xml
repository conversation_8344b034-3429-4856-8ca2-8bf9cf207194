<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.fzgjBdRailwayStation.dao.FzgjBdRailwayStationDao">
    <resultMap type="FzgjBdRailwayStationEntity" id="FzgjBdRailwayStationResult">
        <result property="guid" column="GUID"/>
        <result property="code" column="CODE"/>
        <result property="name" column="NAME"/>
        <result property="status" column="STATUS"/>
        <result property="seq" column="SEQ"/>
        <result property="memo" column="MEMO"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="countryCode" column="COUNTRY_CODE"/>
        <result property="provinceGuid" column="PROVINCE_GUID"/>
        <result property="cityGuid" column="CITY_GUID"/>
        <result property="districtGuid" column="DISTRICT_GUID"/>
    </resultMap>

    <resultMap type="FzgjBdRailwayStationPageEntity" id="FzgjBdRailwayStationPageResult">
        <result property="guid" column="GUID"/>
        <result property="code" column="CODE"/>
        <result property="name" column="NAME"/>
        <result property="status" column="STATUS"/>
        <result property="seq" column="SEQ"/>
        <result property="memo" column="MEMO"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="countryCode" column="COUNTRY_CODE"/>
        <result property="provinceGuid" column="PROVINCE_GUID"/>
        <result property="cityGuid" column="CITY_GUID"/>
        <result property="districtGuid" column="DISTRICT_GUID"/>
        <result property="countryName" column="COUNTRY_NAME"/>
        <result property="provinceName" column="PROVINCE_NAME"/>
        <result property="cityName" column="CITY_NAME"/>
        <result property="districtName" column="DISTRICT_NAME"/>
        <result property="statusName" column="STATUS_NAME"/>
    </resultMap>

    <sql id="selectFzgjBdRailwayStationEntityVo">
        select
            GUID,
            CODE,
            NAME,
            STATUS,
            SEQ,
            MEMO,
            CREATE_USER,
            CREATE_USER_NAME,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_USER_NAME,
            UPDATE_DATE,
            NODE_CODE,
            NODE_NAME,
            COMPANY_CODE,
            COMPANY_NAME,
            GROUP_CODE,
            GROUP_NAME,
            COUNTRY_CODE,
            PROVINCE_GUID,
            CITY_GUID,
            DISTRICT_GUID
        from FZGJ_BD_RAILWAY_STATION
    </sql>

    <select id="selectOneByID" parameterType="String" resultMap="FzgjBdRailwayStationPageResult">
    SELECT
         A.GUID
        ,A.CODE
        ,A.NAME
        ,A.STATUS
        ,A.SEQ
        ,A.MEMO
        ,A.CREATE_USER
        ,A.CREATE_USER_NAME
        ,A.CREATE_DATE
        ,A.UPDATE_USER
        ,A.UPDATE_USER_NAME
        ,A.UPDATE_DATE
        ,A.NODE_CODE
        ,A.NODE_NAME
        ,A.COMPANY_CODE
        ,A.COMPANY_NAME
        ,A.GROUP_CODE
        ,A.GROUP_NAME
        ,A.COUNTRY_CODE
        ,A.PROVINCE_GUID
        ,A.CITY_GUID
        ,A.DISTRICT_GUID
        ,E.CH_NAME  AS COUNTRY_NAME
        ,D.NAME AS PROVINCE_NAME
        ,C.NAME  AS CITY_NAME
        ,B.NAME AS DISTRICT_NAME
        FROM FZGJ_BD_RAILWAY_STATION A
        LEFT JOIN FZGJ_BD_DISTRICT B ON A.DISTRICT_GUID = B.GUID
        LEFT JOIN FZGJ_BD_CITY C ON B.CITY_ID = C.GUID
        LEFT JOIN FZGJ_BD_PROVINCE D ON C.PROVINCE_ID =D.GUID
        LEFT JOIN FZGJ_BD_COUNTRY E ON D.COUNTRY_ID = E.CODE
        WHERE 1=1 and A.GUID = #{guid}
    </select>

    <select id="selectStationPageList" parameterType="FzgjBdRailwayStationEntity" resultMap="FzgjBdRailwayStationPageResult">
        SELECT
         A.GUID
        ,A.CODE
        ,A.NAME
        ,A.STATUS
        ,DECODE(A.STATUS, 'Y', '是', 'N', '否', '') STATUS_NAME
        ,A.SEQ
        ,A.MEMO
        ,A.CREATE_USER
        ,A.CREATE_USER_NAME
        ,A.CREATE_DATE
        ,A.UPDATE_USER
        ,A.UPDATE_USER_NAME
        ,A.UPDATE_DATE
        ,A.NODE_CODE
        ,A.NODE_NAME
        ,A.COMPANY_CODE
        ,A.COMPANY_NAME
        ,A.GROUP_CODE
        ,A.GROUP_NAME
        ,A.COUNTRY_CODE
        ,A.PROVINCE_GUID
        ,A.CITY_GUID
        ,A.DISTRICT_GUID
        ,BCO.CH_NAME  AS COUNTRY_NAME
        ,BP.NAME AS PROVINCE_NAME
        ,BC.NAME  AS CITY_NAME
        ,BD.NAME AS DISTRICT_NAME
        FROM FZGJ_BD_RAILWAY_STATION A
        LEFT JOIN FZGJ_BD_DISTRICT BD ON A.DISTRICT_GUID = BD.GUID
        LEFT JOIN FZGJ_BD_CITY BC ON A.CITY_GUID = BC.GUID
        LEFT JOIN FZGJ_BD_PROVINCE BP ON A.PROVINCE_GUID = BP.GUID
        LEFT JOIN FZGJ_BD_COUNTRY BCO ON A.COUNTRY_CODE = BCO.CODE
        WHERE 1=1
        <if test="groupCode != null">
            AND  A.GROUP_CODE = #{groupCode}
        </if>
        <if test="code != null and code != ''">
            AND A.CODE like '%' || #{code} || '%'
        </if>
        <if test="name != null and name != ''">
            AND A.NAME like '%' || #{name} || '%'
        </if>
        <if test="status != null  and status != ''">
            AND A.STATUS = #{status}
        </if>
        <if test="countryCode != null and countryCode != ''">
            AND A.COUNTRY_CODE = #{countryCode}
        </if>
        <if test="provinceGuid != null and provinceGuid != ''">
            AND A.PROVINCE_GUID = #{provinceGuid}
        </if>
        <if test="cityGuid != null and cityGuid != ''">
            AND A.CITY_GUID = #{cityGuid}
        </if>
        <if test="districtGuid != null and districtGuid != ''">
            AND A.DISTRICT_GUID = #{districtGuid}
        </if>
    </select>
</mapper>