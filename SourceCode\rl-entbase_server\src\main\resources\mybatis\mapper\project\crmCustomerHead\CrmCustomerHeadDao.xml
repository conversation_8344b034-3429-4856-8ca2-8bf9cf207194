<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eci.project.crmCustomerHead.dao.CrmCustomerHeadDao">
    <resultMap type="CrmCustomerHeadEntity" id="CrmCustomerHeadResult">
        <result property="guid" column="GUID"/>
        <result property="code" column="CODE"/>
        <result property="name" column="NAME"/>
        <result property="shortName" column="SHORT_NAME"/>
        <result property="enName" column="EN_NAME"/>
        <result property="country" column="COUNTRY"/>
        <result property="province" column="PROVINCE"/>
        <result property="city" column="CITY"/>
        <result property="district" column="DISTRICT"/>
        <result property="address" column="ADDRESS"/>
        <result property="addressEn" column="ADDRESS_EN"/>
        <result property="tyCode" column="TY_CODE"/>
        <result property="person" column="PERSON"/>
        <result property="clDate" column="CL_DATE"/>
        <result property="zcCurr" column="ZC_CURR"/>
        <result property="zcCapital" column="ZC_CAPITAL"/>
        <result property="stockCode" column="STOCK_CODE"/>
        <result property="customNo" column="CUSTOM_NO"/>
        <result property="isSsgs" column="IS_SSGS"/>
        <result property="frdb" column="FRDB"/>
        <result property="employeeNum" column="EMPLOYEE_NUM"/>
        <result property="isNb" column="IS_NB"/>
        <result property="status" column="STATUS"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="createUserName" column="CREATE_USER_NAME"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="updateUserName" column="UPDATE_USER_NAME"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="nodeCode" column="NODE_CODE"/>
        <result property="nodeName" column="NODE_NAME"/>
        <result property="groupCode" column="GROUP_CODE"/>
        <result property="groupName" column="GROUP_NAME"/>
        <result property="roleCode" column="ROLE_CODE"/>
        <result property="roleName" column="ROLE_NAME"/>
        <result property="companyType" column="COMPANY_TYPE"/>
        <result property="tel" column="TEL"/>
        <result property="isUserControl" column="IS_USER_CONTROL"/>
        <result property="customerB2b" column="CUSTOMER_B2B"/>
        <result property="memo" column="MEMO"/>
        <result property="isCustom" column="IS_CUSTOM"/>
        <result property="customerGroupCode" column="CUSTOMER_GROUP_CODE"/>
        <result property="customerGroupName" column="CUSTOMER_GROUP_NAME"/>
        <result property="comType" column="COM_TYPE"/>
        <result property="hzDate" column="HZ_DATE"/>
        <result property="goodsCategory" column="GOODS_CATEGORY"/>
        <result property="ieType" column="IE_TYPE"/>
        <result property="tradeCountry" column="TRADE_COUNTRY"/>
        <result property="hyd" column="HYD"/>
        <result property="comTypeName" column="COM_TYPE_NAME"/>
        <result property="ieTypeName" column="IE_TYPE_NAME"/>
        <result property="mail" column="MAIL"/>
        <result property="quickMatchCode" column="QUICK_MATCH_CODE"/>
    </resultMap>

    <sql id="selectCrmCustomerHeadEntityVo">
        select
            GUID,
            CODE,
            NAME,
            SHORT_NAME,
            EN_NAME,
            COUNTRY,
            PROVINCE,
            CITY,
            DISTRICT,
            ADDRESS,
            ADDRESS_EN,
            TY_CODE,
            PERSON,
            CL_DATE,
            ZC_CURR,
            ZC_CAPITAL,
            STOCK_CODE,
            CUSTOM_NO,
            IS_SSGS,
            FRDB,
            EMPLOYEE_NUM,
            IS_NB,
            STATUS,
            CREATE_USER,
            CREATE_USER_NAME,
            CREATE_DATE,
            UPDATE_USER,
            UPDATE_USER_NAME,
            UPDATE_DATE,
            COMPANY_CODE,
            COMPANY_NAME,
            NODE_CODE,
            NODE_NAME,
            GROUP_CODE,
            GROUP_NAME,
            ROLE_CODE,
            ROLE_NAME,
            COMPANY_TYPE,
            TEL,
            IS_USER_CONTROL,
            CUSTOMER_B2B,
            MEMO,
            IS_CUSTOM,
            CUSTOMER_GROUP_CODE,
            CUSTOMER_GROUP_NAME,
            COM_TYPE,
            HZ_DATE,
            GOODS_CATEGORY,
            IE_TYPE,
            TRADE_COUNTRY,
            HYD,
            COM_TYPE_NAME,
            IE_TYPE_NAME,
            MAIL,
            QUICK_MATCH_CODE
        from CRM_CUSTOMER_HEAD
    </sql>
</mapper>