package com.eci.project.fzgjBdRailwayStation.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.fzgjBdRailwayStation.entity.FzgjBdRailwayStationEntity;

import org.springframework.stereotype.Service;


/**
* 铁路站点Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-03-25
*/
@Service
public class FzgjBdRailwayStationVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(FzgjBdRailwayStationEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(FzgjBdRailwayStationEntity entity, BusinessType businessType) {

    }

}
