package com.eci.project.etmsOpFileQz.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.db.DBHelper;
import com.eci.common.enums.Enums;
import com.eci.common.util.DateUtils;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.etmsBdTruckLimitedQz.entity.EtmsBdTruckLimitedQzDTOEntity;
import com.eci.project.etmsBdTruckLimitedQz.entity.EtmsBdTruckLimitedQzEntity;
import com.eci.project.etmsOpFileQz.dao.EtmsOpFileQzDao;
import com.eci.project.etmsOpFileQz.entity.EtmsOpFileQzDTOEntity;
import com.eci.project.etmsOpFileQz.entity.EtmsOpFileQzEntity;
import com.eci.project.etmsOpFileQz.validate.EtmsOpFileQzVal;

import com.eci.sso.role.entity.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.Serializable;
import java.util.Arrays;
import java.util.List;


/**
* 业务附件Service业务层处理
*
* @<NAME_EMAIL>
* @date 2025-04-17
*/
@Service
@Slf4j
public class EtmsOpFileQzService implements EciBaseService<EtmsOpFileQzEntity> {

    @Autowired
    private EtmsOpFileQzDao etmsOpFileQzDao;

    @Autowired
    private EtmsOpFileQzVal etmsOpFileQzVal;


    @Override
    public TgPageInfo queryPageList(EtmsOpFileQzEntity entity) {
        startPage();
        String sql="SELECT A.GUID,\n" +
                "         A.OP_TYPE,\n" +
                "          (CASE A.OP_TYPE \n" +
                "               WHEN 'JSZ' THEN '驾驶证' \n" +
                "               WHEN 'XSZ' THEN '行驶证'\n" +
                "               WHEN 'GCXSZ' THEN '车挂行驶证' \n" +
                "               WHEN 'YYZ' THEN '营运证'\n" +
                "               END) AS OP_TYPE_NAME,\n" +
                "         A.FILE_NO,\n" +
                "         A.FILE_NAME,\n" +
                "         A.FILE_NAME AS FILE_SOURCE_NAME,\n" +
                "         A.FILE_TYPE,\n" +
                "         A.MEMO,\n" +
                "         A.OP_NO,\n" +
                "         A.CREATE_USER_NAME,\n" +
                "         A.CREATE_USER,\n" +
                "         A.CREATE_DATE,\n" +
                "         A.UPDATE_USER_NAME,\n" +
                "         A.UPDATE_USER,\n" +
                "         A.UPDATE_DATE,\n" +
                "         A.FILE_URL,\n" +
                "         BD.CHECK_STATUS\n" +
                "    FROM ETMS_BD_TRUCK_QZ BD  \n" +
                "    RIGHT JOIN ETMS_OP_FILE_QZ A ON BD.GUID=A.OP_NO\n" +
                "  WHERE 1=1 AND A.OP_NO=?";
        List<EtmsOpFileQzDTOEntity> pageInfo = DBHelper.selectList(sql, EtmsOpFileQzDTOEntity.class, entity.getOpNo());
        return EciQuery.getPageInfo(pageInfo);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public EtmsOpFileQzEntity save(EtmsOpFileQzEntity entity) {
        // 返回实体对象
        EtmsOpFileQzEntity etmsOpFileQzEntity = null;
        etmsOpFileQzVal.saveValidate(entity,BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setGuid(IdWorker.get32UUID());
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setCreateDate(DateUtils.getNowDate());
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
            entity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            entity.setCompanyName(UserContext.getUserInfo().getCompanyName());
            entity.setNodeCode(UserContext.getUserInfo().getDeptCode());
            entity.setNodeName(UserContext.getUserInfo().getDeptName());
            entity.setModMark(Enums.ModMark.XZ.getValue());
            String suffix = entity.getFileUrl().substring(entity.getFileUrl().lastIndexOf("."));
            entity.setFileType(suffix);
            etmsOpFileQzEntity = etmsOpFileQzDao.insertOne(entity);

        }else{
            entity.setUpdateDate(DateUtils.getNowDate());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
            entity.setModMark(Enums.ModMark.XG.getValue());
            etmsOpFileQzEntity = etmsOpFileQzDao.updateByEntityId(entity);

        }
        return etmsOpFileQzEntity;
    }

    @Override
    public List<EtmsOpFileQzEntity> selectList(EtmsOpFileQzEntity entity) {
        return etmsOpFileQzDao.selectList(entity);
    }

    @Override
    public EtmsOpFileQzEntity selectOneById(Serializable id) {
        return etmsOpFileQzDao.selectById(id);
    }


    @Override
    public void insertBatch(List<EtmsOpFileQzEntity> list) {
        etmsOpFileQzDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return etmsOpFileQzDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return etmsOpFileQzDao.deleteById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public int deleteByList(String ids) {
        List<String> list= Arrays.asList(ids.split(","));
        int count=0;
        for(String str:list){
            QueryWrapper<EtmsOpFileQzEntity> queryWrapper=new QueryWrapper<>();
            queryWrapper.eq("GUID",str);
            List<EtmsOpFileQzEntity> listdel =etmsOpFileQzDao.selectList(queryWrapper);
            if(listdel.size()>0){
                File file = new File(listdel.get(0).getFileUrl());
                if(file.delete()){
                    count+=etmsOpFileQzDao.delete(queryWrapper);
                }
            }
        }
        return count;
    }
}