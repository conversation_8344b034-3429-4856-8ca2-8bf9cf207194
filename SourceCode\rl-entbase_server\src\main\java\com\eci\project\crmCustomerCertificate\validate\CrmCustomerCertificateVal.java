package com.eci.project.crmCustomerCertificate.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.crmCustomerCertificate.entity.CrmCustomerCertificateEntity;

import com.eci.sso.role.entity.UserContext;
import org.springframework.stereotype.Service;

import java.util.Date;


/**
* 司机证件管理Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-05-26
*/
@Service
public class CrmCustomerCertificateVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(CrmCustomerCertificateEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(CrmCustomerCertificateEntity entity, BusinessType businessType) {
        entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        entity.setUpdateDate(new Date());
        entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
        if(businessType==BusinessType.INSERT){
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateDate(new Date());
            entity.setGroupCode(UserContext.getUserInfo().getCompanyCode());
            entity.setGroupName(UserContext.getUserInfo().getCompanyName());
            entity.setCompanyCode(UserContext.getUserInfo().getCompanyCode());
            entity.setCompanyName(UserContext.getUserInfo().getCompanyName());
        }
    }

}
