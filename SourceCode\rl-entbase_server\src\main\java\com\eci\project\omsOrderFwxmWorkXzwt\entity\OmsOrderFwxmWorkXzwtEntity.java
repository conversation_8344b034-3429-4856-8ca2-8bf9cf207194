package com.eci.project.omsOrderFwxmWorkXzwt.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 协作委托表对象 OMS_ORDER_FWXM_WORK_XZWT
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-04-14
*/
@ApiModel("协作委托表")
@TableName("OMS_ORDER_FWXM_WORK_XZWT")
@FieldNameConstants
public class OmsOrderFwxmWorkXzwtEntity extends EciBaseEntity{
    /**
    * 协作委托单编号
    */
    @ApiModelProperty("协作委托单编号(36)")
    @TableId("XZWT_NO")
    private String xzwtNo;

    /**
    * 订单号
    */
    @ApiModelProperty("订单号(36)")
    @TableField("ORDER_NO")
    private String orderNo;

    /**
    * 作业系统委托单据类型
    */
    @ApiModelProperty("作业系统委托单据类型(40)")
    @TableField("BILL_CODE")
    private String billCode;

    /**
    * 作业系统委托单据编号
    */
    @ApiModelProperty("作业系统委托单据编号(50)")
    @TableField("DOC_NO")
    private String docNo;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 归属公司
    */
    @ApiModelProperty("归属公司(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(50)")
    @TableField("NODE_CODE")
    private String nodeCode;

    /**
    * 归属组织
    */
    @ApiModelProperty("归属组织(200)")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    /**
    * 归属集团
    */
    @ApiModelProperty("归属集团(200)")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
    * 发送标记
    */
    @ApiModelProperty("发送标记(1)")
    @TableField("SEND_FLAG")
    private String sendFlag;

    /**
    * 分发人
    */
    @ApiModelProperty("分发人(20)")
    @TableField("SEND_USER")
    private String sendUser;

    /**
    * 分发组织
    */
    @ApiModelProperty("分发组织(36)")
    @TableField("SEND_NODE_CODE")
    private String sendNodeCode;

    /**
    * 分发组织
    */
    @ApiModelProperty("分发组织(200)")
    @TableField("SEND_NODE_NAME")
    private String sendNodeName;

    /**
    * 分发人
    */
    @ApiModelProperty("分发人(50)")
    @TableField("SEND_USER_NAME")
    private String sendUserName;

    /**
    * 作业完成时间
    */
    @ApiModelProperty("作业完成时间(7)")
    @TableField("OP_COMPLETE_OK_DATE")
    private Date opCompleteOkDate;

    @ApiModelProperty("作业完成时间开始")
    @TableField(exist=false)
    private Date opCompleteOkDateStart;

    @ApiModelProperty("作业完成时间结束")
    @TableField(exist=false)
    private Date opCompleteOkDateEnd;

    /**
    * 作业完成标识
    */
    @ApiModelProperty("作业完成标识(1)")
    @TableField("OP_COMPLETE_OK")
    private String opCompleteOk;

    /**
    * 作业数据齐全时间
    */
    @ApiModelProperty("作业数据齐全时间(7)")
    @TableField("DATA_OK_DATE")
    private Date dataOkDate;

    @ApiModelProperty("作业数据齐全时间开始")
    @TableField(exist=false)
    private Date dataOkDateStart;

    @ApiModelProperty("作业数据齐全时间结束")
    @TableField(exist=false)
    private Date dataOkDateEnd;

    /**
    * 作业数据齐全标志（源数据齐全）
    */
    @ApiModelProperty("作业数据齐全标志（源数据齐全）(1)")
    @TableField("DATA_OK")
    private String dataOk;

    /**
    * 结算完成标记（应收应付齐全）
    */
    @ApiModelProperty("结算完成标记（应收应付齐全）(1)")
    @TableField("ARAP_OK")
    private String arapOk;

    /**
    * 结算完成日期
    */
    @ApiModelProperty("结算完成日期(7)")
    @TableField("ARAP_OK_DATE")
    private Date arapOkDate;

    @ApiModelProperty("结算完成日期开始")
    @TableField(exist=false)
    private Date arapOkDateStart;

    @ApiModelProperty("结算完成日期结束")
    @TableField(exist=false)
    private Date arapOkDateEnd;

    /**
    * 供应商业务伙伴
    */
    @ApiModelProperty("供应商业务伙伴(50)")
    @TableField("GYS_CODE")
    private String gysCode;

    /**
    * 供应商业务伙伴
    */
    @ApiModelProperty("供应商业务伙伴(200)")
    @TableField("GYS_NAME")
    private String gysName;

    /**
    * 内部作业组织
    */
    @ApiModelProperty("内部作业组织(50)")
    @TableField("NODE_CODE_NB")
    private String nodeCodeNb;

    /**
    * 内部作业组织
    */
    @ApiModelProperty("内部作业组织(200)")
    @TableField("NODE_NAME_NB")
    private String nodeNameNb;

    /**
    * 供应商业务伙伴所属集团
    */
    @ApiModelProperty("供应商业务伙伴所属集团(50)")
    @TableField("GYS_GROUP_CODE")
    private String gysGroupCode;

    /**
    * 供应商业务伙伴所属集团
    */
    @ApiModelProperty("供应商业务伙伴所属集团(200)")
    @TableField("GYS_GROUP_NAME")
    private String gysGroupName;

    /**
    * 供应商反馈作业数据反馈方式
    */
    @ApiModelProperty("供应商反馈作业数据反馈方式(20)")
    @TableField("RESPONSE_CODE")
    private String responseCode;

    /**
    * 业务数据唯一注册编号
    */
    @ApiModelProperty("业务数据唯一注册编号(50)")
    @TableField("BIZ_REG_ID")
    private String bizRegId;

    /**
    * 需应付结算
    */
    @ApiModelProperty("需应付结算(1)")
    @TableField("IS_AP")
    private String isAp;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public OmsOrderFwxmWorkXzwtEntity() {
        this.setSubClazz(OmsOrderFwxmWorkXzwtEntity.class);
    }

    public OmsOrderFwxmWorkXzwtEntity setXzwtNo(String xzwtNo) {
        this.xzwtNo = xzwtNo;
        this.nodifySetFiled("xzwtNo", xzwtNo);
        return this;
    }

    public String getXzwtNo() {
        this.nodifyGetFiled("xzwtNo");
        return xzwtNo;
    }

    public OmsOrderFwxmWorkXzwtEntity setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        this.nodifySetFiled("orderNo", orderNo);
        return this;
    }

    public String getOrderNo() {
        this.nodifyGetFiled("orderNo");
        return orderNo;
    }

    public OmsOrderFwxmWorkXzwtEntity setBillCode(String billCode) {
        this.billCode = billCode;
        this.nodifySetFiled("billCode", billCode);
        return this;
    }

    public String getBillCode() {
        this.nodifyGetFiled("billCode");
        return billCode;
    }

    public OmsOrderFwxmWorkXzwtEntity setDocNo(String docNo) {
        this.docNo = docNo;
        this.nodifySetFiled("docNo", docNo);
        return this;
    }

    public String getDocNo() {
        this.nodifyGetFiled("docNo");
        return docNo;
    }

    public OmsOrderFwxmWorkXzwtEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public OmsOrderFwxmWorkXzwtEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

    public OmsOrderFwxmWorkXzwtEntity setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
        this.nodifySetFiled("nodeCode", nodeCode);
        return this;
    }

    public String getNodeCode() {
        this.nodifyGetFiled("nodeCode");
        return nodeCode;
    }

    public OmsOrderFwxmWorkXzwtEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        this.nodifySetFiled("nodeName", nodeName);
        return this;
    }

    public String getNodeName() {
        this.nodifyGetFiled("nodeName");
        return nodeName;
    }

    public OmsOrderFwxmWorkXzwtEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public OmsOrderFwxmWorkXzwtEntity setGroupName(String groupName) {
        this.groupName = groupName;
        this.nodifySetFiled("groupName", groupName);
        return this;
    }

    public String getGroupName() {
        this.nodifyGetFiled("groupName");
        return groupName;
    }

    public OmsOrderFwxmWorkXzwtEntity setSendFlag(String sendFlag) {
        this.sendFlag = sendFlag;
        this.nodifySetFiled("sendFlag", sendFlag);
        return this;
    }

    public String getSendFlag() {
        this.nodifyGetFiled("sendFlag");
        return sendFlag;
    }

    public OmsOrderFwxmWorkXzwtEntity setSendUser(String sendUser) {
        this.sendUser = sendUser;
        this.nodifySetFiled("sendUser", sendUser);
        return this;
    }

    public String getSendUser() {
        this.nodifyGetFiled("sendUser");
        return sendUser;
    }

    public OmsOrderFwxmWorkXzwtEntity setSendNodeCode(String sendNodeCode) {
        this.sendNodeCode = sendNodeCode;
        this.nodifySetFiled("sendNodeCode", sendNodeCode);
        return this;
    }

    public String getSendNodeCode() {
        this.nodifyGetFiled("sendNodeCode");
        return sendNodeCode;
    }

    public OmsOrderFwxmWorkXzwtEntity setSendNodeName(String sendNodeName) {
        this.sendNodeName = sendNodeName;
        this.nodifySetFiled("sendNodeName", sendNodeName);
        return this;
    }

    public String getSendNodeName() {
        this.nodifyGetFiled("sendNodeName");
        return sendNodeName;
    }

    public OmsOrderFwxmWorkXzwtEntity setSendUserName(String sendUserName) {
        this.sendUserName = sendUserName;
        this.nodifySetFiled("sendUserName", sendUserName);
        return this;
    }

    public String getSendUserName() {
        this.nodifyGetFiled("sendUserName");
        return sendUserName;
    }

    public OmsOrderFwxmWorkXzwtEntity setOpCompleteOkDate(Date opCompleteOkDate) {
        this.opCompleteOkDate = opCompleteOkDate;
        this.nodifySetFiled("opCompleteOkDate", opCompleteOkDate);
        return this;
    }

    public Date getOpCompleteOkDate() {
        this.nodifyGetFiled("opCompleteOkDate");
        return opCompleteOkDate;
    }

    public OmsOrderFwxmWorkXzwtEntity setOpCompleteOkDateStart(Date opCompleteOkDateStart) {
        this.opCompleteOkDateStart = opCompleteOkDateStart;
        this.nodifySetFiled("opCompleteOkDateStart", opCompleteOkDateStart);
        return this;
    }

    public Date getOpCompleteOkDateStart() {
        this.nodifyGetFiled("opCompleteOkDateStart");
        return opCompleteOkDateStart;
    }

    public OmsOrderFwxmWorkXzwtEntity setOpCompleteOkDateEnd(Date opCompleteOkDateEnd) {
        this.opCompleteOkDateEnd = opCompleteOkDateEnd;
        this.nodifySetFiled("opCompleteOkDateEnd", opCompleteOkDateEnd);
        return this;
    }

    public Date getOpCompleteOkDateEnd() {
        this.nodifyGetFiled("opCompleteOkDateEnd");
        return opCompleteOkDateEnd;
    }
    public OmsOrderFwxmWorkXzwtEntity setOpCompleteOk(String opCompleteOk) {
        this.opCompleteOk = opCompleteOk;
        this.nodifySetFiled("opCompleteOk", opCompleteOk);
        return this;
    }

    public String getOpCompleteOk() {
        this.nodifyGetFiled("opCompleteOk");
        return opCompleteOk;
    }

    public OmsOrderFwxmWorkXzwtEntity setDataOkDate(Date dataOkDate) {
        this.dataOkDate = dataOkDate;
        this.nodifySetFiled("dataOkDate", dataOkDate);
        return this;
    }

    public Date getDataOkDate() {
        this.nodifyGetFiled("dataOkDate");
        return dataOkDate;
    }

    public OmsOrderFwxmWorkXzwtEntity setDataOkDateStart(Date dataOkDateStart) {
        this.dataOkDateStart = dataOkDateStart;
        this.nodifySetFiled("dataOkDateStart", dataOkDateStart);
        return this;
    }

    public Date getDataOkDateStart() {
        this.nodifyGetFiled("dataOkDateStart");
        return dataOkDateStart;
    }

    public OmsOrderFwxmWorkXzwtEntity setDataOkDateEnd(Date dataOkDateEnd) {
        this.dataOkDateEnd = dataOkDateEnd;
        this.nodifySetFiled("dataOkDateEnd", dataOkDateEnd);
        return this;
    }

    public Date getDataOkDateEnd() {
        this.nodifyGetFiled("dataOkDateEnd");
        return dataOkDateEnd;
    }
    public OmsOrderFwxmWorkXzwtEntity setDataOk(String dataOk) {
        this.dataOk = dataOk;
        this.nodifySetFiled("dataOk", dataOk);
        return this;
    }

    public String getDataOk() {
        this.nodifyGetFiled("dataOk");
        return dataOk;
    }

    public OmsOrderFwxmWorkXzwtEntity setArapOk(String arapOk) {
        this.arapOk = arapOk;
        this.nodifySetFiled("arapOk", arapOk);
        return this;
    }

    public String getArapOk() {
        this.nodifyGetFiled("arapOk");
        return arapOk;
    }

    public OmsOrderFwxmWorkXzwtEntity setArapOkDate(Date arapOkDate) {
        this.arapOkDate = arapOkDate;
        this.nodifySetFiled("arapOkDate", arapOkDate);
        return this;
    }

    public Date getArapOkDate() {
        this.nodifyGetFiled("arapOkDate");
        return arapOkDate;
    }

    public OmsOrderFwxmWorkXzwtEntity setArapOkDateStart(Date arapOkDateStart) {
        this.arapOkDateStart = arapOkDateStart;
        this.nodifySetFiled("arapOkDateStart", arapOkDateStart);
        return this;
    }

    public Date getArapOkDateStart() {
        this.nodifyGetFiled("arapOkDateStart");
        return arapOkDateStart;
    }

    public OmsOrderFwxmWorkXzwtEntity setArapOkDateEnd(Date arapOkDateEnd) {
        this.arapOkDateEnd = arapOkDateEnd;
        this.nodifySetFiled("arapOkDateEnd", arapOkDateEnd);
        return this;
    }

    public Date getArapOkDateEnd() {
        this.nodifyGetFiled("arapOkDateEnd");
        return arapOkDateEnd;
    }
    public OmsOrderFwxmWorkXzwtEntity setGysCode(String gysCode) {
        this.gysCode = gysCode;
        this.nodifySetFiled("gysCode", gysCode);
        return this;
    }

    public String getGysCode() {
        this.nodifyGetFiled("gysCode");
        return gysCode;
    }

    public OmsOrderFwxmWorkXzwtEntity setGysName(String gysName) {
        this.gysName = gysName;
        this.nodifySetFiled("gysName", gysName);
        return this;
    }

    public String getGysName() {
        this.nodifyGetFiled("gysName");
        return gysName;
    }

    public OmsOrderFwxmWorkXzwtEntity setNodeCodeNb(String nodeCodeNb) {
        this.nodeCodeNb = nodeCodeNb;
        this.nodifySetFiled("nodeCodeNb", nodeCodeNb);
        return this;
    }

    public String getNodeCodeNb() {
        this.nodifyGetFiled("nodeCodeNb");
        return nodeCodeNb;
    }

    public OmsOrderFwxmWorkXzwtEntity setNodeNameNb(String nodeNameNb) {
        this.nodeNameNb = nodeNameNb;
        this.nodifySetFiled("nodeNameNb", nodeNameNb);
        return this;
    }

    public String getNodeNameNb() {
        this.nodifyGetFiled("nodeNameNb");
        return nodeNameNb;
    }

    public OmsOrderFwxmWorkXzwtEntity setGysGroupCode(String gysGroupCode) {
        this.gysGroupCode = gysGroupCode;
        this.nodifySetFiled("gysGroupCode", gysGroupCode);
        return this;
    }

    public String getGysGroupCode() {
        this.nodifyGetFiled("gysGroupCode");
        return gysGroupCode;
    }

    public OmsOrderFwxmWorkXzwtEntity setGysGroupName(String gysGroupName) {
        this.gysGroupName = gysGroupName;
        this.nodifySetFiled("gysGroupName", gysGroupName);
        return this;
    }

    public String getGysGroupName() {
        this.nodifyGetFiled("gysGroupName");
        return gysGroupName;
    }

    public OmsOrderFwxmWorkXzwtEntity setResponseCode(String responseCode) {
        this.responseCode = responseCode;
        this.nodifySetFiled("responseCode", responseCode);
        return this;
    }

    public String getResponseCode() {
        this.nodifyGetFiled("responseCode");
        return responseCode;
    }

    public OmsOrderFwxmWorkXzwtEntity setBizRegId(String bizRegId) {
        this.bizRegId = bizRegId;
        this.nodifySetFiled("bizRegId", bizRegId);
        return this;
    }

    public String getBizRegId() {
        this.nodifyGetFiled("bizRegId");
        return bizRegId;
    }

    public OmsOrderFwxmWorkXzwtEntity setIsAp(String isAp) {
        this.isAp = isAp;
        this.nodifySetFiled("isAp", isAp);
        return this;
    }

    public String getIsAp() {
        this.nodifyGetFiled("isAp");
        return isAp;
    }

}
