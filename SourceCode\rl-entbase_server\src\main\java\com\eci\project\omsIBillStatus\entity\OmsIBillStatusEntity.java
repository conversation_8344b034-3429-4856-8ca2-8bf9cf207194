package com.eci.project.omsIBillStatus.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Date;


/**
* 单据状态对象 OMS_I_BILL_STATUS
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-05-16
*/
@ApiModel("单据状态")
@TableName("OMS_I_BILL_STATUS")
@FieldNameConstants
public class OmsIBillStatusEntity extends EciBaseEntity{
    /**
    * GUID
    */
    @ApiModelProperty("GUID(36)")
    @TableId("GUID")
    private String guid;

    /**
    * OMS协作任务编号
    */
    @ApiModelProperty("OMS协作任务编号(50)")
    @TableField("WORK_NO")
    private String workNo;

    /**
    * OMS订单号
    */
    @ApiModelProperty("OMS订单号(36)")
    @TableField("ORDER_NO")
    private String orderNo;

    /**
    * 单据状态(作业完成：01作业数据齐全：02作废：03
    */
    @ApiModelProperty("单据状态(作业完成：01作业数据齐全：02作废：03(10)")
    @TableField("DOC_STATUS")
    private String docStatus;

    /**
    * 作业系统代码
    */
    @ApiModelProperty("作业系统代码(10)")
    @TableField("SYS_CODE")
    private String sysCode;

    /**
    * 变更时间
    */
    @ApiModelProperty("变更时间(7)")
    @TableField("OK_DATE")
    private Date okDate;

    @ApiModelProperty("变更时间开始")
    @TableField(exist=false)
    private Date okDateStart;

    @ApiModelProperty("变更时间结束")
    @TableField(exist=false)
    private Date okDateEnd;

    /**
    * 传输时间
    */
    @ApiModelProperty("传输时间(7)")
    @TableField("TRN_DATE")
    private Date trnDate;

    @ApiModelProperty("传输时间开始")
    @TableField(exist=false)
    private Date trnDateStart;

    @ApiModelProperty("传输时间结束")
    @TableField(exist=false)
    private Date trnDateEnd;

    /**
    * 处理标记,0未处理，1已处理，2处理失败
    */
    @ApiModelProperty("处理标记,0未处理，1已处理，2处理失败(1)")
    @TableField("OP_FLAG")
    private String opFlag;

    /**
    * 操作时间
    */
    @ApiModelProperty("操作时间(7)")
    @TableField("OP_DATE")
    private Date opDate;

    @ApiModelProperty("操作时间开始")
    @TableField(exist=false)
    private Date opDateStart;

    @ApiModelProperty("操作时间结束")
    @TableField(exist=false)
    private Date opDateEnd;

    /**
    * 处理结果
    */
    @ApiModelProperty("处理结果(4,000)")
    @TableField("OP_RESULT")
    private String opResult;

    /**
    * 协作委托单编号
    */
    @ApiModelProperty("协作委托单编号(36)")
    @TableField("XZWT_NO")
    private String xzwtNo;

    /**
    * 业务数据唯一注册编号
    */
    @ApiModelProperty("业务数据唯一注册编号(50)")
    @TableField("BIZ_REG_ID")
    private String bizRegId;

    /**
    * 发送企业代码
    */
    @ApiModelProperty("发送企业代码(20)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
    * 发送企业名称
    */
    @ApiModelProperty("发送企业名称(200)")
    @TableField("COMPANY_NAME")
    private String companyName;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public OmsIBillStatusEntity() {
        this.setSubClazz(OmsIBillStatusEntity.class);
    }

    public OmsIBillStatusEntity setGuid(String guid) {
        this.guid = guid;
        this.nodifySetFiled("guid", guid);
        return this;
    }

    public String getGuid() {
        this.nodifyGetFiled("guid");
        return guid;
    }

    public OmsIBillStatusEntity setWorkNo(String workNo) {
        this.workNo = workNo;
        this.nodifySetFiled("workNo", workNo);
        return this;
    }

    public String getWorkNo() {
        this.nodifyGetFiled("workNo");
        return workNo;
    }

    public OmsIBillStatusEntity setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        this.nodifySetFiled("orderNo", orderNo);
        return this;
    }

    public String getOrderNo() {
        this.nodifyGetFiled("orderNo");
        return orderNo;
    }

    public OmsIBillStatusEntity setDocStatus(String docStatus) {
        this.docStatus = docStatus;
        this.nodifySetFiled("docStatus", docStatus);
        return this;
    }

    public String getDocStatus() {
        this.nodifyGetFiled("docStatus");
        return docStatus;
    }

    public OmsIBillStatusEntity setSysCode(String sysCode) {
        this.sysCode = sysCode;
        this.nodifySetFiled("sysCode", sysCode);
        return this;
    }

    public String getSysCode() {
        this.nodifyGetFiled("sysCode");
        return sysCode;
    }

    public OmsIBillStatusEntity setOkDate(Date okDate) {
        this.okDate = okDate;
        this.nodifySetFiled("okDate", okDate);
        return this;
    }

    public Date getOkDate() {
        this.nodifyGetFiled("okDate");
        return okDate;
    }

    public OmsIBillStatusEntity setOkDateStart(Date okDateStart) {
        this.okDateStart = okDateStart;
        this.nodifySetFiled("okDateStart", okDateStart);
        return this;
    }

    public Date getOkDateStart() {
        this.nodifyGetFiled("okDateStart");
        return okDateStart;
    }

    public OmsIBillStatusEntity setOkDateEnd(Date okDateEnd) {
        this.okDateEnd = okDateEnd;
        this.nodifySetFiled("okDateEnd", okDateEnd);
        return this;
    }

    public Date getOkDateEnd() {
        this.nodifyGetFiled("okDateEnd");
        return okDateEnd;
    }
    public OmsIBillStatusEntity setTrnDate(Date trnDate) {
        this.trnDate = trnDate;
        this.nodifySetFiled("trnDate", trnDate);
        return this;
    }

    public Date getTrnDate() {
        this.nodifyGetFiled("trnDate");
        return trnDate;
    }

    public OmsIBillStatusEntity setTrnDateStart(Date trnDateStart) {
        this.trnDateStart = trnDateStart;
        this.nodifySetFiled("trnDateStart", trnDateStart);
        return this;
    }

    public Date getTrnDateStart() {
        this.nodifyGetFiled("trnDateStart");
        return trnDateStart;
    }

    public OmsIBillStatusEntity setTrnDateEnd(Date trnDateEnd) {
        this.trnDateEnd = trnDateEnd;
        this.nodifySetFiled("trnDateEnd", trnDateEnd);
        return this;
    }

    public Date getTrnDateEnd() {
        this.nodifyGetFiled("trnDateEnd");
        return trnDateEnd;
    }
    public OmsIBillStatusEntity setOpFlag(String opFlag) {
        this.opFlag = opFlag;
        this.nodifySetFiled("opFlag", opFlag);
        return this;
    }

    public String getOpFlag() {
        this.nodifyGetFiled("opFlag");
        return opFlag;
    }

    public OmsIBillStatusEntity setOpDate(Date opDate) {
        this.opDate = opDate;
        this.nodifySetFiled("opDate", opDate);
        return this;
    }

    public Date getOpDate() {
        this.nodifyGetFiled("opDate");
        return opDate;
    }

    public OmsIBillStatusEntity setOpDateStart(Date opDateStart) {
        this.opDateStart = opDateStart;
        this.nodifySetFiled("opDateStart", opDateStart);
        return this;
    }

    public Date getOpDateStart() {
        this.nodifyGetFiled("opDateStart");
        return opDateStart;
    }

    public OmsIBillStatusEntity setOpDateEnd(Date opDateEnd) {
        this.opDateEnd = opDateEnd;
        this.nodifySetFiled("opDateEnd", opDateEnd);
        return this;
    }

    public Date getOpDateEnd() {
        this.nodifyGetFiled("opDateEnd");
        return opDateEnd;
    }
    public OmsIBillStatusEntity setOpResult(String opResult) {
        this.opResult = opResult;
        this.nodifySetFiled("opResult", opResult);
        return this;
    }

    public String getOpResult() {
        this.nodifyGetFiled("opResult");
        return opResult;
    }

    public OmsIBillStatusEntity setXzwtNo(String xzwtNo) {
        this.xzwtNo = xzwtNo;
        this.nodifySetFiled("xzwtNo", xzwtNo);
        return this;
    }

    public String getXzwtNo() {
        this.nodifyGetFiled("xzwtNo");
        return xzwtNo;
    }

    public OmsIBillStatusEntity setBizRegId(String bizRegId) {
        this.bizRegId = bizRegId;
        this.nodifySetFiled("bizRegId", bizRegId);
        return this;
    }

    public String getBizRegId() {
        this.nodifyGetFiled("bizRegId");
        return bizRegId;
    }

    public OmsIBillStatusEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public OmsIBillStatusEntity setCompanyName(String companyName) {
        this.companyName = companyName;
        this.nodifySetFiled("companyName", companyName);
        return this;
    }

    public String getCompanyName() {
        this.nodifyGetFiled("companyName");
        return companyName;
    }

}
