package com.eci.project.etmsBdDriverInsurance.entity;

import com.eci.common.utils.ExportUtils.Excel;
import com.eci.crud.entity.EciBaseEntity;

import java.math.BigDecimal;
import java.util.Date;

public class EtmsBdDriverInsuranceSearchEntity extends EciBaseEntity {
    private String guid;
    @Excel(value = "司机姓名",order = 1)
    private String name;
    @Excel(value = "保险类型",order = 2)
    private String insuranceType;
    @Excel(value = "保单编号",order = 3)
    private String policyNo;
    @Excel(value = "投保人/企业名称",order = 4)
    private String policyholder;
    @Excel(value = "保险公司",order = 5)
    private String insurer;
    @Excel(value = "保险金额",order = 6)
    private BigDecimal amount;
    @Excel(value = "保险开始日期",order = 7)
    private String startDate;
    private Date startDateStart;
    private Date startDateEnd;
    @Excel(value = "保险结束日期",order = 8)
    private String endDate;
    private Date endDateStart;
    private Date endDateEnd;
    @Excel(value = "投保期限",order = 9)
    private String insuranceTerm;
    @Excel(value = "是否送审",order = 10)
    private String modMark;
    @Excel(value = "审核通过",order = 11)
    private String checkMark;
    @Excel(value = "保险状态",order = 12)
    private String status;
    @Excel(value = "创建企业",order = 13)
    private String companyName;
    @Excel(value = "创建时间",order = 14)
    private Date createDate;
    private Date createDateStart;
    private Date createDateEnd;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getInsuranceType() {
        return insuranceType;
    }

    public void setInsuranceType(String insuranceType) {
        this.insuranceType = insuranceType;
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public String getPolicyholder() {
        return policyholder;
    }

    public void setPolicyholder(String policyholder) {
        this.policyholder = policyholder;
    }

    public String getInsurer() {
        return insurer;
    }

    public void setInsurer(String insurer) {
        this.insurer = insurer;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public Date getStartDateStart() {
        return startDateStart;
    }

    public void setStartDateStart(Date startDateStart) {
        this.startDateStart = startDateStart;
    }

    public Date getStartDateEnd() {
        return startDateEnd;
    }

    public void setStartDateEnd(Date startDateEnd) {
        this.startDateEnd = startDateEnd;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public Date getEndDateStart() {
        return endDateStart;
    }

    public void setEndDateStart(Date endDateStart) {
        this.endDateStart = endDateStart;
    }

    public Date getEndDateEnd() {
        return endDateEnd;
    }

    public void setEndDateEnd(Date endDateEnd) {
        this.endDateEnd = endDateEnd;
    }

    public String getInsuranceTerm() {
        return insuranceTerm;
    }

    public void setInsuranceTerm(String insuranceTerm) {
        this.insuranceTerm = insuranceTerm;
    }

    public String getModMark() {
        return modMark;
    }

    public void setModMark(String modMark) {
        this.modMark = modMark;
    }

    public String getCheckMark() {
        return checkMark;
    }

    public void setCheckMark(String checkMark) {
        this.checkMark = checkMark;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getCreateDateStart() {
        return createDateStart;
    }

    public void setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
    }

    public Date getCreateDateEnd() {
        return createDateEnd;
    }

    public void setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
    }
}
