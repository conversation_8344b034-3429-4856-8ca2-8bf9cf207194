package com.eci.project.crmCustomerBank.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.eci.crud.dao.EciBaseDao;
import com.eci.project.crmCustomerBank.entity.CrmCustomerBankEntity;


/**
* 供应商开户行信息Dao层
* 接口层, 直接查询数据库使用
* @<NAME_EMAIL>
* @date 2025-05-13
*/
public interface CrmCustomerBankDao extends EciBaseDao<CrmCustomerBankEntity> {

}