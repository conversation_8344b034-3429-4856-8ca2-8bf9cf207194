package com.eci.project.vFzgjBdArea.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.eci.crud.entity.EciBaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.FieldNameConstants;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;



/**
* 对象 V_FZGJ_BD_AREA
* 可以自己扩展字段
* @<NAME_EMAIL>
* @date 2025-05-20
*/
@ApiModel("")
@TableName("V_FZGJ_BD_AREA")
@FieldNameConstants
public class VFzgjBdAreaEntity extends EciBaseEntity{
    @ApiModelProperty("(50)")
    @TableField("COUNTRY_CH_NAME")
    private String countryChName;

    @ApiModelProperty("(50)")
    @TableField("COUNTRY_EN_NAME")
    private String countryEnName;

    @ApiModelProperty("(50)")
    @TableField("COUNTRY_CODE")
    private String countryCode;

    @ApiModelProperty("(50)")
    @TableField("PROVINCE_NAME")
    private String provinceName;

    @ApiModelProperty("(50)")
    @TableField("PROVINCE_CODE")
    private String provinceCode;

    @ApiModelProperty("(50)")
    @TableField("CITY_NAME")
    private String cityName;

    @ApiModelProperty("(50)")
    @TableField("CITY_CODE")
    private String cityCode;

    @ApiModelProperty("(50)")
    @TableField("DISTRICT_NAME")
    private String districtName;

    @ApiModelProperty("(50)")
    @TableField("DISTRICT_CODE")
    private String districtCode;

    @ApiModelProperty("(50)")
    @TableField("AREA_NAME")
    private String areaName;

    @ApiModelProperty("(50)")
    @TableField("AREA_CODE")
    private String areaCode;

    @ApiModelProperty("(50)")
    @TableField("COMPANY_CODE")
    private String companyCode;

    @ApiModelProperty("(50)")
    @TableField("GROUP_CODE")
    private String groupCode;

    @ApiModelProperty("(1)")
    @TableField("STATUS")
    private String status;

    @JsonIgnore
    @TableField(exist = false)
    private Class<?> clazz = this.getClass();

    public Class<?> getClazz() {
        return clazz;
    }

    public void setClazz(Class<?> clazz) {
        this.clazz = clazz;
    }

    public VFzgjBdAreaEntity() {
        this.setSubClazz(VFzgjBdAreaEntity.class);
    }

    public VFzgjBdAreaEntity setCountryChName(String countryChName) {
        this.countryChName = countryChName;
        this.nodifySetFiled("countryChName", countryChName);
        return this;
    }

    public String getCountryChName() {
        this.nodifyGetFiled("countryChName");
        return countryChName;
    }

    public VFzgjBdAreaEntity setCountryEnName(String countryEnName) {
        this.countryEnName = countryEnName;
        this.nodifySetFiled("countryEnName", countryEnName);
        return this;
    }

    public String getCountryEnName() {
        this.nodifyGetFiled("countryEnName");
        return countryEnName;
    }

    public VFzgjBdAreaEntity setCountryCode(String countryCode) {
        this.countryCode = countryCode;
        this.nodifySetFiled("countryCode", countryCode);
        return this;
    }

    public String getCountryCode() {
        this.nodifyGetFiled("countryCode");
        return countryCode;
    }

    public VFzgjBdAreaEntity setProvinceName(String provinceName) {
        this.provinceName = provinceName;
        this.nodifySetFiled("provinceName", provinceName);
        return this;
    }

    public String getProvinceName() {
        this.nodifyGetFiled("provinceName");
        return provinceName;
    }

    public VFzgjBdAreaEntity setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
        this.nodifySetFiled("provinceCode", provinceCode);
        return this;
    }

    public String getProvinceCode() {
        this.nodifyGetFiled("provinceCode");
        return provinceCode;
    }

    public VFzgjBdAreaEntity setCityName(String cityName) {
        this.cityName = cityName;
        this.nodifySetFiled("cityName", cityName);
        return this;
    }

    public String getCityName() {
        this.nodifyGetFiled("cityName");
        return cityName;
    }

    public VFzgjBdAreaEntity setCityCode(String cityCode) {
        this.cityCode = cityCode;
        this.nodifySetFiled("cityCode", cityCode);
        return this;
    }

    public String getCityCode() {
        this.nodifyGetFiled("cityCode");
        return cityCode;
    }

    public VFzgjBdAreaEntity setDistrictName(String districtName) {
        this.districtName = districtName;
        this.nodifySetFiled("districtName", districtName);
        return this;
    }

    public String getDistrictName() {
        this.nodifyGetFiled("districtName");
        return districtName;
    }

    public VFzgjBdAreaEntity setDistrictCode(String districtCode) {
        this.districtCode = districtCode;
        this.nodifySetFiled("districtCode", districtCode);
        return this;
    }

    public String getDistrictCode() {
        this.nodifyGetFiled("districtCode");
        return districtCode;
    }

    public VFzgjBdAreaEntity setAreaName(String areaName) {
        this.areaName = areaName;
        this.nodifySetFiled("areaName", areaName);
        return this;
    }

    public String getAreaName() {
        this.nodifyGetFiled("areaName");
        return areaName;
    }

    public VFzgjBdAreaEntity setAreaCode(String areaCode) {
        this.areaCode = areaCode;
        this.nodifySetFiled("areaCode", areaCode);
        return this;
    }

    public String getAreaCode() {
        this.nodifyGetFiled("areaCode");
        return areaCode;
    }

    public VFzgjBdAreaEntity setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
        this.nodifySetFiled("companyCode", companyCode);
        return this;
    }

    public String getCompanyCode() {
        this.nodifyGetFiled("companyCode");
        return companyCode;
    }

    public VFzgjBdAreaEntity setGroupCode(String groupCode) {
        this.groupCode = groupCode;
        this.nodifySetFiled("groupCode", groupCode);
        return this;
    }

    public String getGroupCode() {
        this.nodifyGetFiled("groupCode");
        return groupCode;
    }

    public VFzgjBdAreaEntity setStatus(String status) {
        this.status = status;
        this.nodifySetFiled("status", status);
        return this;
    }

    public String getStatus() {
        this.nodifyGetFiled("status");
        return status;
    }

}
