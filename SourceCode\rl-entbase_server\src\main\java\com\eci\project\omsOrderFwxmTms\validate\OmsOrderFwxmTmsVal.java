package com.eci.project.omsOrderFwxmTms.validate;

import com.eci.log.enums.BusinessType;

import com.eci.project.omsOrderFwxmTms.entity.OmsOrderFwxmTmsEntity;

import org.springframework.stereotype.Service;


/**
* 委托内容-运输Val接口
* 验证接口
* @<NAME_EMAIL>
* @date 2025-04-14
*/
@Service
public class OmsOrderFwxmTmsVal{

    /**
    * 删除验证
    * @param entity
    */
    public void deleteValidate(OmsOrderFwxmTmsEntity entity) {

    }

    /**
    * 保存验证
    * @param entity
    */
    public void saveValidate(OmsOrderFwxmTmsEntity entity, BusinessType businessType) {

    }

}
