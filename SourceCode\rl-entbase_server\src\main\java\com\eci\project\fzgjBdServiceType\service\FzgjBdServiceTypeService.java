package com.eci.project.fzgjBdServiceType.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.eci.common.ZsrCacheUtil;
import com.eci.common.db.DBHelper;
import com.eci.common.web.BllContext;
import com.eci.crud.service.EciBaseService;
import com.eci.crud.tgQuery.EciQuery;
import com.eci.log.enums.BusinessType;
import com.eci.page.TgPageInfo;

import com.eci.project.fzgjBdServiceItemPt.entity.FzgjBdServiceItemPtEntity;
import com.eci.project.fzgjBdServiceItemPt.entity.TreeModel;
import com.eci.project.fzgjBdServiceType.dao.FzgjBdServiceTypeDao;
import com.eci.project.fzgjBdServiceType.entity.FzgjBdServiceTypeEntity;
import com.eci.project.fzgjBdServiceType.validate.FzgjBdServiceTypeVal;

import com.eci.sso.role.entity.UserContext;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.List;
import java.util.Optional;


/**
 * 服务类型Service业务层处理
 *
 * @<NAME_EMAIL>
 * @date 2025-03-13
 */
@Service
@Slf4j
public class FzgjBdServiceTypeService implements EciBaseService<FzgjBdServiceTypeEntity> {

    @Autowired
    private FzgjBdServiceTypeDao fzgjBdServiceTypeDao;

    @Autowired
    private FzgjBdServiceTypeVal fzgjBdServiceTypeVal;

    private final ZsrCacheUtil<String, List<FzgjBdServiceTypeEntity>> serviceTypeNameCache = new ZsrCacheUtil<>(30); // 缓存周期 30 秒

    /**
     * 根据服务编号获取服务类型名称
     *
     * @param serviceNo 服务编号
     * @return
     */
    public String getServiceTypeNameFromDB(String serviceNo) {
        List<FzgjBdServiceTypeEntity> fzgjBdServiceTypeEntities = serviceTypeNameCache.get(serviceNo, () -> fzgjBdServiceTypeDao.select().list());
        Optional<FzgjBdServiceTypeEntity> serviceType = fzgjBdServiceTypeEntities.stream()
                .filter(c -> c.getCode().equals(serviceNo))
                .findFirst();
        if (serviceType.isPresent()) {
            return serviceType.get().getName();
        }
        return null;
    }

    @Override
    public TgPageInfo queryPageList(FzgjBdServiceTypeEntity entity) {
        EciQuery<FzgjBdServiceTypeEntity> eciQuery = EciQuery.buildQuery(entity);
        List<FzgjBdServiceTypeEntity> entities = fzgjBdServiceTypeDao.queryPageList(eciQuery);
        return EciQuery.getPageInfo(entities);
    }

    /**
     * @return java.util.List<com.eci.project.fzgjBdServiceItemPt.entity.TreeList>
     * <AUTHOR>
     * @Description 用于查询服务类型的顶层数据
     * @Date 2025/3/25 11:30
     * @Param [entity]
     **/
    public List<TreeModel> selectTree(FzgjBdServiceTypeEntity entity) {
        QueryWrapper query = new QueryWrapper();
        query.apply("parentid={0}", entity.getParentid());
        query.apply("status={0}", "Y");
        query.orderByAsc("code");
        List<TreeModel> list = fzgjBdServiceTypeDao.selectTree(query);
        return list;
    }

    public List<FzgjBdServiceTypeEntity> selectAll(){
        return fzgjBdServiceTypeDao.selectAll();
    }

    @SneakyThrows
    @Override
    @Transactional(rollbackFor = Exception.class)
    public FzgjBdServiceTypeEntity save(FzgjBdServiceTypeEntity entity) {
        if (BllContext.getBusinessType() == BusinessType.INSERT) {
            entity.setGuid(IdWorker.getIdStr());
            entity.setCreateDate(new java.util.Date());
            entity.setUpdateDate(new java.util.Date());
            entity.setCreateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setCreateUserName(UserContext.getUserInfo().getTrueName());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        } else {
            entity.setUpdateDate(new java.util.Date());
            entity.setUpdateUser(UserContext.getUserInfo().getUserLoginNo());
            entity.setUpdateUserName(UserContext.getUserInfo().getTrueName());
        }
        entity.setParentid("0");
        // 返回实体对象
        FzgjBdServiceTypeEntity fzgjBdServiceTypeEntity = null;
        fzgjBdServiceTypeVal.saveValidate(entity, BllContext.getBusinessType());

        if (BllContext.getBusinessType() == BusinessType.INSERT) {

            fzgjBdServiceTypeEntity = fzgjBdServiceTypeDao.insertOne(entity);

        } else {

            fzgjBdServiceTypeEntity = fzgjBdServiceTypeDao.updateByEntityId(entity);

        }
        return fzgjBdServiceTypeEntity;
    }

    @Override
    public List<FzgjBdServiceTypeEntity> selectList(FzgjBdServiceTypeEntity entity) {
        return fzgjBdServiceTypeDao.selectList(entity);
    }

    @Override
    public FzgjBdServiceTypeEntity selectOneById(Serializable id) {
        return fzgjBdServiceTypeDao.selectById(id);
    }


    @Override
    public void insertBatch(List<FzgjBdServiceTypeEntity> list) {
        fzgjBdServiceTypeDao.insertList(list);
    }

    @Override
    public int deleteByIds(String ids) {
        return fzgjBdServiceTypeDao.deleteByIds(ids);
    }

    @Override
    public int deleteById(Serializable id) {
        return fzgjBdServiceTypeDao.deleteById(id);
    }

}